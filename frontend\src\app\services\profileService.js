import apiService from '../lib/apiService';

const profileService = {
    getProfile: () => apiService.get('/user/profile', { withCredentials: true }),
    editAccount: (data) => apiService.post('/user/setConfigAccount', data, { withCredentials: true }),
    uploadAvatar: (data) => apiService.post('/user/profileImage', data, { withCredentials: true }),
    changeEmail: (data) => apiService.post('/user/setNewEmail', data, { withCredentials: true }),
    changePassword: (data) => apiService.post('/user/setNewPassword', data, { withCredentials: true }),
    verifyOtp: (data) => apiService.post('/user/verifyOtp', data, { withCredentials: true }),

    getBillingInfo: (userId) => apiService.get(`/user/getBillingInfo/${userId}`, { withCredentials: true }),
    updateBillingInfo: (data) => apiService.put(`/user/editBillingInfo`, data, { withCredentials: true }),
    deleteBillingInfo: (userId) => apiService.delete(`/user/deleteBillingInfo/${userId}`, { withCredentials: true }),
};

export default profileService;
