/**
 * Test script for VPS Reinstall with Root User enabled
 * Tests the enableRootUser and defaultUser functionality
 */

const axios = require('axios');

async function testReinstallWithRootUser() {
  console.log('🧪 Testing VPS Reinstall with Root User');
  console.log('======================================');
  
  try {
    console.log('\n1. Testing standard installation with root user...');
    
    // This simulates the frontend payload for standard installation
    const standardPayload = {
      "imageId": "afecbb85-e2fc-46f0-9684-b46b1faf00bb",
      "password": "TestPassword123!",
      "enableRootUser": true,
      "defaultUser": "root",
      "selectedApplication": "82802c59-6061-45e0-ae1d-bbe855157c66",
      "userData": "#cloud-config\n# Enable password authentication for SSH\nssh_pwauth: true\npassword: TestPassword123!\nchpasswd:\n  expire: false\n\n# Configure SSH to allow password authentication\nwrite_files:\n  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf\n    content: |\n      PasswordAuthentication yes\n      PermitRootLogin yes\n      PubkeyAuthentication yes\n    permissions: '0644'\n\nruncmd:\n  - systemctl restart sshd || service ssh restart\n  - echo \"Password authentication enabled for standard installation\" >> /root/install.log"
    };
    
    console.log('📦 Standard installation payload:');
    console.log(`   - imageId: ${standardPayload.imageId}`);
    console.log(`   - password: ${standardPayload.password ? '***HIDDEN***' : 'not provided'}`);
    console.log(`   - enableRootUser: ${standardPayload.enableRootUser}`);
    console.log(`   - defaultUser: ${standardPayload.defaultUser}`);
    console.log(`   - selectedApplication: ${standardPayload.selectedApplication}`);
    
    const response1 = await axios.post(
      'http://localhost:5002/api/vps/instances/202718127/reinstall',
      standardPayload,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 30000
      }
    );
    
    console.log('✅ Standard installation endpoint accessible');
    console.log(`📊 Status: ${response1.status} ${response1.statusText}`);
    
    console.log('\n2. Testing advanced installation with admin user...');
    
    // This simulates the frontend payload for advanced installation with admin user
    const advancedPayload = {
      "imageId": "afecbb85-e2fc-46f0-9684-b46b1faf00bb",
      "password": "AdminPassword123!",
      "enableRootUser": false,
      "defaultUser": "admin",
      "userData": "#cloud-config\nusers:\n  - name: admin\n    sudo: ALL=(ALL) NOPASSWD:ALL\n    shell: /bin/bash\n    lock_passwd: false\n    passwd: $6$rounds=4096$saltsalt$hash\nssh_pwauth: true"
    };
    
    console.log('📦 Advanced installation payload:');
    console.log(`   - imageId: ${advancedPayload.imageId}`);
    console.log(`   - password: ${advancedPayload.password ? '***HIDDEN***' : 'not provided'}`);
    console.log(`   - enableRootUser: ${advancedPayload.enableRootUser}`);
    console.log(`   - defaultUser: ${advancedPayload.defaultUser}`);
    
    const response2 = await axios.post(
      'http://localhost:5002/api/vps/instances/202718127/reinstall',
      advancedPayload,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 30000
      }
    );
    
    console.log('✅ Advanced installation endpoint accessible');
    console.log(`📊 Status: ${response2.status} ${response2.statusText}`);
    
    console.log('\n3. Testing validation for defaultUser field...');
    
    const validationTests = [
      {
        name: 'Valid root user',
        payload: { imageId: "test", defaultUser: "root" }
      },
      {
        name: 'Valid admin user',
        payload: { imageId: "test", defaultUser: "admin" }
      },
      {
        name: 'Invalid user type',
        payload: { imageId: "test", defaultUser: "invalid" }
      }
    ];
    
    for (const test of validationTests) {
      try {
        console.log(`\n   Testing: ${test.name}`);
        const response = await axios.post(
          'http://localhost:5002/api/vps/instances/202718127/reinstall',
          test.payload,
          {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
          }
        );
        console.log(`   ✅ ${test.name}: Passed validation`);
      } catch (error) {
        if (error.response?.status === 400) {
          console.log(`   ❌ ${test.name}: Validation failed (expected for invalid values)`);
          if (error.response.data.errors) {
            error.response.data.errors.forEach(err => {
              console.log(`      - ${err.key}: ${err.msg}`);
            });
          }
        } else if (error.response?.status === 401) {
          console.log(`   ✅ ${test.name}: Passed validation (auth required)`);
        } else {
          console.log(`   ❓ ${test.name}: ${error.response?.status || error.message}`);
        }
      }
    }
    
    console.log('\n🎉 Root user functionality test completed!');
    console.log('\n✅ Fix Summary:');
    console.log('- Added defaultUser field to frontend payload');
    console.log('- Standard installation: enableRootUser=true, defaultUser="root"');
    console.log('- Advanced installation: enableRootUser based on form, defaultUser based on enableRootUser');
    console.log('- Updated backend validation to accept defaultUser field');
    console.log('- Updated VPS service to map defaultUser field');
    console.log('- Updated Contabo provider to include defaultUser in payload');
    console.log('- Fixed debug logging to show actual payload values');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the backend server is running: npm run dev');
    } else if (error.response) {
      console.log(`📊 HTTP Status: ${error.response.status}`);
      console.log(`📋 Error Response:`, error.response.data);
    }
  }
}

// Run the test
testReinstallWithRootUser();
