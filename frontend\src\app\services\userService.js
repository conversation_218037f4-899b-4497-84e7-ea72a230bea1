import apiService from '../lib/apiService';

const userService = {
  // User profile
  getProfile: () => apiService.get('/user/profile', { withCredentials: true }),
  updateProfile: (data) => apiService.post('/user/setConfigAccount', data, { withCredentials: true }),
  updatePassword: (data) => apiService.post('/user/setNewPassword', data, { withCredentials: true }),
  updateEmail: (data) => apiService.post('/user/setNewEmail', data, { withCredentials: true }),
  verifyOtp: (data) => apiService.post('/user/verifyOtp', data, { withCredentials: true }),
  resendOtp: (data) => apiService.post('/user/verifyEmail/resendOtp', data, { withCredentials: true }),
  removeAccount: () => apiService.post('/user/removeAccount', {}, { withCredentials: true }),
  updateProfileImage: (formData) => apiService.post('/user/profileImage', formData, {
    withCredentials: true,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  
  // User preferences
  updateLanguage: (data) => apiService.put('/user/favoriteLang', data, { withCredentials: true }),
  
  // Payment methods
  getCurrentPaymentMethod: () => apiService.get('/user/currentPaymentMethod', { withCredentials: true }),
  updatePaymentMethod: (data) => apiService.post('/user/updatePaymentMethod', data, { withCredentials: true }),
  
  // Billing information
  getBillingInfo: (userId) => apiService.get(`/user/getBillingInfo/${userId}`, { withCredentials: true }),
  updateBillingInfo: (data) => apiService.put('/user/editBillingInfo', data, { withCredentials: true }),
  deleteBillingInfo: (userId) => apiService.delete(`/user/deleteBillingInfo/${userId}`, { withCredentials: true }),
  
  // Notifications
  getUserNotifications: (params = {}) => {
    const { page = 1, limit = 10, unreadOnly = false } = params;
    return apiService.get(`/user/notifications?page=${page}&limit=${limit}&unreadOnly=${unreadOnly}`, { 
      withCredentials: true 
    });
  },
  
  markNotificationAsRead: (notificationId) => 
    apiService.put(`/user/notifications/${notificationId}/read`, {}, { withCredentials: true }),
  
  markAllNotificationsAsRead: () => 
    apiService.put('/user/notifications/read-all', {}, { withCredentials: true }),
};

export default userService;