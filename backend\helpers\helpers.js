const https = require("https");
const fs = require("fs");
const path = require("path");

const downloadAndMoveImage = (imgUrl, targetDirectory, imageName) => {
  const targetImg = path.join(targetDirectory, imageName);

  // Create a writable stream
  const file = fs.createWriteStream(targetImg);

  return new Promise((resolve, reject) => {
    https
      .get(imgUrl, (response) => {
        response.pipe(file);
        file.on("finish", () => {
          file.close();
          // Move the downloaded image to the target directory
          const newPath = path.join(targetDirectory, imageName);
          fs.rename(targetImg, newPath, (err) => {
            if (err) {
              reject(`Error moving the image: ${err.message}`);
            } else {
              // console.log(`Image moved to ${newPath}`);
              resolve(newPath);
            }
          });
        });
      })
      .on("error", (err) => {
        reject(`Error downloading image: ${err.message}`);
      });
  });
};

const setServerMedia = (imageUrl) => {
  if (!imageUrl) return imageUrl;
  if (imageUrl.indexOf("https://") != -1 || imageUrl.indexOf("http://") != -1)
    return imageUrl;
  return process.env.BACKEND_URL + imageUrl;
};

function getTranslation(key, lang) {
  lang = lang.split("-")[0] || "en"; // Get the language code (e.g., "en" from "en-US")

  const filePath = path.join(
    __dirname,
    "..",
    "locales",
    lang,
    "translation.json"
  );
  const translations = JSON.parse(fs.readFileSync(filePath, "utf8"));

  if (!translations[key]) {
    console.error(
      `Translation for key "${key}" not found in language "${lang}"`
    );
    return key;
  }
  return translations[key] || key;
}

function getNextValidDate(date) {
  const dayOfWeek = date.getDay();

  // Adjust date if it's a weekend
  if (dayOfWeek === 6) {
    // Saturday
    date.setDate(date.getDate() + 2); // Move to Monday
  } else if (dayOfWeek === 0) {
    // Sunday
    date.setDate(date.getDate() + 1); // Move to Monday
  }

  return date;
}

function scheduledPaymentDate() {
  const currentDate = new Date();
  let lastDayOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  ); // Last day of the current month

  // Ensure lastDayOfMonth is not a weekend
  lastDayOfMonth = getNextValidDate(lastDayOfMonth);

  // Calculate the number of days until the end of the month
  const daysUntilEndOfMonth =
    (lastDayOfMonth - currentDate) / (1000 * 60 * 60 * 24);

  let paymentDate;

  if (daysUntilEndOfMonth >= 7) {
    // If there are at least 7 days left, use the last day of the month
    paymentDate = lastDayOfMonth;
  } else {
    // If there are fewer than 7 days left, set payment date to 10 days after the last day of the month
    paymentDate = new Date(lastDayOfMonth);
    paymentDate.setDate(paymentDate.getDate() + 10);

    // Ensure paymentDate is not a weekend
    paymentDate = getNextValidDate(paymentDate);
  }

  return paymentDate;
}

// Create a cookie config helper
const getCookieConfig = (isProd) => ({
  httpOnly: true,
  secure: isProd,
  sameSite: isProd ? "none" : "Lax", // Required for cross-domain
  domain: isProd ? ".ztechengineering.com" : "localhost", // Main domain without subdomain
  path: "/",
});

const getRedirectUrl = (req, isProd, isReferred = false) => {
  return isReferred && req.headers.referer
    ? req.headers.referer
    : isProd
    ? process.env.FRONTEND_URL
    : process.env.FRONTEND_LOCAL_URL;
};

module.exports = {
  downloadAndMoveImage,
  setServerMedia,
  getTranslation,
  getNextValidDate,
  scheduledPaymentDate,
  getCookieConfig,
  getRedirectUrl,
};
