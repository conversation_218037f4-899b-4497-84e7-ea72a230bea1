const cron = require('node-cron');
const CustomNotification = require('../models/CustomNotification');
const { processDueNotifications } = require('../controllers/admin/customNotificationController');

let customNotificationCronJob = null;

/**
 * Start the custom notification cron job
 * This job runs every minute to check for due notifications
 */
const startCustomNotificationCronJob = async () => {
  try {
    // Stop existing job if running
    if (customNotificationCronJob) {
      customNotificationCronJob.destroy();
      console.log('[CUSTOM NOTIFICATION] Stopped existing cron job');
    }

    // Start new cron job - runs every minute
    customNotificationCronJob = cron.schedule('* * * * *', async () => {
      console.log('[CUSTOM NOTIFICATION] Checking for due custom notifications...');
      await processDueNotifications();
    }, {
      scheduled: true,
      timezone: "UTC"
    });

    console.log('[CUSTOM NOTIFICATION] Custom notification cron job started - checking every minute');
    return customNotificationCronJob;
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error starting custom notification cron job:', error);
    throw error;
  }
};

/**
 * Stop the custom notification cron job
 */
const stopCustomNotificationCronJob = () => {
  if (customNotificationCronJob) {
    customNotificationCronJob.destroy();
    customNotificationCronJob = null;
    console.log('[CUSTOM NOTIFICATION] Custom notification cron job stopped');
  }
};

/**
 * Get the current status of the custom notification cron job
 */
const getCustomNotificationCronJobStatus = () => {
  return {
    isRunning: customNotificationCronJob ? true : false,
    cronJob: customNotificationCronJob ? 'active' : 'inactive'
  };
};

/**
 * Get statistics about custom notifications
 */
const getCustomNotificationStats = async () => {
  try {
    const stats = await CustomNotification.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const totalScheduled = await CustomNotification.countDocuments({ status: 'scheduled' });
    const totalSent = await CustomNotification.countDocuments({ status: 'sent' });
    const totalFailed = await CustomNotification.countDocuments({ status: 'failed' });
    const totalCancelled = await CustomNotification.countDocuments({ status: 'cancelled' });

    // Get due notifications count
    const dueNotifications = await CustomNotification.countDocuments({
      scheduledDateTime: { $lte: new Date() },
      status: 'scheduled'
    });

    return {
      total: totalScheduled + totalSent + totalFailed + totalCancelled,
      scheduled: totalScheduled,
      sent: totalSent,
      failed: totalFailed,
      cancelled: totalCancelled,
      due: dueNotifications,
      breakdown: stats
    };
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error getting stats:', error);
    throw error;
  }
};

/**
 * Clean up old notifications (older than 30 days and already sent/failed)
 */
const cleanupOldNotifications = async () => {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await CustomNotification.deleteMany({
      createdAt: { $lt: thirtyDaysAgo },
      status: { $in: ['sent', 'failed', 'cancelled'] }
    });

    console.log(`[CUSTOM NOTIFICATION] Cleaned up ${result.deletedCount} old notifications`);
    return result.deletedCount;
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error cleaning up old notifications:', error);
    throw error;
  }
};

/**
 * Initialize custom notification service
 */
const initializeCustomNotificationService = async () => {
  try {
    console.log('[CUSTOM NOTIFICATION] Initializing custom notification service...');
    
    // Start the cron job
    await startCustomNotificationCronJob();
    
    // Get initial stats
    const stats = await getCustomNotificationStats();
    console.log('[CUSTOM NOTIFICATION] Service initialized. Stats:', stats);
    
    return true;
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error initializing service:', error);
    throw error;
  }
};

module.exports = {
  startCustomNotificationCronJob,
  stopCustomNotificationCronJob,
  getCustomNotificationCronJobStatus,
  getCustomNotificationStats,
  cleanupOldNotifications,
  initializeCustomNotificationService
};
