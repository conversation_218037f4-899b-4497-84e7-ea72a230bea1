/**
 * Utility functions to format log details for non-developers
 */

/**
 * Format a key from camelCase or snake_case to a human-readable label
 * @param {string} key - The key to format
 * @returns {string} - The formatted label
 */
export const formatKey = (key) => {
  if (!key) return '';
  
  // Replace underscores and hyphens with spaces
  let formatted = key.replace(/[_-]/g, ' ');
  
  // Convert camelCase to spaces
  formatted = formatted.replace(/([A-Z])/g, ' $1');
  
  // Capitalize first letter of each word
  formatted = formatted
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
  
  // Special case for ID
  formatted = formatted.replace(/\bId\b/g, 'ID');
  
  return formatted.trim();
};

/**
 * Format a value to be more readable
 * @param {any} value - The value to format
 * @returns {string} - The formatted value
 */
export const formatValue = (value) => {
  if (value === null || value === undefined) {
    return 'None';
  }
  
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  
  if (typeof value === 'string') {
    // Check if it's a date string
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
      try {
        return new Date(value).toLocaleString();
      } catch (e) {
        return value;
      }
    }
    
    // Check if it's an ISO date string
    if (!isNaN(Date.parse(value)) && value.includes('T')) {
      try {
        return new Date(value).toLocaleString();
      } catch (e) {
        return value;
      }
    }
    
    return value;
  }
  
  if (Array.isArray(value)) {
    if (value.length === 0) {
      return 'Empty list';
    }
    
    if (typeof value[0] === 'object' && value[0] !== null) {
      return `List of ${value.length} items`;
    }
    
    return value.map(item => formatValue(item)).join(', ');
  }
  
  if (typeof value === 'object') {
    // Check if it's a MongoDB ObjectId
    if (value._id) {
      return `ID: ${value._id}`;
    }
    
    return 'Complex data';
  }
  
  return String(value);
};

/**
 * Format log details for non-developers
 * @param {object} details - The log details to format
 * @returns {object} - The formatted details
 */
export const formatLogDetails = (details) => {
  if (!details || typeof details !== 'object') {
    return { message: 'No details available' };
  }
  
  const formatted = {};
  
  // First, extract any message field as it's the most important
  if (details.message) {
    formatted.Message = details.message;
  }
  
  // Format all other fields
  Object.entries(details).forEach(([key, value]) => {
    if (key !== 'message') {
      formatted[formatKey(key)] = formatValue(value);
    }
  });
  
  return formatted;
};

/**
 * Format the entire log object for display
 * @param {object} log - The log object to format
 * @returns {object} - The formatted log object
 */
export const formatLogForDisplay = (log) => {
  if (!log) return null;
  
  const formattedLog = {};
  
  // Add a summary section
  formattedLog.Summary = {
    Action: formatKey(log.action),
    'Target Type': log.targetModel || 'Unknown',
    Timestamp: formatValue(log.timestamp),
  };
  
  // Format the target information
  if (log.target) {
    formattedLog['Target Information'] = 
      typeof log.target === 'object' 
        ? Object.entries(log.target).reduce((acc, [key, value]) => {
            acc[formatKey(key)] = formatValue(value);
            return acc;
          }, {})
        : { 'Target ID': formatValue(log.target) };
  }
  
  // Format the details
  if (log.details) {
    formattedLog['Action Details'] = formatLogDetails(log.details);
  }
  
  return formattedLog;
};
