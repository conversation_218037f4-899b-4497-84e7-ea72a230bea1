const ContaboProvider = require('./providers/ContaboProvider');

class CustomImageService {
  constructor() {
    this.contaboProvider = new ContaboProvider();
  }

  /**
   * Créer une image personnalisée
   * @param {Object} imageData - Données de l'image personnalisée
   * @returns {Promise<Object>} Détails de l'image créée
   */
  async createCustomImage(imageData) {
    const maxAttempts = 3;
    const retryDelayMs = 5000; // Démarrer avec 5 secondes
    let lastError = null;

    try {
      console.log('🖼️ Création de l\'image personnalisée :', imageData);

      // Valider les champs requis
      if (!imageData.url) {
        throw new Error('L\'URL de l\'image est requise');
      }
      if (!imageData.name) {
        throw new Error('Le nom de l\'image est requis');
      }
      if (!imageData.version) {
        throw new Error('La version de l\'image est requise');
      }

      // Vérifier si une image avec ce nom existe déjà
      let uniqueName = imageData.name;
      try {
        const existingImages = await this.getCustomImages();
        const existingNames = existingImages.data.map(img => img.name);

        // Si le nom existe, en générer un unique
        if (existingNames.includes(uniqueName)) {
          const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '-');
          uniqueName = `${imageData.name}-${timestamp}`;
          console.log(`⚠️ Le nom d'image '${imageData.name}' existe déjà, utilisation d'un nom unique : '${uniqueName}'`);
        }
      } catch (error) {
        console.log('⚠️ Impossible de vérifier les images existantes, nous procédons avec le nom original');
      }

      // Préparer la charge utile de l'image personnalisée pour l'API Contabo
      const customImagePayload = {
        name: uniqueName,
        description: imageData.description || '',
        url: imageData.url,
        osType: imageData.osType || 'Linux',
        version: imageData.version
      };

      console.log('📤 Envoi de la requête de création d\'image personnalisée :', customImagePayload);

      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          // Appeler l'API Contabo pour créer une image personnalisée
          const response = await this.contaboProvider.makeRequest(
            'POST',
            '/compute/images',
            customImagePayload
          );

          console.log(`✅ Image personnalisée créée avec succès à la tentative ${attempt} :`, response);

          const responseData = response.data.data?.[0] || response.data;
          const imageId = responseData.imageId || responseData.id;

          return {
            success: true,
            data: {
              ...responseData,
              id: imageId,
              imageId: imageId
            },
            message: 'Image personnalisée créée avec succès'
          };
        } catch (error) {
          lastError = error;
          // Ne réessayer que pour les erreurs serveur 5xx, qui sont souvent transitoires.
          if (error.response && error.response.status >= 500 && attempt < maxAttempts) {
            console.warn(`[IMAGE PERSONNALISÉE] Tentative ${attempt}/${maxAttempts} échouée avec une erreur serveur. Nouvelle tentative dans ${retryDelayMs * attempt / 1000}s...`);
            await new Promise(resolve => setTimeout(resolve, retryDelayMs * attempt)); // Attente linéaire
          } else {
            // Gestion spéciale pour l'erreur 409 (nom dupliqué)
            if (error.response && error.response.status === 409) {
              const errorMessage = error.response.data?.message || error.message;
              if (errorMessage.includes('not unique by name')) {
                throw new Error(`DUPLICATE_NAME:Une image avec le nom "${imageData.name}" existe déjà dans votre compte. Veuillez choisir un nom différent ou ajouter un suffixe (ex: ${imageData.name}-v2).`);
              }
            }
            // Ne pas réessayer pour les erreurs client (4xx) ou si le nombre maximum de tentatives est atteint.
            throw error;
          }
        }
      }

    } catch (error) {
      console.error('❌ Échec de la création de l\'image personnalisée :', error.message);
      // Utiliser la dernière erreur capturée lors des tentatives si disponible, car c'est la plus pertinente.
      const finalError = lastError || error;
      throw new Error(`Échec de la création de l'image personnalisée : ${finalError.message}`);
    }
  }

  /**
   * Obtenir toutes les images personnalisées
   * @returns {Promise<Array>} Liste des images personnalisées
   */
  async getCustomImages() {
    try {
      console.log('📋 Récupération des images personnalisées directement depuis l\'API...');

      // Récupérer toutes les images depuis l'API Contabo
      const allImagesFromApi = await this.contaboProvider.getImages();

      // Filtrer pour ne garder que les images personnalisées (standardImage: false)
      // et celles qui sont téléchargées et prêtes ('downloaded' ou 'available')
      const availableCustomImages = allImagesFromApi.filter(img =>
        img.standardImage === false &&
        (img.status === 'available' || img.status === 'downloaded')
      );

      console.log(`✅ ${availableCustomImages.length} images personnalisées prêtes à l'emploi trouvées (sur ${allImagesFromApi.length} images au total).`);

      return {
        success: true,
        data: availableCustomImages,
        message: `${availableCustomImages.length} images personnalisées récupérées`
      };

    } catch (error) {
      console.error('❌ Échec de la récupération des images personnalisées :', error.message);
      throw new Error(`Échec de la récupération des images personnalisées : ${error.message}`);
    }
  }

  /**
   * Supprimer une image personnalisée
   * @param {string} imageId - ID de l'image à supprimer
   * @returns {Promise<Object>} Résultat de la suppression
   */
  async deleteCustomImage(imageId) {
    try {
      console.log('🗑️ Suppression de l\'image personnalisée :', imageId);

      const response = await this.contaboProvider.makeRequest(
        'DELETE',
        `/compute/images/${imageId}`
      );

      console.log('✅ Image personnalisée supprimée avec succès');

      return {
        success: true,
        message: 'Image personnalisée supprimée avec succès'
      };

    } catch (error) {
      console.error('❌ Échec de la suppression de l\'image personnalisée :', error.message);
      throw new Error(`Échec de la suppression de l'image personnalisée : ${error.message}`);
    }
  }

  /**
   * Mettre à jour une image personnalisée
   * @param {string} imageId - ID de l'image à mettre à jour
   * @param {Object} updateData - Données à mettre à jour
   * @returns {Promise<Object>} Résultat de la mise à jour
   */
  async updateCustomImage(imageId, updateData) {
    try {
      console.log('✏️ Mise à jour de l\'image personnalisée :', imageId, updateData);

      const response = await this.contaboProvider.makeRequest(
        'PUT',
        `/compute/images/${imageId}`,
        updateData
      );

      console.log('✅ Image personnalisée mise à jour avec succès');

      return {
        success: true,
        data: response.data,
        message: 'Image personnalisée mise à jour avec succès'
      };

    } catch (error) {
      console.error('❌ Échec de la mise à jour de l\'image personnalisée :', error.message);
      throw new Error(`Échec de la mise à jour de l'image personnalisée : ${error.message}`);
    }
  }

  /**
   * Obtenir les détails d'une image personnalisée
   * @param {string} imageId - ID de l'image
   * @returns {Promise<Object>} Détails de l'image
   */
  async getCustomImageDetails(imageId) {
    try {
      console.log('🔍 Obtention des détails de l\'image personnalisée :', imageId);

      const response = await this.contaboProvider.makeRequest(
        'GET',
        `/compute/images/${imageId}`
      );

      console.log('✅ Détails de l\'image personnalisée récupérés');

      return {
        success: true,
        data: response.data,
        message: 'Détails de l\'image personnalisée récupérés avec succès'
      };

    } catch (error) {
      console.error('❌ Échec de l\'obtention des détails de l\'image personnalisée :', error.message);
      throw new Error(`Échec de l'obtention des détails de l'image personnalisée : ${error.message}`);
    }
  }
}

module.exports = CustomImageService;
