"use client";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
} from "@material-tailwind/react";
import { useEffect, useState, useRef } from "react";
import { useLocale, useTranslations } from "next-intl";
import PaymentStatusModal from "../../../../components/order/paymentStatusModal";
import CancellationModal from "../../../../components/vps/CancellationModal";
import ReinstallModal from "../../../components/ReinstallModal";
import PasswordResetModal from "../../../components/PasswordResetModal";
import {
  VPSActionSuccessNotification,
  ErrorNotification,
  InfoNotification,
  ConsoleNotification,
} from "../../../components/SuccessNotification";
import { useSearchParams } from "next/navigation";
import orderService from "../../../services/orderService";
import vpsService from "../../../services/vpsService";
import { toast } from "react-toastify";
import {
  Server,
  Clock,
  CheckCircle2,
  PhoneCall,
  Rocket,
  AlertCircle,
  RotateCcw,
  Play,
  Square,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  Shield,
  Monitor,
  Database,
  Activity,
  Zap,
  RefreshCw,
  Trash2,
  Edit,
  Eye,
  MoreVertical,
  Settings,
  X,
  CircleFadingArrowUp,
  MapPinHouse,
  FolderPlus,
  Grid2X2,
  Blocks,
  ServerOff,
  KeyIcon,
  Info,
  Lock,
  KeySquare,
  ToggleRight,
} from "lucide-react";
import { OrderStatus } from "../../../config/ConstStatus";
import { getLocalizedContent } from "@/app/helpers/helpers";
import { useIsMobile } from "@/app/hook/useIsMobile";
import { createPortal } from "react-dom";

// Server Status Component
const ServerStatus = ({ status }) => {
  const getStatusConfig = (status) => {
    const statusLower = status?.toLowerCase();

    switch (statusLower) {
      case "running":
        return {
          bgClass: "bg-green-100",
          textClass: "text-green-700",
          iconClass: "text-green-600",
          icon: Activity,
          label: "Running",
        };
      case "stopped":
        return {
          bgClass: "bg-red-100",
          textClass: "text-red-700",
          iconClass: "text-red-600",
          icon: Square,
          label: "Stopped",
        };
      case "rescue":
        return {
          bgClass: "bg-yellow-100",
          textClass: "text-yellow-700",
          iconClass: "text-yellow-600",
          icon: Shield,
          label: "Rescue",
        };
      default:
        return {
          bgClass: "bg-gray-100",
          textClass: "text-gray-700",
          iconClass: "text-gray-600",
          icon: Activity,
          label: status || "Unknown",
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <div
      className={`flex items-center gap-2 px-2 py-1 rounded-full ${config.bgClass}`}
    >
      <Icon className={`w-3 h-3 ${config.iconClass}`} />
      <span className={`text-xs font-medium ${config.textClass}`}>
        {config.label}
      </span>
    </div>
  );
};

// Manage Menu Component
const ManageMenu = ({ server, onClose, onPasswordReset }) => {
  const menuItems = [
    { title: "Upgrade VPS", icon: CircleFadingArrowUp },
    // { title: "Move to other Region", icon: MapPinHouse },
    // { title: "Order Add-On", icon: FolderPlus },
    // { title: "Order Windows", icon: Grid2X2 },
    // { title: "Extend NVMe Storage", icon: Blocks },
    { title: "I can't connect to this server", icon: ServerOff },
    { title: "Password reset", icon: KeySquare },
    // { title: "VNC Information", icon: Info },
    // { title: "VNC Password", icon: Lock },
    { title: "Disable VNC", icon: ToggleRight },
  ];

  const handleMenuItemClick = (item) => {
    if (item.action === "password-reset") {
      onPasswordReset(server);
    }
    onClose();
  };

  return (
    <Menu open={true} handler={onClose}>
      <MenuList className="z-50">
        {menuItems.map((item, index) => (
          <MenuItem
            key={index}
            className="flex items-center gap-2 hover:bg-gray-50"
            onClick={() => handleMenuItemClick(item)}
          >
            {item.icon && <item.icon className="w-4 h-4" />}
            {item.title}
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

// Simple Manage Button Component
const ManageButton = ({ server, onCancelClick, onPasswordReset }) => {
  const [menuOpen, setMenuOpen] = useState(false);

  const handleMenuToggle = () => {
    setMenuOpen((prev) => !prev);
  };

  const handleMenuItemClick = (item) => {
    if (item.action === "password-reset") {
      onPasswordReset(server);
    }
    setMenuOpen(false);
  };

  return (
    <div className="relative">
      {" "}
      {/* Ensure the parent container is positioned */}
      <Menu open={menuOpen} handler={setMenuOpen} placement="bottom-start">
        <MenuHandler>
          <button
            className="p-2 rounded-lg bg-blue-100 text-blue-700 hover:bg-blue-200 hover:shadow-md transition-all duration-200"
            onClick={handleMenuToggle}
            title="Gérer le VPS"
          >
            <Settings className="w-4 h-4" />
          </button>
        </MenuHandler>
        {menuOpen && (
          <MenuList className="z-50">
            {[
              { title: "Upgrade VPS", icon: CircleFadingArrowUp },
              // { title: "Move to other Region", icon: MapPinHouse },
              // { title: "Order Add-On", icon: FolderPlus },
              // { title: "Order Windows", icon: Grid2X2 },
              // { title: "Extend NVMe Storage", icon: Blocks },
              { title: "I can't connect to this server", icon: ServerOff },
              { title: "Password reset", icon: KeySquare },
              // { title: "VNC Information", icon: Info },
              // { title: "VNC Password", icon: Lock },
              // { title: "Disable VNC", icon: ToggleRight },
              {
                title: "Cancel Service",
                icon: X,
                action: "cancel",
                className: "text-red-600 hover:bg-red-50",
              },
            ].map((item, index) => (
              <MenuItem
                key={index}
                className={`flex items-center gap-2 ${item.className || ""}`}
                onClick={() =>
                  item.action === "cancel" &&
                  onCancelClick &&
                  onCancelClick(server)
                }
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                {item.title}
              </MenuItem>
            ))}
          </MenuList>
        )}
      </Menu>
    </div>
  );
};

// Cloud-Init Toggle Component (Real Implementation)
const CloudInitToggle = ({ server, isEnabled, onToggle, isLoading }) => {
  return (
    <button
      onClick={() => onToggle(server.id)}
      disabled={isLoading}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        isEnabled ? "bg-blue-600" : "bg-gray-200"
      } ${isLoading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
      title={`Cloud-Init ${
        isEnabled ? "activé" : "désactivé"
      } (pour prochaine réinstallation)`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
          isEnabled ? "translate-x-6" : "translate-x-1"
        }`}
      />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </button>
  );
};

const OrderSteps = ({ currentStatus }) => {
  const steps = [
    { status: OrderStatus.PENDING, label: "En attente", icon: Clock },
    { status: OrderStatus.PROCESSING, label: "En cours", icon: Rocket },
    { status: OrderStatus.COMPLETED, label: "Terminé", icon: CheckCircle2 },
  ];

  const getStepStatus = (stepStatus) => {
    const statusOrder = [
      OrderStatus.PENDING,
      OrderStatus.PROCESSING,
      OrderStatus.COMPLETED,
    ];
    const currentIndex = statusOrder.indexOf(currentStatus);
    const stepIndex = statusOrder.indexOf(stepStatus);

    if (stepIndex < currentIndex) return "completed";
    if (stepIndex === currentIndex) return "current";
    return "upcoming";
  };

  return (
    <div className="flex items-center justify-between">
      {steps.map((step, index) => {
        const status = getStepStatus(step.status);
        const Icon = step.icon;

        return (
          <div key={step.status} className="flex items-center">
            <div className="flex flex-col items-center">
              <div
                className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center ${
                  status === "completed"
                    ? "bg-green-500 text-white"
                    : status === "current"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-400"
                }`}
              >
                <Icon className="w-4 h-4 md:w-5 md:h-5" />
              </div>
              <Typography className="text-xs md:text-sm mt-2 text-center font-medium">
                {step.label}
              </Typography>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`flex-1 h-1 mx-2 md:mx-4 rounded ${
                  status === "completed" ? "bg-green-500" : "bg-gray-200"
                }`}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

const StatusBadge = ({ status }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case OrderStatus.PENDING:
        return { color: "amber", label: "En attente", icon: Clock };
      case OrderStatus.PROCESSING:
        return { color: "blue", label: "En cours", icon: Rocket };
      case OrderStatus.COMPLETED:
        return { color: "green", label: "Terminé", icon: CheckCircle2 };
      case OrderStatus.CANCELLED:
        return { color: "red", label: "Annulé", icon: AlertCircle };
      default:
        return { color: "gray", label: "Inconnu", icon: AlertCircle };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Chip
      value={config.label}
      color={config.color}
      className="text-xs font-medium"
      icon={<Icon className="w-3 h-3" />}
    />
  );
};

const RescueModal = ({
  open,
  onClose,
  selectedInstanceId,
  onRescueSuccess,
}) => {
  const [rescueImages, setRescueImages] = useState([]);
  const [selectedRescueImage, setSelectedRescueImage] = useState(null);
  const [imagesLoading, setImagesLoading] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [sendPasswordEmail, setSendPasswordEmail] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Load rescue images when modal opens
  useEffect(() => {
    if (open) {
      console.log("🛡️ Modal opened, loading rescue images...");
      console.log("🛡️ Current rescueImages state:", rescueImages);
      loadRescueImages();
    } else {
      // Reset state when modal closes
      setError("");
      setPassword("");
      setConfirmPassword("");
    }
  }, [open]);

  const loadRescueImages = async () => {
    try {
      setImagesLoading(true);
      console.log("🛡️ Loading rescue system images...");

      const response = await vpsService.getRescueImages();
      console.log("🛡️ Full rescue images response:", response);

      // Handle different response structures
      let images = [];
      if (response.data && Array.isArray(response.data)) {
        images = response.data;
      } else if (
        response.data &&
        response.data.data &&
        Array.isArray(response.data.data)
      ) {
        images = response.data.data;
      } else if (
        response.data &&
        response.data.images &&
        Array.isArray(response.data.images)
      ) {
        images = response.data.images;
      } else {
        console.warn("🛡️ Unexpected response structure, using fallback images");
        images = [];
      }

      console.log("🛡️ Processed images:", images);

      // Ensure images is always an array
      if (!Array.isArray(images)) {
        console.warn("🛡️ Images is not an array, using fallback");
        images = [];
      }

      // If no images from API, use fallback
      if (images.length === 0) {
        console.log("🛡️ No images from API, using fallback images");
        images = [
          {
            id: "debian-rescue",
            name: "Debian Rescue (recommended)",
            description:
              "Debian-based rescue system with common recovery tools",
          },
          {
            id: "ubuntu-rescue",
            name: "Ubuntu Rescue",
            description: "Ubuntu-based rescue system",
          },
          {
            id: "centos-rescue",
            name: "CentOS Rescue",
            description: "CentOS-based rescue system",
          },
        ];
      }

      setRescueImages(images);

      // Set the first image as default selection
      if (images.length > 0) {
        setSelectedRescueImage(images[0]);
      }

      console.log(`✅ Loaded ${images.length} rescue images`);
    } catch (error) {
      console.error("❌ Failed to load rescue images:", error);
      // Set default fallback images
      const fallbackImages = [
        {
          id: "debian-rescue",
          name: "Debian Rescue (recommended)",
          description: "Debian-based rescue system with common recovery tools",
        },
        {
          id: "ubuntu-rescue",
          name: "Ubuntu Rescue",
          description: "Ubuntu-based rescue system",
        },
      ];
      setRescueImages(fallbackImages);
      setSelectedRescueImage(fallbackImages[0]);
    } finally {
      setImagesLoading(false);
    }
  };

  const handleGeneratePassword = () => {
    const generatedPassword = Math.random().toString(36).slice(-8);
    setPassword(generatedPassword);
    setConfirmPassword(generatedPassword);
  };

  const validateForm = () => {
    if (!password) {
      setError("Password is required");
      return false;
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return false;
    }
    if (password.length < 6) {
      setError("Password must be at least 6 characters long");
      return false;
    }
    setError("");
    return true;
  };

  const handleStartRescue = async () => {
    if (!validateForm()) return;
    if (!selectedInstanceId) {
      setError("No VPS instance selected");
      return;
    }

    setLoading(true);
    setError("");

    try {
      console.log(`🛡️ Starting rescue system for VPS ${selectedInstanceId}`);
      console.log(`🛡️ Selected rescue image:`, selectedRescueImage);

      // Prepare rescue options
      const rescueOptions = {};

      // Add password if provided
      if (password) {
        rescueOptions.rootPassword = password;
      }

      // Add selected image if available
      if (selectedRescueImage?.id) {
        rescueOptions.imageId = selectedRescueImage.id;
      }

      console.log(`🛡️ Rescue options:`, rescueOptions);

      // Use the executeVPSAction method which calls the backend API correctly
      const response = await vpsService.executeVPSAction(
        selectedInstanceId,
        "rescue",
        rescueOptions
      );

      console.log("✅ Rescue API response:", response);
      console.log("✅ Response structure:", JSON.stringify(response, null, 2));

      // Check if the operation was actually successful
      const isSuccess = response.success || response.data?.success;
      console.log("✅ Success check:", {
        responseSuccess: response.success,
        dataSuccess: response.data?.success,
        finalIsSuccess: isSuccess,
      });

      if (isSuccess) {
        // Show success modal instead of alert
        const rescueData = {
          instanceId: selectedInstanceId,
          rootPassword:
            response.data?.data?.rootPassword ||
            response.data?.rootPassword ||
            null,
          message: "Rescue system started successfully!",
        };

        // Call the success callback to show the success modal
        if (onRescueSuccess) {
          onRescueSuccess(rescueData);
        }

        // Close modal
        onClose();
      } else {
        // Handle case where API returns success: false
        throw new Error(response.message || "Rescue system failed to start");
      }
    } catch (error) {
      console.error("❌ Failed to start rescue system:", error);
      console.error("❌ Error response:", error.response);

      let errorMessage = "Failed to start rescue system. Please try again.";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${
        open ? "block" : "hidden"
      }`}
    >
      <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
        <h3 className="text-lg font-semibold mb-4 text-blue-600">
          Rescue System
        </h3>
        <div className="space-y-4">
          {/* Rescue System Version Dropdown */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Rescue System Version
            </label>
            {imagesLoading ? (
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500">
                Loading rescue images...
              </div>
            ) : (
              <select
                value={selectedRescueImage?.id || ""}
                onChange={(e) => {
                  const safeImages = Array.isArray(rescueImages)
                    ? rescueImages
                    : [];
                  const selected = safeImages.find(
                    (img) => img.id === e.target.value
                  );
                  setSelectedRescueImage(selected);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={
                  !Array.isArray(rescueImages) || rescueImages.length === 0
                }
              >
                {!Array.isArray(rescueImages) || rescueImages.length === 0 ? (
                  <option>No rescue images available</option>
                ) : (
                  rescueImages.map((image) => (
                    <option
                      key={image?.id || Math.random()}
                      value={image?.id || ""}
                    >
                      {image?.name || "Unknown Image"}
                    </option>
                  ))
                )}
              </select>
            )}
            {selectedRescueImage?.description && (
              <p className="text-sm text-gray-600 mt-1">
                {selectedRescueImage.description}
              </p>
            )}
          </div>

          {/* Password Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <div className="flex items-center gap-2">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Button
                variant="outlined"
                color="blue"
                size="sm"
                onClick={() => setShowPassword((prev) => !prev)}
              >
                {showPassword ? "Hide" : "Show"}
              </Button>
            </div>
          </div>

          {/* Confirm Password Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password (confirm)
            </label>
            <div className="flex items-center gap-2">
              <input
                type={showPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Button
                variant="outlined"
                color="blue"
                size="sm"
                onClick={handleGeneratePassword}
              >
                Generate
              </Button>
            </div>
          </div>

          {/* Checkbox */}
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="send-password-email"
              checked={sendPasswordEmail}
              onChange={(e) => setSendPasswordEmail(e.target.checked)}
              className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label
              htmlFor="send-password-email"
              className="text-sm text-gray-700"
            >
              Send password unencryptedly via e-mail (not recommended)
            </label>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {/* Warning Message */}
          <div className="p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-md">
            <p className="text-sm">
              <strong>Warning:</strong> Starting the rescue system will reboot
              your VPS. All running processes will be stopped. Make sure to save
              your work before proceeding.
            </p>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outlined"
            color="gray"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            color="orange"
            onClick={handleStartRescue}
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Starting...
              </>
            ) : (
              <>
                <Shield className="w-4 h-4" />
                Start Rescue System
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

// Rescue Success Modal Component
const RescueSuccessModal = ({ open, onClose, rescueData }) => {
  if (!open || !rescueData) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-gray-900">
              🛡️ Rescue System Started Successfully!
            </h3>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-3">
            Your VPS will reboot into rescue mode within 2-5 minutes.
          </p>

          {rescueData.rootPassword && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-3">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-yellow-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-yellow-800">
                    Generated Root Password
                  </h4>
                  <div className="mt-1">
                    <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                      {rescueData.rootPassword}
                    </code>
                  </div>
                  <p className="text-xs text-yellow-700 mt-1">
                    Please save this password as it will not be shown again.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <h4 className="text-sm font-medium text-blue-800 mb-2">
              What's next:
            </h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Wait 2-5 minutes for the reboot to complete</li>
              <li>• The rescue button will turn orange when active</li>
              <li>• Connect via SSH to access the rescue system</li>
              <li>
                • Use the 'Restart' button when you're done to exit rescue mode
              </li>
            </ul>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

export default function HostingOrdersPage() {
  const t = useTranslations("client");
  const [subOrders, setSubOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [activeTab, setActiveTab] = useState("vps");
  const localeActive = useLocale();
  const [showRescueModal, setShowRescueModal] = useState(false);
  const [selectedInstanceForRescue, setSelectedInstanceForRescue] =
    useState(null);
  const [showRescueSuccessModal, setShowRescueSuccessModal] = useState(false);
  const [rescueSuccessData, setRescueSuccessData] = useState(null);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [selectedInstanceForCancellation, setSelectedInstanceForCancellation] =
    useState(null);
  const [cancellationLoading, setCancellationLoading] = useState(false);

  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const item = searchParams.get("item");
  const categoryName = "Hosting";

  const handleRescueClick = (instanceId) => {
    setSelectedInstanceForRescue(instanceId);
    setShowRescueModal(true);
  };

  const handleRescueSuccess = (rescueData) => {
    setRescueSuccessData(rescueData);
    setShowRescueSuccessModal(true);

    // Refresh VPS data immediately and then again after a delay
    console.log("🔄 Refreshing VPS data after rescue start...");
    refreshVpsData();

    // Refresh again after 5 seconds to catch the rescue mode status
    setTimeout(() => {
      console.log("🔄 Second refresh to catch rescue mode status...");
      refreshVpsData();
    }, 5000);

    // And one more time after 10 seconds to ensure we catch the status
    setTimeout(() => {
      console.log("🔄 Final refresh to ensure rescue mode is detected...");
      refreshVpsData();
    }, 10000);
  };

  const handleCancelClick = (vpsInstance) => {
    setSelectedInstanceForCancellation(vpsInstance);
    setShowCancellationModal(true);
  };

  const handleCancellationConfirm = async (cancellationData) => {
    if (!selectedInstanceForCancellation) return;

    setCancellationLoading(true);
    try {
      console.log(
        "🚫 Cancelling VPS instance:",
        selectedInstanceForCancellation.instanceId
      );
      console.log("📝 Cancellation data:", cancellationData);

      const response = await vpsService.cancelInstance(
        selectedInstanceForCancellation.instanceId,
        cancellationData
      );

      console.log("✅ VPS cancellation successful:", response);

      toast.success("VPS instance cancellation request submitted successfully");

      // Close modal and reset state
      setShowCancellationModal(false);
      setSelectedInstanceForCancellation(null);

      // Refresh VPS data to reflect the cancellation
      refreshVpsData();
    } catch (error) {
      console.error("❌ VPS cancellation failed:", error);
      toast.error(
        error.response?.data?.message || "Failed to cancel VPS instance"
      );
    } finally {
      setCancellationLoading(false);
    }
  };

  const handleCancellationClose = () => {
    if (!cancellationLoading) {
      setShowCancellationModal(false);
      setSelectedInstanceForCancellation(null);
    }
  };

  // Helper function to determine button states based on VPS status
  const getButtonStates = (server) => {
    const status = server.status?.toLowerCase();
    const isInRescue = server.isInRescue || status === "rescue";

    return {
      start: {
        enabled: !isInRescue && status === "stopped",
        loading: false, // No loading state since we go directly to 'running'
        className:
          !isInRescue && status === "stopped"
            ? "bg-green-100 text-green-700 hover:bg-green-200 hover:shadow-md"
            : "bg-gray-100 text-gray-400 cursor-not-allowed",
        title: isInRescue
          ? "Cannot start VPS while in rescue mode"
          : status === "running"
          ? "VPS is already running"
          : "Start VPS",
      },
      stop: {
        enabled: !isInRescue && status === "running",
        loading: false, // No loading state since we go directly to 'stopped'
        className:
          !isInRescue && status === "running"
            ? "bg-red-100 text-red-700 hover:bg-red-200 hover:shadow-md"
            : "bg-gray-100 text-gray-400 cursor-not-allowed",
        title: isInRescue
          ? "Cannot stop VPS while in rescue mode"
          : status === "stopped"
          ? "VPS is already stopped"
          : "Stop VPS",
      },
      restart: {
        enabled: true, // Always enabled
        loading: false, // No loading state since we go directly to 'running'
        className:
          "bg-blue-100 text-blue-700 hover:bg-blue-200 hover:shadow-md",
        title: isInRescue
          ? "Restart VPS (will exit rescue mode and set to running)"
          : "Restart VPS",
      },
      rescue: {
        enabled: status !== "rescue", // Only disabled when in rescue mode
        active: isInRescue,
        className: isInRescue
          ? "bg-orange-100 text-orange-700 cursor-not-allowed"
          : "bg-yellow-100 text-yellow-700 hover:bg-yellow-200 hover:shadow-md",
        title: isInRescue
          ? "VPS is in rescue mode - use restart to exit"
          : "Start rescue mode",
      },
      cloudInit: {
        enabled: !isInRescue && ["running", "stopped"].includes(status),
        className:
          !isInRescue && ["running", "stopped"].includes(status)
            ? "bg-purple-100 text-purple-700 hover:bg-purple-200 hover:shadow-md"
            : "bg-gray-100 text-gray-400 cursor-not-allowed",
        title: isInRescue
          ? "Cannot toggle cloud-init while in rescue mode"
          : "Toggle Cloud-Init",
      },
      reinstall: {
        enabled: !isInRescue && ["running", "stopped"].includes(status),
        className:
          !isInRescue && ["running", "stopped"].includes(status)
            ? "bg-red-100 text-red-700 hover:bg-red-200 hover:shadow-md"
            : "bg-gray-100 text-gray-400 cursor-not-allowed",
        title: isInRescue
          ? "Cannot reinstall VPS while in rescue mode"
          : "Reinstall VPS",
      },
    };
  };

  // Helper function to get status colors
  const getStatusColor = (status) => {
    const statusLower = status?.toLowerCase();

    switch (statusLower) {
      case "running":
        return {
          bg: "bg-green-100",
          text: "text-green-800",
          dot: "bg-green-500",
        };
      case "stopped":
        return {
          bg: "bg-red-100",
          text: "text-red-800",
          dot: "bg-red-500",
        };
      case "rescue":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-800",
          dot: "bg-yellow-500",
        };
      default:
        return {
          bg: "bg-gray-100",
          text: "text-gray-800",
          dot: "bg-gray-500",
        };
    }
  };

  // VPS instances data from API
  const [vpsServers, setVpsServers] = useState([]);
  const [vpsLoading, setVpsLoading] = useState(true);
  const [vpsError, setVpsError] = useState(null);
  const [lastRefresh, setLastRefresh] = useState(null);

  // États pour les snapshots
  const [showSnapshotModal, setShowSnapshotModal] = useState(false);
  const [selectedVpsForSnapshot, setSelectedVpsForSnapshot] = useState(null);
  const [snapshotName, setSnapshotName] = useState("");
  const [snapshotDescription, setSnapshotDescription] = useState("");
  const [snapshots, setSnapshots] = useState([]);
  const [showSnapshotsTable, setShowSnapshotsTable] = useState(false);
  const [snapshotsLoading, setSnapshotsLoading] = useState(false);
  const [snapshotQuota, setSnapshotQuota] = useState(null);
  const [quotaError, setQuotaError] = useState(null);

  // États pour les confirmations
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);

  // États pour le modal de rename
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [renameAction, setRenameAction] = useState(null);
  const [newSnapshotName, setNewSnapshotName] = useState("");

  // États pour les actions en cours
  const [actionsInProgress, setActionsInProgress] = useState({});
  const [actionProgress, setActionProgress] = useState({});

  // États pour Cloud-Init (configuration pour prochaine réinstallation)
  const [cloudInitStates, setCloudInitStates] = useState({});

  // États pour la réinstallation
  const [showReinstallModal, setShowReinstallModal] = useState(false);
  const [selectedVpsForReinstall, setSelectedVpsForReinstall] = useState(null);
  const [reinstallLoading, setReinstallLoading] = useState(false);

  // États pour la réinitialisation de mot de passe
  const [showPasswordResetModal, setShowPasswordResetModal] = useState(false);
  const [selectedVpsForPasswordReset, setSelectedVpsForPasswordReset] =
    useState(null);

  // États pour les notifications de succès
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [successNotificationAction, setSuccessNotificationAction] =
    useState("");
  const [successNotificationServer, setSuccessNotificationServer] =
    useState(null);

  // États pour les autres notifications
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [errorNotificationMessage, setErrorNotificationMessage] = useState("");
  const [showInfoNotification, setShowInfoNotification] = useState(false);
  const [infoNotificationMessage, setInfoNotificationMessage] = useState("");
  const [showConsoleNotification, setShowConsoleNotification] = useState(false);

  // Mock data for Dedicated servers
  const [dedicatedServers, setDedicatedServers] = useState([
    {
      id: "ds-001",
      server: "*************",
      defaultUser: "root",
      hostSystem: "15432",
      status: "running",
    },
    {
      id: "ds-002",
      server: "*************",
      defaultUser: "admin",
      hostSystem: "28901",
      status: "stopped",
    },
  ]);

  // Mock data for Shared servers
  const [sharedServers, setSharedServers] = useState([
    {
      id: "sh-001",
      server: "shared01.ztech.ma",
      resources: "2GB RAM, 50GB SSD",
      status: "running",
      price: "99 MAD/mois",
    },
    {
      id: "sh-002",
      server: "shared02.ztech.ma",
      resources: "4GB RAM, 100GB SSD",
      status: "running",
      price: "199 MAD/mois",
    },
  ]);

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setOrderId(item);
      setOpenModal(true);
    }
  }, [status, item]);

  useEffect(() => {
    const getSubOrders = async () => {
      try {
        const res = await orderService.getSubOrdersByCategory(categoryName);
        setSubOrders(res.data.subOrders);
      } catch (error) {
        console.error("Error getting suborders", error);
      } finally {
        setLoading(false);
      }
    };
    getSubOrders();
  }, [categoryName]);

  // Fetch VPS instances function (moved outside useEffect for reusability)
  const fetchVpsInstances = async () => {
    try {
      setVpsLoading(true);
      setVpsError(null);

      const response = await vpsService.getUserInstances();
      console.log("🔍 VPS Instances API Response:", response);
      console.log("🔍 Response data:", response.data);
      console.log("🔍 Response success:", response.success);
      console.log("🔍 Response meta:", response.meta);

      // Handle different response structures
      let instancesData = [];
      if (
        response.data &&
        response.data.data &&
        Array.isArray(response.data.data)
      ) {
        // Structure: { data: { success: true, data: [...] } }
        instancesData = response.data.data;
        console.log("🔍 Using response.data.data:", instancesData);
      } else if (response.data && Array.isArray(response.data)) {
        instancesData = response.data;
        console.log("🔍 Using response.data:", instancesData);
      } else if (
        response.data &&
        response.data.instances &&
        Array.isArray(response.data.instances)
      ) {
        instancesData = response.data.instances;
        console.log("🔍 Using response.data.instances:", instancesData);
      } else if (Array.isArray(response)) {
        instancesData = response;
        console.log("🔍 Using response:", instancesData);
      }

      console.log("🔍 Final instancesData:", instancesData);

      // Transform API response to match UI structure (adapté pour Contabo)
      const transformedInstances = await Promise.all(
        instancesData.map(async (instance) => {
          console.log("🔍 Transforming instance:", instance);
          console.log("🔍 Instance status from API:", instance.status);
          console.log("🔍 Instance raw data:", instance.raw);

          // Check rescue status for this instance
          let isInRescue = false;
          try {
            const rescueStatusResponse = await vpsService.checkRescueStatus(
              instance.instanceId
            );
            isInRescue =
              rescueStatusResponse.data?.data?.isInRescue ||
              rescueStatusResponse.data?.isInRescue ||
              false;
            console.log(
              `🛡️ Instance ${instance.instanceId} rescue status from API: ${isInRescue}`
            );

            // Also check the raw status directly from the instance data as a fallback
            if (!isInRescue) {
              const rawStatus = instance.raw?.status?.toLowerCase();
              const arrayRawStatus =
                Array.isArray(instance.raw) && instance.raw.length > 0
                  ? instance.raw[0]?.status?.toLowerCase()
                  : null;

              if (rawStatus === "rescue" || arrayRawStatus === "rescue") {
                isInRescue = true;
                console.log(
                  `🛡️ Instance ${
                    instance.instanceId
                  } rescue status detected from raw data: ${
                    rawStatus || arrayRawStatus
                  }`
                );
              }
            }
          } catch (error) {
            console.warn(
              `⚠️ Could not check rescue status for instance ${instance.instanceId}:`,
              error.message
            );

            // Fallback: check raw status directly from instance data
            const rawStatus = instance.raw?.status?.toLowerCase();
            const arrayRawStatus =
              Array.isArray(instance.raw) && instance.raw.length > 0
                ? instance.raw[0]?.status?.toLowerCase()
                : null;

            if (rawStatus === "rescue" || arrayRawStatus === "rescue") {
              isInRescue = true;
              console.log(
                `🛡️ Instance ${
                  instance.instanceId
                } rescue status detected from fallback: ${
                  rawStatus || arrayRawStatus
                }`
              );
            }
          }
          // Créer le nom complet comme Contabo : "IP PRODUCT_NAME-ID"
          const ip = instance.ip || instance.ipAddress || "N/A";
          const productName =
            instance.raw?.productName || instance.productName || "CLOUD VPS";
          const instanceId = instance.instanceId;
          const fullServerName =
            ip !== "N/A"
              ? `${ip}\n${productName} ${instanceId}`
              : `${productName} ${instanceId}`;

          console.log(
            `🔍 Creating server name: IP=${ip}, Product=${productName}, ID=${instanceId}, Full=${fullServerName}`
          );

          return {
            id: instance.instanceId,
            server: fullServerName,
            serverIp: ip,
            serverName: `${productName}-${instanceId}`,
            defaultUser:
              instance.raw?.defaultUser || instance.defaultUser || "root",
            hostSystem:
              instance.raw?.vHostName ||
              instance.hostId ||
              instance.hostSystem ||
              instance.instanceId,
            status: instance.status || "unknown",
            name: instance.name || instance.displayName,
            plan: {
              name:
                instance.raw?.productName ||
                instance.plan ||
                instance.productId ||
                "N/A",
              id: instance.productId || instance.plan,
            },
            region: instance.region || "N/A",
            dataCenter: instance.dataCenter || "N/A",
            ipv4: instance.ip,
            ipv6: instance.ipv6,
            createdAt: instance.createdAt,
            // Données supplémentaires Contabo
            specs: {
              ram: instance.raw?.ramMb
                ? `${Math.round(instance.raw.ramMb / 1024)} GB`
                : "N/A",
              cpu: instance.raw?.cpuCores
                ? `${instance.raw.cpuCores} cores`
                : "N/A",
              disk: instance.raw?.diskMb
                ? `${Math.round(instance.raw.diskMb / 1024)} GB`
                : "N/A",
              osType: instance.raw?.osType || "N/A",
            },
            // Rescue mode status
            isInRescue: isInRescue,
          };
        })
      );

      console.log(
        "🔍 Final transformed instances with status:",
        transformedInstances.map((i) => ({
          id: i.id,
          name: i.name,
          status: i.status,
        }))
      );
      setVpsServers(transformedInstances);
      setLastRefresh(new Date());

      // Initialiser les états Cloud-Init depuis localStorage + API
      const savedCloudInitStates = JSON.parse(
        localStorage.getItem("cloudInitStates") || "{}"
      );
      const initialCloudInitStates = {};

      transformedInstances.forEach((instance) => {
        // Priorité: localStorage > API > false par défaut
        const apiState = instance.cloudInitEnabled || false;
        const savedState = savedCloudInitStates[instance.id];
        const finalState = savedState !== undefined ? savedState : apiState;

        initialCloudInitStates[instance.id] = finalState;
        console.log(
          `🔍 Cloud-Init state for VPS ${instance.id}: API=${apiState}, Saved=${savedState}, Final=${finalState}`
        );
      });

      setCloudInitStates(initialCloudInitStates);
      console.log(`🔍 Initial Cloud-Init states:`, initialCloudInitStates);

      // Cloud-Init géré via localStorage - pas de synchronisation automatique
      console.log(`ℹ️ Cloud-Init states initialized from localStorage`);
    } catch (error) {
      console.error("Error fetching VPS instances:", error);
      setVpsError(error.message || "Failed to load VPS instances");
      // Keep empty array on error
      setVpsServers([]);
    } finally {
      setVpsLoading(false);
    }
  };

  // useEffect to call fetchVpsInstances on component mount
  useEffect(() => {
    fetchVpsInstances();
  }, []);

  // Function to refresh VPS data (including rescue status)
  const refreshVpsData = async () => {
    console.log("🔄 Refreshing VPS data...");
    try {
      setVpsLoading(true);
      const response = await vpsService.getUserInstances();

      let instancesData = [];
      if (
        response.data &&
        response.data.data &&
        Array.isArray(response.data.data)
      ) {
        instancesData = response.data.data;
      } else if (response.data && Array.isArray(response.data)) {
        instancesData = response.data;
      }

      // Transform and check rescue status for each instance
      const transformedInstances = await Promise.all(
        instancesData.map(async (instance) => {
          let isInRescue = false;
          try {
            // Use instanceId (Contabo ID) for API calls, not MongoDB _id
            const contaboInstanceId =
              instance.instanceId || instance.instanceId;
            console.log(
              `🔍 Checking rescue status for instanceId: ${contaboInstanceId} (MongoDB _id: ${instance.instanceId})`
            );

            const rescueStatusResponse = await vpsService.checkRescueStatus(
              contaboInstanceId
            );
            isInRescue =
              rescueStatusResponse.data?.data?.isInRescue ||
              rescueStatusResponse.data?.isInRescue ||
              false;

            // Also check raw status directly as fallback
            if (!isInRescue) {
              const rawStatus = instance.raw?.status?.toLowerCase();
              const arrayRawStatus =
                Array.isArray(instance.raw) && instance.raw.length > 0
                  ? instance.raw[0]?.status?.toLowerCase()
                  : null;
              if (rawStatus === "rescue" || arrayRawStatus === "rescue") {
                isInRescue = true;
              }
            }
          } catch (error) {
            const contaboInstanceId =
              instance.instanceId || instance.instanceId;
            console.warn(
              `⚠️ Could not check rescue status for instance ${contaboInstanceId}`
            );

            // Fallback: check raw status directly
            const rawStatus = instance.raw?.status?.toLowerCase();
            const arrayRawStatus =
              Array.isArray(instance.raw) && instance.raw.length > 0
                ? instance.raw[0]?.status?.toLowerCase()
                : null;
            if (rawStatus === "rescue" || arrayRawStatus === "rescue") {
              isInRescue = true;
            }
          }

          const ip = instance.ip || instance.ipAddress || "N/A";
          const productName =
            instance.raw?.productName || instance.productName || "CLOUD VPS";
          const instanceId = instance.instanceId || instance.instanceId;
          const fullServerName =
            ip !== "N/A"
              ? `${ip}\n${productName} ${instanceId}`
              : `${productName} ${instanceId}`;

          return {
            id: instance.instanceId || instance.instanceId, // Use instanceId first (Contabo ID), fallback to MongoDB _id
            instanceId: instance.instanceId, // Always include the actual instanceId
            mongoId: instance.instanceId, // Keep MongoDB _id for reference
            server: fullServerName,
            serverIp: ip,
            serverName: `${productName}-${instanceId}`,
            defaultUser:
              instance.raw?.defaultUser || instance.defaultUser || "root",
            hostSystem:
              instance.raw?.vHostName ||
              instance.hostId ||
              instance.hostSystem ||
              instance.instanceId,
            status: instance.status || "unknown",
            name: instance.name || instance.displayName,
            plan: {
              name:
                instance.raw?.productName ||
                instance.plan ||
                instance.productId ||
                "N/A",
              id: instance.productId || instance.plan,
            },
            region: instance.region || "N/A",
            dataCenter: instance.dataCenter || "N/A",
            ipv4: instance.ip,
            ipv6: instance.ipv6,
            createdAt: instance.createdAt,
            specs: {
              ram: instance.raw?.ramMb
                ? `${Math.round(instance.raw.ramMb / 1024)} GB`
                : "N/A",
              cpu: instance.raw?.cpuCores
                ? `${instance.raw.cpuCores} cores`
                : "N/A",
              disk: instance.raw?.diskMb
                ? `${Math.round(instance.raw.diskMb / 1024)} GB`
                : "N/A",
              osType: instance.raw?.osType || "N/A",
            },
            isInRescue: isInRescue,
          };
        })
      );

      setVpsServers(transformedInstances);
      setLastRefresh(new Date());
      console.log("✅ VPS data refreshed successfully");
    } catch (error) {
      console.error("❌ Failed to refresh VPS data:", error);
      setVpsError(error.message);
    } finally {
      setVpsLoading(false);
    }
  };

  // VPS control functions
  const handleVpsAction = async (instanceId, action) => {
    let actionKey = null; // Declare actionKey at function scope

    try {
      console.log(`🎯 Executing VPS Action: ${action} on server ${instanceId}`);
      console.log(
        `🔍 Instance ID type: ${typeof instanceId}, value: "${instanceId}"`
      );

      // Gestion spéciale pour la console
      if (action === "console") {
        // Ouvrir la console Contabo dans un nouvel onglet
        const consoleUrl = `https://my.contabo.com/api/v1/compute/instances/${instanceId}/console`;
        window.open(consoleUrl, "_blank");
        setShowConsoleNotification(true);
        return;
      }

      // Messages et durées estimées pour chaque action
      const actionConfig = {
        start: { title: "Démarrage du VPS", duration: 30 },
        stop: { title: "Arrêt du VPS", duration: 20 },
        restart: { title: "Redémarrage du VPS", duration: 45 },
        "cloud-init": { title: "Cloud-Init", duration: 15 },
        reinstall: { title: "Réinstallation du VPS", duration: 300 },
        rescue: { title: "Système de secours", duration: 60 },
      };

      const config = actionConfig[action] || {
        title: `Action ${action}`,
        duration: 30,
      };

      // Démarrer l'indicateur de progression
      actionKey = startActionProgress(instanceId, action, config.duration);

      // Appeler l'API Contabo
      const response = await vpsService.executeVPSAction(instanceId, action);

      console.log(`✅ VPS Action ${action} completed:`, response);

      if (response.data.success) {
        // Compléter la progression
        if (actionKey) {
          completeActionProgress(actionKey);
        }

        // Mettre à jour l'état local pour refléter l'action
        setVpsServers((prev) =>
          prev.map((server) =>
            server.id === instanceId
              ? {
                  ...server,
                  status:
                    action === "start"
                      ? "starting"
                      : action === "stop"
                      ? "stopping"
                      : "restarting",
                }
              : server
          )
        );

        // Rafraîchir la liste des VPS après l'action
        setTimeout(async () => {
          try {
            console.log("🔄 Refreshing VPS list after action...");
            const response = await vpsService.getUserInstances();
            console.log("🔍 Refresh response:", response);

            // Handle response structure (même logique que le useEffect initial)
            let instancesData = [];
            if (
              response.data &&
              response.data.data &&
              Array.isArray(response.data.data)
            ) {
              instancesData = response.data.data;
              console.log(
                "🔍 Using response.data.data for refresh:",
                instancesData
              );
            } else if (response.data && Array.isArray(response.data)) {
              instancesData = response.data;
              console.log("🔍 Using response.data for refresh:", instancesData);
            }

            // Transform API response (même logique que le useEffect initial)
            const transformedInstances = instancesData.map((instance) => {
              console.log("🔍 Transforming instance for refresh:", instance);
              console.log(
                "🔍 REFRESH - Instance status from API:",
                instance.status
              );
              console.log(
                "🔍 REFRESH - Instance raw status:",
                instance.raw?.status
              );

              const finalStatus = instance.status || "unknown";
              console.log("🔍 REFRESH - Final status assigned:", finalStatus);

              // Créer le nom complet comme Contabo : "IP PRODUCT_NAME-ID"
              const ip = instance.ip || instance.ipAddress || "N/A";
              const productName =
                instance.raw?.productName ||
                instance.productName ||
                "CLOUD VPS";
              const instanceId = instance.instanceId || instance.instanceId;
              const fullServerName =
                ip !== "N/A"
                  ? `${ip}\n${productName} ${instanceId}`
                  : `${productName} ${instanceId}`;

              return {
                id: instance.instanceId || instance.instanceId,
                server: fullServerName,
                serverIp: ip,
                serverName: `${productName}-${instanceId}`,
                defaultUser:
                  instance.raw?.defaultUser || instance.defaultUser || "root",
                hostSystem:
                  instance.raw?.vHostName ||
                  instance.hostId ||
                  instance.hostSystem ||
                  instance.instanceId,
                status: finalStatus,
                name: instance.name || instance.displayName,
                plan: {
                  name:
                    instance.raw?.productName ||
                    instance.plan ||
                    instance.productId ||
                    "N/A",
                  id: instance.productId || instance.plan,
                },
                region: instance.region || "N/A",
                dataCenter: instance.dataCenter || "N/A",
                ipv4: instance.ip,
                ipv6: instance.ipv6,
                createdAt: instance.createdAt,
                specs: {
                  ram: instance.raw?.ramMb
                    ? `${Math.round(instance.raw.ramMb / 1024)} GB`
                    : "N/A",
                  cpu: instance.raw?.cpuCores
                    ? `${instance.raw.cpuCores} cores`
                    : "N/A",
                  disk: instance.raw?.diskMb
                    ? `${Math.round(instance.raw.diskMb / 1024)} GB`
                    : "N/A",
                  osType: instance.raw?.osType || "N/A",
                },
              };
            });

            console.log("✅ Refreshed VPS list:", transformedInstances);
            console.log(
              "🔍 REFRESH - Final status in list:",
              transformedInstances.map((i) => ({ id: i.id, status: i.status }))
            );
            setVpsServers(transformedInstances);
          } catch (error) {
            console.error("❌ Error refreshing VPS instances:", error);
          }
        }, 3000);
      } else {
        // Arrêter la progression
        if (actionKey) {
          completeActionProgress(actionKey);
        }
      }
    } catch (error) {
      console.error(`❌ VPS Action ${action} failed:`, error);

      // Arrêter la progression
      if (actionKey) {
        completeActionProgress(actionKey);
      }

      // Gestion spéciale pour les erreurs "already running/stopped" et rescue mode
      if (error.message.includes("already running")) {
        // Forcer un rafraîchissement pour corriger l'affichage
        setTimeout(() => window.location.reload(), 2000);
      } else if (error.message.includes("already stopped")) {
        // Forcer un rafraîchissement pour corriger l'affichage
        setTimeout(() => window.location.reload(), 2000);
      } else if (
        error.message.toLowerCase().includes("rescue") ||
        error.message.toLowerCase().includes("reset password")
      ) {
        // VPS is in rescue mode
        let rescueMessage = `🛡️ VPS is currently in rescue mode!\n\n`;
        rescueMessage += `Contabo reports: "Actions are unavailable as VPS/VDS is in Rescue/Reset Password status"\n\n`;
        rescueMessage += `This confirms the rescue system is active and working correctly.\n\n`;
        rescueMessage += `What you can do:\n`;
        rescueMessage += `• Connect via SSH to access the rescue system\n`;
        rescueMessage += `• Use the "Status" button to verify rescue mode\n`;
        rescueMessage += `• Use the "Restart" button to exit rescue mode\n\n`;
        rescueMessage += `Note: You cannot start/stop a VPS while in rescue mode.`;

        setInfoNotificationMessage(rescueMessage);
        setShowInfoNotification(true);
        return; // Don't show the generic error
      }
    }
  };

  // Fonctions utilitaires pour les actions en cours
  const startActionProgress = (instanceId, action, estimatedDuration = 30) => {
    const actionKey = `${instanceId}-${action}`;
    setActionsInProgress((prev) => ({
      ...prev,
      [actionKey]: {
        instanceId,
        action,
        startTime: Date.now(),
        estimatedDuration: estimatedDuration * 1000, // en millisecondes
        progress: 0,
      },
    }));

    // Simuler la progression
    const interval = setInterval(() => {
      setActionProgress((prev) => {
        const current = prev[actionKey] || 0;
        const newProgress = Math.min(current + 100 / estimatedDuration, 95); // Max 95% jusqu'à completion

        if (newProgress >= 95) {
          clearInterval(interval);
        }

        return {
          ...prev,
          [actionKey]: newProgress,
        };
      });
    }, 1000);

    return actionKey;
  };

  const completeActionProgress = (actionKey) => {
    setActionProgress((prev) => ({
      ...prev,
      [actionKey]: 100,
    }));

    setTimeout(() => {
      setActionsInProgress((prev) => {
        const newState = { ...prev };
        delete newState[actionKey];
        return newState;
      });
      setActionProgress((prev) => {
        const newState = { ...prev };
        delete newState[actionKey];
        return newState;
      });
    }, 2000);
  };

  // Fonction pour gérer Cloud-Init (configuration pour prochaine réinstallation)
  const handleCloudInit = async (instanceId) => {
    // Vérifier l'état actuel (déclaré en dehors du try/catch pour être accessible partout)
    const currentState = cloudInitStates[instanceId] || false;
    const newState = !currentState;

    try {
      console.log(
        `☁️ Toggling Cloud-Init configuration for VPS: ${instanceId}`
      );

      // Mettre à jour l'état local immédiatement pour l'UI
      setCloudInitStates((prev) => ({
        ...prev,
        [instanceId]: newState,
      }));

      // Démarrer l'indicateur de progression
      const actionKey = startActionProgress(instanceId, "cloud-init", 15);

      // Appeler l'API pour mettre à jour la configuration
      const response = await vpsService.executeVPSAction(
        instanceId,
        "cloud-init"
      );

      if (response.data.success) {
        // Compléter la progression
        completeActionProgress(actionKey);
        console.log(
          `✅ Cloud-Init ${
            newState ? "enabled" : "disabled"
          } for next reinstall of VPS ${instanceId}`
        );

        // Sauvegarder l'état dans localStorage pour persistance
        const cloudInitStates = JSON.parse(
          localStorage.getItem("cloudInitStates") || "{}"
        );
        cloudInitStates[instanceId] = newState;
        localStorage.setItem(
          "cloudInitStates",
          JSON.stringify(cloudInitStates)
        );
        console.log(
          `💾 Cloud-Init state saved locally for VPS ${instanceId}: ${newState}`
        );

        // Cloud-Init configuré avec succès sur Contabo
        console.log(
          `✅ Cloud-Init ${
            newState ? "enabled" : "disabled"
          } on Contabo successfully`
        );
      } else {
        // Revenir à l'état précédent en cas d'erreur
        setCloudInitStates((prev) => ({
          ...prev,
          [instanceId]: currentState,
        }));
        completeActionProgress(actionKey);
      }
    } catch (error) {
      console.error(`❌ Cloud-Init configuration failed:`, error);

      // Revenir à l'état précédent en cas d'erreur
      setCloudInitStates((prev) => ({
        ...prev,
        [instanceId]: currentState,
      }));

      // Restaurer l'état sauvé en cas d'erreur
      const cloudInitStates = JSON.parse(
        localStorage.getItem("cloudInitStates") || "{}"
      );
      cloudInitStates[instanceId] = currentState;
      localStorage.setItem("cloudInitStates", JSON.stringify(cloudInitStates));
    }
  };

  // Cloud-Init géré via localStorage uniquement

  // Fonction pour ouvrir le modal de réinstallation
  const handleReinstallClick = (server) => {
    setSelectedVpsForReinstall(server);
    setShowReinstallModal(true);
  };

  // Fonction pour fermer le modal de réinstallation
  const handleCloseReinstallModal = () => {
    setShowReinstallModal(false);
    setSelectedVpsForReinstall(null);
    setReinstallLoading(false);
  };

  // Fonction pour exécuter la réinstallation
  const handleReinstall = async (reinstallData) => {
    if (!selectedVpsForReinstall) return;

    try {
      setReinstallLoading(true);
      console.log(
        `🔄 Starting reinstall for VPS ${selectedVpsForReinstall.id}`,
        reinstallData
      );

      const response = await vpsService.reinstallVPS(
        selectedVpsForReinstall.id,
        reinstallData
      );

      if (response.data.success) {
        console.log(
          `✅ VPS ${selectedVpsForReinstall.id} reinstallation started successfully`
        );

        // Fermer le modal
        handleCloseReinstallModal();

        // Afficher la notification de succès professionnelle
        setSuccessNotificationAction("reinstall");
        setSuccessNotificationServer(selectedVpsForReinstall);
        setShowSuccessNotification(true);

        // Rafraîchir la liste des VPS avec un délai pour permettre la mise à jour
        setTimeout(() => {
          fetchVpsInstances();
        }, 3000); // Attendre 3 secondes

        // Rafraîchir à nouveau après 30 secondes pour capturer le nouveau statut
        setTimeout(() => {
          fetchVpsInstances();
        }, 30000);
      } else {
        throw new Error(response.data.message || "Échec de la réinstallation");
      }
    } catch (error) {
      console.error(
        `❌ Reinstall failed for VPS ${selectedVpsForReinstall.id}:`,
        error
      );
      setErrorNotificationMessage(
        `Erreur lors de la réinstallation : ${error.message}`
      );
      setShowErrorNotification(true);
    } finally {
      setReinstallLoading(false);
    }
  };

  // Fonctions pour la réinitialisation de mot de passe
  const handlePasswordResetClick = (server) => {
    setSelectedVpsForPasswordReset(server);
    setShowPasswordResetModal(true);
  };

  const handleClosePasswordResetModal = () => {
    setShowPasswordResetModal(false);
    setSelectedVpsForPasswordReset(null);
  };

  const handlePasswordResetSuccess = (resetData) => {
    console.log("✅ Password reset completed successfully:", resetData);

    // Fermer le modal
    handleClosePasswordResetModal();

    // Afficher la notification de succès professionnelle
    setSuccessNotificationAction("reset-password");
    setSuccessNotificationServer(selectedVpsForPasswordReset);
    setShowSuccessNotification(true);

    // Rafraîchir la liste des VPS
    setTimeout(() => {
      fetchVpsInstances();
    }, 2000);
  };

  // Fonctions pour fermer les notifications
  const handleCloseSuccessNotification = () => {
    setShowSuccessNotification(false);
    setSuccessNotificationAction("");
    setSuccessNotificationServer(null);
  };

  const handleCloseErrorNotification = () => {
    setShowErrorNotification(false);
    setErrorNotificationMessage("");
  };

  const handleCloseInfoNotification = () => {
    setShowInfoNotification(false);
    setInfoNotificationMessage("");
  };

  const handleCloseConsoleNotification = () => {
    setShowConsoleNotification(false);
  };

  // Fonctions de gestion des snapshots
  const handleSnapshotClick = async (server) => {
    try {
      console.log(`📸 Opening snapshot management for VPS: ${server.id}`);
      setSelectedVpsForSnapshot(server);
      setQuotaError(null);

      // D'abord, charger les snapshots existants pour vérifier le quota
      console.log(`📸 Checking snapshot quota for VPS: ${server.id}`);
      await loadSnapshots(server.id);
      setShowSnapshotsTable(true);

      // Vérifier si on peut créer un nouveau snapshot
      const response = await vpsService.getVPSSnapshots(server.id);
      if (response.data.success) {
        const snapshotData = response.data.data;
        const quota = snapshotData.quota;

        if (quota && quota.canCreate) {
          // Quota OK → Ouvrir le modal de création
          console.log(
            `✅ Quota OK (${quota.current}/${quota.max}), opening creation modal`
          );
          setSnapshotName(`snapshot-${server.id}-${Date.now()}`);
          setSnapshotDescription(
            `Snapshot of ${
              server.name || server.server
            } created on ${new Date().toLocaleDateString()}`
          );
          setShowSnapshotModal(true);
        } else {
          // Quota atteint → Afficher le message d'erreur
          console.log(
            `❌ Quota exceeded (${quota.current}/${quota.max}), showing error message`
          );
          setQuotaError(
            `The maximum number of snapshots for this VPS has been reached (${quota.max}). You need to delete old snapshots before you can create new ones.`
          );
        }
      }
    } catch (error) {
      console.error(`❌ Error checking snapshot quota:`, error);
      // En cas d'erreur, afficher quand même le tableau des snapshots
      await loadSnapshots(server.id);
      setShowSnapshotsTable(true);
    }
  };

  const handleCreateSnapshot = async () => {
    let actionKey = null; // Declare actionKey at function scope

    try {
      console.log(`📸 Creating snapshot for VPS: ${selectedVpsForSnapshot.id}`);
      setQuotaError(null);

      // Démarrer l'indicateur de progression pour la création
      actionKey = startActionProgress(
        selectedVpsForSnapshot.id,
        "create-snapshot",
        60
      );

      const response = await vpsService.createVPSSnapshot(
        selectedVpsForSnapshot.id,
        snapshotName,
        snapshotDescription
      );

      if (response.data.success) {
        // Compléter la progression
        if (actionKey) {
          completeActionProgress(actionKey);
        }

        setShowSnapshotModal(false);

        // Charger et afficher les snapshots
        await loadSnapshots(selectedVpsForSnapshot.id);
        setShowSnapshotsTable(true);
      } else {
        if (actionKey) {
          completeActionProgress(actionKey);
        }
      }
    } catch (error) {
      console.error(`❌ Failed to create snapshot:`, error);

      // Complete the action progress
      if (actionKey) {
        completeActionProgress(actionKey);
      }

      // Vérifier si c'est une erreur de quota
      if (
        error.message.includes("maximum number of snapshots") ||
        error.message.includes("quota")
      ) {
        setQuotaError(error.message);
        setShowSnapshotModal(false);

        // Charger et afficher les snapshots pour permettre la suppression
        await loadSnapshots(selectedVpsForSnapshot.id);
        setShowSnapshotsTable(true);
      }
    }
  };

  const loadSnapshots = async (instanceId) => {
    try {
      setSnapshotsLoading(true);
      console.log(`📸 Loading snapshots for VPS: ${instanceId}`);

      const response = await vpsService.getVPSSnapshots(instanceId);

      if (response.data.success) {
        const snapshotData = response.data.data;
        setSnapshots(snapshotData.snapshots || []);
        setSnapshotQuota(snapshotData.quota || null);
        console.log(
          `✅ Loaded ${snapshotData.snapshots?.length || 0}/${
            snapshotData.quota?.max || "N/A"
          } snapshots`
        );
      } else {
        setSnapshots([]);
        setSnapshotQuota(null);
        console.log(`❌ Failed to load snapshots: ${response.data.message}`);
      }
    } catch (error) {
      console.error(`❌ Error loading snapshots:`, error);
      setSnapshots([]);
      setSnapshotQuota(null);
    } finally {
      setSnapshotsLoading(false);
    }
  };

  const handleSnapshotAction = async (
    action,
    snapshotId,
    currentName,
    newDescription
  ) => {
    try {
      console.log(
        `📸 Executing snapshot action: ${action} on snapshot ${snapshotId}`
      );

      // Pour le rename, ouvrir le modal de saisie
      if (action === "rename") {
        setRenameAction({
          snapshotId,
          currentName,
          description: newDescription,
          onConfirm: (newName) =>
            executeSnapshotAction(action, snapshotId, newName, newDescription),
        });
        setNewSnapshotName(currentName);
        setShowRenameModal(true);
        return;
      }

      // Pour les actions destructives, demander confirmation
      if (action === "rollback" || action === "delete") {
        const confirmMessages = {
          rollback:
            "Attention ! Le rollback va restaurer votre VPS à l'état du snapshot. Toutes les données créées après ce snapshot seront perdues. Continuer ?",
          delete:
            "Êtes-vous sûr de vouloir supprimer ce snapshot ? Cette action est irréversible.",
        };

        setConfirmAction({
          type: action,
          snapshotId,
          message: confirmMessages[action],
          onConfirm: () =>
            executeSnapshotAction(
              action,
              snapshotId,
              currentName,
              newDescription
            ),
        });
        setShowConfirmModal(true);
        return;
      }

      // Pour les autres actions, exécuter directement
      await executeSnapshotAction(
        action,
        snapshotId,
        currentName,
        newDescription
      );
    } catch (error) {
      console.error(`❌ Snapshot action ${action} failed:`, error);
    }
  };

  const executeSnapshotAction = async (
    action,
    snapshotId,
    newName,
    newDescription
  ) => {
    let actionKey = null; // Declare actionKey at function scope

    try {
      // Démarrer l'indicateur de progression
      const actionDurations = {
        rename: 10,
        rollback: 120,
        delete: 30,
      };
      actionKey = startActionProgress(
        selectedVpsForSnapshot.id,
        `${action}-snapshot`,
        actionDurations[action] || 30
      );

      let response;
      switch (action) {
        case "rename":
          response = await vpsService.renameVPSSnapshot(
            selectedVpsForSnapshot.id,
            snapshotId,
            newName,
            newDescription
          );
          break;
        case "rollback":
          response = await vpsService.rollbackVPSSnapshot(
            selectedVpsForSnapshot.id,
            snapshotId
          );
          break;
        case "delete":
          response = await vpsService.deleteVPSSnapshot(
            selectedVpsForSnapshot.id,
            snapshotId
          );
          break;
        default:
          throw new Error(`Unknown snapshot action: ${action}`);
      }

      if (response.data.success) {
        // Compléter la progression
        if (actionKey) {
          completeActionProgress(actionKey);
        }

        // Réinitialiser le message d'erreur de quota (surtout après suppression)
        if (action === "delete") {
          console.log(`🗑️ Snapshot deleted, clearing quota error message`);
          setQuotaError(null);
        }

        // Recharger les snapshots
        await loadSnapshots(selectedVpsForSnapshot.id);
      } else {
        if (actionKey) {
          completeActionProgress(actionKey);
        }
      }
    } catch (error) {
      console.error(`❌ Snapshot action ${action} failed:`, error);
      if (actionKey) {
        completeActionProgress(actionKey);
      }
    }
  };

  const closeSnapshotsTable = () => {
    setShowSnapshotsTable(false);
    setSelectedVpsForSnapshot(null);
    setSnapshots([]);
    setSnapshotQuota(null);
    setQuotaError(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <Typography className="text-gray-600">
            {t("hosting.loading")}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[80vh] p-4 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <Typography
            variant="h1"
            className="text-2xl md:text-3xl font-bold text-gray-900 mb-2"
          >
            Gestion des Serveurs
          </Typography>
          <Typography className="text-sm md:text-base text-gray-600">
            Gérez vos serveurs VPS, dédiés et partagés depuis un seul endroit
          </Typography>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 border-b border-gray-200">
            <button
              onClick={() => setActiveTab("vps")}
              className={`px-4 py-3 text-sm font-medium rounded-t-lg sm:rounded-none transition-all duration-200 ${
                activeTab === "vps"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
              }`}
            >
              <div className="flex items-center gap-2">
                <Server className="w-4 h-4" />
                <span>VPS ({vpsServers.length})</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab("dedicated")}
              className={`px-4 py-3 text-sm font-medium rounded-t-lg sm:rounded-none transition-all duration-200 ${
                activeTab === "dedicated"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
              }`}
            >
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4" />
                <span>Serveurs Dédiés ({dedicatedServers.length})</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab("shared")}
              className={`px-4 py-3 text-sm font-medium rounded-t-lg sm:rounded-none transition-all duration-200 ${
                activeTab === "shared"
                  ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                  : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
              }`}
            >
              <div className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                <span>Serveurs Partagés ({sharedServers.length})</span>
              </div>
            </button>
          </div>
        </div>

        {/* VPS Tab Content */}
        {activeTab === "vps" && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {vpsLoading ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <Typography className="text-gray-600 text-sm">
                    Loading VPS instances...
                  </Typography>
                </div>
              </div>
            ) : vpsError ? (
              <div className="flex items-center justify-center p-8">
                <div className="text-center">
                  <Typography
                    variant="h6"
                    className="text-red-600 mb-2 text-sm"
                  >
                    Error loading VPS instances
                  </Typography>
                  <Typography className="text-gray-600 mb-4 text-xs">
                    {vpsError}
                  </Typography>
                  <Button
                    onClick={() => window.location.reload()}
                    color="blue"
                    size="sm"
                  >
                    Retry
                  </Button>
                </div>
              </div>
            ) : vpsServers.length > 0 ? (
              <>
                {/* VPS Header with Refresh Button */}
                <div className="flex justify-between items-center p-4 border-b border-gray-200">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">
                      VPS Instances ({vpsServers.length})
                    </h3>
                    {lastRefresh && (
                      <p className="text-xs text-gray-500 mt-1">
                        Last updated: {lastRefresh.toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={refreshVpsData}
                    disabled={vpsLoading}
                    className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                    title="Refresh VPS status"
                  >
                    <RefreshCw
                      className={`w-4 h-4 ${vpsLoading ? "animate-spin" : ""}`}
                    />
                    <span>{vpsLoading ? "Refreshing..." : "Refresh"}</span>
                  </button>
                </div>

                {/* Mobile/Tablet Cards View */}
                <div className="block xl:hidden">
                  <div className="space-y-4 p-4">
                    {vpsServers.map((server) => (
                      <div
                        key={server.id}
                        className="bg-gray-50 rounded-lg p-4 space-y-3"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium text-gray-900 whitespace-pre-line leading-tight">
                              {server.server}
                            </div>
                            <p className="text-sm text-gray-500">
                              ID: {server.id}
                            </p>
                          </div>
                          <div
                            className={`w-3 h-3 rounded-full ${
                              getStatusColor(server.status).dot
                            }`}
                            title={`Status: ${server.status}`}
                          ></div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Utilisateur:</span>
                            <p className="font-medium">{server.defaultUser}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Système:</span>
                            <p className="font-medium">{server.hostSystem}</p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 pt-3">
                          {/* Start VPS */}
                          {(() => {
                            const buttonStates = getButtonStates(server);
                            const startState = buttonStates.start;
                            return (
                              <button
                                className={`flex items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${startState.className}`}
                                disabled={
                                  !startState.enabled || startState.loading
                                }
                                onClick={() =>
                                  startState.enabled &&
                                  !startState.loading &&
                                  handleVpsAction(
                                    server.instanceId || server.id,
                                    "start"
                                  )
                                }
                                title={startState.title}
                              >
                                {startState.loading ? (
                                  <RefreshCw className="w-4 h-4 animate-spin" />
                                ) : (
                                  <Play className="w-4 h-4" />
                                )}
                                <span>
                                  {startState.loading ? "Starting..." : "Start"}
                                </span>
                              </button>
                            );
                          })()}

                          {/* Stop VPS */}
                          {(() => {
                            const buttonStates = getButtonStates(server);
                            const stopState = buttonStates.stop;
                            return (
                              <button
                                className={`flex items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${stopState.className}`}
                                disabled={
                                  !stopState.enabled || stopState.loading
                                }
                                onClick={() =>
                                  stopState.enabled &&
                                  !stopState.loading &&
                                  handleVpsAction(
                                    server.instanceId || server.id,
                                    "stop"
                                  )
                                }
                                title={stopState.title}
                              >
                                {stopState.loading ? (
                                  <RefreshCw className="w-4 h-4 animate-spin" />
                                ) : (
                                  <Square className="w-4 h-4" />
                                )}
                                <span>
                                  {stopState.loading ? "Stopping..." : "Stop"}
                                </span>
                              </button>
                            );
                          })()}

                          {/* Restart VPS */}
                          {(() => {
                            const buttonStates = getButtonStates(server);
                            const restartState = buttonStates.restart;
                            return (
                              <button
                                className={`flex items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${restartState.className}`}
                                disabled={
                                  !restartState.enabled || restartState.loading
                                }
                                onClick={() =>
                                  restartState.enabled &&
                                  !restartState.loading &&
                                  handleVpsAction(
                                    server.instanceId || server.id,
                                    "restart"
                                  )
                                }
                                title={restartState.title}
                              >
                                {restartState.loading ? (
                                  <RefreshCw className="w-4 h-4 animate-spin" />
                                ) : (
                                  <RefreshCw className="w-4 h-4" />
                                )}
                                <span>
                                  {restartState.loading
                                    ? "Restarting..."
                                    : "Restart"}
                                </span>
                              </button>
                            );
                          })()}

                          {/* Cloud-Init Toggle */}
                          <div className="flex items-center gap-2">
                            <span className="text-xs font-medium text-gray-700">
                              Cloud-Init:
                            </span>
                            <CloudInitToggle
                              server={server}
                              isEnabled={cloudInitStates[server.id] || false}
                              onToggle={handleCloudInit}
                              isLoading={
                                actionsInProgress[`${server.id}-cloud-init`]
                              }
                            />
                          </div>

                          {/* Reinstall */}
                          <button
                            className="flex items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200 hover:shadow-md transition-all duration-200"
                            onClick={() => handleReinstallClick(server)}
                            title="Réinstaller le VPS"
                          >
                            <RotateCcw className="w-4 h-4" />
                            <span>Reinstall</span>
                          </button>

                          {/* Rescue System */}
                          {(() => {
                            const buttonStates = getButtonStates(server);
                            const rescueState = buttonStates.rescue;
                            return (
                              <button
                                className={`flex items-center gap-1 px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${rescueState.className}`}
                                disabled={
                                  !rescueState.enabled || rescueState.active
                                }
                                onClick={() =>
                                  rescueState.enabled &&
                                  !rescueState.active &&
                                  handleRescueClick(
                                    server.instanceId || server.id
                                  )
                                }
                                title={rescueState.title}
                              >
                                <Shield className="w-4 h-4" />
                                <span>
                                  {rescueState.active ? "In Rescue" : "Rescue"}
                                </span>
                              </button>
                            );
                          })()}
                        </div>

                        <div className="pt-2 border-t border-gray-200 flex justify-center">
                          <ManageButton
                            server={server}
                            onCancelClick={handleCancelClick}
                            onPasswordReset={handlePasswordResetClick}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Desktop Table View - Responsive */}
                <div
                  className="hidden xl:block overflow-x-auto"
                  style={{ position: "relative", zIndex: 1 }}
                >
                  <table className="w-full text-sm table-fixed">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr className="text-xs text-gray-500 uppercase tracking-wider">
                        <th className="px-2 py-3 text-left font-medium w-32">
                          SERVEUR
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-24">
                          UTILISATEUR
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-20">
                          HOST SYSTEM
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          STATUT
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <Play className="w-3 h-3 mb-1" />
                            <span>START</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <Square className="w-3 h-3 mb-1" />
                            <span>STOP</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <RefreshCw className="w-3 h-3 mb-1" />
                            <span>RESTART</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <Settings className="w-3 h-3 mb-1" />
                            <span>CLOUD-INIT</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <RotateCcw className="w-3 h-3 mb-1" />
                            <span>REINSTALL</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <Shield className="w-3 h-3 mb-1" />
                            <span>RESCUE</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-16">
                          <div className="flex flex-col items-center">
                            <Zap className="w-3 h-3 mb-1" />
                            <span>SNAPSHOT</span>
                          </div>
                        </th>
                        <th className="px-2 py-3 text-center font-medium w-20">
                          MANAGE
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {vpsServers.map((server) => (
                        <tr
                          key={server.id}
                          className="hover:bg-gray-50 transition-colors duration-150"
                        >
                          <td className="px-2 py-3 text-sm font-medium text-gray-900">
                            <div className="whitespace-pre-line leading-tight">
                              {server.server}
                            </div>
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.defaultUser}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.hostSystem}
                          </td>
                          <td className="px-2 py-3">
                            <div
                              className={`w-3 h-3 rounded-full ${
                                getStatusColor(server.status).dot
                              }`}
                              title={`Status: ${server.status}`}
                            ></div>
                          </td>
                          <td className="px-2 py-3">
                            {(() => {
                              const buttonStates = getButtonStates(server);
                              const startState = buttonStates.start;
                              return (
                                <button
                                  className={`p-2 rounded-lg transition-all duration-200 ${startState.className}`}
                                  disabled={
                                    !startState.enabled || startState.loading
                                  }
                                  onClick={() =>
                                    startState.enabled &&
                                    !startState.loading &&
                                    handleVpsAction(
                                      server.instanceId || server.id,
                                      "start"
                                    )
                                  }
                                  title={startState.title}
                                >
                                  {startState.loading ? (
                                    <RefreshCw className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Play className="w-4 h-4" />
                                  )}
                                </button>
                              );
                            })()}
                          </td>
                          <td className="px-2 py-3">
                            {(() => {
                              const buttonStates = getButtonStates(server);
                              const stopState = buttonStates.stop;
                              return (
                                <button
                                  className={`p-2 rounded-lg transition-all duration-200 ${stopState.className}`}
                                  disabled={
                                    !stopState.enabled || stopState.loading
                                  }
                                  onClick={() =>
                                    stopState.enabled &&
                                    !stopState.loading &&
                                    handleVpsAction(
                                      server.instanceId || server.id,
                                      "stop"
                                    )
                                  }
                                  title={stopState.title}
                                >
                                  {stopState.loading ? (
                                    <RefreshCw className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Square className="w-4 h-4" />
                                  )}
                                </button>
                              );
                            })()}
                          </td>
                          <td className="px-2 py-3">
                            {(() => {
                              const buttonStates = getButtonStates(server);
                              const restartState = buttonStates.restart;
                              return (
                                <button
                                  className={`p-2 rounded-lg transition-all duration-200 ${restartState.className}`}
                                  disabled={
                                    !restartState.enabled ||
                                    restartState.loading
                                  }
                                  onClick={() =>
                                    restartState.enabled &&
                                    !restartState.loading &&
                                    handleVpsAction(
                                      server.instanceId || server.id,
                                      "restart"
                                    )
                                  }
                                  title={restartState.title}
                                >
                                  <RefreshCw
                                    className={`w-4 h-4 ${
                                      restartState.loading ? "animate-spin" : ""
                                    }`}
                                  />
                                </button>
                              );
                            })()}
                          </td>
                          <td className="px-2 py-3 text-center">
                            <CloudInitToggle
                              server={server}
                              isEnabled={cloudInitStates[server.id] || false}
                              onToggle={handleCloudInit}
                              isLoading={
                                actionsInProgress[`${server.id}-cloud-init`]
                              }
                            />
                          </td>
                          <td className="px-2 py-3">
                            <button
                              className="p-2 rounded-lg bg-red-100 text-red-700 hover:bg-red-200 hover:shadow-md transition-all duration-200"
                              onClick={() => handleReinstallClick(server)}
                              title="Réinstaller le VPS"
                            >
                              <RotateCcw className="w-4 h-4" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            {(() => {
                              const buttonStates = getButtonStates(server);
                              const rescueState = buttonStates.rescue;
                              return (
                                <button
                                  className={`p-2 rounded-lg transition-all duration-200 ${rescueState.className}`}
                                  disabled={
                                    !rescueState.enabled || rescueState.active
                                  }
                                  onClick={() =>
                                    rescueState.enabled &&
                                    !rescueState.active &&
                                    handleRescueClick(
                                      server.instanceId || server.id
                                    )
                                  }
                                  title={rescueState.title}
                                >
                                  <Shield className="w-4 h-4" />
                                </button>
                              );
                            })()}
                          </td>
                          <td className="px-2 py-3">
                            <button
                              className="p-2 rounded-lg bg-yellow-100 text-yellow-700 hover:bg-yellow-200 hover:shadow-md transition-all duration-200"
                              onClick={() => handleSnapshotClick(server)}
                              title="Gérer les snapshots"
                            >
                              <Zap className="w-4 h-4" />
                            </button>
                          </td>
                          <td className="px-2 py-3 text-center">
                            <ManageButton
                              server={server}
                              onCancelClick={handleCancelClick}
                              onPasswordReset={handlePasswordResetClick}
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-lg text-gray-600 mb-2">
                  Aucun VPS trouvé
                </Typography>
                <Typography className="text-gray-500">
                  Vous n&apos;avez pas encore de serveurs VPS
                </Typography>
              </div>
            )}
          </div>
        )}

        {/* Dedicated Tab Content */}
        {activeTab === "dedicated" && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {dedicatedServers.length > 0 ? (
              <>
                {/* Mobile/Tablet Cards View */}
                <div className="block xl:hidden">
                  <div className="space-y-4 p-4">
                    {dedicatedServers.map((server) => (
                      <div
                        key={server.id}
                        className="bg-gray-50 rounded-lg p-4 space-y-3"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">
                              {server.server}
                            </p>
                            <p className="text-sm text-gray-500">
                              ID: {server.id}
                            </p>
                          </div>
                          <div
                            className={`w-3 h-3 rounded-full ${
                              getStatusColor(server.status).dot
                            }`}
                            title={`Status: ${server.status}`}
                          ></div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Utilisateur:</span>
                            <p className="font-medium">{server.defaultUser}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Système:</span>
                            <p className="font-medium">{server.hostSystem}</p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2 pt-2">
                          <button className="text-green-600 hover:text-green-700 p-1">
                            <RefreshCw className="w-5 h-5" />
                          </button>
                          <button className="text-green-600 hover:text-green-700 p-1">
                            <Play className="w-5 h-5" />
                          </button>
                          <button className="text-red-600 hover:text-red-700 p-1">
                            <Square className="w-5 h-5" />
                          </button>
                          <button className="text-orange-600 hover:text-orange-700 p-1">
                            <Shield className="w-5 h-5" />
                          </button>
                          <button className="text-purple-600 hover:text-purple-700 p-1">
                            <RefreshCw className="w-5 h-5" />
                          </button>
                          <button className="text-yellow-600 hover:text-yellow-700 p-1">
                            <Zap className="w-5 h-5" />
                          </button>
                          <button className="text-indigo-600 hover:text-indigo-700 p-1">
                            <Monitor className="w-5 h-5" />
                          </button>
                        </div>

                        <div className="pt-2 border-t border-gray-200 flex justify-center">
                          <ManageButton
                            server={server}
                            onCancelClick={handleCancelClick}
                            onPasswordReset={handlePasswordResetClick}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Desktop Table View - Responsive */}
                <div className="hidden xl:block overflow-x-auto">
                  <table className="w-full text-sm table-fixed">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr className="text-xs text-gray-500 uppercase tracking-wider">
                        <th className="px-2 py-3 text-left font-medium w-32">
                          SERVEUR
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-24">
                          DÉFAUT USER
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-20">
                          HOST SYSTEM
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-16">
                          STATUT
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-16">
                          RESTART
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-16">
                          START
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-16">
                          STOP
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-20">
                          CLOUD-INIT
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-20">
                          REINSTALL
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-24">
                          RESCUE SYSTEM
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-20">
                          SNAPSHOTS
                        </th>
                        <th className="px-2 py-3 text-left font-medium w-20">
                          MANAGE
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {dedicatedServers.map((server) => (
                        <tr
                          key={server.id}
                          className="hover:bg-gray-50 transition-colors duration-150"
                        >
                          <td className="px-2 py-3 text-sm font-medium text-gray-900 truncate">
                            {server.server}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.defaultUser}
                          </td>
                          <td className="px-2 py-3 text-sm text-gray-900 truncate">
                            {server.hostSystem}
                          </td>
                          <td className="px-2 py-3">
                            <div
                              className={`w-3 h-3 rounded-full ${
                                getStatusColor(server.status).dot
                              }`}
                              title={`Status: ${server.status}`}
                            ></div>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-green-600 hover:text-green-700 p-1">
                              <RefreshCw className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-green-600 hover:text-green-700 p-1">
                              <Play className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-red-600 hover:text-red-700 p-1">
                              <Square className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-orange-600 hover:text-orange-700 p-1">
                              <Shield className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-purple-600 hover:text-purple-700 p-1">
                              <RefreshCw className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-yellow-600 hover:text-yellow-700 p-1">
                              <Zap className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3">
                            <button className="text-indigo-600 hover:text-indigo-700 p-1">
                              <Monitor className="w-3 h-3" />
                            </button>
                          </td>
                          <td className="px-2 py-3 text-center">
                            <ManageButton
                              server={server}
                              onCancelClick={handleCancelClick}
                              onPasswordReset={handlePasswordResetClick}
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <HardDrive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-lg text-gray-600 mb-2">
                  Aucun serveur dédié trouvé
                </Typography>
                <Typography className="text-gray-500">
                  Vous n&apos;avez pas encore de serveurs dédiés
                </Typography>
              </div>
            )}
          </div>
        )}

        {/* Shared Tab Content */}
        {activeTab === "shared" && (
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {sharedServers.length > 0 ? (
              <>
                {/* Mobile/Tablet Cards View */}
                <div className="block lg:hidden">
                  <div className="space-y-4 p-4">
                    {sharedServers.map((server) => (
                      <div
                        key={server.id}
                        className="bg-gray-50 rounded-lg p-4 space-y-3"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-gray-900">
                              {server.server}
                            </p>
                            <p className="text-sm text-gray-500">
                              ID: {server.id}
                            </p>
                          </div>
                          <ServerStatus status={server.status} />
                        </div>

                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="text-gray-500">Ressources:</span>
                            <p className="font-medium">{server.resources}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">Prix:</span>
                            <p className="font-medium text-green-600">
                              {server.price}
                            </p>
                          </div>
                        </div>

                        <div className="pt-2 border-t border-gray-200 flex justify-center">
                          <ManageButton
                            server={server}
                            onCancelClick={handleCancelClick}
                            onPasswordReset={handlePasswordResetClick}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Desktop Table View */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Serveur
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Statut
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ressources
                        </th>
                        <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Prix
                        </th>
                        <th className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sharedServers.map((server) => (
                        <tr
                          key={server.id}
                          className="hover:bg-gray-50 transition-colors duration-150"
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {server.server}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {server.id}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <ServerStatus status={server.status} />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {server.resources}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                            {server.price}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <ManageButton
                              server={server}
                              onCancelClick={handleCancelClick}
                              onPasswordReset={handlePasswordResetClick}
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-lg text-gray-600 mb-2">
                  Aucun serveur partagé trouvé
                </Typography>
                <Typography className="text-gray-500">
                  Vous n&apos;avez pas encore de serveurs partagés
                </Typography>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modal de création de snapshot */}
      {showSnapshotModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">
              📸 Créer un snapshot pour{" "}
              {selectedVpsForSnapshot?.name || selectedVpsForSnapshot?.server}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom du snapshot
                </label>
                <input
                  type="text"
                  value={snapshotName}
                  onChange={(e) => setSnapshotName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="snapshot-name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description (optionnelle)
                </label>
                <textarea
                  value={snapshotDescription}
                  onChange={(e) => setSnapshotDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="3"
                  placeholder="Description du snapshot..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowSnapshotModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleCreateSnapshot}
                disabled={!snapshotName.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                📸 Créer le snapshot
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Sous-tableau des snapshots */}
      {showSnapshotsTable && selectedVpsForSnapshot && (
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  📸 Snapshots de{" "}
                  {selectedVpsForSnapshot.name || selectedVpsForSnapshot.server}
                </h3>
                {snapshotQuota && (
                  <p className="text-sm text-gray-600 mt-1">
                    Quota: {snapshotQuota.current}/{snapshotQuota.max} snapshots
                    utilisés
                    {snapshotQuota.remaining > 0
                      ? ` (${snapshotQuota.remaining} restants)`
                      : " (quota atteint)"}
                  </p>
                )}
              </div>
              <button
                onClick={closeSnapshotsTable}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            {/* Message d'erreur de quota */}
            {quotaError && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-medium text-red-800">
                      Quota de snapshots atteint
                    </h4>
                    <p className="text-sm text-red-700 mt-1">{quotaError}</p>
                    <p className="text-sm text-red-600 mt-2">
                      💡 Supprimez un ou plusieurs snapshots ci-dessous pour
                      pouvoir en créer de nouveaux.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="p-6">
            {/* Bouton créer snapshot si quota disponible */}
            {snapshotQuota && snapshotQuota.canCreate && !quotaError && (
              <div className="mb-4">
                <button
                  onClick={() => {
                    setQuotaError(null);
                    setShowSnapshotModal(true);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
                >
                  <Zap className="w-4 h-4" />
                  Créer un nouveau snapshot
                </button>
              </div>
            )}

            {snapshotsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-gray-600">
                  Chargement des snapshots...
                </span>
              </div>
            ) : snapshots.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr className="text-xs text-gray-500 uppercase tracking-wider">
                      <th className="px-4 py-3 text-left font-medium">Name</th>
                      <th className="px-4 py-3 text-left font-medium">
                        Created
                      </th>
                      <th className="px-4 py-3 text-left font-medium">
                        Auto-Deletion
                      </th>
                      <th className="px-4 py-3 text-center font-medium">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {snapshots.map((snapshot) => (
                      <tr
                        key={snapshot.snapshotId}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-4 py-3 font-medium text-gray-900">
                          {snapshot.name}
                        </td>
                        <td className="px-4 py-3 text-gray-600">
                          {new Date(snapshot.createdDate).toLocaleDateString(
                            "fr-FR",
                            {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          )}
                        </td>
                        <td className="px-4 py-3 text-gray-600">
                          {snapshot.autoDeleteDate
                            ? new Date(
                                snapshot.autoDeleteDate
                              ).toLocaleDateString("fr-FR")
                            : "Pas de suppression automatique"}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex justify-center space-x-2">
                            <button
                              onClick={() =>
                                handleSnapshotAction(
                                  "rename",
                                  snapshot.snapshotId,
                                  snapshot.name,
                                  snapshot.description
                                )
                              }
                              className="p-1 text-blue-600 hover:text-blue-800"
                              title="Renommer"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() =>
                                handleSnapshotAction(
                                  "rollback",
                                  snapshot.snapshotId
                                )
                              }
                              className="p-1 text-green-600 hover:text-green-800"
                              title="Rollback"
                            >
                              <RotateCcw className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() =>
                                handleSnapshotAction(
                                  "delete",
                                  snapshot.snapshotId
                                )
                              }
                              className="p-1 text-red-600 hover:text-red-800"
                              title="Supprimer"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                📸 Aucun snapshot trouvé pour ce VPS
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal de création de snapshot */}
      {showSnapshotModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">
              📸 Créer un snapshot pour{" "}
              {selectedVpsForSnapshot?.name || selectedVpsForSnapshot?.server}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom du snapshot
                </label>
                <input
                  type="text"
                  value={snapshotName}
                  onChange={(e) => setSnapshotName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="snapshot-name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description (optionnelle)
                </label>
                <textarea
                  value={snapshotDescription}
                  onChange={(e) => setSnapshotDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="3"
                  placeholder="Description du snapshot..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowSnapshotModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleCreateSnapshot}
                disabled={!snapshotName.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                📸 Créer le snapshot
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de confirmation */}
      {showConfirmModal && confirmAction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-6 h-6 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">
                Confirmation requise
              </h3>
            </div>

            <p className="text-gray-700 mb-6">{confirmAction.message}</p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowConfirmModal(false);
                  setConfirmAction(null);
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  setShowConfirmModal(false);
                  confirmAction.onConfirm();
                  setConfirmAction(null);
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Confirmer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de rename snapshot */}
      {showRenameModal && renameAction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center mb-4">
              <Edit className="w-6 h-6 text-blue-500 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">
                Renommer le snapshot
              </h3>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nouveau nom du snapshot
              </label>
              <input
                type="text"
                value={newSnapshotName}
                onChange={(e) => setNewSnapshotName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Nom du snapshot"
                autoFocus
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowRenameModal(false);
                  setRenameAction(null);
                  setNewSnapshotName("");
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  if (
                    newSnapshotName.trim() &&
                    newSnapshotName !== renameAction.currentName
                  ) {
                    setShowRenameModal(false);
                    renameAction.onConfirm(newSnapshotName.trim());
                    setRenameAction(null);
                    setNewSnapshotName("");
                  }
                }}
                disabled={
                  !newSnapshotName.trim() ||
                  newSnapshotName === renameAction.currentName
                }
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Renommer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Indicateurs d'actions en cours */}
      {Object.keys(actionsInProgress).length > 0 && (
        <div className="fixed bottom-4 right-4 z-50 space-y-2">
          {Object.entries(actionsInProgress).map(([actionKey, actionInfo]) => {
            const progress = actionProgress[actionKey] || 0;
            const timeElapsed = Math.floor(
              (Date.now() - actionInfo.startTime) / 1000
            );
            const estimatedTotal = Math.floor(
              actionInfo.estimatedDuration / 1000
            );
            const timeRemaining = Math.max(0, estimatedTotal - timeElapsed);

            return (
              <div
                key={actionKey}
                className="bg-white shadow-lg rounded-lg p-4 min-w-80 border border-gray-200"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                    <span className="text-sm font-medium text-gray-900">
                      {actionInfo.action.includes("snapshot")
                        ? `📸 ${actionInfo.action
                            .replace("-snapshot", "")
                            .toUpperCase()} SNAPSHOT - VPS ${
                            actionInfo.instanceId
                          }`
                        : `${actionInfo.action.toUpperCase()} - VPS ${
                            actionInfo.instanceId
                          }`}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {timeRemaining > 0
                      ? `${timeRemaining}s restantes`
                      : "Finalisation..."}
                  </span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>

                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>{Math.round(progress)}% terminé</span>
                  <span>
                    {timeElapsed}s / {estimatedTotal}s
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {openModal && (
        <PaymentStatusModal
          status={paymentStatus}
          orderId={orderId}
          onClose={() => setOpenModal(false)}
        />
      )}

      <RescueModal
        open={showRescueModal}
        onClose={() => {
          setShowRescueModal(false);
          setSelectedInstanceForRescue(null);
        }}
        selectedInstanceId={selectedInstanceForRescue}
        onRescueSuccess={handleRescueSuccess}
      />

      <RescueSuccessModal
        open={showRescueSuccessModal}
        onClose={() => {
          setShowRescueSuccessModal(false);
          setRescueSuccessData(null);
        }}
        rescueData={rescueSuccessData}
      />

      <CancellationModal
        isOpen={showCancellationModal}
        onClose={handleCancellationClose}
        onConfirm={handleCancellationConfirm}
        loading={cancellationLoading}
        instanceName={
          selectedInstanceForCancellation?.name ||
          selectedInstanceForCancellation?.displayName
        }
        serviceName={
          selectedInstanceForCancellation?.name ||
          selectedInstanceForCancellation?.displayName ||
          "VPS Instance"
        }
      />

      {/* Modal de réinstallation */}
      <ReinstallModal
        isOpen={showReinstallModal}
        onClose={handleCloseReinstallModal}
        server={selectedVpsForReinstall}
        onReinstall={handleReinstall}
        isLoading={reinstallLoading}
      />

      {/* Modal de réinitialisation de mot de passe */}
      <PasswordResetModal
        isOpen={showPasswordResetModal}
        onClose={handleClosePasswordResetModal}
        server={selectedVpsForPasswordReset}
        onResetSuccess={handlePasswordResetSuccess}
      />

      {/* Notifications */}
      <VPSActionSuccessNotification
        isOpen={showSuccessNotification}
        onClose={handleCloseSuccessNotification}
        action={successNotificationAction}
        serverInfo={successNotificationServer}
      />

      <ErrorNotification
        isOpen={showErrorNotification}
        onClose={handleCloseErrorNotification}
        message={errorNotificationMessage}
      />

      <InfoNotification
        isOpen={showInfoNotification}
        onClose={handleCloseInfoNotification}
        title="Mode Rescue"
        message={infoNotificationMessage}
        autoClose={false}
      />

      <ConsoleNotification
        isOpen={showConsoleNotification}
        onClose={handleCloseConsoleNotification}
      />
    </div>
  );
}
