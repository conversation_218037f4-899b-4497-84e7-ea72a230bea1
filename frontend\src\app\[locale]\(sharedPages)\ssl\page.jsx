"use client";
// Core imports
import { useState, useMemo, useRef, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useRouter, useParams } from "next/navigation";

// Services and utilities
import categoryService from "../../../services/categoryService";
import cartService from "@/app/services/cartService";
import { useAuth } from "@/app/context/AuthContext";
import { toast } from "react-toastify";

// Icons
import { LockIcon } from "lucide-react";

// Components
import SSLHero from "@/components/ssl/SSLHero";
import SSLFeatures from "@/components/ssl/SSLFeatures";
import SSLCard from "@/components/ssl/SSLCard";
import SSLFilters from "@/components/ssl/SSLFilters";
import DynamicMetadataClient from "@/components/DynamicMetadataClient";

/**
 * SSLPage Component - Displays and manages SSL certificate products
 * Supports brand and type-based filtering, mobile/desktop responsive views
 */
const SSLPage = () => {
  // Localization and routing
  const t = useTranslations("SSL2");
  const router = useRouter();
  const params = useParams();
  const locale = params.locale || "en";

  // Core state
  const { setCartCount } = useAuth();
  const [loading, setLoading] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [expandedItem, setExpandedItem] = useState("RapidSSL");

  // Filter and display state
  const [activeTab, setActiveTab] = useState("brand");
  const [filter, setFilter] = useState({ type: "brand", value: "RapidSSL" });
  const [billingPeriod, setBillingPeriod] = useState("1year"); // Options: "month", "1year", "2years", "3years"

  // Data state
  const [sslBrands, setSslBrands] = useState([]);
  const [backendSSLPackages, setBackendSSLPackages] = useState([]);

  // Refs for UI elements
  const subTabsContainerRef = useRef(null);

  // Type categories for filtering
  const typeCategories = ["DV", "OV", "EV", "Wildcard", "Multi-Domain"];

  // Helper Functions
  const normalizeSSLType = (type) => {
    if (!type) return '';
    return type.toUpperCase().replace('MULTI-DOMAINS', 'MULTI-DOMAIN');
  };

  const getTypeCategory = (planType) => {
    if (!planType) return "DV";
    const normalizedType = normalizeSSLType(planType);
    if (normalizedType.includes("WILDCARD")) return "Wildcard";
    if (normalizedType.includes("MULTI-DOMAIN")) return "Multi-Domain";
    if (normalizedType.includes("EV")) return "EV";
    if (normalizedType.includes("OV")) return "OV";
    return "DV";
  };

  // Helper function to check if a plan matches an SSL type
  const matchesSSLType = (planType, filterType) => {
    if (!planType) return false;

    // Normalize both types for consistent comparison
    const normalizedPlanType = normalizeSSLType(planType);
    const normalizedFilterType = normalizeSSLType(filterType);

    // Handle combined types (e.g., "WILDCARD OV")
    const planTypes = normalizedPlanType.split(' ');

    // Return true if any part of the plan type matches the filter
    return planTypes.includes(normalizedFilterType);
  };

  // Helper function to normalize brand names
  const normalizeBrandName = (brand) => {
    if (!brand) return '';
    return brand.toUpperCase().replace(/\s+/g, '');
  };

  // Helper function to check if brands match
  const matchesBrand = (planBrand, filterBrand) => {
    if (!planBrand || !filterBrand) return false;

    const normalizedPlanBrand = normalizeBrandName(planBrand);
    const normalizedFilterBrand = normalizeBrandName(filterBrand);

    return normalizedPlanBrand.includes(normalizedFilterBrand);
  };

  // Filter plans based on active filter
  const filteredPlans = useMemo(() => {
    if (!backendSSLPackages.length) return [];

    return backendSSLPackages.filter(plan => {
      if (filter.type === "brand") {
        return matchesBrand(plan.brandObj.name, filter.value);
      } else if (filter.type === "type") {
        return matchesSSLType(plan.sslType, filter.value);
      }
      return true;
    });
  }, [backendSSLPackages, filter]);

  // Effects
  useEffect(() => {
    const fetchSSLPackages = async () => {
      try {
        setLoading(true);
        const response = await categoryService.getCategory('SSL');

        if (response.data?.brands) {
          setSslBrands(response.data.brands);

          // Set default filter to first available brand
          if (response.data.brands.length > 0) {
            const firstBrand = response.data.brands[0].name;
            setFilter({ type: "brand", value: firstBrand });
            setExpandedItem(firstBrand);
          }

          // Process all packages
          const allPackages = response.data.brands.reduce((acc, brand) => {
            if (brand.packages) {
              const brandPackages = brand.packages.map(pkg => ({
                ...pkg,
                brandName: brand.name,
                sslType: pkg.sslType,
                brandObj: brand
              }));
              return [...acc, ...brandPackages];
            }
            return acc;
          }, []);

          setBackendSSLPackages(allPackages);
        }
      } catch (error) {
        console.error('Error fetching SSL packages:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSSLPackages();
  }, []);

  // Check viewport size
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  }, []);

  // Add this function to handle smooth scrolling to subtabs
  const scrollToSubtab = (tabElement) => {
    if (!tabElement || !isMobile) return;

    // Get the element's position relative to the viewport
    const rect = tabElement.getBoundingClientRect();

    // Calculate scroll position (position from top of document minus some padding)
    const scrollTop = window.pageYOffset + rect.top - 80; // 80px padding from top

    // Scroll smoothly to the element
    window.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
  };

  // Update the toggleExpand function with scrolling behavior
  const toggleExpand = (item, event) => {
    // If there's an event, get the button element
    const targetElement = event?.currentTarget || null;

    if (expandedItem === item) {
      setExpandedItem(null);
    } else {
      setExpandedItem(item);

      // Wait for DOM to update with the expanded content
      setTimeout(() => {
        // If we have the target element, scroll to it
        if (targetElement) {
          scrollToSubtab(targetElement);
        }
      }, 100); // Small delay to ensure content is expanded
    }
  };

  const handleFilterChange = (type, value) => {
    setFilter({ type, value });
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);

    // When changing tabs, set default filters and expand the item
    if (tab === "brand" && sslBrands.length > 0) {
      const firstBrand = sslBrands[0].name;
      setFilter({ type: "brand", value: firstBrand });
      setExpandedItem(firstBrand);
    } else if (tab === "type" && typeCategories.length > 0) {
      const firstType = typeCategories[0];
      setFilter({ type: "type", value: firstType });
      setExpandedItem(firstType);
    }
  };

  const handleSelectPlan = async (plan) => {
    try {
      const selectedPeriod = billingPeriod === "month" ? 1 :
                           billingPeriod === "1year" ? 12 :
                           billingPeriod === "2years" ? 24 : 36;

      const discountEntry = plan.discounts?.find(d => d.period === selectedPeriod);
      const discountRate = discountEntry ? discountEntry.percentage : 0;

      const res = await cartService.addItemToCart({
        packageId: plan._id,
        quantity: 1,
        period: selectedPeriod,
        discountRate,
      });

      setCartCount(res.data.cart.cartCount);
      toast.success(t("notifications.success"));
      router.push("/client/cart");
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to add SSL certificate to cart");
    }
  };

  return (
    <div className="min-h-screen bg-white py-12 sm:py-20">
      <DynamicMetadataClient
  title={`ZtechEngineering | ${t('title')}`}
  desc={`${t('desc')}`} 
/>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <SSLHero />

        {/* Filters with integrated mobile cards */}
        <SSLFilters
          activeTab={activeTab}
          handleTabChange={handleTabChange}
          filter={filter}
          handleFilterChange={handleFilterChange}
          toggleExpand={toggleExpand}
          expandedItem={expandedItem}
          sslBrands={sslBrands}
          typeCategories={typeCategories}
          matchesBrand={matchesBrand}
          filteredPlans={filteredPlans}
          matchesSSLType={matchesSSLType}
          subTabsContainerRef={subTabsContainerRef}
          locale={locale}
          onSelectPlan={handleSelectPlan}
          billingPeriod={billingPeriod}
          setBillingPeriod={setBillingPeriod}
        />

        {/* Desktop SSL Plans Grid */}
        <div className="hidden md:block border border-gray-300 border-3 mt-0">
          {loading ? (
            <div className="flex justify-center my-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <>
              {filteredPlans.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                  {filteredPlans.map((plan) => (
                    <SSLCard
                      key={plan._id}
                      plan={plan}
                      onSelectPlan={handleSelectPlan}
                      locale={locale}
                      billingPeriod={billingPeriod}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <LockIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">
                    {t("empty_state.title")}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {t("empty_state.subtitle")}
                  </p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Features Grid */}
        <SSLFeatures />
      </div>
    </div>
  );
};

export default SSLPage;
