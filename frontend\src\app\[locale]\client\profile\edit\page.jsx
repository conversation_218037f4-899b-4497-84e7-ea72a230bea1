'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import EditEmailForm from '../../../../../components/profile/editEmailForm';
import EditPasswordForm from '../../../../../components/profile/editPasswordForm';
import EditProfileForm from '../../../../../components/profile/editProfileForm';
import { useAuth } from '../../../../context/AuthContext';
import { useTranslations } from 'next-intl';

const EditProfilePage = () => {
    const t = useTranslations('profile');
    const tClient = useTranslations('client');

    const searchParams = useSearchParams();
    const editPassword = searchParams.get('editPassword');
    const editEmail = searchParams.get('editEmail');
    const { user } = useAuth();

    return (
        <div className="flex justify-center py-8 font-poppins">
            <div className="max-w-4xl w-full bg-white p-6">
                {editEmail ? (
                    <EditEmailForm t={t} />
                ) : editPassword ? (
                    <EditPasswordForm userId={user?._id} t={t} />
                ) : (
                    <EditProfileForm userId={user?._id} t={t} tClient={tClient} />
                )}
            </div>
        </div>
    );
};

export default EditProfilePage;
