import PostCard from "./postCard";

export default function RecentPosts({ posts }) {
    const recentPosts = posts.slice(0, 2);

    return (
        <section className="p-10 flex flex-col gap-y-6">
            <h2 className="text-xl font-medium font-inter">Articles récents</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {recentPosts.map((post) => (
                    <PostCard key={post.id} post={post} />
                ))}
            </div>
        </section>
    );
}
