/**
 * Migration script to fix regions with undefined status
 * This will set all regions without status to 'active'
 */

const mongoose = require('mongoose');
const VPSRegion = require('./models/VPSRegion');
require('dotenv').config();

async function fixRegionStatus() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zn_ztech');
    console.log('✅ Connected to MongoDB');

    // Find all regions with undefined or null status
    console.log('🔍 Finding regions with undefined status...');
    const regionsWithoutStatus = await VPSRegion.find({
      $or: [
        { status: { $exists: false } },
        { status: null },
        { status: undefined }
      ]
    });

    console.log(`Found ${regionsWithoutStatus.length} regions without proper status`);

    if (regionsWithoutStatus.length > 0) {
      console.log('Regions to fix:');
      regionsWithoutStatus.forEach(region => {
        console.log(`   - ${region.name} (${region.regionId}) - Current status: ${region.status}`);
      });

      // Update all regions without status to 'active'
      console.log('\n🔧 Updating regions to active status...');
      const updateResult = await VPSRegion.updateMany(
        {
          $or: [
            { status: { $exists: false } },
            { status: null },
            { status: undefined }
          ]
        },
        {
          $set: { status: 'active' }
        }
      );

      console.log(`✅ Updated ${updateResult.modifiedCount} regions to active status`);
    } else {
      console.log('✅ All regions already have proper status');
    }

    // Verify the fix
    console.log('\n📊 Verification - checking all regions...');
    const allRegions = await VPSRegion.find();
    console.log(`Total regions: ${allRegions.length}`);
    
    const activeRegions = await VPSRegion.find({ status: 'active' });
    console.log(`Active regions: ${activeRegions.length}`);
    
    const inactiveRegions = await VPSRegion.find({ status: 'inactive' });
    console.log(`Inactive regions: ${inactiveRegions.length}`);
    
    const maintenanceRegions = await VPSRegion.find({ status: 'maintenance' });
    console.log(`Maintenance regions: ${maintenanceRegions.length}`);

    if (activeRegions.length > 0) {
      console.log('\n✅ Active regions:');
      activeRegions.forEach(region => {
        console.log(`   - ${region.name} (${region.regionId}) - Pricing entries: ${region.pricing?.length || 0}`);
      });
    }

    console.log('\n🎉 Region status fix completed!');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the fix
if (require.main === module) {
  fixRegionStatus();
}

module.exports = { fixRegionStatus };
