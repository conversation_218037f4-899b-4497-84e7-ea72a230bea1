"use client";
import { useState } from "react";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardBody } from "@material-tailwind/react";
import {
  ServerIcon,
  CpuIcon,
  HardDriveIcon,
  CheckIcon,
  ArrowLeftIcon,
  GlobeIcon,
  ShieldIcon,
  ClockIcon
} from "lucide-react";

// Modern OS Icons Components
const UbuntuIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z"/>
    </svg>
  </div>
);

const CentOSIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z"/>
    </svg>
  </div>
);

const DebianIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
    </svg>
  </div>
);

const WindowsIcon = ({ className }) => (
  <div className={`${className} bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center`}>
    <svg viewBox="0 0 24 24" className="w-3/4 h-3/4 text-white" fill="currentColor">
      <path d="M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z"/>
    </svg>
  </div>
);

export default function ConfigureDedicatedPage() {
  const searchParams = useSearchParams();
  const t = useTranslations("dedicated_configure");

  // Dedicated Server Plans Data (matching the data from the main page)
  const dedicatedPlans = [
    {
      id: "ds-10",
      name: "DS 10",
      processor: "AMD RYZEN 12",
      price: 1290,
      originalPrice: 1439,
      cpu: "AMD Ryzen 9 7900",
      cpuDetails: "12 × 3.70 GHz",
      ram: "32 GB REG ECC",
      ramDetails: "Up to 128 GB RAM",
      storage: "1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB"
    },
    {
      id: "ds-20",
      name: "DS 20",
      processor: "AMD RYZEN 12",
      price: 1470,
      originalPrice: 1618,
      cpu: "AMD Ryzen 9 7900",
      cpuDetails: "12 × 3.70 GHz",
      ram: "64 GB REG ECC",
      ramDetails: "Up to 128 GB RAM",
      storage: "1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB"
    },
    {
      id: "ds-30",
      name: "DS 30",
      processor: "AMD RYZEN 12",
      price: 1590,
      originalPrice: 1737,
      cpu: "AMD Ryzen 9 7900",
      cpuDetails: "12 × 3.70 GHz",
      ram: "64 GB REG ECC",
      ramDetails: "Up to 128 GB RAM",
      storage: "2 × 1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB"
    },
    {
      id: "ds-40",
      name: "DS 40",
      processor: "AMD TURIN 32",
      price: 3200,
      originalPrice: 3558,
      cpu: "AMD EPYC 9355P",
      cpuDetails: "32 × 3.55 GHz (4.20 max)",
      ram: "128 GB REG ECC",
      ramDetails: "Up to 768 GB RAM",
      storage: "2 × 1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB"
    },
    {
      id: "ds-50",
      name: "DS 50",
      processor: "AMD TURIN 64",
      price: 7120,
      originalPrice: 7925,
      cpu: "AMD Epyc 9555P",
      cpuDetails: "64 × 3.2 GHz (4.20 max)",
      ram: "192 GB REG ECC",
      ramDetails: "Up to 1152 GB RAM",
      storage: "2 × 1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB"
    }
  ];

  // Initialize plan immediately from URL params
  const planId = searchParams.get('plan');
  const initialPlan = dedicatedPlans.find(p => p.id === planId) || dedicatedPlans[0]; // Fallback to first plan

  const [selectedPlan, setSelectedPlan] = useState(initialPlan);
  const [selectedOS, setSelectedOS] = useState("ubuntu-20.04");
  const [selectedLocation, setSelectedLocation] = useState("france");
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [additionalIPs, setAdditionalIPs] = useState(0);
  const [backupEnabled, setBackupEnabled] = useState(false);
  const [quantity, setQuantity] = useState(1);

  // Operating Systems
  const operatingSystems = [
    { id: "ubuntu-20.04", name: t('operating_systems.ubuntu-20.04') || "Ubuntu 20.04 LTS", icon: UbuntuIcon, type: "linux" },
    { id: "ubuntu-22.04", name: t('operating_systems.ubuntu-22.04') || "Ubuntu 22.04 LTS", icon: UbuntuIcon, type: "linux" },
    { id: "centos-8", name: t('operating_systems.centos-8') || "CentOS 8", icon: CentOSIcon, type: "linux" },
    { id: "debian-11", name: t('operating_systems.debian-11') || "Debian 11", icon: DebianIcon, type: "linux" },
    { id: "windows-2019", name: t('operating_systems.windows-2019') || "Windows Server 2019", icon: WindowsIcon, type: "windows" },
    { id: "windows-2022", name: t('operating_systems.windows-2022') || "Windows Server 2022", icon: WindowsIcon, type: "windows" }
  ];

  // Locations
  const locations = [
    { id: "france", name: t('locations.france') || "France", flag: "🇫🇷", ping: "5ms" },
    { id: "germany", name: t('locations.germany') || "Germany", flag: "🇩🇪", ping: "15ms" },
    { id: "netherlands", name: t('locations.netherlands') || "Netherlands", flag: "🇳🇱", ping: "20ms" },
    { id: "usa", name: t('locations.usa') || "USA", flag: "🇺🇸", ping: "120ms" }
  ];



  const calculateTotal = () => {
    if (!selectedPlan) return 0;
    let total = selectedPlan.price;

    // Additional IPs cost (higher for dedicated servers)
    total += additionalIPs * 50; // 50 MAD per additional IP for dedicated servers

    // Backup cost
    if (backupEnabled) {
      total += Math.round(selectedPlan.price * 0.15); // 15% of server price for backup
    }

    // Apply quantity
    total *= quantity;

    // Period multiplier
    const multipliers = {
      monthly: 1,
      quarterly: 3 * 0.95, // 5% discount
      annually: 12 * 0.85   // 15% discount
    };

    return total * multipliers[selectedPeriod];
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Mobile Optimized */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
            <div className="flex items-center gap-3 sm:gap-4 w-full sm:w-auto">
              <Button
                variant="outlined"
                size="sm"
                onClick={() => window.history.back()}
                className="border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0"
              >
                <ArrowLeftIcon className="w-4 h-4 sm:mr-2" />
                <span className="hidden sm:inline">{t('back') || "Retour"}</span>
              </Button>
              <div className="min-w-0 flex-1">
                <Typography variant="h4" className="text-lg sm:text-2xl text-gray-900 font-bold truncate">
                  {t('configure_dedicated') || "Configurer votre Serveur Dédié"}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 truncate">
                  {selectedPlan.name}
                </Typography>
              </div>
            </div>
            <div className="text-left sm:text-right w-full sm:w-auto flex-shrink-0">
              <Typography className="text-xs sm:text-sm text-gray-500">À partir de</Typography>
              <Typography variant="h3" className="text-lg sm:text-2xl text-blue-600 font-bold">
                {selectedPlan.price} MAD/mois
              </Typography>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Configuration Panel - Mobile Optimized */}
          <div className="lg:col-span-2 space-y-6 sm:space-y-8">

            {/* Plan Summary - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                  <div className="w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                    <ServerIcon className="w-5 sm:w-6 h-5 sm:h-6 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <Typography variant="h5" className="text-lg sm:text-xl text-gray-900 font-bold truncate">
                      {selectedPlan.name}
                    </Typography>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs sm:text-sm text-gray-600 font-medium">{selectedPlan.processor}</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <CpuIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">CPU</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan.cpu}</div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <div className="w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2"></div>
                    <div className="text-xs sm:text-sm text-gray-600">RAM</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan.ram}</div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <HardDriveIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">Storage</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan.storage}</div>
                  </div>
                  <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg">
                    <GlobeIcon className="w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xs sm:text-sm text-gray-600">Traffic</div>
                    <div className="font-bold text-sm sm:text-base text-gray-900">{selectedPlan.traffic}</div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Billing Period - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  1. {t('billing_period') || "Période de facturation"}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('billing_period_desc') || "Choisissez votre période de facturation"}
                </Typography>
                <div className="space-y-3">
                  {[
                    { id: 'monthly', label: t('monthly') || 'Mensuel', discount: '', price: selectedPlan.price },
                    { id: 'quarterly', label: t('quarterly') || 'Trimestriel', discount: t('quarterly_discount') || '5% de réduction', price: Math.round(selectedPlan.price * 3 * 0.95) },
                    { id: 'annually', label: t('annually') || 'Annuel', discount: t('annually_discount') || '15% de réduction', price: Math.round(selectedPlan.price * 12 * 0.85) }
                  ].map((period) => (
                    <div
                      key={period.id}
                      onClick={() => setSelectedPeriod(period.id)}
                      className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedPeriod === period.id
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                          <div className={`w-4 h-4 rounded-full border-2 flex-shrink-0 ${
                            selectedPeriod === period.id ? 'border-blue-600 bg-blue-600' : 'border-gray-300'
                          }`}>
                            {selectedPeriod === period.id && (
                              <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                            )}
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="font-medium text-sm sm:text-base text-gray-900">{period.label}</div>
                            {period.discount && (
                              <div className="text-xs sm:text-sm text-green-600 font-medium">{period.discount}</div>
                            )}
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0">
                          <div className="font-bold text-sm sm:text-base text-gray-900">{period.price} MAD</div>
                          <div className="text-xs sm:text-sm text-gray-500">
                            {period.id === 'monthly' ? t('per_month') || '/mois' : period.id === 'quarterly' ? '/trimestre' : '/an'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Operating System Selection - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  2. {t('operating_system') || "Système d'exploitation"}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('os_desc') || "Sélectionnez votre système d'exploitation"}
                </Typography>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {operatingSystems.map((os) => (
                    <div
                      key={os.id}
                      onClick={() => setSelectedOS(os.id)}
                      className={`relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedOS === os.id
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-3 sm:gap-4">
                        <os.icon className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-sm sm:text-base text-gray-900 break-words">{os.name}</div>
                          <div className="text-xs sm:text-sm text-gray-500 mt-1">
                            {os.type === 'linux' ? 'Linux Distribution' : 'Windows Server'}
                          </div>
                        </div>
                        {selectedOS === os.id && (
                          <CheckIcon className="w-5 h-5 text-blue-600 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Location Selection - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  3. {t('location') || "Localisation"}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('location_desc') || "Choisissez la localisation de votre serveur"}
                </Typography>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {locations.map((location) => (
                    <div
                      key={location.id}
                      onClick={() => setSelectedLocation(location.id)}
                      className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedLocation === location.id
                          ? 'border-blue-600 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center gap-3 sm:gap-4">
                        <div className="text-2xl sm:text-3xl flex-shrink-0">{location.flag}</div>
                        <div className="min-w-0 flex-1">
                          <div className="font-medium text-sm sm:text-base text-gray-900 break-words">{location.name}</div>
                          <div className="text-xs sm:text-sm text-gray-500 mt-1">Ping: {location.ping}</div>
                        </div>
                        {selectedLocation === location.id && (
                          <CheckIcon className="w-5 h-5 text-blue-600 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Additional Options - Mobile Optimized */}
            <Card className="shadow-lg">
              <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4">
                  4. {t('additional_options') || "Options supplémentaires"}
                </Typography>
                <Typography className="text-sm sm:text-base text-gray-600 mb-4">
                  {t('additional_options_desc') || "Personnalisez votre serveur"}
                </Typography>
                <div className="space-y-4">
                  {/* Additional IPs */}
                  <div className="p-3 sm:p-4 border-2 border-gray-200 rounded-lg">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-sm sm:text-base text-gray-900">
                          {t('additional_ips') || "Adresses IP supplémentaires"}
                        </div>
                        <div className="text-xs sm:text-sm text-gray-500 mt-1">50 MAD/mois par IP</div>
                      </div>
                      <div className="flex items-center gap-2 sm:gap-3 justify-center sm:justify-end">
                        <Button
                          size="sm"
                          variant="outlined"
                          className="w-8 h-8 p-0 border-gray-300 hover:border-blue-600"
                          onClick={() => setAdditionalIPs(Math.max(0, additionalIPs - 1))}
                          disabled={additionalIPs === 0}
                        >
                          -
                        </Button>
                        <div className="w-10 h-8 bg-gray-100 rounded flex items-center justify-center">
                          <span className="font-bold text-sm text-gray-900">{additionalIPs}</span>
                        </div>
                        <Button
                          size="sm"
                          variant="outlined"
                          className="w-8 h-8 p-0 border-gray-300 hover:border-blue-600"
                          onClick={() => setAdditionalIPs(additionalIPs + 1)}
                        >
                          +
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Backup Service */}
                  <div className="p-3 sm:p-4 border-2 border-gray-200 rounded-lg">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-sm sm:text-base text-gray-900">
                          {t('backup_service') || "Service de sauvegarde"}
                        </div>
                        <div className="text-xs sm:text-sm text-gray-500 mt-1">
                          +{Math.round(selectedPlan.price * 0.15)} MAD/mois (15% du prix du serveur)
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer flex-shrink-0">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={backupEnabled}
                          onChange={(e) => setBackupEnabled(e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-600/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Summary Panel - Mobile Optimized */}
          <div className="lg:col-span-1">
            <div className="sticky top-4 sm:top-8">
              <Card className="shadow-lg">
                <CardBody className="p-4 sm:p-6">
                <Typography variant="h6" className="text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6">
                  Résumé de la commande
                </Typography>

                <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                  <div className="flex justify-between items-start">
                    <div className="min-w-0 flex-1 pr-2">
                      <span className="text-sm sm:text-base text-gray-600">{selectedPlan.name}</span>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">Quantité:</span>
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            variant="outlined"
                            onClick={() => setQuantity(Math.max(1, quantity - 1))}
                            disabled={quantity === 1}
                            className="w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0"
                          >
                            -
                          </Button>
                          <span className="w-8 text-center font-medium text-xs">{quantity}</span>
                          <Button
                            size="sm"
                            variant="outlined"
                            onClick={() => setQuantity(Math.min(10, quantity + 1))}
                            disabled={quantity === 10}
                            className="w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0"
                          >
                            +
                          </Button>
                        </div>
                      </div>
                    </div>
                    <span className="font-medium text-sm sm:text-base flex-shrink-0">{selectedPlan.price * quantity} MAD</span>
                  </div>

                  {additionalIPs > 0 && (
                    <div className="flex justify-between items-start">
                      <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">IPs additionnelles ({additionalIPs} × {quantity})</span>
                      <span className="font-medium text-sm sm:text-base flex-shrink-0">{additionalIPs * 50 * quantity} MAD</span>
                    </div>
                  )}

                  {backupEnabled && (
                    <div className="flex justify-between items-start">
                      <span className="text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2">Sauvegarde automatique × {quantity}</span>
                      <span className="font-medium text-sm sm:text-base flex-shrink-0">{Math.round(selectedPlan.price * 0.15) * quantity} MAD</span>
                    </div>
                  )}

                  {selectedPeriod !== 'monthly' && (
                    <div className="flex justify-between items-start text-green-600">
                      <span className="text-sm sm:text-base min-w-0 flex-1 pr-2">Réduction ({selectedPeriod === 'quarterly' ? '5%' : '15%'})</span>
                      <span className="text-sm sm:text-base flex-shrink-0">-{Math.round(selectedPlan.price * quantity * (selectedPeriod === 'quarterly' ? 3 * 0.05 : 12 * 0.15))} MAD</span>
                    </div>
                  )}

                  <hr className="border-gray-200" />

                  <div className="flex justify-between items-start text-base sm:text-lg font-bold">
                    <span className="min-w-0 flex-1 pr-2">Total</span>
                    <span className="text-blue-600 flex-shrink-0 text-right">{Math.round(calculateTotal())} MAD/{selectedPeriod === 'monthly' ? 'mois' : selectedPeriod === 'quarterly' ? 'trimestre' : 'an'}</span>
                  </div>

                  {/* Configuration Summary - Mobile Optimized */}
                  <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200">
                    <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                      <div className="flex flex-wrap"><strong>OS:</strong>&nbsp;<span className="break-all">{operatingSystems.find(os => os.id === selectedOS)?.name}</span></div>
                      <div className="flex flex-wrap"><strong>Localisation:</strong>&nbsp;<span className="break-all">{locations.find(loc => loc.id === selectedLocation)?.name}</span></div>
                      <div className="flex flex-wrap"><strong>Période:</strong>&nbsp;<span className="break-all">{selectedPeriod === 'monthly' ? 'Mensuel' : selectedPeriod === 'quarterly' ? 'Trimestriel' : 'Annuel'}</span></div>
                    </div>
                  </div>
                </div>

                  {/* Order Button */}
                <Button
                  size="lg"
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold"
                  onClick={() => {
                    // Handle order submission
                    console.log('Order submitted:', {
                      plan: selectedPlan,
                      period: selectedPeriod,
                      os: selectedOS,
                      location: selectedLocation,
                      additionalIPs,
                      backup: backupEnabled,
                      quantity,
                      total: calculateTotal()
                    });
                  }}
                >
                  Ajouter au panier
                </Button>

                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2">
                    <ShieldIcon className="w-3 sm:w-4 h-3 sm:h-4" />
                    <span>Paiement sécurisé</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500">
                    <ClockIcon className="w-3 sm:w-4 h-3 sm:h-4" />
                    <span>Configuration instantanée</span>
                  </div>
                </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}