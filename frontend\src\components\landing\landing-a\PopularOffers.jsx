'use client'
import React from "react";
import { ArrowUpRightIcon } from "@heroicons/react/24/solid";
import Link from "next/link";
import Image from "next/image";
import popularOffersImg1 from "/public/images/home/<USER>";
import popularOffersImg2 from "/public/images/home/<USER>";
import popularOffersImg3 from "/public/images/home/<USER>";
import { Typography } from "@material-tailwind/react";


const popularOffersData = [
    {
        id: 1,
        title: "popularOffersData.0.title",
        description: [
            "popularOffersData.0.description.0",
            "popularOffersData.0.description.1",
            "popularOffersData.0.description.2",
        ],
        price: "3000",
        unit: " DH HT/m",
        imgSrc: popularOffersImg1,
        url: "/web-development"
    },
    {
        id: 2,
        title: "popularOffersData.1.title",
        description: [
            "popularOffersData.1.description.0",
            "popularOffersData.1.description.1",
            "popularOffersData.1.description.2",
        ],
        price: "429",
        unit: " DH HT/m",
        imgSrc: popularOffersImg2,
        url: "/hosting"
    },
    {
        id: 3,
        title: "popularOffersData.2.title",
        description: [
            "popularOffersData.2.description.0",
            "popularOffersData.2.description.1",
            "popularOffersData.2.description.2",
        ],
        price: "899",
        unit: " DH HT/m",
        imgSrc: popularOffersImg3,
        url: "/cloud-maroc"
    },
];

const PopularOffers = ({ t }) => {
    return (
        <section className="w-full bg-[#F5F6F8]">
            <div className="max-w-[1400px] mx-auto py-12 px-4">
                <div className="text-center mb-8 max-w-md mx-auto">
                    <Typography
                        variant="h2"
                        className="text-3xl font-semibold">
                        {t('popular_offers')}
                    </Typography>
                    <p className="text-gray-600 mt-2">
                        {t('contact_assistance')}
                    </p>
                </div>

                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:max-w-6xl m-auto">
                    {popularOffersData.map((offer) => (
                        <div
                            key={offer.id}
                            className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 flex flex-col"
                        >
                            <Image
                                src={offer.imgSrc}
                                alt={t(offer.title)}
                                width={400}
                                height={300}
                                quality={100}
                                className="w-full h-44 object-cover p-1 rounded-lg"
                            />
                            <div className="px-6 pt-6 pb-0 flex flex-col h-full  flex-grow">
                                <Typography
                                    variant="h3"
                                    className="text-xl font-bold text-gray-800 mb-4">
                                    {t(offer.title)}
                                </Typography>
                                <ul className="gap-y-2 flex flex-col mb-6">
                                    {offer.description.map((desc, idx) => (
                                        <li key={idx} className="flex items-start gap-2 text-gray-600">
                                            <span className="text-blue-500">✔</span>
                                            <span className="text-sm">{t(desc)}</span>
                                        </li>
                                    ))}
                                </ul>

                                <div className="flex items-center justify-between border-t border-gray-400 py-4 mt-auto">
                                    <div className="flex flex-col">
                                        <span className="text-xs font-normal text-gray-800">
                                            {t('starting_from')}
                                        </span>
                                        <span className="text-xl font-bold font-poppins text-black">
                                            {offer.price}
                                            <span className="text-xs font-normal">
                                                {offer.unit}
                                            </span>
                                        </span>
                                    </div>

                                    <Link
                                        href={offer.url}
                                        // target="_blank"
                                        className="text-xs rounded-full text-black font-semibold flex flex-row gap-x-2 border border-secondary py-2 px-5"
                                    >
                                        <span>
                                            {t('view_offers')}
                                        </span>
                                        <span>
                                            <ArrowUpRightIcon className="w-4 my-auto" />
                                        </span>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

            </div>
        </section>
    );
};

export default PopularOffers;
