const express = require('express');

const { setBilling, getBilling, updateBilling } = require("../controllers/billingController");
const { authUserIsVerified } = require('../midelwares/authorization');

const billingRouter = express.Router();
billingRouter.post('/', asyncBackFrontEndLang, authUserIsVerified, setBilling);
billingRouter.get('/:userId', asyncBackFrontEndLang, authUserIsVerified, getBilling);
billingRouter.put('/:userId', asyncBackFrontEndLang, authUserIsVerified, updateBilling);

module.exports = billingRouter;