"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socket.io-parser";
exports.ids = ["vendor-chunks/socket.io-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/socket.io-parser/build/esm-debug/binary.js":
/*!*****************************************************************!*\
  !*** ./node_modules/socket.io-parser/build/esm-debug/binary.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deconstructPacket: () => (/* binding */ deconstructPacket),\n/* harmony export */   reconstructPacket: () => (/* binding */ reconstructPacket)\n/* harmony export */ });\n/* harmony import */ var _is_binary_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-binary.js */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js\");\n\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */ function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return {\n        packet: pack,\n        buffers: buffers\n    };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data) return data;\n    if ((0,_is_binary_js__WEBPACK_IMPORTED_MODULE_0__.isBinary)(data)) {\n        const placeholder = {\n            _placeholder: true,\n            num: buffers.length\n        };\n        buffers.push(data);\n        return placeholder;\n    } else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for(let i = 0; i < data.length; i++){\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    } else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for(const key in data){\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */ function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data) return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" && data.num >= 0 && data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        } else {\n            throw new Error(\"illegal attachments\");\n        }\n    } else if (Array.isArray(data)) {\n        for(let i = 0; i < data.length; i++){\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    } else if (typeof data === \"object\") {\n        for(const key in data){\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/build/esm-debug/binary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/socket.io-parser/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decoder: () => (/* binding */ Decoder),\n/* harmony export */   Encoder: () => (/* binding */ Encoder),\n/* harmony export */   PacketType: () => (/* binding */ PacketType),\n/* harmony export */   protocol: () => (/* binding */ protocol)\n/* harmony export */ });\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/binary.js\");\n/* harmony import */ var _is_binary_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is-binary.js */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-parser\"); // debug()\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */ const RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\"\n];\n/**\n * Protocol version.\n *\n * @public\n */ const protocol = 5;\nvar PacketType;\n(function(PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */ class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */ constructor(replacer){\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */ encode(obj) {\n        debug(\"encoding packet %j\", obj);\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if ((0,_is_binary_js__WEBPACK_IMPORTED_MODULE_2__.hasBinary)(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT ? PacketType.BINARY_EVENT : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id\n                });\n            }\n        }\n        return [\n            this.encodeAsString(obj)\n        ];\n    }\n    /**\n     * Encode packet as string.\n     */ encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT || obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        debug(\"encoded %j as %s\", obj, str);\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */ encodeAsBinary(obj) {\n        const deconstruction = (0,_binary_js__WEBPACK_IMPORTED_MODULE_1__.deconstructPacket)(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */ class Decoder extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_0__.Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */ constructor(reviver){\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */ add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            } else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        } else if ((0,_is_binary_js__WEBPACK_IMPORTED_MODULE_2__.isBinary)(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            } else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        } else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */ decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0))\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT || p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while(str.charAt(++i) !== \"-\" && i != str.length){}\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while(++i){\n                const c = str.charAt(i);\n                if (\",\" === c) break;\n                if (i === str.length) break;\n            }\n            p.nsp = str.substring(start, i);\n        } else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while(++i){\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length) break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            } else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        debug(\"decoded %s as %j\", str, p);\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        } catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch(type){\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && (typeof payload[0] === \"number\" || typeof payload[0] === \"string\" && RESERVED_EVENTS.indexOf(payload[0]) === -1);\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */ destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */ class BinaryReconstructor {\n    constructor(packet){\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */ takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = (0,_binary_js__WEBPACK_IMPORTED_MODULE_1__.reconstructPacket)(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */ finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js":
/*!********************************************************************!*\
  !*** ./node_modules/socket.io-parser/build/esm-debug/is-binary.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasBinary: () => (/* binding */ hasBinary),\n/* harmony export */   isBinary: () => (/* binding */ isBinary)\n/* harmony export */ });\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj)=>{\n    return typeof ArrayBuffer.isView === \"function\" ? ArrayBuffer.isView(obj) : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" || typeof Blob !== \"undefined\" && toString.call(Blob) === \"[object BlobConstructor]\";\nconst withNativeFile = typeof File === \"function\" || typeof File !== \"undefined\" && toString.call(File) === \"[object FileConstructor]\";\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */ function isBinary(obj) {\n    return withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)) || withNativeBlob && obj instanceof Blob || withNativeFile && obj instanceof File;\n}\nfunction hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for(let i = 0, l = obj.length; i < l; i++){\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON && typeof obj.toJSON === \"function\" && arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for(const key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-parser/build/esm-debug/is-binary.js\n");

/***/ })

};
;