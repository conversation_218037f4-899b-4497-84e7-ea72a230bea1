'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Typography } from '@material-tailwind/react';
import {
  Server,
  Play,
  Square,
  RotateCcw,
  Settings,
  Monitor,
  Cpu,
  HardDrive,
  Wifi,
  MapPin,
  Calendar,
  Activity,
  Power,
  PowerOff,
  Shield
} from 'lucide-react';
import vpsService from '../../../services/vpsService';

export default function HostingOrdersPage() {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const status = searchParams.get('status');
  const item = searchParams.get('item');

  // Payment status state
  const [paymentStatus, setPaymentStatus] = useState(null);

  // VPS instances data from API
  const [vpsServers, setVpsServers] = useState([]);
  const [vpsLoading, setVpsLoading] = useState(true);
  const [vpsError, setVpsError] = useState(null);

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setTimeout(() => setPaymentStatus(null), 5000);
    }
  }, [status, item]);

  // Fetch VPS instances from API
  useEffect(() => {
    const fetchVPSInstances = async () => {
      try {
        setVpsLoading(true);
        setVpsError(null);

        const response = await vpsService.getUserInstances();
        console.log("🔍 VPS Instances API Response:", response);
        console.log("🔍 Response data:", response.data);

        // Handle response structure
        let instancesData = [];
        if (response.data && response.data.data && Array.isArray(response.data.data)) {
          instancesData = response.data.data;
          console.log("🔍 Using response.data.data:", instancesData);
        }
        
        console.log("🔍 Final instancesData:", instancesData);

        // Transform API response to match UI structure (adapté pour Contabo)
        const transformedInstances = instancesData.map(instance => {
          console.log("🔍 Transforming instance:", instance);
          return {
            id: instance.id || instance.instanceId,
            server: instance.ip || instance.ipAddress || instance.server || 'N/A',
            defaultUser: instance.raw?.defaultUser || instance.defaultUser || 'root',
            hostSystem: instance.raw?.vHostName || instance.hostId || instance.hostSystem || instance.id,
            status: instance.status || 'unknown',
            name: instance.name || instance.displayName,
            plan: {
              name: instance.raw?.productName || instance.plan || instance.productId || 'N/A',
              id: instance.productId || instance.plan
            },
            region: instance.region || 'N/A',
            dataCenter: instance.dataCenter || 'N/A',
            ipv4: instance.ip,
            ipv6: instance.ipv6,
            createdAt: instance.createdAt,
            // Données supplémentaires Contabo
            specs: {
              ram: instance.raw?.ramMb ? `${Math.round(instance.raw.ramMb / 1024)} GB` : 'N/A',
              cpu: instance.raw?.cpuCores ? `${instance.raw.cpuCores} cores` : 'N/A',
              disk: instance.raw?.diskMb ? `${Math.round(instance.raw.diskMb / 1024)} GB` : 'N/A',
              osType: instance.raw?.osType || 'N/A'
            }
          };
        });

        setVpsServers(transformedInstances);
      } catch (error) {
        console.error("Error fetching VPS instances:", error);
        setVpsError(error.message || "Failed to load VPS instances");
        setVpsServers([]);
      } finally {
        setVpsLoading(false);
      }
    };

    fetchVPSInstances();
  }, []);

  // VPS Actions
  const handleVPSAction = async (serverId, action) => {
    console.log(`🔄 VPS Action: ${action} on server ${serverId}`);
    // TODO: Implémenter les actions Contabo
    alert(`Action ${action} sur le serveur ${serverId} - À implémenter avec l'API Contabo`);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'running': return 'text-green-600 bg-green-100';
      case 'stopped': return 'text-red-600 bg-red-100';
      case 'starting': return 'text-blue-600 bg-blue-100';
      case 'stopping': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'running': return <Activity className="w-4 h-4" />;
      case 'stopped': return <PowerOff className="w-4 h-4" />;
      default: return <Power className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Payment Status Alert */}
        {paymentStatus && (
          <div className={`mb-6 p-4 rounded-lg ${
            paymentStatus === 'success' 
              ? 'bg-green-100 border border-green-400 text-green-700'
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            <p className="font-medium">
              {paymentStatus === 'success' 
                ? '✅ Paiement réussi ! Votre commande est en cours de traitement.'
                : '❌ Échec du paiement. Veuillez réessayer.'}
            </p>
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-8">
          <Typography
            variant="h1"
            className="text-2xl md:text-3xl font-bold text-gray-900 mb-2"
          >
            Gestion des Serveurs VPS
          </Typography>
          <Typography className="text-sm md:text-base text-gray-600">
            Gérez vos serveurs VPS Contabo depuis un seul endroit
          </Typography>
        </div>

        {/* VPS Header */}
        <div className="mb-6">
          <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Server className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900">Serveurs VPS Contabo</h3>
              <p className="text-sm text-blue-700">{vpsServers.length} serveur(s) actif(s)</p>
            </div>
          </div>
        </div>

        {/* VPS Content */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {vpsLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Chargement des serveurs VPS...</span>
            </div>
          ) : vpsError ? (
            <div className="text-center py-12">
              <Server className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <Typography className="text-lg text-red-600 mb-2">
                Erreur de chargement
              </Typography>
              <Typography className="text-gray-500 mb-4">
                {vpsError}
              </Typography>
              <button 
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Réessayer
              </button>
            </div>
          ) : vpsServers.length > 0 ? (
            <div className="p-6">
              <div className="grid gap-6">
                {vpsServers.map((server) => (
                  <div key={server.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    {/* Server Header */}
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-4">
                      <div className="flex items-center gap-4 mb-4 lg:mb-0">
                        <div className="p-3 bg-blue-100 rounded-lg">
                          <Server className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{server.name}</h3>
                          <p className="text-sm text-gray-500">ID: {server.id}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(server.status)}`}>
                          {getStatusIcon(server.status)}
                          {server.status}
                        </span>
                      </div>
                    </div>

                    {/* Server Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="flex items-center gap-2">
                        <Wifi className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">IP Address</p>
                          <p className="text-sm font-medium">{server.server}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">Région</p>
                          <p className="text-sm font-medium">{server.region}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Cpu className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">Plan</p>
                          <p className="text-sm font-medium">{server.plan.name}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-xs text-gray-500">Créé le</p>
                          <p className="text-sm font-medium">
                            {server.createdAt ? new Date(server.createdAt).toLocaleDateString('fr-FR') : 'N/A'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Specifications */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                      <div className="text-center">
                        <Cpu className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                        <p className="text-xs text-gray-500">CPU</p>
                        <p className="text-sm font-medium">{server.specs.cpu}</p>
                      </div>
                      <div className="text-center">
                        <Monitor className="w-5 h-5 text-green-600 mx-auto mb-1" />
                        <p className="text-xs text-gray-500">RAM</p>
                        <p className="text-sm font-medium">{server.specs.ram}</p>
                      </div>
                      <div className="text-center">
                        <HardDrive className="w-5 h-5 text-purple-600 mx-auto mb-1" />
                        <p className="text-xs text-gray-500">Disque</p>
                        <p className="text-sm font-medium">{server.specs.disk}</p>
                      </div>
                      <div className="text-center">
                        <Settings className="w-5 h-5 text-orange-600 mx-auto mb-1" />
                        <p className="text-xs text-gray-500">OS</p>
                        <p className="text-sm font-medium">{server.specs.osType}</p>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={() => handleVPSAction(server.id, 'start')}
                        disabled={server.status === 'running'}
                        className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Play className="w-4 h-4" />
                        Démarrer
                      </button>
                      
                      <button
                        onClick={() => handleVPSAction(server.id, 'stop')}
                        disabled={server.status === 'stopped'}
                        className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Square className="w-4 h-4" />
                        Arrêter
                      </button>
                      
                      <button
                        onClick={() => handleVPSAction(server.id, 'restart')}
                        className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <RotateCcw className="w-4 h-4" />
                        Redémarrer
                      </button>
                      
                      <button
                        onClick={() => handleVPSAction(server.id, 'console')}
                        className="flex items-center gap-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <Monitor className="w-4 h-4" />
                        Console
                      </button>

                      <button
                        onClick={() => handleVPSAction(server.id, 'rescue')}
                        className="flex items-center gap-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                      >
                        <Shield className="w-4 h-4" />
                        Rescue
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <Typography className="text-lg text-gray-600 mb-2">
                Aucun serveur VPS trouvé
              </Typography>
              <Typography className="text-gray-500">
                Vous n&apos;avez pas encore de serveurs VPS
              </Typography>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
