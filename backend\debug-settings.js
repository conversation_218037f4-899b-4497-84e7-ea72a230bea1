/**
 * Debug script to test site settings validation and saving
 * Run this with: node debug-settings.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import the model and validation
const SiteSettings = require('./models/SiteSettings');
const { validateSiteSettings } = require('./middlewares/requests/siteSettingsRequest');

async function testSiteSettings() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://0.0.0.0:27017/zn_ztech');
    console.log('✅ Connected to database');

    // Test data that should work
    const testData = {
      general: {
        siteName: 'Test Site',
        siteDescription: 'Test Description',
        contactEmail: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Test Street',
        socialLinks: {
          linkedin: 'https://linkedin.com/company/test',
          twitter: 'https://twitter.com/test',
          facebook: 'https://facebook.com/test'
        }
      },
      seo: {
        defaultTitle: 'Test Site - Home',
        defaultDescription: 'This is a test site description',
        defaultKeywords: 'test, site, keywords',
        favicon: 'https://example.com/favicon.ico',
        googleAnalyticsId: 'G-TEST123456',
        googleSiteVerification: 'test-verification-code',
        bingVerification: 'test-bing-code',
        robotsTxt: 'User-agent: *\nAllow: /',
        sitemapEnabled: true
      }
    };

    console.log('\n📝 Test 1: Validation Test');
    
    // Mock request and response objects for validation test
    const mockReq = { body: testData };
    const mockRes = {
      status: (code) => ({
        json: (data) => {
          console.log(`❌ Validation failed with status ${code}:`, data);
          return mockRes;
        }
      })
    };
    const mockNext = () => {
      console.log('✅ Validation passed');
    };

    // Test validation
    await validateSiteSettings(mockReq, mockRes, mockNext);

    console.log('\n📝 Test 2: Database Save Test');
    
    // Test direct database save
    const settings = await SiteSettings.create(testData);
    console.log('✅ Settings saved to database:', {
      id: settings._id,
      siteName: settings.general.siteName,
      defaultTitle: settings.seo.defaultTitle
    });

    console.log('\n📝 Test 3: Update Test');
    
    // Test update
    settings.general.siteName = 'Updated Test Site';
    await settings.save();
    console.log('✅ Settings updated successfully');

    console.log('\n📝 Test 4: Partial Update Test');
    
    // Test partial update (only general section)
    const partialData = {
      general: {
        siteName: 'Partially Updated Site',
        contactEmail: '<EMAIL>'
      }
    };

    const existingSettings = await SiteSettings.findOne();
    if (existingSettings) {
      existingSettings.general = { ...existingSettings.general.toObject(), ...partialData.general };
      await existingSettings.save();
      console.log('✅ Partial update successful');
    }

    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    if (error.name === 'ValidationError') {
      console.error('Validation errors:');
      Object.values(error.errors).forEach(err => {
        console.error(`- ${err.path}: ${err.message}`);
      });
    }
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');
  }
}

// Run the test
testSiteSettings();
