name: Deploy ztechEngineering to the PROD Environment

on:
  push:
    branches: [prod]

jobs:
  build:
    runs-on: ["self-hosted", "ztech-nextjs"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: install modules
        run: |
          npm i --force
      - name: build the frontend
        run: |
          npm run build
      - name: delete all the pm2 old processes
        run: |
          pm2 delete all || true
      - name: pm2 start for the frontend port=3001
        run: |
          npm run deploy
          pm2 save
      # - name: Restart Nginx
        # run: sudo service nginx restart