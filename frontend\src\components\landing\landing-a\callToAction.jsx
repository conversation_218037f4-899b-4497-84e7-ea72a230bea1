'use client'
import { ArrowUpRightIcon } from '@heroicons/react/24/solid';
import { Card, CardBody, Typography } from '@material-tailwind/react';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import aboutUsImg from "/public/images/home/<USER>";


function CallToAction({ t }) {
    return (
        <Card id='about-us'
            className="max-w-[1400px] mx-auto md:mt-20 mt-10 bg-gradient-to-br from-blue-600 to-blue-900 h-[550px] md:h-[450px] mb-[400px] overflow-visible relative">
            <video
                autoPlay
                loop
                muted
                playsInline
                className="rounded-lg absolute inset-0 w-full h-full object-cover transform scale-y-[-1]"
            >
                <source src="/videos/about-us-bg2.mp4" type="video/mp4" />
                Your browser does not support the video tag.
            </video>
            <CardBody className="md:p-8 my-4 relative">
                <Typography
                    variant="h3"
                    className="md:max-w-lg mx-auto text-white text-center">
                    {t('quote_call_to_action')}
                </Typography>
            </CardBody>

            <div className="absolute top-[100%] transform -translate-y-1/2 md:w-4/5 w-[95%] mx-auto left-0 right-0">
                <div className="relative h-[400px] rounded-xl overflow-hidden">
                    <Image
                        // src="/images/home/<USER>"
                        // src="/images/home/<USER>"
                        // src="/images/landing/cta-rb.png"
                        // src="/images/landing/cta2.jpg"
                        src={aboutUsImg}
                        alt="Team collaboration"
                        fill
                        className="object-cover rounded-xl md:max-w-3xl m-auto"
                        quality={100}
                    />
                </div>
                <Typography className="text-black text-center max-w-4xl mx-auto mt-6 rounded-lg">
                    {t('company_description')}
                </Typography>
                <div className="flex justify-center mt-4">
                    <Link
                        href="#contact-nous"
                        variant="filled"
                        color="white"
                        className="px-4 py-3 rounded-2xl bg-secondary flex flex-row gap-x-4 items-center hover:text-primary text-white hover:bg-white border-[#05144B33] border"
                    >
                        <span>{t('quote_request')}</span>
                        <span><ArrowUpRightIcon className='w-4 my-auto' /></span>
                    </Link>
                </div>
            </div>
        </Card>
    );
}

export default CallToAction;
