'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Card, 
  CardBody, 
  Button, 
  Textarea,
  Spinner 
} from '@material-tailwind/react';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle, 
  NotebookTabs, 
  Info
} from 'lucide-react';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import ticketService from '../../../../../services/ticketService';
import { 
  getStatusIcon, 
  getPriorityColor, 
  setServerMedia 
} from '../../../../../helpers/helpers';
import ImageModal from '@/components/admin/ImageModal';

// Status icon mapping for cleaner code
const STATUS_ICONS = {
  open: <Info className="text-blue-500" />,
  resolved: <CheckCircle className="text-green-500" />,
  closed: <XCircle className="text-red-500" />,
  pending: <Clock className="text-yellow-500" />,
  error: <AlertCircle className="text-red-500" />
};

const TicketDetailsPage = () => {
  const t = useTranslations('client');
  const { ticketId } = useParams();
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newComment, setNewComment] = useState('');
  const [comments, setComments] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null); // State for modal

  // Memoized fetch function for ticket data
  const fetchTicket = useCallback(async () => {
    if (!ticketId) return;

    try {
      setLoading(true);
      const response = await ticketService.getTicketById(ticketId);
      setTicket(response.data.ticket);
    } catch (err) {
      console.error('Error fetching ticket:', err);
      setError(t('fetch_error')); // Use translated error message
    } finally {
      setLoading(false);
    }
  }, [ticketId, t]);

  // Fetch ticket on mount or when ticketId changes
  useEffect(() => {
    fetchTicket();
  }, [fetchTicket]);

  // Memoized handler for adding new comments
  const handleAddComment = useCallback(() => {
    if (!newComment.trim()) return;

    const comment = {
      id: crypto.randomUUID(), // More reliable unique ID
      text: newComment,
      author: 'You', // TODO: Replace with authenticated user info
      createdAt: new Date().toISOString(),
    };

    setComments((prev) => [comment, ...prev]);
    setNewComment('');
  }, [newComment]);

  // Open image modal handler
  const openImageModal = (image) => {
    setSelectedImage(image);
  };
  const closeImageModal = (e) => {
    if (e) {
      e.preventDefault(); // Prevent default behavior
      e.stopPropagation(); // Stop event bubbling
    }
    setSelectedImage(null); // Close the modal
  };

  // Error state with retry option
  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-6 gap-4">
        <AlertCircle className="h-12 w-12 text-red-500" />
        <Typography variant="h5" className="text-red-600">
          {error}
        </Typography>
        <Button onClick={fetchTicket} color="blue">
          {t('retry')}
        </Button>
      </div>
    );
  }

  // Not found state
  if (!ticket) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Typography variant="h5" className="text-gray-600">
          {t('ticket_not_found')}
        </Typography>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <Typography className="flex gap-2 items-center mb-6 text-xl text-gray-900 font-inter">
        <NotebookTabs className="w-5 h-5" />
        {t('ticket_details')}:
      </Typography>

      <Card className="shadow-xl">
        <CardBody className="space-y-6">
          {/* Ticket Header */}
          <div className="flex flex-col gap-4 md:flex-row md:justify-between md:items-start">
            <div className="flex items-start gap-4">
              {STATUS_ICONS[ticket.status.toLowerCase()] || STATUS_ICONS.error}
              <div>
                <Typography variant="h5" className="font-semibold text-xl text-gray-900">
                  {ticket.subject}
                </Typography>
                <Typography className="text-gray-600">
                  {ticket.message}
                </Typography>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(ticket.priority)}`}>
                {t(ticket.priority)}
              </span>
              <Typography variant="small" className="text-gray-500">
                {t('created_on')}: {new Date(ticket.createdAt).toLocaleDateString("fr-FR")}
              </Typography>
            </div>
          </div>

          {/* Ticket Images */}
          {ticket.images?.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {ticket.images.map((image, index) => (
                <img
                  key={index}
                  src={setServerMedia(image)}
                  alt={`Ticket Image ${index + 1}`}
                  className="h-24 md:h-48 w-full object-cover rounded-lg shadow-md cursor-pointer hover:opacity-90 transition-opacity"
                  loading="lazy"
                  onClick={() => openImageModal(setServerMedia(image))}
                />
              ))}
            </div>
          )}

          {/* Image Modal */}
          <ImageModal
            isOpen={!!selectedImage} 
            imageSrc={selectedImage} 
            onClose={closeImageModal} 
          />

          {/* Ticket Details */}
          <div className="grid gap-4 md:grid-cols-2">
            <DetailItem label={t('contact_information')} value={`${ticket.creator.firstName} ${ticket.creator.lastName} (${ticket.creator.email})`} />
            <DetailItem label={t('category')} value={t(ticket.service)} />
            <DetailItem label={t('status')} value={t(ticket.status)} />
            <DetailItem 
              label={t('resolution_comment')} 
              value={ticket.resolutionComment || t('no_resolution_comment_available')}
              className={!ticket.resolutionComment ? 'text-gray-500' : ''}
            />
          </div>

          {/* Comments Section 
          <div className="space-y-4">
            <Typography variant="h5" className="text-gray-900 font-bold">
              {t('comments')}
            </Typography> */}

            
            {/* {comments.length > 0 ? (
              comments.map((comment) => (
                <Card key={comment.id} className="bg-gray-50">
                  <CardBody>
                    <div className="flex justify-between items-center mb-2">
                      <Typography className="font-medium text-gray-700">
                        {comment.author}
                      </Typography>
                      <Typography variant="small" className="text-gray-500">
                        {new Date(comment.createdAt).toLocaleString()}
                      </Typography>
                    </div>
                    <Typography className="text-gray-600">
                      {comment.text}
                    </Typography>
                  </CardBody>
                </Card>
              ))
            ) : (
              <Typography className="text-gray-500">
                {t('no_comments')}
              </Typography>
            )} */}

            {/* New comment input form 
            <div className="space-y-4">
              <Typography variant="h5" className="text-gray-900 font-bold">
                {t('add_a_comment')}
              </Typography>
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                rows={4}
                placeholder={t('write_comment_placeholder')}
                className="w-full border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              />
              <Button
                onClick={handleAddComment}
                color="blue"
                disabled={!newComment.trim()}
                className="rounded-lg"
              >
                {t('submit_comment')}
              </Button>
            </div>

          </div>
          */}
        </CardBody>
      </Card>
    </div>
  );
};

// Reusable Detail Item Component
const DetailItem = ({ label, value, className = '' }) => (
  <div>
    <Typography variant="small" className="font-medium text-gray-700">
      {label}:
    </Typography>
    <Typography className={`text-gray-600 ${className}`}>
      {value}
    </Typography>
  </div>
);

export default TicketDetailsPage;