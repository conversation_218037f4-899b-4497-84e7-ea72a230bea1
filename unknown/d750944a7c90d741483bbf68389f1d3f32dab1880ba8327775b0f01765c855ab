const mongoose = require('mongoose');
const JobStatus = require('../constants/enums/job-status');

const jobSchema = new mongoose.Schema({
  title: {
    type: {
      en: { type: String, required: true, trim: true },
      fr: { type: String, required: true, trim: true }
    },
    required: true,
  },
  department: {
    type: String,
    required: true,
    trim: true,
  },
  location: {
    type: String,
    required: true,
    trim: true,
  },
  type: {
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'internship'],
    default: 'full-time',
  },
  description: {
    type: {
      en: { type: String, required: true },
      fr: { type: String, required: true }
    },
    required: true,
  },
  requirements: {
    type: [{
      en: { type: String, required: true },
      fr: { type: String, required: true }
    }],
    default: [],
  },
  responsibilities: {
    type: [{
      en: { type: String, required: true },
      fr: { type: String, required: true }
    }],
    default: [],
  },
  benefits: {
    type: [{
      en: { type: String, required: true },
      fr: { type: String, required: true }
    }],
    default: [],
  },
  salary_range: {
    min: { type: Number },
    max: { type: Number },
    currency: { type: String },
  },
  status: {
    type: String,
    enum: Object.values(JobStatus),
    default: JobStatus.DRAFT,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  applications: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'JobApplication'
  }]
});

// Update the updatedAt field on save
jobSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const Job = mongoose.model('Job', jobSchema);

module.exports = Job;