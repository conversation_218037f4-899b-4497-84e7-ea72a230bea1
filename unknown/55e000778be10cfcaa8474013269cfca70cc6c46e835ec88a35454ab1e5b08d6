const CustomNotification = require('../../models/CustomNotification');
const User = require('../../models/User');
const { sendSimpleEmail } = require('../../routes/sendEmail/sendEmail');
const { customNotificationTemplate } = require('../../routes/sendEmail/emailTemplates');

/**
 * Get all custom notifications
 */
const getCustomNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;

    const query = {};
    if (status) {
      query.status = status;
    }

    const notifications = await CustomNotification.find(query)
      .populate('createdBy', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await CustomNotification.countDocuments(query);

    return res.status(200).json({
      success: true,
      data: notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error fetching custom notifications:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch custom notifications',
      error: error.message
    });
  }
};

/**
 * Create a new custom notification
 */
const createCustomNotification = async (req, res) => {
  try {
    const {
      title,
      message,
      scheduledDateTime,
      targetAudience = 'all',
      emailEnabled = true,
      inAppEnabled = true
    } = req.body;

    // Validation
    if (!title || !message || !scheduledDateTime) {
      return res.status(400).json({
        success: false,
        message: 'Title, message, and scheduled date/time are required'
      });
    }

    // Validate that at least one notification channel is enabled
    if (!emailEnabled && !inAppEnabled) {
      return res.status(400).json({
        success: false,
        message: 'At least one notification channel (email or in-app) must be enabled'
      });
    }

    // Check if scheduled time is in the future
    const scheduledDate = new Date(scheduledDateTime);
    if (scheduledDate <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Scheduled time must be in the future'
      });
    }

    const notification = await CustomNotification.create({
      title,
      message,
      scheduledDateTime: scheduledDate,
      targetAudience,
      emailEnabled,
      inAppEnabled,
      createdBy: req.user._id
    });

    const populatedNotification = await CustomNotification.findById(notification._id)
      .populate('createdBy', 'firstName lastName email');

    console.log(`[CUSTOM NOTIFICATION] Created notification: ${notification._id} scheduled for ${scheduledDate}`);

    return res.status(201).json({
      success: true,
      data: populatedNotification,
      message: 'Custom notification scheduled successfully'
    });
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error creating custom notification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create custom notification',
      error: error.message
    });
  }
};

/**
 * Update a custom notification
 */
const updateCustomNotification = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const notification = await CustomNotification.findById(id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Custom notification not found'
      });
    }

    // Don't allow updating sent notifications
    if (notification.status === 'sent') {
      return res.status(400).json({
        success: false,
        message: 'Cannot update a notification that has already been sent'
      });
    }

    // Validate that at least one notification channel is enabled
    const emailEnabled = updateData.emailEnabled !== undefined ? updateData.emailEnabled : notification.emailEnabled;
    const inAppEnabled = updateData.inAppEnabled !== undefined ? updateData.inAppEnabled : notification.inAppEnabled;

    if (!emailEnabled && !inAppEnabled) {
      return res.status(400).json({
        success: false,
        message: 'At least one notification channel (email or in-app) must be enabled'
      });
    }

    // If updating scheduled time, validate it's in the future
    if (updateData.scheduledDateTime) {
      const scheduledDate = new Date(updateData.scheduledDateTime);
      if (scheduledDate <= new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Scheduled time must be in the future'
        });
      }
    }

    const updatedNotification = await CustomNotification.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email');

    console.log(`[CUSTOM NOTIFICATION] Updated notification: ${id}`);

    return res.status(200).json({
      success: true,
      data: updatedNotification,
      message: 'Custom notification updated successfully'
    });
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error updating custom notification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update custom notification',
      error: error.message
    });
  }
};

/**
 * Delete a custom notification
 */
const deleteCustomNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await CustomNotification.findById(id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Custom notification not found'
      });
    }

    // Allow deleting any notification for admin cleanup purposes
    // Note: Sent notifications can be deleted for record keeping

    await CustomNotification.findByIdAndDelete(id);

    console.log(`[CUSTOM NOTIFICATION] Deleted notification: ${id}`);

    return res.status(200).json({
      success: true,
      message: 'Custom notification deleted successfully'
    });
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error deleting custom notification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete custom notification',
      error: error.message
    });
  }
};

/**
 * Cancel a scheduled notification
 */
const cancelCustomNotification = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await CustomNotification.findById(id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Custom notification not found'
      });
    }

    if (notification.status !== 'scheduled') {
      return res.status(400).json({
        success: false,
        message: 'Can only cancel scheduled notifications'
      });
    }

    notification.status = 'cancelled';
    await notification.save();

    console.log(`[CUSTOM NOTIFICATION] Cancelled notification: ${id}`);

    return res.status(200).json({
      success: true,
      message: 'Custom notification cancelled successfully'
    });
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error cancelling custom notification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to cancel custom notification',
      error: error.message
    });
  }
};

/**
 * Process due custom notifications
 */
const processDueNotifications = async () => {
  try {
    console.log('[CUSTOM NOTIFICATION] Checking for due notifications...');

    const dueNotifications = await CustomNotification.findDueNotifications();

    if (dueNotifications.length === 0) {
      console.log('[CUSTOM NOTIFICATION] No due notifications found');
      return;
    }

    console.log(`[CUSTOM NOTIFICATION] Found ${dueNotifications.length} due notifications`);

    for (const notification of dueNotifications) {
      try {
        await sendCustomNotification(notification);
      } catch (error) {
        console.error(`[CUSTOM NOTIFICATION] Error sending notification ${notification._id}:`, error);
        await notification.markAsFailed(error.message);
      }
    }
  } catch (error) {
    console.error('[CUSTOM NOTIFICATION] Error processing due notifications:', error);
  }
};

/**
 * Send a custom notification
 */
const sendCustomNotification = async (notification) => {
  console.log(`[CUSTOM NOTIFICATION] Sending notification: ${notification._id}`);

  let sentCount = 0;
  let failedCount = 0;

  // Get target users based on audience
  let users = [];
  if (notification.targetAudience === 'all') {
    users = await User.find({});
  } else if (notification.targetAudience === 'verified') {
    users = await User.find({ isVerified: true });
  } else if (notification.targetAudience === 'unverified') {
    users = await User.find({ isVerified: false });
  }

  console.log(`[CUSTOM NOTIFICATION] Targeting ${users.length} users`);

  // Send email notifications
  if (notification.emailEnabled) {
    for (const user of users) {
      if (user.email) {
        try {
          await sendSimpleEmail({
            to: user.email,
            subject: notification.title,
            html: customNotificationTemplate({
              userName: user.firstName || 'Valued Customer',
              title: notification.title,
              message: notification.message
            })
          });
          sentCount++;
        } catch (error) {
          console.error(`[CUSTOM NOTIFICATION] Failed to send email to ${user.email}:`, error);
          failedCount++;
        }
      }
    }
  }

  // Send in-app notifications if enabled
  if (notification.inAppEnabled) {
    for (const user of users) {
      try {
        if (global.io) {
          const socketService = require('../../services/socketService')(global.io);

          const inAppNotification = {
            type: 'custom_notification',
            title: notification.title,
            message: notification.message,
            link: '/', // You can customize this link as needed
          };

          await socketService.notifyUser(inAppNotification, user._id);
          console.log(`[CUSTOM NOTIFICATION] In-app notification sent to user ${user._id}`);
          sentCount++; // Count in-app notifications as well
        }
      } catch (notificationError) {
        console.error(`[CUSTOM NOTIFICATION] Failed to send in-app notification to ${user._id}:`, notificationError);
        failedCount++;
      }
    }
  }

  // Mark notification as sent
  await notification.markAsSent(sentCount, failedCount);

  console.log(`[CUSTOM NOTIFICATION] Notification ${notification._id} sent to ${sentCount} users, ${failedCount} failed`);
};

module.exports = {
  getCustomNotifications,
  createCustomNotification,
  updateCustomNotification,
  deleteCustomNotification,
  cancelCustomNotification,
  processDueNotifications
};
