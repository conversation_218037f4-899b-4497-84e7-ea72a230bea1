/**
 * VPS Scheduled Task Model
 * Stores scheduled VPS scraping tasks in database for reliable execution
 * Uses the same pattern as CustomNotification for consistency
 */

const mongoose = require('mongoose');

const vpsScheduledTaskSchema = new mongoose.Schema({
  // Task identification
  taskType: {
    type: String,
    enum: ['scraping'],
    default: 'scraping'
  },
  
  // Scheduling information
  scheduledDateTime: {
    type: Date,
    required: true
  },
  
  // Recurrence settings
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurrenceType: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    required: function() { return this.isRecurring; }
  },
  recurrenceTime: {
    type: String, // Format: "HH:MM"
    required: function() { return this.isRecurring; }
  },
  
  // Task status
  status: {
    type: String,
    enum: ['scheduled', 'running', 'completed', 'failed', 'cancelled'],
    default: 'scheduled'
  },
  
  // Execution tracking
  executedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  
  // Results tracking
  executionResult: {
    success: { type: <PERSON>ole<PERSON> },
    scraped: { type: Number, default: 0 },
    updated: { type: Number, default: 0 },
    created: { type: Number, default: 0 },
    errors: { type: Number, default: 0 },
    message: { type: String },
    error: { type: String }
  },
  
  // Metadata
  createdBy: {
    type: String,
    default: 'system'
  },
  isAutomated: {
    type: Boolean,
    default: true
  },
  
  // Next execution for recurring tasks
  nextExecution: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
vpsScheduledTaskSchema.index({ scheduledDateTime: 1, status: 1 });
vpsScheduledTaskSchema.index({ status: 1, createdAt: -1 });
vpsScheduledTaskSchema.index({ isRecurring: 1, nextExecution: 1 });

// Virtual for checking if task is due
vpsScheduledTaskSchema.virtual('isDue').get(function() {
  return this.scheduledDateTime <= new Date() && this.status === 'scheduled';
});

// Method to mark as running
vpsScheduledTaskSchema.methods.markAsRunning = function() {
  this.status = 'running';
  this.executedAt = new Date();
  return this.save();
};

// Method to mark as completed
vpsScheduledTaskSchema.methods.markAsCompleted = function(result) {
  this.status = 'completed';
  this.completedAt = new Date();
  this.executionResult = result;
  
  // Schedule next execution if recurring
  if (this.isRecurring) {
    this.scheduleNextExecution();
  }
  
  return this.save();
};

// Method to mark as failed
vpsScheduledTaskSchema.methods.markAsFailed = function(error) {
  this.status = 'failed';
  this.completedAt = new Date();
  this.executionResult = {
    success: false,
    error: error.message || error,
    scraped: 0,
    updated: 0,
    created: 0,
    errors: 1
  };
  
  // Schedule next execution if recurring (even if failed)
  if (this.isRecurring) {
    this.scheduleNextExecution();
  }
  
  return this.save();
};

// Method to schedule next execution for recurring tasks
vpsScheduledTaskSchema.methods.scheduleNextExecution = function() {
  if (!this.isRecurring) return;
  
  const [hours, minutes] = this.recurrenceTime.split(':');
  const nextExecution = new Date();
  
  // Set the time
  nextExecution.setHours(parseInt(hours), parseInt(minutes), 0, 0);
  
  // Calculate next execution based on recurrence type
  switch (this.recurrenceType) {
    case 'daily':
      // If time has passed today, schedule for tomorrow
      if (nextExecution <= new Date()) {
        nextExecution.setDate(nextExecution.getDate() + 1);
      }
      break;
      
    case 'weekly':
      // Schedule for next Monday
      const daysUntilMonday = (1 + 7 - nextExecution.getDay()) % 7;
      if (daysUntilMonday === 0 && nextExecution <= new Date()) {
        // If it's Monday but time has passed, schedule for next Monday
        nextExecution.setDate(nextExecution.getDate() + 7);
      } else {
        nextExecution.setDate(nextExecution.getDate() + daysUntilMonday);
      }
      break;
      
    case 'monthly':
      // Schedule for same day next month
      nextExecution.setMonth(nextExecution.getMonth() + 1);
      break;
  }
  
  this.nextExecution = nextExecution;
  
  // Create new scheduled task for next execution
  const NextTask = this.constructor;
  NextTask.create({
    taskType: this.taskType,
    scheduledDateTime: nextExecution,
    isRecurring: this.isRecurring,
    recurrenceType: this.recurrenceType,
    recurrenceTime: this.recurrenceTime,
    createdBy: this.createdBy,
    isAutomated: this.isAutomated
  }).catch(err => {
    console.error('[VPS SCHEDULED TASK] Error creating next recurring task:', err);
  });
};

// Static method to find due tasks
vpsScheduledTaskSchema.statics.findDueTasks = function() {
  return this.find({
    scheduledDateTime: { $lte: new Date() },
    status: 'scheduled'
  }).sort({ scheduledDateTime: 1 });
};

// Static method to create a one-time task
vpsScheduledTaskSchema.statics.createOneTimeTask = function(scheduledDateTime, createdBy = 'admin') {
  return this.create({
    taskType: 'scraping',
    scheduledDateTime: scheduledDateTime,
    isRecurring: false,
    createdBy: createdBy,
    isAutomated: false
  });
};

// Static method to create a recurring task
vpsScheduledTaskSchema.statics.createRecurringTask = function(recurrenceType, recurrenceTime, createdBy = 'system') {
  // Calculate first execution
  const [hours, minutes] = recurrenceTime.split(':');
  const firstExecution = new Date();
  firstExecution.setHours(parseInt(hours), parseInt(minutes), 0, 0);
  
  // If time has passed today, schedule for next occurrence
  if (firstExecution <= new Date()) {
    switch (recurrenceType) {
      case 'daily':
        firstExecution.setDate(firstExecution.getDate() + 1);
        break;
      case 'weekly':
        const daysUntilMonday = (1 + 7 - firstExecution.getDay()) % 7 || 7;
        firstExecution.setDate(firstExecution.getDate() + daysUntilMonday);
        break;
      case 'monthly':
        firstExecution.setMonth(firstExecution.getMonth() + 1);
        break;
    }
  }
  
  return this.create({
    taskType: 'scraping',
    scheduledDateTime: firstExecution,
    isRecurring: true,
    recurrenceType: recurrenceType,
    recurrenceTime: recurrenceTime,
    createdBy: createdBy,
    isAutomated: true
  });
};

// Static method to cancel all scheduled tasks
vpsScheduledTaskSchema.statics.cancelAllScheduledTasks = function() {
  return this.updateMany(
    { status: 'scheduled' },
    { status: 'cancelled' }
  );
};

// Static method to get task statistics
vpsScheduledTaskSchema.statics.getTaskStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

module.exports = mongoose.model('VpsScheduledTask', vpsScheduledTaskSchema);
