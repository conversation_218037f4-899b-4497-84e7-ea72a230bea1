const mongoose = require('mongoose');

const interviewSchema = new mongoose.Schema({
  applicationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'JobApplication',
    required: true
  },
  candidateName: {
    type: String,
    required: true
  },
  candidateEmail: {
    type: String,
    required: true
  },
  interviewerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  scheduledAt: {
    type: Date,
    required: true
  },
  duration: {
    type: Number,
    default: 60, // Duration in minutes
    required: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'completed', 'cancelled', 'rescheduled'],
    default: 'scheduled'
  },
  meetLink: {
    type: String,
    default: null
  },
  notes: {
    type: String,
    default: ''
  },
  feedback: {
    type: String,
    default: ''
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for efficient queries
interviewSchema.index({ applicationId: 1 });
interviewSchema.index({ interviewerId: 1 });
interviewSchema.index({ scheduledAt: 1 });
interviewSchema.index({ status: 1 });

// Virtual for interviewer details
interviewSchema.virtual('interviewer', {
  ref: 'User',
  localField: 'interviewerId',
  foreignField: '_id',
  justOne: true
});

// Virtual for application details
interviewSchema.virtual('application', {
  ref: 'JobApplication',
  localField: 'applicationId',
  foreignField: '_id',
  justOne: true
});

// Ensure virtual fields are serialized
interviewSchema.set('toJSON', { virtuals: true });
interviewSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Interview', interviewSchema);
