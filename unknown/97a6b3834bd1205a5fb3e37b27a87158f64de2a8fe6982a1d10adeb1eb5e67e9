const Ticket = require("../models/Ticket");
const User = require("../models/User");
const fs = require("fs");
const path = require("path");
const ticketService = require("../services/ticketService");
const { sendTicketMail } = require("../routes/sendEmail/sendEmail");
const { v4: uuidv4 } = require("uuid");
const xss = require("xss");
const adminLogger = require("../utils/adminLogger");

// Create a new ticket

exports.createTicket = async (req, res) => {
  try {
    // Extract and sanitize form data from req.body
    const ticketData = {
      subject: xss(req.body.subject),
      message: xss(req.body.message),
      service: xss(req.body.service),
      priority: xss(req.body.priority),
    };
    console.log("🚀 ~ exports.createTicket= ~ ticketData:", ticketData);

    if (
      !ticketData.subject ||
      !ticketData.message ||
      !ticketData.service ||
      !ticketData.priority
    ) {
      throw new Error("required field");
    }

    ticketData.creator = req.user._id; // Set the creator ID
    const identifiant = uuidv4();
    ticketData.identifiant = identifiant.split("-")[0];

    // Handle image uploads
    if (req.files && Array.isArray(req.files)) {
      // Check if files were uploaded under the 'newImages' key
      const newImages = req.files.map(
        (file) => `/images/uploads/${file.filename}`
      ); // Save relative file paths
      ticketData.images = newImages; // Store image paths in the 'images' field
    } else {
      ticketData.images = []; // Initialize an empty array if no images are uploaded
    }

    // Create the ticket using the ticketService
    const ticket = await ticketService.createTicket(ticketData);

    // Send an email notification to the ticket creator
    // Use a non-blocking approach for email sending
    setImmediate(async () => {
      try {
        await sendTicketMail("created", ticket);
        console.log('Email notification sent for new ticket');
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
      }
    });

    // Send real-time notification to admin users via Socket.IO
    // Use a non-blocking approach with setImmediate
    if (global.io) {
      console.log(`[SOCKET DEBUG] Preparing to send socket notification for new ticket ID: ${ticket._id}`);
      console.log(`[SOCKET DEBUG] global.io exists:`, !!global.io);

      setImmediate(() => {
        try {
          // Get the socket service methods - use the global instance
          console.log(`[SOCKET DEBUG] Getting socketService instance for new ticket notification`);
          const socketService = require('../services/socketService')(global.io);

          // Send notification about the new ticket
          console.log(`[SOCKET DEBUG] Calling socketService.notifyNewTicket for ticket ID: ${ticket._id}`);
          socketService.notifyNewTicket(ticket);
          console.log(`[SOCKET DEBUG] Socket notification process completed for new ticket ID: ${ticket._id}`);
        } catch (socketError) {
          console.error(`[SOCKET DEBUG] Failed to send socket notification for new ticket:`, socketError);
        }
      });
    } else {
      console.log(`[SOCKET DEBUG] global.io is not available, skipping socket notification for new ticket`);
    }

    // Respond with success message and created ticket
    res
      .status(201)
      .json({ message: "Ticket created successfully", data: ticket });
  } catch (error) {
    console.error("Error creating ticket:", error.message);
    res.status(500).json({ error: "Failed to create ticket" });
  }
};

// Get all tickets
exports.getAllTickets = async (req, res) => {
  try {
    const tickets = await ticketService.getAllTickets();
    res.status(200).json({ tickets });
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch tickets" });
  }
};

// Get a ticket by ID
exports.getTicketById = async (req, res) => {
  try {
    const ticketId = req.params.id;
    const ticket = await ticketService.getTicketById(ticketId);

    if (!ticket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    res.status(200).json({ ticket });
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch ticket" });
  }
};

// Get tickets for the logged-in user
exports.getUserTickets = async (req, res) => {
  try {
    const userId = req.user._id; // Email is extracted from the URL path
    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    const userTickets = await ticketService.getUserTickets(userId);
    res.status(200).json({ userTickets });
  } catch (error) {
    res.status(500).json({ error: "Failed to fetch user tickets" });
  }
};

exports.updateTicket = async (req, res) => {
  try {
    const { id } = req.params;
    let updateData = {
      subject: xss(req.body.subject),
      message: xss(req.body.message),
      service: xss(req.body.service),
      priority: xss(req.body.priority),
      existingImages: req.body.existingImages
        ? xss(req.body.existingImages)
        : undefined,
    };
    console.log("🚀 ~ exports.updateTicket= ~ updateData:", updateData);

    // Find the ticket in the database
    const ticket = await Ticket.findById(id);
    if (!ticket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    // Parse existing images from request body if available
    let existingImages = [];
    if (updateData.existingImages) {
      try {
        existingImages = JSON.parse(updateData.existingImages);
        // Remove the parsed existingImages from updateData to avoid storing the stringified version
        delete updateData.existingImages;
      } catch (error) {
        return res
          .status(400)
          .json({ error: "Invalid existing images format" });
      }
    }

    // Determine which images were removed (if in edit mode)
    const removedImages = ticket.images.filter(
      (img) => !existingImages.includes(img)
    );

    // Delete removed image files from the server
    if (removedImages.length > 0) {
      removedImages.forEach((imagePath) => {
        try {
          // Only remove from filesystem if it's a local path
          if (imagePath.startsWith("/images/uploads/")) {
            const fullPath = path.join(process.cwd(), "public", imagePath);
            if (fs.existsSync(fullPath)) {
              fs.unlinkSync(fullPath);
              console.log(`Deleted removed image: ${imagePath}`);
            }
          }
        } catch (err) {
          console.error(`Failed to remove image ${imagePath}:`, err);
          // Continue execution even if file deletion fails
        }
      });
    }

    // Check if new images were uploaded
    let allImages = [...existingImages]; // Start with existing images that weren't removed

    if (req.files && req.files.newImages && req.files.newImages.length > 0) {
      // Extract filenames of uploaded images from the newImages field
      const newImages = req.files.newImages.map(
        (file) => `/images/uploads/${file.filename}`
      );
      allImages = [...allImages, ...newImages]; // Add new images
    } else if (req.files && Array.isArray(req.files) && req.files.length > 0) {
      // Handle case where files are directly in req.files array (not in a named field)
      const newImages = req.files.map(
        (file) => `/images/uploads/${file.filename}`
      );
      allImages = [...allImages, ...newImages]; // Add new images
    }

    // Update the images array in updateData
    updateData.images = allImages;

    // Update the ticket in the database
    const updatedTicket = await Ticket.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    ).populate("creator", "firstName lastName email");

    // Send real-time notification to admin users via Socket.IO
    if (global.io) {
      console.log(`[SOCKET DEBUG] Preparing to send socket notification for ticket update`);

      setImmediate(async () => {
        try {
          // Get the socket service methods
          console.log(`[SOCKET DEBUG] Getting socketService instance for ticket update notification`);
          const socketService = require('../services/socketService')(global.io);

          // Send notification about the ticket update
          console.log(`[SOCKET DEBUG] Calling socketService.notifyTicketUpdated for ticket ID: ${updatedTicket._id}`);

          // Fetch complete user information
          const userInfo = await User.findById(req.user?._id).select('firstName lastName role');

          console.log(`[SOCKET DEBUG] User info for notification:`, {
            userId: userInfo?._id,
            firstName: userInfo?.firstName,
            lastName: userInfo?.lastName,
            role: userInfo?.role
          });

          socketService.notifyTicketUpdated(updatedTicket, userInfo);
          console.log(`[SOCKET DEBUG] Socket notification process completed for ticket update`);
        } catch (socketError) {
          console.error(`[SOCKET DEBUG] Failed to send socket notification for ticket update:`, socketError);
        }
      });
    } else {
      console.log(`[SOCKET DEBUG] global.io is not available, skipping ticket update notification`);
    }

    return res
      .status(200)
      .json({ message: "Ticket updated successfully", ticket: updatedTicket });
  } catch (error) {
    console.error("Error updating ticket:", error.message);
    return res.status(500).json({ error: "Failed to update ticket" });
  }
};

// Delete a ticket
exports.deleteTicket = async (req, res) => {
  try {
    const ticketId = req.params.id;

    // Get the ticket before marking it as deleted
    const ticket = await Ticket.findById(ticketId).populate("creator", "firstName lastName email");

    if (!ticket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    // Mark the ticket as deleted
    ticket.status = "deleted";
    const deletedTicket = await ticket.save();

    // Send real-time notification to admin users via Socket.IO
    if (global.io) {
      console.log(`[SOCKET DEBUG] Preparing to send socket notification for ticket deletion`);

      setImmediate(async () => {
        try {
          // Get the socket service methods
          console.log(`[SOCKET DEBUG] Getting socketService instance for ticket deletion notification`);
          const socketService = require('../services/socketService')(global.io);

          // Fetch complete user information
          const userInfo = await User.findById(req.user?._id).select('firstName lastName role');

          console.log(`[SOCKET DEBUG] User info for notification:`, {
            userId: userInfo?._id,
            firstName: userInfo?.firstName,
            lastName: userInfo?.lastName,
            role: userInfo?.role
          });

          // Send notification about the ticket deletion
          console.log(`[SOCKET DEBUG] Calling socketService.notifyTicketDeleted for ticket ID: ${deletedTicket._id}`);
          socketService.notifyTicketDeleted(deletedTicket, userInfo);
          console.log(`[SOCKET DEBUG] Socket notification process completed for ticket deletion`);
        } catch (socketError) {
          console.error(`[SOCKET DEBUG] Failed to send socket notification for ticket deletion:`, socketError);
        }
      });
    } else {
      console.log(`[SOCKET DEBUG] global.io is not available, skipping ticket deletion notification`);
    }

    res.status(200).json({ message: "Ticket deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: "Failed to delete ticket" });
  }
};

exports.updateTicketStatus = async (req, res) => {
  const { id } = req.params;
  const { status, resolutionComment, resolvedBy } = req.body;

  // Log the received payload
  console.log("Received payload:", { status, resolutionComment, resolvedBy });

  try {
    // Find the ticket by ID
    const ticket = await Ticket.findById(id)
      .populate("creator", "firstName lastName email favoriteLang")
      .populate("resolvedBy", "firstName lastName");
    if (!ticket) {
      return res.status(404).json({ error: "Ticket not found" });
    }

    // Update the ticket status
    const previousStatus = ticket.status;
    ticket.status = status;
    ticket.updatedAt = new Date();
    ticket.resolutionComment = "";
    ticket.resolvedBy = null;

    // If the status is "resolved", require a resolution comment and resolvedBy
    if (status === "resolved") {
      if (
        !resolutionComment ||
        !resolvedBy ||
        resolutionComment.trim() === ""
      ) {
        return res.status(400).json({
          error:
            "Resolution comment and resolvedBy are required for resolving a ticket",
        });
      }
      ticket.resolutionComment = resolutionComment;
      ticket.resolvedBy = resolvedBy;

      // Send an email notification to the ticket creator
      // Use a non-blocking approach for email sending
      setImmediate(async () => {
        try {
          await sendTicketMail("resolved", ticket);
          console.log('Email notification sent for resolved ticket');
        } catch (emailError) {
          console.error("Failed to send email notification:", emailError);
        }
      });

      // Log admin action for resolving ticket
      await adminLogger.log(
        req.user?._id,
        "RESOLVE_TICKET",
        "TICKET",
        {
          ticketId: id,
          subject: ticket.subject,
          identifiant: ticket.identifiant,
        },
        {
          message: `Ticket #${ticket.identifiant} was resolved`,
          ticketSubject: ticket.subject,
          previousStatus:
            previousStatus.charAt(0).toUpperCase() + previousStatus.slice(1),
          newStatus: status.charAt(0).toUpperCase() + status.slice(1),
          resolutionComment,
          resolvedBy: req.user
            ? `${req.user.firstName} ${req.user.lastName}`
            : "Unknown admin",
          resolvedAt: new Date().toISOString(),
        }
      );
    } else if (status !== previousStatus) {
      // Log status change for other statuses
      const actionType = status === "closed" ? "CLOSE_TICKET" : "UPDATE_TICKET";

      await adminLogger.log(
        req.user?._id,
        actionType,
        "TICKET",
        {
          ticketId: id,
          subject: ticket.subject,
          identifiant: ticket.identifiant,
        },
        {
          message: `Ticket #${ticket.identifiant} status changed from ${previousStatus} to ${status}`,
          ticketSubject: ticket.subject,
          previousStatus:
            previousStatus.charAt(0).toUpperCase() + previousStatus.slice(1),
          newStatus: status.charAt(0).toUpperCase() + status.slice(1),
          changedBy: req.user
            ? `${req.user.firstName} ${req.user.lastName}`
            : "Unknown admin",
          changedAt: new Date().toISOString(),
          actionType: actionType === "CLOSE_TICKET" ? "Closed" : "Updated",
        }
      );
    }

    // Save the updated ticket
    await ticket.save();

    // Log the updated ticket
    console.log("Updated ticket:", ticket);

    // Send real-time notification to admin users via Socket.IO if status changed
    // Use a non-blocking approach with setImmediate
    if (global.io && status !== previousStatus) {
      console.log(`[SOCKET DEBUG] Preparing to send socket notification for ticket status update`);
      console.log(`[SOCKET DEBUG] Status change: ${previousStatus} -> ${status} for ticket ID: ${ticket._id}`);

      setImmediate(async () => {
        try {
          // Get the socket service methods - use the global instance
          console.log(`[SOCKET DEBUG] Getting socketService instance for status update notification`);
          const socketService = require('../services/socketService')(global.io);

          // Fetch complete user information
          const userInfo = await User.findById(req.user?._id).select('firstName lastName role');

          console.log(`[SOCKET DEBUG] User info for notification:`, {
            userId: userInfo?._id,
            firstName: userInfo?.firstName,
            lastName: userInfo?.lastName,
            role: userInfo?.role
          });

          // Pass the admin ID if the user making the change is an admin
          const adminId = userInfo && userInfo.role === 'admin' ? userInfo._id : null;
          console.log(`[SOCKET DEBUG] Admin ID for status update:`, adminId || 'none');

          // Send notification about the ticket status update to admins
          console.log(`[SOCKET DEBUG] Calling socketService.notifyTicketStatusUpdate`);
          socketService.notifyTicketStatusUpdate(ticket, previousStatus, adminId, userInfo);
          console.log(`[SOCKET DEBUG] Socket notification process completed for ticket status update`);

          // Send notification to the ticket creator (user)
          console.log(`[SOCKET DEBUG] Calling socketService.notifyTicketStatusUpdateToUser`);
          socketService.notifyTicketStatusUpdateToUser(ticket, previousStatus, userInfo);
          console.log(`[SOCKET DEBUG] User notification process completed for ticket status update`);
        } catch (socketError) {
          console.error(`[SOCKET DEBUG] Failed to send socket notification for status update:`, socketError);
        }
      });
    } else {
      if (!global.io) {
        console.log(`[SOCKET DEBUG] global.io is not available, skipping status update notification`);
      }
      if (status === previousStatus) {
        console.log(`[SOCKET DEBUG] Status unchanged (${status}), skipping notification`);
      }
    }

    res.json(ticket);
  } catch (err) {
    console.error("Failed to update ticket status:", err);
    res.status(500).json({ error: "Failed to update ticket status" });
  }
};