const Job = require('../../models/Job');
const JobApplication = require('../../models/JobApplication');
const Interview = require('../../models/Interview');
const fs = require('fs');
const path = require('path');

// Placeholder function to create a new job
const createJob = async (req, res) => {
  try {

    const newJob = new Job(req.body);
    const savedJob = await newJob.save();
    res.status(201).json(savedJob);
  } catch (error) {
    res.status(500).json({ message: 'Error creating job', error: error.message });
  }
};

// Placeholder function to get all jobs
const getAllJobs = async (req, res) => {
  try {
    const jobs = await Job.find();
    res.status(200).json(jobs);
  } catch (error) {
    res.status(500).json({ message: 'Error getting all jobs', error: error.message });
  }
};

// Placeholder function to get a single job by ID
const getJobById = async (req, res) => {
  try {
    const { id } = req.params;
    const job = await Job.findById(id);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }
    res.status(200).json(job);
  } catch (error) {
    res.status(500).json({ message: 'Error getting job by ID', error: error.message });
  }
};

// Function to handle job applications with file uploads
const applyForJob = async (req, res) => {
  try {
    const { id } = req.params;
    const { fullName, email, phone } = req.body;

    // Validate required fields
    if (!fullName || !email) {
      return res.status(400).json({ message: 'Name and email are required' });
    }

    // Check if job exists
    const job = await Job.findById(id);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }

    // Check if resume was uploaded
    if (!req.files || !req.files.resume) {
      return res.status(400).json({ message: 'Resume is required' });
    }

    // Create application data
    const applicationData = {
      job: id,
      name: fullName,
      email: email,
      phone: phone || '',
      resumePath: `/uploads/job-applications/${req.files.resume[0].filename}`
    };

    // Add userId if user is authenticated
    if (req.user && req.user.id) {
      applicationData.userId = req.user.id;
    }

    // Add cover letter if provided
    if (req.files.coverLetter) {
      applicationData.coverLetterPath = `/uploads/job-applications/${req.files.coverLetter[0].filename}`;
    }

    // Create and save the job application
    const application = new JobApplication(applicationData);
    await application.save();

    // Add application to job's applications array
    job.applications.push(application._id);
    await job.save();

    res.status(201).json({
      message: 'Application submitted successfully',
      application: {
        id: application._id,
        name: application.name,
        email: application.email,
        appliedAt: application.appliedAt
      }
    });
  } catch (error) {
    console.error('Error submitting job application:', error);
    res.status(500).json({ message: 'Error submitting application', error: error.message });
  }
};

// Placeholder function to update a job by ID
const updateJob = async (req, res) => {
  try {
    const { jobId } = req.params;
    const updatedJob = await Job.findByIdAndUpdate(jobId, req.body, { new: true });
    if (!updatedJob) {
      return res.status(404).json({ message: 'Job not found' });
    }
    res.status(200).json(updatedJob);
  } catch (error) {
    res.status(500).json({ message: 'Error updating job', error: error.message });
  }
};

// Placeholder function to delete a job by ID
const deleteJob = async (req, res) => {
  try {
    const { jobId } = req.params;
    const deletedJob = await Job.findByIdAndDelete(jobId);
    if (!deletedJob) {
      return res.status(404).json({ message: 'Job not found' });
    }
    res.status(200).json({ message: 'Job deleted successfully', deletedJob });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting job', error: error.message });
  }
};

// Function to get applications for a specific job
const getJobApplications = async (req, res) => {
  try {
    const { jobId } = req.params;

    // Check if job exists
    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }

    // Populate applications from the job
    const populatedJob = await Job.findById(jobId).populate({
      path: 'applications',
      model: 'JobApplication',
      options: { sort: { appliedAt: -1 } } // Sort by most recent first
    });

    res.status(200).json(populatedJob.applications || []);
  } catch (error) {
    console.error('Error fetching job applications:', error);
    res.status(500).json({ message: 'Error fetching job applications', error: error.message });
  }
};

// Function to get all job applications
const getAllJobApplications = async (req, res) => {
  try {
    const applications = await JobApplication.find()
      .populate('job', 'title department')
      .sort({ appliedAt: -1 });

    res.status(200).json(applications);
  } catch (error) {
    console.error('Error fetching all job applications:', error);
    res.status(500).json({ message: 'Error fetching all job applications', error: error.message });
  }
};

// Function to update application status
const updateApplicationStatus = async (req, res) => {
  try {
    const { applicationId } = req.params;
    const { status } = req.body;

    if (!status || !['pending', 'reviewed', 'interviewed', 'accepted', 'rejected'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status value' });
    }

    const application = await JobApplication.findByIdAndUpdate(
      applicationId,
      { status },
      { new: true }
    );

    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }

    res.status(200).json(application);
  } catch (error) {
    console.error('Error updating application status:', error);
    res.status(500).json({ message: 'Error updating application status', error: error.message });
  }
};

// Function to delete an application
const deleteApplication = async (req, res) => {
  try {
    const { applicationId } = req.params;

    const application = await JobApplication.findById(applicationId);
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }

    // Remove application from job's applications array
    await Job.findByIdAndUpdate(
      application.job,
      { $pull: { applications: applicationId } }
    );

    // Delete the application
    await JobApplication.findByIdAndDelete(applicationId);

    // Optionally delete the files
    if (application.resumePath) {
      const resumeFilePath = path.join(__dirname, '..', '..', 'public', application.resumePath);
      if (fs.existsSync(resumeFilePath)) {
        fs.unlinkSync(resumeFilePath);
      }
    }

    if (application.coverLetterPath) {
      const coverLetterFilePath = path.join(__dirname, '..', '..', 'public', application.coverLetterPath);
      if (fs.existsSync(coverLetterFilePath)) {
        fs.unlinkSync(coverLetterFilePath);
      }
    }

    res.status(200).json({ message: 'Application deleted successfully' });
  } catch (error) {
    console.error('Error deleting application:', error);
    res.status(500).json({ message: 'Error deleting application', error: error.message });
  }
};

// Function to get user's application for a specific job
const getUserApplication = async (req, res) => {
  try {
    const { id: jobId } = req.params;
    const userId = req.user.id; // From authentication middleware

    // Check if job exists
    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }

    // Find user's application for this job
    const application = await JobApplication.findOne({
      job: jobId,
      userId: userId
    }).populate('job', 'title department');

    if (!application) {
      return res.status(404).json({ message: 'No application found for this job' });
    }

    // Get interviews for this application
    const interviews = await Interview.find({
      applicationId: application._id
    }).populate('interviewerId', 'name email').sort({ scheduledAt: 1 });

    // Return application with interviews
    const applicationData = {
      ...application.toObject(),
      interviews: interviews
    };

    res.status(200).json(applicationData);
  } catch (error) {
    console.error('Error fetching user application:', error);
    res.status(500).json({ message: 'Error fetching application', error: error.message });
  }
};

// Function to get user's interviews for a specific job application
const getUserApplicationInterviews = async (req, res) => {
  try {
    const { id: jobId } = req.params;
    const userId = req.user.id; // From authentication middleware

    // Find user's application for this job
    const application = await JobApplication.findOne({
      job: jobId,
      userId: userId
    });

    if (!application) {
      return res.status(404).json({ message: 'No application found for this job' });
    }

    // Get interviews for this application
    const interviews = await Interview.find({
      applicationId: application._id
    }).populate('interviewerId', 'name email').sort({ scheduledAt: 1 });

    res.status(200).json(interviews);
  } catch (error) {
    console.error('Error fetching user interviews:', error);
    res.status(500).json({ message: 'Error fetching interviews', error: error.message });
  }
};

module.exports = {
  createJob,
  getAllJobs,
  getJobById,
  updateJob,
  deleteJob,
  applyForJob,
  getJobApplications,
  getAllJobApplications,
  updateApplicationStatus,
  deleteApplication,
  getUserApplication,
  getUserApplicationInterviews
};