'use client';

import React, { useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import jobService from '@/app/services/jobService';
import { ChevronUp, ChevronDown, Search } from 'lucide-react';


function JobsPage() {
  const t = useTranslations('jobs');
  const locale = useLocale();
  const [jobs, setJobs] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);
  const [showMobileFilters, setShowMobileFilters] = React.useState(false);
  // Search state for job title
  const [searchTitle, setSearchTitle] = React.useState("");

  // Filter states (now arrays for checkboxes)
  const [department, setDepartment] = React.useState([]);
  const [experiences, setExperiences] = React.useState([]);
  const [educations, setEducations] = React.useState([]);

  useEffect(() => {
    fetchJobs();
  }, []);



  const fetchJobs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await jobService.getAllJobs();
      console.log(response.data); // Log the response
      setJobs(response.data);
    } catch (err) {
      console.error("Error fetching jobs:", err);
      setError('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  // Filtering logic
  const filteredJobs = jobs.filter((job) => {
    // Get the job title in the current locale or fallback to English
    const jobTitle = typeof job.title === 'object' ? (job.title[locale] || job.title.en || '') : job.title || '';

    // Get the job requirements in the current locale or fallback to English
    const jobRequirements = job.requirements ? job.requirements.map(req => {
      if (typeof req === 'object') {
        return req[locale] || req.en || '';
      }
      return req || '';
    }) : [];

    // Title search
    const titleMatch = searchTitle.trim() === "" || jobTitle.toLowerCase().includes(searchTitle.trim().toLowerCase());
    // Department
    const departmentMatch = department.length === 0 || (job.department && department.includes(job.department));
    // Experience
    const experienceMatch = experiences.length === 0 || experiences.some(exp =>
      jobRequirements.some(req => req.toLowerCase().includes(exp.toLowerCase()))
    );
    // Education
    const educationMatch = educations.length === 0 || educations.some(edu =>
      jobRequirements.some(req => req.toLowerCase().includes(edu.toLowerCase()))
    );

    return titleMatch && departmentMatch && experienceMatch && educationMatch;
  });

  // Checkbox options - using keys for values and translating when displaying
  const departmentOptions = ['HR', 'Marketing', 'Finance', 'Sales', 'Accounting'];
  const experienceOptions = ['Junior', 'Mid', 'Senior'];
  const educationOptions = ['bac+2', 'bac+3', 'bac+5'];

  // Checkbox handlers
  const handleCheckbox = (value, state, setState) => {
    setState(state.includes(value) ? state.filter(v => v !== value) : [...state, value]);
  };

  // Collapsible state for each filter section
  const [openSections, setOpenSections] = React.useState({
    department: true,
    experience: true,
    education: true,
  });
  const toggleSection = (section) => {
    setOpenSections((prev) => ({ ...prev, [section]: !prev[section] }));
  };


  return (
    <div className="container mx-auto px-2 sm:px-4 py-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 gap-4">
        <h1 className="text-3xl font-bold text-gray-800">{t('job_listings_title')}</h1>
        <div className="w-full sm:w-80 flex-shrink-0">
          <div className="relative">
            <input
              type="text"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 pl-10"
              placeholder={t('search_placeholder')}
              value={searchTitle}
              onChange={e => setSearchTitle(e.target.value)}
            />
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
              <Search size={18} />
            </span>
          </div>
        </div>
      </div>
      {/* Mobile filter button */}
      <div className="md:hidden flex justify-end mb-4">
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded-md shadow flex items-center gap-2"
          onClick={() => setShowMobileFilters(true)}
        >
          <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-.293.707l-6.414 6.414A1 1 0 0013 13.414V19a1 1 0 01-1.447.894l-4-2A1 1 0 017 17v-3.586a1 1 0 00-.293-.707L3.293 6.707A1 1 0 013 6V4z"/></svg>
          {t('filters')}
        </button>
      </div>
      {/* Overlay for mobile filters */}
      {showMobileFilters && (
        <div className="fixed inset-0 z-40 flex">
          <div className="fixed inset-0 bg-black bg-opacity-40" onClick={() => setShowMobileFilters(false)}></div>
          <aside className="relative pt-24 bg-white w-4/5 max-w-xs h-full p-4 shadow-lg z-50 animate-slide-in-left overflow-y-auto max-h-screen">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-800">{t('filters')}</h2>
              <button className="text-gray-500 hover:text-gray-700" onClick={() => setShowMobileFilters(false)}>
                <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12"/></svg>
              </button>
            </div>
            {/* Department */}
            <div className="mb-6">
              <button type="button" className="flex items-center justify-between w-full mb-2" onClick={() => toggleSection('department')}>
                <h2 className="font-semibold text-gray-800">{t('department')}</h2>
                {openSections.department ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
              </button>
              {openSections.department && departmentOptions.map(option => (
                <label key={option} className="flex items-center mb-1 text-sm">
                  <input type="checkbox" className="mr-2" checked={department.includes(option)} onChange={() => handleCheckbox(option, department, setDepartment)} />
                  {t(option)}
                </label>
              ))}
            </div>
            {/* Experience */}
            <div className="mb-6">
              <button type="button" className="flex items-center justify-between w-full mb-2" onClick={() => toggleSection('experience')}>
                <h2 className="font-semibold text-gray-800">{t('experience')}</h2>
                {openSections.experience ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
              </button>
              {openSections.experience && experienceOptions.map(option => (
                <label key={option} className="flex items-center mb-1 text-sm">
                  <input type="checkbox" className="mr-2" checked={experiences.includes(option)} onChange={() => handleCheckbox(option, experiences, setExperiences)} />
                  {t(option)}
                </label>
              ))}
            </div>
            {/* Education */}
            <div className="mb-2">
              <button type="button" className="flex items-center justify-between w-full mb-2" onClick={() => toggleSection('education')}>
                <h2 className="font-semibold text-gray-800">{t('education')}</h2>
                {openSections.education ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
              </button>
              {openSections.education && educationOptions.map(option => (
                <label key={option} className="flex items-center mb-1 text-sm">
                  <input type="checkbox" className="mr-2" checked={educations.includes(option)} onChange={() => handleCheckbox(option, educations, setEducations)} />
                  {t(option)}
                </label>
              ))}
            </div>
          </aside>
        </div>
      )}
      <div className="flex flex-col md:grid md:grid-cols-4 gap-8">
        {/* Sidebar Filters (desktop only) */}
        <aside className="hidden md:block w-full md:col-span-1 bg-white p-4 rounded-lg shadow h-fit mb-8 md:mb-0 md:sticky md:top-8 z-10">
          {/* Department */}
          <div className="mb-6">
            <button type="button" className="flex items-center justify-between w-full mb-2" onClick={() => toggleSection('department')}>
              <h2 className="font-semibold text-gray-800">{t('department')}</h2>
              {openSections.department ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
            </button>
            {openSections.department && departmentOptions.map(option => (
              <label key={option} className="flex items-center mb-1 text-sm">
                <input type="checkbox" className="mr-2" checked={department.includes(option)} onChange={() => handleCheckbox(option, department, setDepartment)} />
                {t(option)}
              </label>
            ))}
          </div>
          {/* Experience */}
          <div className="mb-6">
            <button type="button" className="flex items-center justify-between w-full mb-2" onClick={() => toggleSection('experience')}>
              <h2 className="font-semibold text-gray-800">{t('experience')}</h2>
              {openSections.experience ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
            </button>
            {openSections.experience && experienceOptions.map(option => (
              <label key={option} className="flex items-center mb-1 text-sm">
                <input type="checkbox" className="mr-2" checked={experiences.includes(option)} onChange={() => handleCheckbox(option, experiences, setExperiences)} />
                {t(option)}
              </label>
            ))}
          </div>
          {/* Education */}
          <div className="mb-2">
            <button type="button" className="flex items-center justify-between w-full mb-2" onClick={() => toggleSection('education')}>
              <h2 className="font-semibold text-gray-800">{t('education')}</h2>
              {openSections.education ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
            </button>
            {openSections.education && educationOptions.map(option => (
              <label key={option} className="flex items-center mb-1 text-sm">
                <input type="checkbox" className="mr-2" checked={educations.includes(option)} onChange={() => handleCheckbox(option, educations, setEducations)} />
                {option}
              </label>
            ))}
          </div>
        </aside>
        {/* Job Cards */}
        <div className="w-full md:col-span-3 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredJobs.length === 0 && !loading && (
            <div className="col-span-full text-center text-gray-500">{t('no_jobs_found')}</div>
          )}
          {filteredJobs.map((job) => {
            // Get localized content with fallback to English
            const jobTitle = typeof job.title === 'object' ? (job.title[locale] || job.title.en || '') : job.title || '';
            const jobDescription = typeof job.description === 'object' ? (job.description[locale] || job.description.en || '') : job.description || '';

            // Get localized requirements with fallback to English
            const localizedRequirements = job.requirements ? job.requirements.map(req => {
              if (typeof req === 'object') {
                return req[locale] || req.en || '';
              }
              return req || '';
            }) : [];

            return (
              <div key={job._id} className="bg-white h-96 p-6 rounded-lg shadow-md border border-gray-200 flex flex-col justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">{jobTitle}</h2>
                  <div className="text-sm text-gray-600 mb-4">
                    <span>{t(job.department)}</span>{' '}
                    <span>• {job.location}</span>{' '}
                    <span>• {t(job.type)}</span>
                  </div>
                  <p className="text-gray-700 mb-4 line-clamp-3">{jobDescription}</p>
                  {/* Requirements and Responsibilities - simplified for card view */}
                  <div className="mb-4">
                    <h3 className="text-md font-medium text-gray-800 mb-1">{t('requirements')}</h3>
                    <ul className="list-disc list-inside text-gray-700 text-sm">
                      {localizedRequirements.slice(0, 2).map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                      {localizedRequirements.length > 2 && <li className="text-gray-500">...</li>}
                    </ul>
                  </div>
                  {job.salary_range && (
                    <div className="text-gray-700 text-sm mb-4">
                      <span className="font-medium">{t('salary_range')}:</span> {job.salary_range.min} - {job.salary_range.max} {job.salary_range.currency}
                    </div>
                  )}
                </div>
                {/* View Details Button */}
                <div className="mt-4">
                  <a href={`/${locale}/jobs/${job._id}`} className="block">
                    <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200">
                      {t('view_details')}
                    </button>
                  </a>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

export default JobsPage;