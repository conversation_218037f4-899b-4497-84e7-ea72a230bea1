const Interview = require('../models/Interview');
const JobApplication = require('../models/JobApplication');
const User = require('../models/User');
const { google } = require('googleapis');

// Google Calendar setup (you'll need to configure OAuth2)
const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CALENDAR_CLIENT_ID,
  process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
  process.env.GOOGLE_CALENDAR_REDIRECT_URI
);

// Set credentials if available
if (process.env.GOOGLE_CALENDAR_REFRESH_TOKEN) {
  oauth2Client.setCredentials({
    refresh_token: process.env.GOOGLE_CALENDAR_REFRESH_TOKEN
  });
}

const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

// Generate Google Meet link
const generateGoogleMeetLink = async (interviewData) => {
  try {
    // Check if Google Calendar is properly configured
    if (!process.env.GOOGLE_CALENDAR_CLIENT_ID || !process.env.GOOGLE_CALENDAR_CLIENT_SECRET || !process.env.GOOGLE_CALENDAR_REFRESH_TOKEN) {
      console.warn('Google Calendar not configured. Skipping Meet link generation.');
      console.warn('Environment variables status:');
      console.warn('- GOOGLE_CALENDAR_CLIENT_ID:', !!process.env.GOOGLE_CALENDAR_CLIENT_ID);
      console.warn('- GOOGLE_CALENDAR_CLIENT_SECRET:', !!process.env.GOOGLE_CALENDAR_CLIENT_SECRET);
      console.warn('- GOOGLE_CALENDAR_REFRESH_TOKEN:', !!process.env.GOOGLE_CALENDAR_REFRESH_TOKEN);
      return null;
    }

    // Ensure the date is properly formatted
    const startDate = new Date(interviewData.scheduledAt);
    const endDate = new Date(startDate.getTime() + (interviewData.duration || 60) * 60000);

    console.log('Creating Google Calendar event:', {
      candidateName: interviewData.candidateName,
      scheduledAt: startDate.toISOString(),
      duration: interviewData.duration
    });

    const event = {
      summary: `Interview with ${interviewData.candidateName}`,
      description: `Interview scheduled for job application.\n\nCandidate: ${interviewData.candidateName}\nEmail: ${interviewData.candidateEmail}\n\nNotes: ${interviewData.notes || 'No additional notes'}`,
      start: {
        dateTime: startDate.toISOString(),
        timeZone: 'UTC',
      },
      end: {
        dateTime: endDate.toISOString(),
        timeZone: 'UTC',
      },
      attendees: [
        { email: interviewData.candidateEmail },
      ],
      conferenceData: {
        createRequest: {
          requestId: `interview-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          }
        }
      }
    };

    console.log('Inserting calendar event...');
    const response = await calendar.events.insert({
      calendarId: 'primary',
      resource: event,
      conferenceDataVersion: 1,
      sendUpdates: 'all' // Send email invitations
    });

    console.log('Calendar event created:', response.data.id);

    const meetLink = response.data.conferenceData?.entryPoints?.[0]?.uri ||
                     response.data.hangoutLink ||
                     null;

    if (meetLink) {
      console.log('Google Meet link generated:', meetLink);
    } else {
      console.warn('No Meet link found in response:', JSON.stringify(response.data.conferenceData, null, 2));
    }

    return meetLink;
  } catch (error) {
    console.error('Error creating Google Meet link:', error.message);
    console.error('Error details:', error.response?.data || error);

    // Check for specific authentication errors
    if (error.code === 401 || error.message.includes('invalid_grant')) {
      console.error('Google Calendar authentication failed. Please check your credentials and refresh token.');
    }

    return null;
  }
};

// Create a new interview
exports.createInterview = async (req, res) => {
  try {
    const {
      applicationId,
      candidateName,
      candidateEmail,
      interviewerId,
      scheduledAt,
      duration,
      notes
    } = req.body;

    // Validate required fields
    if (!applicationId || !candidateName || !candidateEmail || !interviewerId || !scheduledAt) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Check if application exists
    const application = await JobApplication.findById(applicationId);
    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Job application not found'
      });
    }

    // Check if interviewer exists
    const interviewer = await User.findById(interviewerId);
    if (!interviewer) {
      return res.status(404).json({
        success: false,
        message: 'Interviewer not found'
      });
    }

    // Check if interview date is in the future
    if (new Date(scheduledAt) <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Interview date must be in the future'
      });
    }

    // Create interview
    const interview = new Interview({
      applicationId,
      candidateName,
      candidateEmail,
      interviewerId,
      scheduledAt,
      duration: duration || 60,
      notes: notes || '',
      status: 'scheduled'
    });

    // Generate Google Meet link
    console.log('Attempting to generate Google Meet link for interview...');
    let meetLink = null;

    try {
      meetLink = await generateGoogleMeetLink({
        candidateName,
        candidateEmail,
        scheduledAt,
        duration: duration || 60,
        notes
      });

      if (meetLink) {
        console.log('✅ Google Meet link generated successfully:', meetLink);
        interview.meetLink = meetLink;
      } else {
        console.warn('⚠️ Google Meet link generation returned null');
      }
    } catch (meetError) {
      console.error('❌ Failed to generate Google Meet link:', meetError.message);
      console.error('Meet link error details:', meetError);
      // Continue without Meet link - don't fail the entire interview creation
    }

    console.log('Saving interview to database...');
    await interview.save();
    console.log('✅ Interview saved successfully with ID:', interview._id);

    // Populate interviewer details
    await interview.populate('interviewer', 'firstName lastName email');

    const responseMessage = meetLink
      ? 'Interview scheduled successfully with Google Meet link'
      : 'Interview scheduled successfully (Google Meet link could not be generated)';

    res.status(201).json({
      success: true,
      message: responseMessage,
      data: interview,
      meetLink: meetLink,
      meetLinkGenerated: !!meetLink
    });
  } catch (error) {
    console.error('Error creating interview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create interview',
      error: error.message
    });
  }
};

// Get all interviews
exports.getInterviews = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      interviewerId,
      startDate,
      endDate
    } = req.query;

    const filter = {};

    if (status) filter.status = status;
    if (interviewerId) filter.interviewerId = interviewerId;
    if (startDate || endDate) {
      filter.scheduledAt = {};
      if (startDate) filter.scheduledAt.$gte = new Date(startDate);
      if (endDate) filter.scheduledAt.$lte = new Date(endDate);
    }

    const interviews = await Interview.find(filter)
      .populate('interviewer', 'firstName lastName email')
      .populate('application', 'name email phone jobId')
      .sort({ scheduledAt: 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Interview.countDocuments(filter);

    res.json({
      success: true,
      data: interviews,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching interviews:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch interviews',
      error: error.message
    });
  }
};

// Get interview by ID
exports.getInterviewById = async (req, res) => {
  try {
    const { id } = req.params;

    const interview = await Interview.findById(id)
      .populate('interviewer', 'firstName lastName email')
      .populate('application', 'name email phone jobId');

    if (!interview) {
      return res.status(404).json({
        success: false,
        message: 'Interview not found'
      });
    }

    res.json({
      success: true,
      data: interview
    });
  } catch (error) {
    console.error('Error fetching interview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch interview',
      error: error.message
    });
  }
};

// Update interview
exports.updateInterview = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const interview = await Interview.findById(id);
    if (!interview) {
      return res.status(404).json({
        success: false,
        message: 'Interview not found'
      });
    }

    // Update interview
    Object.assign(interview, updateData);
    interview.updatedAt = new Date();

    await interview.save();
    await interview.populate('interviewer', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Interview updated successfully',
      data: interview
    });
  } catch (error) {
    console.error('Error updating interview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update interview',
      error: error.message
    });
  }
};

// Delete interview
exports.deleteInterview = async (req, res) => {
  try {
    const { id } = req.params;

    const interview = await Interview.findById(id);
    if (!interview) {
      return res.status(404).json({
        success: false,
        message: 'Interview not found'
      });
    }

    await Interview.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Interview deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting interview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete interview',
      error: error.message
    });
  }
};

// Get interviews for a specific application
exports.getApplicationInterviews = async (req, res) => {
  try {
    const { applicationId } = req.params;

    const interviews = await Interview.find({ applicationId })
      .populate('interviewer', 'firstName lastName email')
      .sort({ scheduledAt: 1 });

    res.json({
      success: true,
      data: interviews
    });
  } catch (error) {
    console.error('Error fetching application interviews:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch application interviews',
      error: error.message
    });
  }
};
