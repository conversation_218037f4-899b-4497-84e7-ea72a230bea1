/**
 * <PERSON><PERSON>t to seed VPS regions from Contabo API
 * This will populate the database with real Contabo regions
 */

const mongoose = require('mongoose');
const VPSRegion = require('./models/VPSRegion');
const Package = require('./models/Package');
const VPSProviderFactory = require('./services/providers/VPSProviderFactory');
require('dotenv').config();

async function seedRegions() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zn_ztech');
    console.log('✅ Connected to MongoDB');

    // Get a sample VPS package for pricing
    console.log('📦 Finding VPS packages...');
    const vpsPackages = await Package.find({
      'vpsConfig.provider': 'contabo'
    }).limit(3);
    
    if (vpsPackages.length === 0) {
      console.log('❌ No VPS packages found. Please create VPS packages first.');
      return;
    }
    
    console.log(`Found ${vpsPackages.length} VPS packages:`, vpsPackages.map(p => p.name));

    // Get regions from Contabo API
    console.log('🌐 Fetching regions from Contabo API...');
    const contaboProvider = VPSProviderFactory.createProvider('contabo');
    const contaboRegions = await contaboProvider.getRegions();
    
    console.log(`Found ${contaboRegions.length} Contabo regions`);

    // Create a fake admin user ID for the seeding
    const adminUserId = new mongoose.Types.ObjectId();

    // Sample regions to create (using real Contabo data)
    const regionsToCreate = [
      {
        regionSlug: 'EU',
        regionName: 'Europe (Germany)',
        country: 'Germany',
        countryCode: 'DE',
        continent: 'Europe',
        city: 'Nuremberg',
        flag: '🇩🇪',
        isPopular: true,
        displayOrder: 1
      },
      {
        regionSlug: 'US-central',
        regionName: 'United States (Central)',
        country: 'United States',
        countryCode: 'US',
        continent: 'North America',
        city: 'St. Louis',
        flag: '🇺🇸',
        isPopular: true,
        displayOrder: 2
      },
      {
        regionSlug: 'SIN',
        regionName: 'Asia (Singapore)',
        country: 'Singapore',
        countryCode: 'SG',
        continent: 'Asia',
        city: 'Singapore',
        flag: '🇸🇬',
        isPopular: false,
        displayOrder: 3
      },
      {
        regionSlug: 'AUS',
        regionName: 'Australia (Sydney)',
        country: 'Australia',
        countryCode: 'AU',
        continent: 'Oceania',
        city: 'Sydney',
        flag: '🇦🇺',
        isPopular: false,
        displayOrder: 4
      }
    ];

    let created = 0;
    let updated = 0;

    for (const regionData of regionsToCreate) {
      try {
        // Check if region already exists
        let existingRegion = await VPSRegion.findOne({ regionId: regionData.regionSlug.toLowerCase() });

        if (existingRegion) {
          console.log(`⚠️ Region ${regionData.regionName} already exists, updating...`);
          
          // Update existing region
          existingRegion.name = regionData.regionName;
          existingRegion.country = regionData.country;
          existingRegion.countryCode = regionData.countryCode;
          existingRegion.continent = regionData.continent;
          existingRegion.city = regionData.city;
          existingRegion.flag = regionData.flag;
          existingRegion.isPopular = regionData.isPopular;
          existingRegion.displayOrder = regionData.displayOrder;
          existingRegion.updatedBy = adminUserId;
          
          await existingRegion.save();
          updated++;
        } else {
          // Create pricing for all VPS packages
          const pricing = vpsPackages.map((pkg, index) => ({
            packageId: pkg._id,
            packageName: pkg.name,
            additionalPrice: index * 5 // Different pricing for different packages
          }));

          // Create new region
          const newRegion = new VPSRegion({
            regionId: regionData.regionSlug.toLowerCase(),
            name: regionData.regionName,
            country: regionData.country,
            countryCode: regionData.countryCode,
            continent: regionData.continent,
            city: regionData.city,
            flag: regionData.flag,
            description: `${regionData.city}, ${regionData.country}`,
            status: 'active',
            isPopular: regionData.isPopular,
            displayOrder: regionData.displayOrder,
            pricing: pricing,
            datacenterProvider: 'contabo',
            networkSpeed: '1 Gbps',
            uptime: '99.9%',
            features: ['DDoS Protection', 'IPv6 Support', 'Private Networking'],
            availableServices: ['vps'],
            createdBy: adminUserId
          });

          await newRegion.save();
          console.log(`✅ Created region: ${regionData.regionName}`);
          created++;
        }
      } catch (error) {
        console.error(`❌ Error processing region ${regionData.regionName}:`, error.message);
      }
    }

    console.log('\n🎉 Region seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Created: ${created} regions`);
    console.log(`   - Updated: ${updated} regions`);
    console.log(`   - Total VPS packages: ${vpsPackages.length}`);

    // Verify the results
    const allRegions = await VPSRegion.find({ status: 'active' }).sort({ displayOrder: 1 });
    console.log(`\n📋 Active regions in database: ${allRegions.length}`);
    allRegions.forEach(region => {
      console.log(`   - ${region.name} (${region.regionId}) - ${region.pricing.length} pricing entries`);
    });

  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the seeding
if (require.main === module) {
  seedRegions();
}

module.exports = { seedRegions };
