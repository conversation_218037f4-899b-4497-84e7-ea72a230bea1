/**
 * Test script for Pricing Update Service
 * Tests the integration of scraped Contabo data with the database
 */

const mongoose = require('mongoose');
const GeminiParsingService = require('./services/geminiParsingService');
const PricingUpdateService = require('./services/pricingUpdateService');

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zn_ztech');
    console.log('📊 Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
}

async function testPricingUpdate() {
  console.log('🧪 Testing Complete Scraping → Database Pipeline...\n');

  try {
    // Connect to database
    await connectDB();

    // Step 1: Scrape latest Contabo pricing
    console.log('🌐 Step 1: Scraping Contabo VPS pricing...');
    const geminiService = new GeminiParsingService();
    const scrapingResult = await geminiService.scrapeContaboPricing();

    if (!scrapingResult.success) {
      throw new Error('Failed to scrape pricing data');
    }

    // Parse JSON response from Gemini
    let scrapedPackages;
    try {
      // Extract JSON from the response (remove markdown formatting)
      const jsonMatch = scrapingResult.response.match(/```json\s*([\s\S]*?)\s*```/);
      const jsonString = jsonMatch ? jsonMatch[1] : scrapingResult.response;
      const parsedData = JSON.parse(jsonString);
      scrapedPackages = parsedData.packages;
    } catch (parseError) {
      console.error('❌ Failed to parse JSON response:', parseError.message);
      console.log('Raw response:', scrapingResult.response);
      return;
    }

    console.log(`✅ Scraped ${scrapedPackages.length} VPS packages\n`);

    // Step 2: Update database with scraped data
    console.log('💾 Step 2: Updating database with scraped packages...');
    const pricingService = new PricingUpdateService();
    const updateResult = await pricingService.updateContaboPackages(scrapedPackages);

    // Step 3: Display results
    console.log('\n📊 UPDATE RESULTS:');
    console.log('─'.repeat(60));
    console.log(`✅ Packages Created: ${updateResult.created}`);
    console.log(`🔄 Packages Updated: ${updateResult.updated}`);
    console.log(`❌ Errors: ${updateResult.errors.length}`);
    console.log('');

    if (updateResult.errors.length > 0) {
      console.log('❌ Errors encountered:');
      updateResult.errors.forEach(error => {
        console.log(`   - ${error.package}: ${error.error}`);
      });
      console.log('');
    }

    // Step 4: Show package details with discount calculations
    console.log('📦 PACKAGE DETAILS WITH DISCOUNT STRUCTURE:');
    console.log('─'.repeat(80));

    updateResult.packages.forEach((result, index) => {
      const pkg = result.package;
      const change = result.priceChange;

      console.log(`${index + 1}. ${pkg.name} (${pkg.vpsConfig?.providerProductId})`);
      console.log(`   Action: ${result.action.toUpperCase()}`);
      console.log(`   Base Price: $${change.new}/month`);

      if (result.action === 'updated') {
        const changeSymbol = change.difference >= 0 ? '↗️' : '↘️';
        console.log(`   Price Change: $${change.old} → $${change.new} (${change.percentageChange}%) ${changeSymbol}`);
      }

      // Show discount structure
      console.log(`   Discounts Applied:`);
      if (pkg.discounts && pkg.discounts.length > 0) {
        pkg.discounts.forEach(discount => {
          const period = discount.period === 6 ? '6 months' : discount.period === 12 ? '12 months' : `${discount.period} months`;
          const discountedPrice = change.new * (1 - discount.percentage / 100);
          console.log(`     - ${period}: ${discount.percentage}% off → $${discountedPrice.toFixed(2)}/month`);
        });
      } else {
        console.log(`     - No discounts configured`);
      }

      console.log(`   Description: ${pkg.description}`);
      console.log('');
    });

    // Step 5: Verify database state
    console.log('🔍 Step 3: Verifying database state...');
    const Package = require('./models/Package');
    const contaboPackages = await Package.find({
      'vpsConfig.provider': 'contabo'
    }).populate('brand').populate('category');

    console.log(`📊 Total Contabo VPS packages in database: ${contaboPackages.length}`);
    
    if (contaboPackages.length > 0) {
      console.log('\n💰 Current Contabo VPS Pricing:');
      contaboPackages.forEach(pkg => {
        console.log(`   ${pkg.name}: $${pkg.price}/month (${pkg.vpsConfig.providerProductId})`);
      });
    }

    console.log('\n🎉 Pricing update test completed successfully!');
    console.log('✅ The complete pipeline is working: Gemini Scraping → Database Integration');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('\n📊 Database connection closed');
  }
}

// Run the test
if (require.main === module) {
  testPricingUpdate()
    .then(() => {
      console.log('\n✨ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testPricingUpdate };
