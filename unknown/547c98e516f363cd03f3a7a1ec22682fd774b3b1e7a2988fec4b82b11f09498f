import { BACKEND_URL } from '../config/constant';

const API_BASE_URL = `${BACKEND_URL}/admin/custom-notifications`;

/**
 * Get all custom notifications
 * @param {Object} params - Query parameters (page, limit, status)
 * @returns {Promise<Object>} - API response with notifications and pagination
 */
export const getCustomNotifications = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams(params).toString();
    const url = queryParams ? `${API_BASE_URL}?${queryParams}` : API_BASE_URL;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching custom notifications:', error);
    throw error;
  }
};

/**
 * Create a new custom notification
 * @param {Object} notificationData - The notification data
 * @returns {Promise<Object>} - API response with created notification
 */
export const createCustomNotification = async (notificationData) => {
  try {
    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(notificationData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating custom notification:', error);
    throw error;
  }
};

/**
 * Update a custom notification
 * @param {string} id - The notification ID
 * @param {Object} updateData - The data to update
 * @returns {Promise<Object>} - API response with updated notification
 */
export const updateCustomNotification = async (id, updateData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating custom notification:', error);
    throw error;
  }
};

/**
 * Delete a custom notification
 * @param {string} id - The notification ID
 * @returns {Promise<Object>} - API response
 */
export const deleteCustomNotification = async (id) => {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error deleting custom notification:', error);
    throw error;
  }
};

/**
 * Cancel a scheduled custom notification
 * @param {string} id - The notification ID
 * @returns {Promise<Object>} - API response
 */
export const cancelCustomNotification = async (id) => {
  try {
    const response = await fetch(`${API_BASE_URL}/${id}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error cancelling custom notification:', error);
    throw error;
  }
};

export default {
  getCustomNotifications,
  createCustomNotification,
  updateCustomNotification,
  deleteCustomNotification,
  cancelCustomNotification,
};
