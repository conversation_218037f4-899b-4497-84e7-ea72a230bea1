'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import '../styles/calendar.css';
import { X, Clock, Plus, Eye, Edit, Trash2 } from 'lucide-react';

const localizer = momentLocalizer(moment);

const InterviewCalendar = ({
  isOpen,
  onClose,
  selectedApplication,
  admins = [],
  onScheduleInterview,
  existingInterviews = [],
  onViewInterview,
  onEditInterview,
  onDeleteInterview
}) => {
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentView, setCurrentView] = useState('week');
  const [showInterviewMenu, setShowInterviewMenu] = useState(false);
  const [selectedInterviewEvent, setSelectedInterviewEvent] = useState(null);
  const [interviewData, setInterviewData] = useState({
    interviewer: '',
    duration: 60,
    notes: '',
    type: 'video' // video or in-person
  });

  // Convert existing interviews to calendar events with optimized memoization
  const calendarEvents = useMemo(() => {
    if (!existingInterviews || !Array.isArray(existingInterviews)) {
      return [];
    }

    // Pre-filter valid interviews to avoid processing invalid ones
    const validInterviews = existingInterviews.filter(interview =>
      interview &&
      interview._id &&
      interview.scheduledAt &&
      !isNaN(new Date(interview.scheduledAt).getTime())
    );

    return validInterviews.map(interview => {
      const startDate = new Date(interview.scheduledAt);
      const duration = interview.duration || 60;
      const endDate = new Date(startDate.getTime() + duration * 60000);

      return {
        id: interview._id,
        title: `${interview.candidateName || 'Unknown'}`,
        start: startDate,
        end: endDate,
        resource: interview,
        type: 'interview'
      };
    });
  }, [existingInterviews]);

  // Handle slot selection (when user clicks on calendar) - memoized
  const handleSelectSlot = useCallback((slotInfo) => {
    const now = new Date();
    if (slotInfo.start < now) {
      alert('Cannot schedule interviews in the past');
      return;
    }

    setSelectedSlot(slotInfo);
    setShowScheduleForm(true);
  }, []);

  // Handle event selection (when user clicks on existing interview) - memoized
  const handleSelectEvent = useCallback((event) => {
    const interview = event.resource;
    setSelectedInterviewEvent(interview);
    setShowInterviewMenu(true);
  }, []);

  // Handle form submission
  const handleSubmit = () => {
    if (!selectedSlot || !interviewData.interviewer) {
      alert('Please select an interviewer');
      return;
    }

    const interviewPayload = {
      applicationId: selectedApplication._id,
      candidateName: selectedApplication.name,
      candidateEmail: selectedApplication.email,
      interviewerId: interviewData.interviewer,
      scheduledAt: selectedSlot.start.toISOString(),
      duration: interviewData.duration,
      notes: interviewData.notes,
      type: interviewData.type
    };

    onScheduleInterview(interviewPayload);
    handleCloseForm();
  };

  const handleCloseForm = () => {
    setShowScheduleForm(false);
    setSelectedSlot(null);
    setInterviewData({
      interviewer: '',
      duration: 60,
      notes: '',
      type: 'video'
    });
  };

  const handleClose = () => {
    handleCloseForm();
    closeInterviewMenu();
    onClose();
  };

  // Close interview menu
  const closeInterviewMenu = () => {
    setShowInterviewMenu(false);
    setSelectedInterviewEvent(null);
  };

  // Handle interview actions
  const handleViewInterview = () => {
    if (onViewInterview && selectedInterviewEvent) {
      onViewInterview(selectedInterviewEvent);
      closeInterviewMenu();
    }
  };

  const handleEditInterview = () => {
    if (onEditInterview && selectedInterviewEvent) {
      onEditInterview(selectedInterviewEvent);
      closeInterviewMenu();
    }
  };

  const handleDeleteInterview = () => {
    if (onDeleteInterview && selectedInterviewEvent) {
      if (window.confirm('Are you sure you want to delete this interview?')) {
        onDeleteInterview(selectedInterviewEvent._id);
        closeInterviewMenu();
      }
    }
  };

  // Navigation handlers - memoized
  const handleNavigate = useCallback((newDate) => {
    setCurrentDate(newDate);
  }, []);

  const handleViewChange = useCallback((view) => {
    setCurrentView(view);
  }, []);

  // Custom toolbar component
  const CustomToolbar = ({ label, onNavigate, onView, view }) => {
    return (
      <div className="rbc-toolbar">
        <span className="rbc-btn-group">
          <button
            type="button"
            onClick={() => onNavigate('PREV')}
            className="rbc-btn rbc-btn-prev"
          >
            Back
          </button>
          <button
            type="button"
            onClick={() => onNavigate('TODAY')}
            className="rbc-btn rbc-btn-today"
          >
            Today
          </button>
          <button
            type="button"
            onClick={() => onNavigate('NEXT')}
            className="rbc-btn rbc-btn-next"
          >
            Next
          </button>
        </span>

        <span className="rbc-toolbar-label">{label}</span>

        <span className="rbc-btn-group">
          <button
            type="button"
            className={`rbc-btn ${view === 'month' ? 'rbc-active' : ''}`}
            onClick={() => onView('month')}
          >
            Month
          </button>
          <button
            type="button"
            className={`rbc-btn ${view === 'week' ? 'rbc-active' : ''}`}
            onClick={() => onView('week')}
          >
            Week
          </button>
          <button
            type="button"
            className={`rbc-btn ${view === 'day' ? 'rbc-active' : ''}`}
            onClick={() => onView('day')}
          >
            Day
          </button>
        </span>
      </div>
    );
  };

  // Custom event style getter - memoized
  const eventStyleGetter = useCallback((event) => {
    const status = event.resource?.status || 'scheduled';

    const statusStyles = {
      scheduled: { backgroundColor: '#3b82f6', borderColor: '#1d4ed8' },
      completed: { backgroundColor: '#10b981', borderColor: '#059669' },
      cancelled: { backgroundColor: '#ef4444', borderColor: '#dc2626' },
      rescheduled: { backgroundColor: '#f59e0b', borderColor: '#d97706' },
      default: { backgroundColor: '#6b7280', borderColor: '#4b5563' }
    };

    const { backgroundColor, borderColor } = statusStyles[status] || statusStyles.default;

    return {
      style: {
        backgroundColor,
        borderColor,
        color: 'white',
        border: `2px solid ${borderColor}`,
        borderRadius: '6px',
        fontSize: '12px',
        fontWeight: '500',
        padding: '2px 6px',
        cursor: 'pointer',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }
    };
  }, []);

  // Custom time slot style getter - memoized
  const slotStyleGetter = useCallback((date) => {
    const now = new Date();
    const isPast = date < now;

    return {
      style: {
        backgroundColor: isPast ? '#f3f4f6' : 'white',
        cursor: isPast ? 'not-allowed' : 'pointer'
      }
    };
  }, []);

  // Memoized calendar formats for better performance
  const calendarFormats = useMemo(() => ({
    timeGutterFormat: 'HH:mm',
    eventTimeRangeFormat: ({ start, end }) =>
      `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`,
    dayHeaderFormat: (date) => moment(date).format('ddd MM/DD'),
    dayRangeHeaderFormat: ({ start, end }) =>
      `${moment(start).format('MMM DD')} - ${moment(end).format('MMM DD, YYYY')}`,
    monthHeaderFormat: (date) => moment(date).format('MMMM YYYY'),
    dayFormat: (date) => moment(date).format('DD'),
    weekdayFormat: (date) => moment(date).format('ddd')
  }), []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900">
            Schedule Interview - {selectedApplication?.name}
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Candidate Info */}
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2">Candidate Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Name:</span> {selectedApplication?.name}
            </div>
            <div>
              <span className="font-medium">Email:</span> {selectedApplication?.email}
            </div>
            <div>
              <span className="font-medium">Phone:</span> {selectedApplication?.phone || 'N/A'}
            </div>
          </div>
        </div>

        {/* Instructions and Legend */}
        <div className="mb-4 grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <Plus className="h-4 w-4 inline mr-1" />
              Click on any available time slot in the calendar below to schedule an interview
            </p>
          </div>

          <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-sm font-medium text-gray-700 mb-2">Interview Status Legend:</p>
            <div className="flex flex-wrap gap-3 text-xs">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded mr-1"></div>
                <span>Scheduled</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded mr-1"></div>
                <span>Completed</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded mr-1"></div>
                <span>Cancelled</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded mr-1"></div>
                <span>Rescheduled</span>
              </div>
            </div>
          </div>
        </div>

        {/* Calendar */}
        <div className="mb-4" style={{ height: '500px' }}>
          <Calendar
            localizer={localizer}
            events={calendarEvents}
            startAccessor="start"
            endAccessor="end"
            onSelectSlot={handleSelectSlot}
            onSelectEvent={handleSelectEvent}
            onNavigate={handleNavigate}
            onView={handleViewChange}
            date={currentDate}
            view={currentView}
            selectable
            popup={false} // Disable popup for better performance
            eventPropGetter={eventStyleGetter}
            slotPropGetter={slotStyleGetter}
            views={['month', 'week', 'day']}
            step={30}
            timeslots={2}
            min={new Date(2024, 0, 1, 8, 0)} // 8 AM
            max={new Date(2024, 0, 1, 18, 0)} // 6 PM
            components={{
              toolbar: CustomToolbar
            }}
            formats={calendarFormats}
            showMultiDayTimes={false} // Improve performance
            dayLayoutAlgorithm="no-overlap" // Better layout performance
          />
        </div>

        {/* Schedule Form Modal */}
        {showScheduleForm && selectedSlot && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-60">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold">Schedule Interview</h4>
                <button
                  onClick={handleCloseForm}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                {/* Selected Time Display */}
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center text-sm text-blue-800">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="font-medium">
                      {moment(selectedSlot.start).format('MMMM Do, YYYY [at] HH:mm')}
                    </span>
                  </div>
                </div>

                {/* Duration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Duration
                  </label>
                  <select
                    value={interviewData.duration}
                    onChange={(e) => setInterviewData({...interviewData, duration: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={30}>30 minutes</option>
                    <option value={45}>45 minutes</option>
                    <option value={60}>1 hour</option>
                    <option value={90}>1.5 hours</option>
                    <option value={120}>2 hours</option>
                  </select>
                </div>

                {/* Interviewer */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Interviewer *
                  </label>
                  <select
                    value={interviewData.interviewer}
                    onChange={(e) => setInterviewData({...interviewData, interviewer: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select an interviewer</option>
                    {admins.map(admin => (
                      <option key={admin._id} value={admin._id}>
                        {admin.name} ({admin.email})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Interview Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Interview Type
                  </label>
                  <select
                    value={interviewData.type}
                    onChange={(e) => setInterviewData({...interviewData, type: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="video">Video Call (Google Meet)</option>
                    <option value="in-person">In-Person</option>
                    <option value="phone">Phone Call</option>
                  </select>
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={interviewData.notes}
                    onChange={(e) => setInterviewData({...interviewData, notes: e.target.value})}
                    rows={3}
                    placeholder="Add any additional notes for the interview..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={handleCloseForm}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                  Schedule Interview
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Interview Actions Menu */}
        {showInterviewMenu && selectedInterviewEvent && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-60">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold">Interview Actions</h4>
                <button
                  onClick={closeInterviewMenu}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-800 mb-2">
                  {selectedInterviewEvent.candidateName}
                </h5>
                <div className="text-sm text-gray-600 space-y-1">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>
                      {moment(selectedInterviewEvent.scheduledAt).format('MMMM Do, YYYY [at] HH:mm')}
                    </span>
                  </div>
                  <div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedInterviewEvent.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                      selectedInterviewEvent.status === 'completed' ? 'bg-green-100 text-green-800' :
                      selectedInterviewEvent.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {selectedInterviewEvent.status}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleViewInterview}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 flex items-center justify-center"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </button>
                <button
                  onClick={handleEditInterview}
                  className="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 flex items-center justify-center"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Interview
                </button>
                <button
                  onClick={handleDeleteInterview}
                  className="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 flex items-center justify-center"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Interview
                </button>
              </div>

              <div className="mt-4 pt-4 border-t">
                <button
                  onClick={closeInterviewMenu}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(InterviewCalendar, (prevProps, nextProps) => {
  // Custom comparison function for better performance
  return (
    prevProps.isOpen === nextProps.isOpen &&
    prevProps.selectedApplication?._id === nextProps.selectedApplication?._id &&
    prevProps.admins.length === nextProps.admins.length &&
    prevProps.existingInterviews.length === nextProps.existingInterviews.length &&
    JSON.stringify(prevProps.existingInterviews) === JSON.stringify(nextProps.existingInterviews)
  );
});
