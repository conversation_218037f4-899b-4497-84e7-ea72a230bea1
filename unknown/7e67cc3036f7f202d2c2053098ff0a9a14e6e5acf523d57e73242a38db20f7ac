"use client";

import { useTranslations } from "next-intl";
import TopNavbar from "./topNavbar";
import SiteBranding from "./siteBranding";
import MainNavbar from "./mainNavbar";
import { usePathname } from "next/navigation";
import { Input, Typography } from "@material-tailwind/react";
import { AvatarIcon } from "../avatar/AvatarIcon";
import { useAuth } from "../../app/context/AuthContext";
import { Bell, Command, Menu, Search } from "lucide-react";
import Notifications from "./Notifications";

export default function Header() {
  const t = useTranslations("shared");
  // const pathname = usePathname();
  // const { user } = useAuth(); // Assuming you have a user object in your AuthContext

  // if (pathname.includes("/admin")) {
  //   console.log("pathname contains: " + pathname);
  //   return (
  //     <div className="flex z-50 fixed top-0 left-0 right-0 md:ml-[250px] justify-between items-center px-8 py-4 border-b border-gray-200 bg-white">
  //       <div className="flex items-center justify-center gap-2">
  //         <div className="bg-gray-100 p-2 rounded-lg hover:bg-gray-50 cursor-pointer">
  //           <Menu className="w-6 h-6" />
  //         </div>
  //         {/* <div className="flex items-center gap-1 border border-gray-100 px-2 py-1.5 rounded-lg">
  //           <Search className="w-5 h-5" />
  //           <input
  //             type="text"
  //             placeholder="Search..."
  //             className="outline-none w-64 font-inter"
  //           />
  //           <div className="flex items-center justify-center bg-gray-50 px-2 py-1 rounded-md ">
  //             <Command className="w-4 h-4" />
  //             K
  //           </div>
  //         </div> */}
  //       </div>
  //       <div className="flex items-center justify-center gap-1">
  //         <Notifications />
  //         <AvatarIcon />
  //         <Typography variant="p" className="font-semibold">
  //           {user?.lastName + " " + user?.firstName}
  //         </Typography>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <header className="flex sticky top-0 z-50 right-0 left-0 p-0 w-full rounded-md bg-white border-b shadow-sm mb-0 max-w-[1900px] mx-auto h-[90px] lg:h-[80px]">
      <div className="flex flex-col w-full">
        {/* First row: TopNavbar */}
        <div className="w-full">
          <TopNavbar t={t} />
        </div>

        {/* Second row: Logo and MainNavbar */}
        <div className="w-full flex items-center md:p-0 lg:px-6 justify-between">
          <div className="w-[150px] flex-shrink-0 hidden lg:flex items-center justify-center">
            <SiteBranding />
          </div>
          <div className="lg:w-4/5 w-full">
            <MainNavbar t={t} />
          </div>
        </div>
      </div>
    </header>
  );
}
