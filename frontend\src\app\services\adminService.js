import apiService from "../lib/apiService";

export const adminService = {
  getAllUsers: (params = {}) =>
    apiService.get("/admin/users", { params, withCredentials: true }),
  // Add a new user
  addUser: (userData) =>
    apiService.post("/admin/users", userData, { withCredentials: true }),

  // Update an existing user
  updateUser: (userId, updateData) =>
    apiService.put(`/admin/users/${userId}`, updateData, {
      withCredentials: true,
    }),

  // Delete a user
  deleteUser: (userId) =>
    apiService.delete(`/admin/users/${userId}`, { withCredentials: true }),

  // Get all chat conversations with pagination
  getAllChatConversations: (params = {}) =>
    apiService.get("/admin/chats", { params, withCredentials: true }),

  // Get a specific chat conversation
  getChatConversation: (conversationId) =>
    apiService.get(`/admin/chats/${conversationId}`, { withCredentials: true }),

  // Delete a specific chat conversation
  deleteChatConversation: (conversationId) =>
    apiService.delete(`/admin/chats/${conversationId}`, {
      withCredentials: true,
    }),

  // Delete multiple chat conversations
  deleteMultipleChatConversations: (conversationIds) =>
    apiService.post(
      "/admin/chats/delete-multiple",
      { conversationIds },
      { withCredentials: true }
    ),

  // Delete all chat conversations
  deleteAllChatConversations: () =>
    apiService.delete("/admin/chats", { withCredentials: true }),

  // Get user chat conversations
  getUserChatConversations: (userId, params = {}) =>
    apiService.get(`/admin/users/${userId}/chats`, {
      params,
      withCredentials: true,
    }),

  // Get chatbot context
  getChatbotContext: () =>
    apiService.get("/admin/chatbot-context", { withCredentials: true }),

  // Update chatbot context
  updateChatbotContext: (content) =>
    apiService.put(
      "/admin/chatbot-context",
      { content },
      { withCredentials: true }
    ),

  // Get all packages
  getAllPackages: async (params = {}) => {
    try {
      // Build query string from params
      const queryParams = new URLSearchParams();

      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.brand && params.brand !== "all")
        queryParams.append("brand", params.brand);
      if (params.status && params.status !== "all")
        queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `/admin/packages?${queryString}`
        : "/admin/packages";

      // Make sure we're using apiService consistently
      const response = await apiService.get(url, { withCredentials: true });
      return response;
    } catch (error) {
      console.error("Error fetching packages:", error);
      throw error;
    }
  },

  // Add a new package
  addPackage: (packageData) =>
    apiService.post("/admin/packages", packageData, { withCredentials: true }),

  // Update an existing package
  updatePackage: (packageId, updateData) =>
    apiService.put(`/admin/packages/${packageId}`, updateData, {
      withCredentials: true,
    }),

  // Delete a package
  deletePackage: (packageId) =>
    apiService.delete(`/admin/packages/${packageId}`, {
      withCredentials: true,
    }),

  // Add a new spec
  addSpec: (specData) =>
    apiService.post("/admin/specs", specData, { withCredentials: true }),

  // Update spec
  updateSpec: (specData) =>
    apiService.put("/admin/update-spec", specData, { withCredentials: true }),

  // Get all specs
  getAllSpecs: () => apiService.get("/admin/specs", { withCredentials: true }),

  // Get all categories
  getAllCategories: () =>
    apiService.get("/admin/categories", { withCredentials: true }),

  // Get all brands
  getAllBrands: () =>
    apiService.get("/brand/get-brands", { withCredentials: true }),

  getAllTikcets: () => apiService.get("/tickets", { withCredentials: true }),

  updateTicketStatus: (id, data) =>
    apiService.put(`/tickets/${id}/status`, data, { withCredentials: true }),

  deeplTranslate: (data) =>
    apiService.post("/admin/deepl-translate", data, { withCredentials: true }),

  // New order methods
  getAllOrders: (params = {}) =>
    apiService.get("/admin/orders", {
      params, // Supports query params like page, limit, filters
      withCredentials: true,
    }),

  getOrderDetails: (orderId) =>
    apiService.get(`/admin/orders/${orderId}`, {
      withCredentials: true,
    }),

  updateOrderStatus: (orderId, statusData) =>
    apiService.put(`/admin/orders/${orderId}/status`, statusData, {
      withCredentials: true,
    }),

    // Get all categories
  getCategories: async () => {
    return await apiService.get('/admin/categories');
  },

  updateSubOrderStatus: (orderId, suborderId, statusData) =>
    apiService.put(
      `/admin/orders/${orderId}/${suborderId}/status`,
      statusData,
      { withCredentials: true }
    ),

  getDashboardStats: () =>
    apiService.get("/admin/dashboard/stats", { withCredentials: true }),

  getSSLCertificateStats: () =>
    apiService.get("/admin/dashboard/ssl-stats", { withCredentials: true }),

  // Get admin activity logs with filtering
  getAdminActivityLogs: (params = {}) => {
    console.log("Sending params to API:", params);
    return apiService.get("/admin/activity-logs", {
      params,
      withCredentials: true,
    });
  },

  // Get available filters for activity logs
  getActivityLogFilters: () =>
    apiService.get("/admin/activity-logs/filters", { withCredentials: true }),

  getPackageDistribution: async (categoryId) => {
    return await apiService.get(`/admin/dashboard/package-distribution?categoryId=${categoryId}`);
  },

  // Job methods
  getAllJobs: (params = {}) =>
    apiService.get("/admin/jobs", { params, withCredentials: true }),

  getJobById: (jobId) =>
    apiService.get(`/admin/jobs/${jobId}`, { withCredentials: true }),

  addJob: (jobData) =>
    apiService.post("/admin/jobs", jobData, { withCredentials: true }),

  updateJob: (jobId, updateData) =>
    apiService.put(`/admin/jobs/${jobId}`, updateData, { withCredentials: true }),

  deleteJob: (jobId) =>
    apiService.delete(`/admin/jobs/${jobId}`, { withCredentials: true }),

  // Job application methods
  getJobApplications: (jobId, params = {}) =>
    apiService.get(`/admin/jobs/${jobId}/applications`, { params, withCredentials: true }),

  getAllJobApplications: (params = {}) =>
    apiService.get("/admin/job-applications", { params, withCredentials: true }),

  updateApplicationStatus: (applicationId, statusData) =>
    apiService.put(`/admin/job-applications/${applicationId}/status`, statusData, { withCredentials: true }),

  deleteApplication: (applicationId) =>
    apiService.delete(`/admin/job-applications/${applicationId}`, { withCredentials: true }),

  // Get all admin users for interviewer selection
  getAdmins: () =>
    apiService.get("/admin/admins", { withCredentials: true }),

  // Interview methods
  createInterview: (interviewData) =>
    apiService.post("/admin/interviews", interviewData, { withCredentials: true }),

  getInterviews: (params = {}) =>
    apiService.get("/admin/interviews", { params, withCredentials: true }),

  getInterviewById: (interviewId) =>
    apiService.get(`/admin/interviews/${interviewId}`, { withCredentials: true }),

  updateInterview: (interviewId, updateData) =>
    apiService.put(`/admin/interviews/${interviewId}`, updateData, { withCredentials: true }),

  deleteInterview: (interviewId) =>
    apiService.delete(`/admin/interviews/${interviewId}`, { withCredentials: true }),

  // Get interviews for a specific application
  getApplicationInterviews: (applicationId) =>
    apiService.get(`/admin/applications/${applicationId}/interviews`, { withCredentials: true }),
  // Notification methods
  getAdminNotifications: async (params = {}) => {
    try {
      const response = await apiService.get("/admin/notifications", {
        params,
        withCredentials: true,
      });
      // The backend returns { data: { notifications, unreadCount, totalPages, currentPage } }
      // The component expects response.data.notifications and response.data.unreadCount
      // So, we directly return response which should contain the 'data' object from axios
      return response;
    } catch (error) {
      console.error(
        "Error fetching admin notifications:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },

  markNotificationAsRead: async (notificationId) => {
    try {
      const response = await apiService.put(
        `/admin/notifications/${notificationId}/read`,
        {},
        { withCredentials: true }
      );
      return response.data;
    } catch (error) {
      console.error(
        "Error marking notification as read:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },

  markAllNotificationsAsRead: async () => {
    try {
      const response = await apiService.put(
        "/admin/notifications/read-all",
        {},
        { withCredentials: true }
      );
      return response.data;
    } catch (error) {
      console.error(
        "Error marking all notifications as read:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },

  // Site Settings methods
  getSiteSettings: () =>
    apiService.get("/admin/site-settings", { withCredentials: true }),

  updateSiteSettings: (data) =>
    apiService.put("/admin/site-settings", data, { withCredentials: true }),

  getSiteSettingsSection: (section) =>
    apiService.get(`/admin/site-settings/${section}`, { withCredentials: true }),

  updateSiteSettingsSection: (section, data) =>
    apiService.put(`/admin/site-settings/${section}`, data, { withCredentials: true }),

  resetSiteSettings: () =>
    apiService.post("/admin/site-settings/reset", {}, { withCredentials: true }),
};
