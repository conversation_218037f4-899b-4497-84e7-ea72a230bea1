const { Billing } = require("../models/BillingAndShipping");



exports.setBilling = async (req, res) => {
    try {
        const { BillToName, email, phoneNumber, address } = req.body;

        // Create a new billing instance
        const newBilling = new Billing({
            user: req.auth.id,
            BillToName,
            email,
            phoneNumber,
            address
        });

        // Save the new billing information
        const savedBilling = await newBilling.save();

        return res.status(201).json({
            message: "Billing information created successfully",
            data: savedBilling
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: "Internal Server Error" });
    }
};


exports.getBilling = async (req, res) => {
    try {
        const userId = req.params.userId;
        // Find billing information for the specified user ID
        const billing = await Billing.findOne({ user: userId });

        if (billing) {
            return res.status(200).json({
                message: 'Billing information found',
                data: billing
            });
        } else {
            return res.status(404).json({
                message: 'Billing information not found for the user'
            });
        }
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal Server Error' });
    }
};
exports.updateBilling = async (req, res) => {
    try {
        const userId = req.params.userId;
        const updatedBillingInfo = req.body;

        // Ensure the user ID in the request matches the authenticated user's ID
        if (userId !== req.auth.id) {
            return res.status(403).json({ message: "Unauthorized" });
        }

        // Update the billing information in the database
        const updatedBilling = await Billing.findOneAndUpdate(
            { user: userId },
            updatedBillingInfo,
            { new: true }
        );

        return res.status(200).json({
            message: "Billing information updated successfully",
            data: updatedBilling
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: "Internal Server Error" });
    }
};