'use client'

import React, { useState, useEffect } from 'react';
import {
  Activity,
  DollarSign,
  UserCheck,
  Layers,
  Zap,
  Shield,
  Database,
  Server,
  Cpu,
  CheckCircle,
  ChevronUp,
  Users2,
  LineChart,
  ShoppingCart,
  BarChart2,
} from 'lucide-react';

export default function Dashboard() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    admins: 5,
    users: 100,
    products: 50,
    pages: 20,
    posts: 30,
    media: 40,
    support: 10,
    jobs: 5,
    orders: 200,
    revenue: 50000,
    systemStatus: {
      cpu: 45.5,
      memory: 60.3,
      storage: 70.2,
      uptime: '99.99%'
    }
  });

  // if (loading) {
  //   return (
  //     <div className="min-h-screen bg-gray-50 flex items-center justify-center">
  //       <Loader className="h-8 w-8 text-blue-600 animate-spin" />
  //     </div>
  //   );
  // }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{error}</h2>
          <button
            onClick={() => navigate.push('/admin/login')}
            className="text-blue-600 hover:text-blue-700"
          >
            Return to login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">CPU Usage</p>
              <h3 className="text-3xl font-bold">{stats.systemStatus.cpu.toFixed(1)}%</h3>
            </div>
            <Cpu className="h-8 w-8 text-blue-100" />
          </div>
          <div className="mt-4">
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div
                className="bg-white rounded-full h-2 transition-all duration-500"
                style={{ width: `${stats.systemStatus.cpu}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-500 to-green-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100">Memory Usage</p>
              <h3 className="text-3xl font-bold">{stats.systemStatus.memory.toFixed(1)}%</h3>
            </div>
            <Server className="h-8 w-8 text-green-100" />
          </div>
          <div className="mt-4">
            <div className="w-full bg-green-200 rounded-full h-2">
              <div
                className="bg-white rounded-full h-2 transition-all duration-500"
                style={{ width: `${stats.systemStatus.memory}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-500 to-purple-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">Storage</p>
              <h3 className="text-3xl font-bold">{stats.systemStatus.storage.toFixed(1)}%</h3>
            </div>
            <Database className="h-8 w-8 text-purple-100" />
          </div>
          <div className="mt-4">
            <div className="w-full bg-purple-200 rounded-full h-2">
              <div
                className="bg-white rounded-full h-2 transition-all duration-500"
                style={{ width: `${stats.systemStatus.storage}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-500 to-orange-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100">Uptime</p>
              <h3 className="text-3xl font-bold">{stats.systemStatus.uptime}</h3>
            </div>
            <Activity className="h-8 w-8 text-orange-100" />
          </div>
          <div className="mt-4 text-orange-100">
            System running smoothly
          </div>
        </div>
      </div>

      {/* User & Revenue Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* User Stats */}
        <div className="bg-white p-6 rounded-xl shadow-lg">
          <h3 className="text-lg font-semibold mb-6">User Statistics</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <Users2 className="h-8 w-8 text-blue-600" />
                <span className="text-sm text-blue-600 flex items-center">
                  <ChevronUp className="h-4 w-4 mr-1" />
                  12.5%
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">Total Users</p>
              <p className="text-2xl font-bold">{stats.users}</p>
            </div>

            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <UserCheck className="h-8 w-8 text-green-600" />
                <span className="text-sm text-green-600 flex items-center">
                  <ChevronUp className="h-4 w-4 mr-1" />
                  8.2%
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">Active Users</p>
              <p className="text-2xl font-bold">{Math.floor(stats.users * 0.8)}</p>
            </div>
          </div>

          <div className="mt-6">
            <LineChart className="w-full h-32 text-gray-300" />
          </div>
        </div>

        {/* Revenue Stats */}
        <div className="bg-white p-6 rounded-xl shadow-lg">
          <h3 className="text-lg font-semibold mb-6">Revenue Overview</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <DollarSign className="h-8 w-8 text-purple-600" />
                <span className="text-sm text-purple-600 flex items-center">
                  <ChevronUp className="h-4 w-4 mr-1" />
                  15.3%
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">Total Revenue</p>
              <p className="text-2xl font-bold">${stats.revenue.toLocaleString()}</p>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <ShoppingCart className="h-8 w-8 text-orange-600" />
                <span className="text-sm text-orange-600 flex items-center">
                  <ChevronUp className="h-4 w-4 mr-1" />
                  10.4%
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-2">Orders</p>
              <p className="text-2xl font-bold">{stats.orders}</p>
            </div>
          </div>

          <div className="mt-6">
            <BarChart2 className="w-full h-32 text-gray-300" />
          </div>
        </div>
      </div>

      {/* Content Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Content Overview</h3>
            <Layers className="h-6 w-6 text-blue-600" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Pages</span>
              <span className="font-semibold">{stats.pages}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Blog Posts</span>
              <span className="font-semibold">{stats.posts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Media Items</span>
              <span className="font-semibold">{stats.media}</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Security Status</h3>
            <Shield className="h-6 w-6 text-green-600" />
          </div>
          <div className="space-y-4">
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-5 w-5 mr-2" />
              <span>Firewall Active</span>
            </div>
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-5 w-5 mr-2" />
              <span>SSL Certificate Valid</span>
            </div>
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-5 w-5 mr-2" />
              <span>Database Encrypted</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Performance</h3>
            <Zap className="h-6 w-6 text-yellow-600" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Response Time</span>
              <span className="font-semibold text-green-600">120ms</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Cache Hit Rate</span>
              <span className="font-semibold text-green-600">98.5%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Error Rate</span>
              <span className="font-semibold text-green-600">0.01%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}