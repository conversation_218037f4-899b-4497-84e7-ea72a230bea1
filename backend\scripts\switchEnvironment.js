#!/usr/bin/env node

/**
 * Environment Switcher Script
 * 
 * This script helps you easily switch between production and development environments
 * 
 * Usage:
 *   node scripts/switchEnvironment.js dev    # Switch to development (Atlas)
 *   node scripts/switchEnvironment.js prod   # Switch to production (local MongoDB)
 *   node scripts/switchEnvironment.js status # Check current environment
 */

const fs = require('fs');
const path = require('path');

const CONSTANT_FILE_PATH = path.join(__dirname, '../constants/constant.js');

function getCurrentEnvironment() {
  try {
    const content = fs.readFileSync(CONSTANT_FILE_PATH, 'utf8');
    const isProdMatch = content.match(/const isProd = (true|false);/);
    
    if (isProdMatch) {
      return isProdMatch[1] === 'true' ? 'production' : 'development';
    }
    
    throw new Error('Could not determine current environment');
  } catch (error) {
    console.error('❌ Error reading constant file:', error.message);
    process.exit(1);
  }
}

function switchEnvironment(targetEnv) {
  try {
    const content = fs.readFileSync(CONSTANT_FILE_PATH, 'utf8');
    const newIsProd = targetEnv === 'production' ? 'true' : 'false';
    
    const updatedContent = content.replace(
      /const isProd = (true|false);.*$/m,
      `const isProd = ${newIsProd}; // Set to true for production (uses local MongoDB), false for development (uses Atlas)`
    );
    
    fs.writeFileSync(CONSTANT_FILE_PATH, updatedContent);
    
    console.log(`✅ Environment switched to: ${targetEnv}`);
    console.log(`📍 Database: ${targetEnv === 'production' ? 'Local MongoDB' : 'MongoDB Atlas'}`);
    console.log('🔄 Please restart your server for changes to take effect');
    
  } catch (error) {
    console.error('❌ Error switching environment:', error.message);
    process.exit(1);
  }
}

function showStatus() {
  const currentEnv = getCurrentEnvironment();
  const dbType = currentEnv === 'production' ? 'Local MongoDB' : 'MongoDB Atlas';
  
  console.log('📊 Current Environment Status:');
  console.log(`   Environment: ${currentEnv}`);
  console.log(`   Database: ${dbType}`);
  console.log(`   isProd: ${currentEnv === 'production'}`);
}

function showHelp() {
  console.log('🔧 Environment Switcher');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/switchEnvironment.js dev     # Switch to development (Atlas)');
  console.log('  node scripts/switchEnvironment.js prod    # Switch to production (local MongoDB)');
  console.log('  node scripts/switchEnvironment.js status  # Check current environment');
  console.log('  node scripts/switchEnvironment.js help    # Show this help');
  console.log('');
  console.log('Environments:');
  console.log('  • Development: Uses MongoDB Atlas (requires MONGODB_URI in .env)');
  console.log('  • Production: Uses local MongoDB (mongodb://0.0.0.0:27017/ztech)');
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'dev':
  case 'development':
    switchEnvironment('development');
    break;
    
  case 'prod':
  case 'production':
    switchEnvironment('production');
    break;
    
  case 'status':
    showStatus();
    break;
    
  case 'help':
  case '--help':
  case '-h':
    showHelp();
    break;
    
  default:
    console.log('❌ Invalid command. Use "help" to see available options.');
    showHelp();
    process.exit(1);
}
