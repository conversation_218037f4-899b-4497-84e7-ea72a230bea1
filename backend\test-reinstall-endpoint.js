/**
 * Test script for VPS Reinstall endpoint
 * Tests the /api/vps/instances/:instanceId/reinstall endpoint with frontend payload format
 */

const axios = require('axios');

async function testReinstallEndpoint() {
  console.log('🧪 Testing VPS Reinstall Endpoint');
  console.log('=================================');
  
  try {
    console.log('\n1. Testing /api/vps/instances/202718127/reinstall endpoint...');
    
    // This is the exact payload format that the frontend sends
    const frontendPayload = {
      "imageId": "db1409d2-ed92-4f2f-978e-7b2fa4a1ec90",
      "password": "TestPassword123!",
      "enableRootUser": true,
      "selectedApplication": "82802c59-6061-45e0-ae1d-bbe855157c66",
      "userData": "#cloud-config\n# Enable password authentication for SSH\nssh_pwauth: true\npassword: TestPassword123!\nchpasswd:\n  expire: false\n\n# Configure SSH to allow password authentication\nwrite_files:\n  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf\n    content: |\n      PasswordAuthentication yes\n      PermitRootLogin yes\n      PubkeyAuthentication yes\n    permissions: '0644'\n\nruncmd:\n  - systemctl restart sshd || service ssh restart\n  - echo \"Password authentication enabled for standard installation\" >> /root/install.log"
    };
    
    console.log('📦 Frontend payload structure:');
    console.log(`   - imageId: ${frontendPayload.imageId}`);
    console.log(`   - password: ${frontendPayload.password ? '***HIDDEN***' : 'not provided'}`);
    console.log(`   - enableRootUser: ${frontendPayload.enableRootUser}`);
    console.log(`   - selectedApplication: ${frontendPayload.selectedApplication}`);
    console.log(`   - userData: ${frontendPayload.userData ? 'provided' : 'not provided'}`);
    
    const response = await axios.post(
      'http://localhost:5002/api/vps/instances/202718127/reinstall',
      frontendPayload,
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      }
    );
    
    console.log('✅ Endpoint is accessible');
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`📦 Response:`, response.data);
    
    console.log('\n🎉 Reinstall endpoint test completed successfully!');
    console.log('\n✅ Fix Summary:');
    console.log('- Updated route to use reinstallVPS instead of reinstallInstance');
    console.log('- Added reinstallVPS import to vpsRouter.js');
    console.log('- Updated validation to accept selectedApplication field');
    console.log('- Added payload mapping in vpsService.js:');
    console.log('  * password → rootPassword');
    console.log('  * selectedApplication → applicationId');
    console.log('- Frontend payload format is now supported');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the backend server is running: npm run dev');
    } else if (error.response) {
      console.log(`📊 HTTP Status: ${error.response.status}`);
      console.log(`📋 Error Response:`, error.response.data);
      
      if (error.response.status === 400) {
        console.log('\n🔍 Validation Error Analysis:');
        if (error.response.data.errors) {
          error.response.data.errors.forEach(err => {
            console.log(`   - ${err.key}: ${err.msg}`);
          });
        }
      } else if (error.response.status === 401) {
        console.log('💡 Authentication required - this is expected for this endpoint');
      } else if (error.response.status === 404) {
        console.log('💡 VPS instance not found or endpoint missing');
      }
    }
  }
}

// Test payload validation separately
async function testPayloadValidation() {
  console.log('\n2. Testing payload validation...');
  
  const testPayloads = [
    {
      name: 'Missing imageId',
      payload: {
        "password": "TestPassword123!",
        "selectedApplication": "82802c59-6061-45e0-ae1d-bbe855157c66"
      }
    },
    {
      name: 'Valid minimal payload',
      payload: {
        "imageId": "db1409d2-ed92-4f2f-978e-7b2fa4a1ec90"
      }
    },
    {
      name: 'Valid full payload',
      payload: {
        "imageId": "db1409d2-ed92-4f2f-978e-7b2fa4a1ec90",
        "password": "TestPassword123!",
        "enableRootUser": true,
        "selectedApplication": "82802c59-6061-45e0-ae1d-bbe855157c66",
        "userData": "#cloud-config\nssh_pwauth: true"
      }
    }
  ];
  
  for (const test of testPayloads) {
    try {
      console.log(`\n   Testing: ${test.name}`);
      const response = await axios.post(
        'http://localhost:5002/api/vps/instances/202718127/reinstall',
        test.payload,
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000
        }
      );
      console.log(`   ✅ ${test.name}: Passed validation`);
    } catch (error) {
      if (error.response?.status === 400) {
        console.log(`   ❌ ${test.name}: Validation failed`);
        if (error.response.data.errors) {
          error.response.data.errors.forEach(err => {
            console.log(`      - ${err.key}: ${err.msg}`);
          });
        }
      } else if (error.response?.status === 401) {
        console.log(`   ✅ ${test.name}: Passed validation (auth required)`);
      } else {
        console.log(`   ❓ ${test.name}: ${error.response?.status || error.message}`);
      }
    }
  }
}

// Run the tests
async function runTests() {
  await testReinstallEndpoint();
  await testPayloadValidation();
}

runTests();
