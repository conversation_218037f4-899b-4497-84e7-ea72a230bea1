'use client';

import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Key, Eye, EyeOff, RefreshCw, Info, Shield } from 'lucide-react';
import vpsService from '../services/vpsService';

const PasswordResetModal = ({ 
  isOpen, 
  onClose, 
  server, 
  onResetSuccess 
}) => {
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
    sshKeys: [],
    userData: ''
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        newPassword: '',
        confirmPassword: '',
        sshKeys: [],
        userData: ''
      });
      setErrors({});
      setShowPassword(false);
      setShowConfirmPassword(false);
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors = {};

    // Validate password according to Contabo requirements
    if (!formData.newPassword) {
      newErrors.newPassword = 'Le nouveau mot de passe est requis';
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = 'Le mot de passe doit contenir au moins 8 caractères';
    } else {
      // Check Contabo password requirements
      const password = formData.newPassword;
      const hasUppercase = /[A-Z]/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const numberCount = (password.match(/[0-9]/g) || []).length;
      const specialCount = (password.match(/[!@#$^&*?_~]/g) || []).length;
      const hasInvalidChars = /[^a-zA-Z0-9!@#$^&*?_~]/.test(password);

      if (hasInvalidChars) {
        newErrors.newPassword = 'Caractères autorisés: a-z, A-Z, 0-9, !@#$^&*?_~';
      } else if (!hasUppercase) {
        newErrors.newPassword = 'Le mot de passe doit contenir au moins une majuscule';
      } else if (!hasLowercase) {
        newErrors.newPassword = 'Le mot de passe doit contenir au moins une minuscule';
      } else if (!((numberCount >= 1 && specialCount >= 2) || (numberCount >= 3 && specialCount >= 1))) {
        newErrors.newPassword = 'Le mot de passe doit contenir soit (1 chiffre + 2 caractères spéciaux) soit (3 chiffres + 1 caractère spécial)';
      }
    }

    // Validate password confirmation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'La confirmation du mot de passe est requise';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Les mots de passe ne correspondent pas';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const generateSecurePassword = () => {
    // Contabo password requirements:
    // - At least 8 characters
    // - At least one uppercase letter
    // - At least one lowercase letter
    // - Either: (1 number + 2 special chars) OR (3 numbers + 1 special char)
    // - Special chars: !@#$^&*?_~

    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '!@#$^&*?_~';

    // Strategy: Use 3 numbers + 1 special char (easier to satisfy)
    let password = '';

    // Add required characters
    password += uppercase[Math.floor(Math.random() * uppercase.length)]; // 1 uppercase
    password += lowercase[Math.floor(Math.random() * lowercase.length)]; // 1 lowercase
    password += numbers[Math.floor(Math.random() * numbers.length)];     // 1st number
    password += numbers[Math.floor(Math.random() * numbers.length)];     // 2nd number
    password += numbers[Math.floor(Math.random() * numbers.length)];     // 3rd number
    password += specialChars[Math.floor(Math.random() * specialChars.length)]; // 1 special

    // Add additional characters to reach 12 characters for security
    const allChars = uppercase + lowercase + numbers + specialChars;
    while (password.length < 12) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password to randomize character positions
    const passwordArray = password.split('');
    for (let i = passwordArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];
    }

    const finalPassword = passwordArray.join('');

    // Verify the generated password meets Contabo requirements
    const hasUppercase = /[A-Z]/.test(finalPassword);
    const hasLowercase = /[a-z]/.test(finalPassword);
    const numberCount = (finalPassword.match(/[0-9]/g) || []).length;
    const specialCount = (finalPassword.match(/[!@#$^&*?_~]/g) || []).length;
    const meetsRequirements = hasUppercase && hasLowercase &&
      ((numberCount >= 1 && specialCount >= 2) || (numberCount >= 3 && specialCount >= 1));

    if (!meetsRequirements) {
      // Retry if validation fails (should be rare)
      return generateSecurePassword();
    }

    setFormData(prev => ({
      ...prev,
      newPassword: finalPassword,
      confirmPassword: finalPassword
    }));

    // Clear any existing errors
    setErrors(prev => ({
      ...prev,
      newPassword: '',
      confirmPassword: ''
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // Prepare reset data according to Contabo API
      const resetData = {
        rootPassword: formData.newPassword, // This should be converted to secretId by backend
        defaultUser: "root", // Password reset should always enable root user
        sshKeys: formData.sshKeys.length > 0 ? formData.sshKeys : undefined,
        userData: formData.userData.trim() || undefined
      };

      // Call the API
      const response = await vpsService.resetVPSPassword(server.id, resetData);

      if (response.data.success) {
        // Call success callback if provided
        if (onResetSuccess) {
          onResetSuccess(response.data);
        }

        // Close modal
        onClose();
      } else {
        throw new Error(response.data.message || 'Failed to reset VPS password');
      }

    } catch (error) {
      setErrors({
        submit: error.message || 'Une erreur est survenue lors de la réinitialisation du mot de passe'
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Key className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Réinitialiser le mot de passe
              </h2>
              <p className="text-sm text-gray-500">
                {server?.name || `VPS ${server?.id}`}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Warning */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-start space-x-3 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="font-medium text-orange-800 mb-1">
                Attention - Réinitialisation du mot de passe
              </p>
              <p className="text-orange-700">
                Cette action va réinitialiser le mot de passe root de votre VPS. 
                Assurez-vous de sauvegarder le nouveau mot de passe en lieu sûr.
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Password Fields */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nouveau mot de passe *
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.newPassword}
                  onChange={(e) => handleInputChange('newPassword', e.target.value)}
                  className={`w-full px-3 py-2 pr-20 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.newPassword ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Entrez le nouveau mot de passe"
                  disabled={isLoading}
                />
                <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>
              {errors.newPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.newPassword}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Exigences Contabo: 8+ caractères, 1 majuscule, 1 minuscule, soit (1 chiffre + 2 spéciaux) soit (3 chiffres + 1 spécial). Caractères spéciaux: !@#$^&*?_~
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Confirmer le mot de passe *
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className={`w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Confirmez le nouveau mot de passe"
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                  disabled={isLoading}
                >
                  {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>

            {/* Generate Password Button */}
            <button
              type="button"
              onClick={generateSecurePassword}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
              disabled={isLoading}
            >
              <RefreshCw className="w-4 h-4" />
              <span>Générer un mot de passe sécurisé</span>
            </button>
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              disabled={isLoading}
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex items-center space-x-2 px-6 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Réinitialisation...</span>
                </>
              ) : (
                <>
                  <Shield className="w-4 h-4" />
                  <span>Réinitialiser le mot de passe</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PasswordResetModal;
