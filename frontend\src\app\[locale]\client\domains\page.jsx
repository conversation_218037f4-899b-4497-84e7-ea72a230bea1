"use client";
import { Typo<PERSON>, Card, CardBody } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { Globe } from "lucide-react";
import orderService from "@/app/services/orderService";
import PaymentStatusModal from "@/components/order/paymentStatusModal";
import { PaymentStatus } from "@/app/config/ConstStatus";

export default function DomainOrdersPage() {
  const t = useTranslations("client");
  const ht = useTranslations("DOMAINS");
  const [subOrders, setSubOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  const [paymentStatus, setPaymentStatus] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [openModal, setOpenModal] = useState(false);

  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const item = searchParams.get("item");
  const categoryName = "DOMAINS";

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setOrderId(item);
      setOpenModal(true);
    }
  }, [status, item]);

  const closeModal = () => {
    setOpenModal(false);
  };

  useEffect(() => {
    const getSubOrders = async () => {
      try {
        const res = await orderService.getSubOrdersByCategory(categoryName);
        setSubOrders(res.data.subOrders);
        console.log("res.data.subOrders: ", res);
      } catch (error) {
        console.error("Error getting suborders", error);
      } finally {
        setLoading(false);
      }
    };
    getSubOrders();
  }, [categoryName]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Typography variant="h6" className="text-gray-600">
          {t("loading_orders")}
        </Typography>
      </div>
    );
  }

  if (!subOrders?.length) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Typography variant="h4" className="text-gray-600">
          {t("no_orders_found")}
        </Typography>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <Typography
        variant="h1"
        className="text-3xl font-bold mb-6 text-gray-800"
      >
        {t("your_orders")}
      </Typography>
      <div className="space-y-4">
        {subOrders.map((subOrder) => {
          const {
            _id,
            package: pkg,
            quantity,
            price,
            status,
            createdAt,
            period,
            basedPrice,
          } = subOrder;
          const expireAt = new Date(createdAt);
          expireAt.setMonth(expireAt.getMonth() + period);

          return (
            <Card
              key={_id}
              className="bg-white rounded-xl shadow-sm p-0 border border-gray-200"
            >
              <CardBody className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Globe className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <Typography
                        variant="h6"
                        className="text-lg font-semibold text-gray-900"
                      >
                        {pkg.brand.name + ": " + ht(pkg.name)}
                      </Typography>
                      <Typography className="text-sm text-gray-500">
                        {t("reference")} {pkg.reference}
                      </Typography>
                    </div>
                  </div>
                  <div className="text-right">
                    <span
                      className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium capitalize ${
                        status === OrderStatus.PROCESSING
                          ? "bg-yellow-700 text-white"
                          : "bg-green-100 text-green-800"
                      }`}
                    >
                      {status}
                    </span>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Typography className="text-sm text-gray-500">
                      {t("quantity")}
                    </Typography>
                    <Typography className="mt-1 font-medium">
                      {quantity}
                    </Typography>
                  </div>
                  <div>
                    <Typography className="text-sm text-gray-500">
                      {t("total")}
                    </Typography>
                    <Typography className="mt-1 font-medium">
                      {price * quantity} MAD
                      {basedPrice > price && (
                        <span className="line-through text-gray-400 ml-2">
                          {basedPrice} MAD
                        </span>
                      )}
                    </Typography>
                  </div>
                  <div>
                    <Typography className="text-sm text-gray-500">
                      {t("issued_at")}
                    </Typography>
                    <Typography className="mt-1 font-medium">
                      {new Date(createdAt).toLocaleDateString("en-UK")}
                    </Typography>
                  </div>
                  <div>
                    <Typography className="text-sm text-gray-500">
                      {t("expire_at")}
                    </Typography>
                    <Typography className="mt-1 font-medium">
                      {new Date(expireAt).toLocaleDateString("en-UK")} ({period}{" "}
                      months)
                    </Typography>
                  </div>
                </div>

                {/* <div className="mt-6 flex items-center justify-end space-x-4">
                                    <button
                                        className="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center"
                                        onClick={() => router.push(`/manage/${_id}`)}
                                    >
                                        {t('manage_plan')}
                                        <ArrowRight className="ml-1 h-4 w-4" />
                                    </button>
                                </div> */}
              </CardBody>
            </Card>
          );
        })}
      </div>

      {openModal && (
        <PaymentStatusModal
          status={PaymentStatus}
          orderId={orderId}
          onClose={closeModal}
          t={t}
        />
      )}
    </div>
  );
}
