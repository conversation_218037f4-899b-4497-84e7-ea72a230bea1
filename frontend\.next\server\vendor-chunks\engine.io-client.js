"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/engine.io-client";
exports.ids = ["vendor-chunks/engine.io-client"];
exports.modules = {

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js":
/*!***************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasCORS: () => (/* binding */ hasCORS)\n/* harmony export */ });\n// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== \"undefined\" && \"withCredentials\" in new XMLHttpRequest();\n} catch (err) {\n// if XMLHttp support is disabled in IE then it will throw\n// when trying to create\n}\nconst hasCORS = value;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvY29udHJpYi9oYXMtY29ycy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsc0RBQXNEO0FBQ3RELElBQUlBLFFBQVE7QUFDWixJQUFJO0lBQ0FBLFFBQVEsT0FBT0MsbUJBQW1CLGVBQzlCLHFCQUFxQixJQUFJQTtBQUNqQyxFQUNBLE9BQU9DLEtBQUs7QUFDUiwwREFBMEQ7QUFDMUQsd0JBQXdCO0FBQzVCO0FBQ08sTUFBTUMsVUFBVUgsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvY29udHJpYi9oYXMtY29ycy5qcz9mN2JmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGltcG9ydGVkIGZyb20gaHR0cHM6Ly9naXRodWIuY29tL2NvbXBvbmVudC9oYXMtY29yc1xubGV0IHZhbHVlID0gZmFsc2U7XG50cnkge1xuICAgIHZhbHVlID0gdHlwZW9mIFhNTEh0dHBSZXF1ZXN0ICE9PSAndW5kZWZpbmVkJyAmJlxuICAgICAgICAnd2l0aENyZWRlbnRpYWxzJyBpbiBuZXcgWE1MSHR0cFJlcXVlc3QoKTtcbn1cbmNhdGNoIChlcnIpIHtcbiAgICAvLyBpZiBYTUxIdHRwIHN1cHBvcnQgaXMgZGlzYWJsZWQgaW4gSUUgdGhlbiBpdCB3aWxsIHRocm93XG4gICAgLy8gd2hlbiB0cnlpbmcgdG8gY3JlYXRlXG59XG5leHBvcnQgY29uc3QgaGFzQ09SUyA9IHZhbHVlO1xuIl0sIm5hbWVzIjpbInZhbHVlIiwiWE1MSHR0cFJlcXVlc3QiLCJlcnIiLCJoYXNDT1JTIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js":
/*!**************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */ function encode(obj) {\n    let str = \"\";\n    for(let i in obj){\n        if (obj.hasOwnProperty(i)) {\n            if (str.length) str += \"&\";\n            str += encodeURIComponent(i) + \"=\" + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */ function decode(qs) {\n    let qry = {};\n    let pairs = qs.split(\"&\");\n    for(let i = 0, l = pairs.length; i < l; i++){\n        let pair = pairs[i].split(\"=\");\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvY29udHJpYi9wYXJzZXFzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEscURBQXFEO0FBQ3JEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNBLE9BQU9DLEdBQUc7SUFDdEIsSUFBSUMsTUFBTTtJQUNWLElBQUssSUFBSUMsS0FBS0YsSUFBSztRQUNmLElBQUlBLElBQUlHLGNBQWMsQ0FBQ0QsSUFBSTtZQUN2QixJQUFJRCxJQUFJRyxNQUFNLEVBQ1ZILE9BQU87WUFDWEEsT0FBT0ksbUJBQW1CSCxLQUFLLE1BQU1HLG1CQUFtQkwsR0FBRyxDQUFDRSxFQUFFO1FBQ2xFO0lBQ0o7SUFDQSxPQUFPRDtBQUNYO0FBQ0E7Ozs7O0NBS0MsR0FDTSxTQUFTSyxPQUFPQyxFQUFFO0lBQ3JCLElBQUlDLE1BQU0sQ0FBQztJQUNYLElBQUlDLFFBQVFGLEdBQUdHLEtBQUssQ0FBQztJQUNyQixJQUFLLElBQUlSLElBQUksR0FBR1MsSUFBSUYsTUFBTUwsTUFBTSxFQUFFRixJQUFJUyxHQUFHVCxJQUFLO1FBQzFDLElBQUlVLE9BQU9ILEtBQUssQ0FBQ1AsRUFBRSxDQUFDUSxLQUFLLENBQUM7UUFDMUJGLEdBQUcsQ0FBQ0ssbUJBQW1CRCxJQUFJLENBQUMsRUFBRSxFQUFFLEdBQUdDLG1CQUFtQkQsSUFBSSxDQUFDLEVBQUU7SUFDakU7SUFDQSxPQUFPSjtBQUNYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9lbmdpbmUuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy9jb250cmliL3BhcnNlcXMuanM/Yzc3NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBpbXBvcnRlZCBmcm9tIGh0dHBzOi8vZ2l0aHViLmNvbS9nYWxrbi9xdWVyeXN0cmluZ1xuLyoqXG4gKiBDb21waWxlcyBhIHF1ZXJ5c3RyaW5nXG4gKiBSZXR1cm5zIHN0cmluZyByZXByZXNlbnRhdGlvbiBvZiB0aGUgb2JqZWN0XG4gKlxuICogQHBhcmFtIHtPYmplY3R9XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGVuY29kZShvYmopIHtcbiAgICBsZXQgc3RyID0gJyc7XG4gICAgZm9yIChsZXQgaSBpbiBvYmopIHtcbiAgICAgICAgaWYgKG9iai5oYXNPd25Qcm9wZXJ0eShpKSkge1xuICAgICAgICAgICAgaWYgKHN0ci5sZW5ndGgpXG4gICAgICAgICAgICAgICAgc3RyICs9ICcmJztcbiAgICAgICAgICAgIHN0ciArPSBlbmNvZGVVUklDb21wb25lbnQoaSkgKyAnPScgKyBlbmNvZGVVUklDb21wb25lbnQob2JqW2ldKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gc3RyO1xufVxuLyoqXG4gKiBQYXJzZXMgYSBzaW1wbGUgcXVlcnlzdHJpbmcgaW50byBhbiBvYmplY3RcbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gcXNcbiAqIEBhcGkgcHJpdmF0ZVxuICovXG5leHBvcnQgZnVuY3Rpb24gZGVjb2RlKHFzKSB7XG4gICAgbGV0IHFyeSA9IHt9O1xuICAgIGxldCBwYWlycyA9IHFzLnNwbGl0KCcmJyk7XG4gICAgZm9yIChsZXQgaSA9IDAsIGwgPSBwYWlycy5sZW5ndGg7IGkgPCBsOyBpKyspIHtcbiAgICAgICAgbGV0IHBhaXIgPSBwYWlyc1tpXS5zcGxpdCgnPScpO1xuICAgICAgICBxcnlbZGVjb2RlVVJJQ29tcG9uZW50KHBhaXJbMF0pXSA9IGRlY29kZVVSSUNvbXBvbmVudChwYWlyWzFdKTtcbiAgICB9XG4gICAgcmV0dXJuIHFyeTtcbn1cbiJdLCJuYW1lcyI6WyJlbmNvZGUiLCJvYmoiLCJzdHIiLCJpIiwiaGFzT3duUHJvcGVydHkiLCJsZW5ndGgiLCJlbmNvZGVVUklDb21wb25lbnQiLCJkZWNvZGUiLCJxcyIsInFyeSIsInBhaXJzIiwic3BsaXQiLCJsIiwicGFpciIsImRlY29kZVVSSUNvbXBvbmVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js":
/*!***************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> Levithan <stevenlevithan.com> (MIT license)\n * @api private\n */ const re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    \"source\",\n    \"protocol\",\n    \"authority\",\n    \"userInfo\",\n    \"user\",\n    \"password\",\n    \"host\",\n    \"port\",\n    \"relative\",\n    \"path\",\n    \"directory\",\n    \"file\",\n    \"query\",\n    \"anchor\"\n];\nfunction parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf(\"[\"), e = str.indexOf(\"]\");\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, \";\") + str.substring(e, str.length);\n    }\n    let m = re.exec(str || \"\"), uri = {}, i = 14;\n    while(i--){\n        uri[parts[i]] = m[i] || \"\";\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, \":\");\n        uri.authority = uri.authority.replace(\"[\", \"\").replace(\"]\", \"\").replace(/;/g, \":\");\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri[\"path\"]);\n    uri.queryKey = queryKey(uri, uri[\"query\"]);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == \"/\" || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == \"/\") {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js":
/*!***********************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/globals.node.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CookieJar: () => (/* binding */ CookieJar),\n/* harmony export */   createCookieJar: () => (/* binding */ createCookieJar),\n/* harmony export */   defaultBinaryType: () => (/* binding */ defaultBinaryType),\n/* harmony export */   globalThisShim: () => (/* binding */ globalThisShim),\n/* harmony export */   nextTick: () => (/* binding */ nextTick),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\nconst nextTick = process.nextTick;\nconst globalThisShim = global;\nconst defaultBinaryType = \"nodebuffer\";\nfunction createCookieJar() {\n    return new CookieJar();\n}\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie\n */ function parse(setCookieString) {\n    const parts = setCookieString.split(\"; \");\n    const i = parts[0].indexOf(\"=\");\n    if (i === -1) {\n        return;\n    }\n    const name = parts[0].substring(0, i).trim();\n    if (!name.length) {\n        return;\n    }\n    let value = parts[0].substring(i + 1).trim();\n    if (value.charCodeAt(0) === 0x22) {\n        // remove double quotes\n        value = value.slice(1, -1);\n    }\n    const cookie = {\n        name,\n        value\n    };\n    for(let j = 1; j < parts.length; j++){\n        const subParts = parts[j].split(\"=\");\n        if (subParts.length !== 2) {\n            continue;\n        }\n        const key = subParts[0].trim();\n        const value = subParts[1].trim();\n        switch(key){\n            case \"Expires\":\n                cookie.expires = new Date(value);\n                break;\n            case \"Max-Age\":\n                const expiration = new Date();\n                expiration.setUTCSeconds(expiration.getUTCSeconds() + parseInt(value, 10));\n                cookie.expires = expiration;\n                break;\n            default:\n        }\n    }\n    return cookie;\n}\nclass CookieJar {\n    constructor(){\n        this._cookies = new Map();\n    }\n    parseCookies(values) {\n        if (!values) {\n            return;\n        }\n        values.forEach((value)=>{\n            const parsed = parse(value);\n            if (parsed) {\n                this._cookies.set(parsed.name, parsed);\n            }\n        });\n    }\n    get cookies() {\n        const now = Date.now();\n        this._cookies.forEach((cookie, name)=>{\n            var _a;\n            if (((_a = cookie.expires) === null || _a === void 0 ? void 0 : _a.getTime()) < now) {\n                this._cookies.delete(name);\n            }\n        });\n        return this._cookies.entries();\n    }\n    addCookies(xhr) {\n        const cookies = [];\n        for (const [name, cookie] of this.cookies){\n            cookies.push(`${name}=${cookie.value}`);\n        }\n        if (cookies.length) {\n            xhr.setDisableHeaderCheck(true);\n            xhr.setRequestHeader(\"cookie\", cookies.join(\"; \"));\n        }\n    }\n    appendCookies(headers) {\n        for (const [name, cookie] of this.cookies){\n            headers.append(\"cookie\", `${name}=${cookie.value}`);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* reexport safe */ _transports_polling_fetch_js__WEBPACK_IMPORTED_MODULE_6__.Fetch),\n/* harmony export */   NodeWebSocket: () => (/* reexport safe */ _transports_websocket_node_js__WEBPACK_IMPORTED_MODULE_9__.WS),\n/* harmony export */   NodeXHR: () => (/* reexport safe */ _transports_polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_7__.XHR),\n/* harmony export */   Socket: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_0__.Socket),\n/* harmony export */   SocketWithUpgrade: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_0__.SocketWithUpgrade),\n/* harmony export */   SocketWithoutUpgrade: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_0__.SocketWithoutUpgrade),\n/* harmony export */   Transport: () => (/* reexport safe */ _transport_js__WEBPACK_IMPORTED_MODULE_1__.Transport),\n/* harmony export */   TransportError: () => (/* reexport safe */ _transport_js__WEBPACK_IMPORTED_MODULE_1__.TransportError),\n/* harmony export */   WebSocket: () => (/* reexport safe */ _transports_websocket_js__WEBPACK_IMPORTED_MODULE_10__.WS),\n/* harmony export */   WebTransport: () => (/* reexport safe */ _transports_webtransport_js__WEBPACK_IMPORTED_MODULE_11__.WT),\n/* harmony export */   XHR: () => (/* reexport safe */ _transports_polling_xhr_js__WEBPACK_IMPORTED_MODULE_8__.XHR),\n/* harmony export */   installTimerFunctions: () => (/* reexport safe */ _util_js__WEBPACK_IMPORTED_MODULE_3__.installTimerFunctions),\n/* harmony export */   nextTick: () => (/* reexport safe */ _globals_node_js__WEBPACK_IMPORTED_MODULE_5__.nextTick),\n/* harmony export */   parse: () => (/* reexport safe */ _contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_4__.parse),\n/* harmony export */   protocol: () => (/* binding */ protocol),\n/* harmony export */   transports: () => (/* reexport safe */ _transports_index_js__WEBPACK_IMPORTED_MODULE_2__.transports)\n/* harmony export */ });\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _transports_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transports/index.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contrib/parseuri.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var _transports_polling_fetch_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./transports/polling-fetch.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js\");\n/* harmony import */ var _transports_polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./transports/polling-xhr.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js\");\n/* harmony import */ var _transports_polling_xhr_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./transports/polling-xhr.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js\");\n/* harmony import */ var _transports_websocket_node_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./transports/websocket.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js\");\n/* harmony import */ var _transports_websocket_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./transports/websocket.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js\");\n/* harmony import */ var _transports_webtransport_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./transports/webtransport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js\");\n\n\n\nconst protocol = _socket_js__WEBPACK_IMPORTED_MODULE_0__.Socket.protocol;\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUNuQjtBQUNxRDtBQUNoRSxNQUFNRyxXQUFXSCw4Q0FBTUEsQ0FBQ0csUUFBUSxDQUFDO0FBQ21CO0FBQ1I7QUFDRDtBQUNKO0FBQ0Q7QUFDUztBQUNZO0FBQ2hCO0FBQ21CO0FBQ1Q7QUFDTSIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvaW5kZXguanM/OGE0MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTb2NrZXQgfSBmcm9tIFwiLi9zb2NrZXQuanNcIjtcbmV4cG9ydCB7IFNvY2tldCB9O1xuZXhwb3J0IHsgU29ja2V0V2l0aG91dFVwZ3JhZGUsIFNvY2tldFdpdGhVcGdyYWRlLCB9IGZyb20gXCIuL3NvY2tldC5qc1wiO1xuZXhwb3J0IGNvbnN0IHByb3RvY29sID0gU29ja2V0LnByb3RvY29sO1xuZXhwb3J0IHsgVHJhbnNwb3J0LCBUcmFuc3BvcnRFcnJvciB9IGZyb20gXCIuL3RyYW5zcG9ydC5qc1wiO1xuZXhwb3J0IHsgdHJhbnNwb3J0cyB9IGZyb20gXCIuL3RyYW5zcG9ydHMvaW5kZXguanNcIjtcbmV4cG9ydCB7IGluc3RhbGxUaW1lckZ1bmN0aW9ucyB9IGZyb20gXCIuL3V0aWwuanNcIjtcbmV4cG9ydCB7IHBhcnNlIH0gZnJvbSBcIi4vY29udHJpYi9wYXJzZXVyaS5qc1wiO1xuZXhwb3J0IHsgbmV4dFRpY2sgfSBmcm9tIFwiLi9nbG9iYWxzLm5vZGUuanNcIjtcbmV4cG9ydCB7IEZldGNoIH0gZnJvbSBcIi4vdHJhbnNwb3J0cy9wb2xsaW5nLWZldGNoLmpzXCI7XG5leHBvcnQgeyBYSFIgYXMgTm9kZVhIUiB9IGZyb20gXCIuL3RyYW5zcG9ydHMvcG9sbGluZy14aHIubm9kZS5qc1wiO1xuZXhwb3J0IHsgWEhSIH0gZnJvbSBcIi4vdHJhbnNwb3J0cy9wb2xsaW5nLXhoci5qc1wiO1xuZXhwb3J0IHsgV1MgYXMgTm9kZVdlYlNvY2tldCB9IGZyb20gXCIuL3RyYW5zcG9ydHMvd2Vic29ja2V0Lm5vZGUuanNcIjtcbmV4cG9ydCB7IFdTIGFzIFdlYlNvY2tldCB9IGZyb20gXCIuL3RyYW5zcG9ydHMvd2Vic29ja2V0LmpzXCI7XG5leHBvcnQgeyBXVCBhcyBXZWJUcmFuc3BvcnQgfSBmcm9tIFwiLi90cmFuc3BvcnRzL3dlYnRyYW5zcG9ydC5qc1wiO1xuIl0sIm5hbWVzIjpbIlNvY2tldCIsIlNvY2tldFdpdGhvdXRVcGdyYWRlIiwiU29ja2V0V2l0aFVwZ3JhZGUiLCJwcm90b2NvbCIsIlRyYW5zcG9ydCIsIlRyYW5zcG9ydEVycm9yIiwidHJhbnNwb3J0cyIsImluc3RhbGxUaW1lckZ1bmN0aW9ucyIsInBhcnNlIiwibmV4dFRpY2siLCJGZXRjaCIsIlhIUiIsIk5vZGVYSFIiLCJXUyIsIk5vZGVXZWJTb2NrZXQiLCJXZWJTb2NrZXQiLCJXVCIsIldlYlRyYW5zcG9ydCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/socket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/socket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Socket: () => (/* binding */ Socket),\n/* harmony export */   SocketWithUpgrade: () => (/* binding */ SocketWithUpgrade),\n/* harmony export */   SocketWithoutUpgrade: () => (/* binding */ SocketWithoutUpgrade)\n/* harmony export */ });\n/* harmony import */ var _transports_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transports/index.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./contrib/parseqs.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js\");\n/* harmony import */ var _contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contrib/parseuri.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseuri.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_7__(\"engine.io-client:socket\"); // debug()\nconst withEventListeners = typeof addEventListener === \"function\" && typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", ()=>{\n        debug(\"closing %d connection(s) because the network was lost\", OFFLINE_EVENT_LISTENERS.length);\n        OFFLINE_EVENT_LISTENERS.forEach((listener)=>listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */ class SocketWithoutUpgrade extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_4__.Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */ constructor(uri, opts){\n        super();\n        this.binaryType = _globals_node_js__WEBPACK_IMPORTED_MODULE_6__.defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */ this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = (0,_contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_3__.parse)(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure = parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query) opts.query = parsedUri.query;\n        } else if (opts.host) {\n            opts.hostname = (0,_contrib_parseuri_js__WEBPACK_IMPORTED_MODULE_3__.parse)(opts.host).host;\n        }\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.installTimerFunctions)(this, opts);\n        this.secure = null != opts.secure ? opts.secure : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname = opts.hostname || (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port = opts.port || (typeof location !== \"undefined\" && location.port ? location.port : this.secure ? \"443\" : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t)=>{\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false\n        }, opts);\n        this.opts.path = this.opts.path.replace(/\\/$/, \"\") + (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = (0,_contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_2__.decode)(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = ()=>{\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                debug(\"adding listener for the 'offline' event\");\n                this._offlineEventListener = ()=>{\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\"\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_6__.createCookieJar)();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */ createTransport(name) {\n        debug('creating transport \"%s\"', name);\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = engine_io_parser__WEBPACK_IMPORTED_MODULE_5__.protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id) query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port\n        }, this.opts.transportOptions[name]);\n        debug(\"options: %j\", opts);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */ _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(()=>{\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade && SocketWithoutUpgrade.priorWebsocketSuccess && this.transports.indexOf(\"websocket\") !== -1 ? \"websocket\" : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */ setTransport(transport) {\n        debug(\"setting transport %s\", transport.name);\n        if (this.transport) {\n            debug(\"clearing existing transport %s\", this.transport.name);\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport.on(\"drain\", this._onDrain.bind(this)).on(\"packet\", this._onPacket.bind(this)).on(\"error\", this._onError.bind(this)).on(\"close\", (reason)=>this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */ onOpen() {\n        debug(\"socket open\");\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */ _onPacket(packet) {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n            debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch(packet.type){\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        } else {\n            debug('packet received with socket readyState \"%s\"', this.readyState);\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */ onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState) return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */ _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(()=>{\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */ _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        } else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */ flush() {\n        if (\"closed\" !== this.readyState && this.transport.writable && !this.upgrading && this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            debug(\"flushing %d packets in socket\", packets.length);\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */ _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload && this.transport.name === \"polling\" && this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for(let i = 0; i < this.writeBuffer.length; i++){\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.byteLength)(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                debug(\"only send %d out of %d packets\", i, this.writeBuffer.length);\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        debug(\"payload size is %d (max: %d)\", payloadSize, this._maxPayload);\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */ /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime) return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            debug(\"throttled timer detected, scheduling connection close\");\n            this._pingTimeoutTime = 0;\n            (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_6__.nextTick)(()=>{\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */ write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */ send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */ _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn) this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */ close() {\n        const close = ()=>{\n            this._onClose(\"forced close\");\n            debug(\"socket closing - telling transport to close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = ()=>{\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = ()=>{\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", ()=>{\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    } else {\n                        close();\n                    }\n                });\n            } else if (this.upgrading) {\n                waitForUpgrade();\n            } else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */ _onError(err) {\n        debug(\"socket error %j\", err);\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports && this.transports.length > 1 && this.readyState === \"opening\") {\n            debug(\"trying next transport\");\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */ _onClose(reason, description) {\n        if (\"opening\" === this.readyState || \"open\" === this.readyState || \"closing\" === this.readyState) {\n            debug('socket close with reason: \"%s\"', reason);\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        debug(\"removing listener for the 'offline' event\");\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = engine_io_parser__WEBPACK_IMPORTED_MODULE_5__.protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */ class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor(){\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            debug(\"starting upgrade probes\");\n            for(let i = 0; i < this._upgrades.length; i++){\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */ _probe(name) {\n        debug('probing transport \"%s\"', name);\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = ()=>{\n            if (failed) return;\n            debug('probe transport \"%s\" opened', name);\n            transport.send([\n                {\n                    type: \"ping\",\n                    data: \"probe\"\n                }\n            ]);\n            transport.once(\"packet\", (msg)=>{\n                if (failed) return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    debug('probe transport \"%s\" pong', name);\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport) return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    debug('pausing current transport \"%s\"', this.transport.name);\n                    this.transport.pause(()=>{\n                        if (failed) return;\n                        if (\"closed\" === this.readyState) return;\n                        debug(\"changing transport and sending upgrade packet\");\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([\n                            {\n                                type: \"upgrade\"\n                            }\n                        ]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                } else {\n                    debug('probe transport \"%s\" failed', name);\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed) return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err)=>{\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            debug('probe transport \"%s\" failed because of error: %s', name, err);\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = ()=>{\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 && name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(()=>{\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        } else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */ _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for(let i = 0; i < upgrades.length; i++){\n            if (~this.transports.indexOf(upgrades[i])) filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */ class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}){\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports || o.transports && typeof o.transports[0] === \"string\") {\n            o.transports = (o.transports || [\n                \"polling\",\n                \"websocket\",\n                \"webtransport\"\n            ]).map((transportName)=>_transports_index_js__WEBPACK_IMPORTED_MODULE_0__.transports[transportName]).filter((t)=>!!t);\n        }\n        super(uri, o);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/socket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js":
/*!********************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transport.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Transport: () => (/* binding */ Transport),\n/* harmony export */   TransportError: () => (/* binding */ TransportError)\n/* harmony export */ });\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contrib/parseqs.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/parseqs.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_4__(\"engine.io-client:transport\"); // debug()\nclass TransportError extends Error {\n    constructor(reason, description, context){\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nclass Transport extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__.Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */ constructor(opts){\n        super();\n        this.writable = false;\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.installTimerFunctions)(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */ onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */ open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */ close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */ send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        } else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n            debug(\"transport is not open, discarding packets\");\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */ onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */ onData(data) {\n        const packet = (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_0__.decodePacket)(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */ onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */ onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */ pause(onPause) {}\n    createUri(schema, query = {}) {\n        return schema + \"://\" + this._hostname() + this._port() + this.opts.path + this._query(query);\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port && (this.opts.secure && Number(this.opts.port !== 443) || !this.opts.secure && Number(this.opts.port) !== 80)) {\n            return \":\" + this.opts.port;\n        } else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = (0,_contrib_parseqs_js__WEBPACK_IMPORTED_MODULE_3__.encode)(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transports: () => (/* binding */ transports)\n/* harmony export */ });\n/* harmony import */ var _polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polling-xhr.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js\");\n/* harmony import */ var _websocket_node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./websocket.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js\");\n/* harmony import */ var _webtransport_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webtransport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js\");\n\n\n\nconst transports = {\n    websocket: _websocket_node_js__WEBPACK_IMPORTED_MODULE_1__.WS,\n    webtransport: _webtransport_js__WEBPACK_IMPORTED_MODULE_2__.WT,\n    polling: _polling_xhr_node_js__WEBPACK_IMPORTED_MODULE_0__.XHR\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvdHJhbnNwb3J0cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTRDO0FBQ0g7QUFDRjtBQUNoQyxNQUFNRyxhQUFhO0lBQ3RCQyxXQUFXSCxrREFBRUE7SUFDYkksY0FBY0gsZ0RBQUVBO0lBQ2hCSSxTQUFTTixxREFBR0E7QUFDaEIsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvdHJhbnNwb3J0cy9pbmRleC5qcz9kMzEzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFhIUiB9IGZyb20gXCIuL3BvbGxpbmcteGhyLm5vZGUuanNcIjtcbmltcG9ydCB7IFdTIH0gZnJvbSBcIi4vd2Vic29ja2V0Lm5vZGUuanNcIjtcbmltcG9ydCB7IFdUIH0gZnJvbSBcIi4vd2VidHJhbnNwb3J0LmpzXCI7XG5leHBvcnQgY29uc3QgdHJhbnNwb3J0cyA9IHtcbiAgICB3ZWJzb2NrZXQ6IFdTLFxuICAgIHdlYnRyYW5zcG9ydDogV1QsXG4gICAgcG9sbGluZzogWEhSLFxufTtcbiJdLCJuYW1lcyI6WyJYSFIiLCJXUyIsIldUIiwidHJhbnNwb3J0cyIsIndlYnNvY2tldCIsIndlYnRyYW5zcG9ydCIsInBvbGxpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* binding */ Fetch)\n/* harmony export */ });\n/* harmony import */ var _polling_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polling.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js\");\n\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */ class Fetch extends _polling_js__WEBPACK_IMPORTED_MODULE_0__.Polling {\n    doPoll() {\n        this._fetch().then((res)=>{\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data)=>this.onData(data));\n        }).catch((err)=>{\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data).then((res)=>{\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        }).catch((err)=>{\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\"\n        }).then((res)=>{\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseXHR: () => (/* binding */ BaseXHR),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   XHR: () => (/* binding */ XHR)\n/* harmony export */ });\n/* harmony import */ var _polling_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polling.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var _contrib_has_cors_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contrib/has-cors.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/contrib/has-cors.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_5__(\"engine.io-client:polling\"); // debug()\nfunction empty() {}\nclass BaseXHR extends _polling_js__WEBPACK_IMPORTED_MODULE_0__.Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */ constructor(opts){\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd = typeof location !== \"undefined\" && opts.hostname !== location.hostname || port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */ doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context)=>{\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */ doPoll() {\n        debug(\"xhr poll\");\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context)=>{\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nclass Request extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_1__.Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */ constructor(createRequest, uri, opts){\n        super();\n        this.createRequest = createRequest;\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.installTimerFunctions)(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */ _create() {\n        var _a;\n        const opts = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.pick)(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = this._xhr = this.createRequest(opts);\n        try {\n            debug(\"xhr open %s: %s\", this._method, this._uri);\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for(let i in this._opts.extraHeaders){\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            } catch (e) {}\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                } catch (e) {}\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            } catch (e) {}\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = ()=>{\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(// @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState) return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                } else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(()=>{\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            debug(\"xhr data %s\", this._data);\n            xhr.send(this._data);\n        } catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(()=>{\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */ _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */ _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            } catch (e) {}\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */ _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */ abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */ if (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    } else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for(let i in Request.requests){\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = function() {\n    const xhr = newRequest({\n        xdomain: false\n    });\n    return xhr && xhr.responseType !== null;\n}();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */ class XHR extends BaseXHR {\n    constructor(opts){\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, {\n            xd: this.xd\n        }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || _contrib_has_cors_js__WEBPACK_IMPORTED_MODULE_4__.hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    } catch (e) {}\n    if (!xdomain) {\n        try {\n            return new _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim[[\n                \"Active\"\n            ].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        } catch (e) {}\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   XHR: () => (/* binding */ XHR)\n/* harmony export */ });\n/* harmony import */ var xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! xmlhttprequest-ssl */ \"(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\");\n/* harmony import */ var _polling_xhr_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./polling-xhr.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.js\");\n\n\nconst XMLHttpRequest = xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0__ || /*#__PURE__*/ (xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(xmlhttprequest_ssl__WEBPACK_IMPORTED_MODULE_0__, 2)));\n/**\n * HTTP long-polling based on the `XMLHttpRequest` object provided by the `xmlhttprequest-ssl` package.\n *\n * Usage: Node.js, Deno (compat), Bun (compat)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */ class XHR extends _polling_xhr_js__WEBPACK_IMPORTED_MODULE_1__.BaseXHR {\n    request(opts = {}) {\n        var _a;\n        Object.assign(opts, {\n            xd: this.xd,\n            cookieJar: (_a = this.socket) === null || _a === void 0 ? void 0 : _a._cookieJar\n        }, this.opts);\n        return new _polling_xhr_js__WEBPACK_IMPORTED_MODULE_1__.Request((opts)=>new XMLHttpRequest(opts), this.uri(), opts);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling-xhr.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/polling.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Polling: () => (/* binding */ Polling)\n/* harmony export */ });\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"engine.io-client:polling\"); // debug()\nclass Polling extends _transport_js__WEBPACK_IMPORTED_MODULE_0__.Transport {\n    constructor(){\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */ doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */ pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = ()=>{\n            debug(\"paused\");\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                debug(\"we are currently polling - waiting to pause\");\n                total++;\n                this.once(\"pollComplete\", function() {\n                    debug(\"pre-pause polling complete\");\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                debug(\"we are currently writing - waiting to pause\");\n                total++;\n                this.once(\"drain\", function() {\n                    debug(\"pre-pause writing complete\");\n                    --total || pause();\n                });\n            }\n        } else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */ _poll() {\n        debug(\"polling\");\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */ onData(data) {\n        debug(\"polling got data %s\", data);\n        const callback = (packet)=>{\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({\n                    description: \"transport closed by the server\"\n                });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.decodePayload)(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            } else {\n                debug('ignoring poll - transport state \"%s\"', this.readyState);\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */ doClose() {\n        const close = ()=>{\n            debug(\"writing close packet\");\n            this.write([\n                {\n                    type: \"close\"\n                }\n            ]);\n        };\n        if (\"open\" === this.readyState) {\n            debug(\"transport open - closing\");\n            close();\n        } else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            debug(\"transport not open - deferring close\");\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */ write(packets) {\n        this.writable = false;\n        (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.encodePayload)(packets, (data)=>{\n            this.doWrite(data, ()=>{\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */ uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.randomString)();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/polling.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/websocket.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseWS: () => (/* binding */ BaseWS),\n/* harmony export */   WS: () => (/* binding */ WS)\n/* harmony export */ });\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_4__(\"engine.io-client:websocket\"); // debug()\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" && typeof navigator.product === \"string\" && navigator.product.toLowerCase() === \"reactnative\";\nclass BaseWS extends _transport_js__WEBPACK_IMPORTED_MODULE_0__.Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative ? {} : (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.pick)(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        } catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */ addEventListeners() {\n        this.ws.onopen = ()=>{\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent)=>this.onClose({\n                description: \"websocket connection closed\",\n                context: closeEvent\n            });\n        this.ws.onmessage = (ev)=>this.onData(ev.data);\n        this.ws.onerror = (e)=>this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for(let i = 0; i < packets.length; i++){\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.encodePacket)(packet, this.supportsBinary, (data)=>{\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                } catch (e) {\n                    debug(\"websocket closed before onclose event\");\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_3__.nextTick)(()=>{\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = ()=>{};\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */ uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.randomString)();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim.WebSocket || _globals_node_js__WEBPACK_IMPORTED_MODULE_3__.globalThisShim.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */ class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative ? protocols ? new WebSocketCtor(uri, protocols) : new WebSocketCtor(uri) : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js":
/*!************************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WS: () => (/* binding */ WS)\n/* harmony export */ });\n/* harmony import */ var ws__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ws */ \"(ssr)/./node_modules/ws/wrapper.mjs\");\n/* harmony import */ var _websocket_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./websocket.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.js\");\n\n\n/**\n * WebSocket transport based on the `WebSocket` object provided by the `ws` package.\n *\n * Usage: Node.js, Deno (compat), Bun (compat)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n */ class WS extends _websocket_js__WEBPACK_IMPORTED_MODULE_1__.BaseWS {\n    createSocket(uri, protocols, opts) {\n        var _a;\n        if ((_a = this.socket) === null || _a === void 0 ? void 0 : _a._cookieJar) {\n            opts.headers = opts.headers || {};\n            opts.headers.cookie = typeof opts.headers.cookie === \"string\" ? [\n                opts.headers.cookie\n            ] : opts.headers.cookie || [];\n            for (const [name, cookie] of this.socket._cookieJar.cookies){\n                opts.headers.cookie.push(`${name}=${cookie.value}`);\n            }\n        }\n        return new ws__WEBPACK_IMPORTED_MODULE_0__.WebSocket(uri, protocols, opts);\n    }\n    doWrite(packet, data) {\n        const opts = {};\n        if (packet.options) {\n            opts.compress = packet.options.compress;\n        }\n        if (this.opts.perMessageDeflate) {\n            const len = // @ts-ignore\n            \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n                opts.compress = false;\n            }\n        }\n        this.ws.send(data, opts);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/websocket.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WT: () => (/* binding */ WT)\n/* harmony export */ });\n/* harmony import */ var _transport_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../transport.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/transport.js\");\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n/* harmony import */ var engine_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! engine.io-parser */ \"(ssr)/./node_modules/engine.io-parser/build/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"engine.io-client:webtransport\"); // debug()\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */ class WT extends _transport_js__WEBPACK_IMPORTED_MODULE_0__.Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        } catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed.then(()=>{\n            debug(\"transport closed gracefully\");\n            this.onClose();\n        }).catch((err)=>{\n            debug(\"transport closed due to %s\", err);\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(()=>{\n            this._transport.createBidirectionalStream().then((stream)=>{\n                const decoderStream = (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.createPacketDecoderStream)(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = (0,engine_io_parser__WEBPACK_IMPORTED_MODULE_2__.createPacketEncoderStream)();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = ()=>{\n                    reader.read().then(({ done, value })=>{\n                        if (done) {\n                            debug(\"session is closed\");\n                            return;\n                        }\n                        debug(\"received chunk: %o\", value);\n                        this.onPacket(value);\n                        read();\n                    }).catch((err)=>{\n                        debug(\"an error occurred while reading: %s\", err);\n                    });\n                };\n                read();\n                const packet = {\n                    type: \"open\"\n                };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(()=>this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for(let i = 0; i < packets.length; i++){\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(()=>{\n                if (lastPacket) {\n                    (0,_globals_node_js__WEBPACK_IMPORTED_MODULE_1__.nextTick)(()=>{\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZW5naW5lLmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvdHJhbnNwb3J0cy93ZWJ0cmFuc3BvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEM7QUFDRTtBQUMyQztBQUN6RCxDQUFDLFVBQVU7QUFDM0MsTUFBTUssUUFBUUQsa0NBQVdBLENBQUMsa0NBQWtDLFVBQVU7QUFDdEU7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU1FLFdBQVdOLG9EQUFTQTtJQUM3QixJQUFJTyxPQUFPO1FBQ1AsT0FBTztJQUNYO0lBQ0FDLFNBQVM7UUFDTCxJQUFJO1lBQ0EsYUFBYTtZQUNiLElBQUksQ0FBQ0MsVUFBVSxHQUFHLElBQUlDLGFBQWEsSUFBSSxDQUFDQyxTQUFTLENBQUMsVUFBVSxJQUFJLENBQUNDLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUMsSUFBSSxDQUFDTixJQUFJLENBQUM7UUFDckcsRUFDQSxPQUFPTyxLQUFLO1lBQ1IsT0FBTyxJQUFJLENBQUNDLFlBQVksQ0FBQyxTQUFTRDtRQUN0QztRQUNBLElBQUksQ0FBQ0wsVUFBVSxDQUFDTyxNQUFNLENBQ2pCQyxJQUFJLENBQUM7WUFDTlosTUFBTTtZQUNOLElBQUksQ0FBQ2EsT0FBTztRQUNoQixHQUNLQyxLQUFLLENBQUMsQ0FBQ0w7WUFDUlQsTUFBTSw4QkFBOEJTO1lBQ3BDLElBQUksQ0FBQ00sT0FBTyxDQUFDLHNCQUFzQk47UUFDdkM7UUFDQSx5RkFBeUY7UUFDekYsSUFBSSxDQUFDTCxVQUFVLENBQUNZLEtBQUssQ0FBQ0osSUFBSSxDQUFDO1lBQ3ZCLElBQUksQ0FBQ1IsVUFBVSxDQUFDYSx5QkFBeUIsR0FBR0wsSUFBSSxDQUFDLENBQUNNO2dCQUM5QyxNQUFNQyxnQkFBZ0J0QiwyRUFBeUJBLENBQUN1QixPQUFPQyxnQkFBZ0IsRUFBRSxJQUFJLENBQUNDLE1BQU0sQ0FBQ0MsVUFBVTtnQkFDL0YsTUFBTUMsU0FBU04sT0FBT08sUUFBUSxDQUFDQyxXQUFXLENBQUNQLGVBQWVRLFNBQVM7Z0JBQ25FLE1BQU1DLGdCQUFnQjlCLDJFQUF5QkE7Z0JBQy9DOEIsY0FBY0gsUUFBUSxDQUFDSSxNQUFNLENBQUNYLE9BQU9ZLFFBQVE7Z0JBQzdDLElBQUksQ0FBQ0MsT0FBTyxHQUFHSCxjQUFjRSxRQUFRLENBQUNFLFNBQVM7Z0JBQy9DLE1BQU1DLE9BQU87b0JBQ1RULE9BQ0tTLElBQUksR0FDSnJCLElBQUksQ0FBQyxDQUFDLEVBQUVzQixJQUFJLEVBQUVDLEtBQUssRUFBRTt3QkFDdEIsSUFBSUQsTUFBTTs0QkFDTmxDLE1BQU07NEJBQ047d0JBQ0o7d0JBQ0FBLE1BQU0sc0JBQXNCbUM7d0JBQzVCLElBQUksQ0FBQ0MsUUFBUSxDQUFDRDt3QkFDZEY7b0JBQ0osR0FDS25CLEtBQUssQ0FBQyxDQUFDTDt3QkFDUlQsTUFBTSx1Q0FBdUNTO29CQUNqRDtnQkFDSjtnQkFDQXdCO2dCQUNBLE1BQU1JLFNBQVM7b0JBQUVDLE1BQU07Z0JBQU87Z0JBQzlCLElBQUksSUFBSSxDQUFDQyxLQUFLLENBQUNDLEdBQUcsRUFBRTtvQkFDaEJILE9BQU9JLElBQUksR0FBRyxDQUFDLFFBQVEsRUFBRSxJQUFJLENBQUNGLEtBQUssQ0FBQ0MsR0FBRyxDQUFDLEVBQUUsQ0FBQztnQkFDL0M7Z0JBQ0EsSUFBSSxDQUFDVCxPQUFPLENBQUNXLEtBQUssQ0FBQ0wsUUFBUXpCLElBQUksQ0FBQyxJQUFNLElBQUksQ0FBQytCLE1BQU07WUFDckQ7UUFDSjtJQUNKO0lBQ0FELE1BQU1FLE9BQU8sRUFBRTtRQUNYLElBQUksQ0FBQ2QsUUFBUSxHQUFHO1FBQ2hCLElBQUssSUFBSWUsSUFBSSxHQUFHQSxJQUFJRCxRQUFRRSxNQUFNLEVBQUVELElBQUs7WUFDckMsTUFBTVIsU0FBU08sT0FBTyxDQUFDQyxFQUFFO1lBQ3pCLE1BQU1FLGFBQWFGLE1BQU1ELFFBQVFFLE1BQU0sR0FBRztZQUMxQyxJQUFJLENBQUNmLE9BQU8sQ0FBQ1csS0FBSyxDQUFDTCxRQUFRekIsSUFBSSxDQUFDO2dCQUM1QixJQUFJbUMsWUFBWTtvQkFDWm5ELDBEQUFRQSxDQUFDO3dCQUNMLElBQUksQ0FBQ2tDLFFBQVEsR0FBRzt3QkFDaEIsSUFBSSxDQUFDcEIsWUFBWSxDQUFDO29CQUN0QixHQUFHLElBQUksQ0FBQ3NDLFlBQVk7Z0JBQ3hCO1lBQ0o7UUFDSjtJQUNKO0lBQ0FDLFVBQVU7UUFDTixJQUFJQztRQUNIQSxDQUFBQSxLQUFLLElBQUksQ0FBQzlDLFVBQVUsTUFBTSxRQUFROEMsT0FBTyxLQUFLLElBQUksS0FBSyxJQUFJQSxHQUFHQyxLQUFLO0lBQ3hFO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL2VuZ2luZS5pby1jbGllbnQvYnVpbGQvZXNtLWRlYnVnL3RyYW5zcG9ydHMvd2VidHJhbnNwb3J0LmpzPzM1ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVHJhbnNwb3J0IH0gZnJvbSBcIi4uL3RyYW5zcG9ydC5qc1wiO1xuaW1wb3J0IHsgbmV4dFRpY2sgfSBmcm9tIFwiLi4vZ2xvYmFscy5ub2RlLmpzXCI7XG5pbXBvcnQgeyBjcmVhdGVQYWNrZXREZWNvZGVyU3RyZWFtLCBjcmVhdGVQYWNrZXRFbmNvZGVyU3RyZWFtLCB9IGZyb20gXCJlbmdpbmUuaW8tcGFyc2VyXCI7XG5pbXBvcnQgZGVidWdNb2R1bGUgZnJvbSBcImRlYnVnXCI7IC8vIGRlYnVnKClcbmNvbnN0IGRlYnVnID0gZGVidWdNb2R1bGUoXCJlbmdpbmUuaW8tY2xpZW50OndlYnRyYW5zcG9ydFwiKTsgLy8gZGVidWcoKVxuLyoqXG4gKiBXZWJUcmFuc3BvcnQgdHJhbnNwb3J0IGJhc2VkIG9uIHRoZSBidWlsdC1pbiBgV2ViVHJhbnNwb3J0YCBvYmplY3QuXG4gKlxuICogVXNhZ2U6IGJyb3dzZXIsIE5vZGUuanMgKHdpdGggdGhlIGBAZmFpbHMtY29tcG9uZW50cy93ZWJ0cmFuc3BvcnRgIHBhY2thZ2UpXG4gKlxuICogQHNlZSBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9BUEkvV2ViVHJhbnNwb3J0XG4gKiBAc2VlIGh0dHBzOi8vY2FuaXVzZS5jb20vd2VidHJhbnNwb3J0XG4gKi9cbmV4cG9ydCBjbGFzcyBXVCBleHRlbmRzIFRyYW5zcG9ydCB7XG4gICAgZ2V0IG5hbWUoKSB7XG4gICAgICAgIHJldHVybiBcIndlYnRyYW5zcG9ydFwiO1xuICAgIH1cbiAgICBkb09wZW4oKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlXG4gICAgICAgICAgICB0aGlzLl90cmFuc3BvcnQgPSBuZXcgV2ViVHJhbnNwb3J0KHRoaXMuY3JlYXRlVXJpKFwiaHR0cHNcIiksIHRoaXMub3B0cy50cmFuc3BvcnRPcHRpb25zW3RoaXMubmFtZV0pO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmVtaXRSZXNlcnZlZChcImVycm9yXCIsIGVycik7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fdHJhbnNwb3J0LmNsb3NlZFxuICAgICAgICAgICAgLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgZGVidWcoXCJ0cmFuc3BvcnQgY2xvc2VkIGdyYWNlZnVsbHlcIik7XG4gICAgICAgICAgICB0aGlzLm9uQ2xvc2UoKTtcbiAgICAgICAgfSlcbiAgICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7XG4gICAgICAgICAgICBkZWJ1ZyhcInRyYW5zcG9ydCBjbG9zZWQgZHVlIHRvICVzXCIsIGVycik7XG4gICAgICAgICAgICB0aGlzLm9uRXJyb3IoXCJ3ZWJ0cmFuc3BvcnQgZXJyb3JcIiwgZXJyKTtcbiAgICAgICAgfSk7XG4gICAgICAgIC8vIG5vdGU6IHdlIGNvdWxkIGhhdmUgdXNlZCBhc3luYy9hd2FpdCwgYnV0IHRoYXQgd291bGQgcmVxdWlyZSBzb21lIGFkZGl0aW9uYWwgcG9seWZpbGxzXG4gICAgICAgIHRoaXMuX3RyYW5zcG9ydC5yZWFkeS50aGVuKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuX3RyYW5zcG9ydC5jcmVhdGVCaWRpcmVjdGlvbmFsU3RyZWFtKCkudGhlbigoc3RyZWFtKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGVjb2RlclN0cmVhbSA9IGNyZWF0ZVBhY2tldERlY29kZXJTdHJlYW0oTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIsIHRoaXMuc29ja2V0LmJpbmFyeVR5cGUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJlYWRlciA9IHN0cmVhbS5yZWFkYWJsZS5waXBlVGhyb3VnaChkZWNvZGVyU3RyZWFtKS5nZXRSZWFkZXIoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBlbmNvZGVyU3RyZWFtID0gY3JlYXRlUGFja2V0RW5jb2RlclN0cmVhbSgpO1xuICAgICAgICAgICAgICAgIGVuY29kZXJTdHJlYW0ucmVhZGFibGUucGlwZVRvKHN0cmVhbS53cml0YWJsZSk7XG4gICAgICAgICAgICAgICAgdGhpcy5fd3JpdGVyID0gZW5jb2RlclN0cmVhbS53cml0YWJsZS5nZXRXcml0ZXIoKTtcbiAgICAgICAgICAgICAgICBjb25zdCByZWFkID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZWFkZXJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5yZWFkKClcbiAgICAgICAgICAgICAgICAgICAgICAgIC50aGVuKCh7IGRvbmUsIHZhbHVlIH0pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChkb25lKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVidWcoXCJzZXNzaW9uIGlzIGNsb3NlZFwiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWJ1ZyhcInJlY2VpdmVkIGNodW5rOiAlb1wiLCB2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLm9uUGFja2V0KHZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlYWQoKTtcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgICAgIC5jYXRjaCgoZXJyKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWJ1ZyhcImFuIGVycm9yIG9jY3VycmVkIHdoaWxlIHJlYWRpbmc6ICVzXCIsIGVycik7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgcmVhZCgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHBhY2tldCA9IHsgdHlwZTogXCJvcGVuXCIgfTtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5xdWVyeS5zaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgcGFja2V0LmRhdGEgPSBge1wic2lkXCI6XCIke3RoaXMucXVlcnkuc2lkfVwifWA7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRoaXMuX3dyaXRlci53cml0ZShwYWNrZXQpLnRoZW4oKCkgPT4gdGhpcy5vbk9wZW4oKSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHdyaXRlKHBhY2tldHMpIHtcbiAgICAgICAgdGhpcy53cml0YWJsZSA9IGZhbHNlO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhY2tldHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IHBhY2tldCA9IHBhY2tldHNbaV07XG4gICAgICAgICAgICBjb25zdCBsYXN0UGFja2V0ID0gaSA9PT0gcGFja2V0cy5sZW5ndGggLSAxO1xuICAgICAgICAgICAgdGhpcy5fd3JpdGVyLndyaXRlKHBhY2tldCkudGhlbigoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGxhc3RQYWNrZXQpIHtcbiAgICAgICAgICAgICAgICAgICAgbmV4dFRpY2soKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy53cml0YWJsZSA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcImRyYWluXCIpO1xuICAgICAgICAgICAgICAgICAgICB9LCB0aGlzLnNldFRpbWVvdXRGbik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZG9DbG9zZSgpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICAoX2EgPSB0aGlzLl90cmFuc3BvcnQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jbG9zZSgpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6WyJUcmFuc3BvcnQiLCJuZXh0VGljayIsImNyZWF0ZVBhY2tldERlY29kZXJTdHJlYW0iLCJjcmVhdGVQYWNrZXRFbmNvZGVyU3RyZWFtIiwiZGVidWdNb2R1bGUiLCJkZWJ1ZyIsIldUIiwibmFtZSIsImRvT3BlbiIsIl90cmFuc3BvcnQiLCJXZWJUcmFuc3BvcnQiLCJjcmVhdGVVcmkiLCJvcHRzIiwidHJhbnNwb3J0T3B0aW9ucyIsImVyciIsImVtaXRSZXNlcnZlZCIsImNsb3NlZCIsInRoZW4iLCJvbkNsb3NlIiwiY2F0Y2giLCJvbkVycm9yIiwicmVhZHkiLCJjcmVhdGVCaWRpcmVjdGlvbmFsU3RyZWFtIiwic3RyZWFtIiwiZGVjb2RlclN0cmVhbSIsIk51bWJlciIsIk1BWF9TQUZFX0lOVEVHRVIiLCJzb2NrZXQiLCJiaW5hcnlUeXBlIiwicmVhZGVyIiwicmVhZGFibGUiLCJwaXBlVGhyb3VnaCIsImdldFJlYWRlciIsImVuY29kZXJTdHJlYW0iLCJwaXBlVG8iLCJ3cml0YWJsZSIsIl93cml0ZXIiLCJnZXRXcml0ZXIiLCJyZWFkIiwiZG9uZSIsInZhbHVlIiwib25QYWNrZXQiLCJwYWNrZXQiLCJ0eXBlIiwicXVlcnkiLCJzaWQiLCJkYXRhIiwid3JpdGUiLCJvbk9wZW4iLCJwYWNrZXRzIiwiaSIsImxlbmd0aCIsImxhc3RQYWNrZXQiLCJzZXRUaW1lb3V0Rm4iLCJkb0Nsb3NlIiwiX2EiLCJjbG9zZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/transports/webtransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js":
/*!***************************************************************!*\
  !*** ./node_modules/engine.io-client/build/esm-debug/util.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   byteLength: () => (/* binding */ byteLength),\n/* harmony export */   installTimerFunctions: () => (/* binding */ installTimerFunctions),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   randomString: () => (/* binding */ randomString)\n/* harmony export */ });\n/* harmony import */ var _globals_node_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./globals.node.js */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/globals.node.js\");\n\nfunction pick(obj, ...attr) {\n    return attr.reduce((acc, k)=>{\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.clearTimeout;\nfunction installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n    } else {\n        obj.setTimeoutFn = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.setTimeout.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n        obj.clearTimeoutFn = _globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim.clearTimeout.bind(_globals_node_js__WEBPACK_IMPORTED_MODULE_0__.globalThisShim);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nfunction byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for(let i = 0, l = str.length; i < l; i++){\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        } else if (c < 0x800) {\n            length += 2;\n        } else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        } else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */ function randomString() {\n    return Date.now().toString(36).substring(3) + Math.random().toString(36).substring(2, 5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/engine.io-client/build/esm-debug/util.js\n");

/***/ })

};
;