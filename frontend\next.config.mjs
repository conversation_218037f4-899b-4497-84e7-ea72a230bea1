import createNextIntlPlugin from "next-intl/plugin";

// Create a proper next-intl configuration
const withNextIntl = createNextIntlPlugin('./i18n.js');

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    nextScriptWorkers: true,
  },
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
        ],
      }
    ];
  },
};

export default withNextIntl(nextConfig);
