"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/universal-cookie";
exports.ids = ["vendor-chunks/universal-cookie"];
exports.modules = {

/***/ "(ssr)/./node_modules/universal-cookie/esm/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/universal-cookie/esm/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cookies)\n/* harmony export */ });\nvar cookie = {};\n/*!\n * cookie\n * Copyright(c) 2012-2014 Roman Shtylman\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */ var hasRequiredCookie;\nfunction requireCookie() {\n    if (hasRequiredCookie) return cookie;\n    hasRequiredCookie = 1;\n    /**\n\t * Module exports.\n\t * @public\n\t */ cookie.parse = parse;\n    cookie.serialize = serialize;\n    /**\n\t * Module variables.\n\t * @private\n\t */ var __toString = Object.prototype.toString;\n    var __hasOwnProperty = Object.prototype.hasOwnProperty;\n    /**\n\t * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n\t * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n\t * which has been replaced by the token definition in RFC 7230 appendix B.\n\t *\n\t * cookie-name       = token\n\t * token             = 1*tchar\n\t * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n\t *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n\t *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n\t */ var cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n    /**\n\t * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n\t *\n\t * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n\t * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n\t *                     ; US-ASCII characters excluding CTLs,\n\t *                     ; whitespace DQUOTE, comma, semicolon,\n\t *                     ; and backslash\n\t */ var cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n    /**\n\t * RegExp to match domain-value in RFC 6265 sec 4.1.1\n\t *\n\t * domain-value      = <subdomain>\n\t *                     ; defined in [RFC1034], Section 3.5, as\n\t *                     ; enhanced by [RFC1123], Section 2.1\n\t * <subdomain>       = <label> | <subdomain> \".\" <label>\n\t * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n\t *                     Labels must be 63 characters or less.\n\t *                     'let-dig' not 'letter' in the first char, per RFC1123\n\t * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n\t * <let-dig-hyp>     = <let-dig> | \"-\"\n\t * <let-dig>         = <letter> | <digit>\n\t * <letter>          = any one of the 52 alphabetic characters A through Z in\n\t *                     upper case and a through z in lower case\n\t * <digit>           = any one of the ten digits 0 through 9\n\t *\n\t * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n\t *\n\t * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n\t * character is not permitted, but a trailing %x2E (\".\"), if present, will\n\t * cause the user agent to ignore the attribute.)\n\t */ var domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n    /**\n\t * RegExp to match path-value in RFC 6265 sec 4.1.1\n\t *\n\t * path-value        = <any CHAR except CTLs or \";\">\n\t * CHAR              = %x01-7F\n\t *                     ; defined in RFC 5234 appendix B.1\n\t */ var pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n    /**\n\t * Parse a cookie header.\n\t *\n\t * Parse the given cookie header string into an object\n\t * The object has the various cookies as keys(names) => values\n\t *\n\t * @param {string} str\n\t * @param {object} [opt]\n\t * @return {object}\n\t * @public\n\t */ function parse(str, opt) {\n        if (typeof str !== \"string\") {\n            throw new TypeError(\"argument str must be a string\");\n        }\n        var obj = {};\n        var len = str.length;\n        // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n        if (len < 2) return obj;\n        var dec = opt && opt.decode || decode;\n        var index = 0;\n        var eqIdx = 0;\n        var endIdx = 0;\n        do {\n            eqIdx = str.indexOf(\"=\", index);\n            if (eqIdx === -1) break; // No more cookie pairs.\n            endIdx = str.indexOf(\";\", index);\n            if (endIdx === -1) {\n                endIdx = len;\n            } else if (eqIdx > endIdx) {\n                // backtrack on prior semicolon\n                index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n                continue;\n            }\n            var keyStartIdx = startIndex(str, index, eqIdx);\n            var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n            var key = str.slice(keyStartIdx, keyEndIdx);\n            // only assign once\n            if (!__hasOwnProperty.call(obj, key)) {\n                var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n                var valEndIdx = endIndex(str, endIdx, valStartIdx);\n                if (str.charCodeAt(valStartIdx) === 0x22 /* \" */  && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */ ) {\n                    valStartIdx++;\n                    valEndIdx--;\n                }\n                var val = str.slice(valStartIdx, valEndIdx);\n                obj[key] = tryDecode(val, dec);\n            }\n            index = endIdx + 1;\n        }while (index < len);\n        return obj;\n    }\n    function startIndex(str, index, max) {\n        do {\n            var code = str.charCodeAt(index);\n            if (code !== 0x20 /*   */  && code !== 0x09 /* \\t */ ) return index;\n        }while (++index < max);\n        return max;\n    }\n    function endIndex(str, index, min) {\n        while(index > min){\n            var code = str.charCodeAt(--index);\n            if (code !== 0x20 /*   */  && code !== 0x09 /* \\t */ ) return index + 1;\n        }\n        return min;\n    }\n    /**\n\t * Serialize data into a cookie header.\n\t *\n\t * Serialize a name value pair into a cookie string suitable for\n\t * http headers. An optional options object specifies cookie parameters.\n\t *\n\t * serialize('foo', 'bar', { httpOnly: true })\n\t *   => \"foo=bar; httpOnly\"\n\t *\n\t * @param {string} name\n\t * @param {string} val\n\t * @param {object} [opt]\n\t * @return {string}\n\t * @public\n\t */ function serialize(name, val, opt) {\n        var enc = opt && opt.encode || encodeURIComponent;\n        if (typeof enc !== \"function\") {\n            throw new TypeError(\"option encode is invalid\");\n        }\n        if (!cookieNameRegExp.test(name)) {\n            throw new TypeError(\"argument name is invalid\");\n        }\n        var value = enc(val);\n        if (!cookieValueRegExp.test(value)) {\n            throw new TypeError(\"argument val is invalid\");\n        }\n        var str = name + \"=\" + value;\n        if (!opt) return str;\n        if (null != opt.maxAge) {\n            var maxAge = Math.floor(opt.maxAge);\n            if (!isFinite(maxAge)) {\n                throw new TypeError(\"option maxAge is invalid\");\n            }\n            str += \"; Max-Age=\" + maxAge;\n        }\n        if (opt.domain) {\n            if (!domainValueRegExp.test(opt.domain)) {\n                throw new TypeError(\"option domain is invalid\");\n            }\n            str += \"; Domain=\" + opt.domain;\n        }\n        if (opt.path) {\n            if (!pathValueRegExp.test(opt.path)) {\n                throw new TypeError(\"option path is invalid\");\n            }\n            str += \"; Path=\" + opt.path;\n        }\n        if (opt.expires) {\n            var expires = opt.expires;\n            if (!isDate(expires) || isNaN(expires.valueOf())) {\n                throw new TypeError(\"option expires is invalid\");\n            }\n            str += \"; Expires=\" + expires.toUTCString();\n        }\n        if (opt.httpOnly) {\n            str += \"; HttpOnly\";\n        }\n        if (opt.secure) {\n            str += \"; Secure\";\n        }\n        if (opt.partitioned) {\n            str += \"; Partitioned\";\n        }\n        if (opt.priority) {\n            var priority = typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n            switch(priority){\n                case \"low\":\n                    str += \"; Priority=Low\";\n                    break;\n                case \"medium\":\n                    str += \"; Priority=Medium\";\n                    break;\n                case \"high\":\n                    str += \"; Priority=High\";\n                    break;\n                default:\n                    throw new TypeError(\"option priority is invalid\");\n            }\n        }\n        if (opt.sameSite) {\n            var sameSite = typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n            switch(sameSite){\n                case true:\n                    str += \"; SameSite=Strict\";\n                    break;\n                case \"lax\":\n                    str += \"; SameSite=Lax\";\n                    break;\n                case \"strict\":\n                    str += \"; SameSite=Strict\";\n                    break;\n                case \"none\":\n                    str += \"; SameSite=None\";\n                    break;\n                default:\n                    throw new TypeError(\"option sameSite is invalid\");\n            }\n        }\n        return str;\n    }\n    /**\n\t * URL-decode string value. Optimized to skip native call when no %.\n\t *\n\t * @param {string} str\n\t * @returns {string}\n\t */ function decode(str) {\n        return str.indexOf(\"%\") !== -1 ? decodeURIComponent(str) : str;\n    }\n    /**\n\t * Determine if value is a Date.\n\t *\n\t * @param {*} val\n\t * @private\n\t */ function isDate(val) {\n        return __toString.call(val) === \"[object Date]\";\n    }\n    /**\n\t * Try decoding a string using a decoding function.\n\t *\n\t * @param {string} str\n\t * @param {function} decode\n\t * @private\n\t */ function tryDecode(str, decode) {\n        try {\n            return decode(str);\n        } catch (e) {\n            return str;\n        }\n    }\n    return cookie;\n}\nvar cookieExports = requireCookie();\nfunction hasDocumentCookie() {\n    const testingValue = typeof global === \"undefined\" ? undefined : global.TEST_HAS_DOCUMENT_COOKIE;\n    if (typeof testingValue === \"boolean\") {\n        return testingValue;\n    }\n    // Can we get/set cookies on document.cookie?\n    return typeof document === \"object\" && typeof document.cookie === \"string\";\n}\nfunction parseCookies(cookies) {\n    if (typeof cookies === \"string\") {\n        return cookieExports.parse(cookies);\n    } else if (typeof cookies === \"object\" && cookies !== null) {\n        return cookies;\n    } else {\n        return {};\n    }\n}\nfunction readCookie(value, options = {}) {\n    const cleanValue = cleanupCookieValue(value);\n    if (!options.doNotParse) {\n        try {\n            return JSON.parse(cleanValue);\n        } catch (e) {\n        // At least we tried\n        }\n    }\n    // Ignore clean value if we failed the deserialization\n    // It is not relevant anymore to trim those values\n    return value;\n}\nfunction cleanupCookieValue(value) {\n    // express prepend j: before serializing a cookie\n    if (value && value[0] === \"j\" && value[1] === \":\") {\n        return value.substr(2);\n    }\n    return value;\n}\nclass Cookies {\n    constructor(cookies, defaultSetOptions = {}){\n        this.changeListeners = [];\n        this.HAS_DOCUMENT_COOKIE = false;\n        this.update = ()=>{\n            if (!this.HAS_DOCUMENT_COOKIE) {\n                return;\n            }\n            const previousCookies = this.cookies;\n            this.cookies = cookieExports.parse(document.cookie);\n            this._checkChanges(previousCookies);\n        };\n        const domCookies = typeof document === \"undefined\" ? \"\" : document.cookie;\n        this.cookies = parseCookies(cookies || domCookies);\n        this.defaultSetOptions = defaultSetOptions;\n        this.HAS_DOCUMENT_COOKIE = hasDocumentCookie();\n    }\n    _emitChange(params) {\n        for(let i = 0; i < this.changeListeners.length; ++i){\n            this.changeListeners[i](params);\n        }\n    }\n    _checkChanges(previousCookies) {\n        const names = new Set(Object.keys(previousCookies).concat(Object.keys(this.cookies)));\n        names.forEach((name)=>{\n            if (previousCookies[name] !== this.cookies[name]) {\n                this._emitChange({\n                    name,\n                    value: readCookie(this.cookies[name])\n                });\n            }\n        });\n    }\n    _startPolling() {\n        this.pollingInterval = setInterval(this.update, 300);\n    }\n    _stopPolling() {\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n        }\n    }\n    get(name, options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        return readCookie(this.cookies[name], options);\n    }\n    getAll(options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        const result = {};\n        for(let name in this.cookies){\n            result[name] = readCookie(this.cookies[name], options);\n        }\n        return result;\n    }\n    set(name, value, options) {\n        if (options) {\n            options = Object.assign(Object.assign({}, this.defaultSetOptions), options);\n        } else {\n            options = this.defaultSetOptions;\n        }\n        const stringValue = typeof value === \"string\" ? value : JSON.stringify(value);\n        this.cookies = Object.assign(Object.assign({}, this.cookies), {\n            [name]: stringValue\n        });\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = cookieExports.serialize(name, stringValue, options);\n        }\n        this._emitChange({\n            name,\n            value,\n            options\n        });\n    }\n    remove(name, options) {\n        const finalOptions = options = Object.assign(Object.assign(Object.assign({}, this.defaultSetOptions), options), {\n            expires: new Date(1970, 1, 1, 0, 0, 1),\n            maxAge: 0\n        });\n        this.cookies = Object.assign({}, this.cookies);\n        delete this.cookies[name];\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = cookieExports.serialize(name, \"\", finalOptions);\n        }\n        this._emitChange({\n            name,\n            value: undefined,\n            options\n        });\n    }\n    addChangeListener(callback) {\n        this.changeListeners.push(callback);\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 1) {\n            if (false) {} else {\n                this._startPolling();\n            }\n        }\n    }\n    removeChangeListener(callback) {\n        const idx = this.changeListeners.indexOf(callback);\n        if (idx >= 0) {\n            this.changeListeners.splice(idx, 1);\n        }\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 0) {\n            if (false) {} else {\n                this._stopPolling();\n            }\n        }\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/universal-cookie/esm/index.mjs\n");

/***/ })

};
;