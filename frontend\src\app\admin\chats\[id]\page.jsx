'use client';
import React, { useState, useEffect, useRef } from 'react';
import { adminService } from '../../../services/adminService';
import { Typo<PERSON>, Card, CardBody, Button, Spinner } from '@material-tailwind/react';
import { MessageSquare, ArrowLeft, Clock, User, Download } from 'lucide-react';
import { toast } from 'react-toastify';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import './chat.css';

export default function ChatConversationDetail({ params }) {
  const { id } = params;
  const [conversation, setConversation] = useState(null);
  const [loading, setLoading] = useState(true);
  const messagesEndRef = useRef(null);
  const router = useRouter();

  useEffect(() => {
    const fetchConversation = async () => {
      try {
        setLoading(true);
        const response = await adminService.getChatConversation(id);
        setConversation(response.data);
      } catch (error) {
        toast.error('Failed to fetch conversation details');
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversation();
  }, [id]);

  useEffect(() => {
    // Scroll to bottom of messages
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversation]);

  const handleBack = () => {
    router.push('/admin/chats');
  };

  const exportConversation = () => {
    if (!conversation) return;

    const messages = conversation.messages.map(msg =>
      `${msg.role.toUpperCase()} (${new Date(msg.timestamp).toLocaleString()}): ${msg.content}`
    ).join('\n\n');

    const content = `Chat Session: ${conversation.sessionId}\n` +
      `Created: ${new Date(conversation.createdAt).toLocaleString()}\n` +
      `Last Activity: ${new Date(conversation.lastActivity).toLocaleString()}\n\n` +
      `MESSAGES:\n\n${messages}`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${conversation.sessionId}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner className="h-12 w-12" color="blue" />
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="text-center py-12">
        <Typography variant="h5" color="gray">
          Conversation not found
        </Typography>
        <Button color="blue" className="mt-4" onClick={handleBack}>
          Back to Conversations
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <Button variant="text" color="blue" className="p-2" onClick={handleBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <Typography variant="h4" color="blue-gray" className="font-bold">
            Chat Conversation
          </Typography>
        </div>

        <Button
          variant="outlined"
          color="blue"
          className="flex items-center gap-2"
          onClick={exportConversation}
        >
          <Download className="h-4 w-4" />
          Export Chat
        </Button>
      </div>

      <Card className="bg-white shadow-lg rounded-lg">
        <CardBody>
          {/* Conversation Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <Typography variant="small" className="font-medium text-gray-700">
                Session ID:
              </Typography>
              <Typography className="text-gray-600">
                {conversation.sessionId}
              </Typography>
            </div>
            <div>
              <Typography variant="small" className="font-medium text-gray-700">
                Created:
              </Typography>
              <Typography className="text-gray-600">
                {new Date(conversation.createdAt).toLocaleString()}
              </Typography>
            </div>
            <div>
              <Typography variant="small" className="font-medium text-gray-700">
                Last Activity:
              </Typography>
              <Typography className="text-gray-600">
                {new Date(conversation.lastActivity).toLocaleString()}
              </Typography>
            </div>
            <div>
              <Typography variant="small" className="font-medium text-gray-700">
                Messages:
              </Typography>
              <Typography className="text-gray-600">
                {conversation.messages.length}
              </Typography>
            </div>

            {/* User Information */}
            {conversation.userInfo && (
              <>
                <div className="col-span-1 md:col-span-2 border-t border-gray-200 pt-3 mt-2">
                  <Typography variant="small" className="font-medium text-gray-700 mb-2">
                    User Information:
                  </Typography>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {conversation.userInfo.userId ? (
                      <>
                        <div>
                          <Typography variant="small" className="font-medium text-gray-700">
                            User:
                          </Typography>
                          <Typography className="text-gray-600">
                            {conversation.userInfo.userId.firstName} {conversation.userInfo.userId.lastName || ''}
                          </Typography>
                        </div>
                        <div>
                          <Typography variant="small" className="font-medium text-gray-700">
                            Email:
                          </Typography>
                          <Typography className="text-gray-600">
                            {conversation.userInfo.userId.email}
                          </Typography>
                        </div>
                        <div>
                          <Typography variant="small" className="font-medium text-gray-700">
                            User ID:
                          </Typography>
                          <Typography className="text-gray-600">
                            {conversation.userInfo.userId.identifiant || conversation.userInfo.userId._id}
                          </Typography>

                        </div>
                      </>
                    ) : (
                      <div>
                        <Typography variant="small" className="font-medium text-gray-700">
                          User:
                        </Typography>
                        <Typography className="text-gray-600">
                          Guest User
                        </Typography>
                      </div>
                    )}

                    <div>
                      <Typography variant="small" className="font-medium text-gray-700">
                        IP Address:
                      </Typography>
                      <Typography className="text-gray-600">
                        {conversation.userInfo.ip || 'Not available'}
                      </Typography>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>

          {/* Chat Messages */}
          <div className="border rounded-lg p-4 bg-gray-50 h-[500px] overflow-y-auto">
            <div className="flex flex-col gap-4">
              {conversation.messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-3 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-blue-500 text-white rounded-br-none'
                        : message.role === 'assistant'
                        ? 'bg-white border border-gray-200 rounded-tl-none'
                        : 'bg-gray-200 text-gray-700 w-full'
                    }`}
                  >
                    {message.role === 'system' && (
                      <div className="font-medium mb-1 text-xs uppercase tracking-wide">
                        System Message
                      </div>
                    )}
                    <div
                      className={message.role === 'assistant' ? 'chat-message-content' : ''}
                      dangerouslySetInnerHTML={{ __html: message.content }}
                    />
                    <div className="text-xs mt-1 opacity-70">
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
