import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const WhoTrustUs = ({ t }) => {
  const partners = [
    { src: "/images/home/<USER>/Figure.png" },
    { src: "/images/home/<USER>/Figure-1.png" },
    { src: "/images/home/<USER>/Figure-2.png" },
    { src: "/images/home/<USER>/Figure-3.png" },
    { src: "/images/home/<USER>/Figure-4.png" },
    { src: "/images/home/<USER>/Figure-5.png" },
    { src: "/images/home/<USER>/Figure-6.png" },
    { src: "/images/home/<USER>/Figure-7.png" },
    { src: "/images/home/<USER>/Figure-8.png" },
    { src: "/images/home/<USER>/Figure-9.png" },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 5000, // Slightly slower for better visibility
    slidesToShow: 5,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 0,
    cssEase: "linear",
    pauseOnHover: true,
    arrows: false,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };

  return (
    <div className="mx-auto px-4 py-8 my-0 max-w-[1400px]">
      <div className="w-full my-auto flex flex-col gap-12">
        <div className="text-center">
          <Typography
            variant="h3"
            className="text-primary text-xl md:text-3xl xl:text-4xl font-inter font-bold"
          >
            {t("trusted_by_us")}
          </Typography>
        </div>

        <div className="w-full overflow-hidden">
          <Slider {...settings}>
            {partners.map((partner, index) => (
              <div key={index} className="px-4">
                <div
                  className="bg-white rounded-xl transition-all duration-300 
                    hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)] 
                    hover:scale-103
                    h-[120px] 
                    flex items-center justify-center
                    border border-gray-300"
                >
                  <div className="relative w-full h-full flex items-center justify-center">
                    <Image
                      src={partner.src}
                      alt={`Partner ${index + 1}`}
                      fill
                      className="object-contain p-0 transition-all duration-300
                          hover:brightness-110 hover:contrast-110 hover:cursor-pointer"
                      quality={100}
                    />
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>

      <style jsx global>{`
        .slick-track {
          display: flex !important;
          align-items: center !important;
        }

        .slick-slide {
          height: auto !important;
          padding: 10px 0;
        }

        .slick-list {
          margin: 0 -16px;
          overflow: visible;
        }

        @media (max-width: 768px) {
          .slick-list {
            margin: 0 -8px;
            overflow: hidden;
          }
        }
      `}</style>
    </div>
  );
};
export default WhoTrustUs;
