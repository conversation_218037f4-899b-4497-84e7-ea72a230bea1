const VPSInstance = require("../models/VPSInstance");
const NotificationSetting = require("../models/NotificationSetting");
const { sendSimpleEmail } = require("../routes/sendEmail/sendEmail");
const Notification = require("../models/Notification");
const cron = require("node-cron");

class VPSBillingNotificationService {
  constructor() {
    this.cronJobs = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the VPS billing notification service
   */
  async initialize() {
    try {
      console.log("🔔 Initializing VPS Billing Notification Service...");

      // Get VPS billing notification settings
      const settings = await this.getSettings();

      if (settings && settings.enabled) {
        await this.setupCronJobs(settings);
      }

      this.isInitialized = true;
      console.log(
        "✅ VPS Billing Notification Service initialized successfully"
      );
    } catch (error) {
      console.error(
        "❌ Failed to initialize VPS Billing Notification Service:",
        error
      );
    }
  }

  /**
   * Get VPS billing notification settings
   */
  async getSettings() {
    try {
      const settings = await NotificationSetting.findOne({
        type: "vps_billing",
      });

      if (!settings) {
        // Create default settings if none exist
        const defaultSettings = await NotificationSetting.create({
          type: "vps_billing",
          enabled: false,
          daysBefore: [7, 3, 1],
          methods: {
            email: true,
            inApp: true,
          },
          schedule: {
            frequency: "daily",
          },
        });
        return defaultSettings;
      }

      return settings;
    } catch (error) {
      console.error("❌ Error getting VPS billing settings:", error);
      return null;
    }
  }

  /**
   * Setup cron jobs based on settings
   */
  async setupCronJobs(settings) {
    try {
      // Clear existing cron jobs
      this.clearCronJobs();

      let cronExpression;

      switch (settings.schedule?.frequency) {
        case "hourly":
          cronExpression = "0 * * * *"; // Every hour
          break;
        case "twice-daily":
          // Run at 9:00 AM and 6:00 PM
          cronExpression = "0 9,18 * * *";
          break;
        case "daily":
        default:
          cronExpression = "0 9 * * *"; // Daily at 9:00 AM
          break;
      }

      console.log(
        `🕐 Setting up VPS billing cron job with expression: ${cronExpression}`
      );

      const cronJob = cron.schedule(
        cronExpression,
        async () => {
          await this.checkAndSendBillingNotifications();
        },
        {
          scheduled: true,
          timezone: "UTC",
        }
      );

      this.cronJobs.set("vps_billing", cronJob);
      console.log("✅ VPS billing cron job scheduled successfully");
    } catch (error) {
      console.error("❌ Error setting up VPS billing cron jobs:", error);
    }
  }

  /**
   * Clear all existing cron jobs
   */
  clearCronJobs() {
    this.cronJobs.forEach((job, key) => {
      job.destroy();
      console.log(`🗑️ Cleared cron job: ${key}`);
    });
    this.cronJobs.clear();
  }

  /**
   * Check VPS instances and send billing notifications
   */
  async checkAndSendBillingNotifications() {
    try {
      console.log("🔍 Checking VPS instances for upcoming billing dates...");

      const settings = await this.getSettings();
      if (!settings || !settings.enabled) {
        console.log("⏭️ VPS billing notifications are disabled, skipping...");
        return;
      }

      const daysBefore = settings.daysBefore || [7, 3, 1];
      const currentDate = new Date();

      // Check for each configured day before billing
      for (const days of daysBefore) {
        const targetDate = new Date();
        targetDate.setDate(currentDate.getDate() + days);
        targetDate.setHours(0, 0, 0, 0);

        const nextDay = new Date(targetDate);
        nextDay.setDate(targetDate.getDate() + 1);

        // Find VPS instances with billing date matching the target date
        const vpsInstances = await VPSInstance.find({
          "billing.nextBillingDate": {
            $gte: targetDate,
            $lt: nextDay,
          },
          status: { $in: ["running", "stopped", "pending"] }, // Only active instances
        }).populate("user", "email firstName lastName");

        console.log(
          `📅 Found ${vpsInstances.length} VPS instances with billing date in ${days} days`
        );

        // Send notifications for each instance
        for (const vpsInstance of vpsInstances) {
          await this.sendBillingNotification(vpsInstance, days, settings);
        }
      }

      console.log("✅ VPS billing notification check completed");
    } catch (error) {
      console.error("❌ Error checking VPS billing notifications:", error);
    }
  }

  /**
   * Send billing notification for a specific VPS instance
   */
  async sendBillingNotification(vpsInstance, daysBefore, settings) {
    try {
      const user = vpsInstance.user;
      if (!user) {
        console.warn(
          `⚠️ No user found for VPS instance ${vpsInstance.instanceId}`
        );
        return;
      }

      const billingDate = new Date(vpsInstance.billing.nextBillingDate);
      const formattedDate = billingDate.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });

      console.log(
        `📧 Sending billing notification to ${user.email} for VPS ${
          vpsInstance.displayName || vpsInstance.instanceId
        }`
      );

      // Send email notification
      if (settings.methods?.email) {
        await this.sendEmailNotification(
          user,
          vpsInstance,
          daysBefore,
          formattedDate,
          settings
        );
      }

      // Send in-app notification
      if (settings.methods?.inApp) {
        await this.sendInAppNotification(
          user,
          vpsInstance,
          daysBefore,
          formattedDate,
          settings
        );
      }
    } catch (error) {
      console.error(
        `❌ Error sending billing notification for VPS ${vpsInstance.instanceId}:`,
        error
      );
    }
  }

  /**
   * Send email billing notification
   */
  async sendEmailNotification(
    user,
    vpsInstance,
    daysBefore,
    formattedDate,
    settings
  ) {
    try {
      // Use configurable title or fallback to default
      const subject = this.replacePlaceholders(
        settings.notificationTitle ||
          `VPS Billing Reminder - Payment Due ${
            daysBefore === 1 ? "Tomorrow" : `in ${daysBefore} Days`
          }`,
        user,
        vpsInstance,
        daysBefore,
        formattedDate
      );

      // Create a simple HTML email content
      const htmlContent = this.generateEmailHTML(
        user,
        vpsInstance,
        daysBefore,
        formattedDate,
        settings
      );

      const emailData = {
        to: user.email,
        subject: subject,
        html: htmlContent,
      };

      await sendSimpleEmail(emailData);
      console.log(`✅ Email notification sent to ${user.email}`);
    } catch (error) {
      console.error(`❌ Error sending email notification:`, error);
    }
  }

  /**
   * Generate HTML content for the billing reminder email
   */
  generateEmailHTML(user, vpsInstance, daysBefore, formattedDate, settings) {
    const customerName = user.firstName || user.email;
    const vpsName = vpsInstance.displayName || `VPS ${vpsInstance.instanceId}`;
    const amount = vpsInstance.billing?.monthlyPrice || "N/A";
    const currency = vpsInstance.billing?.currency || "MAD";

    const urgencyColor =
      daysBefore === 1 ? "#dc3545" : daysBefore <= 3 ? "#f39c12" : "#17a2b8";

    // Use configurable message or fallback to default
    const customMessage = settings.notificationMessage
      ? this.replacePlaceholders(
          settings.notificationMessage,
          user,
          vpsInstance,
          daysBefore,
          formattedDate
        )
      : `Dear ${customerName}, your VPS service "${vpsName}" has an upcoming billing date on ${formattedDate}. Please ensure your payment method is up to date to avoid any service interruption.`;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPS Billing Reminder</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; }
        .container { background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e9ecef; }
        .logo { font-size: 28px; font-weight: bold; color: #2563eb; margin-bottom: 10px; }
        .alert-box { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin: 20px 0; border-left: 4px solid ${urgencyColor}; }
        .vps-details { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .detail-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #e9ecef; }
        .detail-label { font-weight: 600; color: #495057; }
        .detail-value { color: #212529; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; text-align: center; color: #6c757d; font-size: 14px; }
        .urgent { color: ${urgencyColor}; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">ZTech</div>
            <h2>VPS Billing Reminder</h2>
        </div>

        <div class="alert-box">
            <span style="font-size: 24px; margin-right: 10px;">⏰</span>
            <strong>
                <span class="urgent">
                    ${
                      daysBefore === 1
                        ? "Payment Due Tomorrow!"
                        : `Payment Due in ${daysBefore} Days`
                    }
                </span>
            </strong>
        </div>

        <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 6px; border-left: 4px solid ${urgencyColor};">
            <p style="margin: 0; line-height: 1.6;">${customMessage}</p>
        </div>

        <div class="vps-details">
            <h3 style="margin-top: 0; color: #2563eb;">VPS Service Details</h3>

            <div class="detail-row">
                <span class="detail-label">Service Name:</span>
                <span class="detail-value">${vpsName}</span>
            </div>

            <div class="detail-row">
                <span class="detail-label">VPS ID:</span>
                <span class="detail-value">${vpsInstance.instanceId}</span>
            </div>

            <div class="detail-row">
                <span class="detail-label">Billing Date:</span>
                <span class="detail-value">${formattedDate}</span>
            </div>

            <div class="detail-row">
                <span class="detail-label">Amount:</span>
                <span class="detail-value">${amount} ${currency}</span>
            </div>

            <div class="detail-row">
                <span class="detail-label">Days Until Billing:</span>
                <span class="detail-value">
                    <span class="urgent">${daysBefore} day${
      daysBefore > 1 ? "s" : ""
    }</span>
                </span>
            </div>
        </div>

        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

        <p>Thank you for choosing ZTech for your VPS hosting needs!</p>

        <div class="footer">
            <p>
                <strong>ZTech Support Team</strong><br>
                Email: <EMAIL>
            </p>
            <p style="font-size: 12px; color: #999;">
                This is an automated billing reminder. Please do not reply to this email.
            </p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Send in-app billing notification
   */
  async sendInAppNotification(
    user,
    vpsInstance,
    daysBefore,
    formattedDate,
    settings
  ) {
    try {
      // Use configurable title or fallback to default
      const title = this.replacePlaceholders(
        settings.notificationTitle || `VPS Billing Reminder`,
        user,
        vpsInstance,
        daysBefore,
        formattedDate
      );

      // Use configurable message or fallback to default
      const message = this.replacePlaceholders(
        settings.notificationMessage ||
          `Your VPS service "${
            vpsInstance.displayName || vpsInstance.instanceId
          }" has an upcoming billing date on ${formattedDate}. Please ensure your payment method is up to date.`,
        user,
        vpsInstance,
        daysBefore,
        formattedDate
      );

      // Create notification directly using the Notification model
      const notification = new Notification({
        userId: user._id,
        userType: "client",
        type: "billing_reminder",
        title: title,
        message: message,
        isRead: false,
      });

      await notification.save();

      console.log(`✅ In-app notification created for user ${user._id}`);
    } catch (error) {
      console.error(`❌ Error sending in-app notification:`, error);
    }
  }

  /**
   * Update settings and restart cron jobs
   */
  async updateSettings(newSettings) {
    try {
      console.log("🔄 Updating VPS billing notification settings...");

      const settings = await NotificationSetting.findOneAndUpdate(
        { type: "vps_billing" },
        newSettings,
        { new: true, upsert: true }
      );

      if (settings.enabled) {
        await this.setupCronJobs(settings);
      } else {
        this.clearCronJobs();
      }

      console.log("✅ VPS billing notification settings updated successfully");
      return settings;
    } catch (error) {
      console.error("❌ Error updating VPS billing settings:", error);
      throw error;
    }
  }

  /**
   * Manually trigger billing notification check (for testing)
   */
  async triggerManualCheck() {
    console.log("🔧 Manually triggering VPS billing notification check...");
    await this.checkAndSendBillingNotifications();
  }

  /**
   * Replace placeholders in notification content with actual values
   */
  replacePlaceholders(content, user, vpsInstance, daysBefore, formattedDate) {
    if (!content) return content;

    const customerName = user.firstName || user.email;
    const vpsName = vpsInstance.displayName || `VPS ${vpsInstance.instanceId}`;

    return content
      .replace(/\{\{customerName\}\}/g, customerName)
      .replace(/\{\{vpsName\}\}/g, vpsName)
      .replace(/\{\{billingDate\}\}/g, formattedDate)
      .replace(/\{\{daysUntilBilling\}\}/g, daysBefore.toString());
  }
}

module.exports = new VPSBillingNotificationService();
