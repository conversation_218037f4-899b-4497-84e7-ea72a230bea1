"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import WhyTrustUs from "@/components/landing/landing-a/whyTrustUs";
import PopularOffers from "@/components/landing/landing-a/PopularOffers";
import CallToAction from "@/components/landing/landing-a/callToAction";
import Testimonials from "@/components/home/<USER>";
import ContactForm2 from "@/components/shared/contactForm2";
import DiscountOffer from "@/components/landing/landing-b/discountOffer";

function LandingPageA() {
  const t = useTranslations("landing");
  const [data, setData] = useState({
    page: "Landing page A",
    offerName: "No offer selected",
    id: 0,
    url: "/landing/demandez-devis",
  });

  return (
    // <div className='h-full max-w-[1400px] mx-auto w-full'>
    <div className="w-full min-h-screen p-0 m-0 font-inter mb-0">
      <DiscountOffer t={t} />
      {/* <Banner t={t} /> */}
      <WhyTrustUs t={t} />
      <PopularOffers t={t} />
      <CallToAction t={t} />
      <Testimonials t={t} />
      <ContactForm2 data={data} setData={setData} t={t} />
    </div>
  );
}

export default LandingPageA;
