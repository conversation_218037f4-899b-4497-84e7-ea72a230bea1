import React, { useState, useEffect } from 'react';
import { Input, Button, Typography, Checkbox } from '@material-tailwind/react';
import profileService from '../../app/services/profileService';
import AvatarUploader from './avatarUploader';
import CompanyInfoForm from './companyInfoForm';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { FaFacebook } from 'react-icons/fa';

const EditProfileForm = ({ userId, t, tClient }) => {
    const [formData, setFormData] = useState({});
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const [avatarPreview, setAvatarPreview] = useState('');
    const router = useRouter();


    useEffect(() => {
        const getUserProfile = async () => {
            try {
                const userProfileRes = await profileService.getProfile();
                const userData = userProfileRes.data;

                // Calculate if this is an OAuth user without password
                const isOAuthWithoutPassword = userData.isOAuth && !userData.hasPassword;

                // Set default isCompany value if not already set
                if (!userData.billingInfo) {
                    userData.billingInfo = { isCompany: false };
                } else if (userData.billingInfo.userType) {
                    // Convert from old userType to isCompany if needed
                    userData.billingInfo.isCompany = userData.billingInfo.userType === 'company';
                    delete userData.billingInfo.userType;
                } else if (userData.billingInfo.isCompany === undefined) {
                    userData.billingInfo.isCompany = false;
                }

                setFormData({
                    ...userData,
                    isOAuthWithoutPassword // Add this field for UI rendering logic
                });
                console.log("userProfileRes.data: ", userProfileRes.data);
                setAvatarPreview(userData.photo);
            } catch (error) {
                console.error('Error fetching profile:', error);
            }
        };
        getUserProfile();
    }, []);

    const handleInputChange = (e) => {
        const { name, value } = e.target;

        // Handle billing info fields
        if (name.startsWith('company') || name === 'isCompany') {
            setFormData((prevData) => ({
                ...prevData,
                billingInfo: {
                    ...prevData.billingInfo,
                    [name]: value
                }
            }));
        } else {
            setFormData((prevData) => ({ ...prevData, [name]: value }));
        }

        setErrors((prevErrors) => ({ ...prevErrors, [name]: '' }));
    };

    // Handle checkbox change for company status
    const handleCompanyStatusChange = (e) => {
        const isChecked = e.target.checked;
        setFormData((prevData) => ({
            ...prevData,
            billingInfo: {
                ...prevData.billingInfo,
                isCompany: isChecked
            }
        }));
    };

    const handleAvatarChange = async (file) => {
        const formData = new FormData();
        formData.append('avatar', file);
        formData.append('id', userId);

        try {
            const uploadRes = await profileService.uploadAvatar(formData);
            setFormData((prevData) => ({ ...prevData, photo: uploadRes.data.data.fileUrl }));
            setAvatarPreview(uploadRes.data.data.fileUrl);
            toast.success(t('avatar_upload.success'));
        } catch (error) {
            setErrors((prevErrors) => ({ ...prevErrors, photo: t('avatar_upload.error', 'Failed to upload avatar') }));
        }
    };

    // Add form validation
    const validateForm = () => {
        let isValid = true;
        const newErrors = {};

        // Always validate firstName and lastName
        if (!formData.firstName?.trim()) {
            newErrors.firstName = t('first_name_required', 'First name is required');
            isValid = false;
        }

        if (!formData.lastName?.trim()) {
            newErrors.lastName = t('last_name_required', 'Last name is required');
            isValid = false;
        }

        // Validate password only if not OAuth without password
        if (!formData.isOAuthWithoutPassword && !formData.password?.trim()) {
            newErrors.password = t('current_password_required', 'Current password is required');
            isValid = false;
        }

        // Validate company fields if company account is selected
        if (formData.billingInfo?.isCompany) {
            if (!formData.billingInfo.companyICE?.trim()) {
                newErrors.companyICE = tClient('company_ice') + ' ' + tClient('is_required');
                isValid = false;
            } else if (!/^\d{15}$/.test(formData.billingInfo.companyICE)) {
                newErrors.companyICE = tClient('invalid_ice');
                isValid = false;
            }

            if (!formData.billingInfo.companyEmail?.trim()) {
                newErrors.companyEmail = tClient('company_email') + ' ' + tClient('is_required');
                isValid = false;
            } else if (!/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/.test(formData.billingInfo.companyEmail)) {
                newErrors.companyEmail = tClient('invalid_email');
                isValid = false;
            }

            if (!formData.billingInfo.companyPhone?.trim()) {
                newErrors.companyPhone = tClient('company_phone') + ' ' + tClient('is_required');
                isValid = false;
            } else if (!/^\+?[0-9]{7,15}$/.test(formData.billingInfo.companyPhone)) {
                newErrors.companyPhone = tClient('invalid_phone');
                isValid = false;
            }

            if (!formData.billingInfo.companyAddress?.trim()) {
                newErrors.companyAddress = tClient('company_address') + ' ' + tClient('is_required');
                isValid = false;
            }
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Validate form before submission
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        try {
            // Ensure isOAuth is explicitly set in the data being sent
            const dataToSend = {
                ...formData,
                isOAuth: !!formData.isOAuth,
                password: formData.isOAuthWithoutPassword ? "" : formData.password
            };

            // Remove the helper field we added
            delete dataToSend.isOAuthWithoutPassword;

            // First update the user profile
            await profileService.editAccount(dataToSend);

            // Then update the billing information if it exists
            if (formData.billingInfo) {
                // Make sure the user ID is included in the billing info
                const billingInfoWithUserId = {
                    ...formData.billingInfo,
                    userId: formData._id // Include the user ID
                };
                await profileService.updateBillingInfo(billingInfoWithUserId);
            }

            toast.success(t('profile_updated', 'Profile updated successfully'));
            window.location.href = '/client/profile';
        } catch (error) {
            console.error("Error updating profile:", error);
            if (error.response?.data?.errors) {
                const serverErrors = error.response.data.errors.reduce((acc, curr) => {
                    acc[curr.key] = curr.msg;
                    return acc;
                }, {});
                setErrors(serverErrors);
            } else {
                toast.error(t('update_failed', 'Failed to update profile'));
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="">
            <Typography variant='h2' className="text-3xl font-semibold text-gray-800 mb-6">
                {t('edit_profile')}
            </Typography>

            <form onSubmit={handleSubmit} className="space-y-6">
                <AvatarUploader
                    avatarPreview={avatarPreview}
                    onAvatarChange={handleAvatarChange}
                    error={errors.photo}
                    t={t}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Input
                            type="text"
                            name="firstName"
                            label={t('first_name')}
                            value={formData.firstName || ''}
                            onChange={handleInputChange}
                        />
                        {errors.firstName && <p className="text-red-500 text-sm">{errors.firstName}</p>}
                    </div>
                    <div>
                        <Input
                            type="text"
                            name="lastName"
                            label={t('last_name')}
                            value={formData.lastName || ''}
                            onChange={handleInputChange}
                        />
                        {errors.lastName && <p className="text-red-500 text-sm">{errors.lastName}</p>}
                    </div>
                </div>

                {/* Company Account Selection */}
                <div className="mt-6">
                    <div className="flex items-center gap-2">
                        <Checkbox
                            name="isCompany"
                            label={tClient('company_account')}
                            checked={formData.billingInfo?.isCompany || false}
                            onChange={handleCompanyStatusChange}
                        />
                    </div>
                </div>

                {/* Company Information (conditionally rendered) */}
                {formData.billingInfo?.isCompany && (
                    <CompanyInfoForm
                        formData={formData.billingInfo || {}}
                        handleInputChange={handleInputChange}
                        errors={errors}
                        tClient={tClient}
                    />
                )}


                {/* Show password field if not OAuth without password */}
                {!formData.isOAuthWithoutPassword && (
                    <div>
                        <Input
                            type="password"
                            name="password"
                            label={t('current_password')}
                            value={formData.password || ''}
                            onChange={handleInputChange}
                            />
                        {errors.password && <p className="text-red-500 text-sm">{errors.password}</p>}
                    </div>
                )}

                {/* Show OAuth provider info if this is an OAuth account */}
                {formData?.isOAuth && (
                    <div className="flex items-center gap-x-2 bg-gray-100 p-2 rounded-md mb-4 w-fit">
                        {/* Conditionally render the logo based on the provider */}
                        {formData?.socialMediaData?.provider === 'google' && (
                            <img
                                src="/images/google.png"
                                alt="Google"
                                className="w-6 h-6"
                            />
                        )}
                        {formData?.socialMediaData?.provider === 'facebook' && (
                            <FaFacebook color='#1976d2' width={100} fontSize={30} />
                        )}
                        <p className="text-sm text-gray-700">
                            {t('this_account_is_linked_by', 'This account is linked by')} {formData?.socialMediaData?.provider}
                        </p>
                    </div>
                )}

                <div className="flex flex-row gap-x-10">
                    <Button
                        type="submit" disabled={loading}
                        className='bg-secondary font-normal font-poppins rounded-md'>
                        {loading ? t('updating', 'Updating...') : t('save_changes', 'Save Changes')}
                    </Button>
                    <Button
                        onClick={() => router.push('/client/profile')}
                        className='bg-gray-50 text-gray-800 hover:shadow-sm border-gray-200 px-10 border shadow-none font-medium font-poppins rounded-md'>
                        {t('back', 'Back')}
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default EditProfileForm;
