"use client";

import { createContext, useContext, useState } from "react";

const GlobalStateContext = createContext();

export const GlobalStateProvider = ({ children }) => {
    const [data, setData] = useState({
        page: "-----",
        offerName: "No offer selected",
        id: 0,
        url: "/from context",
    });

    return (
        <GlobalStateContext.Provider value={{ data, setData }}>
            {children}
        </GlobalStateContext.Provider>
    );
};

export const useGlobalState = () => useContext(GlobalStateContext);
