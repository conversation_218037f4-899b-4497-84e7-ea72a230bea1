import React from 'react';
import { Card, CardBody, Typography, Button, Chip } from '@material-tailwind/react';
import { TrashIcon, ServerIcon, GlobeAltIcon, CpuChipIcon } from '@heroicons/react/24/outline';

const VPSCartItem = ({ item, onUpdateQuantity, onRemove }) => {
  const { package: pkg, quantity, customConfiguration } = item;
  
  // Check if this is a VPS item
  if (!pkg?.vpsConfig || !customConfiguration) {
    return null;
  }

  const config = customConfiguration;
  const frontendConfig = config.frontendConfig || {};

  // Format specifications
  const formatSpecs = () => {
    const specs = [];
    if (config.cpu) specs.push(`${config.cpu} vCPU`);
    if (config.ram) specs.push(`${config.ram}GB RAM`);
    if (config.storage) specs.push(`${config.storage}GB Storage`);
    return specs.join(' • ');
  };

  // Get region display name
  const getRegionName = (regionCode) => {
    const regionMap = {
      'EU': 'European Union',
      'US-east': 'United States East',
      'US-west': 'United States West',
      'SIN': 'Singapore',
      'JPN': 'Japan',
      'UK': 'United Kingdom',
      'IND': 'India'
    };
    return regionMap[regionCode] || regionCode;
  };

  // Get OS display name
  const getOSName = (osCode) => {
    const osMap = {
      'ubuntu-20.04': 'Ubuntu 20.04 LTS',
      'ubuntu-22.04': 'Ubuntu 22.04 LTS',
      'ubuntu-24.04': 'Ubuntu 24.04 LTS',
      'centos-7': 'CentOS 7',
      'centos-8': 'CentOS 8',
      'debian-10': 'Debian 10',
      'debian-11': 'Debian 11',
      'windows-server-2019': 'Windows Server 2019',
      'windows-server-2022': 'Windows Server 2022'
    };
    return osMap[osCode] || osCode;
  };

  return (
    <Card className="mb-4 shadow-md">
      <CardBody className="p-4">
        <div className="flex flex-col lg:flex-row lg:items-center gap-4">
          {/* VPS Icon and Basic Info */}
          <div className="flex items-center gap-3 flex-1">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <ServerIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <Typography variant="h6" className="text-gray-900 font-semibold">
                {pkg.name}
              </Typography>
              <Typography className="text-sm text-gray-600">
                {formatSpecs()}
              </Typography>
              <div className="flex items-center gap-2 mt-1">
                <Chip
                  value={config.planId}
                  size="sm"
                  className="bg-blue-50 text-blue-700"
                />
                <Chip
                  value={config.provider}
                  size="sm"
                  className="bg-green-50 text-green-700"
                />
              </div>
            </div>
          </div>

          {/* Configuration Details */}
          <div className="flex-1 lg:max-w-md">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-2">
                <GlobeAltIcon className="w-4 h-4 text-gray-500" />
                <span className="text-gray-600">Region:</span>
                <span className="font-medium">{getRegionName(config.region)}</span>
              </div>
              <div className="flex items-center gap-2">
                <CpuChipIcon className="w-4 h-4 text-gray-500" />
                <span className="text-gray-600">OS:</span>
                <span className="font-medium">{getOSName(config.operatingSystem)}</span>
              </div>
              {config.displayName && (
                <div className="sm:col-span-2">
                  <span className="text-gray-600">Name:</span>
                  <span className="font-medium ml-2">{config.displayName}</span>
                </div>
              )}
            </div>

            {/* Additional Options */}
            {(config.addons || frontendConfig) && (
              <div className="mt-2 flex flex-wrap gap-1">
                {config.addons?.autobackup && (
                  <Chip value="Auto Backup" size="sm" className="bg-yellow-50 text-yellow-700" />
                )}
                {config.addons?.privatenetworking && (
                  <Chip value="Private Network" size="sm" className="bg-purple-50 text-purple-700" />
                )}
                {config.addons?.monitoring && (
                  <Chip value="Monitoring" size="sm" className="bg-orange-50 text-orange-700" />
                )}
                {frontendConfig.additionalIPs > 0 && (
                  <Chip 
                    value={`+${frontendConfig.additionalIPs} IP${frontendConfig.additionalIPs > 1 ? 's' : ''}`} 
                    size="sm" 
                    className="bg-indigo-50 text-indigo-700" 
                  />
                )}
              </div>
            )}
          </div>

          {/* Quantity and Price */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outlined"
                className="w-8 h-8 p-0"
                onClick={() => onUpdateQuantity(item._id, Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                -
              </Button>
              <span className="w-8 text-center font-medium">{quantity}</span>
              <Button
                size="sm"
                variant="outlined"
                className="w-8 h-8 p-0"
                onClick={() => onUpdateQuantity(item._id, Math.min(10, quantity + 1))}
                disabled={quantity >= 10}
              >
                +
              </Button>
            </div>

            <div className="text-right">
              <Typography variant="h6" className="text-gray-900 font-bold">
                {(pkg.price * quantity).toFixed(2)} MAD
              </Typography>
              <Typography className="text-sm text-gray-500">
                {pkg.price} MAD/month
              </Typography>
            </div>

            <Button
              size="sm"
              variant="text"
              color="red"
              className="w-8 h-8 p-0"
              onClick={() => onRemove(item._id)}
            >
              <TrashIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Configuration Summary for Mobile */}
        <div className="lg:hidden mt-3 pt-3 border-t border-gray-200">
          <Typography className="text-xs text-gray-500 mb-1">Configuration:</Typography>
          <Typography className="text-sm text-gray-700">
            {getRegionName(config.region)} • {getOSName(config.operatingSystem)}
            {config.displayName && ` • ${config.displayName}`}
          </Typography>
        </div>
      </CardBody>
    </Card>
  );
};

export default VPSCartItem;
