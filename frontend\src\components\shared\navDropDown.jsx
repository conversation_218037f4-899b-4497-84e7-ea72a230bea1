"use client"
import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { BsArrowUpRight } from 'react-icons/bs';
import { Typography } from '@material-tailwind/react';
import Image from 'next/image';
import useDeviceScale from '../../app/hook/useDeviceScale';

const NavDropDown = ({ content, position, t }) => {
    const deviceType = useDeviceScale()
    console.log(deviceType)
    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute z-50"
            style={{
                top: position.top - (deviceType <= 4 ? 30 : 28),
                left: -0,
                right: 0,
            }}>
            {/* <div className="mx-auto flex items-baseline justify-end z-50">
                <svg
                    width="26"
                    height="20"
                    viewBox="0 0 26 23"
                    fill="white"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.40267 2.23076C11.0015 -0.538468 14.9985 -0.538461 16.5973 2.23077L24.9911 16.7692C26.5899 19.5385 24.5914 23 21.3938 23H4.60621C1.40858 23 -0.589937 19.5385 1.00888 16.7692L9.40267 2.23076Z" fill="#497ef7" />
                </svg>
            </div> */}
            <div className="flex mr-auto  w-full max-w-[1000px] border-secondary border-2 rounded-sm bg-secondary ">
                {/* Left Column: Menu Items */}
                <div className="w-[50%] bg-white p-4 rounded-bl-lg">
                    <div className="inline-flex row-auto gap-x-4 mb-6">
                        <Link
                            href={content.url}
                            aria-label={"Go to " + content.title}
                            className='pb-1 border-b-[2px] border-secondary'>
                            {content.isImg ? (
                                <Image
                                    width={32}
                                    height={32}
                                    className='w-8 h-8'
                                    src={content.icon}
                                    alt={t(content.title)} />
                            ) : (
                                content.icon
                            )}
                        </Link>
                        <Link
                            href={content.url}
                            aria-label={"Go to " + content.title}>
                            <Typography
                                className='text-lg my-auto capitalize font-semibold text-primary p-0 '
                                variant='h3'>
                                {t(content.title)}
                            </Typography>
                        </Link>
                    </div>
                    <div className="flex flex-col gap-y-4">
                        {content?.items.map((subItem, index) => (
                            <div
                                key={index}
                                className="flex flex-col justify-between font-inter">
                                <Link
                                    href={content.url}
                                    aria-label={"Go to " + subItem.title}
                                    className='text-base text-[#212121] font-medium normal-case'>
                                    {t(subItem.title)}
                                </Link>
                                <span className='text-xs normal-case font-light text-[#37436F]'>{subItem.pricing ? t(subItem.pricing) : ""}</span>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Column: Offering Section */}
                <div className="w-[50%] bg-secondary text-white px-4 pt-16 pb-4 rounded-br-lg flex flex-col gap-y-2 items-start justify-center">
                    <h4 className="font-semibold text-lg normal-case">
                        {t(content.rightTitle)}
                    </h4>
                    <p className="text-sm font-thin normal-case">
                        {t(content.description)}
                    </p>
                    <Link
                        href={content.url + "#contact-nous"}
                        aria-label={"Go to " + content.title}
                        className=" bg-white pl-2 pr-1 mt-6 py-1 text-black normal-case w-3/4 flex items-center justify-between gap-x-5 font-normal rounded-full">
                        <span className='text-sm font-inter'>{t('contact_us')}</span>
                        <span className='p-2 bg-black rounded-full border-[0.69px] border-[#2C52F7]'
                            style={{ boxShadow: '0px 0px 19.61px 7.54px rgba(66, 99, 242, 0.39)' }}
                        >
                            <BsArrowUpRight className='text-white' />
                        </span>
                    </Link>
                </div>
            </div>
        </motion.div >
    );
};

export default NavDropDown;
