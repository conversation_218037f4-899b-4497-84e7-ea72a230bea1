'use client'
import { Typography } from '@material-tailwind/react'
import React from 'react'
import { DIVISIONsvg, LIGHTNINGsvg, MALEPERSONsvg, MOSQUEsvg, SHIELDsvg } from '../../icons/svgIcons'
import { ArrowRightIcon, PhoneIcon } from '@heroicons/react/24/solid'
import Link from 'next/link';
import Image from 'next/image'
import datacenterImg from "/public/images/services/data-center.png";

const features = [
    {
        title: "features.0.title",
        description: "features.0.description"
    },
    {
        title: "features.1.title",
        description: "features.1.description"
    },
    {
        title: "features.2.title",
        description: "features.2.description"
    }
];

const benefits = [
    {
        icon: <SHIELDsvg />,
        title: "features.0.title",
        description: "features.0.description"
    },
    {
        icon: <LIGHTNINGsvg />,
        title: "features.1.title",
        description: "features.2.description"
    },
    {
        icon: <MALEPERSONsvg />,
        title: "features.2.title",
        description: "features.2.description"
    }
]

const advantages = [
    {
        icon: <MOSQUEsvg />,
        title: "advantages.0.title",
        description: "advantages.0.description"
    },
    {
        icon: <MALEPERSONsvg />,
        title: "advantages.1.title",
        description: "advantages.1.description"
    },
    {
        icon: <DIVISIONsvg />,
        title: "advantages.2.title",
        description: "advantages.2.description"
    }
]

function ServersMain({ t }) {
    return (
        <div className="w-full">
            <div className='max-w-[1400px] mx-auto w-full flex flex-col gap-y-0 p-0'>
                <div className="w-full md:p-16 p-4 flex flex-col md:flex-row items-center justify-between gap-8">
                    <div className="bg-white">
                        <Typography
                            variant='h2'
                            className="text-3xl font-semibold mb-6 text-black max-w-lg">
                            {t('dedicated_server_advantages')}
                        </Typography>
                        <p className='max-w-md'>
                            {t('dedicated_server_benefits_intro')}
                        </p>
                        <div className="mt-10 flex flex-col border-l-2 border-gray-700 px-4 py-2 gap-y-6 max-w-lg">
                            {features.map((feature, index) => (
                                <div key={index} className="flex flex-col gap-y-2">
                                    <Typography
                                        variant="h5"
                                        className="text-secondary font-inter font-medium text-xl"
                                    >
                                        {t(feature.title)}
                                    </Typography>
                                    <p className="text-gray-700">{t(feature.description)}</p>
                                </div>
                            ))}
                        </div>
                        <div className="flex md:flex-row flex-col items-center gap-4 mt-8">
                            <Link
                                href="#contact-nous"
                                className='px-6 py-3 md:py-2 flex justify-between md:justify-normal md:w-fit w-full  gap-x-4 text-black shadow-none uppercase border-2 border-black hover:border-secondary rounded-3xl bg-transparent hover:bg-secondary hover:text-white font-inter text-sm font-medium' >
                                {t('request_quote')}
                                <ArrowRightIcon className='w-4 my-auto' />
                            </Link>
                            <Link
                                href="tel:+212662841605"
                                className='px-6 md:py-2 py-3 flex md:flex-row flex-row-reverse justify-between md:justify-normal md:w-fit w-full gap-x-2 text-secondary shadow-none uppercase border-2 border-gray-500 rounded-3xl hover:bg-primary hover:text-white font-inter text-sm font-medium'>
                                <PhoneIcon className='text-secondary md:w-4 w-5 my-auto' />
                                <p className='md:m-auto'>+212 662 841 605</p>
                            </Link>
                        </div>
                    </div>
                    <div className="bg-[#F2F4FB] rounded-xl border shadow-inner max-w-lg md:min-h-[500px] p-4 md:p-0 flex items-center justify-center w-full h-full">
                        <img
                            loading="lazy"
                            src="/images/services/servers-connected.png"
                            alt="Data center"
                            className="w-[350px] h-auto rounded-xl object-cover m-auto"
                        />
                    </div>
                </div>

                <div className="bg-[#F2F4FB] p-10">
                    <Typography
                        variant='h2'
                        className="text-[28px] font-inter font-semibold text-center mb-4">
                        {t('dedicated_server_offers')}
                    </Typography>
                    <div className="flex flex-col gap-y-4 md:flex-row gap-x-6 max-w-5xl m-auto p-4 justify-between">
                        {benefits.map((item, index) => (
                            <div
                                key={index}
                                className="md:w-1/3 w-full bg-white shadow-md rounded-lg p-6 border border-secondary text-center"
                            >
                                <div className="w-full flex items-center justify-center text-4xl text-blue-500 mb-4">
                                    <span className="border border-[#F2F4FB] rounded-md p-2 bg-[#F2F4FB] hover:border-secondary">
                                        {item.icon}
                                    </span>
                                </div>
                                <h3 className="font-medium text-lg mb-2 w-1/2 m-auto">{t(item.title)}</h3>
                                <p className="text-gray-800 text-[15px]">{t(item.description)}</p>
                            </div>
                        ))}
                    </div>
                </div>

                <div className='bg-transparent items-center flex flex-col justify-center gap-y-2 p-0 overflow-hidden w-full'>
                    <div className="w-full flex flex-col md:flex-row justify-between mt-0 md:p-10 p-4">
                        <div className="bg-white md:w-1/2">
                            <Typography
                                variant='h2'
                                className="text-3xl font-semibold mb-6 text-black md:max-w-lg">
                                {t('advantages_title')}
                            </Typography>

                            <div className="mt-10 flex flex-col px-4 py-2 gap-y-6 max-w-lg">
                                {advantages.map((item, index) => (
                                    <div
                                        key={index}
                                        className="flex flex-row justify-between gap-x-4">
                                        <div
                                            className="border border-[#F2F4FB] rounded-md bg-[#F2F4FB] w-[100px] h-[90px] p-6 flex m-auto items-center text-4xl text-primary hover:border-secondary">
                                            {item.icon}
                                        </div>
                                        <div className="flex flex-col gap-y-2">
                                            <Typography
                                                variant='h5'
                                                className="text-secondary font-inter text-xl font-medium">
                                                {t(item.title)}
                                            </Typography>
                                            <p className="text-gray-700">
                                                {t(item.description)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="flex md:flex-row flex-col items-center gap-4 mt-8">
                                <Link
                                    href="#contact-nous"
                                    className='px-4 py-3 md:py-2 flex justify-between md:justify-normal md:w-fit w-full  gap-x-4 text-black shadow-none uppercase border-2 hover:border-secondary border-black rounded-3xl bg-transparent hover:bg-secondary hover:text-white font-inter text-sm font-medium' >
                                    {t('request_quote')}
                                    <ArrowRightIcon className='w-4 my-auto' />
                                </Link>
                                <Link
                                    href="tel:+212662841605"
                                    className='px-6 md:py-2 py-3 flex md:flex-row flex-row-reverse justify-between md:justify-normal md:w-fit w-full gap-x-2 text-secondary shadow-none uppercase border-2 border-gray-500 rounded-3xl hover:bg-primary hover:text-white font-inter text-sm font-medium'>
                                    <PhoneIcon className='text-secondary md:w-4 w-5 my-auto' />
                                    <p className='md:m-auto'>+212 662 841 605</p>
                                </Link>
                            </div>
                        </div>
                        <div className="md:w-1/2 hidden md:flex items-center justify-center md:px-10 pb-0 rounded-3xl md:mt-0 mt-4">
                            <Image
                                loading="lazy"
                                src={datacenterImg}
                                alt="Data center"
                                placeholder='blur'
                                className="h-full w-[90%] rounded-3xl object-cover m-auto"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ServersMain;