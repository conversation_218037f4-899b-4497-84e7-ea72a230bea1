export const formatDate = (date) => {
  try {
    if (!date) return "";
    return new Date(date).toISOString().split("T")[0];
  } catch (error) {
    console.error("Date formatting error:", error);
  }
};

// Additional date formatting functions can be added here as needed
export const formatShortDate = (date) => {
  try {
    if (!date) return "";
    return new Date(date).toLocaleDateString("en-UK");
  } catch (error) {
    console.error("Date formatting error:", error);
  }
};
