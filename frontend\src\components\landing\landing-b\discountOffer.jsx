'use client';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Typo<PERSON> } from '@material-tailwind/react';
import { <PERSON><PERSON>_Neue } from "next/font/google";
import useCountdown from '../../../app/hook/useCountdown';
import { useRouter } from 'next/navigation';
import categoryService from '../../../app/services/categoryService';
import cartService from '../../../app/services/cartService';
import { useAuth } from '../../../app/context/AuthContext';
import CustomButton from '../../shared/customButton';

const bebasNeue = Bebas_Neue({
    weight: "400",
    subsets: ["latin"],
});


const promotionPacksOld = [
    {
        title: "promotionPacks.0.title",
        id: 2,
        price: "promotionPacks.0.price",
        originalPrice: "promotionPacks.0.originalPrice",
        slogan: "promotionPacks.0.slogan",
        features: [
            "promotionPacks.0.features.0",
            "promotionPacks.0.features.1",
            "promotionPacks.0.features.2",
            "promotionPacks.0.features.3",
            "promotionPacks.0.features.4"
        ]
    },
    {
        title: "promotionPacks.1.title",
        id: 3,
        price: "promotionPacks.1.price",
        originalPrice: "promotionPacks.1.originalPrice",
        slogan: "promotionPacks.1.slogan",
        features: [
            "promotionPacks.1.features.0",
            "promotionPacks.1.features.1",
            "promotionPacks.1.features.2",
            "promotionPacks.1.features.3",
            "promotionPacks.1.features.4"
        ]
    }
];

function DiscountOffer({ t }) {
    const [promotionPacks, setPromotionPacks] = useState(promotionPacksOld);
    const { setCartCount, cartCount } = useAuth()
    const targetDate = new Date("2025-01-31T00:00:00");
    const timeLeft = useCountdown(targetDate);
    const router = useRouter();


    useEffect(() => {
        // Fetching data from JSON file
        const getPromotionsData = async () => {
            try {
                const response = await categoryService.getCategory('Promotions');
                console.log('response.data ..: ', response.data.brands[0].packages);
                setPromotionPacks(response.data.brands[0].packages);
            } catch (error) {
                console.error('error fetching promotions: ', error);
            }
        };
        getPromotionsData();
    }, []);

    const handleAddPackageToCart = async (id) => {
        try {
            const res = await cartService.addItemToCart({ packageId: id, quantity: 1 });
            console.log('addPackageToCart :', cartCount);
            setCartCount(res.data.cart.cartCount);
        } catch (error) {
            console.error('error adding package to cart', error);
        }
    }

    const PromotionPack = ({ pack }) => (
        <div className="bg-white/15 backdrop-blur-md border border-white/20 rounded-sm p-4 transform transition-all duration-300 hover:shadow-2xl font-outfit flex flex-col">
            <Typography
                variant='h2'
                className="text-xl font-bold text-white mb-2 border-b border-white/20 pb-2 font-outfit">
                {t(pack.name)}
            </Typography>

            <div className="flex items-center justify-start px-6 text-white mb-6">
                <div className="relative">
                    <span className="text-2xl font-extrabold text-white font-outfit">{pack.price} DH</span>
                    <span className="absolute top-[70%] right-0 text-base text-red-500 line-through italic font-outfit transform translate-x-1/4">{pack.regularPrice} DH</span>
                </div>
            </div>

            <p className="text-gray-300 italic mb-6">{'"' + t(pack.description) + '"'}</p>

            <ul className="flex flex-col gap-y-2 mb-6 flex-grow">
                {pack.specifications?.map((feature, index) => (
                    <li key={index} className="flex items-center text-white text-base">
                        <svg className="w-5 h-5 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {t(feature.key)}
                    </li>
                ))}
            </ul>
            <div className="flex items-center">
                <Button
                    onClick={() => handleAddPackageToCart(pack._id)}
                    name='contact-us-btn'
                    color='white'
                    size='lg'
                    className='hidden mx-auto justify-center text-secondary rounded-full py-2 font-bold font-outfit  hover:text-primary hover:bg-blue-300 px-6 bg-white'>
                    {t('request_quote')}
                </Button>
                <CustomButton
                    onClick={() => {
                        handleAddPackageToCart(pack._id)
                        router.push('/client/cart');
                    }}
                    price={"price= " + pack.price}
                    label={'Add to Cart'}
                    name={'custom-button'}
                />
            </div>
        </div >
    );

    return (
        <div className="relative w-full bg-gradient-to-b from-[#000f38] to-[#00093d] overflow-hidden">
            <div className="absolute inset-0 bg-cover bg-center opacity-30"
                style={{
                    backgroundImage: "radial-gradient(circle at center, rgba(0,255,255,0.1) 0%, rgba(0,15,56,0.8) 70%)",
                    transform: "perspective(1000px) rotateX(60deg)",
                    animation: "wormhole-rotation 20s infinite linear"
                }}
            />
            <div className="max-w-[1400px] mx-auto min-h-[1000px] rounded-3xl"
                style={{
                    backgroundImage: "url('/images/landing/blue-whole-bg-effect.svg')",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "bottom"
                }}
            >
                <div className="flex flex-col items-center justify-center text-white py-6 relative z-10">
                    <Typography
                        variant='h1'
                        className="text-5xl font-bold mb-2 text-center font-jim_ngihtshade drop-shadow-[0_0_10px_rgba(0,255,255,0.5)]">
                        {t('promotional_offers')}
                    </Typography>
                    <div className="md:mb-6 mb-4 bg-white/20 text-white text-lg px-6 py-2 rounded-full flex items-center gap-x-4 animate-pulse">
                        <span role="img" aria-label="alert" className="text-2xl">
                            🔔
                        </span>
                        {t('exclusive_promotion')}
                        <span role="img" aria-label="alert" className="text-2xl">
                            🔔
                        </span>
                    </div>
                    {/* Countdown */}
                    <div className={`flex items-center justify-center mb-12 md:gap-x-6 gap-x-4 ${bebasNeue.className}`}>
                        {/* Render countdown digits */}
                        {["days", "hours", "minutes"].map((unit) => (
                            <div key={unit} className="flex flex-col items-center">
                                <div className="flex md:gap-2 gap-1">
                                    {String(timeLeft[unit]).padStart(2, "0").split("").map((digit, index) => (
                                        <div
                                            key={`${unit}-${index}`}
                                            className="bg-gradient-to-b from-[#1a2352] to-[#0a1c4c] rounded-lg md:px-5 px-3 py-0 text-white md:text-5xl text-3xl font-bold shadow-2xl border border-white/10 md:hover:scale-105 transition-transform"
                                        >
                                            {digit}
                                        </div>
                                    ))}
                                </div>
                                <span className="text-sm mt-2 text-gray-400 uppercase tracking-wider">{t(unit)}</span>
                            </div>
                        ))}
                    </div>

                    {/* Promotion Packs */}
                    <div className="grid md:grid-cols-2 gap-8 w-full max-w-4xl px-2">
                        {promotionPacks?.map((pack, index) => (
                            <PromotionPack
                                key={index}
                                pack={pack}
                            // selectPack={handleOfferClick}
                            />
                        ))}
                    </div>
                </div>
            </div>
            {/* Additional decorative elements */}
            <style jsx global>
                {`
                    @keyframes wormhole-rotation {
                    0% { transform: perspective(1000px) rotateX(60deg) rotate(0deg); }
                    100% { transform: perspective(1000px) rotateX(60deg) rotate(360deg); }
                    }
                `}
            </style>
        </div>
    );
}

export default DiscountOffer;
