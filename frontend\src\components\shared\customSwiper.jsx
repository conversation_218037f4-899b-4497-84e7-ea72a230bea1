import React, { useState } from 'react';
import Slider from 'react-slick';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Typography } from '@material-tailwind/react';

// Custom Arrow Components
const CustomPrevArrow = ({ onClick }) => {
    return (
        <div
            onClick={onClick}
            className="absolute -bottom-10 left-[30%] md:left-[42%] z-10 cursor-pointer bg-white border border-gray-300 rounded-full p-1 transition-transform hover:scale-110"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 23 23"
                fill="black"
                width="24"
                height="24"
            >
                <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6z" />
            </svg>
        </div>
    );
};

const CustomNextArrow = ({ onClick }) => {
    return (
        <div
            onClick={onClick}
            className="absolute -bottom-10 left-[60%] md:left-[50%] z-10 cursor-pointer bg-white border border-gray-300 rounded-full p-1 transition-transform hover:scale-110"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 23 23"
                fill="black"
                width="24"
                height="24"
            >
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6z" />
            </svg>
        </div>
    );
};

const CustomSwiper = ({ data, t }) => {
    const [focusedSlide, setFocusedSlide] = useState(0);
    const [isExpanded, setIsExpanded] = useState(false);

    const toggleDescription = () => {
        setIsExpanded((prev) => !prev);
    };

    const settings = {
        dots: false,
        infinite: true,
        speed: 500,
        slidesToShow: 3,
        slidesToScroll: 1,
        beforeChange: (_, next) => setFocusedSlide(next),
        nextArrow: <CustomNextArrow />,
        prevArrow: <CustomPrevArrow />,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                },
            },
        ],
    };

    return (
        <div className="w-4/5 m-auto md:overflow-hidden relative md:p-10">
            <Slider {...settings} className="p-0">
                {data.map((review, index) => (
                    <div
                        key={index}
                        className={`p-2 border bg-white border-gray-300 transition-transform rounded-xl duration-300 ${index === focusedSlide ? "scale-100 shadow-custom-heavy" : "scale-90 shadow-custom-light"
                            }`}
                    >
                        <div className="min-h-[300px] w-full bg-white p-4 gap-y-2 flex flex-col cursor-pointer">
                            <div className="flex gap-x-5 mb-2">
                                <svg width="100" height="19" viewBox="0 0 100 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0.0078125 18.2707H18.2965V0H0.0078125V18.2707Z" fill="#F4BA07" />
                                    <path d="M15.5984 7.7734L5.22523 15.3014L6.73887 10.6488L2.77661 7.7734H7.67417L9.1875 3.12048L10.7008 7.7734H15.5984ZM9.18787 12.4263L12.0208 11.831L13.1495 15.3014L9.18787 12.4263Z" fill="white" />
                                    <path d="M20.218 18.2707H38.5067V0H20.218V18.2707Z" fill="#F4BA07" />
                                    <path d="M20.218 18.2707H29.3624V0H20.218V18.2707Z" fill="#F4BA07" />
                                    <path d="M29.5812 12.3553L32.0104 11.8308L33.134 15.3801L29.3244 12.5396L25.3662 15.3801L26.9025 10.7106L22.8806 7.82472H27.8518L29.3874 3.15491L30.9237 7.82472H35.8945L29.5812 12.3553Z" fill="white" />
                                    <path d="M40.4424 18.2707H58.7311V0H40.4424V18.2707Z" fill="#F4BA07" />
                                    <path d="M40.4424 18.2707H49.5867V0H40.4424V18.2707Z" fill="#F4BA07" />
                                    <path d="M56.0334 7.7734L45.6603 15.3014L47.1739 10.6488L43.2117 7.7734H48.1092L49.6226 3.12048L51.1359 7.7734L56.0334 7.7734ZM49.6229 12.4263L52.4559 11.831L53.5846 15.3014L49.6229 12.4263Z" fill="white" />
                                    <path d="M60.6665 18.2707H78.9552V0H60.6665V18.2707Z" fill="#F4BA07" />
                                    <path d="M60.6665 18.2707H69.8109V0H60.6665V18.2707Z" fill="#F4BA07" />
                                    <path d="M76.2568 7.7734L65.884 15.3014L67.3973 10.6488L63.4351 7.7734H68.3326L69.8459 3.12048L71.3593 7.7734L76.2568 7.7734ZM69.8463 12.4263L72.6793 11.831L73.808 15.3014L69.8463 12.4263Z" fill="white" />
                                    <path d="M80.8772 18.2707H99.1659V0H80.8772V18.2707Z" fill="#F4BA07" />
                                    <path d="M80.8772 18.2707H90.0216V0H80.8772V18.2707Z" fill="#F4BA07" />
                                    <path d="M96.4673 7.7734L86.0944 15.3014L87.6078 10.6488L83.6455 7.7734H88.5431L90.0564 3.12048L91.5697 7.7734H96.4673ZM90.0568 12.4263L92.8897 11.831L94.0184 15.3014L90.0568 12.4263Z" fill="white" />
                                </svg>
                                <div className="flex gap-x-1 w-fit">
                                    <span className='m-auto'>
                                        <svg
                                            width="14"
                                            height="15"
                                            viewBox="0 0 14 15"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fillRule="evenodd" clipRule="evenodd" d="M7 14.0703C10.866 14.0703 14 10.9363 14 7.07031C14 3.20432 10.866 0.0703125 7 0.0703125C3.13401 0.0703125 0 3.20432 0 7.07031C0 10.9363 3.13401 14.0703 7 14.0703ZM6.09217 7.88432L9.20311 4.77341C9.44874 4.52788 9.84688 4.52788 10.0923 4.77341C10.338 5.01895 10.338 5.41704 10.0923 5.66257L6.62009 9.13479C6.59573 9.17314 6.56682 9.20943 6.53333 9.24287C6.28787 9.48852 5.88965 9.48852 5.64402 9.24287L3.7059 7.18062C3.46046 6.93495 3.46046 6.537 3.7059 6.29133C3.95154 6.04579 4.34968 6.04579 4.59512 6.29133L6.09217 7.88432Z" fill="black" />
                                        </svg>
                                    </span>
                                    <span
                                        className={`m-auto text-center font-roboto capitalize text-sm font-semibold text-black ${index === focusedSlide ? "text-opacity-100" : "text-opacity-50"
                                            }`}>
                                        {t('verified')}
                                    </span>
                                </div>
                            </div>
                            <div
                                onClick={toggleDescription}
                                className="flex flex-col gap-y-2 flex-grow px-2">
                                <Typography
                                    variant="h3"
                                    className={`text-lg font-bold text-black ${index === focusedSlide ? "text-opacity-100" : "text-opacity-50"
                                        }`}
                                >
                                    {t(review.title)}
                                </Typography>
                                <p
                                    className={`mb-2 text-black text-[15px] ${index === focusedSlide ? "text-opacity-100" : "text-opacity-50"
                                        }`}
                                >
                                    {isExpanded ? t(review.description) : `${t(review.description).substring(0, 100)}...`}
                                </p>
                                <div className="flex-grow"></div>
                                <div className="text-gray-500 text-sm flex flex-col gap-y-2">
                                    <p>
                                        <span className="font-semibold">{review.name}</span>
                                    </p>
                                    <p>
                                        <span className="font-semibold">{t(review.date)}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </Slider>
        </div>
    );
};

export default CustomSwiper;
