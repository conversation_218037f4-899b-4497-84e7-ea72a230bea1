const User = require("../../models/User");
const Order = require("../../models/Order");
const Package = require("../../models/Package");
const Payment = require("../../models/Payment");
const Ticket = require("../../models/Ticket");
const SubOrder = require("../../models/SubOrder");
const adminLogger = require("../../utils/adminLogger");
const Category = require("../../models/Category");
const Brand = require("../../models/Brand");

/**
 * Get dashboard statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDashboardStats = async (req, res) => {
  try {
    // Get total users count
    let totalUsers = 0;
    try {
      totalUsers = await User.countDocuments();
    } catch (err) {
      console.error("Error counting users:", err);
    }

    // Get total packages count
    let totalPackages = 0;
    try {
      totalPackages = await Package.countDocuments();
    } catch (err) {
      console.error("Error counting packages:", err);
    }

    // Get total orders count
    let totalOrders = 0;
    try {
      totalOrders = await Order.countDocuments();
    } catch (err) {
      console.error("Error counting orders:", err);
    }

    // Get total revenue from completed orders
    let totalRevenue = 0;
    try {
      // Since Payment model doesn't have an amount field, we need to get the totalPrice from the Order
      const completedPayments = await Payment.find({
        status: "completed",
      }).populate("order", "totalPrice");

      // Calculate revenue from order totalPrice
      totalRevenue = completedPayments.reduce((sum, payment) => {
        // Check if order exists and has totalPrice
        if (payment.order && payment.order.totalPrice) {
          return sum + payment.order.totalPrice;
        }
        return sum;
      }, 0);
    } catch (err) {
      console.error("Error calculating revenue:", err);
    }

    // Get recent orders (last 5)
    let formattedRecentOrders = [];
    try {
      const recentOrders = await Order.find()
        .sort({ createdAt: -1 })
        .limit(5)
        .populate("user", "firstName lastName email")
        .lean();

      // Format recent orders for display
      formattedRecentOrders = recentOrders.map((order) => ({
        id: order._id,
        identifiant: order.identifiant,
        customer: order.user
          ? `${order.user.firstName} ${order.user.lastName}`
          : "Unknown",
        amount: order.totalPrice || 0,
        status: order.status || "UNKNOWN",
        date: order.createdAt
          ? order.createdAt.toISOString().split("T")[0]
          : "Unknown",
      }));
    } catch (err) {
      console.error("Error fetching recent orders:", err);
    }

    // Get recent support tickets (last 5)
    let recentTickets = [];
    try {
      const tickets = await Ticket.find()
        .sort({ createdAt: -1 })
        .limit(5)
        .populate("creator", "firstName lastName email")
        .lean();

      // Format recent tickets for display
      recentTickets = tickets.map((ticket) => ({
        id: ticket._id,
        identifiant: ticket.identifiant,
        subject: ticket.subject,
        customer: ticket.creator
          ? `${ticket.creator.firstName} ${ticket.creator.lastName}`
          : "Unknown",
        priority: ticket.priority,
        status: ticket.status,
        date: ticket.createdAt
          ? ticket.createdAt.toISOString().split("T")[0]
          : "Unknown",
      }));
    } catch (err) {
      console.error("Error fetching recent tickets:", err);
    }

    // Return all statistics
    // Get monthly revenue for the current year
    let monthlyRevenue = Array(12).fill(0);
    let weeklyRevenue = Array(7).fill(0); // Sunday to Saturday
    let yearlyRevenue = {}; // {year: total}
    try {
      const now = new Date();
      const currentYear = now.getFullYear();
      const completedPayments = await Payment.find({
        status: "completed",
      }).populate("order", "totalPrice createdAt");

      completedPayments.forEach((payment) => {
        if (
          payment.order &&
          payment.order.totalPrice &&
          payment.order.createdAt
        ) {
          const date = new Date(payment.order.createdAt);

          // Monthly revenue
          if (date.getFullYear() === currentYear) {
            const month = date.getMonth(); // 0 = Jan, 11 = Dec
            monthlyRevenue[month] += payment.order.totalPrice;
          }

          // Weekly revenue (last 7 days)
          const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
          if (diffDays < 7) {
            // 0 = today, 6 = 6 days ago
            const weekDay = 6 - diffDays; // so index 6 is today, 0 is 6 days ago
            if (weekDay >= 0 && weekDay < 7) {
              weeklyRevenue[weekDay] += payment.order.totalPrice;
            }
          }

          // Yearly revenue
          const year = date.getFullYear();
          if (!yearlyRevenue[year]) yearlyRevenue[year] = 0;
          yearlyRevenue[year] += payment.order.totalPrice;
        }
      });
    } catch (err) {
      console.error("Error calculating revenues:", err);
    }

    // Get SSL certificate summary for dashboard
    let sslSummary = {
      total: 0,
      active: 0,
      expiringSoon: 0,
      expired: 0,
      pending: 0
    };

    try {
      const subOrdersWithSSL = await SubOrder.find({
        'ssl': { $exists: true, $ne: {} }
      }).lean();

      const statusCounts = {
        PENDING: 0,
        PROCESSING: 0,
        PENDING_VALIDATION: 0,
        VALIDATED: 0,
        ISSUED: 0,
        INSTALLED: 0,
        EXPIRED: 0,
        REVOKED: 0,
        FAILED: 0
      };

      let expiringSoonCount = 0;
      const now = new Date();

      subOrdersWithSSL.forEach(subOrder => {
        if (subOrder.ssl && typeof subOrder.ssl === 'object') {
          // Handle both Map and plain object structures
          let certificates = [];
          if (subOrder.ssl instanceof Map) {
            certificates = Array.from(subOrder.ssl.values());
          } else {
            certificates = Object.values(subOrder.ssl);
          }

          certificates.forEach(cert => {
            if (cert && cert.status) {
              if (statusCounts.hasOwnProperty(cert.status)) {
                statusCounts[cert.status]++;
              }

              // Check for expiring soon certificates
              if (cert.expiresAt && (cert.status === 'ISSUED' || cert.status === 'INSTALLED')) {
                const expiryDate = new Date(cert.expiresAt);
                const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
                if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
                  expiringSoonCount++;
                }
              }
            }
          });
        }
      });

      sslSummary = {
        total: Object.values(statusCounts).reduce((sum, count) => sum + count, 0),
        active: statusCounts.ISSUED + statusCounts.INSTALLED,
        expiringSoon: expiringSoonCount,
        expired: statusCounts.EXPIRED,
        pending: statusCounts.PENDING + statusCounts.PROCESSING + statusCounts.PENDING_VALIDATION
      };
    } catch (err) {
      console.error("Error calculating SSL summary:", err);
    }

    const responseData = {
      success: true,
      data: {
        users: totalUsers,
        packages: totalPackages,
        orders: totalOrders,
        revenue: totalRevenue,
        monthlyRevenue,
        weeklyRevenue,
        yearlyRevenue,
        recentOrders: formattedRecentOrders,
        recentTickets: recentTickets,
        support: recentTickets.length,
        sslSummary: sslSummary,
      },
    };

    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching dashboard statistics",
      error: error.message,
    });
  }
};

// Get package distribution by category
exports.getPackageDistributionByCategory = async (req, res) => {
  try {
    const categoryId = req.query.categoryId;
    if (!categoryId) {
      return res.status(400).json({ success: false, message: "Category ID is required" });
    }

    // Find all brands in the category
    const brands = await Brand.find({ category: categoryId }).lean();
    // console.log(brands);

    const completedPayments = await Payment.find({
      status: "completed",
    }).populate({
      path: 'order',
      populate: {
        path: 'subOrders'
      }
    })
    // console.log(completedPayments);

    // Map package IDs to brand IDs
    const packageIdToBrandId = {};
    brands.forEach(brand => {
      brand.packages.forEach(pkgId => {
        packageIdToBrandId[pkgId.toString()] = brand._id.toString();
      });
    });

    const brandSales = {};
    const packageSales = {};
    brands.forEach(brand => {
      brandSales[brand._id.toString()] = 0;
      brand.packages.forEach(pkgId => {
        packageSales[pkgId.toString()] = 0;
      });
    });
    completedPayments.forEach(payment => {
      if (payment.order && payment.order.subOrders) {
        payment.order.subOrders.forEach(subOrder => {
          const pkgId = subOrder.package?.toString();
          const brandId = packageIdToBrandId[pkgId];
          if (brandId && brandSales.hasOwnProperty(brandId)) {
            const quantity = subOrder.quantity || 0;
            brandSales[brandId] += quantity;
            packageSales[pkgId] += quantity;
          }
        });
      }
    });


    // For each brand, count the number of packages
    const brandData = await Promise.all(
      brands.map(async (brand) => {
        const packages = await Package.find({ brand: brand._id });
        const packageCount = packages.length;
        return {
          brandId: brand._id,
          brandName: brand.name,
          sales: brandSales[brand._id.toString()] || 0,
          packageCount,
          packages: packages.map(pkg => ({
            packageId: pkg._id,
            name: pkg.name,
            sales: packageSales[pkg._id.toString()] || 0
          })),
        };
      })
    );

    res.status(200).json({
      success: true,
      data: brandData,
    });
  } catch (error) {
    console.error("Error fetching package distribution:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

/**
 * Get SSL certificate statistics for dashboard charts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSSLCertificateStats = async (req, res) => {
  try {
    console.log('Fetching SSL certificate statistics...');

    // Get all suborders that have SSL certificates
    const subOrdersWithSSL = await SubOrder.find({
      'ssl': { $exists: true, $ne: {} }
    }).lean();

    console.log(`Found ${subOrdersWithSSL.length} suborders with SSL certificates`);

    // If no SSL certificates found, return empty data structure
    if (subOrdersWithSSL.length === 0) {
      console.log('No SSL certificates found, returning empty data structure');
      return res.status(200).json({
        success: true,
        data: {
          summary: {
            total: 0,
            active: 0,
            expiringSoon: 0,
            expired: 0,
            pending: 0
          },
          statusBreakdown: {
            PENDING: 0,
            PROCESSING: 0,
            PENDING_VALIDATION: 0,
            VALIDATED: 0,
            ISSUED: 0,
            INSTALLED: 0,
            EXPIRED: 0,
            REVOKED: 0,
            FAILED: 0
          },
          monthlyTrends: {
            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            series: [
              {
                name: 'Pending',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
              },
              {
                name: 'Processing',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
              },
              {
                name: 'Issued',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
              },
              {
                name: 'Installed',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
              },
              {
                name: 'Expired',
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
              }
            ]
          }
        }
      });
    }

    // Initialize counters
    const statusCounts = {
      PENDING: 0,
      PROCESSING: 0,
      PENDING_VALIDATION: 0,
      VALIDATED: 0,
      ISSUED: 0,
      INSTALLED: 0,
      EXPIRED: 0,
      REVOKED: 0,
      FAILED: 0
    };

    // Initialize monthly data for current year (Jan to Dec)
    const now = new Date();
    const currentYear = now.getFullYear();
    const monthlyData = {
      PENDING: Array(12).fill(0),
      PROCESSING: Array(12).fill(0),
      ISSUED: Array(12).fill(0),
      INSTALLED: Array(12).fill(0),
      EXPIRED: Array(12).fill(0)
    };

    // Fixed month labels from Jan to Dec
    const monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Process each suborder's SSL certificates
    subOrdersWithSSL.forEach((subOrder, index) => {
      console.log(`Processing suborder ${index + 1}:`, {
        id: subOrder._id,
        sslType: typeof subOrder.ssl,
        sslKeys: subOrder.ssl ? Object.keys(subOrder.ssl) : 'none'
      });

      if (subOrder.ssl && typeof subOrder.ssl === 'object') {
        // Handle both Map and plain object structures
        let certificates = [];
        if (subOrder.ssl instanceof Map) {
          certificates = Array.from(subOrder.ssl.values());
        } else {
          certificates = Object.values(subOrder.ssl);
        }

        console.log(`Found ${certificates.length} certificates in suborder ${subOrder._id}`);

        certificates.forEach((cert, certIndex) => {
          console.log(`Certificate ${certIndex}:`, {
            status: cert?.status,
            domain: cert?.domain,
            issuedAt: cert?.issuedAt,
            expiresAt: cert?.expiresAt
          });

          if (cert && cert.status) {
            // Count by status
            if (statusCounts.hasOwnProperty(cert.status)) {
              statusCounts[cert.status]++;
            }

            // Process monthly data for current year
            const certDate = cert.issuedAt ? new Date(cert.issuedAt) : new Date(cert.createdAt || subOrder.createdAt);
            const certYear = certDate.getFullYear();
            const certMonth = certDate.getMonth(); // 0 = Jan, 11 = Dec

            // Only include certificates from the current year
            if (certYear === currentYear) {
              // Track by actual status (certMonth is already 0-11, perfect for array index)
              if (monthlyData.hasOwnProperty(cert.status)) {
                monthlyData[cert.status][certMonth]++;
              }
            }
          }
        });
      }
    });

    // Calculate totals for summary
    const totalActive = statusCounts.ISSUED + statusCounts.INSTALLED;
    const totalExpired = statusCounts.EXPIRED;
    const totalPending = statusCounts.PENDING + statusCounts.PROCESSING + statusCounts.PENDING_VALIDATION;

    // Calculate expiring soon (certificates that expire within 30 days)
    let expiringSoonCount = 0;
    subOrdersWithSSL.forEach(subOrder => {
      if (subOrder.ssl && typeof subOrder.ssl === 'object') {
        // Handle both Map and plain object structures
        let certificates = [];
        if (subOrder.ssl instanceof Map) {
          certificates = Array.from(subOrder.ssl.values());
        } else {
          certificates = Object.values(subOrder.ssl);
        }

        certificates.forEach(cert => {
          if (cert && cert.expiresAt && (cert.status === 'ISSUED' || cert.status === 'INSTALLED')) {
            const expiryDate = new Date(cert.expiresAt);
            const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
            if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
              expiringSoonCount++;
            }
          }
        });
      }
    });

    const responseData = {
      success: true,
      data: {
        summary: {
          total: Object.values(statusCounts).reduce((sum, count) => sum + count, 0),
          active: totalActive,
          expiringSoon: expiringSoonCount,
          expired: totalExpired,
          pending: totalPending
        },
        statusBreakdown: statusCounts,
        monthlyTrends: {
          categories: monthLabels,
          series: [
            {
              name: 'Pending',
              data: monthlyData.PENDING
            },
            {
              name: 'Processing',
              data: monthlyData.PROCESSING
            },
            {
              name: 'Issued',
              data: monthlyData.ISSUED
            },
            {
              name: 'Installed',
              data: monthlyData.INSTALLED
            },
            {
              name: 'Expired',
              data: monthlyData.EXPIRED
            }
          ]
        }
      }
    };

    console.log('SSL Stats Response:', JSON.stringify(responseData, null, 2));
    return res.status(200).json(responseData);
  } catch (error) {
    console.error("Error fetching SSL certificate stats:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching SSL certificate statistics",
      error: error.message,
    });
  }
};


