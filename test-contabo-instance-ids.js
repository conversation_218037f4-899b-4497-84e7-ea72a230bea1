/**
 * Test script to check Contabo instance ID formats
 */

const ContaboProvider = require('./backend/services/providers/ContaboProvider');

async function testContaboInstanceIds() {
  console.log('🧪 Testing Contabo Instance ID Formats');
  console.log('======================================');

  try {
    const contaboProvider = new ContaboProvider();
    
    console.log('\n1. Testing Contabo API connection...');
    
    // Test getting instances from Contabo API
    console.log('\n2. Fetching instances from Contabo API...');
    const instances = await contaboProvider.getCustomerVPS('test');
    
    console.log(`✅ Found ${instances.length} instances`);
    
    if (instances.length > 0) {
      console.log('\n3. Analyzing instance ID formats...');
      
      instances.forEach((instance, index) => {
        console.log(`\nInstance ${index + 1}:`);
        console.log(`  - ID: "${instance.id}" (type: ${typeof instance.id})`);
        console.log(`  - Name: "${instance.name}"`);
        console.log(`  - Status: "${instance.status}"`);
        console.log(`  - Is numeric: ${/^\d+$/.test(String(instance.id))}`);
        console.log(`  - Length: ${String(instance.id).length}`);
        
        if (instance.raw) {
          console.log(`  - Raw instanceId: "${instance.raw.instanceId}"`);
          console.log(`  - Raw id: "${instance.raw.id}"`);
        }
      });
      
      // Test rescue with the first instance
      const testInstance = instances[0];
      console.log(`\n4. Testing rescue validation with instance: ${testInstance.id}`);
      
      try {
        // This should validate the instance ID format
        const result = await contaboProvider.startRescueSystem(testInstance.id, {
          // Don't actually start rescue, just test validation
        });
        console.log('✅ Instance ID format is valid for rescue');
      } catch (error) {
        console.log(`❌ Instance ID validation failed: ${error.message}`);
      }
    } else {
      console.log('⚠️ No instances found to test');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      console.log('\n💡 This is expected if Contabo API credentials are not configured');
      console.log('   The test shows that the API connection logic is working');
    }
  }
}

// Run the test
testContaboInstanceIds();
