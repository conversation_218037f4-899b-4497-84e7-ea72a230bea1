const mongoose = require('mongoose');

const chatbotContextSchema = new mongoose.Schema(
  {
    content: {
      type: String,
      required: true,
      default: 'ZTech Engineering is a Moroccan technology company specializing in web development, cloud services, hosting, and cybersecurity.'
    },
    name: {
      type: String,
      required: true,
      default: 'default',
      unique: true
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    updatedBy: {
      type: String,
      default: 'system'
    }
  },
  { timestamps: true }
);

// Create a static method to get or create the default context
chatbotContextSchema.statics.getDefaultContext = async function() {
  let context = await this.findOne({ name: 'default' });
  
  if (!context) {
    // If no default context exists, create one
    context = await this.create({
      name: 'default',
      content: 'ZTech Engineering is a Moroccan technology company specializing in web development, cloud services, hosting, and cybersecurity.'
    });
  }
  
  return context;
};

const ChatbotContext = mongoose.model('ChatbotContext', chatbotContextSchema);

module.exports = ChatbotContext;
