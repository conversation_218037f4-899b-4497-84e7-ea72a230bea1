"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tailwind-merge";
exports.ids = ["vendor-chunks/tailwind-merge"];
exports.modules = {

/***/ "(ssr)/./node_modules/tailwind-merge/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./tailwind-merge.cjs.development.js */ \"(ssr)/./node_modules/tailwind-merge/dist/tailwind-merge.cjs.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdGFpbHdpbmQtbWVyZ2UvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFDQTtBQUVBLElBQUlBLEtBQXlCLEVBQWMsRUFFMUMsTUFBTTtJQUNMQyw2SkFBeUI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL3RhaWx3aW5kLW1lcmdlL2Rpc3QvaW5kZXguanM/OWE3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbid1c2Ugc3RyaWN0J1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vdGFpbHdpbmQtbWVyZ2UuY2pzLnByb2R1Y3Rpb24ubWluLmpzJylcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi90YWlsd2luZC1tZXJnZS5janMuZGV2ZWxvcG1lbnQuanMnKVxufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tailwind-merge/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tailwind-merge/dist/tailwind-merge.cjs.development.js":
/*!****************************************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/tailwind-merge.cjs.development.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n/**\r\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\r\n *\r\n * Specifically:\r\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\r\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\r\n *\r\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\r\n */ function twJoin() {\n    var index = 0;\n    var argument;\n    var resolvedValue;\n    var string = \"\";\n    while(index < arguments.length){\n        if (argument = arguments[index++]) {\n            if (resolvedValue = toValue(argument)) {\n                string && (string += \" \");\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n}\nfunction toValue(mix) {\n    if (typeof mix === \"string\") {\n        return mix;\n    }\n    var resolvedValue;\n    var string = \"\";\n    for(var k = 0; k < mix.length; k++){\n        if (mix[k]) {\n            if (resolvedValue = toValue(mix[k])) {\n                string && (string += \" \");\n                string += resolvedValue;\n            }\n        }\n    }\n    return string;\n}\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nvar CLASS_PART_SEPARATOR = \"-\";\nfunction createClassUtils(config) {\n    var classMap = createClassMap(config);\n    function getClassGroupId(className) {\n        var classParts = className.split(CLASS_PART_SEPARATOR); // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === \"\" && classParts.length !== 1) {\n            classParts.shift();\n        }\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n    }\n    function getConflictingClassGroupIds(classGroupId) {\n        return config.conflictingClassGroups[classGroupId] || [];\n    }\n    return {\n        getClassGroupId: getClassGroupId,\n        getConflictingClassGroupIds: getConflictingClassGroupIds\n    };\n}\nfunction getGroupRecursive(classParts, classPartObject) {\n    var _classPartObject$vali;\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId;\n    }\n    var currentClassPart = classParts[0];\n    var nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n    var classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart;\n    }\n    if (classPartObject.validators.length === 0) {\n        return undefined;\n    }\n    var classRest = classParts.join(CLASS_PART_SEPARATOR);\n    return (_classPartObject$vali = classPartObject.validators.find(function(_ref) {\n        var validator = _ref.validator;\n        return validator(classRest);\n    })) == null ? void 0 : _classPartObject$vali.classGroupId;\n}\nvar arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nfunction getGroupIdForArbitraryProperty(className) {\n    if (arbitraryPropertyRegex.test(className)) {\n        var arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n        var property = arbitraryPropertyClassName == null ? void 0 : arbitraryPropertyClassName.substring(0, arbitraryPropertyClassName.indexOf(\":\"));\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return \"arbitrary..\" + property;\n        }\n    }\n}\n/**\r\n * Exported for testing only\r\n */ function createClassMap(config) {\n    var theme = config.theme, prefix = config.prefix;\n    var classMap = {\n        nextPart: new Map(),\n        validators: []\n    };\n    var prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n    prefixedClassGroupEntries.forEach(function(_ref2) {\n        var classGroupId = _ref2[0], classGroup = _ref2[1];\n        processClassesRecursively(classGroup, classMap, classGroupId, theme);\n    });\n    return classMap;\n}\nfunction processClassesRecursively(classGroup, classPartObject, classGroupId, theme) {\n    classGroup.forEach(function(classDefinition) {\n        if (typeof classDefinition === \"string\") {\n            var classPartObjectToEdit = classDefinition === \"\" ? classPartObject : getPart(classPartObject, classDefinition);\n            classPartObjectToEdit.classGroupId = classGroupId;\n            return;\n        }\n        if (typeof classDefinition === \"function\") {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n                return;\n            }\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId: classGroupId\n            });\n            return;\n        }\n        Object.entries(classDefinition).forEach(function(_ref3) {\n            var key = _ref3[0], classGroup = _ref3[1];\n            processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n        });\n    });\n}\nfunction getPart(classPartObject, path) {\n    var currentClassPartObject = classPartObject;\n    path.split(CLASS_PART_SEPARATOR).forEach(function(pathPart) {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: []\n            });\n        }\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n    });\n    return currentClassPartObject;\n}\nfunction isThemeGetter(func) {\n    return func.isThemeGetter;\n}\nfunction getPrefixedClassGroupEntries(classGroupEntries, prefix) {\n    if (!prefix) {\n        return classGroupEntries;\n    }\n    return classGroupEntries.map(function(_ref4) {\n        var classGroupId = _ref4[0], classGroup = _ref4[1];\n        var prefixedClassGroup = classGroup.map(function(classDefinition) {\n            if (typeof classDefinition === \"string\") {\n                return prefix + classDefinition;\n            }\n            if (typeof classDefinition === \"object\") {\n                return Object.fromEntries(Object.entries(classDefinition).map(function(_ref5) {\n                    var key = _ref5[0], value = _ref5[1];\n                    return [\n                        prefix + key,\n                        value\n                    ];\n                }));\n            }\n            return classDefinition;\n        });\n        return [\n            classGroupId,\n            prefixedClassGroup\n        ];\n    });\n}\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nfunction createLruCache(maxCacheSize) {\n    if (maxCacheSize < 1) {\n        return {\n            get: function get() {\n                return undefined;\n            },\n            set: function set() {}\n        };\n    }\n    var cacheSize = 0;\n    var cache = new Map();\n    var previousCache = new Map();\n    function update(key, value) {\n        cache.set(key, value);\n        cacheSize++;\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0;\n            previousCache = cache;\n            cache = new Map();\n        }\n    }\n    return {\n        get: function get(key) {\n            var value = cache.get(key);\n            if (value !== undefined) {\n                return value;\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value);\n                return value;\n            }\n        },\n        set: function set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value);\n            } else {\n                update(key, value);\n            }\n        }\n    };\n}\nvar IMPORTANT_MODIFIER = \"!\";\nfunction createSplitModifiers(config) {\n    var separator = config.separator || \":\"; // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    return function splitModifiers(className) {\n        var bracketDepth = 0;\n        var modifiers = [];\n        var modifierStart = 0;\n        for(var index = 0; index < className.length; index++){\n            var _char = className[index];\n            if (bracketDepth === 0 && _char === separator[0]) {\n                if (separator.length === 1 || className.slice(index, index + separator.length) === separator) {\n                    modifiers.push(className.slice(modifierStart, index));\n                    modifierStart = index + separator.length;\n                }\n            }\n            if (_char === \"[\") {\n                bracketDepth++;\n            } else if (_char === \"]\") {\n                bracketDepth--;\n            }\n        }\n        var baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n        var hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n        var baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n        return {\n            modifiers: modifiers,\n            hasImportantModifier: hasImportantModifier,\n            baseClassName: baseClassName\n        };\n    };\n}\n/**\r\n * Sorts modifiers according to following schema:\r\n * - Predefined modifiers are sorted alphabetically\r\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\r\n */ function sortModifiers(modifiers) {\n    if (modifiers.length <= 1) {\n        return modifiers;\n    }\n    var sortedModifiers = [];\n    var unsortedModifiers = [];\n    modifiers.forEach(function(modifier) {\n        var isArbitraryVariant = modifier[0] === \"[\";\n        if (isArbitraryVariant) {\n            sortedModifiers.push.apply(sortedModifiers, unsortedModifiers.sort().concat([\n                modifier\n            ]));\n            unsortedModifiers = [];\n        } else {\n            unsortedModifiers.push(modifier);\n        }\n    });\n    sortedModifiers.push.apply(sortedModifiers, unsortedModifiers.sort());\n    return sortedModifiers;\n}\nfunction createConfigUtils(config) {\n    return _extends({\n        cache: createLruCache(config.cacheSize),\n        splitModifiers: createSplitModifiers(config)\n    }, createClassUtils(config));\n}\nvar SPLIT_CLASSES_REGEX = /\\s+/;\nfunction mergeClassList(classList, configUtils) {\n    var splitModifiers = configUtils.splitModifiers, getClassGroupId = configUtils.getClassGroupId, getConflictingClassGroupIds = configUtils.getConflictingClassGroupIds;\n    /**\r\n   * Set of classGroupIds in following format:\r\n   * `{importantModifier}{variantModifiers}{classGroupId}`\r\n   * @example 'float'\r\n   * @example 'hover:focus:bg-color'\r\n   * @example 'md:!pr'\r\n   */ var classGroupsInConflict = new Set();\n    return classList.trim().split(SPLIT_CLASSES_REGEX).map(function(originalClassName) {\n        var _splitModifiers = splitModifiers(originalClassName), modifiers = _splitModifiers.modifiers, hasImportantModifier = _splitModifiers.hasImportantModifier, baseClassName = _splitModifiers.baseClassName;\n        var classGroupId = getClassGroupId(baseClassName);\n        if (!classGroupId) {\n            return {\n                isTailwindClass: false,\n                originalClassName: originalClassName\n            };\n        }\n        var variantModifier = sortModifiers(modifiers).join(\":\");\n        var modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n        return {\n            isTailwindClass: true,\n            modifierId: modifierId,\n            classGroupId: classGroupId,\n            originalClassName: originalClassName\n        };\n    }).reverse() // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n    .filter(function(parsed) {\n        if (!parsed.isTailwindClass) {\n            return true;\n        }\n        var modifierId = parsed.modifierId, classGroupId = parsed.classGroupId;\n        var classId = modifierId + classGroupId;\n        if (classGroupsInConflict.has(classId)) {\n            return false;\n        }\n        classGroupsInConflict.add(classId);\n        getConflictingClassGroupIds(classGroupId).forEach(function(group) {\n            return classGroupsInConflict.add(modifierId + group);\n        });\n        return true;\n    }).reverse().map(function(parsed) {\n        return parsed.originalClassName;\n    }).join(\" \");\n}\nfunction createTailwindMerge() {\n    for(var _len = arguments.length, createConfig = new Array(_len), _key = 0; _key < _len; _key++){\n        createConfig[_key] = arguments[_key];\n    }\n    var configUtils;\n    var cacheGet;\n    var cacheSet;\n    var functionToCall = initTailwindMerge;\n    function initTailwindMerge(classList) {\n        var firstCreateConfig = createConfig[0], restCreateConfig = createConfig.slice(1);\n        var config = restCreateConfig.reduce(function(previousConfig, createConfigCurrent) {\n            return createConfigCurrent(previousConfig);\n        }, firstCreateConfig());\n        configUtils = createConfigUtils(config);\n        cacheGet = configUtils.cache.get;\n        cacheSet = configUtils.cache.set;\n        functionToCall = tailwindMerge;\n        return tailwindMerge(classList);\n    }\n    function tailwindMerge(classList) {\n        var cachedResult = cacheGet(classList);\n        if (cachedResult) {\n            return cachedResult;\n        }\n        var result = mergeClassList(classList, configUtils);\n        cacheSet(classList, result);\n        return result;\n    }\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments));\n    };\n}\nfunction fromTheme(key) {\n    var themeGetter = function themeGetter(theme) {\n        return theme[key] || [];\n    };\n    themeGetter.isThemeGetter = true;\n    return themeGetter;\n}\nvar arbitraryValueRegex = /^\\[(.+)\\]$/;\nvar fractionRegex = /^\\d+\\/\\d+$/;\nvar stringLengths = /*#__PURE__*/ new Set([\n    \"px\",\n    \"full\",\n    \"screen\"\n]);\nvar tshirtUnitRegex = /^(\\d+)?(xs|sm|md|lg|xl)$/;\nvar lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh)/; // Shadow always begins with x and y offset separated by underscore\nvar shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nfunction isLength(classPart) {\n    return !Number.isNaN(Number(classPart)) || stringLengths.has(classPart) || fractionRegex.test(classPart) || isArbitraryLength(classPart);\n}\nfunction isArbitraryLength(classPart) {\n    var _arbitraryValueRegex$;\n    var arbitraryValue = (_arbitraryValueRegex$ = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$[1];\n    if (arbitraryValue) {\n        return arbitraryValue.startsWith(\"length:\") || lengthUnitRegex.test(arbitraryValue);\n    }\n    return false;\n}\nfunction isArbitrarySize(classPart) {\n    var _arbitraryValueRegex$2;\n    var arbitraryValue = (_arbitraryValueRegex$2 = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$2[1];\n    return arbitraryValue ? arbitraryValue.startsWith(\"size:\") : false;\n}\nfunction isArbitraryPosition(classPart) {\n    var _arbitraryValueRegex$3;\n    var arbitraryValue = (_arbitraryValueRegex$3 = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$3[1];\n    return arbitraryValue ? arbitraryValue.startsWith(\"position:\") : false;\n}\nfunction isArbitraryUrl(classPart) {\n    var _arbitraryValueRegex$4;\n    var arbitraryValue = (_arbitraryValueRegex$4 = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$4[1];\n    return arbitraryValue ? arbitraryValue.startsWith(\"url(\") || arbitraryValue.startsWith(\"url:\") : false;\n}\nfunction isArbitraryNumber(classPart) {\n    var _arbitraryValueRegex$5;\n    var arbitraryValue = (_arbitraryValueRegex$5 = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$5[1];\n    return arbitraryValue ? !Number.isNaN(Number(arbitraryValue)) || arbitraryValue.startsWith(\"number:\") : false;\n}\n/**\r\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\r\n */ var isArbitraryWeight = isArbitraryNumber;\nfunction isInteger(classPart) {\n    var _arbitraryValueRegex$6;\n    var arbitraryValue = (_arbitraryValueRegex$6 = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$6[1];\n    if (arbitraryValue) {\n        return Number.isInteger(Number(arbitraryValue));\n    }\n    return Number.isInteger(Number(classPart));\n}\nfunction isArbitraryValue(classPart) {\n    return arbitraryValueRegex.test(classPart);\n}\nfunction isAny() {\n    return true;\n}\nfunction isTshirtSize(classPart) {\n    return tshirtUnitRegex.test(classPart);\n}\nfunction isArbitraryShadow(classPart) {\n    var _arbitraryValueRegex$7;\n    var arbitraryValue = (_arbitraryValueRegex$7 = arbitraryValueRegex.exec(classPart)) == null ? void 0 : _arbitraryValueRegex$7[1];\n    if (arbitraryValue) {\n        return shadowRegex.test(arbitraryValue);\n    }\n    return false;\n}\nvar validators = {\n    __proto__: null,\n    isLength: isLength,\n    isArbitraryLength: isArbitraryLength,\n    isArbitrarySize: isArbitrarySize,\n    isArbitraryPosition: isArbitraryPosition,\n    isArbitraryUrl: isArbitraryUrl,\n    isArbitraryNumber: isArbitraryNumber,\n    isArbitraryWeight: isArbitraryWeight,\n    isInteger: isInteger,\n    isArbitraryValue: isArbitraryValue,\n    isAny: isAny,\n    isTshirtSize: isTshirtSize,\n    isArbitraryShadow: isArbitraryShadow\n};\nfunction getDefaultConfig() {\n    var colors = fromTheme(\"colors\");\n    var spacing = fromTheme(\"spacing\");\n    var blur = fromTheme(\"blur\");\n    var brightness = fromTheme(\"brightness\");\n    var borderColor = fromTheme(\"borderColor\");\n    var borderRadius = fromTheme(\"borderRadius\");\n    var borderSpacing = fromTheme(\"borderSpacing\");\n    var borderWidth = fromTheme(\"borderWidth\");\n    var contrast = fromTheme(\"contrast\");\n    var grayscale = fromTheme(\"grayscale\");\n    var hueRotate = fromTheme(\"hueRotate\");\n    var invert = fromTheme(\"invert\");\n    var gap = fromTheme(\"gap\");\n    var gradientColorStops = fromTheme(\"gradientColorStops\");\n    var inset = fromTheme(\"inset\");\n    var margin = fromTheme(\"margin\");\n    var opacity = fromTheme(\"opacity\");\n    var padding = fromTheme(\"padding\");\n    var saturate = fromTheme(\"saturate\");\n    var scale = fromTheme(\"scale\");\n    var sepia = fromTheme(\"sepia\");\n    var skew = fromTheme(\"skew\");\n    var space = fromTheme(\"space\");\n    var translate = fromTheme(\"translate\");\n    var getOverscroll = function getOverscroll() {\n        return [\n            \"auto\",\n            \"contain\",\n            \"none\"\n        ];\n    };\n    var getOverflow = function getOverflow() {\n        return [\n            \"auto\",\n            \"hidden\",\n            \"clip\",\n            \"visible\",\n            \"scroll\"\n        ];\n    };\n    var getSpacingWithAuto = function getSpacingWithAuto() {\n        return [\n            \"auto\",\n            spacing\n        ];\n    };\n    var getLengthWithEmpty = function getLengthWithEmpty() {\n        return [\n            \"\",\n            isLength\n        ];\n    };\n    var getIntegerWithAuto = function getIntegerWithAuto() {\n        return [\n            \"auto\",\n            isInteger\n        ];\n    };\n    var getPositions = function getPositions() {\n        return [\n            \"bottom\",\n            \"center\",\n            \"left\",\n            \"left-bottom\",\n            \"left-top\",\n            \"right\",\n            \"right-bottom\",\n            \"right-top\",\n            \"top\"\n        ];\n    };\n    var getLineStyles = function getLineStyles() {\n        return [\n            \"solid\",\n            \"dashed\",\n            \"dotted\",\n            \"double\",\n            \"none\"\n        ];\n    };\n    var getBlendModes = function getBlendModes() {\n        return [\n            \"normal\",\n            \"multiply\",\n            \"screen\",\n            \"overlay\",\n            \"darken\",\n            \"lighten\",\n            \"color-dodge\",\n            \"color-burn\",\n            \"hard-light\",\n            \"soft-light\",\n            \"difference\",\n            \"exclusion\",\n            \"hue\",\n            \"saturation\",\n            \"color\",\n            \"luminosity\",\n            \"plus-lighter\"\n        ];\n    };\n    var getAlign = function getAlign() {\n        return [\n            \"start\",\n            \"end\",\n            \"center\",\n            \"between\",\n            \"around\",\n            \"evenly\"\n        ];\n    };\n    var getZeroAndEmpty = function getZeroAndEmpty() {\n        return [\n            \"\",\n            \"0\",\n            isArbitraryValue\n        ];\n    };\n    var getBreaks = function getBreaks() {\n        return [\n            \"auto\",\n            \"avoid\",\n            \"all\",\n            \"avoid-page\",\n            \"page\",\n            \"left\",\n            \"right\",\n            \"column\"\n        ];\n    };\n    return {\n        cacheSize: 500,\n        theme: {\n            colors: [\n                isAny\n            ],\n            spacing: [\n                isLength\n            ],\n            blur: [\n                \"none\",\n                \"\",\n                isTshirtSize,\n                isArbitraryLength\n            ],\n            brightness: [\n                isInteger\n            ],\n            borderColor: [\n                colors\n            ],\n            borderRadius: [\n                \"none\",\n                \"\",\n                \"full\",\n                isTshirtSize,\n                isArbitraryLength\n            ],\n            borderSpacing: [\n                spacing\n            ],\n            borderWidth: getLengthWithEmpty(),\n            contrast: [\n                isInteger\n            ],\n            grayscale: getZeroAndEmpty(),\n            hueRotate: [\n                isInteger\n            ],\n            invert: getZeroAndEmpty(),\n            gap: [\n                spacing\n            ],\n            gradientColorStops: [\n                colors\n            ],\n            inset: getSpacingWithAuto(),\n            margin: getSpacingWithAuto(),\n            opacity: [\n                isInteger\n            ],\n            padding: [\n                spacing\n            ],\n            saturate: [\n                isInteger\n            ],\n            scale: [\n                isInteger\n            ],\n            sepia: getZeroAndEmpty(),\n            skew: [\n                isInteger,\n                isArbitraryValue\n            ],\n            space: [\n                spacing\n            ],\n            translate: [\n                spacing\n            ]\n        },\n        classGroups: {\n            // Layout\n            /**\r\n       * Aspect Ratio\r\n       * @see https://tailwindcss.com/docs/aspect-ratio\r\n       */ aspect: [\n                {\n                    aspect: [\n                        \"auto\",\n                        \"square\",\n                        \"video\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Container\r\n       * @see https://tailwindcss.com/docs/container\r\n       */ container: [\n                \"container\"\n            ],\n            /**\r\n       * Columns\r\n       * @see https://tailwindcss.com/docs/columns\r\n       */ columns: [\n                {\n                    columns: [\n                        isTshirtSize\n                    ]\n                }\n            ],\n            /**\r\n       * Break After\r\n       * @see https://tailwindcss.com/docs/break-after\r\n       */ \"break-after\": [\n                {\n                    \"break-after\": getBreaks()\n                }\n            ],\n            /**\r\n       * Break Before\r\n       * @see https://tailwindcss.com/docs/break-before\r\n       */ \"break-before\": [\n                {\n                    \"break-before\": getBreaks()\n                }\n            ],\n            /**\r\n       * Break Inside\r\n       * @see https://tailwindcss.com/docs/break-inside\r\n       */ \"break-inside\": [\n                {\n                    \"break-inside\": [\n                        \"auto\",\n                        \"avoid\",\n                        \"avoid-page\",\n                        \"avoid-column\"\n                    ]\n                }\n            ],\n            /**\r\n       * Box Decoration Break\r\n       * @see https://tailwindcss.com/docs/box-decoration-break\r\n       */ \"box-decoration\": [\n                {\n                    \"box-decoration\": [\n                        \"slice\",\n                        \"clone\"\n                    ]\n                }\n            ],\n            /**\r\n       * Box Sizing\r\n       * @see https://tailwindcss.com/docs/box-sizing\r\n       */ box: [\n                {\n                    box: [\n                        \"border\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\r\n       * Display\r\n       * @see https://tailwindcss.com/docs/display\r\n       */ display: [\n                \"block\",\n                \"inline-block\",\n                \"inline\",\n                \"flex\",\n                \"inline-flex\",\n                \"table\",\n                \"inline-table\",\n                \"table-caption\",\n                \"table-cell\",\n                \"table-column\",\n                \"table-column-group\",\n                \"table-footer-group\",\n                \"table-header-group\",\n                \"table-row-group\",\n                \"table-row\",\n                \"flow-root\",\n                \"grid\",\n                \"inline-grid\",\n                \"contents\",\n                \"list-item\",\n                \"hidden\"\n            ],\n            /**\r\n       * Floats\r\n       * @see https://tailwindcss.com/docs/float\r\n       */ \"float\": [\n                {\n                    \"float\": [\n                        \"right\",\n                        \"left\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Clear\r\n       * @see https://tailwindcss.com/docs/clear\r\n       */ clear: [\n                {\n                    clear: [\n                        \"left\",\n                        \"right\",\n                        \"both\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Isolation\r\n       * @see https://tailwindcss.com/docs/isolation\r\n       */ isolation: [\n                \"isolate\",\n                \"isolation-auto\"\n            ],\n            /**\r\n       * Object Fit\r\n       * @see https://tailwindcss.com/docs/object-fit\r\n       */ \"object-fit\": [\n                {\n                    object: [\n                        \"contain\",\n                        \"cover\",\n                        \"fill\",\n                        \"none\",\n                        \"scale-down\"\n                    ]\n                }\n            ],\n            /**\r\n       * Object Position\r\n       * @see https://tailwindcss.com/docs/object-position\r\n       */ \"object-position\": [\n                {\n                    object: [].concat(getPositions(), [\n                        isArbitraryValue\n                    ])\n                }\n            ],\n            /**\r\n       * Overflow\r\n       * @see https://tailwindcss.com/docs/overflow\r\n       */ overflow: [\n                {\n                    overflow: getOverflow()\n                }\n            ],\n            /**\r\n       * Overflow X\r\n       * @see https://tailwindcss.com/docs/overflow\r\n       */ \"overflow-x\": [\n                {\n                    \"overflow-x\": getOverflow()\n                }\n            ],\n            /**\r\n       * Overflow Y\r\n       * @see https://tailwindcss.com/docs/overflow\r\n       */ \"overflow-y\": [\n                {\n                    \"overflow-y\": getOverflow()\n                }\n            ],\n            /**\r\n       * Overscroll Behavior\r\n       * @see https://tailwindcss.com/docs/overscroll-behavior\r\n       */ overscroll: [\n                {\n                    overscroll: getOverscroll()\n                }\n            ],\n            /**\r\n       * Overscroll Behavior X\r\n       * @see https://tailwindcss.com/docs/overscroll-behavior\r\n       */ \"overscroll-x\": [\n                {\n                    \"overscroll-x\": getOverscroll()\n                }\n            ],\n            /**\r\n       * Overscroll Behavior Y\r\n       * @see https://tailwindcss.com/docs/overscroll-behavior\r\n       */ \"overscroll-y\": [\n                {\n                    \"overscroll-y\": getOverscroll()\n                }\n            ],\n            /**\r\n       * Position\r\n       * @see https://tailwindcss.com/docs/position\r\n       */ position: [\n                \"static\",\n                \"fixed\",\n                \"absolute\",\n                \"relative\",\n                \"sticky\"\n            ],\n            /**\r\n       * Top / Right / Bottom / Left\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ inset: [\n                {\n                    inset: [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Right / Left\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ \"inset-x\": [\n                {\n                    \"inset-x\": [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Top / Bottom\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ \"inset-y\": [\n                {\n                    \"inset-y\": [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Top\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ top: [\n                {\n                    top: [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Right\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ right: [\n                {\n                    right: [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Bottom\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ bottom: [\n                {\n                    bottom: [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Left\r\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\r\n       */ left: [\n                {\n                    left: [\n                        inset\n                    ]\n                }\n            ],\n            /**\r\n       * Visibility\r\n       * @see https://tailwindcss.com/docs/visibility\r\n       */ visibility: [\n                \"visible\",\n                \"invisible\",\n                \"collapse\"\n            ],\n            /**\r\n       * Z-Index\r\n       * @see https://tailwindcss.com/docs/z-index\r\n       */ z: [\n                {\n                    z: [\n                        isInteger\n                    ]\n                }\n            ],\n            // Flexbox and Grid\n            /**\r\n       * Flex Basis\r\n       * @see https://tailwindcss.com/docs/flex-basis\r\n       */ basis: [\n                {\n                    basis: [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Flex Direction\r\n       * @see https://tailwindcss.com/docs/flex-direction\r\n       */ \"flex-direction\": [\n                {\n                    flex: [\n                        \"row\",\n                        \"row-reverse\",\n                        \"col\",\n                        \"col-reverse\"\n                    ]\n                }\n            ],\n            /**\r\n       * Flex Wrap\r\n       * @see https://tailwindcss.com/docs/flex-wrap\r\n       */ \"flex-wrap\": [\n                {\n                    flex: [\n                        \"wrap\",\n                        \"wrap-reverse\",\n                        \"nowrap\"\n                    ]\n                }\n            ],\n            /**\r\n       * Flex\r\n       * @see https://tailwindcss.com/docs/flex\r\n       */ flex: [\n                {\n                    flex: [\n                        \"1\",\n                        \"auto\",\n                        \"initial\",\n                        \"none\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Flex Grow\r\n       * @see https://tailwindcss.com/docs/flex-grow\r\n       */ grow: [\n                {\n                    grow: getZeroAndEmpty()\n                }\n            ],\n            /**\r\n       * Flex Shrink\r\n       * @see https://tailwindcss.com/docs/flex-shrink\r\n       */ shrink: [\n                {\n                    shrink: getZeroAndEmpty()\n                }\n            ],\n            /**\r\n       * Order\r\n       * @see https://tailwindcss.com/docs/order\r\n       */ order: [\n                {\n                    order: [\n                        \"first\",\n                        \"last\",\n                        \"none\",\n                        isInteger\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Template Columns\r\n       * @see https://tailwindcss.com/docs/grid-template-columns\r\n       */ \"grid-cols\": [\n                {\n                    \"grid-cols\": [\n                        isAny\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Column Start / End\r\n       * @see https://tailwindcss.com/docs/grid-column\r\n       */ \"col-start-end\": [\n                {\n                    col: [\n                        \"auto\",\n                        {\n                            span: [\n                                isInteger\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Column Start\r\n       * @see https://tailwindcss.com/docs/grid-column\r\n       */ \"col-start\": [\n                {\n                    \"col-start\": getIntegerWithAuto()\n                }\n            ],\n            /**\r\n       * Grid Column End\r\n       * @see https://tailwindcss.com/docs/grid-column\r\n       */ \"col-end\": [\n                {\n                    \"col-end\": getIntegerWithAuto()\n                }\n            ],\n            /**\r\n       * Grid Template Rows\r\n       * @see https://tailwindcss.com/docs/grid-template-rows\r\n       */ \"grid-rows\": [\n                {\n                    \"grid-rows\": [\n                        isAny\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Row Start / End\r\n       * @see https://tailwindcss.com/docs/grid-row\r\n       */ \"row-start-end\": [\n                {\n                    row: [\n                        \"auto\",\n                        {\n                            span: [\n                                isInteger\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Row Start\r\n       * @see https://tailwindcss.com/docs/grid-row\r\n       */ \"row-start\": [\n                {\n                    \"row-start\": getIntegerWithAuto()\n                }\n            ],\n            /**\r\n       * Grid Row End\r\n       * @see https://tailwindcss.com/docs/grid-row\r\n       */ \"row-end\": [\n                {\n                    \"row-end\": getIntegerWithAuto()\n                }\n            ],\n            /**\r\n       * Grid Auto Flow\r\n       * @see https://tailwindcss.com/docs/grid-auto-flow\r\n       */ \"grid-flow\": [\n                {\n                    \"grid-flow\": [\n                        \"row\",\n                        \"col\",\n                        \"dense\",\n                        \"row-dense\",\n                        \"col-dense\"\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Auto Columns\r\n       * @see https://tailwindcss.com/docs/grid-auto-columns\r\n       */ \"auto-cols\": [\n                {\n                    \"auto-cols\": [\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fr\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Grid Auto Rows\r\n       * @see https://tailwindcss.com/docs/grid-auto-rows\r\n       */ \"auto-rows\": [\n                {\n                    \"auto-rows\": [\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fr\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Gap\r\n       * @see https://tailwindcss.com/docs/gap\r\n       */ gap: [\n                {\n                    gap: [\n                        gap\n                    ]\n                }\n            ],\n            /**\r\n       * Gap X\r\n       * @see https://tailwindcss.com/docs/gap\r\n       */ \"gap-x\": [\n                {\n                    \"gap-x\": [\n                        gap\n                    ]\n                }\n            ],\n            /**\r\n       * Gap Y\r\n       * @see https://tailwindcss.com/docs/gap\r\n       */ \"gap-y\": [\n                {\n                    \"gap-y\": [\n                        gap\n                    ]\n                }\n            ],\n            /**\r\n       * Justify Content\r\n       * @see https://tailwindcss.com/docs/justify-content\r\n       */ \"justify-content\": [\n                {\n                    justify: getAlign()\n                }\n            ],\n            /**\r\n       * Justify Items\r\n       * @see https://tailwindcss.com/docs/justify-items\r\n       */ \"justify-items\": [\n                {\n                    \"justify-items\": [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\r\n       * Justify Self\r\n       * @see https://tailwindcss.com/docs/justify-self\r\n       */ \"justify-self\": [\n                {\n                    \"justify-self\": [\n                        \"auto\",\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\r\n       * Align Content\r\n       * @see https://tailwindcss.com/docs/align-content\r\n       */ \"align-content\": [\n                {\n                    content: [].concat(getAlign(), [\n                        \"baseline\"\n                    ])\n                }\n            ],\n            /**\r\n       * Align Items\r\n       * @see https://tailwindcss.com/docs/align-items\r\n       */ \"align-items\": [\n                {\n                    items: [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"baseline\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\r\n       * Align Self\r\n       * @see https://tailwindcss.com/docs/align-self\r\n       */ \"align-self\": [\n                {\n                    self: [\n                        \"auto\",\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\",\n                        \"baseline\"\n                    ]\n                }\n            ],\n            /**\r\n       * Place Content\r\n       * @see https://tailwindcss.com/docs/place-content\r\n       */ \"place-content\": [\n                {\n                    \"place-content\": [].concat(getAlign(), [\n                        \"baseline\",\n                        \"stretch\"\n                    ])\n                }\n            ],\n            /**\r\n       * Place Items\r\n       * @see https://tailwindcss.com/docs/place-items\r\n       */ \"place-items\": [\n                {\n                    \"place-items\": [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"baseline\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            /**\r\n       * Place Self\r\n       * @see https://tailwindcss.com/docs/place-self\r\n       */ \"place-self\": [\n                {\n                    \"place-self\": [\n                        \"auto\",\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"stretch\"\n                    ]\n                }\n            ],\n            // Spacing\n            /**\r\n       * Padding\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ p: [\n                {\n                    p: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Padding X\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ px: [\n                {\n                    px: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Padding Y\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ py: [\n                {\n                    py: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Padding Top\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ pt: [\n                {\n                    pt: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Padding Right\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ pr: [\n                {\n                    pr: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Padding Bottom\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ pb: [\n                {\n                    pb: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Padding Left\r\n       * @see https://tailwindcss.com/docs/padding\r\n       */ pl: [\n                {\n                    pl: [\n                        padding\n                    ]\n                }\n            ],\n            /**\r\n       * Margin\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ m: [\n                {\n                    m: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Margin X\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ mx: [\n                {\n                    mx: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Margin Y\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ my: [\n                {\n                    my: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Margin Top\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ mt: [\n                {\n                    mt: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Margin Right\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ mr: [\n                {\n                    mr: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Margin Bottom\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ mb: [\n                {\n                    mb: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Margin Left\r\n       * @see https://tailwindcss.com/docs/margin\r\n       */ ml: [\n                {\n                    ml: [\n                        margin\n                    ]\n                }\n            ],\n            /**\r\n       * Space Between X\r\n       * @see https://tailwindcss.com/docs/space\r\n       */ \"space-x\": [\n                {\n                    \"space-x\": [\n                        space\n                    ]\n                }\n            ],\n            /**\r\n       * Space Between X Reverse\r\n       * @see https://tailwindcss.com/docs/space\r\n       */ \"space-x-reverse\": [\n                \"space-x-reverse\"\n            ],\n            /**\r\n       * Space Between Y\r\n       * @see https://tailwindcss.com/docs/space\r\n       */ \"space-y\": [\n                {\n                    \"space-y\": [\n                        space\n                    ]\n                }\n            ],\n            /**\r\n       * Space Between Y Reverse\r\n       * @see https://tailwindcss.com/docs/space\r\n       */ \"space-y-reverse\": [\n                \"space-y-reverse\"\n            ],\n            // Sizing\n            /**\r\n       * Width\r\n       * @see https://tailwindcss.com/docs/width\r\n       */ w: [\n                {\n                    w: [\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Min-Width\r\n       * @see https://tailwindcss.com/docs/min-width\r\n       */ \"min-w\": [\n                {\n                    \"min-w\": [\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Max-Width\r\n       * @see https://tailwindcss.com/docs/max-width\r\n       */ \"max-w\": [\n                {\n                    \"max-w\": [\n                        \"0\",\n                        \"none\",\n                        \"full\",\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        \"prose\",\n                        {\n                            screen: [\n                                isTshirtSize\n                            ]\n                        },\n                        isTshirtSize,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\r\n       * Height\r\n       * @see https://tailwindcss.com/docs/height\r\n       */ h: [\n                {\n                    h: [\n                        spacing,\n                        \"auto\",\n                        \"min\",\n                        \"max\",\n                        \"fit\"\n                    ]\n                }\n            ],\n            /**\r\n       * Min-Height\r\n       * @see https://tailwindcss.com/docs/min-height\r\n       */ \"min-h\": [\n                {\n                    \"min-h\": [\n                        \"min\",\n                        \"max\",\n                        \"fit\",\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Max-Height\r\n       * @see https://tailwindcss.com/docs/max-height\r\n       */ \"max-h\": [\n                {\n                    \"max-h\": [\n                        spacing,\n                        \"min\",\n                        \"max\",\n                        \"fit\"\n                    ]\n                }\n            ],\n            // Typography\n            /**\r\n       * Font Size\r\n       * @see https://tailwindcss.com/docs/font-size\r\n       */ \"font-size\": [\n                {\n                    text: [\n                        \"base\",\n                        isTshirtSize,\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\r\n       * Font Smoothing\r\n       * @see https://tailwindcss.com/docs/font-smoothing\r\n       */ \"font-smoothing\": [\n                \"antialiased\",\n                \"subpixel-antialiased\"\n            ],\n            /**\r\n       * Font Style\r\n       * @see https://tailwindcss.com/docs/font-style\r\n       */ \"font-style\": [\n                \"italic\",\n                \"not-italic\"\n            ],\n            /**\r\n       * Font Weight\r\n       * @see https://tailwindcss.com/docs/font-weight\r\n       */ \"font-weight\": [\n                {\n                    font: [\n                        \"thin\",\n                        \"extralight\",\n                        \"light\",\n                        \"normal\",\n                        \"medium\",\n                        \"semibold\",\n                        \"bold\",\n                        \"extrabold\",\n                        \"black\",\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\r\n       * Font Family\r\n       * @see https://tailwindcss.com/docs/font-family\r\n       */ \"font-family\": [\n                {\n                    font: [\n                        isAny\n                    ]\n                }\n            ],\n            /**\r\n       * Font Variant Numeric\r\n       * @see https://tailwindcss.com/docs/font-variant-numeric\r\n       */ \"fvn-normal\": [\n                \"normal-nums\"\n            ],\n            /**\r\n       * Font Variant Numeric\r\n       * @see https://tailwindcss.com/docs/font-variant-numeric\r\n       */ \"fvn-ordinal\": [\n                \"ordinal\"\n            ],\n            /**\r\n       * Font Variant Numeric\r\n       * @see https://tailwindcss.com/docs/font-variant-numeric\r\n       */ \"fvn-slashed-zero\": [\n                \"slashed-zero\"\n            ],\n            /**\r\n       * Font Variant Numeric\r\n       * @see https://tailwindcss.com/docs/font-variant-numeric\r\n       */ \"fvn-figure\": [\n                \"lining-nums\",\n                \"oldstyle-nums\"\n            ],\n            /**\r\n       * Font Variant Numeric\r\n       * @see https://tailwindcss.com/docs/font-variant-numeric\r\n       */ \"fvn-spacing\": [\n                \"proportional-nums\",\n                \"tabular-nums\"\n            ],\n            /**\r\n       * Font Variant Numeric\r\n       * @see https://tailwindcss.com/docs/font-variant-numeric\r\n       */ \"fvn-fraction\": [\n                \"diagonal-fractions\",\n                \"stacked-fractons\"\n            ],\n            /**\r\n       * Letter Spacing\r\n       * @see https://tailwindcss.com/docs/letter-spacing\r\n       */ tracking: [\n                {\n                    tracking: [\n                        \"tighter\",\n                        \"tight\",\n                        \"normal\",\n                        \"wide\",\n                        \"wider\",\n                        \"widest\",\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\r\n       * Line Height\r\n       * @see https://tailwindcss.com/docs/line-height\r\n       */ leading: [\n                {\n                    leading: [\n                        \"none\",\n                        \"tight\",\n                        \"snug\",\n                        \"normal\",\n                        \"relaxed\",\n                        \"loose\",\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * List Style Type\r\n       * @see https://tailwindcss.com/docs/list-style-type\r\n       */ \"list-style-type\": [\n                {\n                    list: [\n                        \"none\",\n                        \"disc\",\n                        \"decimal\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * List Style Position\r\n       * @see https://tailwindcss.com/docs/list-style-position\r\n       */ \"list-style-position\": [\n                {\n                    list: [\n                        \"inside\",\n                        \"outside\"\n                    ]\n                }\n            ],\n            /**\r\n       * Placeholder Color\r\n       * @deprecated since Tailwind CSS v3.0.0\r\n       * @see https://tailwindcss.com/docs/placeholder-color\r\n       */ \"placeholder-color\": [\n                {\n                    placeholder: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Placeholder Opacity\r\n       * @see https://tailwindcss.com/docs/placeholder-opacity\r\n       */ \"placeholder-opacity\": [\n                {\n                    \"placeholder-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Text Alignment\r\n       * @see https://tailwindcss.com/docs/text-align\r\n       */ \"text-alignment\": [\n                {\n                    text: [\n                        \"left\",\n                        \"center\",\n                        \"right\",\n                        \"justify\",\n                        \"start\",\n                        \"end\"\n                    ]\n                }\n            ],\n            /**\r\n       * Text Color\r\n       * @see https://tailwindcss.com/docs/text-color\r\n       */ \"text-color\": [\n                {\n                    text: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Text Opacity\r\n       * @see https://tailwindcss.com/docs/text-opacity\r\n       */ \"text-opacity\": [\n                {\n                    \"text-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Text Decoration\r\n       * @see https://tailwindcss.com/docs/text-decoration\r\n       */ \"text-decoration\": [\n                \"underline\",\n                \"overline\",\n                \"line-through\",\n                \"no-underline\"\n            ],\n            /**\r\n       * Text Decoration Style\r\n       * @see https://tailwindcss.com/docs/text-decoration-style\r\n       */ \"text-decoration-style\": [\n                {\n                    decoration: [].concat(getLineStyles(), [\n                        \"wavy\"\n                    ])\n                }\n            ],\n            /**\r\n       * Text Decoration Thickness\r\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\r\n       */ \"text-decoration-thickness\": [\n                {\n                    decoration: [\n                        \"auto\",\n                        \"from-font\",\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Text Underline Offset\r\n       * @see https://tailwindcss.com/docs/text-underline-offset\r\n       */ \"underline-offset\": [\n                {\n                    \"underline-offset\": [\n                        \"auto\",\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Text Decoration Color\r\n       * @see https://tailwindcss.com/docs/text-decoration-color\r\n       */ \"text-decoration-color\": [\n                {\n                    decoration: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Text Transform\r\n       * @see https://tailwindcss.com/docs/text-transform\r\n       */ \"text-transform\": [\n                \"uppercase\",\n                \"lowercase\",\n                \"capitalize\",\n                \"normal-case\"\n            ],\n            /**\r\n       * Text Overflow\r\n       * @see https://tailwindcss.com/docs/text-overflow\r\n       */ \"text-overflow\": [\n                \"truncate\",\n                \"text-ellipsis\",\n                \"text-clip\"\n            ],\n            /**\r\n       * Text Indent\r\n       * @see https://tailwindcss.com/docs/text-indent\r\n       */ indent: [\n                {\n                    indent: [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Vertical Alignment\r\n       * @see https://tailwindcss.com/docs/vertical-align\r\n       */ \"vertical-align\": [\n                {\n                    align: [\n                        \"baseline\",\n                        \"top\",\n                        \"middle\",\n                        \"bottom\",\n                        \"text-top\",\n                        \"text-bottom\",\n                        \"sub\",\n                        \"super\",\n                        isArbitraryLength\n                    ]\n                }\n            ],\n            /**\r\n       * Whitespace\r\n       * @see https://tailwindcss.com/docs/whitespace\r\n       */ whitespace: [\n                {\n                    whitespace: [\n                        \"normal\",\n                        \"nowrap\",\n                        \"pre\",\n                        \"pre-line\",\n                        \"pre-wrap\"\n                    ]\n                }\n            ],\n            /**\r\n       * Word Break\r\n       * @see https://tailwindcss.com/docs/word-break\r\n       */ \"break\": [\n                {\n                    \"break\": [\n                        \"normal\",\n                        \"words\",\n                        \"all\",\n                        \"keep\"\n                    ]\n                }\n            ],\n            /**\r\n       * Content\r\n       * @see https://tailwindcss.com/docs/content\r\n       */ content: [\n                {\n                    content: [\n                        \"none\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Backgrounds\n            /**\r\n       * Background Attachment\r\n       * @see https://tailwindcss.com/docs/background-attachment\r\n       */ \"bg-attachment\": [\n                {\n                    bg: [\n                        \"fixed\",\n                        \"local\",\n                        \"scroll\"\n                    ]\n                }\n            ],\n            /**\r\n       * Background Clip\r\n       * @see https://tailwindcss.com/docs/background-clip\r\n       */ \"bg-clip\": [\n                {\n                    \"bg-clip\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\",\n                        \"text\"\n                    ]\n                }\n            ],\n            /**\r\n       * Background Opacity\r\n       * @deprecated since Tailwind CSS v3.0.0\r\n       * @see https://tailwindcss.com/docs/background-opacity\r\n       */ \"bg-opacity\": [\n                {\n                    \"bg-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Background Origin\r\n       * @see https://tailwindcss.com/docs/background-origin\r\n       */ \"bg-origin\": [\n                {\n                    \"bg-origin\": [\n                        \"border\",\n                        \"padding\",\n                        \"content\"\n                    ]\n                }\n            ],\n            /**\r\n       * Background Position\r\n       * @see https://tailwindcss.com/docs/background-position\r\n       */ \"bg-position\": [\n                {\n                    bg: [].concat(getPositions(), [\n                        isArbitraryPosition\n                    ])\n                }\n            ],\n            /**\r\n       * Background Repeat\r\n       * @see https://tailwindcss.com/docs/background-repeat\r\n       */ \"bg-repeat\": [\n                {\n                    bg: [\n                        \"no-repeat\",\n                        {\n                            repeat: [\n                                \"\",\n                                \"x\",\n                                \"y\",\n                                \"round\",\n                                \"space\"\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\r\n       * Background Size\r\n       * @see https://tailwindcss.com/docs/background-size\r\n       */ \"bg-size\": [\n                {\n                    bg: [\n                        \"auto\",\n                        \"cover\",\n                        \"contain\",\n                        isArbitrarySize\n                    ]\n                }\n            ],\n            /**\r\n       * Background Image\r\n       * @see https://tailwindcss.com/docs/background-image\r\n       */ \"bg-image\": [\n                {\n                    bg: [\n                        \"none\",\n                        {\n                            \"gradient-to\": [\n                                \"t\",\n                                \"tr\",\n                                \"r\",\n                                \"br\",\n                                \"b\",\n                                \"bl\",\n                                \"l\",\n                                \"tl\"\n                            ]\n                        },\n                        isArbitraryUrl\n                    ]\n                }\n            ],\n            /**\r\n       * Background Color\r\n       * @see https://tailwindcss.com/docs/background-color\r\n       */ \"bg-color\": [\n                {\n                    bg: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Gradient Color Stops From\r\n       * @see https://tailwindcss.com/docs/gradient-color-stops\r\n       */ \"gradient-from\": [\n                {\n                    from: [\n                        gradientColorStops\n                    ]\n                }\n            ],\n            /**\r\n       * Gradient Color Stops Via\r\n       * @see https://tailwindcss.com/docs/gradient-color-stops\r\n       */ \"gradient-via\": [\n                {\n                    via: [\n                        gradientColorStops\n                    ]\n                }\n            ],\n            /**\r\n       * Gradient Color Stops To\r\n       * @see https://tailwindcss.com/docs/gradient-color-stops\r\n       */ \"gradient-to\": [\n                {\n                    to: [\n                        gradientColorStops\n                    ]\n                }\n            ],\n            // Borders\n            /**\r\n       * Border Radius\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ rounded: [\n                {\n                    rounded: [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Top\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-t\": [\n                {\n                    \"rounded-t\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Right\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-r\": [\n                {\n                    \"rounded-r\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Bottom\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-b\": [\n                {\n                    \"rounded-b\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Left\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-l\": [\n                {\n                    \"rounded-l\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Top Left\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-tl\": [\n                {\n                    \"rounded-tl\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Top Right\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-tr\": [\n                {\n                    \"rounded-tr\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Bottom Right\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-br\": [\n                {\n                    \"rounded-br\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Radius Bottom Left\r\n       * @see https://tailwindcss.com/docs/border-radius\r\n       */ \"rounded-bl\": [\n                {\n                    \"rounded-bl\": [\n                        borderRadius\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w\": [\n                {\n                    border: [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width X\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w-x\": [\n                {\n                    \"border-x\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width Y\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w-y\": [\n                {\n                    \"border-y\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width Top\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w-t\": [\n                {\n                    \"border-t\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width Right\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w-r\": [\n                {\n                    \"border-r\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width Bottom\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w-b\": [\n                {\n                    \"border-b\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Width Left\r\n       * @see https://tailwindcss.com/docs/border-width\r\n       */ \"border-w-l\": [\n                {\n                    \"border-l\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Border Opacity\r\n       * @see https://tailwindcss.com/docs/border-opacity\r\n       */ \"border-opacity\": [\n                {\n                    \"border-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Border Style\r\n       * @see https://tailwindcss.com/docs/border-style\r\n       */ \"border-style\": [\n                {\n                    border: [].concat(getLineStyles(), [\n                        \"hidden\"\n                    ])\n                }\n            ],\n            /**\r\n       * Divide Width X\r\n       * @see https://tailwindcss.com/docs/divide-width\r\n       */ \"divide-x\": [\n                {\n                    \"divide-x\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Divide Width X Reverse\r\n       * @see https://tailwindcss.com/docs/divide-width\r\n       */ \"divide-x-reverse\": [\n                \"divide-x-reverse\"\n            ],\n            /**\r\n       * Divide Width Y\r\n       * @see https://tailwindcss.com/docs/divide-width\r\n       */ \"divide-y\": [\n                {\n                    \"divide-y\": [\n                        borderWidth\n                    ]\n                }\n            ],\n            /**\r\n       * Divide Width Y Reverse\r\n       * @see https://tailwindcss.com/docs/divide-width\r\n       */ \"divide-y-reverse\": [\n                \"divide-y-reverse\"\n            ],\n            /**\r\n       * Divide Opacity\r\n       * @see https://tailwindcss.com/docs/divide-opacity\r\n       */ \"divide-opacity\": [\n                {\n                    \"divide-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Divide Style\r\n       * @see https://tailwindcss.com/docs/divide-style\r\n       */ \"divide-style\": [\n                {\n                    divide: getLineStyles()\n                }\n            ],\n            /**\r\n       * Border Color\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color\": [\n                {\n                    border: [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Border Color X\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color-x\": [\n                {\n                    \"border-x\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Border Color Y\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color-y\": [\n                {\n                    \"border-y\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Border Color Top\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color-t\": [\n                {\n                    \"border-t\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Border Color Right\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color-r\": [\n                {\n                    \"border-r\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Border Color Bottom\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color-b\": [\n                {\n                    \"border-b\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Border Color Left\r\n       * @see https://tailwindcss.com/docs/border-color\r\n       */ \"border-color-l\": [\n                {\n                    \"border-l\": [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Divide Color\r\n       * @see https://tailwindcss.com/docs/divide-color\r\n       */ \"divide-color\": [\n                {\n                    divide: [\n                        borderColor\n                    ]\n                }\n            ],\n            /**\r\n       * Outline Style\r\n       * @see https://tailwindcss.com/docs/outline-style\r\n       */ \"outline-style\": [\n                {\n                    outline: [\n                        \"\"\n                    ].concat(getLineStyles())\n                }\n            ],\n            /**\r\n       * Outline Offset\r\n       * @see https://tailwindcss.com/docs/outline-offset\r\n       */ \"outline-offset\": [\n                {\n                    \"outline-offset\": [\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Outline Width\r\n       * @see https://tailwindcss.com/docs/outline-width\r\n       */ \"outline-w\": [\n                {\n                    outline: [\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Outline Color\r\n       * @see https://tailwindcss.com/docs/outline-color\r\n       */ \"outline-color\": [\n                {\n                    outline: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Ring Width\r\n       * @see https://tailwindcss.com/docs/ring-width\r\n       */ \"ring-w\": [\n                {\n                    ring: getLengthWithEmpty()\n                }\n            ],\n            /**\r\n       * Ring Width Inset\r\n       * @see https://tailwindcss.com/docs/ring-width\r\n       */ \"ring-w-inset\": [\n                \"ring-inset\"\n            ],\n            /**\r\n       * Ring Color\r\n       * @see https://tailwindcss.com/docs/ring-color\r\n       */ \"ring-color\": [\n                {\n                    ring: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Ring Opacity\r\n       * @see https://tailwindcss.com/docs/ring-opacity\r\n       */ \"ring-opacity\": [\n                {\n                    \"ring-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Ring Offset Width\r\n       * @see https://tailwindcss.com/docs/ring-offset-width\r\n       */ \"ring-offset-w\": [\n                {\n                    \"ring-offset\": [\n                        isLength\n                    ]\n                }\n            ],\n            /**\r\n       * Ring Offset Color\r\n       * @see https://tailwindcss.com/docs/ring-offset-color\r\n       */ \"ring-offset-color\": [\n                {\n                    \"ring-offset\": [\n                        colors\n                    ]\n                }\n            ],\n            // Effects\n            /**\r\n       * Box Shadow\r\n       * @see https://tailwindcss.com/docs/box-shadow\r\n       */ shadow: [\n                {\n                    shadow: [\n                        \"\",\n                        \"inner\",\n                        \"none\",\n                        isTshirtSize,\n                        isArbitraryShadow\n                    ]\n                }\n            ],\n            /**\r\n       * Box Shadow Color\r\n       * @see https://tailwindcss.com/docs/box-shadow-color\r\n       */ \"shadow-color\": [\n                {\n                    shadow: [\n                        isAny\n                    ]\n                }\n            ],\n            /**\r\n       * Opacity\r\n       * @see https://tailwindcss.com/docs/opacity\r\n       */ opacity: [\n                {\n                    opacity: [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Mix Beldn Mode\r\n       * @see https://tailwindcss.com/docs/mix-blend-mode\r\n       */ \"mix-blend\": [\n                {\n                    \"mix-blend\": getBlendModes()\n                }\n            ],\n            /**\r\n       * Background Blend Mode\r\n       * @see https://tailwindcss.com/docs/background-blend-mode\r\n       */ \"bg-blend\": [\n                {\n                    \"bg-blend\": getBlendModes()\n                }\n            ],\n            // Filters\n            /**\r\n       * Filter\r\n       * @deprecated since Tailwind CSS v3.0.0\r\n       * @see https://tailwindcss.com/docs/filter\r\n       */ filter: [\n                {\n                    filter: [\n                        \"\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Blur\r\n       * @see https://tailwindcss.com/docs/blur\r\n       */ blur: [\n                {\n                    blur: [\n                        blur\n                    ]\n                }\n            ],\n            /**\r\n       * Brightness\r\n       * @see https://tailwindcss.com/docs/brightness\r\n       */ brightness: [\n                {\n                    brightness: [\n                        brightness\n                    ]\n                }\n            ],\n            /**\r\n       * Contrast\r\n       * @see https://tailwindcss.com/docs/contrast\r\n       */ contrast: [\n                {\n                    contrast: [\n                        contrast\n                    ]\n                }\n            ],\n            /**\r\n       * Drop Shadow\r\n       * @see https://tailwindcss.com/docs/drop-shadow\r\n       */ \"drop-shadow\": [\n                {\n                    \"drop-shadow\": [\n                        \"\",\n                        \"none\",\n                        isTshirtSize,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Grayscale\r\n       * @see https://tailwindcss.com/docs/grayscale\r\n       */ grayscale: [\n                {\n                    grayscale: [\n                        grayscale\n                    ]\n                }\n            ],\n            /**\r\n       * Hue Rotate\r\n       * @see https://tailwindcss.com/docs/hue-rotate\r\n       */ \"hue-rotate\": [\n                {\n                    \"hue-rotate\": [\n                        hueRotate\n                    ]\n                }\n            ],\n            /**\r\n       * Invert\r\n       * @see https://tailwindcss.com/docs/invert\r\n       */ invert: [\n                {\n                    invert: [\n                        invert\n                    ]\n                }\n            ],\n            /**\r\n       * Saturate\r\n       * @see https://tailwindcss.com/docs/saturate\r\n       */ saturate: [\n                {\n                    saturate: [\n                        saturate\n                    ]\n                }\n            ],\n            /**\r\n       * Sepia\r\n       * @see https://tailwindcss.com/docs/sepia\r\n       */ sepia: [\n                {\n                    sepia: [\n                        sepia\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Filter\r\n       * @deprecated since Tailwind CSS v3.0.0\r\n       * @see https://tailwindcss.com/docs/backdrop-filter\r\n       */ \"backdrop-filter\": [\n                {\n                    \"backdrop-filter\": [\n                        \"\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Blur\r\n       * @see https://tailwindcss.com/docs/backdrop-blur\r\n       */ \"backdrop-blur\": [\n                {\n                    \"backdrop-blur\": [\n                        blur\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Brightness\r\n       * @see https://tailwindcss.com/docs/backdrop-brightness\r\n       */ \"backdrop-brightness\": [\n                {\n                    \"backdrop-brightness\": [\n                        brightness\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Contrast\r\n       * @see https://tailwindcss.com/docs/backdrop-contrast\r\n       */ \"backdrop-contrast\": [\n                {\n                    \"backdrop-contrast\": [\n                        contrast\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Grayscale\r\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\r\n       */ \"backdrop-grayscale\": [\n                {\n                    \"backdrop-grayscale\": [\n                        grayscale\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Hue Rotate\r\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\r\n       */ \"backdrop-hue-rotate\": [\n                {\n                    \"backdrop-hue-rotate\": [\n                        hueRotate\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Invert\r\n       * @see https://tailwindcss.com/docs/backdrop-invert\r\n       */ \"backdrop-invert\": [\n                {\n                    \"backdrop-invert\": [\n                        invert\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Opacity\r\n       * @see https://tailwindcss.com/docs/backdrop-opacity\r\n       */ \"backdrop-opacity\": [\n                {\n                    \"backdrop-opacity\": [\n                        opacity\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Saturate\r\n       * @see https://tailwindcss.com/docs/backdrop-saturate\r\n       */ \"backdrop-saturate\": [\n                {\n                    \"backdrop-saturate\": [\n                        saturate\n                    ]\n                }\n            ],\n            /**\r\n       * Backdrop Sepia\r\n       * @see https://tailwindcss.com/docs/backdrop-sepia\r\n       */ \"backdrop-sepia\": [\n                {\n                    \"backdrop-sepia\": [\n                        sepia\n                    ]\n                }\n            ],\n            // Tables\n            /**\r\n       * Border Collapse\r\n       * @see https://tailwindcss.com/docs/border-collapse\r\n       */ \"border-collapse\": [\n                {\n                    border: [\n                        \"collapse\",\n                        \"separate\"\n                    ]\n                }\n            ],\n            /**\r\n       * Border Spacing\r\n       * @see https://tailwindcss.com/docs/border-spacing\r\n       */ \"border-spacing\": [\n                {\n                    \"border-spacing\": [\n                        borderSpacing\n                    ]\n                }\n            ],\n            /**\r\n       * Border Spacing X\r\n       * @see https://tailwindcss.com/docs/border-spacing\r\n       */ \"border-spacing-x\": [\n                {\n                    \"border-spacing-x\": [\n                        borderSpacing\n                    ]\n                }\n            ],\n            /**\r\n       * Border Spacing Y\r\n       * @see https://tailwindcss.com/docs/border-spacing\r\n       */ \"border-spacing-y\": [\n                {\n                    \"border-spacing-y\": [\n                        borderSpacing\n                    ]\n                }\n            ],\n            /**\r\n       * Table Layout\r\n       * @see https://tailwindcss.com/docs/table-layout\r\n       */ \"table-layout\": [\n                {\n                    table: [\n                        \"auto\",\n                        \"fixed\"\n                    ]\n                }\n            ],\n            // Transitions and Animation\n            /**\r\n       * Tranisition Property\r\n       * @see https://tailwindcss.com/docs/transition-property\r\n       */ transition: [\n                {\n                    transition: [\n                        \"none\",\n                        \"all\",\n                        \"\",\n                        \"colors\",\n                        \"opacity\",\n                        \"shadow\",\n                        \"transform\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Transition Duration\r\n       * @see https://tailwindcss.com/docs/transition-duration\r\n       */ duration: [\n                {\n                    duration: [\n                        isInteger\n                    ]\n                }\n            ],\n            /**\r\n       * Transition Timing Function\r\n       * @see https://tailwindcss.com/docs/transition-timing-function\r\n       */ ease: [\n                {\n                    ease: [\n                        \"linear\",\n                        \"in\",\n                        \"out\",\n                        \"in-out\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Transition Delay\r\n       * @see https://tailwindcss.com/docs/transition-delay\r\n       */ delay: [\n                {\n                    delay: [\n                        isInteger\n                    ]\n                }\n            ],\n            /**\r\n       * Animation\r\n       * @see https://tailwindcss.com/docs/animation\r\n       */ animate: [\n                {\n                    animate: [\n                        \"none\",\n                        \"spin\",\n                        \"ping\",\n                        \"pulse\",\n                        \"bounce\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Transforms\n            /**\r\n       * Transform\r\n       * @see https://tailwindcss.com/docs/transform\r\n       */ transform: [\n                {\n                    transform: [\n                        \"\",\n                        \"gpu\",\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Scale\r\n       * @see https://tailwindcss.com/docs/scale\r\n       */ scale: [\n                {\n                    scale: [\n                        scale\n                    ]\n                }\n            ],\n            /**\r\n       * Scale X\r\n       * @see https://tailwindcss.com/docs/scale\r\n       */ \"scale-x\": [\n                {\n                    \"scale-x\": [\n                        scale\n                    ]\n                }\n            ],\n            /**\r\n       * Scale Y\r\n       * @see https://tailwindcss.com/docs/scale\r\n       */ \"scale-y\": [\n                {\n                    \"scale-y\": [\n                        scale\n                    ]\n                }\n            ],\n            /**\r\n       * Rotate\r\n       * @see https://tailwindcss.com/docs/rotate\r\n       */ rotate: [\n                {\n                    rotate: [\n                        isInteger,\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Translate X\r\n       * @see https://tailwindcss.com/docs/translate\r\n       */ \"translate-x\": [\n                {\n                    \"translate-x\": [\n                        translate\n                    ]\n                }\n            ],\n            /**\r\n       * Translate Y\r\n       * @see https://tailwindcss.com/docs/translate\r\n       */ \"translate-y\": [\n                {\n                    \"translate-y\": [\n                        translate\n                    ]\n                }\n            ],\n            /**\r\n       * Skew X\r\n       * @see https://tailwindcss.com/docs/skew\r\n       */ \"skew-x\": [\n                {\n                    \"skew-x\": [\n                        skew\n                    ]\n                }\n            ],\n            /**\r\n       * Skew Y\r\n       * @see https://tailwindcss.com/docs/skew\r\n       */ \"skew-y\": [\n                {\n                    \"skew-y\": [\n                        skew\n                    ]\n                }\n            ],\n            /**\r\n       * Transform Origin\r\n       * @see https://tailwindcss.com/docs/transform-origin\r\n       */ \"transform-origin\": [\n                {\n                    origin: [\n                        \"center\",\n                        \"top\",\n                        \"top-right\",\n                        \"right\",\n                        \"bottom-right\",\n                        \"bottom\",\n                        \"bottom-left\",\n                        \"left\",\n                        \"top-left\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // Interactivity\n            /**\r\n       * Accent Color\r\n       * @see https://tailwindcss.com/docs/accent-color\r\n       */ accent: [\n                {\n                    accent: [\n                        \"auto\",\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Appearance\r\n       * @see https://tailwindcss.com/docs/appearance\r\n       */ appearance: [\n                \"appearance-none\"\n            ],\n            /**\r\n       * Cursor\r\n       * @see https://tailwindcss.com/docs/cursor\r\n       */ cursor: [\n                {\n                    cursor: [\n                        \"auto\",\n                        \"default\",\n                        \"pointer\",\n                        \"wait\",\n                        \"text\",\n                        \"move\",\n                        \"help\",\n                        \"not-allowed\",\n                        \"none\",\n                        \"context-menu\",\n                        \"progress\",\n                        \"cell\",\n                        \"crosshair\",\n                        \"vertical-text\",\n                        \"alias\",\n                        \"copy\",\n                        \"no-drop\",\n                        \"grab\",\n                        \"grabbing\",\n                        \"all-scroll\",\n                        \"col-resize\",\n                        \"row-resize\",\n                        \"n-resize\",\n                        \"e-resize\",\n                        \"s-resize\",\n                        \"w-resize\",\n                        \"ne-resize\",\n                        \"nw-resize\",\n                        \"se-resize\",\n                        \"sw-resize\",\n                        \"ew-resize\",\n                        \"ns-resize\",\n                        \"nesw-resize\",\n                        \"nwse-resize\",\n                        \"zoom-in\",\n                        \"zoom-out\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            /**\r\n       * Caret Color\r\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\r\n       */ \"caret-color\": [\n                {\n                    caret: [\n                        colors\n                    ]\n                }\n            ],\n            /**\r\n       * Pointer Events\r\n       * @see https://tailwindcss.com/docs/pointer-events\r\n       */ \"pointer-events\": [\n                {\n                    \"pointer-events\": [\n                        \"none\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\r\n       * Resize\r\n       * @see https://tailwindcss.com/docs/resize\r\n       */ resize: [\n                {\n                    resize: [\n                        \"none\",\n                        \"y\",\n                        \"x\",\n                        \"\"\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Behavior\r\n       * @see https://tailwindcss.com/docs/scroll-behavior\r\n       */ \"scroll-behavior\": [\n                {\n                    scroll: [\n                        \"auto\",\n                        \"smooth\"\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-m\": [\n                {\n                    \"scroll-m\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin X\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-mx\": [\n                {\n                    \"scroll-mx\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin Y\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-my\": [\n                {\n                    \"scroll-my\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin Top\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-mt\": [\n                {\n                    \"scroll-mt\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin Right\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-mr\": [\n                {\n                    \"scroll-mr\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin Bottom\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-mb\": [\n                {\n                    \"scroll-mb\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Margin Left\r\n       * @see https://tailwindcss.com/docs/scroll-margin\r\n       */ \"scroll-ml\": [\n                {\n                    \"scroll-ml\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-p\": [\n                {\n                    \"scroll-p\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding X\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-px\": [\n                {\n                    \"scroll-px\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding Y\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-py\": [\n                {\n                    \"scroll-py\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding Top\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-pt\": [\n                {\n                    \"scroll-pt\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding Right\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-pr\": [\n                {\n                    \"scroll-pr\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding Bottom\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-pb\": [\n                {\n                    \"scroll-pb\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Padding Left\r\n       * @see https://tailwindcss.com/docs/scroll-padding\r\n       */ \"scroll-pl\": [\n                {\n                    \"scroll-pl\": [\n                        spacing\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Snap Align\r\n       * @see https://tailwindcss.com/docs/scroll-snap-align\r\n       */ \"snap-align\": [\n                {\n                    snap: [\n                        \"start\",\n                        \"end\",\n                        \"center\",\n                        \"align-none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Snap Stop\r\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\r\n       */ \"snap-stop\": [\n                {\n                    snap: [\n                        \"normal\",\n                        \"always\"\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Snap Type\r\n       * @see https://tailwindcss.com/docs/scroll-snap-type\r\n       */ \"snap-type\": [\n                {\n                    snap: [\n                        \"none\",\n                        \"x\",\n                        \"y\",\n                        \"both\"\n                    ]\n                }\n            ],\n            /**\r\n       * Scroll Snap Type Strictness\r\n       * @see https://tailwindcss.com/docs/scroll-snap-type\r\n       */ \"snap-strictness\": [\n                {\n                    snap: [\n                        \"mandatory\",\n                        \"proximity\"\n                    ]\n                }\n            ],\n            /**\r\n       * Touch Action\r\n       * @see https://tailwindcss.com/docs/touch-action\r\n       */ touch: [\n                {\n                    touch: [\n                        \"auto\",\n                        \"none\",\n                        \"pinch-zoom\",\n                        \"manipulation\",\n                        {\n                            pan: [\n                                \"x\",\n                                \"left\",\n                                \"right\",\n                                \"y\",\n                                \"up\",\n                                \"down\"\n                            ]\n                        }\n                    ]\n                }\n            ],\n            /**\r\n       * User Select\r\n       * @see https://tailwindcss.com/docs/user-select\r\n       */ select: [\n                {\n                    select: [\n                        \"none\",\n                        \"text\",\n                        \"all\",\n                        \"auto\"\n                    ]\n                }\n            ],\n            /**\r\n       * Will Change\r\n       * @see https://tailwindcss.com/docs/will-change\r\n       */ \"will-change\": [\n                {\n                    \"will-change\": [\n                        \"auto\",\n                        \"scroll\",\n                        \"contents\",\n                        \"transform\",\n                        isArbitraryValue\n                    ]\n                }\n            ],\n            // SVG\n            /**\r\n       * Fill\r\n       * @see https://tailwindcss.com/docs/fill\r\n       */ fill: [\n                {\n                    fill: [\n                        colors,\n                        \"none\"\n                    ]\n                }\n            ],\n            /**\r\n       * Stroke Width\r\n       * @see https://tailwindcss.com/docs/stroke-width\r\n       */ \"stroke-w\": [\n                {\n                    stroke: [\n                        isLength,\n                        isArbitraryNumber\n                    ]\n                }\n            ],\n            /**\r\n       * Stroke\r\n       * @see https://tailwindcss.com/docs/stroke\r\n       */ stroke: [\n                {\n                    stroke: [\n                        colors,\n                        \"none\"\n                    ]\n                }\n            ],\n            // Accessibility\n            /**\r\n       * Screen Readers\r\n       * @see https://tailwindcss.com/docs/screen-readers\r\n       */ sr: [\n                \"sr-only\",\n                \"not-sr-only\"\n            ]\n        },\n        conflictingClassGroups: {\n            overflow: [\n                \"overflow-x\",\n                \"overflow-y\"\n            ],\n            overscroll: [\n                \"overscroll-x\",\n                \"overscroll-y\"\n            ],\n            inset: [\n                \"inset-x\",\n                \"inset-y\",\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ],\n            \"inset-x\": [\n                \"right\",\n                \"left\"\n            ],\n            \"inset-y\": [\n                \"top\",\n                \"bottom\"\n            ],\n            flex: [\n                \"basis\",\n                \"grow\",\n                \"shrink\"\n            ],\n            gap: [\n                \"gap-x\",\n                \"gap-y\"\n            ],\n            p: [\n                \"px\",\n                \"py\",\n                \"pt\",\n                \"pr\",\n                \"pb\",\n                \"pl\"\n            ],\n            px: [\n                \"pr\",\n                \"pl\"\n            ],\n            py: [\n                \"pt\",\n                \"pb\"\n            ],\n            m: [\n                \"mx\",\n                \"my\",\n                \"mt\",\n                \"mr\",\n                \"mb\",\n                \"ml\"\n            ],\n            mx: [\n                \"mr\",\n                \"ml\"\n            ],\n            my: [\n                \"mt\",\n                \"mb\"\n            ],\n            \"font-size\": [\n                \"leading\"\n            ],\n            \"fvn-normal\": [\n                \"fvn-ordinal\",\n                \"fvn-slashed-zero\",\n                \"fvn-figure\",\n                \"fvn-spacing\",\n                \"fvn-fraction\"\n            ],\n            \"fvn-ordinal\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-slashed-zero\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-figure\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-spacing\": [\n                \"fvn-normal\"\n            ],\n            \"fvn-fraction\": [\n                \"fvn-normal\"\n            ],\n            rounded: [\n                \"rounded-t\",\n                \"rounded-r\",\n                \"rounded-b\",\n                \"rounded-l\",\n                \"rounded-tl\",\n                \"rounded-tr\",\n                \"rounded-br\",\n                \"rounded-bl\"\n            ],\n            \"rounded-t\": [\n                \"rounded-tl\",\n                \"rounded-tr\"\n            ],\n            \"rounded-r\": [\n                \"rounded-tr\",\n                \"rounded-br\"\n            ],\n            \"rounded-b\": [\n                \"rounded-br\",\n                \"rounded-bl\"\n            ],\n            \"rounded-l\": [\n                \"rounded-tl\",\n                \"rounded-bl\"\n            ],\n            \"border-spacing\": [\n                \"border-spacing-x\",\n                \"border-spacing-y\"\n            ],\n            \"border-w\": [\n                \"border-w-t\",\n                \"border-w-r\",\n                \"border-w-b\",\n                \"border-w-l\"\n            ],\n            \"border-w-x\": [\n                \"border-w-r\",\n                \"border-w-l\"\n            ],\n            \"border-w-y\": [\n                \"border-w-t\",\n                \"border-w-b\"\n            ],\n            \"border-color\": [\n                \"border-color-t\",\n                \"border-color-r\",\n                \"border-color-b\",\n                \"border-color-l\"\n            ],\n            \"border-color-x\": [\n                \"border-color-r\",\n                \"border-color-l\"\n            ],\n            \"border-color-y\": [\n                \"border-color-t\",\n                \"border-color-b\"\n            ],\n            \"scroll-m\": [\n                \"scroll-mx\",\n                \"scroll-my\",\n                \"scroll-mt\",\n                \"scroll-mr\",\n                \"scroll-mb\",\n                \"scroll-ml\"\n            ],\n            \"scroll-mx\": [\n                \"scroll-mr\",\n                \"scroll-ml\"\n            ],\n            \"scroll-my\": [\n                \"scroll-mt\",\n                \"scroll-mb\"\n            ],\n            \"scroll-p\": [\n                \"scroll-px\",\n                \"scroll-py\",\n                \"scroll-pt\",\n                \"scroll-pr\",\n                \"scroll-pb\",\n                \"scroll-pl\"\n            ],\n            \"scroll-px\": [\n                \"scroll-pr\",\n                \"scroll-pl\"\n            ],\n            \"scroll-py\": [\n                \"scroll-pt\",\n                \"scroll-pb\"\n            ]\n        }\n    };\n}\nvar twMerge = /*#__PURE__*/ createTailwindMerge(getDefaultConfig);\n/**\r\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\r\n * @param configExtension Partial config to merge into the `baseConfig`.\r\n */ function mergeConfigs(baseConfig, configExtension) {\n    for(var key in configExtension){\n        mergePropertyRecursively(baseConfig, key, configExtension[key]);\n    }\n    return baseConfig;\n}\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar overrideTypes = /*#__PURE__*/ new Set([\n    \"string\",\n    \"number\",\n    \"boolean\"\n]);\nfunction mergePropertyRecursively(baseObject, mergeKey, mergeValue) {\n    if (!hasOwnProperty.call(baseObject, mergeKey) || overrideTypes.has(typeof mergeValue) || mergeValue === null) {\n        baseObject[mergeKey] = mergeValue;\n        return;\n    }\n    if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n        baseObject[mergeKey] = baseObject[mergeKey].concat(mergeValue);\n        return;\n    }\n    if (typeof mergeValue === \"object\" && typeof baseObject[mergeKey] === \"object\") {\n        if (baseObject[mergeKey] === null) {\n            baseObject[mergeKey] = mergeValue;\n            return;\n        }\n        for(var nextKey in mergeValue){\n            mergePropertyRecursively(baseObject[mergeKey], nextKey, mergeValue[nextKey]);\n        }\n    }\n}\nfunction extendTailwindMerge(configExtension) {\n    for(var _len = arguments.length, createConfig = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        createConfig[_key - 1] = arguments[_key];\n    }\n    return typeof configExtension === \"function\" ? createTailwindMerge.apply(void 0, [\n        getDefaultConfig,\n        configExtension\n    ].concat(createConfig)) : createTailwindMerge.apply(void 0, [\n        function() {\n            return mergeConfigs(getDefaultConfig(), configExtension);\n        }\n    ].concat(createConfig));\n}\n/**\r\n * @deprecated Will be removed in next major version. Use `twJoin` instead.\r\n */ var join = twJoin;\nexports.createTailwindMerge = createTailwindMerge;\nexports.extendTailwindMerge = extendTailwindMerge;\nexports.fromTheme = fromTheme;\nexports.getDefaultConfig = getDefaultConfig;\nexports.join = join;\nexports.mergeConfigs = mergeConfigs;\nexports.twJoin = twJoin;\nexports.twMerge = twMerge;\nexports.validators = validators; //# sourceMappingURL=tailwind-merge.cjs.development.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tailwind-merge/dist/tailwind-merge.cjs.development.js\n");

/***/ })

};
;