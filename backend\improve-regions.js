/**
 * <PERSON><PERSON><PERSON> to improve existing regions with better data
 * This will update regions with real Contabo data and proper status
 */

const mongoose = require('mongoose');
const VPSRegion = require('./models/VPSRegion');
const VPSProviderFactory = require('./services/providers/VPSProviderFactory');
require('dotenv').config();

// Mapping of region IDs to better data
const regionMappings = {
  'ind': {
    name: 'Asia (India)',
    country: 'India',
    countryCode: 'IN',
    continent: 'Asia',
    city: 'Mumbai',
    flag: '🇮🇳',
    description: 'Mumbai, India'
  },
  'us-east': {
    name: 'United States (East)',
    country: 'United States',
    countryCode: 'US',
    continent: 'North America',
    city: 'New York',
    flag: '🇺🇸',
    description: 'New York, United States'
  },
  'sin': {
    name: 'Asia (Singapore)',
    country: 'Singapore',
    countryCode: 'SG',
    continent: 'Asia',
    city: 'Singapore',
    flag: '🇸🇬',
    description: 'Singapore'
  },
  'eu': {
    name: 'Europe (Germany)',
    country: 'Germany',
    countryCode: 'DE',
    continent: 'Europe',
    city: 'Nuremberg',
    flag: '🇩🇪',
    description: 'Nuremberg, Germany'
  },
  'us-central': {
    name: 'United States (Central)',
    country: 'United States',
    countryCode: 'US',
    continent: 'North America',
    city: 'St. Louis',
    flag: '🇺🇸',
    description: 'St. Louis, United States'
  },
  'aus': {
    name: 'Australia (Sydney)',
    country: 'Australia',
    countryCode: 'AU',
    continent: 'Oceania',
    city: 'Sydney',
    flag: '🇦🇺',
    description: 'Sydney, Australia'
  }
};

async function improveRegions() {
  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zn_ztech');
    console.log('✅ Connected to MongoDB');

    // Get all existing regions
    console.log('🔍 Finding existing regions...');
    const existingRegions = await VPSRegion.find();
    console.log(`Found ${existingRegions.length} existing regions`);

    let updated = 0;

    for (const region of existingRegions) {
      try {
        console.log(`\n🔧 Processing region: ${region.name} (${region.regionId})`);
        
        let hasChanges = false;
        const updates = {};

        // Set status to active if not set
        if (!region.status || region.status === null || region.status === undefined) {
          updates.status = 'active';
          hasChanges = true;
          console.log('   - Setting status to active');
        }

        // Improve region data if we have better mapping
        const betterData = regionMappings[region.regionId];
        if (betterData) {
          if (region.country === 'Unknown' || !region.country) {
            updates.country = betterData.country;
            hasChanges = true;
            console.log(`   - Updating country: ${betterData.country}`);
          }

          if (region.city === 'Unknown' || !region.city) {
            updates.city = betterData.city;
            hasChanges = true;
            console.log(`   - Updating city: ${betterData.city}`);
          }

          if (region.flag === '🌍' || !region.flag) {
            updates.flag = betterData.flag;
            hasChanges = true;
            console.log(`   - Updating flag: ${betterData.flag}`);
          }

          if (!region.countryCode || region.countryCode === 'XX') {
            updates.countryCode = betterData.countryCode;
            hasChanges = true;
            console.log(`   - Updating country code: ${betterData.countryCode}`);
          }

          if (!region.continent || region.continent === 'Unknown') {
            updates.continent = betterData.continent;
            hasChanges = true;
            console.log(`   - Updating continent: ${betterData.continent}`);
          }

          if (!region.description || region.description.includes('Unknown')) {
            updates.description = betterData.description;
            hasChanges = true;
            console.log(`   - Updating description: ${betterData.description}`);
          }
        }

        // Set default values for missing fields
        if (!region.isPopular) {
          updates.isPopular = ['us-east', 'eu', 'sin'].includes(region.regionId);
          hasChanges = true;
          console.log(`   - Setting isPopular: ${updates.isPopular}`);
        }

        if (!region.displayOrder) {
          const orderMap = { 'eu': 1, 'us-east': 2, 'sin': 3, 'ind': 4, 'us-central': 5, 'aus': 6 };
          updates.displayOrder = orderMap[region.regionId] || 10;
          hasChanges = true;
          console.log(`   - Setting displayOrder: ${updates.displayOrder}`);
        }

        // Apply updates if any
        if (hasChanges) {
          await VPSRegion.updateOne({ _id: region._id }, { $set: updates });
          console.log('   ✅ Region updated successfully');
          updated++;
        } else {
          console.log('   ⏭️ No changes needed');
        }

      } catch (error) {
        console.error(`   ❌ Error updating region ${region.regionId}:`, error.message);
      }
    }

    console.log(`\n🎉 Region improvement completed!`);
    console.log(`📊 Summary: Updated ${updated} out of ${existingRegions.length} regions`);

    // Verify the results
    console.log('\n📋 Final verification...');
    const activeRegions = await VPSRegion.find({ status: 'active' }).sort({ displayOrder: 1 });
    console.log(`Active regions: ${activeRegions.length}`);
    
    activeRegions.forEach(region => {
      console.log(`   - ${region.name} (${region.regionId})`);
      console.log(`     Country: ${region.country}, City: ${region.city}`);
      console.log(`     Flag: ${region.flag}, Popular: ${region.isPopular}`);
      console.log(`     Pricing entries: ${region.pricing?.length || 0}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Improvement failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the improvement
if (require.main === module) {
  improveRegions();
}

module.exports = { improveRegions };
