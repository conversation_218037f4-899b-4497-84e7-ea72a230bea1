import axios from 'axios';
import { BACKEND_URL } from '../config/constant';

/**
 * Service for managing notification settings
 */
const notificationSettingsService = {
  /**
   * Get all notification settings
   * @returns {Promise} - Promise that resolves to notification settings
   */
  getNotificationSettings: async () => {
    try {
      const response = await axios.get(`${BACKEND_URL}/admin/notification-settings`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      throw error;
    }
  },

  /**
   * Get a specific notification setting by type
   * @param {string} type - The notification type (e.g., 'abandoned_cart')
   * @returns {Promise} - Promise that resolves to the notification setting
   */
  getNotificationSettingByType: async (type) => {
    try {
      const response = await axios.get(`${BACKEND_URL}/admin/notification-settings/${type}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching notification setting for type ${type}:`, error);
      throw error;
    }
  },

  /**
   * Update a notification setting
   * @param {string} type - The notification type (e.g., 'abandoned_cart')
   * @param {Object} data - The updated notification setting data
   * @returns {Promise} - Promise that resolves to the updated notification setting
   */
  updateNotificationSetting: async (type, data) => {
    try {
      const response = await axios.put(`${BACKEND_URL}/admin/notification-settings/${type}`, data, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating notification setting for type ${type}:`, error);
      throw error;
    }
  },

  /**
   * Manually trigger a notification process
   * @param {string} type - The notification type (e.g., 'abandoned_cart')
   * @returns {Promise} - Promise that resolves when the notification process is triggered
   */
  triggerNotification: async (type) => {
    try {
      const response = await axios.post(`${BACKEND_URL}/admin/notification-settings/${type}/trigger`, {}, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error(`Error triggering notification for type ${type}:`, error);
      throw error;
    }
  }
};

export default notificationSettingsService;
