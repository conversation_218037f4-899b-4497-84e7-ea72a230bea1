import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Typography } from "@material-tailwind/react";

const CloudAdvantagesMobile = ({ advantages, t }) => {

    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: false,
                },
            },
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                },
            },
        ],
    };

    return (
        <div className="md:hidden relative my-8">
            <Slider {...settings}>
                {advantages.map((advantage) => (
                    <div key={advantage.id} className="flex flex-col items-center text-center p-4">
                        <div className="bg-transparent p-4 mb-4 flex justify-center">{advantage.icon}</div>
                        <Typography variant="h3" className="text-lg font-semibold">
                            {t(advantage.title)}
                        </Typography>
                        <p className="text-gray-400 mt-2 text-sm">{t(advantage.description)}</p>
                    </div>
                ))}
            </Slider>
        </div>
    );
};

export default CloudAdvantagesMobile;
