const express = require('express');
const { createSpecification, getSpecifications, getSpecification, updateSpecification, deleteSpecification } = require('../controllers/specificationController');
const specificationRouter = express.Router();

specificationRouter.post('/create-specification', createSpecification);
specificationRouter.get('/get-specifications', getSpecifications);
specificationRouter.get('/get-specification/:id', getSpecification);
specificationRouter.put('/update-specification/:id', updateSpecification);
specificationRouter.delete('/delete-specification/:id', deleteSpecification);

module.exports = specificationRouter;
