import { Typography } from "@material-tailwind/react";

const CloudAdvantagesDesktop = ({ advantages, t }) => {
    return (
        <div className="hidden md:flex flex-col md:flex-row justify-around text-center space-y-6 md:space-y-0 mb-8">
            {advantages.map((advantage) => (
                <div key={advantage.id} className="flex flex-col items-center">
                    <div className="bg-transparent p-4 mb-4">{advantage.icon}</div>
                    <Typography variant="h3" className="text-lg font-semibold">
                        {t(advantage.title)}
                    </Typography>
                    <p className="text-gray-400 mt-2 text-sm">{t(advantage.description)}</p>
                </div>
            ))}
        </div>
    );
};

export default CloudAdvantagesDesktop;
