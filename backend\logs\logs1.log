2024-07-22 09:35:37:3537 info: Server is running on port 5000 7/22/2024:9:35:37 AM
2024-07-22 10:06:40:640 info: Server is running on port 5000 7/22/2024:10:06:39 AM
2024-07-22 10:09:33:933 info: Server is running on port 5000 7/22/2024:10:09:33 AM
2024-07-22 10:09:41:941 info: Server is running on port 5000 7/22/2024:10:09:41 AM
2024-07-22 10:10:07:107 info: Server is running on port 5000 7/22/2024:10:10:07 AM
2024-07-22 10:10:32:1032 info: Server is running on port 5000 7/22/2024:10:10:32 AM
2024-07-22 10:10:55:1055 info: Server is running on port 5000 7/22/2024:10:10:55 AM
2024-07-22 10:11:25:1125 info: Server is running on port 5000 7/22/2024:10:11:25 AM
2024-07-22 10:12:21:1221 info: Server is running on port 5000 7/22/2024:10:12:21 AM
2024-07-22 10:12:36:1236 info: Server is running on port 5000 7/22/2024:10:12:36 AM
2024-07-22 10:12:53:1253 info: Server is running on port 5000 7/22/2024:10:12:53 AM
2024-07-22 10:13:04:134 info: Server is running on port 5000 7/22/2024:10:13:04 AM
2024-07-22 10:13:11:1311 info: Server is running on port 5000 7/22/2024:10:13:11 AM
2024-07-22 10:14:32:1432 info: Server is running on port 5000 7/22/2024:10:14:32 AM
2024-07-22 10:15:08:158 info: Server is running on port 5000 7/22/2024:10:15:08 AM
2024-07-22 10:15:14:1514 info: Server is running on port 5000 7/22/2024:10:15:14 AM
2024-07-22 10:16:26:1626 info: Server is running on port 5000 7/22/2024:10:16:26 AM
2024-07-22 10:16:39:1639 info: Server is running on port 5000 7/22/2024:10:16:39 AM
2024-07-22 10:16:49:1649 info: Server is running on port 5000 7/22/2024:10:16:49 AM
2024-07-22 10:17:02:172 info: Server is running on port 5000 7/22/2024:10:17:02 AM
2024-07-22 10:17:15:1715 info: Server is running on port 5000 7/22/2024:10:17:15 AM
2024-07-22 10:17:40:1740 info: Server is running on port 5000 7/22/2024:10:17:40 AM
2024-07-22 10:17:54:1754 info: Server is running on port 5000 7/22/2024:10:17:54 AM
2024-07-22 10:18:31:1831 info: Server is running on port 5000 7/22/2024:10:18:31 AM
2024-07-22 10:18:51:1851 info: Server is running on port 5000 7/22/2024:10:18:51 AM
2024-07-22 10:19:06:196 info: Server is running on port 5000 7/22/2024:10:19:06 AM
2024-07-22 10:19:33:1933 info: Server is running on port 5000 7/22/2024:10:19:33 AM
2024-07-22 10:19:39:1939 info: Server is running on port 5000 7/22/2024:10:19:39 AM
2024-07-22 10:20:13:2013 info: Server is running on port 5000 7/22/2024:10:20:13 AM
2024-07-22 10:22:54:2254 info: Server is running on port 5000 7/22/2024:10:22:54 AM
2024-07-22 10:47:03:473 info: Server is running on port 5000 7/22/2024:10:47:03 AM
2024-07-22 10:47:35:4735 info: Server is running on port 5000 7/22/2024:10:47:35 AM
2024-07-22 10:48:13:4813 info: Server is running on port 5000 7/22/2024:10:48:13 AM
2024-07-22 10:48:25:4825 info: Server is running on port 5000 7/22/2024:10:48:25 AM
2024-07-22 10:48:34:4834 info: Server is running on port 5000 7/22/2024:10:48:34 AM
2024-07-22 10:51:05:515 info: Server is running on port 5000 7/22/2024:10:51:05 AM
2024-07-22 10:51:16:5116 info: Server is running on port 5000 7/22/2024:10:51:16 AM
2024-07-22 10:53:59:5359 info: Server is running on port 5000 7/22/2024:10:53:59 AM
2024-07-22 10:56:13:5613 info: Server is running on port 5000 7/22/2024:10:56:13 AM
2024-07-22 10:59:21:5921 info: Server is running on port 5000 7/22/2024:10:59:21 AM
2024-08-11 01:40:36:4036 info: this inquery 66b6a9dd1686010a082cfbc6 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-08-07T00:33:21.201Z,
  __v: 0,
  photo: '/productImages/1693390217342unnamed.jpg',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr'
} 8/11/2024:1:40:36 AM
2024-08-15 17:33:30:3330 info: this inquery 66be27afbeafa36b66ac7ee3 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-08-15T11:14:20.287Z,
  __v: 0,
  photo: '/productImages/1693390217342unnamed.jpg',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr'
} 8/15/2024:5:33:29 PM
2024-09-08 17:26:05:265 info: warn:-Error: Either sender or senderFromAdmin must be provided. 9/8/2024:5:26:05 PM
2024-09-08 17:26:06:266 info: warn:-Error: Either sender or senderFromAdmin must be provided. 9/8/2024:5:26:06 PM
2024-09-08 17:26:07:267 info: warn:-Error: Either sender or senderFromAdmin must be provided. 9/8/2024:5:26:07 PM
2024-09-08 17:26:20:2620 info: warn:-Error: Greeting never received 9/8/2024:5:26:20 PM
2024-09-08 17:26:28:2628 info: warn:-Error: Either sender or senderFromAdmin must be provided. 9/8/2024:5:26:28 PM
2024-09-08 17:28:29:2829 info: warn:-Error: read ECONNRESET 9/8/2024:5:28:29 PM
2024-09-18 13:26:58:2658 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:26:58 PM
2024-09-18 13:26:58:2658 info: warn:-ReferenceError: updateInquiryWithPaymentMethod is not defined 9/18/2024:1:26:58 PM
2024-09-18 13:28:42:2842 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:28:42 PM
2024-09-18 13:28:42:2842 info: warn:-ReferenceError: updateInquiryWithPaymentMethod is not defined 9/18/2024:1:28:42 PM
2024-09-18 13:52:57:5257 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:52:56 PM
2024-09-18 13:54:05:545 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:54:05 PM
2024-09-18 13:55:35:5535 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:55:35 PM
2024-09-18 13:55:35:5535 info: warn:-TypeError: Cannot read properties of undefined (reading 'status') 9/18/2024:1:55:35 PM
2024-09-18 13:55:53:5553 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:55:53 PM
2024-09-18 13:55:53:5553 info: warn:-TypeError: Cannot read properties of undefined (reading 'status') 9/18/2024:1:55:53 PM
2024-09-18 13:57:36:5736 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:57:36 PM
2024-09-18 13:57:36:5736 info: warn:-ReferenceError: scheduledPaymentDate is not defined 9/18/2024:1:57:36 PM
2024-09-18 13:58:15:5815 info: this inquery 66e2e6fb468ca9d2f4d55f08 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T10:00:19.421Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eaa4b3e12ba56ec2f96eb4")
} 9/18/2024:1:58:15 PM
2024-09-18 13:58:15:5815 info: warn:-TypeError: createNewNotification is not a function 9/18/2024:1:58:15 PM
2024-09-18 14:44:08:448 info: warn:-TypeError: createNewNotification is not a function 9/18/2024:2:44:08 PM
2024-09-18 14:44:11:4411 info: warn:-TypeError: createNewNotification is not a function 9/18/2024:2:44:11 PM
2024-09-18 14:44:12:4412 info: warn:-TypeError: createNewNotification is not a function 9/18/2024:2:44:12 PM
2024-09-18 14:46:49:4649 info: warn:-TypeError: createNewNotification is not a function 9/18/2024:2:46:49 PM
2024-09-18 14:46:52:4652 info: warn:-TypeError: createNewNotification is not a function 9/18/2024:2:46:52 PM
2024-09-18 14:57:58:5758 info: this inquery 66ead91ff010ca70d53f32d3 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T13:57:31.565Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethodType: 'BankAccount',
  paymentMethod: new ObjectId("66eadc4b08a17119c49c0043")
} 9/18/2024:2:57:58 PM
2024-09-19 08:56:27:5627 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:8:56:27 AM
2024-09-19 08:57:00:570 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:8:57:00 AM
2024-09-19 08:58:43:5843 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:8:58:43 AM
2024-09-19 09:05:28:528 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:9:05:28 AM
2024-09-19 09:06:23:623 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:9:06:23 AM
2024-09-19 09:07:53:753 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:9:07:53 AM
2024-09-19 09:09:15:915 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:9:09:15 AM
2024-09-19 09:09:18:918 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: {
  _id: new ObjectId("64ca7c963affb5cb882c3130"),
  firstName: 'youness',
  lastName: 'elhalloui',
  email: '<EMAIL>',
  role: 'VENDOR',
  state: 'ENABLED',
  createdAt: 2023-08-02T15:56:06.038Z,
  updatedAt: 2024-09-18T17:41:14.977Z,
  __v: 0,
  photo: '/productImages/kabour.png',
  identifiant: 'younesselhallaoui1',
  coverPhoto: '/productImages/1693512780266coverImage.jpg',
  favoriteLang: 'fr',
  paymentMethod: new ObjectId("66eae8f2142a27289602af29"),
  paymentMethodType: 'MtoAccount'
} 9/19/2024:9:09:18 AM
2024-09-19 09:12:40:1240 info: this inquery 66eadec608a17119c49c02a8 is completed normal, receiver: 66eadec608a17119c49c02a8 9/19/2024:9:12:40 AM
2024-09-19 09:39:41:3941 info: warn:-VersionError: No matching document found for id "66ebe338e26139bcaf30c138" version 0 modifiedPaths "state, shippingFee, amount" 9/19/2024:9:39:41 AM
2024-09-19 09:48:26:4826 info: this inquery 66ebe338e26139bcaf30c138 is completed normal, receiver: 66ebe338e26139bcaf30c138 9/19/2024:9:48:26 AM
2024-09-19 09:49:44:4944 info: this inquery 66ebe338e26139bcaf30c138 is completed normal, receiver: 66ebe338e26139bcaf30c138 9/19/2024:9:49:43 AM
2024-09-23 10:53:36:5336 info: warn:-VersionError: No matching document found for id "66f13a82932617127fc22a44" version 0 modifiedPaths "state" 9/23/2024:10:53:36 AM
2024-09-26 10:29:41:2941 info: this inquery 66f2db312e2e63b07ad89bcb is completed normal, receiver: 66f2db312e2e63b07ad89bcb 9/26/2024:10:29:41 AM
2024-09-27 17:34:26:3426 info: warn:-CastError: Cast to ObjectId failed for value "undefined" (type string) at path "_id" for model "Inquiry" 9/27/2024:5:34:26 PM
2024-09-30 12:03:06:36 info: this inquery 66fa8334298f2d84799a995b is completed normal, receiver: 66fa8334298f2d84799a995b 9/30/2024:12:03:06 PM
2024-10-14 19:13:39:1339 info: warn:-VersionError: No matching document found for id "670d5f2cde2732974b89de82" version 0 modifiedPaths "state" 10/14/2024:7:13:39 PM
2024-10-15 13:13:43:1343 info: warn:-TypeError: Cannot set properties of null (setting 'status') 10/15/2024:1:13:43 PM
2024-10-15 13:14:24:1424 info: this inquery 670d690fdc79713cce92161f is completed normal, receiver: 670d690fdc79713cce92161f 10/15/2024:1:14:24 PM
2024-10-15 13:31:10:3110 info: warn:-VersionError: No matching document found for id "670e606e8e9ec18faeb0c03a" version 0 modifiedPaths "state" 10/15/2024:1:31:10 PM
2024-10-17 10:22:22:2222 info: order of abderrahim undefined 10/17/2024:10:22:22 AM
2024-10-23 11:31:49:3149 info: warn:-VersionError: No matching document found for id "6718d076539e7cf870a51bd3" version 0 modifiedPaths "state" 10/23/2024:11:31:49 AM
2024-10-23 13:17:53:1753 info: warn:-VersionError: No matching document found for id "6718e69f241bf81b845d11a8" version 0 modifiedPaths "state" 10/23/2024:1:17:53 PM
2024-10-23 13:33:24:3324 info: order of abderrahim undefined 10/23/2024:1:33:24 PM
2024-10-24 16:21:01:211 info: order of abderrahim undefined 10/24/2024:4:21:01 PM
2024-10-24 16:29:56:2956 info: this inquery 671a6465bdb22a50c6f5ff56 is completed normal, receiver: 671a6465bdb22a50c6f5ff56 10/24/2024:4:29:55 PM
2024-10-28 17:14:08:148 info: warn:-TypeError: sendNotificationToUser is not a function 10/28/2024:5:14:08 PM
2024-10-28 17:15:52:1552 info: warn:-TypeError: sendNotificationToUser is not a function 10/28/2024:5:15:52 PM
2024-10-29 10:38:35:3835 info: warn:-TypeError: sendNotificationToUser is not a function 10/29/2024:10:38:35 AM
2024-10-29 10:43:00:430 info: warn:-TypeError: userSocket.emit is not a function 10/29/2024:10:43:00 AM
2024-10-29 10:48:43:4843 info: order of abderrahim undefined 10/29/2024:10:48:43 AM
