const Joi = require("joi");
const { getDefaultOptions } = require("./sharedDefaultOptions");

const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;

exports.registerValidator = async (req, res, next) => {
  console.log("req.body: ", req.body);
  const registerSchema = Joi.object({
    firstName: Joi.string()
      .regex(/^\S/)
      .min(3)
      .lowercase()
      .required()
      .label(req.t("attributes.first_name")),
    "g-recaptcha-response": Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.recaptcha")),
    lastName: Joi.string()
      .regex(/^\S/)
      .min(3)
      .required()
      .label(req.t("attributes.last_name")),
    password: Joi.string()
      .min(8)
      .required()
      .label(req.t("attributes.password")),
    confirmPassword: Joi.any()
      .valid(Joi.ref("password"))
      .required()
      .label(req.t("attributes.password_confirmation")),
    email: Joi.string()
      .email()
      .regex(emailRegex)
      .required()
      .label(req.t("attributes.email")),
  });

  const result = registerSchema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req),
  });

  if (result.error) {
    // Extract the error details from the result
    const errors = result.error.details;

    // Create a table of keys and error messages
    const errorArray = errors.map((error) => {
      if (error.context.key == "confirmPassword") {
        error.message = error.message.replace(
          "ref:password",
          req.t("attributes.password").toLowerCase()
        );
      }
      return { key: error.context.key, msg: error.message };
    });

    const errMsg = "Validation failed.";
    return res.status(400).json({ message: errMsg, errors: errorArray });
  }
  next();
};

exports.cartRegisterValidator = async (req, res, next) => {
  console.log("req.body: ", req.body);
  const registerSchema = Joi.object({
    firstName: Joi.string()
      .regex(/^\S/)
      .min(3)
      .lowercase()
      .required()
      .label(req.t("attributes.first_name")),
    lastName: Joi.string()
      .regex(/^\S/)
      .min(3)
      .required()
      .label(req.t("attributes.last_name")),
    phone: Joi.string()
      .regex(/^\S/)
      .min(10)
      .required()
      .label(req.t("attributes.phone")),
    country: Joi.string()
      .regex(/^\S/)
      .min(3)
      .required()
      .label(req.t("attributes.country")),
    address: Joi.string()
      .regex(/^\S/)
      .min(6)
      .required()
      .label(req.t("attributes.address")),
    password: Joi.string()
      .min(8)
      .required()
      .label(req.t("attributes.password")),
    confirmPassword: Joi.any()
      .valid(Joi.ref("password"))
      .required()
      .label(req.t("attributes.password_confirmation")),
    email: Joi.string()
      .email()
      .regex(emailRegex)
      .required()
      .label(req.t("attributes.email")),
    "g-recaptcha-response": Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.recaptcha")),
  });

  const result = registerSchema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req),
  });

  if (result.error) {
    // Extract the error details from the result
    const errors = result.error.details;

    // Create a table of keys and error messages
    const errorArray = errors.map((error) => {
      if (error.context.key == "confirmPassword") {
        error.message = error.message.replace(
          "ref:password",
          req.t("attributes.password").toLowerCase()
        );
      }
      return { key: error.context.key, msg: error.message };
    });

    const errMsg = "Validation failed.";
    return res.status(400).json({ message: errMsg, errors: errorArray });
  }
  next();
};

exports.loginValidator = (req, res, next) => {
  const registerSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
  });

  const result = registerSchema.validate(req.body, { abortEarly: false });
  if (result.error) {
    return res.status(400).json({ message: req.t("login_failed") });
  }

  ///validate by database
  next();
};

exports.forgetPasswordValidator = (req, res, next) => {
  const registerSchema = Joi.object({
    email: Joi.string().email().required().label(req.t("attributes.email")),
    "g-recaptcha-response": Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.recaptcha")),
  });

  const result = registerSchema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req),
  });
  if (result.error) {
    const errors = result.error.details;
    const errorArray = errors.map((error) => {
      return { key: error.context.key, msg: error.message };
    });
    const errMsg = req.t("send_code_email_error");
    return res.status(400).json({ message: errMsg, errors: errorArray });
  }
  next();
};

exports.resetPasswordValidator = (req, res, next) => {
  const { password, confirmPassword } = req.body;
  const registerSchema = Joi.object({
    password: Joi.string()
      .min(8)
      .required()
      .label(req.t("attributes.password")),
    confirmPassword: Joi.any()
      .valid(Joi.ref("password"))
      .required()
      .label(req.t("attributes.password_confirmation")),
  });

  const result = registerSchema.validate(
    { password, confirmPassword },
    {
      abortEarly: false,
      ...getDefaultOptions(req),
    }
  );
  if (result.error) {
    const errors = result.error.details;

    const errorArray = errors.map((error) => {
      if (error.context.key == "confirmPassword") {
        error.message = error.message.replace(
          "ref:password",
          req.t("attributes.password").toLowerCase()
        );
      }
      return { key: error.context.key, msg: error.message };
    });
    const errMsg = "Validation failed.";
    return res.status(400).json({ message: errMsg, errors: errorArray });
  }
  next();
};
