"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  Button,
  Input,
  Select,
  Option,
} from "@material-tailwind/react";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Ellipsis,
  Tickets,
  XCircle,
  Search,
} from "lucide-react";
import ticketService from "../../../../services/ticketService";
import { useAuth } from "../../../../context/AuthContext";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { getStatusIcon, getPriorityColor } from "../../../../helpers/helpers";
import { useTranslations } from "next-intl";

// Skeleton Loader Component
const SkeletonLoader = () => (
  <div className="space-y-4" role="status" aria-label="Loading tickets">
    {[...Array(3)].map((_, index) => (
      <Card key={index} className="bg-white shadow-sm animate-pulse">
        <CardBody className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex-1 space-y-3">
              <div className="h-5 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="flex flex-wrap gap-2">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-4 bg-gray-200 rounded w-32"></div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
              <div className="h-4 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        </CardBody>
      </Card>
    ))}
  </div>
);

const TicketsPage = () => {
  const t = useTranslations("client");
  const router = useRouter();
  const { user } = useAuth();

  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPriority, setSelectedPriority] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState(null);

  // Memoized fetch tickets function
  const fetchTickets = useCallback(async () => {
    if (!user?.email) return;

    try {
      setLoading(true);
      const response = await ticketService.getUserTickets(user.email);
      setTickets(response.data.userTickets);
    } catch (err) {
      console.error("Error fetching tickets:", err);
      setError(t("fetch_error"));
    } finally {
      setLoading(false);
    }
  }, [user, t]);

  useEffect(() => {
    fetchTickets();
  }, [fetchTickets]);

  // Memoized filtered tickets
  const filteredTickets = useMemo(() => {
    return tickets.filter((ticket) => {
      const matchesSearch =
        ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.creator.email.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesPriority =
        selectedPriority === "all" || ticket.priority === selectedPriority;
      const matchesStatus =
        selectedStatus === "all" || ticket.status === selectedStatus;

      return matchesSearch && matchesPriority && matchesStatus;
    });
  }, [tickets, searchQuery, selectedPriority, selectedStatus]);

  // Toggle dropdown menu
  const toggleDropdown = useCallback((ticketId) => {
    setOpenDropdownId((prev) => (prev === ticketId ? null : ticketId));
  }, []);

  // Handle ticket deletion
  const handleDelete = useCallback(async () => {
    if (!ticketToDelete) return;

    try {
      await ticketService.deleteTicket(ticketToDelete);
      setTickets((prev) =>
        prev.filter((ticket) => ticket._id !== ticketToDelete)
      );
      setShowDeleteModal(false);
      setTicketToDelete(null);
    } catch (err) {
      console.error("Error deleting ticket:", err);
      setError(t("delete_error"));
    }
  }, [ticketToDelete, t]);

  // Open delete confirmation
  const confirmDelete = useCallback((ticketId) => {
    setTicketToDelete(ticketId);
    setShowDeleteModal(true);
    setOpenDropdownId(null);
  }, []);

  // Handle ticket update
  const handleUpdate = useCallback(
    (ticketId) => {
      router.push(`/client/support/add-ticket?edit=true&id=${ticketId}`);
    },
    [router]
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown-container")) {
        setOpenDropdownId(null);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  return (
    <div className="container mx-auto p-6 min-h-screen bg-gray-50">
      <div className="flex items-center justify-between mb-6">
        <Typography className="flex gap-2 items-center text-xl text-gray-900 font-inter">
          <Tickets className="w-8 h-8" />
          {t("mysupporttickets")}
        </Typography>
      </div>

      {/* Search and Filter Bar */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <Input
          type="text"
          label={t('search_ticket')} // Add this line for the label
          placeholder={t("search_ticket")}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          icon={<Search className="h-5 w-5" />}
          className="w-full"
        />
        <Select
          value={selectedPriority}
          onChange={(value) => setSelectedPriority(value)}
          label={t("priority")}
          className="w-full"
        >
          <Option value="all">{t("allpriorities")}</Option>
          <Option value="urgent">{t("urgent")}</Option>
          <Option value="high">{t("high")}</Option>
          <Option value="medium">{t("medium")}</Option>
          <Option value="low">{t("low")}</Option>
        </Select>
        <Select
          value={selectedStatus}
          onChange={(value) => setSelectedStatus(value)}
          label={t("status")}
          className="w-full"
        >
          <Option value="all">{t("allstatus")}</Option>
          <Option value="open">{t("open")}</Option>
          <Option value="in_progress">{t("inprogress")}</Option>
          <Option value="resolved">{t("resolved")}</Option>
          <Option value="closed">{t("closed")}</Option>
        </Select>
      </div>

      {/* Ticket List */}
      {loading ? (
        <SkeletonLoader />
      ) : error ? (
        <div className="flex flex-col items-center justify-center p-6 bg-white rounded-lg shadow-sm">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <Typography className="text-red-600 mb-4">{error}</Typography>
          <Button onClick={fetchTickets} color="blue">
            {t("retry")}
          </Button>
        </div>
      ) : filteredTickets.length > 0 ? (
        <div className="space-y-4">
          {filteredTickets.map((ticket) => (
            <Card
              key={ticket._id}
              className="relative bg-white shadow-sm hover:shadow-md transition-shadow"
              role="article"
            >
              <Link
                href={`/client/support/tickets/${ticket._id}`}
                className="block"
              >
                <CardBody className="p-6">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                    <div className="flex-1">
                      <Typography
                        variant="h6"
                        className="font-semibold text-gray-900 mb-1"
                      >
                        {ticket.subject}
                      </Typography>
                      <Typography className="text-gray-600 mb-2 line-clamp-2">
                        {ticket.message}
                      </Typography>
                      <div className="flex flex-wrap gap-2 text-sm">
                        <span
                          className={`px-2 py-1 rounded-full ${getPriorityColor(
                            ticket.priority
                          )}`}
                        >
                          {t(ticket.priority)}
                        </span>
                        <span className="text-gray-500">
                          {t("created")}:{" "}
                          {new Date(ticket.createdAt).toLocaleDateString(
                            "fr-FR"
                          )}
                        </span>
                        <span className="text-gray-500">
                          {t("by")}: {ticket.creator.lastName} (
                          {ticket.creator.email})
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(ticket.status)}
                      <span className="text-sm text-gray-600">
                        {t(ticket.status)}
                      </span>
                    </div>
                  </div>
                </CardBody>
              </Link>
              {ticket.status === "open" && (
                <div className="absolute top-2 right-2 dropdown-container">
                  <Button
                    variant="text"
                    size="sm"
                    onClick={() => toggleDropdown(ticket._id)}
                    className="p-1 hover:bg-gray-100 rounded-full"
                    aria-label={t("ticket_options")}
                  >
                    <Ellipsis className="w-6 h-6 text-gray-600" />
                  </Button>
                  {openDropdownId === ticket._id && (
                    <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <Button
                        variant="text"
                        size="sm"
                        onClick={() => handleUpdate(ticket._id)}
                        className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100"
                      >
                        {t("update")}
                      </Button>
                      <Button
                        variant="text"
                        size="sm"
                        onClick={() => confirmDelete(ticket._id)}
                        className="w-full text-left px-4 py-2 text-red-600 hover:bg-gray-100"
                      >
                        {t("delete")}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </Card>
          ))}
        </div>
      ) : (
        <div className="p-6 bg-white shadow-sm text-center rounded-md">
          <Typography className="text-gray-500">
            {t("no_tickets_found")}
          </Typography>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <Typography variant="h6" className="mb-4 text-gray-900">
              {t("confirm_deletion")}
            </Typography>
            <Typography className="mb-6 text-gray-600">
              {t("confirm_deletion_message")}
            </Typography>
            <div className="flex justify-end gap-4">
              <Button
                variant="text"
                onClick={() => setShowDeleteModal(false)}
                className="text-gray-600 hover:bg-gray-100"
              >
                {t("cancel")}
              </Button>
              <Button
                color="red"
                onClick={handleDelete}
                className="hover:bg-red-700"
              >
                {t("delete")}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TicketsPage;
