const mongoose = require('mongoose');
const VPSRegion = require('./models/VPSRegion');
const Package = require('./models/Package');

async function fixDuplicatePricing() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/hosting-website');
    console.log('✅ Connected to MongoDB');

    // Get all VPS packages
    const vpsPackages = await Package.find({
      name: { $in: ['CLOUD VPS 10', 'CLOUD VPS 20'] }
    }).select('_id name');

    console.log('VPS Packages found:');
    vpsPackages.forEach(pkg => {
      console.log(`  ${pkg.name}: ${pkg._id}`);
    });

    if (vpsPackages.length !== 2) {
      console.error('❌ Expected 2 VPS packages, found:', vpsPackages.length);
      return;
    }

    // Get all regions
    const regions = await VPSRegion.find();
    console.log(`Found ${regions.length} regions to check`);

    let fixedCount = 0;

    for (const region of regions) {
      console.log(`\n🔍 Checking region: ${region.name} (${region.regionId})`);
      console.log(`   Current pricing entries: ${region.pricing.length}`);

      // Check if region has pricing for both VPS packages
      const existingPackageIds = region.pricing.map(p => p.packageId.toString());
      const missingPackages = vpsPackages.filter(pkg =>
        !existingPackageIds.includes(pkg._id.toString())
      );

      console.log(`   Existing packages: ${existingPackageIds.length}`);
      console.log(`   Missing packages: ${missingPackages.length}`);

      // Add missing packages with default price 0
      if (missingPackages.length > 0) {
        console.log(`   ➕ Adding missing packages:`);

        for (const pkg of missingPackages) {
          region.pricing.push({
            packageId: pkg._id,
            packageName: pkg.name,
            additionalPrice: 0
          });
          console.log(`      Added: ${pkg.name} with price 0 MAD`);
        }

        await region.save();
        fixedCount++;
        console.log(`   ✅ Region ${region.name} updated and saved`);
      }

      // Also remove any duplicates
      const pricingMap = new Map();
      const duplicates = [];

      region.pricing.forEach((pricing, index) => {
        const packageId = pricing.packageId.toString();

        if (pricingMap.has(packageId)) {
          console.log(`   ❌ DUPLICATE found for package ${pricing.packageName} (${packageId})`);
          duplicates.push(index);
        } else {
          pricingMap.set(packageId, pricing);
          console.log(`   ✅ Package ${pricing.packageName}: ${pricing.additionalPrice} MAD`);
        }
      });

      if (duplicates.length > 0) {
        console.log(`   🧹 Removing ${duplicates.length} duplicate entries...`);

        duplicates.reverse().forEach(index => {
          const removed = region.pricing.splice(index, 1)[0];
          console.log(`      Removed duplicate: ${removed.packageName}`);
        });

        await region.save();
        console.log(`   ✅ Duplicates removed from ${region.name}`);
      }
    }

    console.log(`\n🎉 Cleanup completed! Fixed ${fixedCount} regions with duplicates.`);
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

fixDuplicatePricing();
