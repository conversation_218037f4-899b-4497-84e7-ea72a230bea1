const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const refundSchema = new Schema({
    refundId: { type: String, required: true, unique: true, index: true },
    order: { type: Schema.Types.ObjectId, ref: 'Order', required: true, index: true },
    payment: { type: Schema.Types.ObjectId, ref: 'Payment', required: true, index: true },
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true, index: true },
    dateRequested: { type: Date, required: true, default: Date.now },
    dateProcessed: { type: Date },
    requestedReason: { type: String },
    adminNotes: { type: String },
    status: { type: String, enum: ['pending', 'approved', 'rejected', 'partial', 'full', 'processing', 'completed', 'failed'], default: 'pending', index: true },
    refundType: { type: String, enum: ['full', 'partial'], required: true },
    refundAmount: { type: Number, required: true },
    currency: { type: String, required: true },
    paymentMethod: { type: String },
    transactionId: { type: String },
    refundedSubOrders: [{
        subOrder: { type: Schema.Types.ObjectId, ref: 'SubOrder' },
        amount: { type: Number }
    }],
    admin: { type: Schema.Types.ObjectId, ref: 'Admin', index: true },
    rejectionReason: { type: String }
}, { timestamps: true });

const Refund = mongoose.model('Refund', refundSchema);
module.exports = Refund;