import { ArrowRightIcon } from "@heroicons/react/24/solid";
import { Typography } from "@material-tailwind/react";
import Link from "next/link";
import React from "react";
import Lottie from "lottie-react";
import { motion } from "framer-motion";
import costReductionAnimation from "src/assets/cloud-in-morocco-1.json";
import dataSecurityAnimation from "src/assets/cloud-in-morocco-2.json";
import accessibilityAnimation from "src/assets/cloud-in-morocco-3.json";
import scalabilityAnimation from "src/assets/cloud-in-morocco-4.json";

const CloudMaroc = ({ t }) => {
  const cloudMaFeatures = [
    {
      title: "cloudMaFeatures.0.title",
      description: "cloudMaFeatures.0.description",
      icon: "cost-reduction", // You'll replace this with your actual image
    },
    {
      title: "cloudMaFeatures.1.title",
      description: "cloudMaFeatures.1.description",
      icon: "data-security", // You'll replace this with your actual image
    },
    {
      title: "cloudMaFeatures.2.title",
      description: "cloudMaFeatures.2.description",
      icon: "accessibility", // You'll replace this with your actual image
    },
    {
      title: "cloudMaFeatures.3.title",
      description: "cloudMaFeatures.3.description",
      icon: "scalability", // You'll replace this with your actual image
    },
  ];

  return (
    <div className="w-full bg-white py-16 relative overflow-hidden">
      {/* Decorative elements - Side (Top) */}
      {/* Left Decoration */}
      <div className="absolute top-20 left-0 transform -translate-x-1/2 opacity-30 z-0">
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t("decorative_pattern_left", "Decorative Pattern Left")}
          className="w-48 h-auto md:w-64" // No change here
        />
      </div>
      {/* Right Decoration */}
      <div className="absolute top-20 right-0 transform translate-x-1/2 opacity-30 z-0">
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t("decorative_pattern_right", "Decorative Pattern Right")}
          className="w-48 h-auto md:w-64 grayscale" // Added grayscale
        />
      </div>

      {/* Decorative elements - Title Area */}
      {/* Left Small Decoration */}
      <div className="absolute top-20 left-1/2 transform -translate-x-[26rem] opacity-30 z-0 hidden md:block">
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t(
            "small_decorative_pattern_left",
            "Small Decorative Pattern Left"
          )}
          className="w-20 h-auto grayscale" // Added grayscale
        />
      </div>
      {/* Right Small Decoration (Top) */}
      <div className="absolute top-20 right-1/2 transform translate-x-[26rem] opacity-30 z-0 hidden md:block">
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t(
            "small_decorative_pattern_right",
            "Small Decorative Pattern Right"
          )}
          className="w-20 h-auto" // No change here
        />
      </div>

      {/* Decorative elements - Bottom Area (Small) */}
      {/* Left Small Decoration (Bottom) */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-[30rem] opacity-30 z-0 hidden md:block">
        {" "}
        {/* Changed bottom-12 to bottom-4 and -translate-x-[26rem] to -translate-x-[30rem] */}
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t(
            "small_decorative_pattern_left_bottom",
            "Small Decorative Pattern Left Bottom"
          )}
          className="w-24 h-auto grayscale" // Added grayscale
        />
      </div>
      {/* Right Small Decoration (Bottom) */}
      <div className="absolute bottom-4 right-1/2 transform translate-x-[30rem] opacity-30 z-0 hidden md:block">
        {" "}
        {/* Changed bottom-12 to bottom-4 and translate-x-[26rem] to translate-x-[30rem] */}
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t(
            "small_decorative_pattern_right_bottom",
            "Small Decorative Pattern Right Bottom"
          )}
          className="w-24 h-auto" // No change here
        />
      </div>

      {/* Decorative elements - Side (Bottom) - Adjusted */}
      {/* Left Bottom Large Decoration */}
      <div className="absolute bottom-[230px] left-0 transform -translate-x-1/2 translate-y-1/2 opacity-30 z-0">
        {" "}
        {/* Changed bottom-12 to bottom-44 */}
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t(
            "decorative_pattern_left_bottom_large",
            "Decorative Pattern Left Bottom Large"
          )}
          className="w-48 h-auto md:w-64" // No change here
        />
      </div>
      {/* Right Bottom Large Decoration */}
      <div className="absolute bottom-[230px] right-0 transform translate-x-1/2 translate-y-1/2 opacity-30 z-0">
        {" "}
        {/* Changed bottom-12 to bottom-[230px] */}
        <img
          src="/images/cloud-morocco/decoration.png"
          alt={t(
            "decorative_pattern_right_bottom_large",
            "Decorative Pattern Right Bottom Large"
          )}
          className="w-48 h-auto md:w-64 grayscale" // Added grayscale
        />
      </div>

            <section className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-12 relative z-10">
                {/* Header Section */}
                <motion.div
                    className="text-center mb-12"
                    initial={{ opacity: 0, y: -20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                >
                    <motion.div
                        className="bg-blue-600 rounded-full inline-block px-4 py-2 mb-4 text-sm text-white font-medium font-inter uppercase"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                    >
                        {t('innovative_title')}
                    </motion.div>
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.5 }}
                    >
                        <Typography variant='h1' className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            {t('cloud_maroc.title')}
                        </Typography>
                    </motion.div>
                    <motion.p
                        className="text-base text-gray-600 max-w-3xl mx-auto"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.7 }}
                    >
                        {t('innovative_description')}
                    </motion.p>
                </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-12 max-w-5xl mx-auto lg:pl-20">
          {" "}
          {/* Added lg:pl-8 */}
          {/* Cost Reduction - Top Left */}
          <motion.div
            className="bg-white rounded-lg border-2 border-gray-300 lg:col-span-6 relative" // No col-start here
            style={{ boxShadow: "-100px -30px 120px rgba(65, 105, 225, 0.08)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <div
              className="p-4 flex justify-center bg-gradient-to-b from-[#f1f5ff] to-white rounded-t-lg"
              style={{ minHeight: "220px" }}
            >
              <Lottie
                animationData={costReductionAnimation}
                loop={true}
                className="w-full h-[200px]"
              />
            </div>
            <div className="p-6">
              <Typography
                variant="h3"
                className="text-lg font-semibold text-gray-900 mb-2"
              >
                {t("cloudMaFeatures.0.title")}
              </Typography>
              <p className="text-gray-600 text-sm">
                {t("cloudMaFeatures.0.description")}
              </p>
            </div>
            {/* Adjusted Decorative Image */}
            <img
              src="/images/cloud-morocco/decoration.png"
              alt={t("decorative_element", "Decorative Element")}
              className="absolute bottom-40 left-7 opacity-2 transform -translate-x-1/2 w-48 h-auto opacity-30 -z-10 grayscale" // Added grayscale
            />
          </motion.div>
          {/* Data Security and Privacy - Top Right */}
          <motion.div
            className="bg-white rounded-lg border-2 border-gray-300 overflow-hidden lg:col-span-5" // No change here
            style={{ boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <div className="p-6">
              <Typography
                variant="h3"
                className="text-lg font-semibold text-gray-900 mb-2"
              >
                {t("cloudMaFeatures.1.title")}
              </Typography>
              <p className="text-gray-600 text-sm">
                {t("cloudMaFeatures.1.description")}
              </p>
            </div>
            <div
              className="p-4 flex justify-center bg-gradient-to-b from-[#f1f5ff] to-white rounded-b-lg"
              style={{ minHeight: "250px" }}
            >
              <Lottie
                animationData={dataSecurityAnimation}
                loop={true}
                className="w-full h-[230px]"
              />
            </div>
          </motion.div>
          {/* Scalability - Bottom Left */}
          <motion.div
            className="bg-white rounded-lg border-2 border-gray-300 overflow-hidden lg:col-span-5" // No col-start here
            style={{ boxShadow: "-100px -30px 120px rgba(65, 105, 225, 0.08)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div
              className="p-4 flex justify-center bg-gradient-to-b from-[#f1f5ff] to-white rounded-t-lg"
              style={{ minHeight: "220px" }}
            >
              <Lottie
                animationData={accessibilityAnimation}
                loop={true}
                className="w-full h-[200px]"
              />
            </div>
            <div className="p-6">
              <Typography
                variant="h3"
                className="text-lg font-semibold text-gray-900 mb-2"
              >
                {t("cloudMaFeatures.3.title")}
              </Typography>
              <p className="text-gray-600 text-sm">
                {t("cloudMaFeatures.3.description")}
              </p>
            </div>
          </motion.div>
          {/* Accessibility and Flexibility - Bottom Right */}
          <motion.div
            className="bg-white rounded-lg border-2 border-gray-300 overflow-hidden lg:col-span-6" // No change here
            style={{ boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)" }}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="p-6">
              <Typography
                variant="h3"
                className="text-lg font-semibold text-gray-900 mb-2"
              >
                {t("cloudMaFeatures.2.title")}
              </Typography>
              <p className="text-gray-600 text-sm">
                {t("cloudMaFeatures.2.description")}
              </p>
            </div>
            <div
              className="p-4 flex justify-center bg-gradient-to-b from-[#f1f5ff] to-white rounded-t-lg"
              style={{ minHeight: "220px" }}
            >
              <Lottie
                animationData={scalabilityAnimation}
                loop={true}
                className="w-fit h-[250px]"
              />
            </div>
          </motion.div>
        </div>

        {/* See More Button */}
        <motion.div
          className="text-center mt-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Link
            href="/cloud-maroc"
            className="inline-flex items-center gap-2 bg-white border-2 border-blue-800 hover:border-blue-900 text-blue-800 hover:text-blue-900 font-medium py-3 px-6 rounded-full transition-colors duration-300" // Updated classes for styling
          >
            <span>{t("voir_plus")}</span>
            <ArrowRightIcon className="h-4 w-4" />
          </Link>
        </motion.div>
      </section>
    </div>
  );
};

export default CloudMaroc;
