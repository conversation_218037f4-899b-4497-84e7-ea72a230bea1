'use client'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image'
import React from 'react'


function ServicesPense({ services, sectionData, t }) {

    return (
        <div className={`${sectionData.bgColor} w-full md:p-10 p-2`}>
            <div className={`max-w-[1400px] mx-auto flex flex-col ${sectionData.flexPositioning} items-center justify-between px-2 md:py-10 md:px-20`}>
                {/* Left Side: Services Content */}
                <div className="flex flex-col md:w-2/5 gap-y-8">
                    <Typography
                        variant='h1'
                        className="text-2xl md:text-3xl font-semibold text-gray-800">
                        {t(sectionData.title)} <span className="text-black font-bold">{t(sectionData.focusWord) || ""}</span>
                    </Typography>
                    <div className="flex flex-col md:gap-y-4 gap-y-8">
                        {services.map((service) => (
                            <div
                                key={service.id}
                                className="flex items-start gap-x-4 md:w-4/5 md:p-4 rounded-lg"
                            >
                                <div className="bg-white shadow-md p-2 flex items-center justify-center rounded-lg border">
                                    {service.icon}
                                </div>
                                <div className="flex flex-col gap-y-3">
                                    <Typography
                                        variant='h3'
                                        className="text-lg font-semibold text-gray-800">
                                        {t(service.title)}
                                    </Typography>
                                    <p className="text-sm text-gray-600">{t(service.description)}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Side: Image with 3D Book-Like Effect */}
                <div className="relative md:w-1/2 flex justify-center mt-8 md:mt-0">
                    {/* Bottom Layer */}
                    <div className="hidden md:block absolute md:w-[450px] w-[290px] h-[440px] bg-[#F5F6F8] border-transparent rounded-[38.85px] transform rotate-y-6 md:translate-y-10 translate-x-8 shadow-[0_0_2px_black]"></div>
                    {/* Middle Layer */}
                    <div className="hidden md:block absolute md:w-[465px] w-[300px] h-[450px] bg-blue-500 rounded-[38.85px] transform rotate-y-4 md:translate-y-6 translate-x-3 shadow-md"></div>
                    {/* Image Layer */}
                    <div className="relative md:w-[465px]  md:h-[465px] bg-white rounded-[38.85px] overflow-hidden transform rotate-y-2 translate-y-0 translate-x-0">
                        {/* <img
                            loading="lazy"
                            src={sectionData.sideImg}
                            alt="Happy person holding a tablet"
                            className="w-full h-full object-cover rounded-[38.85px]"
                        /> */}
                        <Image
                            src={sectionData.sideImg}
                            width={900}
                            height={600}
                            quality={100}
                            alt="ZTEchengineering"
                            className="w-full h-full object-cover rounded-[38.85px] border"
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ServicesPense