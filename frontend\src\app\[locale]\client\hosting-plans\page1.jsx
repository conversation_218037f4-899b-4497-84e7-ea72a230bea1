"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, CardB<PERSON>, Chip } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import PaymentStatusModal from "../../../../components/order/paymentStatusModal";
import { useSearchParams } from "next/navigation";
import orderService from "../../../services/orderService";
import {
  Server,
  Clock,
  CheckCircle2,
  PhoneCall,
  Settings,
  Rocket,
  AlertCircle,
} from "lucide-react";
import { OrderStatus } from "../../../config/ConstStatus";
import { getLocalizedContent } from "@/app/helpers/helpers";
import { useIsMobile } from "@/app/hook/useIsMobile";

const OrderSteps = ({ currentStatus }) => {
  const t = useTranslations("client");
  const isMobile = useIsMobile();

  const steps = [
    {
      icon: CheckCircle2,
      label: t("hostingWrapper.steps.order_confirmed"),
      status: "completed",
    },
    {
      icon: PhoneCall,
      label: t("hostingWrapper.steps.team_contact"),
      status: currentStatus === OrderStatus.PROCESSING ? "current" : "upcoming",
    },
    {
      icon: Settings,
      label: t("hostingWrapper.steps.setup_config"),
      status: "upcoming",
    },
    {
      icon: Rocket,
      label: t("hostingWrapper.steps.go_live"),
      status: "upcoming",
    },
  ];

  return (
    <div className="w-full py-4">
      <div
        className={`flex ${
          isMobile ? "flex-col gap-4" : "justify-between"
        } items-center`}
      >
        {steps.map((step, idx) => (
          <div
            key={idx}
            className={`flex ${
              isMobile ? "flex-row gap-4" : "flex-col"
            } items-center ${isMobile ? "w-full" : "flex-1"}`}
          >
            <div
              className={`
              w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center ${
                !isMobile && "mb-2"
              }
              ${
                step.status === "completed"
                  ? "bg-green-500 text-white"
                  : step.status === "current"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 text-gray-500"
              }
            `}
            >
              <step.icon className="w-5 h-5 md:w-6 md:h-6" />
            </div>
            <div className={`${isMobile ? "flex-1" : "text-center"}`}>
              <p
                className={`text-sm font-medium
                ${
                  step.status === "completed"
                    ? "text-green-500"
                    : step.status === "current"
                    ? "text-blue-500"
                    : "text-gray-500"
                }
              `}
              >
                {step.label}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const StatusBadge = ({ status }) => {
  const statusStyles = {
    [OrderStatus.PROCESSING]: {
      bg: "bg-blue-100",
      text: "text-blue-700",
      icon: AlertCircle,
    },
    [OrderStatus.ACTIVE]: {
      bg: "bg-green-100",
      text: "text-green-700",
      icon: CheckCircle2,
    },
  };

  const style = statusStyles[status] || statusStyles[OrderStatus.PROCESSING];

  return (
    <div
      className={`flex items-center gap-2 px-3 py-1 rounded-full ${style.bg} ${style.text}`}
    >
      <style.icon className="w-4 h-4" />
      <span className="text-sm font-medium capitalize">
        {status.toLowerCase()}
      </span>
    </div>
  );
};

export default function HostingOrdersPage() {
  const t = useTranslations("client");
  const [subOrders, setSubOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const localeActive = useLocale();

  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const item = searchParams.get("item");
  const categoryName = "Hosting";

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setOrderId(item);
      setOpenModal(true);
    }
  }, [status, item]);

  useEffect(() => {
    const getSubOrders = async () => {
      try {
        const res = await orderService.getSubOrdersByCategory(categoryName);
        setSubOrders(res.data.subOrders);
      } catch (error) {
        console.error("Error getting suborders", error);
      } finally {
        setLoading(false);
      }
    };
    getSubOrders();
  }, [categoryName]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <Typography className="text-gray-600">
            {t("hosting.loading")}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-[80vh] p-4 md:p-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6 md:mb-8">
          <Typography
            variant="h1"
            className="text-lg md:text-xl mb-2 font-medium text-gray-800"
          >
            {t("hostingWrapper.your_hosting_plans")}
          </Typography>
          <Typography className="text-sm md:text-base text-gray-600">
            {t("hostingWrapper.manage_description")}
          </Typography>
        </div>

        {subOrders?.length > 0 ? (
          <div className="space-y-4 md:space-y-6">
            {subOrders?.map((subOrder) => {
              const {
                _id,
                package: pkg,
                quantity,
                price,
                status,
                createdAt,
                period,
                basedPrice,
              } = subOrder;
              const expireAt = new Date(createdAt);
              expireAt.setMonth(expireAt.getMonth() + period);

              return (
                <Card key={_id} className="overflow-hidden">
                  <CardBody className="p-4 md:p-6">
                    {/* Header */}
                    <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4 mb-6">
                      <div className="flex gap-4">
                        <div className="h-10 w-10 md:h-12 md:w-12 bg-blue-100 rounded-xl flex items-center justify-center">
                          <Server className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
                        </div>
                        <div>
                          <Typography className="text-base md:text-xl font-semibold text-gray-900">
                            {getLocalizedContent(
                              pkg.brand,
                              "name",
                              localeActive
                            ) +
                              ": " +
                              getLocalizedContent(pkg, "name", localeActive)}
                          </Typography>
                          <Typography className="text-xs md:text-sm text-gray-500">
                            {t("hostingWrapper.plan_reference")} {pkg.reference}
                          </Typography>
                        </div>
                      </div>
                      <StatusBadge status={status} />
                    </div>

                    {/* Progress Steps - Only show for processing status */}
                    {status === OrderStatus.PROCESSING && (
                      <div className="bg-blue-50 rounded-xl p-4 md:p-6 mb-6">
                        <div className="flex items-center gap-2 mb-4">
                          <Clock className="w-4 h-4 md:w-5 md:h-5 text-blue-600" />
                          <Typography className="text-sm md:text-base text-blue-700 font-medium">
                            {t("hostingWrapper.setup_time")}
                          </Typography>
                        </div>
                        <OrderSteps currentStatus={status} />
                      </div>
                    )}

                    {/* Details Grid */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mt-6">
                      <div>
                        <Typography className="text-xs md:text-sm text-gray-500 mb-1">
                          {t("hostingWrapper.billing_cycle")}
                        </Typography>
                        <Typography className="text-sm md:text-base font-medium">
                          {period} {t("hostingWrapper.months")}
                        </Typography>
                      </div>
                      <div>
                        <Typography className="text-xs md:text-sm text-gray-500 mb-1">
                          {t("hostingWrapper.total_amount")}
                        </Typography>
                        <div className="flex items-center gap-2">
                          <Typography className="text-sm md:text-base font-medium">
                            {price * quantity} MAD
                          </Typography>
                          {basedPrice > price && (
                            <Typography className="text-xs md:text-sm line-through text-gray-400">
                              {basedPrice} MAD
                            </Typography>
                          )}
                        </div>
                      </div>
                      <div>
                        <Typography className="text-xs md:text-sm text-gray-500 mb-1">
                          {t("hostingWrapper.start_date")}
                        </Typography>
                        <Typography className="text-sm md:text-base font-medium">
                          {new Date(createdAt).toLocaleDateString()}
                        </Typography>
                      </div>
                      <div>
                        <Typography className="text-xs md:text-sm text-gray-500 mb-1">
                          {t("hostingWrapper.renewal_date")}
                        </Typography>
                        <Typography className="text-sm md:text-base font-medium">
                          {new Date(expireAt).toLocaleDateString()}
                        </Typography>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8 md:py-12 bg-white rounded-xl shadow-sm">
            <Server className="w-12 h-12 md:w-16 md:h-16 text-gray-400 mx-auto mb-4" />
            <Typography
              variant="h6"
              className="text-base md:text-lg text-gray-600 mb-2"
            >
              {t("hostingWrapper.no_plans")}
            </Typography>
            <Typography className="text-sm md:text-base text-gray-500">
              {t("hostingWrapper.browse_plans_message")}
            </Typography>
          </div>
        )}
      </div>

      {openModal && (
        <PaymentStatusModal
          status={paymentStatus}
          orderId={orderId}
          onClose={() => setOpenModal(false)}
          t={t}
        />
      )}
    </div>
  );
}
