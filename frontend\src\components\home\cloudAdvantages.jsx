import { <PERSON><PERSON>, Typo<PERSON> } from '@material-tailwind/react';
import React from 'react';
import { COMPUTERsvg, HEADsvg, SUPPORTsvg } from '../../icons/svgIcons';
import CloudAdvantagesMobile from './cloudAdvantagesMobile';
import CloudAdvantagesDesktop from './cloudAdvantagesDesktop';
import Link from 'next/link';

const advantages = [
    {
        id: 1,
        icon: <HEADsvg />,
        title: "cloud_advantages.0.title",
        description: "cloud_advantages.0.description",
    },
    {
        id: 2,
        icon: <COMPUTERsvg />,
        title: "cloud_advantages.1.title",
        description: "cloud_advantages.1.description",
    },
    {
        id: 3,
        icon: <SUPPORTsvg />,
        title: "cloud_advantages.2.title",
        description: "cloud_advantages.2.description",
    },
];


const CloudAdvantages = ({ t }) => {

    return (
        <section
            className="max-w-[1400px] bg-cover bg-center text-white font-inter py-12 px-0 md:px-16 rounded-3xl mx-auto md:my-10"
            style={{
                backgroundImage: `url('/images/home/<USER>')`,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
                backgroundPosition: "center"
            }}
        >
            <Typography
                variant='h2'
                className="md:text-3xl text-2xl font-inter font-bold mb-6 px-5 text-center">
                {t('cloud_benefits')}
            </Typography>

            <p className="text-gray-300 mb-8 font-inter text-base px-5">
                {t('cloud_expertise')}
            </p>

            <div className="flex justify-center items-center mb-8 space-x-4 px-5">
                <div className="hidden md:block bg-gray-500 bg-opacity-30 text-center text-white text-sm px-6 py-3 rounded-full shadow-lg">
                    {t('benefits_of_ztechengineering')}
                </div>
            </div>

            <CloudAdvantagesDesktop advantages={advantages} t={t} />
            <CloudAdvantagesMobile advantages={advantages} t={t} />

            <div className="text-center flex flex-col gap-y-3 p-2 w-full">
                <span
                    className="py-3 px-6 w-fit mx-auto h-full bg-white bg-opacity-30 text-white font-normal md:font-medium text-[15px] normal-case font-inter rounded-xl md:rounded-full md:shadow-sm">
                    {t('choose_innovation')}
                </span>
                <Link href="/cloud-maroc"
                    className="py-2 px-6 w-fit mx-auto h-full bg-secondary hover:bg-blue-700 text-white font-normal md:font-medium text-[15px] normal-case font-inter rounded-xl md:rounded-full md:shadow-sm">
                    <span className='uppercase'>
                        {t('voir_plus')}
                    </span>
                </Link>
            </div>
        </section>
    );
};

export default CloudAdvantages;
