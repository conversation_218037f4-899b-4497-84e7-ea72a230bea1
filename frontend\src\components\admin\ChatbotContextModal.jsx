'use client';
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>eader,
  Dialog<PERSON>ody,
  Dialog<PERSON>ooter,
  Button,
  Textarea,
  Spinner
} from '@material-tailwind/react';
import { adminService } from '../../app/services/adminService';
import { toast } from 'react-toastify';

export default function ChatbotContextModal({ open, onClose }) {
  const [context, setContext] = useState('');
  const [originalContext, setOriginalContext] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load the context when the modal opens
  useEffect(() => {
    if (open) {
      loadContext();
    }
  }, [open]);

  const loadContext = async () => {
    try {
      setLoading(true);
      const response = await adminService.getChatbotContext();
      setContext(response.data.content);
      setOriginalContext(response.data.content);
    } catch (error) {
      console.error('Error loading chatbot context:', error);
      toast.error('Failed to load chatbot context');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await adminService.updateChatbotContext(context);
      setOriginalContext(context);
      toast.success('Chatbot context updated successfully');
      onClose();
    } catch (error) {
      console.error('Error updating chatbot context:', error);
      toast.error('Failed to update chatbot context');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setContext(originalContext);
    onClose();
  };

  const hasChanges = context !== originalContext;

  return (
    <Dialog open={open} handler={onClose} size="lg">
      <DialogHeader>Edit Chatbot Context</DialogHeader>
      <DialogBody>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Spinner className="h-8 w-8" />
          </div>
        ) : (
          <>
            <p className="mb-4 text-gray-700">
              This context is used to provide the chatbot with information about your company.
              It will be used to generate responses to user queries.
            </p>
            <Textarea
              label="Chatbot Context"
              value={context}
              onChange={(e) => setContext(e.target.value)}
              rows={15}
              className="w-full"
            />
          </>
        )}
      </DialogBody>
      <DialogFooter className="space-x-2">
        <Button variant="outlined" color="gray" onClick={handleCancel}>
          Cancel
        </Button>
        <Button 
          variant="filled" 
          color="blue" 
          onClick={handleSave}
          disabled={saving || !hasChanges}
          className="flex items-center gap-2"
        >
          {saving ? (
            <>
              <Spinner className="h-4 w-4" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </DialogFooter>
    </Dialog>
  );
}
