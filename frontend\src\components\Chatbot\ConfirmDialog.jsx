import React from 'react';
import PropTypes from 'prop-types';
import { useTranslations } from 'next-intl';

const ConfirmDialog = ({ isOpen, message, onConfirm, onCancel }) => {
  const t = useTranslations('shared.chatbot');
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[1002] flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50"
        onClick={onCancel}
      />

      {/* Dialog */}
      <div className="relative bg-white rounded-xl shadow-xl overflow-hidden max-w-sm w-full mx-4 transform transition-all">
        {/* Header */}
        <div className="bg-[#497ef7] text-white px-4 py-3 flex items-center">
          <h3 className="font-medium text-sm">{t('confirm_action')}</h3>
        </div>

        {/* Content */}
        <div
          className="p-5 chatbot-bg-pattern"
          style={{
            background: 'linear-gradient(to bottom, #f8faff, #f0f4ff)'
          }}
        >
          <p className="text-gray-700 text-sm">{message}</p>
        </div>

        {/* Actions */}
        <div
          className="flex justify-end p-3 border-t border-gray-100"
          style={{
            background: 'linear-gradient(to bottom, #f8faff, #f0f4ff)'
          }}
        >
          <button
            onClick={onCancel}
            className="px-4 py-1.5 rounded-md text-gray-600 text-sm font-medium hover:bg-gray-100 transition-colors mr-2"
          >
            {t('cancel')}
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-1.5 rounded-md bg-[#497ef7] text-white text-sm font-medium hover:bg-[#3968d8] transition-colors shadow-sm"
          >
            {t('ok')}
          </button>
        </div>
      </div>
    </div>
  );
};

ConfirmDialog.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  message: PropTypes.string.isRequired,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired
};

export default ConfirmDialog;
