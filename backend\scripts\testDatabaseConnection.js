#!/usr/bin/env node

/**
 * Database Connection Test Script
 * 
 * This script tests the database connection based on current environment settings
 * and provides detailed information about the connection status.
 */

const mongoose = require('mongoose');
const { isProd, DATABASE_CONFIG } = require('../constants/constant');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🧪 Testing Database Connection...\n');
  
  try {
    let dbURI;
    let environmentInfo;
    
    if (isProd) {
      // Production: Use local MongoDB
      const config = DATABASE_CONFIG.production;
      dbURI = `mongodb://${config.host}:${config.port}/${config.database}`;
      environmentInfo = {
        environment: 'Production',
        database: 'Local MongoDB',
        host: config.host,
        port: config.port,
        database: config.database,
        uri: dbURI
      };
    } else {
      // Development: Use MongoDB Atlas
      dbURI = process.env.MONGODB_URI;
      environmentInfo = {
        environment: 'Development',
        database: 'MongoDB Atlas',
        uri: dbURI ? 'Connected via MONGODB_URI' : 'MONGODB_URI not found'
      };
    }

    console.log('📊 Environment Information:');
    console.log(`   Environment: ${environmentInfo.environment}`);
    console.log(`   Database Type: ${environmentInfo.database}`);
    if (environmentInfo.host) {
      console.log(`   Host: ${environmentInfo.host}`);
      console.log(`   Port: ${environmentInfo.port}`);
      console.log(`   Database: ${environmentInfo.database}`);
    }
    console.log(`   isProd: ${isProd}`);
    console.log('');

    if (!dbURI) {
      throw new Error('Database URI is not defined. Check your environment variables.');
    }

    console.log('🔌 Attempting to connect...');
    
    const startTime = Date.now();
    await mongoose.connect(dbURI, { 
      useNewUrlParser: true, 
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 10000 // 10 second timeout
    });
    
    const connectionTime = Date.now() - startTime;
    
    console.log(`✅ Connection successful! (${connectionTime}ms)`);
    console.log(`📍 Connected to: ${mongoose.connection.name}`);
    console.log(`🏠 Host: ${mongoose.connection.host}:${mongoose.connection.port}`);
    console.log(`📊 Ready State: ${mongoose.connection.readyState} (1 = connected)`);
    
    // Test a simple operation
    console.log('\n🧪 Testing database operations...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`📁 Found ${collections.length} collections in database`);
    
    if (collections.length > 0) {
      console.log('   Collections:', collections.map(c => c.name).join(', '));
    }
    
    console.log('\n✅ Database connection test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Database connection failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.name === 'MongooseServerSelectionError') {
      console.error('\n💡 Troubleshooting tips:');
      if (isProd) {
        console.error('   • Make sure MongoDB is running locally');
        console.error('   • Check if MongoDB service is started');
        console.error('   • Verify MongoDB is listening on port 27017');
        console.error('   • Try: mongosh mongodb://localhost:27017');
      } else {
        console.error('   • Check your MONGODB_URI in .env file');
        console.error('   • Verify your IP is whitelisted in MongoDB Atlas');
        console.error('   • Confirm database credentials are correct');
        console.error('   • Check network connectivity');
      }
    }
    
    process.exit(1);
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('🔌 Connection closed');
    }
  }
}

// Run the test
testDatabaseConnection();
