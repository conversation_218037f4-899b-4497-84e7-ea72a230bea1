const express = require('express');
const { generateCSR, submitCSR, updateSSLCertificateStatus, verifyCSR } = require('../controllers/sslController');
const { fetchUserMiddleware } = require('../midelwares/authorization');
const { asyncBackFrontEndLang } = require('../midelwares/sharedMidd');

const sslRouter = express.Router();

// Route to generate CSR
sslRouter.post('/generate-csr', 
    asyncBackFrontEndLang, 
    fetchUserMiddleware, 
    generateCSR
);

// Route to submit CSR for a specific certificate in a suborder
sslRouter.post('/submit-csr/:subOrderId', 
    asyncBackFrontEndLang, 
    fetchUserMiddleware, 
    submitCSR
);

// Route to update SSL certificate status
sslRouter.put('/ssl-certificate/:subOrderId/:certificateIndex', 
    asyncBackFrontEndLang, 
    fetchUserMiddleware, 
    updateSSLCertificateStatus
);

// Add this route to your existing routes
sslRouter.post('/verify-csr', 
    asyncBackFrontEndLang, 
    fetchUserMiddleware, 
    verifyCSR
);

module.exports = sslRouter;