"use client";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Input,
  Spinner,
  Typography,
} from "@material-tailwind/react";
import { costomInputClasses, costomLabelClasses } from "./sharedBetweenAuth";
import { useState } from "react";
import ReCAPTCHA from "react-google-recaptcha";
import { generateObjectFromHeaders } from "../../app/helpers/helpers";
import { useRouter } from "next/navigation";
import authService from "../../app/services/authService";
import SocialMediaLogin from "./socialLogin";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import logoImg from "/public/images/home/<USER>";

const getHeaders = () => {
  return [
    {
      name: "firstName",
      type: "text",
      placeHolder: "first_name",
      title: "prenom",
      errors: "",
    },
    {
      name: "lastName",
      type: "text",
      placeHolder: "last_name",
      title: "nom",
      errors: "",
    },
    {
      name: "email",
      type: "email",
      title: "email",
      placeHolder: "email",
      errors: "",
    },
    {
      name: "password",
      type: "password",
      title: "password",
      placeHolder: "password",
      errors: "",
    },
    {
      name: "confirmPassword",
      title: "confirmPassword",
      type: "password",
      placeHolder: "confirm_password",
      errors: "",
    },
  ];
};
const Register = () => {
  const t = useTranslations("auth");
  const router = useRouter();

  const [loadingbtn, setLoadingbtn] = useState(false);
  const [headers, setHeaders] = useState(getHeaders());
  const [user, setUser] = useState(generateObjectFromHeaders(headers));
  const [recaptchaValue, setRecaptchaValue] = useState("");
  const [recaptchaError, setRecaptchaError] = useState("");

  const save = async () => {
    event.preventDefault();
    setRecaptchaError("");
    if (!recaptchaValue) {
      console.log("Recaptcha value is required for this request to work");
      setRecaptchaError(t("recaptcha_required"));
      return;
    }
    setLoadingbtn(true);
    // init errors
    headers.map((header, index) => (headers[index].errors = ""));
    setHeaders([...headers]);
    try {
      const userPayload = {
        ...user,
        "g-recaptcha-response": recaptchaValue,
      };
      const res = await authService.register(userPayload);
      console.log("res of registration...", res);
      setLoadingbtn(false);
      navigate("verification?emailActivation=pending");
    } catch (error) {
      console.log(error);
      setLoadingbtn(false);
      if (error.response.status == 400) {
        const errors = error.response.data.errors;
        errors.forEach((err) => {
          const index = headers.findIndex((header) => header.name == err.key);
          if (index >= 0) {
            headers[index].errors = err.msg
              .replace(headers[index].name, headers[index].title)
              .replace(/"/g, "");
          }
          if (err["key"] == "g-recaptcha-response") {
            setRecaptchaError(err["msg"]);
          }
        });
        setHeaders([...headers]);
      }
    }
  };

  const handleChange = (e) => {
    console.log("e: " + e.target.id);
    setUser({ ...user, [e.target.id]: e.target.value });
  };

  const navigate = (toPage) => {
    // router.push("/auth/" + toPage);
    window.location.href = "/auth/" + toPage;
    // ('/auth/' + toPage);
  };
  return (
    <div className="flex flex-col items-center md:py-4 md:mt-4 md:space-y-6 w-full">
      {/* Logo and Login Redirect */}
      <div className="hidden flex-col items-center">
        <Link href="/">
          <Image
            width={200}
            height={60}
            className="object-cover object-center"
            alt="nature image"
            placeholder="blur"
            src={logoImg}
          />
        </Link>
        <Typography
          color="gray"
          className="md:mt-4 text-sm font-normal text-center"
        >
          {t("do_you_have_account")}
          <span
            onClick={() => navigate("login")}
            className="mx-1 cursor-pointer font-bold hover:underline"
          >
            {t("login")}
          </span>
        </Typography>
      </div>

      {/* Form and Social Media Section */}
      <div className="flex flex-wrap flex-col-reverse md:flex-row items-start w-full max-w-4xl rounded-lg shadow-none">
        {/* Registration Form */}
        <Card
          className="flex-1 p-6 w-full md:w-1/2"
          color="transparent"
          shadow={false}
        >
          <form
            onSubmit={save}
            className="md:space-y-6 space-y-2 w-full max-w-md mx-auto"
          >
            <div className="space-y-4">
              {headers.map((header) => (
                <div key={header.name}>
                  <Input
                    onChange={handleChange}
                    size="lg"
                    type={header.type}
                    id={header.name}
                    label={t(header.placeHolder)}
                    error={!!header.errors}
                    className={`${costomInputClasses}`}
                    labelProps={{ className: costomLabelClasses }}
                  />
                  {header.errors && (
                    <Typography
                      variant="small"
                      color="red"
                      className="mt-1 text-xs"
                    >
                      {header.errors}
                    </Typography>
                  )}
                </div>
              ))}
            </div>

            {/* ReCAPTCHA */}
            <div className="flex flex-col items-center">
              <ReCAPTCHA
                sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
                onChange={setRecaptchaValue}
              />
              {recaptchaError && (
                <Typography
                  variant="small"
                  color="red"
                  className="mt-2 text-xs"
                >
                  {recaptchaError}
                </Typography>
              )}
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={loadingbtn}
              className="w-full mt-2 bg-secondary flex justify-center items-center text-base font-medium hover:bg-primary-200"
            >
              {loadingbtn && <Spinner className="h-4 w-4 text-white mr-2" />}
              {t("register")}
            </Button>
          </form>
        </Card>

        {/* Divider and Social Media Login */}
        <div className="flex-1 p-6 pb-0 md:pb-6 w-full md:w-1/2">
          <div className="flex flex-col-reverse md:flex-col">
            <div className="flex items-center my-6">
              <span className="flex-grow border-t border-gray-300 mr-3"></span>
              <span className="text-gray-500">{t("or")}</span>
              <span className="flex-grow border-t border-gray-300 ml-3"></span>
            </div>
            <SocialMediaLogin t={t} title={"register"} />
          </div>
          <div className="flex flex-col items-center md:mt-4">
            <Typography
              color="gray"
              className="mt-4 text-sm font-normal text-center"
            >
              {t("do_you_have_account")}
              <span
                onClick={() => navigate("login")}
                className="mx-1 cursor-pointer font-semibold underline text-primary"
              >
                {t("login")}
              </span>
            </Typography>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
