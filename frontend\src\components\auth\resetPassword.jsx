"use client";
import React, { useEffect, useState } from "react";
import { costomInputClasses, costomLabelClasses } from "./sharedBetweenAuth";
import {
  <PERSON><PERSON>,
  Card,
  Input,
  Spinner,
  Typography,
} from "@material-tailwind/react";
import authService from "../../app/services/authService";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import Link from "next/link";
import Image from "next/image";

const ResetPassword = () => {
  const t = useTranslations("auth");
  const searchParams = useSearchParams();
  const router = useRouter();
  const userId = searchParams.get("userId");
  const activationField = searchParams.get("activationField");

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [fieldErrors, setFieldErrors] = useState({});
  const [globalError, setGlobalError] = useState("");
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [resetSuccessful, setResetSuccessful] = useState(false);

  useEffect(() => {
    if (!userId || !activationField) {
      router.push("/auth/login");
    }
  }, [userId, activationField]);

  const save = async (event) => {
    event.preventDefault();
    setFieldErrors({});
    setGlobalError("");
    setLoadingBtn(true);

    const data = {
      userId,
      activationField,
      password,
      confirmPassword,
    };

    try {
      await authService.resetPassword(data);
      setLoadingBtn(false);
      setResetSuccessful(true);
      setTimeout(() => {
        router.push("/auth/login");
      }, 4000);
    } catch (error) {
      console.error(error);
      setLoadingBtn(false);
      const errors = error?.response?.data?.errors || [];
      const formattedErrors = errors.reduce((acc, err) => {
        acc[err.key] = err.msg.replace(/"/g, "");
        return acc;
      }, {});
      setFieldErrors(formattedErrors);

      const globalErrorMessage =
        error?.response?.data?.message || t("unexpected_error");
      setGlobalError(globalErrorMessage);
    }
  };

  return (
    <div className="w-full grid justify-center py-4 2xl:mt-16 xl:mt-10 md:mt-8 mt-6">
      {resetSuccessful ? (
        <div className="text-green-600 text-center mb-6">
          <Image
            className="h-auto mx-auto"
            src={"/images/verification-success.svg"}
            width={400}
            height={400}
            alt="Password reset successful"
          />
          <Typography variant="body1">
            {t("password_reset_successful")}
          </Typography>
        </div>
      ) : (
        <>
          <div className="grid justify-items-center items-center">
            <Link href="/">
              <Image
                width={200}
                height={60}
                className="object-cover object-center"
                src="/images/home/<USER>"
                alt="Logo"
              />
            </Link>
          </div>

          <Card
            className="my-4 sm:w-96 w-80"
            color="transparent"
            shadow={false}
          >
            {globalError && (
              <Typography
                variant="small"
                color="red"
                className="text-center mb-4"
              >
                {globalError}
              </Typography>
            )}
            <form onSubmit={save} className="mt-4 mb-2 md:max-w-screen-lg">
              <div className="flex flex-col gap-1 mb-6">
                <Input
                  key="newPassword"
                  onChange={(e) => setPassword(e.target.value)}
                  size="lg"
                  type="password"
                  id="newPassword"
                  label={t("new_password")}
                  className={costomInputClasses}
                  labelProps={{
                    className: costomLabelClasses,
                  }}
                  error={!!fieldErrors.password}
                />
                {fieldErrors.password && (
                  <Typography
                    id="passwordError"
                    variant="small"
                    color="red"
                    className="flex mb-2 items-center gap-1 text-xs"
                  >
                    {fieldErrors.password}
                  </Typography>
                )}
                <Input
                  key="repeatPassword"
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  size="lg"
                  type="password"
                  id="repeatPassword"
                  label={t("confirm_password")}
                  className={costomInputClasses}
                  labelProps={{
                    className: costomLabelClasses,
                  }}
                  error={!!fieldErrors.confirmPassword}
                />
                {fieldErrors.confirmPassword && (
                  <Typography
                    id="confirmPasswordError"
                    variant="small"
                    color="red"
                    className="flex items-center gap-1 text-xs"
                  >
                    {fieldErrors.confirmPassword}
                  </Typography>
                )}
              </div>
              <Button
                type="submit"
                disabled={loadingBtn}
                className="bg-secondary flex justify-center gap-2 items-center font-medium py-2 text-base hover:bg-primary-200"
                style={{ boxShadow: "none", textTransform: "none" }}
                fullWidth
              >
                {loadingBtn && <Spinner className="h-4 w-4 text-white" />}
                {t("reset_password")}
              </Button>
            </form>
            {/* Divider */}
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500">{t("or")}</span>
              </div>
            </div>

            {/* Back to Login */}
            <div className="text-center">
              <button
                onClick={() => router.push("/auth/login")}
                className="text-blue-500 hover:text-blue-700 text-sm transition-all font-medium"
              >
                {t("back_to_login")}
              </button>
            </div>
          </Card>
        </>
      )}
    </div>
  );
};

export default ResetPassword;
