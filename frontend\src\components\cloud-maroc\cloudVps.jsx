"use client";
import React from "react";
import { <PERSON>po<PERSON>, <PERSON>, But<PERSON> } from "@material-tailwind/react";
import {
  BACKUPsvg2,
  BLUECHECKsvg,
  DASHBOARDsvg,
  RAMsvg,
  SSDDISKsvg,
  VCPUsvg,
} from "../../icons/svgIcons";
import Image from "next/image";
import { ArrowRightIcon } from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { CheckCircle, TimerIcon } from "lucide-react";
import Lottie from "lottie-react";
import VPSHostingAnimation from "src/assets/vps-hosting.json";
import Section from "../home/<USER>";


const CloudVps = ({ t, setData }) => {
  const router = useRouter();

  const handleOfferClick = (offerName) => {
    const initialeMessage = t("pack_cloud_vps");
    setData((prevState) => ({
      ...prevState,
      offerName,
      id: 2,
      initialeMessage,
    }));
    router.push("#contact-nous");
  };

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  };

  return (
    <Section className="bg-[#F2F4FB]">
      <div className="mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="flex flex-col md:flex-row justify-between gap-8 md:gap-32 items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeInUp}
        >
          <div className="md:basis-1/2 hidden md:block">
          <Lottie
                animationData={VPSHostingAnimation}
                loop={true}
                className="w-full max-w-sm lg:max-w-md"
              />
          </div>
          <div className="md:basis-3/4 flex flex-col gap-y-6 text-left">
            <Typography
              variant="h2"
              className="font-extrabold font-inter text-3xl md:text-5xl capitalize text-primary"
            >
              {t("cloud_vps_title")}
            </Typography>
            <Typography className="text-gray-700 text-base md:text-lg">
              {t("cloud_vps_description")}
              <br />
              <br />
              {t("cloud_vps_support")}
            </Typography>
            <Button
              onClick={() => handleOfferClick(t("cloud_vps_title"))}
              className="w-full md:w-fit flex items-center justify-center gap-3 py-2 px-6 text-white uppercase rounded-3xl bg-secondary hover:bg-blue-700 font-inter text-sm font-medium transition-all"
            >
              {t("request_quote")}
              <ArrowRightIcon className="w-4 h-4" />
            </Button>
          </div>
          <div className="md:basis-1/2 block md:hidden">
          <Lottie
                animationData={VPSHostingAnimation}
                loop={true}
                className="w-full max-w-sm lg:max-w-md"
              />
          </div>
          
        </motion.div>
        {/* Features Section */}
        <motion.div
          className="max-w-[1400px] mx-auto py-10 text-center text-primary"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeInUp}
        >
          <div className="flex flex-col items-center mb-10">
            <Typography
              variant="h4"
              className="text-2xl md:text-3xl font-semibold text-primary"
            >
              {t("host_cloud_vps_in_morocco")}
            </Typography>
            <div className="flex items-center gap-2 mt-2">
              <Image
                width={32}
                height={32}
                src="/images/services/Morocco-flag.png"
                alt="Morocco flag"
                className="w-8 h-8"
              />
              <Typography
                variant="h4"
                className="text-3xl font-inter text-secondary font-medium"
              >
                {t("maroc")}
              </Typography>
              <Image
                width={32}
                height={32}
                src="/images/services/Morocco-flag.png"
                alt="Morocco flag"
                className="w-8 h-8"
              />
            </div>
          </div>

          {/* Unique Grid Arrangement */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-6 max-w-6xl mx-auto">
            {/* Card 1: Large Featured Card */}
            <FeatureCard
              icon={<VCPUsvg />}
              title="512 vCPU"
              desc={t("up_to_512_vcpu_per_instance")}
              className="sm:col-span-2 lg:col-span-1"
            />

            {/* Card 2: Medium Card */}
            <FeatureCard
              icon={<RAMsvg />}
              title={t("1024_gb_ram")}
              desc={t("up_to_1024_gb_ram_per_instance")}
              className="sm:col-span-2 lg:col-span-2"
            />

            {/* Card 3: Small Card */}
            <FeatureCard
              icon={<SSDDISKsvg />}
              title={t("nvme_ssd_storage")}
              desc={t("customizable_and_scalable")}
              className="sm:col-span-1 lg:col-span-1"
            />

            

            {/* Card 4: Medium Card */}
            <FeatureCard
              icon={<DASHBOARDsvg />}
              title={t("management_interface")}
              desc={t("for_remote_instances")}
              className="sm:col-span-2 lg:col-span-2"
            />
            {/* Card 5: Small Card */}
            <FeatureCard
              icon={<BACKUPsvg2 />}
              title={t("automated_backups")}
              desc={t("on_remote_servers")}
              bg="bg-gradient-to-r from-[#497EF7] to-[#423FD9]"
              textColor="text-white"
              className="sm:col-span-1 lg:col-span-2 lg:row-span-2"
            />

            {/* Card 6: Small Card */}
            <FeatureCard
              icon={<SSDDISKsvg />}
              title={t("custom_managed_services")}
              desc=""
              className="sm:col-span-1 lg:col-span-1"
            />

            {/* Card 7: Small Card */}
            <FeatureCard
              icon={<SSDDISKsvg />}
              title={t("shared_or_dedicated_connectivity")}
              desc=""
              className="sm:col-span-1 lg:col-span-1"
            />

            {/* Card 11: Large Card */}
            <FeatureCard
              icon={<SSDDISKsvg />}
              title={t("redundancy")}
              desc={t("on_two_locations")}
              className="sm:col-span-2 lg:col-span-1"
            />

            {/* Card 10: Medium Featured Card */}
            <FeatureCard
              icon={<BLUECHECKsvg />}
              title={t("advanced_waf")}
              desc={t("included")}
              className="sm:col-span-1 lg:col-span-1"
            />
            {/* Card 8: Small Card */}
            <FeatureCard
              icon={<CheckCircle className="w-8 h-8 text-indigo-600" />}
              title={t("24_hour_activation")}
              desc={t("fast_activation_in_24_hours")}
              className="sm:col-span-1 lg:col-span-2"
            />

            {/* Card 9: Small Card */}
            <FeatureCard
              icon={<TimerIcon className="w-8 h-8 text-indigo-600" />}
              title={t("uptime_99_9_percent")}
              desc={t("guaranteed")}
              className="sm:col-span-1 lg:col-span-2"
            />
          </div>
        </motion.div>
      </div>
    </Section>
  );
};

// Reusable Feature Card Component
const FeatureCard = ({
    icon,
    title,
    desc,
    bg = "bg-white",
    textColor = "text-black",
    extraContent,
    className = "",
  }) => (
    <Card
      className={`p-4 flex flex-col items-center justify-center gap-2 ${bg} shadow-md rounded-lg h-full min-h-[150px] hover:scale-105 transition-transform duration-300 ${className}`}
    >
      {icon && (
        <div
          className={`w-fit h-fit rounded-xl p-2 ${
            bg === "bg-white" ? "bg-[#F2F4FB]" : ""
          }`}
        >
          {icon}
        </div>
      )}
      <Typography
        variant="h6"
        className={`font-semibold text-sm text-center ${textColor}`}
      >
        {title}
      </Typography>
      {desc && (
        <Typography
          className={`text-xs text-center ${
            textColor === "text-white" ? "text-gray-200" : "text-gray-600"
          }`}
        >
          {desc}
        </Typography>
      )}
      {extraContent && (
        <div className="flex items-center justify-center gap-2">
          {extraContent}
        </div>
      )}
    </Card>
  );

export default CloudVps;
