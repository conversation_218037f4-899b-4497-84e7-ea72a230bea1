export const Shipping_Fee = 170;

export const MarocRegions = [
  { code: 'tta', frLabel: "Tangier-Tetouan-Al Hociema", arLabel: "طنجة تطوان الحسيمة" },
  { code: 'o', frLabel: "Oriental", arLabel: "شرقية" },
  { code: 'fm', frLabel: "Fez-Meknes", arLabel: "فاس - مكناس" },
  { code: 'r', frLabel: "Rabat", arLabel: "الرباط" },
  { code: 'bk', frLabel: "<PERSON><PERSON>", arLabel: "بني ملال خنيفرة" },
  { code: 'sc', frLabel: "Settat-Casablanca", arLabel: "سطات الدار البيضاء" },
  { code: 'ms', frLabel: "Marrakech-Safi", arLabel: "مراكش آسفي" },
  { code: 'dt', frLabel: "Draa-Taffilalt", arLabel: "درعة تافيلالت" },
  { code: 'sm', frLabel: "Souss-Massa", arLabel: "سوس ماسة" },
  { code: 'go', frLabel: "Guelmim-Oued Noun", arLabel: "كلميم واد نون" },
  { code: 'le', frLabel: "Laayoune-Sakia El Hamra", arLabel: "العيون-الساقية الحمراء" },
  { code: 'de', frLabel: "Dakhla-Oued Ed-Dahab", arLabel: "الداخلة-وادي الذهب" },
];


export const getRegionFromCode = (code) => {
  return MarocRegions.filter((region => region.code == code))[0]
}