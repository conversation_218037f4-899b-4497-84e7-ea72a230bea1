{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx", "src/app/[locale]/admin/support/page.jsx", "src/app/[locale]/admin/pages/page.jsx", "src/app/[locale]/admin/settings/page.jsx", "src/app/[locale]/admin/products/page.jsx", "src/app/[locale]/admin/orders/page.jsx", "src/app/[locale]/admin/marketing/page.jsx", "src/app/[locale]/admin/Login.jsx", "src/app/[locale]/admin/jobs/page.jsx", "src/app/[locale]/admin/users/page.jsx", "src/app/[locale]/admin/posts/page.jsx", "src/app/[locale]/admin/Dashboard.jsx"], "exclude": ["node_modules"]}