import apiService from '../lib/apiService';

/**
 * Service for managing site settings
 */
const siteSettingsService = {
  /**
   * Get all site settings
   * @returns {Promise} - Promise that resolves to site settings
   */
  getSiteSettings: async () => {
    try {
      const response = await apiService.get('/admin/site-settings', { withCredentials: true });
      return response.data;
    } catch (error) {
      console.error('Error fetching site settings:', error);
      throw error;
    }
  },

  /**
   * Update all site settings
   * @param {Object} data - The updated site settings data
   * @returns {Promise} - Promise that resolves to the updated site settings
   */
  updateSiteSettings: async (data) => {
    try {
      const response = await apiService.put('/admin/site-settings', data, { withCredentials: true });
      return response.data;
    } catch (error) {
      console.error('Error updating site settings:', error);
      throw error;
    }
  },

  /**
   * Get a specific section of site settings (general or seo)
   * @param {string} section - The section name ('general' or 'seo')
   * @returns {Promise} - Promise that resolves to the section data
   */
  getSiteSettingsSection: async (section) => {
    try {
      const response = await apiService.get(`/admin/site-settings/${section}`, { withCredentials: true });
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${section} settings:`, error);
      throw error;
    }
  },

  /**
   * Update a specific section of site settings
   * @param {string} section - The section name ('general' or 'seo')
   * @param {Object} data - The updated section data
   * @returns {Promise} - Promise that resolves to the updated site settings
   */
  updateSiteSettingsSection: async (section, data) => {
    try {
      const response = await apiService.put(`/admin/site-settings/${section}`, data, { withCredentials: true });
      return response.data;
    } catch (error) {
      console.error(`Error updating ${section} settings:`, error);
      throw error;
    }
  },

  /**
   * Reset site settings to default values
   * @returns {Promise} - Promise that resolves to the default site settings
   */
  resetSiteSettings: async () => {
    try {
      const response = await apiService.post('/admin/site-settings/reset', {}, { withCredentials: true });
      return response.data;
    } catch (error) {
      console.error('Error resetting site settings:', error);
      throw error;
    }
  }
};

export default siteSettingsService;
