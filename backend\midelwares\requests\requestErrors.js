const enErrors = {
    'string.empty': '{{#label}} cannot be empty',
    'string.min': `{{#label}} must be at least {{#limit}} characters`,
    'string.alphanum': '{{#label}} must contain only alphanumeric characters',
    'number.base': '{{#label}} must be a number',
    'number.less': '{{#label}} must be less than the regular price',
    'string.pattern.base': 'invalid value',
    'any.invalid': '{{#label}} is not valid',
    'domain.invalid': 'Invalid domain',
    'email.invalid': 'Email address is not valid',
    'string.email': 'Email address is not valid',
    'phoneNumber.invalid': 'Phone number is not valid',
    'array.unique': '{{#label}} contains duplicate value',
    'custom.max11char': 'Maximum of 11 alphanumeric characters. Must start with a letter',
    'any.required': 'This field is required',
    'any.only': '{{#label}} must match {{#valids.0}}',
    'password.same': 'New password cannot be the same as the old password', // New error message
};

const frErrors = {
    'string.empty': '{{#label}} ne peut pas être vide',
    'string.min': `{{#label}} doit comporter au moins {{#limit}} caractères`,
    'string.alphanum': '{{#label}} ne doit contenir que des caractères alphanumériques',
    'number.base': '{{#label}} doit être un numéro',
    'number.less': '{{#label}} doit être inférieur à le prix habituel',
    'string.pattern.base': 'valeur non valide',
    'any.invalid': '{{#label}} valeur non valide',
    'domain.invalid': 'Domaine non valide',
    'email.invalid': 'Adresse e-mail non pas valide',
    'string.email': 'Adresse e-mail non pas valide',
    'phoneNumber.invalid': 'Numéro de téléphone non valide',
    'array.unique': '{{#label}} Valeur en double',
    'custom.max11char': 'Maximum 11 caractères alphanumériques. Doit commencer par une lettre',
    'any.required': 'Ce champ est obligatoire',
    'any.only': '{{#label}} doit correspondre à {{#valids.0}}',
    'password.same': 'Le nouveau mot de passe ne peut pas être le même que l’ancien mot de passe', // New error message
};

module.exports = { enErrors, frErrors };
