"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  CardBody,
  Button,
  Textarea,
  Spinner,
  Dialog,
  DialogBody,
} from "@material-tailwind/react";
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Info,
  ArrowLeft,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import ticketService from "../../../services/ticketService";
import {
  getStatusIcon,
  getPriorityColor,
  setServerMedia,
} from "../../../helpers/helpers";
import ImageModal from "@/components/admin/ImageModal";

// Status icon mapping for cleaner code
const STATUS_ICONS = {
  open: <Info className="text-blue-500" />,
  resolved: <CheckCircle className="text-green-500" />,
  closed: <XCircle className="text-red-500" />,
  pending: <Clock className="text-yellow-500" />,
  error: <AlertCircle className="text-red-500" />,
};

const TicketDetailsPage = () => {
  const { ticketId } = useParams();
  const router = useRouter();
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newComment, setNewComment] = useState("");
  const [comments, setComments] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null); // State for modal

  // Memoized fetch function
  const fetchTicket = useCallback(async () => {
    if (!ticketId) return;

    try {
      setLoading(true);
      const response = await ticketService.getTicketById(ticketId);
      setTicket(response.data.ticket);
    } catch (err) {
      console.error("Error fetching ticket:", err);
      setError("Failed to load ticket details. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [ticketId]);

  useEffect(() => {
    fetchTicket();
  }, [fetchTicket]);

  // Memoized comment handler
  const handleAddComment = useCallback(() => {
    if (!newComment.trim()) return;

    const comment = {
      id: crypto.randomUUID(),
      text: newComment,
      author: "Current User", // Replace with auth context
      createdAt: new Date().toISOString(),
    };

    setComments((prev) => [comment, ...prev]);
    setNewComment("");
  }, [newComment]);

  // Handle back navigation
  const handleBack = () => {
    router.back();
  };

  // Handle image click to open modal
  const openImageModal = (image) => {
    setSelectedImage(image);
  };

  // Close modal
  const closeImageModal = (e) => {
    if (e) {
      e.preventDefault(); // Prevent default behavior
      e.stopPropagation(); // Stop event bubbling
    }
    setSelectedImage(null); // Close the modal
  };

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (selectedImage) {
      document.body.style.overflow = "hidden"; // Disable scrolling
    } else {
      document.body.style.overflow = "auto"; // Re-enable scrolling
    }
    return () => {
      document.body.style.overflow = "auto"; // Cleanup on unmount
    };
  }, [selectedImage]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Spinner className="h-12 w-12 text-blue-500" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-6 gap-4">
        <AlertCircle className="h-12 w-12 text-red-500" />
        <Typography variant="h5" className="text-red-600">
          {error}
        </Typography>
        <Button onClick={fetchTicket} color="blue">
          Retry
        </Button>
      </div>
    );
  }

  // Not found state
  if (!ticket) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Typography variant="h5" className="text-gray-600">
          Ticket Not Found
        </Typography>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button
          variant="text"
          color="gray"
          onClick={handleBack}
          className="flex items-center gap-2 hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft size={20} />
          Back
        </Button>
      </div>

      <Card className="shadow-xl">
        <CardBody className="space-y-6">
          {/* Header */}
          <div className="flex flex-col gap-4 md:flex-row md:justify-between md:items-start">
            <div className="flex items-start gap-4">
              {STATUS_ICONS[ticket.status] || STATUS_ICONS.error}
              <div>
                <Typography variant="h4" className="text-gray-900 font-bold">
                  {ticket.subject}
                </Typography>
                <Typography className="text-gray-600">
                  {ticket.message}
                </Typography>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(
                  ticket.priority
                )}`}
              >
                {ticket.priority}
              </span>
              <Typography variant="small" className="text-gray-500">
                Created:{" "}
                {new Date(ticket.createdAt).toLocaleDateString("fr-FR")}
              </Typography>
            </div>
          </div>

          {/* Images with click handler */}
          {ticket.images?.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {ticket.images.map((image, index) => (
                <img
                  key={index}
                  src={setServerMedia(image)}
                  alt={`Ticket Image ${index + 1}`}
                  className="h-48 w-full object-cover rounded-lg shadow-md cursor-pointer hover:opacity-90 transition-opacity"
                  loading="lazy"
                  onClick={() => openImageModal(setServerMedia(image))}
                />
              ))}
            </div>
          )}

          {/* Image Modal */}
          <ImageModal
            isOpen={!!selectedImage}
            imageSrc={selectedImage}
            onClose={closeImageModal}
          />

          {/* Details */}
          <div className="grid gap-4 md:grid-cols-2">
            <DetailItem
              label="Contact"
              value={`${ticket.creator.firstName} ${ticket.creator.lastName} (${ticket.creator.email})`}
            />
            <DetailItem label="Category" value={ticket.service} />
            <DetailItem label="Status" value={ticket.status} />
            <DetailItem
              label="Resolution"
              value={
                ticket.resolutionComment || "No resolution comment available."
              }
              className={!ticket.resolutionComment ? "text-gray-500" : ""}
            />
          </div>

          {/* Comments Section (Commented out as requested) */}
          {/*
          <CommentsSection 
            comments={comments} 
            newComment={newComment}
            onCommentChange={setNewComment}
            onSubmitComment={handleAddComment}
          />
          */}
        </CardBody>
      </Card>
    </div>
  );
};

// Reusable Detail Item Component
const DetailItem = ({ label, value, className = "" }) => (
  <div>
    <Typography variant="small" className="font-medium text-gray-700">
      {label}:
    </Typography>
    <Typography className={`text-gray-600 ${className}`}>{value}</Typography>
  </div>
);

// Image Modal Component

// Comments Section Component (Commented out as requested)
/*
const CommentsSection = ({ comments, newComment, onCommentChange, onSubmitComment }) => (
  <div className="space-y-4">
    <Typography variant="h5" className="text-gray-900 font-bold">
      Comments
    </Typography>
    
    {comments.length > 0 ? (
      comments.map((comment) => (
        <Card key={comment.id} className="bg-gray-50">
          <CardBody>
            <div className="flex justify-between items-center mb-2">
              <Typography className="font-medium text-gray-700">
                {comment.author}
              </Typography>
              <Typography variant="small" className="text-gray-500">
                {new Date(comment.createdAt).toLocaleString()}
              </Typography>
            </div>
            <Typography className="text-gray-600">{comment.text}</Typography>
          </CardBody>
        </Card>
      ))
    ) : (
      <Typography className="text-gray-500">No comments yet.</Typography>
    )}

    <Textarea
      value={newComment}
      onChange={(e) => onCommentChange(e.target.value)}
      placeholder="Write your comment here..."
      className="w-full"
    />
    <Button 
      onClick={onSubmitComment}
      color="blue"
      disabled={!newComment.trim()}
    >
      Submit Comment
    </Button>
  </div>
);
*/

export default TicketDetailsPage;
