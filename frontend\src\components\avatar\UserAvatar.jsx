"use client";
import React, { useState, useRef, useEffect } from "react";
import { UserCircle, User, LogOut, Home, ChevronDown } from "lucide-react";
import { setServerMedia } from "../../app/helpers/helpers";
import { useRouter } from "next/navigation";
import { useAuth } from "../../app/context/AuthContext";

const UserAvatar = ({ user, size = "md", showName = false, className = "" }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef(null);
  const router = useRouter();
  const { logout } = useAuth();

  // Size classes mapping
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-10 h-10",
    xl: "w-12 h-12"
  };

  // Icon size mapping
  const iconSizes = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-10 h-10"
  };

  // Get the appropriate size class
  const avatarSize = sizeClasses[size] || sizeClasses.md;
  const iconSize = iconSizes[size] || iconSizes.md;

  // Handle click outside to close menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMenuOpen]);

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      setIsMenuOpen(false);
      router.push("/auth/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // Navigate to profile
  const goToProfile = () => {
    router.push("/client/profile");
    setIsMenuOpen(false);
  };

  // Navigate to client website
  const goToClientWebsite = () => {
    router.push("/");
    setIsMenuOpen(false);
  };

  // Toggle menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <div className={`relative flex items-center space-x-2 ${className}`} ref={menuRef}>
      <button 
        onClick={toggleMenu}
        className="flex items-center space-x-2 focus:outline-none"
        aria-expanded={isMenuOpen}
        aria-haspopup="true"
      >
        <div className={`${avatarSize} rounded-full flex items-center justify-center overflow-hidden ${!user?.photo ? "bg-blue-500 text-white" : ""}`}>
          {user?.photo ? (
            <img 
              src={setServerMedia(user.photo)} 
              alt={`${user.firstName || ''} ${user.lastName || ''}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.target.onerror = null;
                e.target.style.display = "none";
                e.target.parentNode.classList.add("bg-blue-500", "text-white");
                e.target.parentNode.innerHTML = `<div class="${iconSize}"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user"><circle cx="12" cy="8" r="5"/><path d="M20 21a8 8 0 0 0-16 0"/></svg></div>`;
              }}
            />
          ) : (
            <UserCircle className={iconSize} />
          )}
        </div>
        
        {showName && user && (
          <div className="flex items-center space-x-1">
            <span className="text-sm font-medium text-gray-700 hidden md:inline-block">
              {`${user?.firstName || ''} ${user?.lastName || ''}` || "User"}
            </span>
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </div>
        )}
      </button>
      
      {/* Dropdown Menu */}
      {isMenuOpen && (
        <div className="absolute right-0 mt-2 top-full z-10 w-48 bg-white rounded-md shadow-lg py-1 border border-gray-200">
          <div className="px-4 py-2 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-900 truncate">
              {`${user?.firstName || ''} ${user?.lastName || ''}`}
            </p>
            <p className="text-xs text-gray-500 truncate">{user?.email}</p>
          </div>
          
          <button
            onClick={goToProfile}
            className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <User className="mr-3 h-4 w-4 text-gray-500" />
            Profile
          </button>
          
          <button
            onClick={goToClientWebsite}
            className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <Home className="mr-3 h-4 w-4 text-gray-500" />
            Client Website
          </button>
          
          <div className="border-t border-gray-100 my-1"></div>
          
          <button
            onClick={handleLogout}
            className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50"
          >
            <LogOut className="mr-3 h-4 w-4 text-red-500" />
            Sign Out
          </button>
        </div>
      )}
    </div>
  );
};

export default UserAvatar;
