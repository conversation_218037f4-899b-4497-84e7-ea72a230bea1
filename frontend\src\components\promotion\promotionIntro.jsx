import { Card, Typography } from '@material-tailwind/react'
import React from 'react'
import { EARTHsvg, H24svg, SHIELDCHECKsvg, ZTECHLOGOsvg } from '../../icons/svgIcons';
import Link from 'next/link';
import { BsCart3 } from 'react-icons/bs';

function PromotionIntro({ t }) {

    const featuresData = [
        {
            icon: <ZTECHLOGOsvg />,
            title: 'features.0.title',
            description: 'features.0.description',
        },
        {
            icon: <EARTHsvg />,
            title: 'features.1.title',
            description: "features.1.description",
        },
        {
            icon: <SHIELDCHECKsvg width={32} />,
            title: 'features.2.title',
            description: "features.2.description",
        },
        {
            icon: <H24svg width={32} />,
            title: 'features.3.title',
            description: "features.3.description",
        },
    ];


    return (
        <main className='w-full'>
            <div
                className="max-w-[1400px] mx-auto px-6 pt-6 flex flex-col relative">
                <section
                    style={{
                        backgroundImage: "url('/images/services/bg-img.svg')",
                        backgroundRepeat: "no-repeat",
                        backgroundSize: "cover",
                        backgroundPosition: "center"
                    }}
                    className="bg-center text-white mx-auto w-full flex flex-col items-center gap-y-6 py-10 px-2  md:p-16 rounded-3xl">
                    <div className="bg-[#090416] p-0">
                        <img
                            src="/images/10remis.svg"
                            alt="10% remise"
                            className='mx-auto p-0 m-0'
                        />
                    </div>
                    <Typography
                        variant='h1'
                        className='text-5xl font-extrabold text-white max-w-xl text-center'>
                        {t('exclusiveOfferMessage')}
                    </Typography>
                    <p className='text-center md:max-w-xl w-full'>
                        {t('discountMessage')}
                    </p>
                    <Link
                        href="/web-development"
                        className="capitalize text-base text-primary hover:bg-secondary hover:text-white hover:border-secondary font-medium border rounded-full font-inter py-2 bg-white px-6 flex flex-row gap-x-2">
                        <span>
                            {t('orderNowButton')}
                        </span>
                        <span><BsCart3 className='text-[20px] my-auto' /></span>
                    </Link>
                </section>

                <section className="bg-transparent  max-w-4xl mx-auto md:mt-24 mt-10 flex flex-col gap-y-12">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {featuresData.map((feature, index) => (
                            <Card
                                key={index}
                                className="group  relative p-6 bg-white rounded-lg shadow-md border overflow-hidden transition-all duration-500 ease-in-out"
                            >
                                {/* Gradient Background */}
                                <div
                                    className={`absolute inset-0 bg-gradient-secondary scale-y-0 origin-bottom transition-transform duration-500 ease-in-out group-hover:scale-y-100`}
                                ></div>
                                <div className="flex flex-col items-start gap-y-4 z-10 relative">
                                    <div className="text-secondary group-hover:bg-white rounded-full p-1 text-2xl flex-shrink-0">{feature.icon}</div>
                                    <div>
                                        <Typography
                                            variant='h3'
                                            className="font-semibold text-lg mb-2 group-hover:text-white">
                                            {t(feature.title)}
                                        </Typography>
                                        <p className="text-gray-600 group-hover:text-white">
                                            {t(feature.description)}
                                        </p>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                    <div className="bg-gradient-secondary md:p-16 p-4 rounded-3xl shadow-inner flex flex-col gap-y-6">
                        <Typography
                            variant='h1'
                            className='md:text-2xl text-lg text-white font-semibold text-center font-inter'>
                            {t('orderNowDescription')}
                        </Typography>
                        <Link
                            href="/web-development"
                            className="capitalize w-fit mx-auto text-base text-primary hover:bg-secondary hover:text-white hover:border-secondary font-medium border rounded-full font-inter py-2 bg-white px-6 flex flex-row gap-x-2">
                            <span>{t('orderNowTitle')}</span>
                            <span><BsCart3 className='text-[20px] my-auto' /></span>
                        </Link>
                    </div>
                </section>
            </div>
        </main>
    )
}

export default PromotionIntro