'use client'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image'
import React from 'react'
import hostingIntroImage from '/public/images/services/hosting-intro2.webp';

function Intro({ t }) {

    return (
        <div
            style={{
                backgroundImage: "url('/images/services/hosting-intro-bg-gradiant.png')",
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
                backgroundPosition: "center"
            }}
            className='w-full'>

            <div className="max-w-[1400px] mx-auto relative text-center flex flex-col items-center justify-center gap-y-2 p-4 overflow-hidden w-full">
                <p className="text-sm text-secondary font-semibold">
                    {t('our_services')}
                </p>
                <Typography variant='h1'
                    className='font-inter text-4xl md:text-5xl font-thin text-primary mb-2'>
                    {t('title')}
                </Typography>
                <p className='w-full md:w-2/3 text-center m-auto text-primary'>
                    {t('description')}
                </p>
                <div className="w-[90%] h-[300px] overflow-hidden rounded-xl my-5">
                    {/* <img
                        src="/images/services/hosting-intro.png"
                        alt="hosting"
                        className="w-full h-full object-cover"
                    /> */}
                    <Image
                        // src="/images/services/hosting-intro.png"
                        src={hostingIntroImage}
                        width={900}
                        height={300}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        alt="hosting"
                        className="w-full h-full object-cover"
                        placeholder='blur'
                        priority={true}
                    />
                </div>
            </div>
        </div>
    )
}

export default Intro