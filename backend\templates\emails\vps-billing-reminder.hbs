<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPS Billing Reminder</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }
        .alert-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .vps-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        .detail-value {
            color: #212529;
        }
        .cta-button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }
        .cta-button:hover {
            background-color: #1d4ed8;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .urgent {
            color: #dc3545;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">ZTech</div>
            <h2>VPS Billing Reminder</h2>
        </div>

        <div class="alert-box">
            <span class="alert-icon">⏰</span>
            <strong>
                {{#if (eq daysBefore 1)}}
                    <span class="urgent">Payment Due Tomorrow!</span>
                {{else if (lte daysBefore 3)}}
                    <span class="warning">Payment Due in {{daysBefore}} Days</span>
                {{else}}
                    <span class="info">Upcoming Payment in {{daysBefore}} Days</span>
                {{/if}}
            </strong>
        </div>

        <p>Dear {{customerName}},</p>

        <p>This is a friendly reminder that your VPS service has an upcoming billing date. Please review the details below and ensure your payment method is up to date to avoid any service interruption.</p>

        <div class="vps-details">
            <h3 style="margin-top: 0; color: #2563eb;">VPS Service Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Service Name:</span>
                <span class="detail-value">{{vpsName}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">VPS ID:</span>
                <span class="detail-value">{{vpsId}}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Billing Date:</span>
                <span class="detail-value">{{billingDate}}</span>
            </div>
            
            {{#if amount}}
            <div class="detail-row">
                <span class="detail-label">Amount:</span>
                <span class="detail-value">{{amount}} {{currency}}</span>
            </div>
            {{/if}}
            
            <div class="detail-row">
                <span class="detail-label">Days Until Billing:</span>
                <span class="detail-value">
                    {{#if (eq daysBefore 1)}}
                        <span class="urgent">{{daysBefore}} day</span>
                    {{else if (lte daysBefore 3)}}
                        <span class="warning">{{daysBefore}} days</span>
                    {{else}}
                        <span class="info">{{daysBefore}} days</span>
                    {{/if}}
                </span>
            </div>
        </div>

        <div style="text-align: center;">
            <a href="{{clientPortalUrl}}/client/hosting-plans" class="cta-button">
                Manage VPS Services
            </a>
        </div>

        <div style="margin: 20px 0; padding: 15px; background-color: #e3f2fd; border-radius: 6px;">
            <h4 style="margin-top: 0; color: #1976d2;">💡 Important Notes:</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Ensure your payment method is valid and has sufficient funds</li>
                <li>Update your billing information if needed in your account settings</li>
                <li>Contact support if you have any questions about your billing</li>
                {{#if (eq daysBefore 1)}}
                <li><strong>Service may be suspended if payment fails tomorrow</strong></li>
                {{/if}}
            </ul>
        </div>

        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

        <p>Thank you for choosing ZTech for your VPS hosting needs!</p>

        <div class="footer">
            <p>
                <strong>ZTech Support Team</strong><br>
                Email: <EMAIL><br>
                Phone: +****************
            </p>
            <p style="font-size: 12px; color: #999;">
                This is an automated billing reminder. Please do not reply to this email.
            </p>
        </div>
    </div>
</body>
</html>
