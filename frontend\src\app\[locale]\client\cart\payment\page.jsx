'use client';
import { CheckBadgeIcon } from '@heroicons/react/24/solid';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../../context/AuthContext';
import { Spinner } from '@material-tailwind/react';

function PaymentPage() {
    const t = useTranslations('client');
    const router = useRouter();
    const { cartCount } = useAuth();

    // Redirect to the previous page if the cartCount is greater than 0
    useEffect(() => {
        if (cartCount > 0) {
            router.back();
        } else {
            setTimeout(() => {
                router.push('/client/orders')
            }, 5000);
        }
    }, [cartCount, router]);

    // Show loading spinner or success message based on the cartCount
    if (cartCount > 0) {
        return null; // Prevent rendering the page if cartCount is not 0
    }

    return (
        <div className="flex justify-center items-center mt-40">
            <div className="p-8 rounded-lg shadow-lg border-t border-gray-100 max-w-xl w-full space-y-6">
                {cartCount == 0 ? (
                    <>
                        <div className="text-center">
                            <div className="mb-6 flex flex-col items-center justify-center">
                                <CheckBadgeIcon className="text-green-500" width={50} />
                                <p className="text-lg text-gray-800 font-semibold">{t('success')}!</p>
                            </div>
                            <p className="text-xl text-gray-800 mb-4">{t('paymentGatewayMessage')}</p>
                            <p className="text-base text-gray-600">{t('redirectMessage')}</p>
                        </div>
                    </>
                ) : (
                    <div className="flex justify-center items-center">
                        <Spinner width={100} />
                    </div>
                )}
            </div>
        </div>
    );
}

export default PaymentPage;
