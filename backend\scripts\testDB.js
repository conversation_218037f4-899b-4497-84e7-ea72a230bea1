/**
 * Simple script to test database connection and check existing packages
 */

const mongoose = require('mongoose');
const Package = require('../models/Package');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const testDB = async () => {
  try {
    console.log('🔍 Testing database connection and packages...\n');
    
    await connectDB();
    
    // Check existing packages
    const packages = await Package.find({}).populate('brand');
    console.log(`📦 Found ${packages.length} packages in database`);
    
    if (packages.length > 0) {
      console.log('\n📋 Existing packages:');
      packages.forEach(pkg => {
        console.log(`- ${pkg.name} (${pkg._id}) - ${pkg.price} MAD`);
        if (pkg.vpsConfig) {
          console.log(`  VPS Config: ${pkg.vpsConfig.provider} - ${pkg.vpsConfig.providerProductId}`);
        }
      });
    }
    
    console.log('\n✅ Database test completed');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

testDB();
