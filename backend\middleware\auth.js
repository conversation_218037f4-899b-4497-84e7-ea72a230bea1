const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * Middleware d'authentification JWT
 */
const authenticateToken = async (req, res, next) => {
  try {
    console.log('=== AUTHENTICATE TOKEN MIDDLEWARE ===');
    console.log('Headers:', req.headers.authorization ? 'Authorization header present' : 'No authorization header');
    console.log('Cookies:', req.cookies.token ? 'Token cookie present' : 'No token cookie');

    const authHeader = req.headers['authorization'];
    let token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    // Si pas de token dans l'en-tête, chercher dans les cookies
    if (!token && req.cookies.token) {
      token = req.cookies.token;
      console.log('✅ Token found in cookies');
    }

    if (!token) {
      console.log('❌ No token found in authorization header or cookies');
      return res.status(401).json({
        success: false,
        message: 'Token d\'accès requis'
      });
    }

    console.log('✅ Token found, verifying...');

    // Vérifier le token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    console.log('✅ Token decoded successfully');
    console.log('Decoded payload:', { id: decoded.id, userId: decoded.userId, role: decoded.role });

    // Récupérer l'utilisateur (support pour 'id' et 'userId')
    const userId = decoded.userId || decoded.id;
    console.log('Looking for user with ID:', userId);

    const user = await User.findById(userId).select('-password');

    if (!user) {
      console.log('❌ User not found in database');
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    console.log('✅ User found:', { id: user._id, role: user.role, status: user.status });

    // Vérifier si l'utilisateur est actif (accepter undefined pour les admins)
    if (user.status && user.status !== 'active') {
      console.log('❌ User status is not active:', user.status);
      return res.status(401).json({
        success: false,
        message: 'Compte utilisateur désactivé'
      });
    }

    console.log('✅ User status is active, proceeding...');

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token invalide'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expiré'
      });
    }

    console.error('Erreur d\'authentification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification'
    });
  }
};

/**
 * Middleware pour vérifier les permissions admin
 */
const requireAdmin = (req, res, next) => {
  try {
    console.log('=== REQUIRE ADMIN MIDDLEWARE ===');

    if (!req.user) {
      console.log('❌ No user in request');
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    console.log('User role:', req.user.role);

    // Vérifier si l'utilisateur est admin (insensible à la casse)
    const userRole = req.user.role?.toLowerCase();
    console.log('User role (lowercase):', userRole);

    if (userRole !== 'admin' && userRole !== 'super_admin') {
      console.log('❌ User is not admin');
      return res.status(403).json({
        success: false,
        message: 'Accès refusé. Permissions administrateur requises.'
      });
    }

    console.log('✅ User is admin, proceeding...');
    next();
  } catch (error) {
    console.error('Erreur de vérification des permissions admin:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la vérification des permissions'
    });
  }
};

/**
 * Middleware pour vérifier les permissions super admin
 */
const requireSuperAdmin = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    // Vérifier si l'utilisateur est super admin (insensible à la casse)
    const userRole = req.user.role?.toLowerCase();
    if (userRole !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé. Permissions super administrateur requises.'
      });
    }

    next();
  } catch (error) {
    console.error('Erreur de vérification des permissions super admin:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la vérification des permissions'
    });
  }
};

/**
 * Middleware optionnel d'authentification (n'échoue pas si pas de token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    let token = authHeader && authHeader.split(' ')[1];

    // Si pas de token dans l'en-tête, chercher dans les cookies
    if (!token && req.cookies.token) {
      token = req.cookies.token;
    }

    if (!token) {
      req.user = null;
      return next();
    }

    // Vérifier le token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // Récupérer l'utilisateur (support pour 'id' et 'userId')
    const userId = decoded.userId || decoded.id;
    const user = await User.findById(userId).select('-password');
    
    if (user && (!user.status || user.status === 'active')) {
      req.user = user;
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    // En cas d'erreur, continuer sans utilisateur
    req.user = null;
    next();
  }
};

/**
 * Middleware pour limiter le taux de requêtes
 */
const rateLimit = (windowMs = 15 * 60 * 1000, max = 100) => {
  const requests = new Map();

  return (req, res, next) => {
    const ip = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Nettoyer les anciennes entrées
    for (const [key, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter(timestamp => timestamp > windowStart);
      if (validTimestamps.length === 0) {
        requests.delete(key);
      } else {
        requests.set(key, validTimestamps);
      }
    }

    // Vérifier le taux pour cette IP
    const ipRequests = requests.get(ip) || [];
    const validRequests = ipRequests.filter(timestamp => timestamp > windowStart);

    if (validRequests.length >= max) {
      return res.status(429).json({
        success: false,
        message: 'Trop de requêtes. Veuillez réessayer plus tard.',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // Ajouter cette requête
    validRequests.push(now);
    requests.set(ip, validRequests);

    next();
  };
};

/**
 * Middleware de validation des permissions pour les ressources
 */
const checkResourcePermission = (resourceType) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentification requise'
        });
      }

      // Super admin a accès à tout (insensible à la casse)
      const userRole = req.user.role?.toLowerCase();
      if (userRole === 'super_admin') {
        return next();
      }

      // Admin a accès aux ressources de base
      if (userRole === 'admin') {
        const allowedResources = ['vps-images', 'os-images', 'app-images', 'packages', 'users'];
        if (allowedResources.includes(resourceType)) {
          return next();
        }
      }

      // Utilisateur normal n'a accès qu'à ses propres ressources
      if (userRole === 'user') {
        const userResources = ['profile', 'orders', 'support'];
        if (userResources.includes(resourceType)) {
          return next();
        }
      }

      return res.status(403).json({
        success: false,
        message: `Accès refusé pour la ressource: ${resourceType}`
      });
    } catch (error) {
      console.error('Erreur de vérification des permissions de ressource:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la vérification des permissions'
      });
    }
  };
};

module.exports = {
  authenticateToken,
  requireAdmin,
  requireSuperAdmin,
  optionalAuth,
  rateLimit,
  checkResourcePermission
};
