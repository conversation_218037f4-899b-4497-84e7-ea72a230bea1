"use client";

import Link from "next/link";
import { <PERSON><PERSON>, Typo<PERSON> } from "@material-tailwind/react";
import { MessagesSquare, Bell, X, FlagTriangleLeft, Goal } from "lucide-react";
import { useTranslations } from "next-intl";

const SkeletonLoader = () => (
  <div className="space-y-6">
    {[...Array(3)].map((_, index) => (
      <div
        key={index}
        className="p-4 rounded-lg bg-gray-50 border border-gray-200 animate-pulse"
      >
        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
        <div className="flex justify-between items-center">
          <div className="h-3 bg-gray-300 rounded w-1/4"></div>
          <div className="h-3 bg-gray-300 rounded w-1/6"></div>
        </div>
        <div className="h-3 bg-gray-300 rounded w-1/3 mt-2"></div>
      </div>
    ))}
  </div>
);

const Sidebar = ({ isSidebar<PERSON>pen, setIsSidebarOpen, recentTickets, loading }) => {
  const t = useTranslations("client");
  const sidebarItems = [
    { label: "mysupporttickets", icon: MessagesSquare, href: "/client/support/tickets" },
    { label: "announcements", icon: Bell, href: "/client/support/announcements" },
  ];

  return (
    <div
      className={`fixed flex flex-col gap-10 lg:static inset-y-0 left-0 z-40 bg-white border-r border-gray-200 transform ${
        isSidebarOpen ? "translate-x-0" : "-translate-x-full"
      } lg:translate-x-0 transition-transform duration-300 ease-in-out w-72 pt-20 lg:pt-10 px-4`}
    >
      {/* Close Button */}
      <button
        onClick={() => setIsSidebarOpen(false)}
        className="absolute top-24 right-4 text-gray-600 hover:text-gray-800 lg:hidden"
        aria-label="Close Sidebar"
      >
        <X className="h-6 w-6" />
      </button>

      {/* Recent Tickets */}
      <div className="mb-8 mt-28 lg:mt-1.5">
        <Typography className="flex gap-2 items-center mb-4 font-inter text-gray-900 text-md">
          <Goal className="w-4 h-4" />
          {t("yourrecenttickets")}
        </Typography>
        <div className="space-y-6">
          {loading ? (
            <SkeletonLoader />
          ) : recentTickets && recentTickets.length > 0 ? (
            recentTickets.map((ticket) => (
              <Link key={ticket._id} href={`/client/support/tickets/${ticket._id}`} passHref>
                <div className="mb-4 p-2 rounded-md bg-gray-50 border border-gray-200 hover:bg-gray-100 transition duration-200">
                  <Typography color="blue" className="font-medium text-sm">
                    {ticket.subject}
                  </Typography>
                  <div className="flex justify-between items-center text-xs text-gray-600 mt-1">
                    <span>{t(ticket.status)}</span>
                    <span>{new Date(ticket.createdAt).toLocaleDateString("fr-FR")}</span>
                  </div>
                  <Typography variant="small" className="text-xs text-gray-500 mt-1">
                    ID: #{ticket.identifiant.split("-")[0]}
                  </Typography>
                </div>
              </Link>
            ))
          ) : (
            <div className="p-4 rounded-lg bg-gray-50 border border-gray-200 text-gray-600 text-xs">
              {t("no_recent_tickets")}
            </div>
          )}
        </div>
      </div>

      {/* Sidebar Navigation */}
      <nav className="absolute w-64 bottom-4 left-1/2 transform -translate-x-1/2 space-y-2 flex flex-col">
        {sidebarItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link key={item.label} href={item.href} passHref>
              <Button
                variant="text"
                className="w-full flex items-center gap-2 py-2 px-4 rounded-lg hover:bg-gray-100 text-gray-700 transition duration-200"
                color="gray"
                component="a"
              >
                <IconComponent className="h-5 w-5 text-gray-600" />
                <span className="text-sm font-medium">{t(item.label)}</span>
              </Button>
            </Link>
          );
        })}
        <Link href="/client/support/add-ticket" passHref>
          <Button
            className="w-64 py-2 px-3 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition duration-200"
            color="blue"
            component="a"
          >
            {t("openticket")}
          </Button>
        </Link>
      </nav>
    </div>
  );
};

export default Sidebar;