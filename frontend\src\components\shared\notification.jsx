import React from 'react';
import PropTypes from 'prop-types';
import Image from 'next/image';
import { Typography } from '@material-tailwind/react';

const Notification = ({ success, imageSrc, imageAlt, text, textVariant, textClass }) => {
    if (!success) return null;

    return (
        <div className="text-center mb-6">
            {imageSrc && (
                <Image
                    className="h-auto mx-auto"
                    src={imageSrc}
                    width={300}
                    height={300}
                    alt={imageAlt}
                />
            )}
            <Typography variant={textVariant || 'body1'} className={textClass || 'text-green-600'}>
                {text}
            </Typography>
        </div>
    );
};

Notification.propTypes = {
    success: PropTypes.bool,
    imageSrc: PropTypes.string,
    imageAlt: PropTypes.string,
    text: PropTypes.string.isRequired,
    textVariant: PropTypes.string,
    textClass: PropTypes.string,
};

export default Notification;
