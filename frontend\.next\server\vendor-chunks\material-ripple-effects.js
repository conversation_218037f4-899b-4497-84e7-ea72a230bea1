"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/material-ripple-effects";
exports.ids = ["vendor-chunks/material-ripple-effects"];
exports.modules = {

/***/ "(ssr)/./node_modules/material-ripple-effects/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/material-ripple-effects/index.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("\nmodule.exports = class Ripple {\n    constructor(){\n        this.x = 0;\n        this.y = 0;\n        this.z = 0;\n    }\n    findFurthestPoint(clickPointX, elementWidth, offsetX, clickPointY, elementHeight, offsetY) {\n        this.x = clickPointX - offsetX > elementWidth / 2 ? 0 : elementWidth;\n        this.y = clickPointY - offsetY > elementHeight / 2 ? 0 : elementHeight;\n        this.z = Math.hypot(this.x - (clickPointX - offsetX), this.y - (clickPointY - offsetY));\n        return this.z;\n    }\n    appyStyles(element, color, rect, radius, event) {\n        element.classList.add(\"ripple\");\n        element.style.backgroundColor = color === \"dark\" ? \"rgba(0,0,0, 0.2)\" : \"rgba(255,255,255, 0.3)\";\n        element.style.borderRadius = \"50%\";\n        element.style.pointerEvents = \"none\";\n        element.style.position = \"absolute\";\n        element.style.left = event.clientX - rect.left - radius + \"px\";\n        element.style.top = event.clientY - rect.top - radius + \"px\";\n        element.style.width = element.style.height = radius * 2 + \"px\";\n    }\n    applyAnimation(element) {\n        element.animate([\n            {\n                transform: \"scale(0)\",\n                opacity: 1\n            },\n            {\n                transform: \"scale(1.5)\",\n                opacity: 0\n            }\n        ], {\n            duration: 500,\n            easing: \"linear\"\n        });\n    }\n    create(event, color) {\n        const element = event.currentTarget;\n        element.style.position = \"relative\";\n        element.style.overflow = \"hidden\";\n        const rect = element.getBoundingClientRect();\n        const radius = this.findFurthestPoint(event.clientX, element.offsetWidth, rect.left, event.clientY, element.offsetHeight, rect.top);\n        const circle = document.createElement(\"span\");\n        this.appyStyles(circle, color, rect, radius, event);\n        this.applyAnimation(circle);\n        element.appendChild(circle);\n        setTimeout(()=>circle.remove(), 500);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/material-ripple-effects/index.js\n");

/***/ })

};
;