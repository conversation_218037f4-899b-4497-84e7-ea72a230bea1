"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/intl-messageformat";
exports.ids = ["vendor-chunks/intl-messageformat"];
exports.modules = {

/***/ "(ssr)/./node_modules/intl-messageformat/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.ErrorCode),\n/* harmony export */   FormatError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.FormatError),\n/* harmony export */   IntlMessageFormat: () => (/* reexport safe */ _src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat),\n/* harmony export */   InvalidValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.MissingValueError),\n/* harmony export */   PART_TYPE: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.PART_TYPE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatToParts: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _src_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/core */ \"(ssr)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var _src_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/error */ \"(ssr)/./node_modules/intl-messageformat/lib/src/error.js\");\n/* harmony import */ var _src_formatters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/formatters */ \"(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7QUFJQSxHQUMrQztBQUNwQjtBQUNDO0FBQ0s7QUFDSjtBQUM3QixpRUFBZUEsd0RBQWlCQSxFQUFDLENBQ2pDLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9pbmRleC5qcz9lMTJiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG5Db3B5cmlnaHQgKGMpIDIwMTQsIFlhaG9vISBJbmMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG5Db3B5cmlnaHRzIGxpY2Vuc2VkIHVuZGVyIHRoZSBOZXcgQlNEIExpY2Vuc2UuXG5TZWUgdGhlIGFjY29tcGFueWluZyBMSUNFTlNFIGZpbGUgZm9yIHRlcm1zLlxuKi9cbmltcG9ydCB7IEludGxNZXNzYWdlRm9ybWF0IH0gZnJvbSAnLi9zcmMvY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL3NyYy9jb3JlJztcbmV4cG9ydCAqIGZyb20gJy4vc3JjL2Vycm9yJztcbmV4cG9ydCAqIGZyb20gJy4vc3JjL2Zvcm1hdHRlcnMnO1xuZXhwb3J0IHsgSW50bE1lc3NhZ2VGb3JtYXQgfTtcbmV4cG9ydCBkZWZhdWx0IEludGxNZXNzYWdlRm9ybWF0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbIkludGxNZXNzYWdlRm9ybWF0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1 || {}), c2 || {}), Object.keys(c1).reduce(function(all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), c2[k] || {});\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function(all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function() {\n            return {\n                get: function(key) {\n                    return store[key];\n                },\n                set: function(key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) {\n        cache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n    }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        })\n    };\n}\nvar IntlMessageFormat = /** @class */ function() {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) {\n            locales = IntlMessageFormat.defaultLocale;\n        }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n        this.format = function(values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function(all, part) {\n                if (!all.length || part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal || typeof all[all.length - 1] !== \"string\") {\n                    all.push(part.value);\n                } else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || \"\";\n            }\n            return result;\n        };\n        this.formatToParts = function(values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function() {\n            var _a;\n            return {\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) || Intl.NumberFormat.supportedLocalesOf(_this.locales)[0]\n            };\n        };\n        this.getAst = function() {\n            return _this.ast;\n        };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === \"string\") {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError(\"IntlMessageFormat.__parse must be set to process `message` of type `string`\");\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\n                \"formatters\"\n            ]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), {\n                locale: this.resolvedLocale\n            }));\n        } else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError(\"A message must be provided as a String or AST.\");\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters = opts && opts.formatters || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function() {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale = new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function(locales) {\n        if (typeof Intl.Locale === \"undefined\") {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === \"string\" ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0\n            },\n            currency: {\n                style: \"currency\"\n            },\n            percent: {\n                style: \"percent\"\n            }\n        },\n        date: {\n            short: {\n                month: \"numeric\",\n                day: \"numeric\",\n                year: \"2-digit\"\n            },\n            medium: {\n                month: \"short\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            long: {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            full: {\n                weekday: \"long\",\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            }\n        },\n        time: {\n            short: {\n                hour: \"numeric\",\n                minute: \"numeric\"\n            },\n            medium: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\"\n            },\n            long: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            },\n            full: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            }\n        }\n    };\n    return IntlMessageFormat;\n}();\n //# sourceMappingURL=core.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function(ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function() {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error);\n\nvar InvalidValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, 'Invalid values for \"'.concat(variableId, '\": \"').concat(value, '\". Options are \"').concat(Object.keys(options).join('\", \"'), '\"'), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError);\n\nvar InvalidValueTypeError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, 'Value for \"'.concat(value, '\" must be of type ').concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError);\n\nvar MissingValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, 'The intl string context variable \"'.concat(variableId, '\" was not provided to the string \"').concat(originalMessage, '\"'), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError);\n //# sourceMappingURL=error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function(PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function(all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart || lastPart.type !== PART_TYPE.literal || part.type !== PART_TYPE.literal) {\n            all.push(part);\n        } else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === \"function\";\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, // For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value\n            }\n        ];\n    }\n    var result = [];\n    for(var _i = 0, els_1 = els; _i < els_1.length; _i++){\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === \"number\") {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue)\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === \"string\" || typeof value === \"number\") {\n                value = typeof value === \"string\" || typeof value === \"number\" ? String(value) : \"\";\n            }\n            result.push({\n                type: typeof value === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                value: value\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.date[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.time[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.number[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            if (style && style.scale) {\n                value = value * (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getNumberFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, \"function\", originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function(p) {\n                return p.value;\n            }));\n            if (!Array.isArray(chunks)) {\n                chunks = [\n                    chunks\n                ];\n            }\n            result.push.apply(result, chunks.map(function(c) {\n                return {\n                    type: typeof c === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError('Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \"@formatjs/intl-pluralrules\"\\n', _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters.getPluralRules(locales, {\n                    type: el.pluralType\n                }).select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n} //# sourceMappingURL=formatters.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.ErrorCode),\n/* harmony export */   FormatError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.FormatError),\n/* harmony export */   IntlMessageFormat: () => (/* reexport safe */ _src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat),\n/* harmony export */   InvalidValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* reexport safe */ _src_error__WEBPACK_IMPORTED_MODULE_0__.MissingValueError),\n/* harmony export */   PART_TYPE: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.PART_TYPE),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatToParts: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* reexport safe */ _src_formatters__WEBPACK_IMPORTED_MODULE_1__.isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _src_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/core */ \"(rsc)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var _src_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./src/error */ \"(rsc)/./node_modules/intl-messageformat/lib/src/error.js\");\n/* harmony import */ var _src_formatters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/formatters */ \"(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_src_core__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7QUFJQSxHQUMrQztBQUNwQjtBQUNDO0FBQ0s7QUFDSjtBQUM3QixpRUFBZUEsd0RBQWlCQSxFQUFDLENBQ2pDLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvaW50bC1tZXNzYWdlZm9ybWF0L2xpYi9pbmRleC5qcz9lMTJiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG5Db3B5cmlnaHQgKGMpIDIwMTQsIFlhaG9vISBJbmMuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG5Db3B5cmlnaHRzIGxpY2Vuc2VkIHVuZGVyIHRoZSBOZXcgQlNEIExpY2Vuc2UuXG5TZWUgdGhlIGFjY29tcGFueWluZyBMSUNFTlNFIGZpbGUgZm9yIHRlcm1zLlxuKi9cbmltcG9ydCB7IEludGxNZXNzYWdlRm9ybWF0IH0gZnJvbSAnLi9zcmMvY29yZSc7XG5leHBvcnQgKiBmcm9tICcuL3NyYy9jb3JlJztcbmV4cG9ydCAqIGZyb20gJy4vc3JjL2Vycm9yJztcbmV4cG9ydCAqIGZyb20gJy4vc3JjL2Zvcm1hdHRlcnMnO1xuZXhwb3J0IHsgSW50bE1lc3NhZ2VGb3JtYXQgfTtcbmV4cG9ydCBkZWZhdWx0IEludGxNZXNzYWdlRm9ybWF0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbIkludGxNZXNzYWdlRm9ybWF0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/ \n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1 || {}), c2 || {}), Object.keys(c1).reduce(function(all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), c2[k] || {});\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function(all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function() {\n            return {\n                get: function(key) {\n                    return store[key];\n                },\n                set: function(key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) {\n        cache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n    }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function() {\n            var _a;\n            var args = [];\n            for(var _i = 0; _i < arguments.length; _i++){\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([\n                void 0\n            ], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic\n        })\n    };\n}\nvar IntlMessageFormat = /** @class */ function() {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) {\n            locales = IntlMessageFormat.defaultLocale;\n        }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {}\n        };\n        this.format = function(values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function(all, part) {\n                if (!all.length || part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal || typeof all[all.length - 1] !== \"string\") {\n                    all.push(part.value);\n                } else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || \"\";\n            }\n            return result;\n        };\n        this.formatToParts = function(values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function() {\n            var _a;\n            return {\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) || Intl.NumberFormat.supportedLocalesOf(_this.locales)[0]\n            };\n        };\n        this.getAst = function() {\n            return _this.ast;\n        };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === \"string\") {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError(\"IntlMessageFormat.__parse must be set to process `message` of type `string`\");\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\n                \"formatters\"\n            ]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), {\n                locale: this.resolvedLocale\n            }));\n        } else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError(\"A message must be provided as a String or AST.\");\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters = opts && opts.formatters || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function() {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale = new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function(locales) {\n        if (typeof Intl.Locale === \"undefined\") {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === \"string\" ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0\n            },\n            currency: {\n                style: \"currency\"\n            },\n            percent: {\n                style: \"percent\"\n            }\n        },\n        date: {\n            short: {\n                month: \"numeric\",\n                day: \"numeric\",\n                year: \"2-digit\"\n            },\n            medium: {\n                month: \"short\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            long: {\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            },\n            full: {\n                weekday: \"long\",\n                month: \"long\",\n                day: \"numeric\",\n                year: \"numeric\"\n            }\n        },\n        time: {\n            short: {\n                hour: \"numeric\",\n                minute: \"numeric\"\n            },\n            medium: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\"\n            },\n            long: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            },\n            full: {\n                hour: \"numeric\",\n                minute: \"numeric\",\n                second: \"numeric\",\n                timeZoneName: \"short\"\n            }\n        }\n    };\n    return IntlMessageFormat;\n}();\n //# sourceMappingURL=core.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function(ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function() {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error);\n\nvar InvalidValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, 'Invalid values for \"'.concat(variableId, '\": \"').concat(value, '\". Options are \"').concat(Object.keys(options).join('\", \"'), '\"'), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError);\n\nvar InvalidValueTypeError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, 'Value for \"'.concat(value, '\" must be of type ').concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError);\n\nvar MissingValueError = /** @class */ function(_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, 'The intl string context variable \"'.concat(variableId, '\" was not provided to the string \"').concat(originalMessage, '\"'), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError);\n //# sourceMappingURL=error.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function(PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function(all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart || lastPart.type !== PART_TYPE.literal || part.type !== PART_TYPE.literal) {\n            all.push(part);\n        } else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === \"function\";\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, // For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value\n            }\n        ];\n    }\n    var result = [];\n    for(var _i = 0, els_1 = els; _i < els_1.length; _i++){\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === \"number\") {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue)\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === \"string\" || typeof value === \"number\") {\n                value = typeof value === \"string\" || typeof value === \"number\" ? String(value) : \"\";\n            }\n            result.push({\n                type: typeof value === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                value: value\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.date[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.time[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style) ? el.style.parsedOptions : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getDateTimeFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === \"string\" ? formats.number[el.style] : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style) ? el.style.parsedOptions : undefined;\n            if (style && style.scale) {\n                value = value * (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters.getNumberFormat(locales, style).format(value)\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, \"function\", originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function(p) {\n                return p.value;\n            }));\n            if (!Array.isArray(chunks)) {\n                chunks = [\n                    chunks\n                ];\n            }\n            result.push.apply(result, chunks.map(function(c) {\n                return {\n                    type: typeof c === \"string\" ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError('Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \"@formatjs/intl-pluralrules\"\\n', _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters.getPluralRules(locales, {\n                    type: el.pluralType\n                }).select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n} //# sourceMappingURL=formatters.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ })

};
;