"use client";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { CheckIcon } from "lucide-react";
import {
  Typo<PERSON>,
  <PERSON>,
  CardBody,
  <PERSON><PERSON>,
} from "@material-tailwind/react";
import "react-loading-skeleton/dist/skeleton.css";
import PricingPlanGrid from "@/components/hosting/pricingPlanGrid";
import { getFormattedPlans, getMaxDiscount } from "@/app/helpers/helpers";
import packageService from "@/app/services/packageService";
import <PERSON><PERSON> from "lottie-react";
import DedicatedHostingAnimation from "src/assets/dedicated-hosting.json";
import { CheckCircleIcon, RocketIcon, ServerIcon, ArrowRight, HardDriveIcon } from "lucide-react";
import DynamicMetadataClient from "@/components/DynamicMetadataClient";

export default function DedicatedHostingPage() {
  const t = useTranslations("hosting");
  const [data, setData] = useState({});



  // Données des serveurs dédiés basées sur l'image (prix convertis en MAD)
  const dedicatedServers = [
    {
      name: "DS 10",
      processor: "AMD RYZEN 12",
      originalPrice: "1,439 MAD",
      price: "1,290",
      currency: "",
      period: " MAD/mois",
      cpu: "AMD Ryzen 9 7900",
      cpuDetails: "12 × 3.70 GHz",
      ram: "32 GB REG ECC",
      ramDetails: "Up to 128 GB RAM",
      storage: "1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB",
      badge: "No Location Fees in us"
    },
    {
      name: "DS 20",
      processor: "AMD RYZEN 12",
      originalPrice: "1,618 MAD",
      price: "1,470",
      currency: "",
      period: " MAD/mois",
      cpu: "AMD Ryzen 9 7900",
      cpuDetails: "12 × 3.70 GHz",
      ram: "64 GB REG ECC",
      ramDetails: "Up to 128 GB RAM",
      storage: "1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB",
      badge: "No Location Fees in us",
      popular: true
    },
    {
      name: "DS 30",
      processor: "AMD RYZEN 12",
      originalPrice: "1,737 MAD",
      price: "1,590",
      currency: "",
      period: " MAD/mois",
      cpu: "AMD Ryzen 9 7900",
      cpuDetails: "12 × 3.70 GHz",
      ram: "64 GB REG ECC",
      ramDetails: "Up to 128 GB RAM",
      storage: "2 × 1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB",
      badge: "No Location Fees in us"
    },
    {
      name: "DS 40",
      processor: "AMD TURIN 32",
      originalPrice: "3,558 MAD",
      price: "3,200",
      currency: "",
      period: " MAD/mois",
      cpu: "AMD EPYC 9355P",
      cpuDetails: "32 × 3.55 GHz (4.20 max)",
      ram: "128 GB REG ECC",
      ramDetails: "Up to 768 GB RAM",
      storage: "2 × 1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB",
      badge: "No Location Fees in us"
    },
    {
      name: "DS 50",
      processor: "AMD TURIN 64",
      originalPrice: "7,925 MAD",
      price: "7,120",
      currency: "",
      period: " MAD/mois",
      cpu: "AMD Epyc 9555P",
      cpuDetails: "64 × 3.2 GHz (4.20 max)",
      ram: "192 GB REG ECC",
      ramDetails: "Up to 1152 GB RAM",
      storage: "2 × 1 TB NVMe",
      storageDetails: "More storage available",
      network: "1 Gbit/s Port",
      traffic: "32 TB Traffic*",
      trafficDetails: "Up to 324 TB",
      badge: "No Location Fees in us"
    }
  ];



  return (
    <div className="min-h-screen bg-gray-50 pt-4 pb-20">
      <DynamicMetadataClient
  title={`ZtechEngineering | ${t('dedicated.title')}`}
  desc={`${t('dedicated.desc')}`}
/>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <section className="py-16">
          <div className="max-w-4xl">
            <Typography
              variant="h1"
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-blue-600 leading-tight mb-6"
            >
              Dedicated Server<br />
              Hosting
            </Typography>
            <Typography
              className="text-base md:text-lg text-gray-600 leading-relaxed max-w-lg"
            >
              Enterprise-grade dedicated servers with full hardware control and maximum performance.
            </Typography>
          </div>
        </section>

        {/* Billing Period Toggle */}
        <section className="mb-12">
          <div className="flex justify-center">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                className="px-6 py-2 rounded-md text-sm font-medium bg-white text-gray-900 shadow-sm"
              >
                Monthly
              </button>
              <button
                className="px-6 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900"
              >
                Yearly <span className="text-green-600 text-xs ml-1">Save up to 15%</span>
              </button>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            {dedicatedServers.map((server) => (
              <div
                key={server.name}
                className={`relative bg-white border border-gray-200 hover:shadow-lg transition-all duration-300 rounded-lg ${
                  server.popular ? 'border-blue-500 shadow-lg' : ''
                }`}
              >
                {server.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="p-6">
                  {/* Server Name */}
                  <div className="text-center mb-4">
                    <Typography variant="h6" className="text-lg font-semibold text-gray-800 mb-2">
                      {server.name}
                    </Typography>
                    <Typography className="text-sm text-gray-600 mb-4">
                      Dedicated Server Hosting
                    </Typography>
                  </div>

                  {/* Discount Badge */}
                  <div className="text-center mb-4">
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                      SAVE 15%
                    </span>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-6">
                    <div className="text-gray-500 text-sm line-through mb-1">
                      {server.originalPrice}/mo
                    </div>
                    <div className="text-4xl font-bold text-gray-900 mb-1">
                      {server.price}
                    </div>
                    <div className="text-gray-600 text-sm">MAD/mo</div>
                  </div>

                  {/* Select Plan Button */}
                  <Button
                    className={`w-full mb-6 ${
                      server.popular
                        ? 'bg-blue-500 hover:bg-blue-600 text-white'
                        : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      window.location.href = `/fr/hosting/dedicated/configure?plan=${server.name.toLowerCase().replace(' ', '-')}`;
                    }}
                  >
                    Select Plan →
                  </Button>

                  {/* Specifications */}
                  <div className="space-y-3 text-sm text-gray-700">
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.cpu}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.cpuDetails}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.ram}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.ramDetails}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.storage}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.storageDetails}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.network}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.traffic}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>{server.trafficDetails}</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>Full Root Access</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>24/7 Support</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>99.9% Uptime SLA</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>DDoS Protection</span>
                    </div>
                    <div className="flex items-center">
                      <CheckIcon className="w-4 h-4 mr-3 text-green-500" />
                      <span>Free Setup</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50">
          {/* Section Title */}
          <div className="text-center mb-12">
            <Typography
              variant="h2"
              color="blue-gray"
              className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4"
            >
              {t("features_title")}
            </Typography>
            <Typography
              variant="lead"
              color="gray"
              className="text-lg md:text-xl font-light text-gray-600 max-w-2xl mx-auto"
            >
              {t("features_description")}
            </Typography>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 px-4 sm:px-6 lg:px-8">
            {[
              {
                title: t('dedicated_features.0.title'),
                description: t('dedicated_features.0.description'),
                icon: <CheckIcon className="h-8 w-8 text-green-700" />,
              },
              {
                title: t('dedicated_features.1.title'),
                description: t('dedicated_features.1.description'),
                icon: <CheckIcon className="h-8 w-8 text-green-700" />,
              },
              {
                title: t('dedicated_features.2.title'),
                description: t('dedicated_features.2.description'),
                icon: <CheckIcon className="h-8 w-8 text-green-700" />,
              },
            ].map((feature, index) => (
              <Card
                key={index}
                className="bg-white hover:shadow-2xl transition-shadow duration-300 rounded-2xl border border-gray-100 p-6 flex flex-col items-center text-center"
              >
                <div className="mb-6">
                  {feature.icon}
                </div>
                <CardBody className="p-0">
                  <Typography
                    variant="h5"
                    color="blue-gray"
                    className="text-xl md:text-2xl font-semibold mb-3"
                  >
                    {feature.title}
                  </Typography>
                  <Typography
                    color="gray"
                    className="text-sm md:text-base font-normal text-gray-600"
                  >
                    {feature.description}
                  </Typography>
                </CardBody>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}