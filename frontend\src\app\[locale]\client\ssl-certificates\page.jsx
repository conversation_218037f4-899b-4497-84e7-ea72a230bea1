"use client";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardBody,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import orderService from "../../../services/orderService";
import {
  ShieldCheck,
  Calendar,
  AlertCircle,
  Download,
  ChevronDown,
  Shield,
  Clock,
  CheckCircle2,
  XCircle,
} from "lucide-react";
import { getLocalizedContent, truncateString } from "@/app/helpers/helpers";

export default function SslOrdersPage() {
  const t = useTranslations("client");
  const router = useRouter();
  const [subOrders, setSubOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedOrders, setExpandedOrders] = useState(new Set());
  const localeActive = useLocale();
  const categoryName = "SSL";

  const handleAccordionClick = (orderId) => {
    const newExpanded = new Set(expandedOrders);
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId);
    } else {
      newExpanded.add(orderId);
    }
    setExpandedOrders(newExpanded);
  };

  const handleActivate = (subOrderId, certificateIndex) => {
    router.push(`/${localeActive}/client/ssl-certificates/activate/${subOrderId}?index=${certificateIndex}`);
  };

  // For SSL certificate status
  const getStatusColor = (status) => {
    switch (status?.toUpperCase()) {
      case 'PROCESSING':
        return "bg-yellow-100 text-yellow-800";
      case 'ISSUED':
        return "bg-green-100 text-green-800";
      case 'INSTALLED':
        return "bg-emerald-100 text-emerald-800";
      case 'EXPIRED':
        return "bg-red-100 text-red-800";
      case 'PENDING':
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toUpperCase()) {
      case 'PROCESSING':
        return <Clock className="h-4 w-4" />;
      case 'ISSUED':
        return <CheckCircle2 className="h-4 w-4" />;
      case 'INSTALLED':
        return <ShieldCheck className="h-4 w-4" />;
      case 'EXPIRED':
        return <XCircle className="h-4 w-4" />;
      case 'PENDING':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // For suborder status
  const getSuborderStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return "bg-emerald-100 text-green-600 font-bold";
      case 'expired':
        return "bg-red-100 text-red-600 font-bold";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSuborderStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'expired':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  // Should we display the suborder status?
  const shouldDisplaySuborderStatus = (status) => {
    return status === 'active' || status === 'expired';
  };

  useEffect(() => {
    const getSubOrders = async () => {
      try {
        const res = await orderService.getSubOrdersByCategory(categoryName);
        console.log("Fetched suborders:", res?.data?.subOrders);
        setSubOrders(res?.data?.subOrders || []); // Ensure we always set an array
      } catch (error) {
        console.error("Error getting suborders", error);
        // Handle 400 error specifically - likely means no orders found
        if (error.response?.status === 400) {
          setSubOrders([]); // Set empty array rather than leaving it undefined
        }
      } finally {
        setLoading(false);
      }
    };
    getSubOrders();
  }, [categoryName]); // Add categoryName as dependency

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (!subOrders?.length) {
    return (
      <div className="min-h-[80vh] flex items-center justify-center p-4">
        <div className="text-center p-8 md:py-12 bg-white rounded-xl shadow-sm max-w-lg w-full">
          <ShieldCheck className="w-12 h-12 md:w-16 md:h-16 text-gray-400 mx-auto mb-4" />
          <Typography variant="h6" className="text-base md:text-lg text-gray-600 mb-2">
            {t("no_orders_found")}
          </Typography>
          <Typography className="text-sm md:text-base text-gray-500 mb-4">
            {t("ssl.browse_certificates_message")}
          </Typography>
          <Button
            color="blue"
            variant="outlined"
            className="mt-2"
            onClick={() => router.push("/ssl")}
          >
            {t("ssl.browse_certificates")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 md:p-8 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h1" className="text-2xl font-medium text-gray-800">
          {t("ssl.my_certificates")}
        </Typography>

        <Button
          variant="outlined"
          color="blue"
          className="flex items-center gap-2 px-4 py-2 bg-blue-50 border-blue-200 hover:bg-blue-100 transition-colors"
          onClick={() => router.push(`/${localeActive}/ssl`)}
        >
          <Shield className="h-4 w-4 text-blue-500" />
          {t("ssl.explore_plans") || "Explore SSL Plans"}
        </Button>
      </div>

      <div className="space-y-5">
        {subOrders.map((order) => (
          <Card key={order._id} className="overflow-hidden border border-gray-100 shadow-sm hover:shadow-md transition-shadow">
            <CardBody className="p-0">
              {/* Order Header - Always visible */}
              <div className="p-5 bg-white">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-1">
                      <Typography className="font-semibold text-lg text-gray-800">
                        {getLocalizedContent(order.package, "name", localeActive)}
                      </Typography>

                      {/* Display suborder status badge only for active or expired */}
                      {shouldDisplaySuborderStatus(order.status) && (
                        <span className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-sm font-medium capitalize ${getSuborderStatusColor(order.status)}`}>
                          {getSuborderStatusIcon(order.status)}
                          {order.status.toLowerCase()}
                        </span>
                      )}
                    </div>
                    <div className="flex flex-wrap gap-x-6 gap-y-2 text-sm text-gray-600 mt-2">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-blue-500" />
                        <span className="text-gray-500">{t("ssl.order_date")}:</span>
                        <span className="font-medium">{new Date(order.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <ShieldCheck className="h-4 w-4 text-blue-500" />
                        <span className="text-gray-500">{t("ssl.quantity")}:</span>
                        <span className="font-medium">{order.quantity}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-blue-500" />
                        <span className="text-gray-500">{t("ssl.total_price")}:</span>
                        <span className="font-medium">MAD {(order.price).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="text"
                    color="blue"
                    className="flex items-center gap-2 mt-3 md:mt-0 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors"
                    onClick={() => handleAccordionClick(order._id)}
                  >
                    {expandedOrders.has(order._id) ? t("ssl.hide_certificates") : t("ssl.show_certificates")}
                    <ChevronDown
                      className={`h-4 w-4 transition-transform ${
                        expandedOrders.has(order._id) ? "rotate-180" : ""
                      }`}
                    />
                  </Button>
                </div>
              </div>

              {/* Certificates Accordion Content */}
              {expandedOrders.has(order._id) && (
                <div className="bg-white border-t border-gray-100">
                  <div className="p-5">
                    <Typography className="font-medium text-gray-700 mb-4 flex items-center gap-2">
                      <Shield className="h-5 w-5 text-blue-500" />
                      {t("ssl.certificates")} ({order.quantity})
                    </Typography>

                    <div className="space-y-4">
                      {[...Array(order.quantity)].map((_, i) => {
                        // Get certificate data
                        const cert = order.ssl?.[i.toString()] || {};
                        const isPending = !cert.status || cert.status === 'PENDING';

                        return (
                          <div key={i} className="bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
                            {/* Certificate Header */}
                            <div className="p-4 md:p-5 flex flex-col md:flex-row justify-between gap-4 border-b border-gray-200 bg-white">
                              <div className="flex items-center gap-3">
                                <div className="bg-blue-50 p-2 rounded-full">
                                  <ShieldCheck className="h-5 w-5 text-blue-500" />
                                </div>
                                <div>
                                  <Typography className="font-semibold text-gray-800">
                                    {t("ssl.certificate")} #{i + 1}
                                  </Typography>
                                  {/* {cert.domain && (
                                    <Typography className="text-sm text-gray-600">
                                      {cert.domain}
                                    </Typography>
                                  )} */}
                                </div>
                              </div>

                              <div className="flex items-center gap-3">
                                {cert.status && (
                                  <span className={`inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium capitalize ${getStatusColor(cert.status)}`}>
                                    {getStatusIcon(cert.status)}
                                    {cert.status?.toLowerCase()}
                                  </span>
                                )}

                                {isPending && (
                                  <Button
                                    variant="filled"
                                    color="blue"
                                    size="sm"
                                    className="flex items-center gap-2 shadow-sm"
                                    onClick={() => handleActivate(order._id, i)}
                                  >
                                    <Shield className="h-4 w-4" />
                                    {t("ssl.activate")}
                                  </Button>
                                )}
                              </div>
                            </div>

                            {/* Certificate Details */}
                            {!isPending && (
                              <div className="p-4 md:p-5 bg-gray-50">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                                    <Typography className="text-xs text-gray-500 mb-1">
                                      {t("ssl.domain")}
                                    </Typography>
                                    <Typography className="font-medium text-gray-800">
                                      {cert.domain || '-'}
                                    </Typography>
                                  </div>

                                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                                    <Typography className="text-xs text-gray-500 mb-1">
                                      {t("ssl.validation_email")}
                                    </Typography>
                                    <Typography className="font-medium text-gray-800 truncate" title={cert.validationEmail}>
                                      {cert.validationEmail || '-'}
                                    </Typography>
                                  </div>

                                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                                    <Typography className="text-xs text-gray-500 mb-1">
                                      {t("ssl.issued_at")}
                                    </Typography>
                                    <Typography className="font-medium text-gray-800 flex items-center gap-1">
                                      <Calendar className="h-3.5 w-3.5 text-blue-500" />
                                      {cert.issuedAt ? new Date(cert.issuedAt).toLocaleDateString() : '-'}
                                    </Typography>
                                  </div>

                                  <div className="p-3 bg-white rounded-lg border border-gray-200">
                                    <Typography className="text-xs text-gray-500 mb-1">
                                      {t("ssl.expire_at")}
                                    </Typography>
                                    <Typography className="font-medium text-gray-800 flex items-center gap-1">
                                      <Calendar className="h-3.5 w-3.5 text-blue-500" />
                                      {cert.expiresAt ? new Date(cert.expiresAt).toLocaleDateString() : '-'}
                                    </Typography>
                                  </div>
                                </div>

                                {cert.csr && (
                                  <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200">
                                    <Typography className="text-xs text-gray-500 mb-1 flex items-center justify-between">
                                      <span>{t("ssl.your_csr")}</span>
                                      <Tooltip content={t("ssl.copy_csr")}>
                                        <Button
                                          variant="text"
                                          size="sm"
                                          color="blue"
                                          className="p-1 h-6 w-6"
                                          onClick={() => navigator.clipboard.writeText(cert.csr)}
                                        >
                                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                            <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"></path>
                                          </svg>
                                        </Button>
                                      </Tooltip>
                                    </Typography>
                                    <div className="mt-1 font-mono text-xs bg-gray-50 p-2.5 rounded border border-gray-200 overflow-x-auto">
                                      {truncateString(cert.csr, 70)}
                                    </div>
                                  </div>
                                )}

                                {/* {cert.status === 'ISSUED' && (
                                  <div className="mt-4 flex justify-end">
                                    <Button
                                      variant="outlined"
                                      color="blue"
                                      size="sm"
                                      className="flex items-center gap-2"
                                    >
                                      <Download className="h-4 w-4" />
                                      {t("ssl.download_certificate")}
                                    </Button>
                                  </div>
                                )} */}
                              </div>
                            )}

                            {isPending && (
                              <div className="p-4 md:p-5 text-center bg-gray-50">
                                <div className="bg-white p-4 rounded-lg inline-flex items-center justify-center mb-3 border border-gray-200">
                                  <Clock className="h-6 w-6 text-blue-500" />
                                </div>
                                <Typography className="text-sm text-gray-600">
                                  {t("ssl.activate_message")}
                                </Typography>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        ))}
      </div>
    </div>
  );
}
