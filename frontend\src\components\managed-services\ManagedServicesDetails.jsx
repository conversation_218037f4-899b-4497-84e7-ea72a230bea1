import React from 'react'
import Section from '../home/<USER>';
import { motion } from "framer-motion";
import { Typography } from '@material-tailwind/react';
import Image from 'next/image';


const BENEFITS = Object.freeze([
    {
      icon: "/images/services/sittings-search.svg",
      title: "benefits.0.title",
      description: "benefits.0.description",
    },
    {
      icon: "/images/services/shield-clock.svg",
      title: "benefits.1.title",
      description: "benefits.1.description",
    },
    {
      icon: "/images/services/handshake.svg",
      title: "benefits.2.title",
      description: "benefits.2.description",
    },
  ]);

const ManagedServicesDetails = ({t, animations}) => {
  return (
    <Section bgColor="bg-gray-50">
        <div className="text-center py-12">
          {/* Animated Header */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={animations.fadeInUp}
            className="mb-8"
          >
            <Typography
              variant="h2"
              className="text-3xl font-inter md:text-4xl font-bold text-gray-900 leading-tight"
            >
              {t("cloud_management_services")}
            </Typography>
            <Typography
              variant="lead"
              className="mt-4 text-lg font-inter text-gray-600 max-w-2xl mx-auto leading-relaxed"
            >
              {t("cloud_management_benefits")}
            </Typography>
          </motion.div>

          {/* Benefits Grid */}
          <div className="mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {BENEFITS.map((item, index) => (
              <motion.div
                key={item.title}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={animations.fadeInUp}
                transition={{ delay: index * 0.1 }}
                className="bg-white p-6 rounded-2xl shadow-sm hover:shadow-md transition-all duration-300 ease-in-out transform hover:-translate-y-1"
              >
                {/* Icon */}
                <div className="flex justify-center mb-6">
                  <Image
                    src={item.icon}
                    alt={t(item.title)}
                    width={48}
                    height={48}
                    sizes="48px"
                    quality={85}
                    className="rounded-md bg-indigo-100 p-2"
                    loading="lazy"
                  />
                </div>

                {/* Title */}
                <Typography
                  variant="h5"
                  className="text-xl font-inter font-semibold text-gray-900 mb-2"
                >
                  {t(item.title)}
                </Typography>

                {/* Description */}
                <Typography
                  variant="paragraph"
                  className="text-base font-inter text-gray-600 leading-relaxed"
                >
                  {t(item.description)}
                </Typography>
              </motion.div>
            ))}
          </div>
        </div>
      </Section>
  )
}

export default ManagedServicesDetails