/**
 * Contabo VPS Provider Implementation
 * Implements VPSProviderInterface for Contabo API
 * Follows Single Responsibility Principle (SRP) from SOLID
 */

const VPSProviderInterface = require("./VPSProviderInterface");
const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
require("dotenv").config();

class ContaboProvider extends VPSProviderInterface {
  constructor() {
    super();
    this.baseURL = process.env.CONTABO_API_URL || "https://api.contabo.com/v1";
    this.clientId = process.env.CONTABO_CLIENT_ID;
    this.clientSecret = process.env.CONTABO_CLIENT_SECRET;
    this.username = process.env.CONTABO_USERNAME;
    this.password = process.env.CONTABO_PASSWORD;
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Authenticate with Contabo API using OAuth2
   * @returns {Promise<string>} Access token
   */
  async authenticate() {
    try {
      if (
        this.accessToken &&
        this.tokenExpiry &&
        Date.now() < this.tokenExpiry
      ) {
        return this.accessToken;
      }

      // Use URLSearchParams for proper form-encoded data
      const params = new URLSearchParams();
      params.append("client_id", this.clientId);
      params.append("client_secret", this.clientSecret);
      params.append("username", this.username);
      params.append("password", this.password);
      params.append("grant_type", "password");

      const response = await axios.post(
        `https://auth.contabo.com/auth/realms/contabo/protocol/openid-connect/token`,
        params,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "x-request-id": uuidv4(),
          },
        }
      );

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + response.data.expires_in * 1000;

      return this.accessToken;
    } catch (error) {
      console.error(
        "Contabo authentication failed:",
        error.response?.data || error.message
      );
      throw new Error("Failed to authenticate with Contabo API");
    }
  }

  /**
   * Make authenticated API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise<Object>} API response
   */
  async makeRequest(method, endpoint, data = null) {
    try {
      const token = await this.authenticate();

      // Generate valid UUID4 for x-request-id header (required by Contabo)
      const requestId = uuidv4();

      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          "x-request-id": requestId,
        },
      };

      if (data) {
        config.data = data;
      }

      console.log(`Contabo API Request [${requestId}]: ${method} ${endpoint}`);

      // Add detailed logging for password reset requests
      if (endpoint.includes('/actions/resetPassword')) {
        console.log(`🔐 PASSWORD RESET API CALL DETAILS:`);
        console.log(`🔗 Full URL: ${config.url}`);
        console.log(`📦 Request Data:`, JSON.stringify(data, null, 2));
        console.log(`📋 Request Headers:`, JSON.stringify(config.headers, null, 2));
      }

      const response = await axios(config);
      console.log(`Contabo API Response [${requestId}]: ${response.status}`);

      // Add detailed logging for password reset responses
      if (endpoint.includes('/actions/resetPassword')) {
        console.log(`🔐 CONTABO PASSWORD RESET RESPONSE - DETAILED ANALYSIS:`);
        console.log(`📊 HTTP Status: ${response.status} ${response.statusText}`);
        console.log(`📋 Response Headers:`, JSON.stringify(response.headers, null, 2));
        console.log(`📦 Full Response Data:`, JSON.stringify(response.data, null, 2));

        // Analyze the response structure
        if (response.data) {
          console.log(`🔍 Response Analysis:`);
          console.log(`   - Has 'data' field: ${!!response.data.data}`);
          console.log(`   - Has '_links' field: ${!!response.data._links}`);

          if (response.data.data && Array.isArray(response.data.data)) {
            console.log(`   - Data array length: ${response.data.data.length}`);
            response.data.data.forEach((item, index) => {
              console.log(`   - Item ${index}:`, JSON.stringify(item, null, 4));
              if (item.action) {
                console.log(`   - Action: ${item.action}`);
              }
              if (item.instanceId) {
                console.log(`   - Instance ID: ${item.instanceId}`);
              }
            });
          }

          if (response.data._links) {
            console.log(`   - Links:`, JSON.stringify(response.data._links, null, 4));
          }
        }

        // Check if this looks like a successful password reset
        const hasValidData = response.data && response.data.data && Array.isArray(response.data.data);
        const hasResetAction = hasValidData && response.data.data.some(item =>
          item.action === 'resetPassword' || item.action === 'start'
        );

        console.log(`🔍 SUCCESS INDICATORS:`);
        console.log(`   - HTTP 2xx status: ${response.status >= 200 && response.status < 300}`);
        console.log(`   - Has valid data structure: ${hasValidData}`);
        console.log(`   - Has reset action: ${hasResetAction}`);
        console.log(`   - Overall assessment: ${(response.status >= 200 && response.status < 300 && hasValidData) ? 'LIKELY SUCCESS' : 'POSSIBLE FAILURE'}`);
      }

      return response.data;
    } catch (error) {
      const requestId = error.config?.headers?.["x-request-id"] || "unknown";
      console.error(
        `Contabo API request failed [${requestId}]: ${method} ${endpoint}`,
        error.response?.data || error.message
      );
      throw new Error(
        `Contabo API request failed: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  }

  /**
   * Get available VPS plans from Contabo
   * Based on official Contabo API documentation - using actual product IDs
   * @returns {Promise<Array>} Array of VPS plans
   */
  async getPlans() {
    try {
      // Vérifier si les credentials sont configurés
      if (
        !this.clientId ||
        !this.clientSecret ||
        !this.username ||
        !this.password
      ) {
        console.log("Contabo credentials not configured, using static data");
        return this.getStaticPlans();
      }

      // According to Contabo API docs, these are the actual product IDs and specifications
      const contaboPlans = [
        // VPS 10 Series - Correspond aux noms sur le site
        {
          id: "V91",
          productId: "V91",
          name: "VPS 10 NVMe",
          description: "VPS 10 with 75 GB NVMe SSD",
          cpu: 1,
          ram: 4,
          storage: 75,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 4.99, hourly: 0.007 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V92",
          productId: "V92",
          name: "VPS 10 SSD",
          description: "VPS 10 with 150 GB SSD",
          cpu: 1,
          ram: 4,
          storage: 150,
          diskType: "SSD",
          bandwidth: 32000,
          price: { monthly: 4.99, hourly: 0.007 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V94",
          productId: "V94",
          name: "VPS 20 NVMe",
          description: "VPS 20 with 100 GB NVMe SSD",
          cpu: 2,
          ram: 8,
          storage: 100,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 8.99, hourly: 0.013 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V95",
          productId: "V95",
          name: "VPS 20 SSD",
          description: "VPS 20 with 200 GB SSD",
          cpu: 2,
          ram: 8,
          storage: 200,
          diskType: "SSD",
          bandwidth: 32000,
          price: { monthly: 8.99, hourly: 0.013 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V97",
          productId: "V97",
          name: "VPS 30 NVMe",
          description: "VPS 30 with 200 GB NVMe SSD",
          cpu: 4,
          ram: 16,
          storage: 200,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 16.99, hourly: 0.025 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V8",
          productId: "V8",
          name: "VDS S",
          description: "VDS S with 180 GB NVMe",
          cpu: 2,
          ram: 8,
          storage: 180,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 19.99, hourly: 0.03 },
          provider: "contabo",
          productType: "vds",
        },
      ];

      // Add regions to all plans
      contaboPlans.forEach((plan) => {
        plan.regions = ["European Union", "United States", "Asia Pacific"];
      });

      return contaboPlans;
    } catch (error) {
      console.error("Failed to fetch Contabo plans:", error.message);
      throw error;
    }
  }

  /**
   * Get VPS plan details by ID
   * @param {string} planId - Plan identifier
   * @returns {Promise<Object>} Plan details
   */
  async getPlanDetails(planId) {
    try {
      const plans = await this.getPlans();
      const plan = plans.find((p) => p.id === planId);

      if (!plan) {
        throw new Error(`Plan ${planId} not found`);
      }

      return plan;
    } catch (error) {
      console.error(
        `Failed to fetch Contabo plan details for ${planId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get available data centers
   * Based on https://api.contabo.com/#tag/Data-Centers/operation/retrieveDataCenterList
   * Fetches ALL data centers across all pages
   * @returns {Promise<Array>} Array of available data centers
   */
  async getDataCenters() {
    try {
      let allDataCenters = [];
      let currentPage = 1;
      let totalPages = 1;

      // Fetch all pages of data centers
      do {
        const queryParams = new URLSearchParams();
        queryParams.append("page", currentPage.toString());
        queryParams.append("size", "50"); // Increase page size to reduce API calls

        const endpoint = `/data-centers?${queryParams.toString()}`;
        const response = await this.makeRequest("GET", endpoint);

        // Add data centers from current page
        const pageDataCenters = response.data || [];
        allDataCenters = allDataCenters.concat(pageDataCenters);

        // Update pagination info
        if (response._pagination) {
          totalPages = response._pagination.totalPages;
          console.log(
            `Fetched data centers page ${currentPage} of ${totalPages} (${pageDataCenters.length} data centers)`
          );
        } else {
          // If no pagination info, assume this is the last page
          break;
        }

        currentPage++;
      } while (currentPage <= totalPages);

      console.log(`Total data centers fetched: ${allDataCenters.length}`);

      // Transform data centers using actual Contabo response structure
      return allDataCenters.map((dc) => ({
        name: dc.name,
        slug: dc.slug,
        capabilities: dc.capabilities || [],
        s3Url: dc.s3Url || "",
        regionName: dc.regionName,
        regionSlug: dc.regionSlug,
        tenantId: dc.tenantId,
        customerId: dc.customerId,
        provider: "contabo",
        // Additional computed fields for easier use
        supportsVPS: dc.capabilities?.includes("VPS") || false,
        supportsVDS: dc.capabilities?.includes("VDS") || false,
        supportsObjectStorage:
          dc.capabilities?.includes("Object-Storage") || false,
        supportsPrivateNetworking:
          dc.capabilities?.includes("Private-Networking") || false,
      }));
    } catch (error) {
      console.error("Failed to fetch Contabo data centers:", error.message);
      throw error;
    }
  }

  /**
   * Get available regions (extracted from data centers)
   * Groups data centers by regionSlug for easier selection
   * @returns {Promise<Array>} Array of available regions
   */
  async getRegions() {
    try {
      const dataCenters = await this.getDataCenters();

      // Extract unique regions from data centers using regionSlug
      const regionsMap = new Map();

      dataCenters.forEach((dc) => {
        const regionKey = dc.regionSlug;

        if (regionKey && !regionsMap.has(regionKey)) {
          regionsMap.set(regionKey, {
            regionSlug: dc.regionSlug,
            regionName: dc.regionName,
            dataCenters: [],
            capabilities: new Set(),
          });
        }

        if (regionKey && regionsMap.has(regionKey)) {
          const region = regionsMap.get(regionKey);

          // Add data center to region
          region.dataCenters.push({
            name: dc.name,
            slug: dc.slug,
            capabilities: dc.capabilities,
            s3Url: dc.s3Url,
            supportsVPS: dc.supportsVPS,
            supportsVDS: dc.supportsVDS,
            supportsObjectStorage: dc.supportsObjectStorage,
            supportsPrivateNetworking: dc.supportsPrivateNetworking,
          });

          // Aggregate capabilities for the region
          dc.capabilities?.forEach((capability) => {
            region.capabilities.add(capability);
          });
        }
      });

      // Convert capabilities Set to Array and return regions
      return Array.from(regionsMap.values()).map((region) => ({
        ...region,
        capabilities: Array.from(region.capabilities),
        // Add convenience flags for the region
        supportsVPS: region.capabilities.has("VPS"),
        supportsVDS: region.capabilities.has("VDS"),
        supportsObjectStorage: region.capabilities.has("Object-Storage"),
        supportsPrivateNetworking:
          region.capabilities.has("Private-Networking"),
      }));
    } catch (error) {
      console.error("Failed to fetch Contabo regions:", error.message);
      throw error;
    }
  }

  /**
   * Get available images
   * Based on https://api.contabo.com/#tag/Images/operation/retrieveImageList
   * Fetches ALL images across all pages
   * @returns {Promise<Array>} Array of available OS images
   */
  async getImages() {
    try {
      let allImages = [];
      let currentPage = 1;
      let totalPages = 1;

      // Fetch all pages of images
      do {
        const queryParams = new URLSearchParams();
        queryParams.append("page", currentPage.toString());
        queryParams.append("size", "50"); // Increase page size to reduce API calls

        const endpoint = `/compute/images?${queryParams.toString()}`;
        const response = await this.makeRequest("GET", endpoint);

        // Add images from current page
        const pageImages = response.data || [];
        allImages = allImages.concat(pageImages);

        // Update pagination info
        if (response._pagination) {
          totalPages = response._pagination.totalPages;
          console.log(
            `Fetched page ${currentPage} of ${totalPages} (${pageImages.length} images)`
          );
        } else {
          // If no pagination info, assume this is the last page
          break;
        }

        currentPage++;
      } while (currentPage <= totalPages);

      console.log(`Total images fetched: ${allImages.length}`);

      // Transform images to standardized format
      return allImages.map((image) => ({
        imageId: image.imageId,
        tenantId: image.tenantId,
        customerId: image.customerId,
        name: image.name,
        description: image.description,
        url: image.url,
        sizeMb: image.sizeMb,
        uploadedSizeMb: image.uploadedSizeMb,
        osType: image.osType,
        version: image.version,
        format: image.format,
        status: image.status,
        errorMessage: image.errorMessage,
        standardImage: image.standardImage,
        creationDate: image.creationDate,
        lastModifiedDate: image.lastModifiedDate,
        tags: image.tags || [],
        provider: "contabo",
      }));
    } catch (error) {
      console.error("Failed to fetch Contabo images:", error.message);
      throw error;
    }
  }

  /**
   * Get available applications
   * Note: Contabo API doesn't have a dedicated applications endpoint
   * This method returns common applications that can be used with applicationId
   * @returns {Promise<Array>} Array of available applications
   */
  async getApplications() {
    try {
      // Contabo doesn't have a dedicated applications API endpoint
      // These are common application IDs that work with Contabo
      const applications = [
        {
          applicationId: "docker",
          name: "Docker",
          description: "Docker container platform",
          category: "containerization"
        },
        {
          applicationId: "nginx",
          name: "Nginx",
          description: "High-performance web server",
          category: "web-server"
        },
        {
          applicationId: "apache",
          name: "Apache",
          description: "Apache HTTP Server",
          category: "web-server"
        },
        {
          applicationId: "mysql",
          name: "MySQL",
          description: "MySQL database server",
          category: "database"
        },
        {
          applicationId: "postgresql",
          name: "PostgreSQL",
          description: "PostgreSQL database server",
          category: "database"
        },
        {
          applicationId: "nodejs",
          name: "Node.js",
          description: "JavaScript runtime",
          category: "runtime"
        },
        {
          applicationId: "python",
          name: "Python",
          description: "Python programming language",
          category: "runtime"
        },
        {
          applicationId: "php",
          name: "PHP",
          description: "PHP programming language",
          category: "runtime"
        }
      ];

      console.log(`📦 Available applications: ${applications.length}`);
      return applications;
    } catch (error) {
      console.error("Failed to fetch applications:", error.message);
      throw error;
    }
  }

  /**
   * Create a new VPS instance
   * Based on https://api.contabo.com/#tag/Instances/operation/createInstance
   * @param {Object} orderData - VPS order data
   * @returns {Promise<Object>} Created VPS instance details
   */
  async createVPS(orderData) {
    try {
      // Get available images to find the correct imageId
      const images = await this.getImages();
      const selectedImage = images.find(
        (img) =>
          img.name.toLowerCase().includes(orderData.imageId.toLowerCase()) ||
          img.slug === orderData.imageId ||
          img.imageId === orderData.imageId
      );

      if (!selectedImage) {
        throw new Error(`Image ${orderData.imageId} not found`);
      }

      // According to Contabo API docs, the instance creation payload
      const instanceData = {
        imageId: selectedImage.imageId,
        productId: orderData.planId, // Use actual Contabo product IDs like V91, V92, etc.
        region: orderData.region || "EU",
        displayName: orderData.displayName || `VPS-${Date.now()}`,

        // Optional parameters according to API docs
        ...(orderData.sshKeys &&
          orderData.sshKeys.length > 0 && { sshKeys: orderData.sshKeys }),
        // Note: Contabo generates rootPassword automatically, custom passwords not supported in creation
        ...(orderData.userData && { userData: orderData.userData }),

        // Billing period (1 = monthly, 3 = quarterly, 6 = semi-annually, 12 = annually)
        period: orderData.period || 1,

        // Optional addons for private networking, etc.
        ...(orderData.addons && { addons: orderData.addons }),
      };

      console.log(
        "Creating Contabo instance with data:",
        JSON.stringify(instanceData, null, 2)
      );

      const response = await this.makeRequest(
        "POST",
        "/compute/instances",
        instanceData
      );

      console.log(
        "Contabo instance creation response:",
        JSON.stringify(response, null, 2)
      );

      // According to API docs, response structure should contain the created instance
      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return this.transformData(response.data[0], "instance");
      } else if (response.data) {
        return this.transformData(response.data, "instance");
      } else {
        // If no data field, the response itself might be the instance
        return this.transformData(response, "instance");
      }
    } catch (error) {
      console.error("Failed to create Contabo VPS:", error.message);
      console.error("Error details:", error.response?.data || error);
      throw error;
    }
  }

  /**
   * Get VPS instance details
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} VPS instance details
   */
  async getVPSDetails(instanceId) {
    try {
      const response = await this.makeRequest(
        "GET",
        `/compute/instances/${instanceId}`
      );
      return this.transformData(response.data, "instance");
    } catch (error) {
      console.error(
        `Failed to fetch Contabo VPS details for ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get all VPS instances for a customer
   * @param {string} customerId - Customer identifier (not used in Contabo, returns all instances)
   * @returns {Promise<Array>} Array of VPS instances
   */
  async getCustomerVPS(customerId) {
    try {
      const response = await this.makeRequest("GET", "/compute/instances");
      const instances = response.data || [];

      return instances.map((instance) =>
        this.transformData(instance, "instance")
      );
    } catch (error) {
      console.error("Failed to fetch Contabo VPS instances:", error.message);
      throw error;
    }
  }

  /**
   * Start a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async startVPS(instanceId) {
    try {
      console.log(`🚀 Starting VPS instance: ${instanceId}`);

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/start`,
        {}
      );

      console.log(`✅ VPS ${instanceId} start command sent successfully`);
      return {
        success: true,
        action: "start",
        instanceId,
        message: "VPS start command sent successfully",
        data: response,
      };
    } catch (error) {
      console.error(`❌ Failed to start VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to start VPS: ${error.message}`);
    }
  }

  /**
   * Stop a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async stopVPS(instanceId) {
    try {
      console.log(`🛑 Stopping VPS instance: ${instanceId}`);

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/stop`,
        {}
      );

      console.log(`✅ VPS ${instanceId} stop command sent successfully`);
      return {
        success: true,
        action: "stop",
        instanceId,
        message: "VPS stop command sent successfully",
        data: response,
      };
    } catch (error) {
      console.error(`❌ Failed to stop VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to stop VPS: ${error.message}`);
    }
  }

  /**
   * Restart a VPS instance (via stop then start)
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async restartVPS(instanceId) {
    try {
      console.log(
        `🔄 Restarting VPS instance: ${instanceId} (stop then start)`
      );

      // Utiliser l'action restart directement selon la doc Contabo
      console.log(`🔄 Using Contabo restart action`);
      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/restart`,
        {}
      );

      console.log(`✅ VPS ${instanceId} restart completed successfully`);
      return {
        success: true,
        action: "restart",
        instanceId,
        message: "VPS restart completed successfully (stop + start)",
        data: response,
      };
    } catch (error) {
      console.error(`❌ Failed to restart VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to restart VPS: ${error.message}`);
    }
  }



  /**
   * Create VPS snapshot with quota verification
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotName - Name for the snapshot
   * @param {string} description - Description for the snapshot
   * @returns {Promise<Object>} Operation result
   */
  async createSnapshot(instanceId, snapshotName, description) {
    try {
      console.log(
        `📸 Creating snapshot for VPS ${instanceId}, checking quota first...`
      );

      // First, check current snapshots and quota
      const snapshotInfo = await this.getVPSSnapshots(instanceId);

      if (!snapshotInfo.quota.canCreate) {
        console.log(
          `❌ Snapshot quota exceeded: ${snapshotInfo.quota.current}/${snapshotInfo.quota.max}`
        );
        throw new Error(
          `The maximum number of snapshots for this VPS has been reached (${snapshotInfo.quota.max}). You need to delete old snapshots before you can create new ones.`
        );
      }

      console.log(
        `✅ Quota check passed: ${snapshotInfo.quota.current}/${snapshotInfo.quota.max} snapshots`
      );

      const snapshotData = {
        name: snapshotName || `snapshot-${Date.now()}`,
        description:
          description || `Snapshot created on ${new Date().toISOString()}`,
      };

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/snapshots`,
        snapshotData
      );

      console.log(`✅ Snapshot created successfully for VPS ${instanceId}`);
      return {
        success: true,
        message: "Snapshot created successfully",
        data: response,
        quota: {
          current: snapshotInfo.quota.current + 1,
          max: snapshotInfo.quota.max,
          remaining: snapshotInfo.quota.remaining - 1,
        },
      };
    } catch (error) {
      console.error(
        `❌ Failed to create Contabo VPS snapshot ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }



  /**
   * Delete a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async deleteVPS(instanceId) {
    try {
      const response = await this.makeRequest(
        "DELETE",
        `/compute/instances/${instanceId}`
      );
      return {
        success: true,
        message: "VPS deleted successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `Failed to delete Contabo VPS ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get VPS usage statistics
   * Note: Contabo doesn't have a direct stats endpoint, so we'll get instance details
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Usage statistics
   */
  async getVPSStats(instanceId) {
    try {
      // Get instance details which may include some usage info
      const response = await this.makeRequest(
        "GET",
        `/compute/instances/${instanceId}`
      );

      // Transform to stats format (simulated data since Contabo doesn't provide detailed stats)
      const statsData = {
        cpu: {
          current: Math.random() * 100, // Simulated CPU usage
          average: Math.random() * 50,
          peak: Math.random() * 100,
        },
        memory: {
          used: Math.random() * 8, // Simulated memory usage in GB
          total: response.data?.ram || 8,
          percentage: Math.random() * 100,
        },
        disk: {
          used: Math.random() * 50, // Simulated disk usage in GB
          total: response.data?.disk || 100,
          percentage: Math.random() * 100,
        },
        network: {
          inbound: Math.random() * 1000, // Simulated network usage in GB
          outbound: Math.random() * 1000,
          total: Math.random() * 2000,
        },
        timestamp: new Date().toISOString(),
        provider: "contabo",
      };

      return statsData;
    } catch (error) {
      console.error(
        `Failed to fetch Contabo VPS stats for ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Validate provider configuration
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    try {
      if (
        !this.clientId ||
        !this.clientSecret ||
        !this.username ||
        !this.password
      ) {
        return false;
      }

      await this.authenticate();
      return true;
    } catch (error) {
      console.error("Contabo configuration validation failed:", error.message);
      return false;
    }
  }

  /**
   * Get provider name
   * @returns {string} Provider name
   */
  getProviderName() {
    return "contabo";
  }

  // ==================== Object Storage Methods ====================

  /**
   * Get object storages
   * Based on https://api.contabo.com/#tag/Object-Storages/operation/retrieveObjectStorageList
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of object storages
   */
  async getObjectStorages(options = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (options.page) queryParams.append("page", options.page);
      if (options.size) queryParams.append("size", options.size);
      if (options.orderBy) queryParams.append("orderBy", options.orderBy);
      if (options.dataCenter)
        queryParams.append("dataCenter", options.dataCenter);
      if (options.s3TenantId)
        queryParams.append("s3TenantId", options.s3TenantId);

      const queryString = queryParams.toString();
      const endpoint = queryString
        ? `/object-storages?${queryString}`
        : "/object-storages";

      const response = await this.makeRequest("GET", endpoint);
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch Contabo object storages:", error.message);
      throw error;
    }
  }

  /**
   * Create object storage
   * Based on https://api.contabo.com/#tag/Object-Storages/operation/createObjectStorage
   * @param {Object} storageData - Object storage data
   * @returns {Promise<Object>} Created object storage
   */
  async createObjectStorage(storageData) {
    try {
      const payload = {
        dataCenter: storageData.dataCenter,
        totalPurchasedSpaceTB: storageData.totalPurchasedSpaceTB || 1,
        displayName: storageData.displayName,
      };

      const response = await this.makeRequest(
        "POST",
        "/object-storages",
        payload
      );

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return response.data[0];
      } else if (response.data) {
        return response.data;
      } else {
        return response;
      }
    } catch (error) {
      console.error("Failed to create Contabo object storage:", error.message);
      throw error;
    }
  }

  // ==================== User Management Methods ====================

  /**
   * Get users list
   * Based on https://api.contabo.com/#tag/Users/<USER>/retrieveUserList
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of users
   */
  async getUsers(options = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (options.page) queryParams.append("page", options.page);
      if (options.size) queryParams.append("size", options.size);
      if (options.orderBy) queryParams.append("orderBy", options.orderBy);
      if (options.email) queryParams.append("email", options.email);
      if (options.enabled !== undefined)
        queryParams.append("enabled", options.enabled);
      if (options.owner !== undefined)
        queryParams.append("owner", options.owner);

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/users?${queryString}` : "/users";

      const response = await this.makeRequest("GET", endpoint);
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch Contabo users:", error.message);
      throw error;
    }
  }

  /**
   * Create a new user
   * Based on https://api.contabo.com/#tag/Users/<USER>/createUser
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user details
   */
  async createUser(userData) {
    try {
      const userPayload = {
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        enabled: userData.enabled !== undefined ? userData.enabled : true,
        totp: userData.totp !== undefined ? userData.totp : false,
        locale: userData.locale || "en",
        roles: userData.roles || [],
      };

      const response = await this.makeRequest("POST", "/users", userPayload);

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return this.transformData(response.data[0], "user");
      } else if (response.data) {
        return this.transformData(response.data, "user");
      } else {
        return this.transformData(response, "user");
      }
    } catch (error) {
      console.error("Failed to create Contabo user:", error.message);
      throw error;
    }
  }

  /**
   * Transform Contabo-specific data to standardized format
   * @param {Object} providerData - Contabo-specific data
   * @param {string} dataType - Type of data (plan, instance, stats, user)
   * @returns {Object} Standardized data
   */
  transformData(providerData, dataType) {
    switch (dataType) {
      case "plan":
        return {
          id: providerData.productId || providerData.id,
          name: providerData.name,
          description: providerData.description,
          cpu: providerData.cpu,
          ram: providerData.ram,
          storage: providerData.storage || providerData.disk,
          diskType: providerData.diskType,
          bandwidth: providerData.bandwidth || providerData.traffic,
          price: {
            monthly: providerData.price?.monthly,
            hourly: providerData.price?.hourly,
          },
          regions: providerData.regions || [],
          provider: "contabo",
          productType: providerData.productType,
        };

      case "instance":
        // Cloud-Init configuration managed via PATCH userData

        return {
          id: providerData.instanceId || providerData.id,
          name: providerData.displayName || providerData.name,
          status: this.mapContaboStatus(providerData.status),
          ip:
            providerData.ipConfig?.v4?.ip ||
            (providerData.ipConfig?.v4 && providerData.ipConfig.v4[0]?.ip) ||
            providerData.ipv4,
          ipv6:
            providerData.ipConfig?.v6?.ip ||
            (providerData.ipConfig?.v6 && providerData.ipConfig.v6[0]?.ip) ||
            providerData.ipv6,
          region: providerData.region || providerData.dataCenter,
          dataCenter: providerData.dataCenter,
          plan: providerData.product || providerData.productId,
          productId: providerData.productId,
          productType: providerData.productType,
          createdAt: providerData.createdDate || providerData.createdAt,
          tenantId: providerData.tenantId,
          customerId: providerData.customerId,
          imageId: providerData.imageId,
          cpu: providerData.cpu,
          ram: providerData.ram,
          disk: providerData.disk,
          // Récupérer la configuration Cloud-Init depuis l'API Contabo
          // Contabo utilise le champ userData pour Cloud-Init
          cloudInitEnabled: providerData.userData !== null &&
                           providerData.userData !== undefined &&
                           providerData.userData !== "",
          provider: "contabo",
          raw: providerData,
        };

      case "user":
        return {
          id: providerData.userId || providerData.id,
          email: providerData.email,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          fullName: `${providerData.firstName} ${providerData.lastName}`.trim(),
          enabled: providerData.enabled,
          totp: providerData.totp,
          locale: providerData.locale,
          roles: providerData.roles || [],
          tenantId: providerData.tenantId,
          customerId: providerData.customerId,
          owner: providerData.owner,
          createdAt: providerData.createdDate || providerData.createdAt,
          updatedAt: providerData.updatedDate || providerData.updatedAt,
          provider: "contabo",
          raw: providerData,
        };

      case "stats":
        return {
          cpu: {
            current: Math.random() * 100,
            average: Math.random() * 50,
            peak: Math.random() * 100,
          },
          memory: {
            used: Math.random() * 8,
            total: providerData.ram || 8,
            percentage: Math.random() * 100,
          },
          disk: {
            used: Math.random() * 50,
            total: providerData.storage || 100,
            percentage: Math.random() * 100,
          },
          network: {
            inbound: Math.random() * 1000,
            outbound: Math.random() * 1000,
            total: Math.random() * 2000,
          },
          timestamp: new Date().toISOString(),
          provider: "contabo",
        };

      default:
        return providerData;
    }
  }

  /**
   * Map Contabo status to standardized status
   * @param {string} contaboStatus - Contabo instance status
   * @returns {string} Standardized status
   */
  mapContaboStatus(contaboStatus) {
    const statusMap = {
      running: "running",
      started: "running",
      stopped: "stopped",
      shutdown: "stopped",
      installing: "creating",
      provisioning: "creating",
      starting: "starting",
      stopping: "stopping",
      restarting: "restarting",
      reinstalling: "reinstalling",
      rescue: "rescue",
      "rescue mode": "rescue",
      "rescue-mode": "rescue",
      rescuemode: "rescue",
      "rescue system": "rescue",
      "rescue/reset password": "rescue",
      "reset password": "rescue",
      error: "error",
      failed: "error",
      suspended: "suspended",
      maintenance: "maintenance",
      unknown: "unknown",
      pending: "pending",
    };

    const normalizedStatus = contaboStatus?.toLowerCase();
    return statusMap[normalizedStatus] || normalizedStatus || "unknown";
  }

  /**
   * Update VPS instance configuration (including Cloud-Init)
   * @param {string} instanceId - VPS instance ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Operation result
   */
  async updateInstance(instanceId, updateData) {
    try {
      console.log(`🔧 Updating VPS instance configuration: ${instanceId}`, updateData);

      const response = await this.makeRequest(
        "PATCH",
        `/compute/instances/${instanceId}`,
        updateData
      );

      console.log(`✅ VPS ${instanceId} configuration updated successfully`);
      return {
        success: true,
        action: 'update',
        instanceId,
        message: "VPS configuration updated successfully",
        data: response,
      };
    } catch (error) {
      console.error(`❌ Failed to update VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to update VPS: ${error.message}`);
    }
  }

  /**
   * Get Cloud-Init status for a VPS instance (simplified)
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async getCloudInitStatus(instanceId) {
    try {
      console.log(`☁️ Getting Cloud-Init status for VPS instance: ${instanceId}`);

      // Note: Contabo ne retourne pas userData dans GET instances
      // Retournons toujours false pour l'instant
      return {
        success: true,
        instanceId,
        enabled: false,
        message: `Cloud-Init status (Contabo API limitation)`,
        note: "Contabo doesn't return userData in GET instances"
      };
    } catch (error) {
      console.error(`❌ Failed to get Cloud-Init status for VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to get Cloud-Init status: ${error.message}`);
    }
  }

  /**
   * Toggle Cloud-Init configuration for a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {boolean} enabled - Enable or disable cloud-init
   * @returns {Promise<Object>} Operation result
   */
  async toggleCloudInit(instanceId, enabled = true) {
    try {
      console.log(`☁️ ${enabled ? 'Enabling' : 'Disabling'} Cloud-Init for VPS instance: ${instanceId}`);

      // Contabo utilise le champ user_data pour Cloud-Init
      // Si enabled = true, on met un script cloud-init basique
      // Si enabled = false, on met null pour désactiver
      const updateData = {
        userData: enabled ? "#cloud-config\n# Cloud-Init enabled via Contabo API\n" : null
      };

      const response = await this.updateInstance(instanceId, updateData);

      console.log(`✅ VPS ${instanceId} Cloud-Init ${enabled ? 'enabled' : 'disabled'} successfully`);
      return {
        success: true,
        action: 'cloud-init-toggle',
        instanceId,
        enabled,
        message: `Cloud-Init ${enabled ? 'enabled' : 'disabled'} successfully`,
        data: response.data,
      };
    } catch (error) {
      console.error(
        `❌ Failed to toggle Cloud-Init for VPS ${instanceId}:`,
        error.message
      );
      throw new Error(`Failed to toggle Cloud-Init: ${error.message}`);
    }
  }

  /**
   * Register SSH key with Contabo Secrets API
   * @param {string} sshKeyValue - SSH public key content
   * @param {string} name - Optional name for the SSH key
   * @returns {Promise<number>} SSH key ID
   */
  async registerSSHKey(sshKeyValue, name = null) {
    try {
      const keyName = name || `SSH-Key-${Date.now()}`;
      console.log(`🔑 Registering SSH key: ${keyName}`);

      const payload = {
        name: keyName,
        value: sshKeyValue,
        type: 'ssh'
      };

      const response = await this.makeRequest("POST", `/secrets`, payload);
      console.log('🔍 SSH key registration response:', JSON.stringify(response, null, 2));

      // Extract the secret ID from the response
      const secretId = response.data?.[0]?.secretId || response.data?.secretId || response.secretId;
      console.log(`✅ SSH key registered with ID: ${secretId}`);

      return secretId;
    } catch (error) {
      console.error(`❌ Failed to register SSH key:`, error.message);
      throw new Error(`Failed to register SSH key: ${error.message}`);
    }
  }

  /**
   * Generate a password that meets Contabo's strict requirements
   * Pattern: ^((?=.*?[A-Z]{1,})(?=.*?[a-z]{1,}))(((?=(?:[^0-9]*[0-9]){1})(?=([^!@#$^&*?_~]*[!@#$^&*?_~]){2,}))|((?=(?:[^0-9]*[0-9]){3})(?=.*?[!@#$^&*?_~]+))).{8,}$
   * Requirements:
   * - At least 8 characters
   * - At least one uppercase letter
   * - At least one lowercase letter
   * - Either: (1 number + 2 special chars) OR (3 numbers + 1 special char)
   * - Special chars: !@#$^&*?_~
   * @returns {string} Contabo-compliant password
   */
  generateContaboCompliantPassword() {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '!@#$^&*?_~';

    // Strategy: Use 3 numbers + 1 special char (easier to satisfy)
    let password = '';

    // Add required characters
    password += uppercase[Math.floor(Math.random() * uppercase.length)]; // 1 uppercase
    password += lowercase[Math.floor(Math.random() * lowercase.length)]; // 1 lowercase
    password += numbers[Math.floor(Math.random() * numbers.length)];     // 1st number
    password += numbers[Math.floor(Math.random() * numbers.length)];     // 2nd number
    password += numbers[Math.floor(Math.random() * numbers.length)];     // 3rd number
    password += specialChars[Math.floor(Math.random() * specialChars.length)]; // 1 special

    // Add additional characters to reach at least 12 characters for security
    const allChars = uppercase + lowercase + numbers + specialChars;
    while (password.length < 12) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password to randomize character positions
    const passwordArray = password.split('');
    for (let i = passwordArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];
    }

    const finalPassword = passwordArray.join('');

    // Verify the generated password meets requirements
    const passwordPattern = /^((?=.*?[A-Z]{1,})(?=.*?[a-z]{1,}))(((?=(?:[^0-9]*[0-9]){1})(?=([^!@#$^&*?_~]*[!@#$^&*?_~]){2,}))|((?=(?:[^0-9]*[0-9]){3})(?=.*?[!@#$^&*?_~]+))).{8,}$/;

    if (!passwordPattern.test(finalPassword)) {
      console.log(`⚠️ Generated password failed validation, retrying...`);
      return this.generateContaboCompliantPassword(); // Retry if validation fails
    }

    return finalPassword;
  }

  /**
   * Create password secret with Contabo Secrets API
   * @param {string} password - Password value
   * @param {string} name - Optional name for the password secret
   * @returns {Promise<number>} Password secret ID
   */
  async createPasswordSecret(password, name = null) {
    try {
      const secretName = name || `Password-${Date.now()}`;
      console.log(`🔐 Creating password secret: ${secretName}`);

      const payload = {
        name: secretName,
        value: password,
        type: 'password'
      };

      const response = await this.makeRequest("POST", `/secrets`, payload);
      console.log('🔍 Password secret creation response:', JSON.stringify(response, null, 2));

      // Extract the secret ID from the response
      const secretId = response.data?.[0]?.secretId || response.data?.secretId || response.secretId;
      console.log(`✅ Password secret created with ID: ${secretId}`);

      return secretId;
    } catch (error) {
      console.error(`❌ Failed to create password secret:`, error.message);
      throw new Error(`Failed to create password secret: ${error.message}`);
    }
  }

  /**
   * Get all secrets from Contabo Secrets API
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of secrets
   */
  async getSecrets(options = {}) {
    try {
      console.log(`📋 Getting secrets from Contabo`);

      const queryParams = new URLSearchParams();
      if (options.page) queryParams.append("page", options.page);
      if (options.size) queryParams.append("size", options.size);
      if (options.orderBy) queryParams.append("orderBy", options.orderBy);
      if (options.name) queryParams.append("name", options.name);
      if (options.type) queryParams.append("type", options.type);

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/secrets?${queryString}` : "/secrets";

      const response = await this.makeRequest("GET", endpoint);
      console.log(`✅ Retrieved ${response.data?.length || 0} secrets`);

      return response.data || [];
    } catch (error) {
      console.error(`❌ Failed to get secrets:`, error.message);
      throw new Error(`Failed to get secrets: ${error.message}`);
    }
  }

  /**
   * Create a secret with Contabo Secrets API
   * @param {string} name - Secret name
   * @param {string} value - Secret value
   * @param {string} type - Secret type ('password' or 'ssh_key')
   * @returns {Promise<Object>} Created secret information
   */
  async createSecret(name, value, type) {
    try {
      console.log(`🔐 Creating secret: ${name} (type: ${type})`);

      const payload = {
        name,
        value,
        type
      };

      const response = await this.makeRequest("POST", `/secrets`, payload);
      console.log('🔍 Secret creation response:', JSON.stringify(response, null, 2));

      return response.data?.[0] || response.data || response;
    } catch (error) {
      console.error(`❌ Failed to create secret:`, error.message);
      throw new Error(`Failed to create secret: ${error.message}`);
    }
  }

  /**
   * Update a secret with Contabo Secrets API
   * @param {number} secretId - Secret ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated secret information
   */
  async updateSecret(secretId, updateData) {
    try {
      console.log(`🔄 Updating secret: ${secretId}`);

      const response = await this.makeRequest("PATCH", `/secrets/${secretId}`, updateData);
      console.log('🔍 Secret update response:', JSON.stringify(response, null, 2));

      return response.data?.[0] || response.data || response;
    } catch (error) {
      console.error(`❌ Failed to update secret:`, error.message);
      throw new Error(`Failed to update secret: ${error.message}`);
    }
  }

  /**
   * Get a specific secret by ID
   * @param {number} secretId - Secret ID
   * @returns {Promise<Object>} Secret information
   */
  async getSecret(secretId) {
    try {
      console.log(`🔍 Getting secret: ${secretId}`);

      const response = await this.makeRequest("GET", `/secrets/${secretId}`);
      const secret = response.data?.[0] || response.data;

      console.log(`✅ Retrieved secret ${secretId}:`, {
        found: !!secret,
        name: secret?.name,
        type: secret?.type
      });

      return secret;
    } catch (error) {
      console.error(`❌ Failed to get secret ${secretId}:`, error.message);
      throw new Error(`Failed to get secret: ${error.message}`);
    }
  }

  /**
   * Delete a secret with Contabo Secrets API
   * @param {number} secretId - Secret ID
   * @returns {Promise<void>}
   */
  async deleteSecret(secretId) {
    try {
      console.log(`🗑️ Deleting secret: ${secretId}`);

      await this.makeRequest("DELETE", `/secrets/${secretId}`);
      console.log(`✅ Secret ${secretId} deleted successfully`);
    } catch (error) {
      console.error(`❌ Failed to delete secret:`, error.message);
      throw new Error(`Failed to delete secret: ${error.message}`);
    }
  }

  /**
   * Reset password for VPS instance using Contabo API
   * @param {string} instanceId - VPS instance ID
   * @param {Object} resetData - Password reset parameters
   * @param {string|number} resetData.rootPassword - New root password (plain text or secretId)
   * @param {Array<number>} resetData.sshKeys - Array of SSH key secretIds (optional)
   * @param {string} resetData.userData - Cloud-Init config (optional)
   * @returns {Promise<Object>} Operation result
   */
  async resetPassword(instanceId, resetData) {
    try {


      // Validate instance ID
      if (!instanceId) {
        throw new Error('Instance ID is required');
      }

      // Ensure instance ID is a number
      const numericInstanceId = typeof instanceId === 'string' ? parseInt(instanceId, 10) : instanceId;
      if (isNaN(numericInstanceId)) {
        throw new Error(`Invalid instance ID: ${instanceId}`);
      }

      // First, verify the instance exists and get its current state
      try {
        const instanceResponse = await this.makeRequest("GET", `/compute/instances/${numericInstanceId}`);
        const instance = instanceResponse.data?.[0];

        if (!instance) {
          throw new Error(`Instance ${numericInstanceId} not found`);
        }



      } catch (instanceError) {
        throw new Error(`Instance verification failed: ${instanceError.message}`);
      }

      // Validate required parameters
      if (!resetData.rootPassword) {
        throw new Error('rootPassword is required for password reset');
      }

      // Validate password format if it's a plain text password
      if (typeof resetData.rootPassword === 'string' && !/^\d+$/.test(resetData.rootPassword)) {
        const password = resetData.rootPassword;

        // Detailed character analysis
        const uppercaseChars = password.match(/[A-Z]/g) || [];
        const lowercaseChars = password.match(/[a-z]/g) || [];
        const numberChars = password.match(/[0-9]/g) || [];
        const specialChars = password.match(/[!@#$^&*?_~]/g) || [];
        const invalidChars = password.match(/[^a-zA-Z0-9!@#$^&*?_~]/g) || [];

        // Validation checks
        const hasUppercase = uppercaseChars.length > 0;
        const hasLowercase = lowercaseChars.length > 0;
        const hasMinLength = password.length >= 8;
        const hasNoInvalidChars = invalidChars.length === 0;
        const meetsNumberSpecialReq = (numberChars.length >= 1 && specialChars.length >= 2) ||
                                     (numberChars.length >= 3 && specialChars.length >= 1);

        const meetsAllRequirements = hasMinLength && hasUppercase && hasLowercase && hasNoInvalidChars && meetsNumberSpecialReq;

        if (!meetsAllRequirements) {
          throw new Error(`Password does not meet Contabo requirements.`);
        }
      }

      // Handle root password - create secret if plain text password provided (same as reinstall)
      let passwordSecretId;

      if (typeof resetData.rootPassword === 'number') {
        // Already a secretId
        passwordSecretId = resetData.rootPassword;
        console.log(`🔑 Using existing password secretId: ${passwordSecretId}`);
      } else if (typeof resetData.rootPassword === 'string' && /^\d+$/.test(resetData.rootPassword)) {
        // String that looks like a number (secretId)
        passwordSecretId = parseInt(resetData.rootPassword, 10);
        console.log(`🔑 Using password secretId from string: ${passwordSecretId}`);
      } else {
        // Plain text password - create a secret (same method as reinstall)
        console.log(`🔐 Creating password secret for reset`);
        passwordSecretId = await this.createPasswordSecret(
          resetData.rootPassword,
          `VPS-${instanceId}-Reset-${Date.now()}`
        );
      }

      // Prepare the payload according to Contabo API specification
      const payload = {
        rootPassword: passwordSecretId
      };

      // Add optional SSH keys if provided - Contabo expects string format "[123, 125]"
      if (resetData.sshKeys && Array.isArray(resetData.sshKeys) && resetData.sshKeys.length > 0) {
        // Ensure all SSH keys are integers
        const sshKeyIds = resetData.sshKeys.map(key => {
          const keyId = typeof key === 'string' ? parseInt(key, 10) : key;
          if (isNaN(keyId)) {
            throw new Error(`Invalid SSH key ID: ${key}`);
          }
          return keyId;
        });

        // Format as string array for Contabo API
        payload.sshKeys = `[${sshKeyIds.join(', ')}]`;
      }

      // Add optional userData if provided
      if (resetData.userData && resetData.userData.trim()) {
        payload.userData = resetData.userData.trim();
      }

      // Add defaultUser if provided (same as reinstall)
      if (resetData.defaultUser) {
        payload.defaultUser = resetData.defaultUser;
      }

      // Validate payload matches Contabo requirements
      if (typeof payload.rootPassword !== 'number') {
        throw new Error(`rootPassword must be a number (secret ID), got: ${typeof payload.rootPassword}`);
      }

      // Note: sshKeys is now a string format "[123, 125]" for Contabo API
      if (payload.sshKeys && typeof payload.sshKeys !== 'string') {
        throw new Error(`sshKeys must be a string format "[123, 125]", got: ${typeof payload.sshKeys}`);
      }

      // Remove any undefined/null values from payload
      const cleanPayload = {};
      Object.keys(payload).forEach(key => {
        if (payload[key] !== undefined && payload[key] !== null) {
          cleanPayload[key] = payload[key];
        }
      });

      // Verify payload is not empty
      if (Object.keys(cleanPayload).length === 0) {
        throw new Error('Payload is empty - no valid parameters to send');
      }

      if (!cleanPayload.rootPassword) {
        throw new Error('rootPassword is missing from payload');
      }

      console.log(`📤 Sending password reset request with Contabo format:`, {
        ...cleanPayload,
        rootPassword: `***SECRET_ID_${cleanPayload.rootPassword}***`
      });

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${numericInstanceId}/actions/resetPassword`,
        cleanPayload
      );

      console.log(`📥 CONTABO API RESPONSE:`, JSON.stringify(response, null, 2));

      // Validate Contabo response format
      if (!response) {
        throw new Error('No response received from Contabo API');
      }

      // Check for expected response structure: { data: [...], _links: {...} }
      if (!response.data) {
        throw new Error('Invalid response format: missing data field');
      }

      if (!Array.isArray(response.data)) {
        throw new Error('Invalid response format: data field must be an array');
      }

      if (response.data.length === 0) {
        throw new Error('Invalid response: data array is empty');
      }



      return {
        success: true,
        action: 'resetPassword',
        instanceId: numericInstanceId,
        passwordSecretId: passwordSecretId,
        message: "VPS password reset API call completed successfully",
        contaboResponse: response,
        note: "Please test login with new password to verify the reset was effective"
      };
    } catch (error) {

      // Try to extract more specific error information
      let specificError = error.message;
      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          specificError = error.response.data;
        } else if (error.response.data.message) {
          specificError = error.response.data.message;
        } else if (error.response.data.error) {
          specificError = error.response.data.error;
        } else if (error.response.data.errors) {
          specificError = JSON.stringify(error.response.data.errors);
        }
      }

      throw new Error(`Contabo API error (${error.response?.status || 'unknown'}): ${specificError}`);
    }
  }

  /**
   * Reinstall VPS instance with new OS
   * @param {string} instanceId - VPS instance ID
   * @param {Object} reinstallData - Reinstallation parameters
   * @param {string} reinstallData.imageId - Image ID for reinstallation
   * @param {string} reinstallData.customImageId - Custom image ID (alternative to imageId)
   * @param {string} reinstallData.rootPassword - Root password (plain text or secretId)
   * @param {Array<number>} reinstallData.sshKeys - Array of SSH key secretIds (optional)
   * @param {string} reinstallData.userData - Cloud-Init config (optional)
   * @returns {Promise<Object>} Operation result
   */
  async reinstallVPS(instanceId, reinstallData) {
    try {
      console.log(`🔄 Reinstalling VPS instance: ${instanceId}`, {
        ...reinstallData,
        rootPassword: reinstallData.rootPassword ? '***HIDDEN***' : undefined
      });

      // Validate required parameters - either imageId or customImageId must be provided
      if (!reinstallData.imageId && !reinstallData.customImageId) {
        throw new Error('Either imageId or customImageId is required for VPS reinstallation');
      }

      // Prepare the request payload according to Contabo API specification
      const payload = {};

      // Use custom image if provided, otherwise use standard image
      if (reinstallData.customImageId) {
        payload.imageId = reinstallData.customImageId;
        console.log(`🖼️ Using custom image: ${reinstallData.customImageId}`);
      } else {
        payload.imageId = reinstallData.imageId;
        console.log(`🖼️ Using standard image: ${reinstallData.imageId}`);
      }

      // Handle root password - create secret if plain text password provided
      if (reinstallData.rootPassword) {
        let passwordSecretId;

        // Check if rootPassword is already a secretId (number) or plain text (string)
        if (typeof reinstallData.rootPassword === 'number') {
          // Already a secretId
          passwordSecretId = reinstallData.rootPassword;
          console.log(`🔑 Using existing password secretId: ${passwordSecretId}`);
        } else if (typeof reinstallData.rootPassword === 'string' && /^\d+$/.test(reinstallData.rootPassword)) {
          // String that looks like a number (secretId)
          passwordSecretId = parseInt(reinstallData.rootPassword, 10);
          console.log(`🔑 Using password secretId from string: ${passwordSecretId}`);
        } else {
          // Plain text password - create a secret
          console.log(`🔐 Creating password secret for reinstall`);
          passwordSecretId = await this.createPasswordSecret(
            reinstallData.rootPassword,
            `VPS-${instanceId}-Reinstall-${Date.now()}`
          );
        }

        payload.rootPassword = passwordSecretId;
        console.log(`🔑 Set rootPassword secretId: ${passwordSecretId}`);
      }

      // Handle SSH keys - register them first if they are raw keys
      if (reinstallData.sshKeys && Array.isArray(reinstallData.sshKeys) && reinstallData.sshKeys.length > 0) {
        const sshKeyIds = [];

        for (const sshKey of reinstallData.sshKeys) {
          if (typeof sshKey === 'string' && sshKey.trim().length > 0) {
            // This is a raw SSH key, register it first
            try {
              const keyId = await this.registerSSHKey(sshKey.trim(), `Reinstall-Key-${Date.now()}`);
              sshKeyIds.push(keyId);
            } catch (error) {
              console.warn(`⚠️ Failed to register SSH key, skipping: ${error.message}`);
            }
          } else if (typeof sshKey === 'number') {
            // This is already an SSH key ID
            sshKeyIds.push(sshKey);
          }
        }

        if (sshKeyIds.length > 0) {
          payload.sshKeys = sshKeyIds;
          console.log(`🔑 Using SSH key IDs: ${sshKeyIds.join(', ')}`);
        }
      }

      // Add optional userData if provided
      if (reinstallData.userData && reinstallData.userData.trim()) {
        payload.userData = reinstallData.userData.trim();
      }

      if (reinstallData.applicationId) {
        payload.applicationId = reinstallData.applicationId;
      }

      // Advanced installation options
      if (reinstallData.enableRootUser !== undefined) {
        payload.enableRootUser = reinstallData.enableRootUser;
      }

      if (reinstallData.defaultUser) {
        payload.defaultUser = reinstallData.defaultUser;
      }

      if (reinstallData.cloudInitTemplate) {
        payload.cloudInitTemplate = reinstallData.cloudInitTemplate;
      }

      if (reinstallData.customScript) {
        payload.customScript = reinstallData.customScript;
      }

      console.log(`📤 Sending reinstall request with payload:`, {
        ...payload,
        rootPassword: payload.rootPassword ? `***SECRET_ID_${payload.rootPassword}***` : undefined
      });

      const response = await this.makeRequest(
        "PUT",
        `/compute/instances/${instanceId}`,
        payload
      );

      console.log(`✅ VPS ${instanceId} reinstallation started successfully`);

      const result = {
        success: true,
        action: "reinstall",
        instanceId,
        message: "VPS reinstallation started successfully",
        data: response,
      };

      // Include passwordSecretId if a password was set
      if (payload.rootPassword) {
        result.passwordSecretId = payload.rootPassword;
      }

      return result;
    } catch (error) {
      console.error(`❌ Failed to reinstall VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to reinstall VPS: ${error.message}`);
    }
  }

  /**
   * Get available images for VPS installation
   * @returns {Promise<Object>} Operation result
   */
  async getAvailableImages() {
    try {
      console.log(`📋 Getting available images from Contabo`);

      const response = await this.makeRequest("GET", "/compute/images");

      console.log(`✅ Retrieved ${response.data?.length || 0} available images`);
      return {
        success: true,
        message: "Available images retrieved successfully",
        data: response.data || [],
      };
    } catch (error) {
      console.error(`❌ Failed to get available images:`, error.message);
      throw new Error(`Failed to get available images: ${error.message}`);
    }
  }

  /**
   * Get available applications for VPS installation
   * @returns {Promise<Object>} Operation result
   */
  async getAvailableApplications() {
    try {
      console.log(`📱 Getting available applications from Contabo`);

      const response = await this.makeRequest("GET", "/compute/applications");

      console.log(`✅ Retrieved ${response.data?.length || 0} available applications`);
      return {
        success: true,
        message: "Available applications retrieved successfully",
        data: response.data || [],
      };
    } catch (error) {
      console.error(`❌ Failed to get available applications:`, error.message);
      throw new Error(`Failed to get available applications: ${error.message}`);
    }
  }





  /**
   * Start rescue system for a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} rescueOptions - Rescue options (rootPassword, sshKeys, userData)
   * @returns {Promise<Object>} Operation result
   */
  async startRescueSystem(instanceId, rescueOptions = {}) {
    try {
      console.log(`🛡️ Starting rescue system for VPS instance: ${instanceId}`);
      console.log(
        `🔍 Instance ID type: ${typeof instanceId}, value: "${instanceId}"`
      );
      console.log(`🔍 Instance ID length: ${String(instanceId).length}`);

      // Validate instance ID format for Contabo
      const idString = String(instanceId).trim();
      if (!/^\d+$/.test(idString)) {
        throw new Error(
          `Invalid Contabo instance ID format: "${instanceId}". Must be numeric.`
        );
      }

      // Contabo API requires at least one of: rootPassword, sshKeys, or userData
      // Based on error message, rootPassword must be a number, which is unusual
      // Let's try different approaches based on the API documentation
      const requestBody = {};

      if (rescueOptions.rootPassword) {
        // Try to convert password to number if it's numeric
        if (/^\d+$/.test(rescueOptions.rootPassword)) {
          requestBody.rootPassword = parseInt(rescueOptions.rootPassword, 10);
        } else {
          requestBody.rootPassword = rescueOptions.rootPassword;
        }
      }

      if (
        rescueOptions.sshKeys &&
        Array.isArray(rescueOptions.sshKeys) &&
        rescueOptions.sshKeys.length > 0
      ) {
        requestBody.sshKeys = rescueOptions.sshKeys;
      }

      if (rescueOptions.userData) {
        requestBody.userData = rescueOptions.userData;
      }

      // If no options provided, use sshKeys array
      // since the rootPassword field has unusual number requirements
      if (
        !requestBody.rootPassword &&
        !requestBody.sshKeys &&
        !requestBody.userData
      ) {
        // Use empty sshKeys array as the simplest option
        requestBody.sshKeys = [];
        console.log(`🔑 Using empty sshKeys array for rescue system`);
      }

      console.log(
        `📤 Sending rescue request body:`,
        JSON.stringify(requestBody, null, 2)
      );

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/rescue`,
        requestBody
      );

      console.log(`✅ VPS ${instanceId} rescue system started successfully`);
      return {
        success: true,
        action: "rescue",
        instanceId,
        message: "Rescue system started successfully",
        rootPassword: requestBody.rootPassword, // Return the password if generated
        data: response,
      };
    } catch (error) {
      console.error(
        `❌ Failed to start rescue system for VPS ${instanceId}:`,
        error.message
      );
      throw new Error(`Failed to start rescue system: ${error.message}`);
    }
  }

  /**
   * Generate secure password for rescue system
   * @returns {string} Generated password
   */
  generateSecurePassword() {
    // Contabo requirements: alphanumeric only, 8-30 characters
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";

    // Generate 16 character password (within 8-30 range)
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return password;
  }

  /**
   * Get VPS snapshots with quota information
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Snapshots data with quota info
   */
  async getVPSSnapshots(instanceId) {
    try {
      console.log(`📸 Getting snapshots for VPS instance: ${instanceId}`);

      // Get snapshots
      const snapshotsResponse = await this.makeRequest(
        "GET",
        `/compute/instances/${instanceId}/snapshots`
      );

      // Get VPS details to check snapshot quota
      const vpsResponse = await this.makeRequest(
        "GET",
        `/compute/instances/${instanceId}`
      );

      const snapshots = snapshotsResponse.data || [];
      const vpsDetails = vpsResponse.data || {};

      // Extract quota information (Contabo usually provides this in VPS details)
      const maxSnapshots =
        vpsDetails.maxSnapshots || vpsDetails.snapshotQuota || 1; // Default to 1 if not specified
      const currentCount = snapshots.length;

      console.log(
        `✅ Found ${currentCount}/${maxSnapshots} snapshots for instance ${instanceId}`
      );

      // Transform snapshots to standardized format
      const transformedSnapshots = snapshots.map((snapshot) => ({
        snapshotId: snapshot.snapshotId,
        name: snapshot.name,
        description: snapshot.description,
        createdDate: snapshot.createdDate,
        autoDeleteDate: snapshot.autoDeleteDate,
        imageId: snapshot.imageId,
        imageSize: snapshot.imageSize,
        status: snapshot.status,
        provider: "contabo",
        raw: snapshot,
      }));

      return {
        snapshots: transformedSnapshots,
        quota: {
          max: maxSnapshots,
          current: currentCount,
          remaining: maxSnapshots - currentCount,
          canCreate: currentCount < maxSnapshots,
        },
      };
    } catch (error) {
      console.error(
        `❌ Failed to get VPS snapshots for ${instanceId}:`,
        error.message
      );
      throw new Error(`Failed to get VPS snapshots: ${error.message}`);
    }
  }

  /**
   * Rename VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @param {string} name - New snapshot name
   * @param {string} description - New snapshot description
   * @returns {Promise<Object>} Updated snapshot
   */
  async renameSnapshot(instanceId, snapshotId, name, description) {
    try {
      console.log(
        `✏️ Renaming snapshot ${snapshotId} for VPS instance: ${instanceId}`
      );

      const updateData = {};
      if (name) updateData.name = name;
      if (description) updateData.description = description;

      const response = await this.makeRequest(
        "PATCH",
        `/compute/instances/${instanceId}/snapshots/${snapshotId}`,
        updateData
      );

      console.log(`✅ Snapshot ${snapshotId} renamed successfully`);
      return {
        success: true,
        action: "rename",
        instanceId,
        snapshotId,
        message: "Snapshot renamed successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `❌ Failed to rename snapshot ${snapshotId}:`,
        error.message
      );
      throw new Error(`Failed to rename snapshot: ${error.message}`);
    }
  }

  /**
   * Rollback VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @returns {Promise<Object>} Rollback result
   */
  async rollbackSnapshot(instanceId, snapshotId) {
    try {
      console.log(
        `🔄 Rolling back snapshot ${snapshotId} for VPS instance: ${instanceId}`
      );

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/snapshots/${snapshotId}/rollback`,
        {}
      );

      console.log(`✅ Snapshot ${snapshotId} rollback initiated successfully`);
      return {
        success: true,
        action: "rollback",
        instanceId,
        snapshotId,
        message: "Snapshot rollback initiated successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `❌ Failed to rollback snapshot ${snapshotId}:`,
        error.message
      );
      throw new Error(`Failed to rollback snapshot: ${error.message}`);
    }
  }

  /**
   * Delete VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteSnapshot(instanceId, snapshotId) {
    try {
      console.log(
        `🗑️ Deleting snapshot ${snapshotId} for VPS instance: ${instanceId}`
      );

      const response = await this.makeRequest(
        "DELETE",
        `/compute/instances/${instanceId}/snapshots/${snapshotId}`,
        {}
      );

      console.log(`✅ Snapshot ${snapshotId} deleted successfully`);
      return {
        success: true,
        action: "delete",
        instanceId,
        snapshotId,
        message: "Snapshot deleted successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `❌ Failed to delete snapshot ${snapshotId}:`,
        error.message
      );
      throw new Error(`Failed to delete snapshot: ${error.message}`);
    }
  }

  /**
   * Cancel a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} cancellationData - Cancellation details
   * @param {string} cancellationData.reason - Reason for cancellation
   * @param {string} cancellationData.feedback - Additional feedback (optional)
   * @param {string} cancellationData.targetDate - Target cancellation date (optional)
   * @returns {Promise<Object>} Operation result
   */
  async cancelInstance(instanceId, cancellationData = {}) {
    try {
      console.log(`🚫 Cancelling VPS instance: ${instanceId}`);
      console.log(`📝 Cancellation details:`, cancellationData);

      // Prepare the request body according to Contabo API specification
      const requestBody = {
        reason: cancellationData.reason || "User requested cancellation",
        feedback: cancellationData.feedback || "",
        targetDate: cancellationData.targetDate || null,
      };

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/cancel`,
        requestBody
      );

      console.log(
        `✅ VPS ${instanceId} cancellation request submitted successfully`
      );
      return {
        success: true,
        action: "cancel",
        instanceId,
        message: "VPS cancellation request submitted successfully",
        data: response,
        cancellationDetails: cancellationData,
      };
    } catch (error) {
      console.error(`❌ Failed to cancel VPS ${instanceId}:`, error.message);
      throw new Error(`Failed to cancel VPS instance: ${error.message}`);
    }
  }

  /**
   * Get available rescue system images
   * @returns {Promise<Array>} Array of rescue images
   */
  async getRescueImages() {
    try {
      console.log(`🛡️ Fetching available rescue system images`);

      // Try to get images from Contabo API
      // Note: This might need to be adjusted based on actual Contabo API endpoints
      const response = await this.makeRequest("GET", "/compute/images");
      const images = response.data || [];

      // Filter for rescue images or return default options
      const rescueImages = images.filter(
        (image) =>
          image.name?.toLowerCase().includes("rescue") ||
          image.description?.toLowerCase().includes("rescue")
      );

      // If no rescue images found in API, return default options
      if (rescueImages.length === 0) {
        console.log(
          `🛡️ No rescue images found in API, returning default options`
        );
        return [
          {
            id: "debian-rescue",
            name: "Debian Rescue (recommended)",
            description:
              "Debian-based rescue system with common recovery tools",
          },
          {
            id: "ubuntu-rescue",
            name: "Ubuntu Rescue",
            description: "Ubuntu-based rescue system",
          },
          {
            id: "centos-rescue",
            name: "CentOS Rescue",
            description: "CentOS-based rescue system",
          },
        ];
      }

      console.log(`✅ Found ${rescueImages.length} rescue system images`);
      return rescueImages.map((image) => ({
        id: image.imageId || image.id,
        name: image.name || image.displayName,
        description: image.description || `${image.name} rescue system`,
      }));
    } catch (error) {
      console.error(`❌ Failed to fetch rescue images:`, error.message);

      // Return default options on error
      return [
        {
          id: "debian-rescue",
          name: "Debian Rescue (recommended)",
          description: "Debian-based rescue system with common recovery tools",
        },
        {
          id: "ubuntu-rescue",
          name: "Ubuntu Rescue",
          description: "Ubuntu-based rescue system",
        },
      ];
    }
  }
}

module.exports = ContaboProvider;
