"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-oauth";
exports.ids = ["vendor-chunks/@react-oauth"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-oauth/google/dist/index.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/@react-oauth/google/dist/index.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleLogin: () => (/* binding */ GoogleLogin),\n/* harmony export */   GoogleOAuthProvider: () => (/* binding */ GoogleOAuthProvider),\n/* harmony export */   googleLogout: () => (/* binding */ googleLogout),\n/* harmony export */   hasGrantedAllScopesGoogle: () => (/* binding */ hasGrantedAllScopesGoogle),\n/* harmony export */   hasGrantedAnyScopeGoogle: () => (/* binding */ hasGrantedAnyScopeGoogle),\n/* harmony export */   useGoogleLogin: () => (/* binding */ useGoogleLogin),\n/* harmony export */   useGoogleOAuth: () => (/* binding */ useGoogleOAuth),\n/* harmony export */   useGoogleOneTapLogin: () => (/* binding */ useGoogleOneTapLogin)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ GoogleLogin,GoogleOAuthProvider,googleLogout,hasGrantedAllScopesGoogle,hasGrantedAnyScopeGoogle,useGoogleLogin,useGoogleOAuth,useGoogleOneTapLogin auto */ \nfunction useLoadGsiScript(options = {}) {\n    const { nonce, onScriptLoadSuccess, onScriptLoadError } = options;\n    const [scriptLoadedSuccessfully, setScriptLoadedSuccessfully] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const onScriptLoadSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onScriptLoadSuccess);\n    onScriptLoadSuccessRef.current = onScriptLoadSuccess;\n    const onScriptLoadErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onScriptLoadError);\n    onScriptLoadErrorRef.current = onScriptLoadError;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const scriptTag = document.createElement(\"script\");\n        scriptTag.src = \"https://accounts.google.com/gsi/client\";\n        scriptTag.async = true;\n        scriptTag.defer = true;\n        scriptTag.nonce = nonce;\n        scriptTag.onload = ()=>{\n            var _a;\n            setScriptLoadedSuccessfully(true);\n            (_a = onScriptLoadSuccessRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadSuccessRef);\n        };\n        scriptTag.onerror = ()=>{\n            var _a;\n            setScriptLoadedSuccessfully(false);\n            (_a = onScriptLoadErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadErrorRef);\n        };\n        document.body.appendChild(scriptTag);\n        return ()=>{\n            document.body.removeChild(scriptTag);\n        };\n    }, [\n        nonce\n    ]);\n    return scriptLoadedSuccessfully;\n}\nconst GoogleOAuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nfunction GoogleOAuthProvider({ clientId, nonce, onScriptLoadSuccess, onScriptLoadError, children }) {\n    const scriptLoadedSuccessfully = useLoadGsiScript({\n        nonce,\n        onScriptLoadSuccess,\n        onScriptLoadError\n    });\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            clientId,\n            scriptLoadedSuccessfully\n        }), [\n        clientId,\n        scriptLoadedSuccessfully\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(GoogleOAuthContext.Provider, {\n        value: contextValue\n    }, children);\n}\nfunction useGoogleOAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(GoogleOAuthContext);\n    if (!context) {\n        throw new Error(\"Google OAuth components must be used within GoogleOAuthProvider\");\n    }\n    return context;\n}\nfunction extractClientId(credentialResponse) {\n    var _a;\n    const clientId = (_a = credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.clientId) !== null && _a !== void 0 ? _a : credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.client_id;\n    return clientId;\n}\nconst containerHeightMap = {\n    large: 40,\n    medium: 32,\n    small: 20\n};\nfunction GoogleLogin({ onSuccess, onError, useOneTap, promptMomentNotification, type = \"standard\", theme = \"outline\", size = \"large\", text, shape, logo_alignment, width, locale, click_listener, containerProps, ...props }) {\n    const btnContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\n    const onSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onSuccess);\n    onSuccessRef.current = onSuccess;\n    const onErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onError);\n    onErrorRef.current = onError;\n    const promptMomentNotificationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(promptMomentNotification);\n    promptMomentNotificationRef.current = promptMomentNotification;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n        if (!scriptLoadedSuccessfully) return;\n        (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.initialize({\n            client_id: clientId,\n            callback: (credentialResponse)=>{\n                var _a;\n                if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\n                }\n                const { credential, select_by } = credentialResponse;\n                onSuccessRef.current({\n                    credential,\n                    clientId: extractClientId(credentialResponse),\n                    select_by\n                });\n            },\n            ...props\n        });\n        (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.renderButton(btnContainerRef.current, {\n            type,\n            theme,\n            size,\n            text,\n            shape,\n            logo_alignment,\n            width,\n            locale,\n            click_listener\n        });\n        if (useOneTap) (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\n        return ()=>{\n            var _a, _b, _c;\n            if (useOneTap) (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        clientId,\n        scriptLoadedSuccessfully,\n        useOneTap,\n        type,\n        theme,\n        size,\n        text,\n        shape,\n        logo_alignment,\n        width,\n        locale\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n        ...containerProps,\n        ref: btnContainerRef,\n        style: {\n            height: containerHeightMap[size],\n            ...containerProps === null || containerProps === void 0 ? void 0 : containerProps.style\n        }\n    });\n}\nfunction googleLogout() {\n    var _a, _b, _c;\n    (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.disableAutoSelect();\n}\n/* eslint-disable import/export */ function useGoogleLogin({ flow = \"implicit\", scope = \"\", onSuccess, onError, onNonOAuthError, overrideScope, state, ...props }) {\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\n    const clientRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const onSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onSuccess);\n    onSuccessRef.current = onSuccess;\n    const onErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onError);\n    onErrorRef.current = onError;\n    const onNonOAuthErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onNonOAuthError);\n    onNonOAuthErrorRef.current = onNonOAuthError;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a, _b;\n        if (!scriptLoadedSuccessfully) return;\n        const clientMethod = flow === \"implicit\" ? \"initTokenClient\" : \"initCodeClient\";\n        const client = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2[clientMethod]({\n            client_id: clientId,\n            scope: overrideScope ? scope : `openid profile email ${scope}`,\n            callback: (response)=>{\n                var _a, _b;\n                if (response.error) return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, response);\n                (_b = onSuccessRef.current) === null || _b === void 0 ? void 0 : _b.call(onSuccessRef, response);\n            },\n            error_callback: (nonOAuthError)=>{\n                var _a;\n                (_a = onNonOAuthErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onNonOAuthErrorRef, nonOAuthError);\n            },\n            state,\n            ...props\n        });\n        clientRef.current = client;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        clientId,\n        scriptLoadedSuccessfully,\n        flow,\n        scope,\n        state\n    ]);\n    const loginImplicitFlow = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((overrideConfig)=>{\n        var _a;\n        return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestAccessToken(overrideConfig);\n    }, []);\n    const loginAuthCodeFlow = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        var _a;\n        return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestCode();\n    }, []);\n    return flow === \"implicit\" ? loginImplicitFlow : loginAuthCodeFlow;\n}\nfunction useGoogleOneTapLogin({ onSuccess, onError, promptMomentNotification, cancel_on_tap_outside, prompt_parent_id, state_cookie_domain, hosted_domain, use_fedcm_for_prompt = false, disabled, auto_select }) {\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\n    const onSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onSuccess);\n    onSuccessRef.current = onSuccess;\n    const onErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onError);\n    onErrorRef.current = onError;\n    const promptMomentNotificationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(promptMomentNotification);\n    promptMomentNotificationRef.current = promptMomentNotification;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n        if (!scriptLoadedSuccessfully) return;\n        if (disabled) {\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n            return;\n        }\n        (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.initialize({\n            client_id: clientId,\n            callback: (credentialResponse)=>{\n                var _a;\n                if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\n                    return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\n                }\n                const { credential, select_by } = credentialResponse;\n                onSuccessRef.current({\n                    credential,\n                    clientId: extractClientId(credentialResponse),\n                    select_by\n                });\n            },\n            hosted_domain,\n            cancel_on_tap_outside,\n            prompt_parent_id,\n            state_cookie_domain,\n            use_fedcm_for_prompt,\n            auto_select\n        });\n        (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\n        return ()=>{\n            var _a, _b, _c;\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n        };\n    }, [\n        clientId,\n        scriptLoadedSuccessfully,\n        cancel_on_tap_outside,\n        prompt_parent_id,\n        state_cookie_domain,\n        hosted_domain,\n        use_fedcm_for_prompt,\n        disabled,\n        auto_select\n    ]);\n}\n/**\r\n * Checks if the user granted all the specified scope or scopes\r\n * @returns True if all the scopes are granted\r\n */ function hasGrantedAllScopesGoogle(tokenResponse, firstScope, ...restScopes) {\n    var _a, _b, _c;\n    if (!(window === null || window === void 0 ? void 0 : window.google)) return false;\n    return ((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAllScopes(tokenResponse, firstScope, ...restScopes)) || false;\n}\n/**\r\n * Checks if the user granted any of the specified scope or scopes.\r\n * @returns True if any of the scopes are granted\r\n */ function hasGrantedAnyScopeGoogle(tokenResponse, firstScope, ...restScopes) {\n    var _a, _b, _c;\n    if (!(window === null || window === void 0 ? void 0 : window.google)) return false;\n    return ((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAnyScope(tokenResponse, firstScope, ...restScopes)) || false;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-oauth/google/dist/index.esm.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@react-oauth/google/dist/index.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/@react-oauth/google/dist/index.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GoogleLogin: () => (/* binding */ e0),
/* harmony export */   GoogleOAuthProvider: () => (/* binding */ e1),
/* harmony export */   googleLogout: () => (/* binding */ e2),
/* harmony export */   hasGrantedAllScopesGoogle: () => (/* binding */ e3),
/* harmony export */   hasGrantedAnyScopeGoogle: () => (/* binding */ e4),
/* harmony export */   useGoogleLogin: () => (/* binding */ e5),
/* harmony export */   useGoogleOAuth: () => (/* binding */ e6),
/* harmony export */   useGoogleOneTapLogin: () => (/* binding */ e7)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#GoogleLogin`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#GoogleOAuthProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#googleLogout`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#hasGrantedAllScopesGoogle`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#hasGrantedAnyScopeGoogle`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#useGoogleLogin`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#useGoogleOAuth`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\stage PFE\ztech_dev\frontend\node_modules\@react-oauth\google\dist\index.esm.js#useGoogleOneTapLogin`);


/***/ })

};
;