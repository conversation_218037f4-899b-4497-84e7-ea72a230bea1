const express = require("express");
// const { requireSignIn, authUserIsVerified } = require('../midelwares/authorization');
const {
  initiatePayment,
  handleCallback,
  handleFailure,
  handleSuccess,
  handleCancel,
  getTransaction,
  capturePayment,
  cancelPayment,
  refundPayment,
} = require("../controllers/payzoneController");
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");

const payzoneRouter = express.Router();
// Route to handle form submission and initiate CMI payment
payzoneRouter.post("/", asyncBackFrontEndLang, initiatePayment);
// payzoneRouter.get("/test", asyncBackFrontEndLang, testHealthcheck);
payzoneRouter.post("/callback", handleCallback);
payzoneRouter.get("/success", asyncBackFrontEndLang, handleSuccess);
payzoneRouter.get("/failed", asyncBackFrontEndLang, handleFailure);
payzoneRouter.get("/cancel", asyncBackFrontEndLang, handleCancel);

payzoneRouter.get("/transaction/:id", getTransaction);
payzoneRouter.post("/capture/:id", capturePayment);
payzoneRouter.post("/cancel/:id", cancelPayment);
payzoneRouter.post("/refund/:id", refundPayment);
module.exports = payzoneRouter;
