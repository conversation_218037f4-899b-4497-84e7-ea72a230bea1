import React from "react";
import { Receipt, CreditCard, Lock } from "lucide-react";

const TAX_RATE = 0.2; // 10% tax rate

function Summary({ totalPrice, totalDiscount, onPlaceOrder, orderLoading, t }) {
  const tax = totalPrice * TAX_RATE;
  const total = totalPrice + tax;
  const originalPrice = totalPrice + totalDiscount;

  return (
    <div className="bg-white w-full rounded-xl shadow-sm border border-gray-200 p-6 ">
      <div className="flex items-center space-x-2 mb-6">
        <Receipt className="h-5 w-5 text-gray-500" />
        <h2 className="text-lg font-medium text-gray-900">{t("summary")}</h2>
      </div>

      <div className="space-y-4">
        <div className="relative flex justify-between text-sm">
          <span className="text-gray-600">{t("subtotal")}</span>
          <span className="font-medium">
            {" "}
            <span className="text-[12px]">MAD</span>{" "}
            {(isNaN(totalPrice) ? 0 : totalPrice).toFixed(2)}{" "}
            <small className="font-medium">(HT)</small>{" "}
          </span>
          {totalDiscount > 0 && (
            <small className="absolute right-[38px] -top-[14px] font-medium pl-0 text-gray-800">
              {" "}
              <del>
                {" "}
                <span className="text-[12px]">MAD</span>{" "}
                {(isNaN(originalPrice) ? 0 : originalPrice).toFixed(2)}{" "}
              </del>{" "}
            </small>
          )}
        </div>

        {totalDiscount > 0 && (
          <div className="flex justify-between text-sm">
            <div>
              <span className="text-gray-600">{t("discount")}</span>
              <span className="text-gray-600">
                {" "}
                (-
                {(isNaN((totalDiscount / originalPrice) * 100)
                  ? 0
                  : (totalDiscount / originalPrice) * 100
                ).toFixed(2)}
                %)
              </span>
            </div>
            <span className="font-medium text-green-500 ">
              {" "}
              - <span className="text-[12px]">MAD</span>{" "}
              {(isNaN(totalDiscount) ? 0 : totalDiscount).toFixed(2)}{" "}
            </span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <span className="text-gray-600">{t("tax")} (20%)</span>
          <span className="font-medium">
            {" "}
            <span className="text-[12px]">MAD</span>{" "}
            {(isNaN(tax) ? 0 : tax).toFixed(2)}{" "}
          </span>
        </div>

        <div className="border-t border-gray-200 pt-4 mt-4">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-900">{t("total")}</span>
            <span className="text-lg font-bold text-gray-900">
              <small>MAD</small> {(isNaN(total) ? 0 : total).toFixed(2)}{" "}
              <small className="font-medium">(TTC)</small>{" "}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">{t("tax_included")}</p>
        </div>
      </div>

      <button
        onClick={onPlaceOrder}
        disabled={orderLoading || totalPrice <= 0 || isNaN(total.toFixed(2))}
        className={`w-full ${
          orderLoading || totalPrice <= 0 || isNaN(total.toFixed(2))
            ? "bg-gray-400 cursor-not-allowed "
            : "bg-blue-600 hover:bg-blue-700 cursor-pointer"
        }  text-white py-3 px-4 rounded-lg  flex items-center justify-center transition-colors mt-6`}
      >
        {orderLoading ? (
          <span>{t("loading")}</span>
        ) : (
          <>
            <CreditCard className="h-5 w-5 mr-2" />
            {t("place_order")}
          </>
        )}
      </button>
      <div className="mt-8 flex flex-col items-center  justify-center">
        {/* <img
          src="/images/logo-cmi-visa-mastercard-maroc-lcdmaroc.png"
          alt="CMI, Visa, Mastercard Logo"
          className="h-6 w-fit"
        /> */}
        <img
          className=" rounded-lg"
          src="/images/payzone/Moyen-de-sécurité-ABB3.png"
          alt="Moyen-de-sécurité"
        />
        <p className="mt-4 text-sm text-gray-600 flex justify-center items-center gap-2">
          <Lock size={30} className="text-green-800 h-fit" />
          {t("payment_info")}
        </p>
      </div>
    </div>
  );
}

export default Summary;
