"use client"

import { useState, useEffect, useRef } from "react";
import { Bell, Check, Clock, Info, AlertCircle, Loader2, Edit, Trash, ShoppingBag, Shield, Star, Server, Code } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useAuth } from "../../app/context/AuthContext";
import io from 'socket.io-client';
import { formatDistanceToNow } from 'date-fns';
import userService from "../../app/services/userService";
import { isProd, BACKEND_URL } from "../../app/config/constant";

const UserNotifications = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [socketStatus, setSocketStatus] = useState({
    connected: false,
    error: null
  });
  const [isMounted, setIsMounted] = useState(false);

  const notificationRef = useRef(null);
  const socket = useRef(null);
  const router = useRouter();
  const { user } = useAuth();

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Set mounted state
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Connect to socket when component mounts and user is available
  useEffect(() => {
    if (!isMounted || !user || !user._id) return;

    // Use the BACKEND_URL from our constants file for socket connection
    // This ensures we connect to the correct backend in both dev and prod environments
    const socketUrl = BACKEND_URL;
    console.log(`[SOCKET DEBUG-FRONTEND] Socket URL: ${socketUrl}`);

    // Clean up previous socket if it exists
    if (socket.current) {
      console.log('[SOCKET DEBUG-FRONTEND] Cleaning up previous socket connection');
      socket.current.disconnect();
      socket.current = null;
    }

    // Create new socket connection with explicit options
    socket.current = io(socketUrl, {
      query: { userId: user._id }, // Pass userId for specific room
      transports: ['websocket', 'polling'], // Try both transports
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 30000, // Increase timeout
      withCredentials: true,
      forceNew: true, // Force a new connection
      autoConnect: true, // Ensure auto-connect is enabled
      debug: !isProd // Enable debug mode in development
    });

    // Log connection attempt
    console.log(`[SOCKET DEBUG-FRONTEND] Attempting to connect to ${socketUrl} with transports:`, ['websocket', 'polling']);

    socket.current.on('connect', () => {
      console.log('[SOCKET DEBUG-FRONTEND] Socket connected for user notifications');
      console.log('[SOCKET DEBUG-FRONTEND] Socket ID:', socket.current.id);
      console.log('[SOCKET DEBUG-FRONTEND] Connected with user ID:', user._id);

      // Update socket status
      setSocketStatus(prev => ({ ...prev, connected: true, error: null }));

      // Add a small delay before joining the user room (ensures connection is fully established)
      setTimeout(() => {
        // Join user room
        socket.current.emit('join-user');
        console.log('[SOCKET DEBUG-FRONTEND] Joining user room for user ID:', user._id);
      }, 500);
    });

    socket.current.on('user-room-joined', (response) => {
      console.log('[SOCKET DEBUG-FRONTEND] User room join response:', response);
      if (response.success) {
        console.log('[SOCKET DEBUG-FRONTEND] Successfully joined user room');
      } else {
        console.error('[SOCKET DEBUG-FRONTEND] Failed to join user room:', response.error);
      }
    });

    socket.current.on('disconnect', (reason) => {
      console.log('[SOCKET DEBUG-FRONTEND] Socket disconnected:', reason);
      setSocketStatus(prev => ({ ...prev, connected: false }));
    });

    socket.current.on('connect_error', (error) => {
      console.error('[SOCKET DEBUG-FRONTEND] Socket connection error:', error);
      setSocketStatus(prev => ({ ...prev, error: error.message }));

      // Attempt to reconnect after a delay
      setTimeout(() => {
        console.log('[SOCKET DEBUG-FRONTEND] Attempting to reconnect...');
        if (socket.current) {
          socket.current.connect();
        }
      }, 5000); // Try to reconnect after 5 seconds
    });

    // Listen for 'notification' event from the backend
    socket.current.on('notification', (newNotification) => {
      console.log('🔔 [SOCKET DEBUG-FRONTEND] New notification received via socket:');
      console.log('📋 [SOCKET DEBUG-FRONTEND] Notification type:', newNotification.type);
      console.log('📄 [SOCKET DEBUG-FRONTEND] Notification title:', newNotification.title);
      console.log('💬 [SOCKET DEBUG-FRONTEND] Notification message:', newNotification.message);
      console.log('🆔 [SOCKET DEBUG-FRONTEND] Notification ID:', newNotification._id);

      // Highlight SSL notifications specifically
      if (newNotification.type === 'ssl_update' || newNotification.type === 'ssl_expiry') {
        console.log('🔐 [SSL NOTIFICATION] This is an SSL-related notification!');
        console.log('🔐 [SSL NOTIFICATION] Should route to: /client/ssl-certificates');
      }

      if (!isProd) {
        console.log('📊 [SOCKET DEBUG-FRONTEND] Full notification data:', newNotification);
      }

      // Add the new notification to the state
      setNotifications(prev => [newNotification, ...prev]);

      // Update unread count
      if (!newNotification.isRead) {
        setUnreadCount(prev => prev + 1);
      }
    });

    // Clean up socket connection when component unmounts
    return () => {
      if (socket.current) {
        console.log('[SOCKET DEBUG-FRONTEND] Disconnecting socket on cleanup');
        socket.current.disconnect();
        socket.current = null;
      }
    };
  }, [isMounted, user]);

  // Fetch initial notifications and unread count on mount
  useEffect(() => {
    if (!isMounted || !user || !user._id) return;

    console.log('[SOCKET DEBUG-FRONTEND] Fetching initial user notifications on mount');

    const fetchInitialNotifs = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await userService.getUserNotifications({ page: 1, limit: 20, unreadOnly: false });
        console.log('[SOCKET DEBUG-FRONTEND] Initial user notifications fetched:', response.data.notifications?.length || 0);
        setNotifications(response.data.notifications || []);
        setUnreadCount(response.data.unreadCount || 0);
      } catch (err) {
        console.error("[SOCKET DEBUG-FRONTEND] Failed to fetch initial user notifications:", err);
        setError("Failed to load notifications.");
        setNotifications([]); // Clear notifications on error
        setUnreadCount(0);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialNotifs();

  }, [isMounted, user]); // Dependencies: runs on mount and when user changes

  // Fetch notifications when dropdown is opened (optional, could be removed if initial fetch is sufficient)
  // Keeping this for now to potentially refresh when opened, but the main fetch is on mount.
  useEffect(() => {
    if (isOpen && isMounted && user) {
      console.log('[SOCKET DEBUG-FRONTEND] Dropdown opened, notifications should already be loaded or will be updated via socket.');
      // The initial fetch on mount handles the initial load.
      // Socket updates handle real-time additions.
      // No need to refetch the entire list just because the dropdown opens.
      // If you need to mark all as read when opening, that logic would go here.
    }
  }, [isOpen, isMounted, user]);

  const toggleNotifications = () => {
    setIsOpen(!isOpen);
  };

  // Function to manually reconnect the socket
  const handleReconnect = () => {
    if (!socket.current) {
      return;
    }

    console.log('[SOCKET DEBUG-FRONTEND] Manual reconnection attempt');

    // First disconnect if already connected
    if (socket.current.connected) {
      socket.current.disconnect();
    }

    // Then try to reconnect
    socket.current.connect();

    // Update UI to show connecting state
    setSocketStatus(prev => ({ ...prev, connected: false, error: null }));
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      await userService.markNotificationAsRead(notificationId);

      // Update local state
      setNotifications(prev =>
        prev.map(n => n._id === notificationId ? { ...n, isRead: true } : n)
      );

      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await userService.markAllNotificationsAsRead();

      // Update local state
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error("Failed to mark all notifications as read:", error);
    }
  };

  const timeAgo = (date) => {
    try {
      return formatDistanceToNow(new Date(date), { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  const handleNotificationClick = (notification) => {
    console.log('🔔 Notification clicked!');
    console.log('📋 Notification type:', notification.type);
    console.log('📄 Full notification object:', notification);

    if (!notification.isRead) {
      handleMarkAsRead(notification._id);
    }

    // Redirect based on notification type with flexible routing
    const notificationRoutes = {
      'ssl_expiry': '/client/ssl-certificates',
      'ssl_update': '/client/ssl-certificates',
      'hosting_update': '/client/hosting-plans',
      'webdev_update': '/client/web-development',
      'abandoned_cart': '/client/cart'
    };

    console.log('🗺️ Routing decision:');
    console.log('   Available routes:', notificationRoutes);
    console.log('   Target route for type "' + notification.type + '":', notificationRoutes[notification.type]);
    console.log('   Fallback link:', notification.link);

    // Check if we have a specific route for this notification type
    if (notificationRoutes[notification.type]) {
      console.log('✅ Using specific route:', notificationRoutes[notification.type]);
      router.push(notificationRoutes[notification.type]);
    } else if (notification.link) {
      console.log('⚠️ Using fallback link:', notification.link);
      // Fallback to notification link if provided
      router.push(notification.link);
    } else {
      console.log('🔄 No redirection - staying on current page');
      // Default fallback - could redirect to orders page or stay on current page
      console.log('No specific redirection logic for notification type:', notification.type);
    }

    console.log('🎯 [NOTIFICATION CLICK] Summary:');
    console.log('   Type:', notification.type);
    console.log('   Expected route:', notificationRoutes[notification.type] || 'fallback');
    console.log('   Action taken:', notificationRoutes[notification.type] ? 'Specific route' : 'Fallback/None');
    console.log('---');

    setIsOpen(false); // Close dropdown after handling
  };

  if (!isMounted) {
    return (
      <div className="relative">
        <button className="relative p-2 rounded-full hover:bg-gray-100">
          <Bell className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    );
  }

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'ticket_status_update':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'ticket_updated':
        return <Edit className="w-4 h-4 text-purple-500" />;
      case 'ticket_deleted':
        return <Trash className="w-4 h-4 text-red-500" />;
      case 'order_update':
        return <ShoppingBag className="w-4 h-4 text-amber-500" />;
      case 'ssl_expiry':
      case 'ssl_update':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'hosting_update':
        return <Server className="w-4 h-4 text-green-500" />;
      case 'webdev_update':
        return <Code className="w-4 h-4 text-purple-500" />;
      case 'abandoned_cart':
        return <ShoppingBag className="w-4 h-4 text-orange-500" />;
      case 'custom_notification':
        return <Star className="w-4 h-4 text-purple-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  // Function to get enhanced notification message with context
  const getEnhancedMessage = (notification) => {
    // For order notifications, add more context
    if (notification.type === 'order_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              Updated by: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // For SSL notifications, add more context
    if (notification.type === 'ssl_expiry' || notification.type === 'ssl_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              Updated by: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // For hosting notifications, add more context
    if (notification.type === 'hosting_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              Updated by: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // For web development notifications, add more context
    if (notification.type === 'webdev_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              Updated by: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // Default display for other notification types
    return (
      <div>
        <p className="text-xs text-gray-600">{notification.message}</p>
        {notification.actionBy && notification.actionBy.firstName && (
          <p className="text-xs text-gray-500 mt-1">
            By: {notification.actionBy.firstName} {notification.actionBy.lastName}
            {notification.actionBy.role && ` (${notification.actionBy.role})`}
          </p>
        )}
      </div>
    );
  };

  return (
    <div className="relative" ref={notificationRef}>
      <button
        className="relative p-2 rounded-full hover:bg-gray-100 border border-gray-100"
        aria-label="Notifications"
        onClick={toggleNotifications}
        title={socketStatus.connected ? "Connected to notifications" : socketStatus.error ? `Connection error: ${socketStatus.error}` : "Connecting..."}
      >
        <Bell className="w-5 h-5 text-gray-600 " />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 flex size-2">
            <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
            <span className="relative inline-flex size-2 rounded-full bg-red-500"></span>
          </span>
        )}
        {/* Socket connection status indicator - hidden for production */}
        {/* <span className={`absolute bottom-0 right-0 w-2 h-2 rounded-full ${
          socketStatus.connected ? 'bg-green-500' : socketStatus.error ? 'bg-red-500' : 'bg-yellow-500 animate-pulse'
        }`}></span> */}
      </button>

      {/* Notification dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-custom-heavy border border-gray-200 z-50 overflow-hidden">
          <div className="p-3 border-b border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-gray-700">Notifications</h3>
              {unreadCount > 0 && (
                <span className="px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full">
                  {unreadCount} new
                </span>
              )}
            </div>
            {/* Socket connection status - hidden for production */}
            {/* Only show reconnect button when there's a connection error */}
            {socketStatus.error && (
              <div className="flex justify-end mt-2 text-xs">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleReconnect();
                  }}
                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                >
                  Reconnect
                </button>
              </div>
            )}
            {/* Show socket status only in development */}
            {!isProd && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="flex items-center gap-2 text-xs">
                  <span>Socket:</span>
                  {socketStatus.connected ? (
                    <span className="text-green-600 flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                      Connected
                    </span>
                  ) : (
                    <span className="text-red-600 flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                      Disconnected
                    </span>
                  )}
                  {socketStatus.error && (
                    <span className="text-red-500 ml-2">Error: {socketStatus.error}</span>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto hide-scrollbar">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="w-6 h-6 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="py-8 px-4 text-center text-red-500">
                <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                <p>{error}</p>
              </div>
            ) : notifications.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification) => (
                  <div
                    key={notification._id}
                    className={`p-3 hover:bg-gray-50 transition-colors cursor-pointer ${
                      !notification.isRead ? 'bg-blue-50/50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          !notification.isRead ? 'bg-primary/10' : 'bg-gray-100'
                        }`}>
                          {getNotificationIcon(notification.type)}
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start mb-1">
                          <h4 className={`text-sm font-medium truncate ${
                            !notification.isRead ? 'text-primary' : 'text-gray-800'
                          }`}>
                            {notification.title}
                          </h4>
                          <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                            {timeAgo(notification.createdAt)}
                          </span>
                        </div>
                        {getEnhancedMessage(notification)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-12 px-4 text-center text-gray-500">
                <Bell className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>No notifications yet</p>
              </div>
            )}
          </div>

          {notifications.length > 0 && (
            <div className="p-2 border-t border-gray-200">
              <button
                className="w-full py-2 text-xs font-medium text-center text-primary hover:bg-primary/5 rounded-md transition-colors"
                onClick={handleMarkAllAsRead}
              >
                Mark all as read
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UserNotifications;
