const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Package = require('../models/Package');
const Category = require('../models/Category');
const Brand = require('../models/Brand');
const Specification = require('../models/Specification');

// Database connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/zn_ztech';
    console.log('Connecting to MongoDB:', mongoURI);
    
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Create a single VPS package
const createVPSPackage = async () => {
  try {
    console.log('🚀 Starting VPS package creation...');

    // 1. Find or create VPS category
    let vpsCategory = await Category.findOne({ name: 'VPS' });
    if (!vpsCategory) {
      console.log('Creating VPS category...');
      vpsCategory = new Category({
        name: 'VPS',
        name_fr: 'VPS',
        description: 'Virtual Private Servers',
        description_fr: 'Serveurs Privés Virtuels'
      });
      await vpsCategory.save();
      console.log('✅ VPS category created');
    } else {
      console.log('✅ VPS category found');
    }

    // 2. Find or create VPS brand
    let vpsBrand = await Brand.findOne({ name: 'VPS Hosting' });
    if (!vpsBrand) {
      console.log('Creating VPS Hosting brand...');
      vpsBrand = new Brand({
        name: 'VPS Hosting',
        name_fr: 'Hébergement VPS',
        category: vpsCategory._id,
        packages: []
      });
      await vpsBrand.save();
      console.log('✅ VPS Hosting brand created');
    } else {
      console.log('✅ VPS Hosting brand found');
    }

    // 3. Check if package already exists
    const existingPackage = await Package.findOne({ 
      name: 'CLOUD VPS 10',
      brand: vpsBrand._id 
    });

    if (existingPackage) {
      console.log('⚠️ Package "CLOUD VPS 10" already exists');
      console.log('Package details:', {
        id: existingPackage._id,
        name: existingPackage.name,
        price: existingPackage.price,
        vpsConfig: existingPackage.vpsConfig
      });
      return existingPackage;
    }

    // 4. Create specifications
    console.log('Creating specifications...');
    const specifications = [
      '1 vCPU Core',
      '4 GB RAM', 
      '75 GB NVMe SSD',
      '32 TB Traffic',
      '99.9% Uptime',
      '24/7 Support'
    ];

    const specificationIds = [];
    for (const specText of specifications) {
      const spec = new Specification({
        value: specText,
        value_fr: specText
      });
      await spec.save();
      specificationIds.push(spec._id);
      console.log(`✅ Created specification: ${specText}`);
    }

    // 5. Create VPS package
    console.log('Creating VPS package...');
    const packageData = {
      reference: `VPS-${Date.now()}`,
      name: 'CLOUD VPS 10',
      name_fr: 'CLOUD VPS 10',
      description: '1 CPU, 4GB RAM, 75GB NVMe Storage',
      description_fr: '1 CPU, 4GB RAM, 75GB Stockage NVMe',
      price: 46,
      regularPrice: 52,
      category: vpsCategory._id,
      brand: vpsBrand._id,
      specifications: specificationIds,
      vpsConfig: {
        provider: 'contabo',
        providerProductId: 'V91',
        providerPlanName: 'VPS 10 NVMe'
      },
      status: 'PUBLISHED'
    };

    console.log('Package data:', packageData);

    const newPackage = new Package(packageData);
    const savedPackage = await newPackage.save();

    console.log('✅ Package saved with ID:', savedPackage._id);

    // 6. Update brand with package reference
    await Brand.findByIdAndUpdate(
      vpsBrand._id,
      { $push: { packages: savedPackage._id } }
    );

    console.log('✅ Brand updated with package reference');

    // 7. Verify package creation
    const verifyPackage = await Package.findById(savedPackage._id)
      .populate('category')
      .populate('brand')
      .populate('specifications');

    console.log('🎉 VPS Package created successfully!');
    console.log('Package details:', {
      id: verifyPackage._id,
      name: verifyPackage.name,
      price: verifyPackage.price,
      category: verifyPackage.category.name,
      brand: verifyPackage.brand.name,
      vpsConfig: verifyPackage.vpsConfig,
      specifications: verifyPackage.specifications.length
    });

    return verifyPackage;

  } catch (error) {
    console.error('❌ Error creating VPS package:', error);
    console.error('Error details:', error.message);
    throw error;
  }
};

// Run the script
const run = async () => {
  try {
    await connectDB();
    await createVPSPackage();
    console.log('\n✅ VPS package creation completed!');
  } catch (error) {
    console.error('\n❌ Script failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed.');
  }
};

// Execute if run directly
if (require.main === module) {
  run();
}

module.exports = { createVPSPackage };
