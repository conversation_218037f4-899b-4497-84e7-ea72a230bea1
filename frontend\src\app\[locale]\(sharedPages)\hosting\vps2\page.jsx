"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import Intro from "@/components/hosting/intro";
import HostingPricingTable from "@/components/hosting/hostingPricingTable";
import ContactForm2 from "@/components/shared/contactForm2";

function VPSHostingPage2() {
  const t = useTranslations("hosting");

  const [data, setData] = useState({
    page: "Hosting",
    offerName: "No offer selected",
    id: 0,
    url: "/hosting",
  });

  return (
    <div className="h-full mx-auto w-full">
      <Intro t={t} />
      <HostingPricingTable setData={setData} t={t} />
      <ContactForm2 data={data} setData={setData} t={t} />
    </div>
  );
}

export default VPSHostingPage2;
