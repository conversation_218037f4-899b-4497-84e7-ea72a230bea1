'use client';

import React, { useState } from 'react';
import { X, Upload, Info } from 'lucide-react';

export default function TestCustomImagePage() {
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState({
    customImageId: '', // ID of selected custom image
    customImageUrl: '', // URL for creating new custom image
    customImageName: '',
    customImageOsType: 'Linux',
    customImageVersion: '',
    customImageDescription: ''
  });

  const customImages = []; // Empty for testing

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">Test Custom Image Modal</h1>
        
        {/* Test the exact same structure as in ReinstallModal */}
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Image
              <Info className="w-4 h-4 inline ml-1 text-purple-500" title="Sélectionner ou créer une image personnalisée" />
            </label>
            <div className="space-y-2">
              <select
                value={formData.customImageId || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, customImageId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">Select existing Custom Image</option>
                {customImages.map((image) => (
                  <option key={image.imageId || image.id} value={image.imageId || image.id}>
                    {image.name} {image.version ? `(${image.version})` : ''} - {image.osType}
                  </option>
                ))}
              </select>
              
              <div className="text-center">
                <button
                  type="button"
                  onClick={() => {
                    console.log('🔄 Opening Custom Image Modal...');
                    setShowModal(true);
                  }}
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Add Custom Image
                </button>
              </div>
            </div>
            
            {!formData.customImageId && (
              <p className="text-xs text-red-600 mt-1">Image is required.</p>
            )}
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h3 className="font-medium mb-2">Debug Info:</h3>
          <p>Modal State: {showModal ? 'OPEN' : 'CLOSED'}</p>
          <p>Selected Image: {formData.customImageId || 'None'}</p>
          <p>Custom Images Count: {customImages.length}</p>
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">Add Custom Image</h2>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6 space-y-4">
              {/* Image URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="URL de l'image personnalisée" />
                </label>
                <input
                  type="url"
                  value={formData.customImageUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageUrl: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/my-custom-image.iso"
                />
              </div>

              {/* Image Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image Name
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Nom de l'image personnalisée" />
                </label>
                <input
                  type="text"
                  value={formData.customImageName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="My Custom Image"
                />
              </div>

              {/* OS Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OS Type
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Type de système d'exploitation" />
                </label>
                <select
                  value={formData.customImageOsType}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageOsType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Linux">Linux</option>
                  <option value="Windows">Windows</option>
                </select>
              </div>

              {/* Version */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Version
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Version du système d'exploitation" />
                </label>
                <input
                  type="text"
                  value={formData.customImageVersion}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageVersion: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="22.04, 2022, etc."
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Description de l'image personnalisée" />
                </label>
                <textarea
                  value={formData.customImageDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageDescription: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Description de votre image personnalisée..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t">
              <button
                type="button"
                onClick={() => setShowModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => {
                  console.log('Custom Image Data:', formData);
                  alert('Custom Image would be created!');
                  setShowModal(false);
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Upload
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
