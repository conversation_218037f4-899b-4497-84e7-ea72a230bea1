const Specification = require('../models/Specification');

// Create a new specification
exports.createSpecification = async (req, res) => {
    try {
        const specification = new Specification(req.body);
        await specification.save();
        res.status(201).json(specification);
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};

// Get all specifications
exports.getSpecifications = async (req, res) => {
    try {
        const specifications = await Specification.find().populate('package');
        res.status(200).json(specifications);
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};

// Get a single specification
exports.getSpecification = async (req, res) => {
    try {
        const specification = await Specification.findById(req.params.id).populate('package');
        if (!specification) throw new Error('Specification not found');
        res.status(200).json(specification);
    } catch (err) {
        res.status(404).json({ error: err.message });
    }
};

// Update a specification
exports.updateSpecification = async (req, res) => {
    try {
        const specification = await Specification.findByIdAndUpdate(req.params.id, req.body, { new: true });
        res.status(200).json(specification);
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};

// Delete a specification
exports.deleteSpecification = async (req, res) => {
    try {
        await Specification.findByIdAndDelete(req.params.id);
        res.status(200).json({ message: 'Specification deleted successfully' });
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};
