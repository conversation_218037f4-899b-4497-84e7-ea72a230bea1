import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import React, { useState } from "react";

const tabs = [
  "why_choose_us.performant_servers",
  "why_choose_us.custom_web_development",
  "why_choose_us.secure_cloud_hosting",
  "why_choose_us.cloud_expertise_and_migration",
];

const content = [
  {
    title: "why_choose_us.content.0.title",
    description: "why_choose_us.content.0.description",
    image: "/images/home/<USER>",
  },
  {
    title: "why_choose_us.content.1.title",
    description: "why_choose_us.content.1.description",
    image: "/images/home/<USER>",
  },
  {
    title: "why_choose_us.content.2.title",
    description: "why_choose_us.content.2.description",
    image: "/images/home/<USER>",
  },
  {
    title: "why_choose_us.content.3.title",
    description: "why_choose_us.content.3.description",
    image: "/images/home/<USER>",
  },
];

const WhyChooseUs = ({ t }) => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <section className="">
      <div className="max-w-[1400px] m-auto md:px-8 px-4 md:py-20 py-6 text-center my-5">
        <Typography className="text-3xl font-medium font-inter mb-2">
          {t("why_choose_us.why_choose_ztechengineering")}
        </Typography>
        <p className="text-gray-600 md:mb-14 mb-6">
          {t("why_choose_us.transition_guide")}
        </p>

        <div className="flex md:flex-col flex-col-reverse">
          {/* Tabs */}
          <div className="flex md:flex-row max-w-5xl mx-auto flex-col justify-between md:mb-6 gap-2">
            {tabs.map((tab, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(index)}
                className={`px-2 text-sm rounded font-medium border hover:text-white hover:bg-secondary uppercase hover:border-secondary ${
                  activeTab === index
                    ? "text-primary font-medium border-primary"
                    : "text-[#6A7292] border-transparent"
                }`}
              >
                {t(tab)}
              </button>
            ))}
          </div>

          {/* Content Section */}
          <div className="relative w-full overflow-hidden">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${activeTab * 100}%)` }}
            >
              {content.map((item, index) => (
                <div
                  key={index}
                  className="w-full flex-shrink-0 flex flex-col md:flex-row justify-center items-center gap-8 px-4 py-10"
                >
                  <div className="w-full md:w-1/3">
                    <Image
                      // loading="lazy"
                      src={item.image}
                      alt="Tab content"
                      width={350}
                      height={350}
                      quality={80}
                      sizes="(max-width: 768px) 100vw, 50vw"
                      className="rounded-lg shadow-none w-full h-[350px]"
                    />
                  </div>
                  <div className="w-full md:w-1/2 text-left">
                    <Typography
                      variant="h3"
                      className="text-xl font-semibold text-blue-800 mb-4 capitalize"
                    >
                      {t(item.title)}
                    </Typography>
                    <p className="text-gray-700">{t(item.description)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
