"use client";
import React, { useState, useEffect, useCallback } from "react";
import { Plus, AlertCircle, Package } from "lucide-react";
import PackagesTable from "./packagesTable";
import SearchHeader from "../../../components/admin/SearchHeader";
import SearchInput from "../../../components/admin/SearchInput";
import PackageFilters from "../../../components/admin/package/packageFilters";
import PackageForm from "./packageForm";
import { adminService } from "../../services/adminService";
import { toast } from "react-toastify";
import { Select, Option, Button, Typography } from "@material-tailwind/react";
import useDebounce from "@/app/hook/useDebounce";

const ITEMS_PER_PAGE_OPTIONS = [5, 10, 25, 50];

export default function PackageManagement() {
  const [packages, setPackages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [brandFilter, setBrandFilter] = useState("all");
  const [stateFilter, setStateFilter] = useState("all");
  const [showForm, setShowForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [selectedSpecs, setSelectedSpecs] = useState([]);
  const [brands, setBrands] = useState([]);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const [formData, setFormData] = useState({
    name: "",
    name_fr: "",
    description: "",
    description_fr: "",
    price: 0,
    discounts: [],
    image: "",
    brand: "",
    status: "",
    specifications: [],
    vpsConfig: {
      provider: "",
      providerProductId: "",
      providerPlanName: ""
    }
  });
  const [editingId, setEditingId] = useState(null);

  const fetchPackages = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: debouncedSearchQuery,
        brand: brandFilter !== "all" ? brandFilter : undefined,
        status: stateFilter !== "all" ? stateFilter : undefined,
      };
      
      console.log("Fetching packages with params:", params);
      const response = await adminService.getAllPackages(params);
      console.log("API response:", response);
      
      setPackages(response.data.data || []);
      setTotalItems(response.data.pagination?.totalItems || 0);
      setTotalPages(response.data.pagination?.totalPages || 0);
    } catch (err) {
      console.error("Error details:", err);
      setError(`Failed to fetch packages: ${err.message || 'Unknown error'}`);
      toast.error(`Failed to fetch packages: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, debouncedSearchQuery, brandFilter, stateFilter]);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const response = await adminService.getAllBrands();
        setBrands(response.data);
      } catch (error) {
        console.error("Error fetching brands:", error);
      }
    };
    fetchBrands();
  }, []);

  const handleEdit = (pack) => {
    setFormData({
      name: pack.name || "",
      name_fr: pack.name_fr || "",
      description: pack.description || "",
      description_fr: pack.description_fr || "",
      sslType: pack.sslType,
      price: pack.price,
      discounts: pack.discounts || [],
      image: pack.image || "",
      category: pack.category,
      brand: pack.brand._id,
      status: pack.status,
      specifications: pack.specifications || [],
      vpsConfig: pack.vpsConfig || {
        provider: "",
        providerProductId: "",
        providerPlanName: "",
        storageTypes: []
      }
    });
    setSelectedSpecs(pack.specifications || []);
    setEditingId(pack._id);
    setShowForm(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    try {
      console.log("Form data:", formData);
      console.log("VPS Config:", formData.vpsConfig);
      console.log("Storage Types:", formData.vpsConfig?.storageTypes);
      const packageData = {
        name: formData.name,
        name_fr: formData.name_fr,
        description: formData.description,
        description_fr: formData.description_fr,
        price: formData.price,
        image: formData.image || null,
        sslType: formData.type,
        category: formData.category,
        brand: formData.brand,
        discounts: formData.discounts.filter((d) => d.percentage >= 0),
        status: formData.status,
        specifications: selectedSpecs.map((spec) => spec._id),
        // Include VPS configuration if provided
        ...(formData.vpsConfig && formData.vpsConfig.provider && {
          vpsConfig: {
            provider: formData.vpsConfig.provider,
            providerProductId: formData.vpsConfig.providerProductId || null,
            providerPlanName: formData.vpsConfig.providerPlanName || null,
            storageTypes: formData.vpsConfig.storageTypes || []
          }
        })
      };
      if (editingId) {
        const updatePackageRes = await adminService.updatePackage(
          editingId,
          packageData
        );
        toast.success("Package updated successfully");
        fetchPackages(); // Refresh the list after update
      } else {
        const addNewPackageRes = await adminService.addPackage(packageData);
        toast.success("Package added successfully");
        fetchPackages(); // Refresh the list after adding
      }

      setFormData({
        name: "",
        name_fr: "",
        description: "",
        description_fr: "",
        price: 0,
        discounts: [],
        image: "",
        brand: "",
        status: "",
        specifications: [],
        vpsConfig: {
          provider: "",
          providerProductId: "",
          providerPlanName: "",
          storageTypes: []
        }
      });
      setShowForm(false);
      setEditingId(null);
      setSelectedSpecs([]);
    } catch (err) {
      setError(err.message);
      toast.error("Failed to save package");
    }
  };

  const handleDelete = async (packageId) => {
    if (!window.confirm("Are you sure you want to delete this package?"))
      return;

    try {
      await adminService.deletePackage(packageId);
      toast.success("Package deleted successfully");
      fetchPackages(); // Refresh the list after deletion
    } catch (err) {
      setError("Failed to delete package");
      toast.error("Failed to delete package");
    }
  };

  // Pagination handlers
  const handleItemsPerPageChange = useCallback((value) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  }, []);

  const handlePrevPage = useCallback(() => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  }, [totalPages]);

  // Search and filter handlers
  const handleSearchChange = useCallback((value) => {
    setSearchQuery(value);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const handleBrandFilterChange = useCallback((value) => {
    setBrandFilter(value);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  const handleStateFilterChange = useCallback((value) => {
    setStateFilter(value);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  return (
    <div className="space-y-6 bg-gray-50 p-6 rounded-xl shadow-sm">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <Package className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="text-2xl font-medium bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Package Management
          </h2>
        </div>
        <button
          onClick={() => {
            setShowForm(true);
            setEditingId(null);
            setSelectedSpecs([]);
            setFormData({
              name: "",
              name_fr: "",
              description: "",
              description_fr: "",
              price: 0,
              image: "",
              brand: "",
              discounts: [],
              specifications: [],
              status: "",
              vpsConfig: {
                provider: "",
                providerProductId: "",
                providerPlanName: "",
                storageTypes: []
              }
            });
          }}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-5 py-2.5 rounded-lg hover:shadow-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 flex items-center group"
        >
          <Plus className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
          <span className="font-medium">New Package</span>
        </button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center border border-red-200 animate-pulse">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {showForm && (
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 transition-all duration-300 animate-paint-from-bottom">
          <PackageForm
            formData={formData}
            brands={brands}
            setFormData={setFormData}
            handleSubmit={handleSubmit}
            setShowForm={setShowForm}
            editingId={editingId}
            selectedSpecs={selectedSpecs}
            setSelectedSpecs={setSelectedSpecs}
          />
        </div>
      )}

      <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
        <SearchHeader>
          <SearchInput
            searchQuery={searchQuery}
            setSearchQuery={handleSearchChange}
            placeholder={"Search packages by name or description..."}
          />
          <PackageFilters
            brandFilter={brandFilter}
            setBrandFilter={handleBrandFilterChange}
            stateFilter={stateFilter}
            setStateFilter={handleStateFilterChange}
            brands={brands}
          />
        </SearchHeader>
        <PackagesTable
          packages={packages}
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          loading={loading}
        />
        
        {/* Pagination Controls */}
        <div className="flex items-center justify-between p-4 border-t border-blue-gray-50">
          <div className="flex items-center gap-4">
            <Select
              label="Items per page"
              value={itemsPerPage.toString()}
              onChange={handleItemsPerPageChange}
              containerProps={{ className: "min-w-[120px]" }}
            >
              {ITEMS_PER_PAGE_OPTIONS.map((option) => (
                <Option key={option} value={option.toString()}>
                  {option}
                </Option>
              ))}
            </Select>
            <Typography variant="small" color="blue-gray" className="font-normal">
              Page {currentPage} of {totalPages} ({totalItems} items)
            </Typography>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outlined"
              size="sm"
              onClick={handlePrevPage}
              disabled={currentPage === 1 || loading}
              className="flex items-center gap-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
                />
              </svg>
              Previous
            </Button>
            <Button
              variant="outlined"
              size="sm"
              onClick={handleNextPage}
              disabled={currentPage === totalPages || loading}
              className="flex items-center gap-2"
            >
              Next
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className="h-4 w-4"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
