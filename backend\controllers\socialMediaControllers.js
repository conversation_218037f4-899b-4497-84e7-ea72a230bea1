const AccountState = require("../constants/enums/account-state");
const AccountRole = require("../constants/enums/account-role");
const User = require("../models/User");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");
const { ObjectId } = require("bson");
const { isProd } = require("../constants/constant");
const { clearGuestToken } = require("../midelwares/sharedMidd");
const { getCookieConfig, getRedirectUrl } = require("../helpers/helpers");
const cartService = require("../services/cartService");

// Helper function to decode guest token
const decodeGuestToken = (guestToken) => {
  if (!guestToken) return null;
  try {
    return jwt.verify(guestToken, process.env.JWT_SECRET).id;
  } catch (err) {
    console.warn("⚠️ Invalid guest token:", err.message);
    return null;
  }
};

// Helper function to generate JWT token
const generateJwtToken = (user) => {
  return jwt.sign(
    {
      id: user._id,
      name: user.firstName,
      email: user.email,
      photo: user.photo,
      role: user.role,
    },
    process.env.JWT_SECRET,
    { expiresIn: "1d" }
  );
};

exports.googleAuthCallback = async (req, res) => {
  console.log("🔹 Google OAuth callback triggered");

  if (!req.user) {
    return res.status(401).json({ error: "Authentication failed" });
  }

  const { profile } = req.user;
  const email = profile.emails?.[0]?.value;
  const isVerified = profile.emails?.[0]?.verified || false;

  if (!email) {
    return res.status(400).json({ error: "Google did not provide an email" });
  }

  try {
    let user = await User.findOne({ email });
    let guestId = decodeGuestToken(req.cookies?.guest_token);

    if (user) {
      console.log("✅ Existing user found:", user.email);

      if (!user.isOAuth) {
        user.socialMediaData = {
          provider: "google",
          id: profile.id,
          profileData: profile._json,
        };
        await user.save();
      }

      // Merge guest cart if applicable
      if (guestId) {
        await cartService.mergeGuestCartToUserCart(guestId, user._id);
      }
    } else {
      user = new User({
        _id: guestId || new mongoose.Types.ObjectId(),
        firstName: profile.name?.givenName || "",
        lastName: profile.name?.familyName || "",
        email,
        photo: profile.photos?.[0]?.value || "",
        role: AccountRole.Customer,
        state: isVerified ? AccountState.VERIFIED : AccountState.NOT_VERIFIED,
        isOAuth: true,
        socialMediaData: {
          provider: "google",
          id: profile.id,
          emailVerified: isVerified,
          profileData: profile._json,
        },
        billingInfo: {
          BillToName: profile.displayName || "",
          email,
        },
      });

      await user.save();
    }

    // Generate and set JWT token
    const token = generateJwtToken(user);
    res.cookie("token", token, {
      ...getCookieConfig(isProd),
      maxAge: 24 * 60 * 60 * 1000, // 1 day
    });

    clearGuestToken(req, res);
    console.log("🚀 User authenticated, redirecting...");

    return res.redirect(getRedirectUrl(req, isProd, false));
  } catch (err) {
    console.error("❌ Error during Google auth callback:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.facebookAuthCallback = async (req, res) => {
  console.log("Facebook Auth Callback triggered");
  if (req.query.error === "access_denied") {
    console.log("User canceled Facebook login. Redirecting to home...");
    return res.redirect(getRedirectUrl(req, isProd, false));
  }

  if (!req.user) {
    return res.status(401).json({ error: "Authentication failed" });
  }

  const { profile } = req.user;
  const email = profile.emails?.[0]?.value || null;
  const avatar = profile.photos?.[0]?.value || "";
  const firstName = profile.name?.givenName || "";
  const lastName = profile.name?.familyName || "";
  const isVerified = true;

  if (!email) {
    console.log("Facebook did not provide an email. Cannot proceed.");
    return res.status(400).json({ error: "Email is required" });
  }

  try {
    let user = await User.findOne({ email });
    let guestId = decodeGuestToken(req.cookies?.guest_token);

    if (user) {
      if (!user.isOAuth) {
        user.socialMediaData = {
          provider: "facebook",
          id: profile.id,
          profileData: profile._json,
        };
        await user.save();
      }

      // Merge guest cart if applicable
      if (guestId) {
        await cartService.mergeGuestCartToUserCart(guestId, user._id);
      }
    } else {
      user = new User({
        _id: guestId || new mongoose.Types.ObjectId(),
        firstName,
        lastName,
        email,
        photo: avatar,
        role: AccountRole.Customer,
        state: isVerified ? AccountState.VERIFIED : AccountState.NOT_VERIFIED,
        isOAuth: true,
        socialMediaData: {
          provider: "facebook",
          id: profile.id,
          profileData: profile._json,
        },
        billingInfo: {
          BillToName: `${firstName} ${lastName}`.trim(),
          email,
        },
      });

      await user.save();
    }

    // Generate and set JWT token
    const token = generateJwtToken(user);
    res.cookie("token", token, {
      ...getCookieConfig(isProd),
      maxAge: 24 * 60 * 60 * 1000, // 1 day
    });

    clearGuestToken(req, res);
    return res.redirect(getRedirectUrl(req, isProd, false));
  } catch (err) {
    console.error("Error during Facebook auth callback:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.githubAuthCallback = async (req, res) => {
  console.log("Github OAuth callback triggered");

  if (!req.user) {
    return res.status(401).json({ error: "Authentication failed" });
  }

  const { profile } = req.user;
  const email = profile.emails?.[0]?.value || null;
  const avatar = profile.photos?.[0]?.value || "";
  const firstName = profile?.username || profile.displayName || "";
  const BillToName = profile?.displayName || firstName;
  const lastName = "";
  const isVerified = true;
  const country = profile._json?.location?.split(",")?.pop()?.trim() || "";

  if (!email) {
    console.log("Github did not provide an email. Cannot proceed.");
    return res.status(400).json({ error: "Email is required" });
  }

  try {
    let user = await User.findOne({ email });
    let guestId = decodeGuestToken(req.cookies?.guest_token);

    if (user) {
      if (!user.isOAuth) {
        user.socialMediaData = {
          provider: "github",
          id: profile.id,
          profileUrl: profile.profileUrl || null,
          nodeId: profile.nodeId | null,
          profileData: profile._json,
        };
        await user.save();
      }
      // Merge guest cart if applicable
      if (guestId) {
        await cartService.mergeGuestCartToUserCart(guestId, user._id);
      }
    } else {
      user = new User({
        _id: guestId || new mongoose.Types.ObjectId(),
        firstName,
        lastName,
        email,
        photo: avatar,
        role: AccountRole.Customer,
        state: isVerified ? AccountState.VERIFIED : AccountState.NOT_VERIFIED,
        isOAuth: true,
        socialMediaData: {
          provider: "github",
          id: profile.id,
          profileUrl: profile.profileUrl || null,
          nodeId: profile.nodeId | null,
          profileData: profile._json,
        },
        billingInfo: {
          BillToName,
          email,
          country,
        },
      });

      await user.save();
      console.log("New user created:", user.email);
    }

    // Generate and set JWT token
    const token = generateJwtToken(user);
    res.cookie("token", token, {
      ...getCookieConfig(isProd),
      maxAge: 24 * 60 * 60 * 1000, // 1 day
    });

    clearGuestToken(req, res);
    return res.redirect(getRedirectUrl(req, isProd, false));
  } catch (err) {
    console.error("Error during Github auth callback:", err);
    return res.status(500).json({ error: "Internal server error" });
  }
};
