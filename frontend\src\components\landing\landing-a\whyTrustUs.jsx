'use client'
import React from 'react'
import { LIGHTNINGsvg, MALEPERSONsvg, ZTECHLOGOsvg } from '../../../icons/svgIcons'
import { Card, Typography } from '@material-tailwind/react'

const cardsData = [
    {
        icon: <ZTECHLOGOsvg />,
        title: "why_trust_us.0.title",
        description: "why_trust_us.0.description",
    },
    {
        icon: <LIGHTNINGsvg />,
        title: "why_trust_us.1.title",
        description: "why_trust_us.1.description",
    },
    {
        icon: <MALEPERSONsvg />,
        title: "why_trust_us.2.title",
        description: "why_trust_us.2.description",
    },
];

function WhyTrustUs({ t }) {
    return (
        <div className="text-center py-16 font-inter px-4 max-w-[1400px] mx-auto">
            <Typography variant='h2' className="text-3xl font-semibold mb-8">
                {t('why_trust_us_title')}
            </Typography>
            <div className="flex flex-col md:flex-row justify-center items-stretch gap-6 w-full">
                {cardsData.map((card, index) => (
                    <Card
                        key={index}
                        className="flex flex-row justify-between bg-white rounded-md border shadow-md p-6 w-full  h-full"
                    >
                        <div className="h-full flex items-center justify-center flex-shrink-0 flex-grow my-auto">
                            {card.icon}
                        </div>
                        <div className="flex flex-col pl-4">
                            <Typography variant='h3' className="text-base font-bold mb-2 text-left">
                                {t(card.title)}
                            </Typography>
                            <p className="text-gray-600 text-sm text-left">
                                {t(card.description)}
                            </p>
                        </div>
                    </Card>
                ))}
            </div>
        </div>
    )
}

export default WhyTrustUs
