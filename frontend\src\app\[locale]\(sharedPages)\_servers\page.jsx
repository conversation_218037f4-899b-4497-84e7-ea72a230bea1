"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import ServersIntro from "@/components/servers/serversIntro";
import ServersMain from "@/components/servers/serversMain";
import ContactForm2 from "@/components/shared/contactForm2";

function Servers() {
  const t = useTranslations("servers");

  const [data, setData] = useState({
    page: "Servers",
    offerName: "No offer selected",
    id: 0,
    url: "/servers",
  });

  return (
    <div className="bg-white flex flex-col gap-y-0 h-full w-full">
      <ServersIntro t={t} />
      <ServersMain t={t} />
      <ContactForm2 data={data} setData={setData} t={t} />
    </div>
  );
}

export default Servers;
