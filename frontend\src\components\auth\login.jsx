"use client";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Input,
  Spinner,
  Typography,
} from "@material-tailwind/react";
import { costomInputClasses, costomLabelClasses } from "./sharedBetweenAuth";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { generateObjectFromHeaders } from "../../app/helpers/helpers";
import { useRouter } from "next/navigation";
import { useAuth } from "../../app/context/AuthContext";
import SocialMediaLogin from "./socialLogin";
import ErrorAlert from "../shared/errorAlert";
import { handlePendingCartItem } from "../../app/utils/pendingCartHandler";

const getHeaders = (t) => {
  return [
    {
      name: "email",
      type: "email",
      placeHolder: t("email"),
      errors: false,
    },
    {
      name: "password",
      type: "password",
      placeHolder: t("password"),
      errors: false,
    },
  ];
};

const Login = () => {
  const t = useTranslations("auth");
  const { login, setCartCount } = useAuth();
  const [headers, setHeaders] = useState(getHeaders(t));
  useEffect(() => {
    setHeaders(getHeaders(t));
  }, [t]);

  const [user, setUser] = useState(generateObjectFromHeaders(headers));
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [msgError, setMsgError] = useState("");
  const router = useRouter();

  const save = async (e) => {
    e.preventDefault();
    setLoadingBtn(true);

    try {
      const res = await login(user);
      console.log("res.data ::::", res);

      headers.map((header, index) => (headers[index].errors = false));
      setLoadingBtn(false);
      // Check if there's a pending cart item to add
      // if (res.data.hasPendingCartItem) {
      //   // Add the pending item to the cart
      //   await handlePendingCartItem(setCartCount);

      //   // Redirect to cart page
      //   router.refresh();
      //   setTimeout(() => {
      //     window.location.href = "/client/cart";
      //     window.location.reload(true);
      //   }, 100);
      // } else {
      //   // Store the previous page URL before refreshing
      //   const previousPage = document.referrer || "/";
      //   router.refresh();
      //   setTimeout(() => {
      //     window.location.href = previousPage;
      //     window.location.reload(true);
      //   }, 100);
      // }
      setTimeout(() => {
        router.back();
      }, 100);
    } catch (error) {
      if (
        error.response &&
        error.response.status === 400 &&
        error.response.data.code === AccountNotVerifiedCode
      ) {
        // const x = await storeUserInLocal(error.response.data);
        navigate("verification?emailActivation=pending");
        return;
      }
      if (error && error.status === 400) {
        setMsgError(error.data.message);
        headers.forEach((header, index) => {
          headers[index].errors = true;
        });
        setHeaders([...headers]);
      } else {
        // Handle other types of errors
        console.error("Unhandled error:", error);
      }
      setLoadingBtn(false);
    }
  };

  const handleChange = (e) => {
    setUser({ ...user, [e.target.id]: e.target.value });
  };

  const navigate = (toPage) => {
    router.push("/auth/" + toPage);
  };

  return (
    <div className="w-full grid justify-center md:py-14 py-6">
      <SocialMediaLogin t={t} title={"login"} />

      <div className="flex items-center justify-center my-4">
        <span className="border-t border-gray-300 flex-grow mr-3"></span>
        <span className="text-gray-500">{t("or")}</span>
        <span className="border-t border-gray-300 flex-grow ml-3"></span>
      </div>

      <Card className="w-full px-2 py-0" color="transparent" shadow={false}>
        <form onSubmit={save} className="w-80 max-w-screen-lg sm:w-96 ">
          {headers[0].errors && <ErrorAlert error={msgError} />}
          <div className="flex flex-col gap-3">
            {headers.map((header, index) => (
              <Input
                key={header.name + index}
                onChange={handleChange}
                size="lg"
                type={header.type}
                id={header.name}
                label={header.placeHolder}
                className={`${costomInputClasses}`}
                labelProps={{
                  className: costomLabelClasses,
                }}
                required
                error={header.errors}
              />
            ))}
          </div>

          <Button
            key={"loginButton"}
            type="submit"
            disabled={loadingBtn}
            className="mt-6 py-2 font-medium text-base font-inter bg-secondary flex justify-center gap-2 items-center hover:bg-primary-200"
            style={{ boxShadow: "none", textTransform: "none" }}
            fullWidth
          >
            {loadingBtn ? <Spinner className="h-4 w-4 text-white" /> : null}
            {t("login")}
          </Button>
          <Typography color="gray" className="mt-4 font-normal">
            <Link
              href={"/auth/forgotPassword"}
              className="font-medium text-sm text-primary underline transition-colors"
            >
              {t("do_forgot_password")}
            </Link>
          </Typography>
          <div id={"createtext"}>
            <Typography
              id={"dontHaveAccount"}
              color="gray"
              className="mt-2 text-sm font-normal text-gray-800"
            >
              {t("havent_account")}
              <span
                id={"createAccountEasly"}
                onClick={() => navigate("register")}
                className="mx-1 cursor-pointer font-semibold text-primary underline transition-colors"
              >
                {t("create_it_easily")}
              </span>
            </Typography>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default Login;
