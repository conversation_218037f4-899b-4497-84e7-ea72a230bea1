// "use client";

// import { useLocale } from "next-intl";
// import { useRouter } from "next/navigation";
// import {
//   Menu,
//   <PERSON><PERSON><PERSON><PERSON><PERSON>,
//   MenuList,
//   MenuItem,
//   But<PERSON>,
// } from "@material-tailwind/react";
// import { useEffect, useTransition } from "react";
// import { FaChevronRight } from "react-icons/fa";
// import { Globe } from "lucide-react";
// import Cookies from "js-cookie";

// export default function LocaleSwitcher() {
//   const [isPending, startTransition] = useTransition();
//   const router = useRouter();
//   const localeActive = useLocale();

//   // Define available locales and filter out the active one
//   const locales = [
//     { code: "en", label: "EN" },
//     { code: "fr", label: "FR" },
//   ];
//   const otherLocale = locales.find((locale) => locale.code !== localeActive);

//   useEffect(() => {
//     const NEXT_LOCALE = Cookies.get("NEXT_LOCALE");
//     if (locales.some((locale) => locale.code === NEXT_LOCALE)) {
//       console.log("NEXT_LOCALE: ", NEXT_LOCALE);
//       Cookies.set("NEXT_LOCALE", NEXT_LOCALE, {
//         domain: ".ztechengineering.com",
//         path: "/",
//         sameSite: "None",
//         secure: true,
//       });

//       Cookies.set("i18next", NEXT_LOCALE, {
//         domain: ".ztechengineering.com",
//         path: "/",
//         sameSite: "None",
//         secure: true,
//       });
//     }
//   }, []);

//   const onLocaleChange = (nextLocale) => {
//     startTransition(() => {
//       const currentPath = window.location.pathname.replace(
//         `/${localeActive}`,
//         ""
//       );

//       Cookies.set("NEXT_LOCALE", nextLocale, {
//         domain: ".ztechengineering.com",
//         path: "/",
//         sameSite: "None",
//         secure: true,
//       });

//       Cookies.set("i18next", nextLocale, {
//         domain: ".ztechengineering.com",
//         path: "/",
//         sameSite: "None",
//         secure: true,
//       });

//       router.replace(`/${nextLocale}${currentPath}`);
//     });
//   };

//   return (
//     <Menu>
//       <MenuHandler>
//         <Button
//           variant="text"
//           className="flex items-center font-medium text-white bg-secondary hover:bg-secondary text-sm py-0 px-4 rounded"
//           disabled={isPending}
//         >
//           <Globe className="w-[16px] mx-2" />
//           {localeActive.toUpperCase()}
//           <FaChevronRight className="w-4 h-4 ml-2 text-white" />
//         </Button>
//       </MenuHandler>
//       <MenuList className="p-0 bg-transparent shadow-none border-none w-fit">
//         {otherLocale && (
//           <MenuItem
//             className="text-white bg-secondary  hover:bg-secondary w-fit mx-auto"
//             onClick={() => onLocaleChange(otherLocale.code)}
//           >
//             {otherLocale.label}
//           </MenuItem>
//         )}
//       </MenuList>
//     </Menu>
//   );
// }

"use client";

import { useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import {
  Menu,
  MenuHandler,
  MenuList,
  MenuItem,
  Button,
} from "@material-tailwind/react";
import { useEffect, useTransition } from "react";
import { FaChevronRight } from "react-icons/fa";
import { Globe } from "lucide-react";
import Cookies from "js-cookie";
import { COOKIE_DOMAIN } from "@/app/config/constant";

export default function LocaleSwitcher() {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const localeActive = useLocale();

  // Define available locales and filter out the active one
  const locales = [
    { code: "en", label: "EN" },
    { code: "fr", label: "FR" },
  ];
  const otherLocale = locales.find((locale) => locale.code !== localeActive);

  useEffect(() => {
    // No need to create a new cookie if it's already set, you just need to update it
    // Cookies.set("NEXT_LOCALE", localeActive, {
    //   domain: COOKIE_DOMAIN,
    // });
    Cookies.set("BACKEND_LANG", localeActive, { domain: COOKIE_DOMAIN });
  }, [localeActive]);

  const onLocaleChange = (nextLocale) => {
    startTransition(() => {
      // Get the full URL and create a URL object
      const url = new URL(window.location.href);

      // Remove the locale from pathname while preserving the rest of the path and query params
      const newPathname = url.pathname.replace(
        new RegExp(`^/${localeActive}`),
        `/${nextLocale}`
      );

      // No need to create a new cookie if it's already set, you just need to update it
      // Cookies.set("NEXT_LOCALE", nextLocale, {
      //   domain: COOKIE_DOMAIN,
      // });

      Cookies.set("BACKEND_LANG", nextLocale, { domain: COOKIE_DOMAIN });

      // Preserve search params
      const newUrl = `${newPathname}${url.search}`;

      router.replace(newUrl);
    });
  };

  return (
    <Menu>
      <MenuHandler>
        <Button
          variant="text"
          className="flex items-center font-medium text-white bg-secondary hover:bg-secondary text-sm py-0 px-4 rounded"
          disabled={isPending}
        >
          <Globe className="w-[16px] mx-2" />
          {localeActive.toUpperCase()}
          <FaChevronRight className="w-4 h-4 ml-2 text-white" />
        </Button>
      </MenuHandler>
      <MenuList className="p-0 bg-transparent shadow-none border-none w-fit">
        {otherLocale && (
          <MenuItem
            className="text-white bg-secondary hover:bg-secondary w-fit mx-auto"
            onClick={() => onLocaleChange(otherLocale.code)}
          >
            {otherLocale.label}
          </MenuItem>
        )}
      </MenuList>
    </Menu>
  );
}
