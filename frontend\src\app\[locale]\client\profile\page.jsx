'use client';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import profileService from '../../../services/profileService';
import { PencilSquareIcon, UserCircleIcon } from '@heroicons/react/24/solid';
import { Avatar, Typography } from '@material-tailwind/react';
import { setServerMedia } from '../../../helpers/helpers';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

const ProfileDetail = ({ label, value, isLoading }) => (
    <div>
        <p className="text-gray-600">{label}</p>
        {isLoading ? (
            <Skeleton width={200} height={20} />
        ) : (
            <p className="font-medium text-sm text-gray-800">{value || 'N/A'}</p>
        )}
    </div>
);

function ProfilePage() {
    const t = useTranslations('profile');
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const router = useRouter();

    const toggleDropdown = () => {
        setDropdownOpen(!dropdownOpen);
    };

    useEffect(() => {
        const getUserProfile = async () => {
            try {
                const userProfileRes = await profileService.getProfile();
                setUserData(userProfileRes.data);
                // console.log("user : " ,userProfileRes.data )
                // console.log("isOAuth : ", userProfileRes.data?.isOAuth)
                // console.log("hasPassword : ", userProfileRes.data?.hasPassword)
            } catch (err) {
                console.error('Error getting user profile: ', err);
                setError('Failed to load profile data.');
            } finally {
                setLoading(false);
            }
        };
        // console.log("isOAuthWithoutPassword : ", isOAuthWithoutPassword)
        getUserProfile();
    }, []);

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    if (error) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <p className="text-red-500 text-lg">{error}</p>
            </div>
        );
    }

    return (
        <div className="flex justify-center items-center py-8">
            <div className="max-w-4xl w-full bg-white p-6 space-y-6">
                {/* Avatar and Name Section */}
                <div className="relative flex flex-col md:flex-row items-center space-x-4">
                    <div className="relative">
                        {loading ? (
                            <Skeleton circle height={96} width={96} />
                        ) : (
                            userData?.photo ? (
                                <Avatar
                                    src={setServerMedia(userData?.photo)}
                                    color="gray"
                                    alt="Avatar"
                                    variant="circular"
                                    size="xxl"
                                    className="p-0 border"
                                />
                            ) : (
                                <UserCircleIcon className="h-20 w-20" />
                            )
                        )}
                    </div>
                    <div>
                        {loading ? (
                            <>
                                <Skeleton width={200} height={24} />
                                <Skeleton width={300} height={20} />
                            </>
                        ) : (
                            <>
                                {userData?.firstName ?
                                    <Typography variant='h1' className="md:text-3xl text-2xl font-semibold text-gray-800">
                                        {userData.firstName + ' ' + userData.lastName}
                                    </Typography>
                                    :
                                    <Link
                                        href="/auth/register"
                                        className="text-blue-600">
                                        {t('please_signup')}
                                    </Link>
                                }
                                <p className="text-gray-500 md:text-xl text-base">{userData?.email}</p>
                            </>
                        )}
                    </div>

                    {!loading && userData?.firstName && (
                        <button
                            onClick={toggleDropdown}
                            aria-label="Edit profile"
                            className="absolute md:-bottom-0 -bottom-10 -right-0 rounded-full p-2 flex items-center gap-x-2"
                        >
                            <PencilSquareIcon width={22} className="text-gray-900" />
                            <span className='capitalize text-sm'>
                                {t('edit')}
                            </span>
                        </button>
                    )}

                    {dropdownOpen && (
                        <div className="absolute right-0 md:top-28 top-52 mt-2 min-w-48 bg-white shadow-lg rounded-md border">
                            <ul>
                                <li>
                                    <button
                                        onClick={() => router.push('/client/profile/edit')}
                                        className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                                    >
                                        {t('edit_profile')}
                                    </button>
                                    <hr />
                                </li>

                                {/* Show change email if user is not OAuth or if OAuth user has set password */}
                                {(!userData?.isOAuth || userData?.hasPassword) && (
                                    <li>
                                        <button
                                            onClick={() => router.push('/client/profile/edit?editEmail=true')}
                                            className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                                        >
                                            {t('change_email')}
                                        </button>
                                        <hr />
                                    </li>
                                )}
                                {/* Always show change password option */}
                                <li>
                                    <button
                                        onClick={() => router.push('/client/profile/edit?editPassword=true')}
                                        className="block w-full text-left px-4 py-2 text-sm text-gray-800 hover:bg-gray-100"
                                    >
                                        {(!userData?.isOAuth || userData?.hasPassword) ? t('change_password') : t('set_password')}
                                    </button>
                                </li>
                            </ul>
                        </div>
                    )}
                </div>

                {/* Personal Information Section */}
                <div className="border-y py-4">
                    <Typography variant="h2" className="text-xl font-semibold text-gray-800">
                        {t('personal_info')}
                    </Typography>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <ProfileDetail
                            label={t("role")}
                            value={t(userData?.role ? userData.role : "guest")}
                            isLoading={loading}
                        />
                        <ProfileDetail
                            label={t("state")}
                            value={t(userData?.state ? userData.state : "unknown")}
                            isLoading={loading}
                        />
                    </div>
                </div>

                {/* Company Information Section - Only shown if user is a company */}
                {userData?.firstName && userData?.billingInfo?.isCompany && (
                    <div className="border-b py-4">
                        <Typography
                            variant="h2"
                            className="text-xl font-semibold text-gray-800">
                            {t('company_info')}
                        </Typography>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <ProfileDetail
                                label={t("company_ice")}
                                value={userData?.billingInfo?.companyICE}
                                isLoading={loading}
                            />
                            <ProfileDetail
                                label={t("company_email")}
                                value={userData?.billingInfo?.companyEmail}
                                isLoading={loading}
                            />
                            <ProfileDetail
                                label={t("company_phone")}
                                value={userData?.billingInfo?.companyPhone}
                                isLoading={loading}
                            />
                            <ProfileDetail
                                label={t("company_address")}
                                value={userData?.billingInfo?.companyAddress}
                                isLoading={loading}
                            />
                        </div>
                    </div>
                )}

                {/* Account Information Section */}
                {userData?.firstName && (
                    <div>
                        <Typography
                            variant="h2"
                            className="text-xl font-semibold text-gray-800">
                            {t('account_info')}
                        </Typography>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <ProfileDetail
                                label={t("created_at")}
                                value={formatDate(userData?.createdAt)}
                                isLoading={loading}
                            />
                            <ProfileDetail
                                label={t("last_updated")}
                                value={formatDate(userData?.updatedAt)}
                                isLoading={loading}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default ProfilePage;
