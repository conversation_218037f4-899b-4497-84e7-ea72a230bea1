const mongoose = require('mongoose');
const OrderStatus = require('../constants/enums/order-status');
const Schema = mongoose.Schema;

// Create a new enum for SSL certificate status
const SSLCertificateStatus = {
    PENDING: 'PENDING',
    PROCESSING: 'PROCESSING',
    PENDING_VALIDATION: 'PENDING_VALIDATION',
    VALIDATED: 'VALIDATED',
    ISSUED: 'ISSUED',
    INSTALLED: 'INSTALLED',
    EXPIRED: 'EXPIRED',
    REVOKED: 'REVOKED',
    FAILED: 'FAILED'
};

// Schema for individual SSL certificates within a suborder
const sslCertificateSchema = new Schema({
    csr: {
        type: String,
        default: null
    },
    domain: {
        type: String,
        default: null
    },
    validationEmail: {
        type: String,
        default: null
    },
    status: {
        type: String,
        enum: Object.values(SSLCertificateStatus),
        default: SSLCertificateStatus.PENDING
    },
    certificateFile: {
        type: String,
        default: null
    },
    issuedAt: {
        type: Date,
        default: null
    },
    expiresAt: {
        type: Date,
        default: null
    },
    // Add the installation service field
    installService: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});

// Create VPS status enum
const VPSStatus = {
    PENDING: 'PENDING',
    PROVISIONING: 'PROVISIONING',
    ACTIVE: 'ACTIVE',
    SUSPENDED: 'SUSPENDED',
    TERMINATED: 'TERMINATED',
    FAILED: 'FAILED'
};

// Schema for VPS configuration within a suborder
const vpsConfigSchema = new Schema({
    provider: {
        type: String,
        enum: ['contabo', 'digitalocean', 'vultr', 'linode'],
        default: 'contabo'
    },
    planId: {
        type: String,
        required: true
    },
    region: {
        type: String,
        required: true
    },
    operatingSystem: {
        type: String,
        required: true
    },
    displayName: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: Object.values(VPSStatus),
        default: VPSStatus.PENDING
    },
    // Provider-specific data
    providerInstanceId: {
        type: String,
        default: null
    },
    ipAddress: {
        type: String,
        default: null
    },
    ipv6Address: {
        type: String,
        default: null
    },
    rootPassword: {
        type: String,
        default: null // Should be encrypted in production
    },
    sshKeys: [{
        type: String
    }],
    // VPS specifications
    specifications: {
        cpu: Number,
        ram: Number, // in GB
        storage: Number, // in GB
        bandwidth: Number // in GB
    },
    // Lifecycle dates
    provisionedAt: {
        type: Date,
        default: null
    },
    activatedAt: {
        type: Date,
        default: null
    },
    suspendedAt: {
        type: Date,
        default: null
    },
    terminatedAt: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});

const subOrderSchema = new Schema({
    identifiant: { type: String, required: true, unique: true },
    package: { type: Schema.Types.ObjectId, ref: 'Package', required: true },
    quantity: { type: Number, required: true },
    period: { type: Number, required: true, default: 1 },
    price: { type: Number, required: true },
    discount: { type: Number }, // Amount discounted from the price
    basedPrice: { type: Number, required: true }, // Price without discount
    status: {
        type: String,
        required: true,
        enum: [
            OrderStatus.PENDING,
            OrderStatus.PROCESSING,
            OrderStatus.COMPLETED,
            OrderStatus.SHIPPED,
            OrderStatus.PROCESSINGREFUND,
            OrderStatus.REFUNDED,
            OrderStatus.FAILED,
            OrderStatus.CANCELLED,
            OrderStatus.ACTIVE,
            OrderStatus.EXPIRED,
        ],
        default: OrderStatus.PENDING,
    },
    // New field for multiple SSL certificates
    ssl: {
        type: Map,
        of: sslCertificateSchema,
        default: new Map()
    },
    // VPS configuration for VPS orders
    vps: {
        type: vpsConfigSchema,
        default: null
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Export the enums
module.exports.SSLCertificateStatus = SSLCertificateStatus;
module.exports.VPSStatus = VPSStatus;

const SubOrder = mongoose.model('SubOrder', subOrderSchema);
module.exports = SubOrder;
