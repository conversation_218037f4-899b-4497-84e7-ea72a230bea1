'use client'
import React, { useState } from 'react';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import '../../styles/globals.css';

// Import required modules
import { Typography } from '@material-tailwind/react';
import CustomSwiper from '../shared/customSwiper';
import { useTranslations } from 'next-intl';

const reviews = [
    {
        title: "review1.title",
        description: "review1.description",
        date: "review1.date",
        name: "<PERSON><PERSON> baddag,"
    },
    {
        title: "review2.title",
        description: "review2.description",
        date: "review2.date",
        name: '<PERSON><PERSON>,'
    },
    {
        title: "review3.title",
        description: "review3.description",
        date: "review3.date",
        name: '<PERSON>'
    }
];

const Testimonials = () => {
    const t = useTranslations('Home');

    // Inside your testimonial mapping
    const [isExpanded, setIsExpanded] = useState(false);

    // Function to toggle description
    const toggleDescription = () => {
        setIsExpanded((prev) => !prev);
    };

    return (
        <section className="py-10 bg-white max-w-[1400px] mx-auto">
            <Typography
                className="text-secondary text-xs text-center font-inter font-bold"
            >
                {t('testimonials')}
            </Typography>
            <Typography
                variant='h1'
                className="text-center text-3xl font-bold text-gray-800 mb-8">
                {t('what_they_say')}
            </Typography>
            <div className="flex flex-col md:flex-row justify-center items-center mb-8">
                <div className="flex flex-col items-center text-black mx-auto p-10">
                    <Typography variant="h5" className="font-bold">
                        {t('excellent')}
                    </Typography>
                    <div className="flex items-center">
                        <svg
                            width="140"
                            height="30"
                            viewBox="0 0 163 30"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.00463867 29.9772H29.9345V0.0742188H0.00463867V29.9772Z" fill="#F4BA07" />
                            <path d="M25.519 12.7966L8.54311 25.1175L11.0202 17.5028L4.53589 12.7966H12.5509L15.0274 5.1814L17.504 12.7966H25.519ZM15.0281 20.4119L19.6642 19.4376L21.5114 25.1175L15.0281 20.4119Z" fill="white" />
                            <path d="M33.0793 29.9772H63.0092V0.0742188H33.0793V29.9772Z" fill="#F4BA07" />
                            <path d="M33.0793 29.9772H48.0443V0.0742188H33.0793V29.9772Z" fill="#F4BA07" />
                            <path d="M48.4024 20.2956L52.3779 19.4372L54.2167 25.2462L47.9821 20.5972L41.5045 25.2462L44.0186 17.6038L37.4368 12.8806H45.5722L48.0853 5.23767L50.5995 12.8806H58.7343L48.4024 20.2956Z" fill="white" />
                            <path d="M66.177 29.9772H96.1069V0.0742188H66.177V29.9772Z" fill="#F4BA07" />
                            <path d="M66.177 29.9772H81.1419V0.0742188H66.177V29.9772Z" fill="#F4BA07" />
                            <path d="M91.6919 12.7966L74.716 25.1175L77.1931 17.5028L70.7087 12.7966H78.7237L81.2003 5.1814L83.6769 12.7966L91.6919 12.7966ZM81.2009 20.4119L85.8371 19.4376L87.6842 25.1175L81.2009 20.4119Z" fill="white" />
                            <path d="M99.2742 29.9772H129.204V0.0742188H99.2742V29.9772Z" fill="#F4BA07" />
                            <path d="M99.2742 29.9772H114.239V0.0742188H99.2742V29.9772Z" fill="#F4BA07" />
                            <path d="M124.788 12.7966L107.813 25.1175L110.289 17.5028L103.805 12.7966H111.82L114.296 5.1814L116.773 12.7966L124.788 12.7966ZM114.297 20.4119L118.933 19.4376L120.78 25.1175L114.297 20.4119Z" fill="white" />
                            <path d="M132.349 29.9772H162.279V0.0742188H132.349V29.9772Z" fill="#DCDCE6" />
                            <path d="M132.349 29.9772H147.314V0.0742188H132.349V29.9772Z" fill="#F4BA07" />
                            <path d="M157.863 12.7966L140.888 25.1175L143.364 17.5028L136.88 12.7966H144.895L147.371 5.1814L149.848 12.7966H157.863ZM147.372 20.4119L152.008 19.4376L153.855 25.1175L147.372 20.4119Z" fill="white" />
                        </svg>
                    </div>
                    <Typography className="text-xs text-gray-600">{t('based_on_reviews')}</Typography>
                    <img
                        loading="lazy"
                        src="/images/home/<USER>"
                        alt="Google"
                        className="w-16 mt-2"
                    />
                </div>
                <CustomSwiper data={reviews} t={t} />
            </div>
        </section>
    );
};

export default Testimonials;
