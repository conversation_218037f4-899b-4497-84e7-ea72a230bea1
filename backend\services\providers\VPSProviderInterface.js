/**
 * VPS Provider Interface
 * Abstract interface that all VPS providers must implement
 * Follows Interface Segregation Principle (ISP) from SOLID
 */

class VPSProviderInterface {
  /**
   * Get available VPS plans from the provider
   * @returns {Promise<Array>} Array of VPS plans
   */
  async getPlans() {
    throw new Error('getPlans method must be implemented');
  }

  /**
   * Get VPS plan details by ID
   * @param {string} planId - Plan identifier
   * @returns {Promise<Object>} Plan details
   */
  async getPlanDetails(planId) {
    throw new Error('getPlanDetails method must be implemented');
  }

  /**
   * Create a new VPS instance
   * @param {Object} orderData - VPS order data
   * @returns {Promise<Object>} Created VPS instance details
   */
  async createVPS(orderData) {
    throw new Error('createVPS method must be implemented');
  }

  /**
   * Get VPS instance details
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} VPS instance details
   */
  async getVPSDetails(instanceId) {
    throw new Error('getVPSDetails method must be implemented');
  }

  /**
   * Get all VPS instances for a customer
   * @param {string} customerId - Customer identifier
   * @returns {Promise<Array>} Array of VPS instances
   */
  async getCustomerVPS(customerId) {
    throw new Error('getCustomerVPS method must be implemented');
  }

  /**
   * Start a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async startVPS(instanceId) {
    throw new Error('startVPS method must be implemented');
  }

  /**
   * Stop a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async stopVPS(instanceId) {
    throw new Error('stopVPS method must be implemented');
  }

  /**
   * Restart a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async restartVPS(instanceId) {
    throw new Error('restartVPS method must be implemented');
  }

  /**
   * Delete a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async deleteVPS(instanceId) {
    throw new Error('deleteVPS method must be implemented');
  }

  /**
   * Reset VPS root password
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result with new password
   */
  async resetPassword(instanceId) {
    throw new Error('resetPassword method must be implemented');
  }

  /**
   * Create VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotName - Name for the snapshot
   * @returns {Promise<Object>} Operation result
   */
  async createSnapshot(instanceId, snapshotName) {
    throw new Error('createSnapshot method must be implemented');
  }

  /**
   * Reinstall VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {string} imageId - New image ID to install
   * @returns {Promise<Object>} Operation result
   */
  async reinstallVPS(instanceId, imageId) {
    throw new Error('reinstallVPS method must be implemented');
  }

  /**
   * Get available images
   * @returns {Promise<Array>} Array of available OS images
   */
  async getImages() {
    throw new Error('getImages method must be implemented');
  }

  /**
   * Get available regions
   * @returns {Promise<Array>} Array of available regions
   */
  async getRegions() {
    throw new Error('getRegions method must be implemented');
  }

  /**
   * Get available data centers
   * @returns {Promise<Array>} Array of available data centers
   */
  async getDataCenters() {
    throw new Error('getDataCenters method must be implemented');
  }

  /**
   * Get available regions
   * @returns {Promise<Array>} Array of available regions
   */
  async getRegions() {
    throw new Error('getRegions method must be implemented');
  }

  // Object Storage Methods (Optional - not all providers may support these)

  /**
   * Get object storages
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of object storages
   */
  async getObjectStorages(options = {}) {
    throw new Error('getObjectStorages method must be implemented');
  }

  /**
   * Create object storage
   * @param {Object} storageData - Object storage data
   * @returns {Promise<Object>} Created object storage
   */
  async createObjectStorage(storageData) {
    throw new Error('createObjectStorage method must be implemented');
  }

  /**
   * Get VPS usage statistics
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Usage statistics
   */
  async getVPSStats(instanceId) {
    throw new Error('getVPSStats method must be implemented');
  }

  /**
   * Validate provider configuration
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    throw new Error('validateConfig method must be implemented');
  }

  /**
   * Get provider name
   * @returns {string} Provider name
   */
  getProviderName() {
    throw new Error('getProviderName method must be implemented');
  }

  /**
   * Transform provider-specific data to standardized format
   * @param {Object} providerData - Provider-specific data
   * @param {string} dataType - Type of data (plan, instance, etc.)
   * @returns {Object} Standardized data
   */
  transformData(providerData, dataType) {
    throw new Error('transformData method must be implemented');
  }
}

module.exports = VPSProviderInterface;
