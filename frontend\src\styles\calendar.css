/* Custom styles for react-big-calendar with performance optimizations */

.rbc-calendar {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  /* Performance optimizations */
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.rbc-header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 8px;
  font-weight: 600;
  color: #374151;
}

.rbc-month-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  /* Performance optimizations for month view */
  contain: layout style paint;
  transform: translateZ(0);
}

.rbc-month-row {
  border-bottom: 1px solid #e2e8f0;
  /* Optimize rendering */
  contain: layout style;
  will-change: auto;
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-date-cell {
  padding: 8px;
  text-align: right;
  /* Optimize text rendering */
  text-rendering: optimizeSpeed;
  font-feature-settings: "kern" 0;
}

.rbc-week-view,
.rbc-day-view {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.rbc-time-view .rbc-time-gutter {
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
}

.rbc-time-view .rbc-time-content {
  border-left: none;
}

.rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

.rbc-time-slot:hover {
  background-color: #f8fafc;
}

.rbc-day-slot .rbc-time-slot {
  border-top: 1px solid #f1f5f9;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0;
}

.rbc-event {
  background-color: #3b82f6;
  border: none;
  border-radius: 4px;
  color: white;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  /* Performance optimizations for events */
  will-change: transform;
  transform: translateZ(0);
  contain: layout style paint;
}

.rbc-event.rbc-selected {
  background-color: #1d4ed8;
}

.rbc-event:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.rbc-slot-selection {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px solid #3b82f6;
}

.rbc-date-cell.rbc-off-range {
  color: #9ca3af;
}

.rbc-date-cell.rbc-now {
  background-color: #fef3c7;
  font-weight: 600;
}

.rbc-date-cell:hover {
  background-color: #f8fafc;
  transition: background-color 0.15s ease;
}

.rbc-day-bg {
  border-right: 1px solid #e2e8f0;
}

.rbc-day-bg:last-child {
  border-right: none;
}

.rbc-day-bg.rbc-off-range-bg {
  background-color: #f9fafb;
}

.rbc-day-bg.rbc-today {
  background-color: #fef3c7;
}

.rbc-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 12px;
}

.rbc-toolbar button {
  background-color: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  font-size: 14px;
  min-height: 36px;
}

.rbc-toolbar button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rbc-toolbar button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.rbc-toolbar button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rbc-toolbar button.rbc-active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.rbc-toolbar button.rbc-active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

.rbc-toolbar-label {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 16px;
}

.rbc-btn-group {
  display: inline-flex;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rbc-btn-group button {
  border-radius: 0;
  border-right: none;
  margin: 0;
}

.rbc-btn-group button:first-child {
  border-radius: 6px 0 0 6px;
}

.rbc-btn-group button:last-child {
  border-radius: 0 6px 6px 0;
  border-right: 1px solid #d1d5db;
}

.rbc-btn-group button:not(:last-child) {
  border-right: 1px solid #e5e7eb;
}

.rbc-btn-group button.rbc-active:not(:last-child) {
  border-right: 1px solid #1d4ed8;
}

.rbc-show-more {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  cursor: pointer;
}

.rbc-show-more:hover {
  background-color: #e5e7eb;
}

/* Custom styles for interview events */
.rbc-event.interview-event {
  background-color: #10b981;
  border-left: 4px solid #059669;
}

.rbc-event.interview-event.scheduled {
  background-color: #3b82f6;
  border-left: 4px solid #1d4ed8;
}

.rbc-event.interview-event.completed {
  background-color: #10b981;
  border-left: 4px solid #059669;
}

.rbc-event.interview-event.cancelled {
  background-color: #ef4444;
  border-left: 4px solid #dc2626;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-toolbar {
    flex-direction: column;
    gap: 12px;
  }

  .rbc-toolbar-label {
    margin: 0;
    text-align: center;
  }

  .rbc-btn-group {
    justify-content: center;
  }
}

/* Loading state */
.calendar-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #6b7280;
}

/* Empty state */
.calendar-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: #6b7280;
  text-align: center;
}

.calendar-empty svg {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}
