name: Deploy ztechEngineering to the ZTEKENGINEERING PROD Environment

on:
  push:
    branches: [ztek_prod]

jobs:
  build:
    runs-on: ["self-hosted", "ztekengineering"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install frontend modules
        run: |
          cd frontend
          npm i --force

      - name: Build the frontend
        run: |
          cd frontend
          npm run build

      - name: pm2 start for the frontend (port 3001)
        run: |
          cd frontend
          pm2 list | grep -q ztech-frontend && pm2 delete ztech-frontend || true
          pm2 start npm --name ztech-frontend -- start -- --port=3001

      # Write the .env file for the backend from the GitHub secret
      # - name: Write .env for backend
      #   run: |
      #     cd backend
      #     echo "${{ secrets.PROD_ENV_FILE_CONTENT }}" > .env

      # - name: Install modules for the backend
      #   run: |
      #     cd backend
      #     npm i --force

      # - name: pm2 start for the backend (port 5002)
      #   run: |
      #     cd backend
      #     pm2 delete ztech-backend
      #     pm2 start npm --name ztech-backend -- start -- --port=5002
      #     pm2 save

      #- name: Restart Nginx
      #  run: sudo service nginx restart
