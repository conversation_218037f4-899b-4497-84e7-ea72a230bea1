import { Card, Typography } from "@material-tailwind/react";
import Link from "next/link";
import React from "react";
import { BsArrowUpRight } from "react-icons/bs";

const whyChooseData = [
    {
        id: 1,
        image: "/images/why-us.png",
        title: "whyChooseData.0",
    },
    {
        id: 2,
        image: "/images/service-details-1.jpg",
        title: "whyChooseData.1",
    },
    {
        id: 3,
        image: "/images/landing/landing-b-side-img.webp",
        title: "whyChooseData.2",
    },
];

const features = [
    {
        icon: (
            <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1360_6132)">
                    <path d="M21.2068 24.4116V29.349L23.5724 30.3336V23.1178C22.8293 23.6274 22.0368 24.0609 21.2068 24.4116ZM8.42973 23.1178V30.2843L10.7953 29.2998V24.4116C9.96532 24.0609 9.1728 23.6274 8.42973 23.1178ZM16.001 25.462C14.884 25.4626 13.7713 25.3236 12.6887 25.0482V32.5L16.001 31.1214L19.3133 32.4999V25.0482C18.2308 25.3236 17.1181 25.4626 16.001 25.462ZM16.001 0.5C9.64104 0.5 4.4668 5.67424 4.4668 12.0343C4.4668 18.3943 9.64104 23.5685 16.001 23.5685C22.361 23.5685 27.5353 18.3943 27.5353 12.0342C27.5353 5.67418 22.3611 0.5 16.001 0.5ZM16.001 21.1312C10.959 21.1312 6.85692 17.0292 6.85692 11.9871C6.85692 6.94499 10.959 2.84293 16.001 2.84293C21.0431 2.84293 25.1451 6.94499 25.1451 11.9871C25.1451 17.0292 21.0431 21.1312 16.001 21.1312ZM16.001 4.73637C12.003 4.73637 8.75035 7.98899 8.75035 11.9871C8.75035 15.9852 12.003 19.2377 16.001 19.2377C19.999 19.2377 23.2517 15.9851 23.2517 11.987C23.2517 7.98899 19.9991 4.73637 16.001 4.73637ZM18.9811 16.2187L16.001 14.652L13.021 16.2187L13.5902 12.9005L11.1793 10.5505L14.5111 10.0663L16.001 7.04717L17.491 10.0663L20.8228 10.5505L18.412 12.9005L18.9811 16.2187Z" fill="white" />
                </g>
                <defs>
                    <clipPath id="clip0_1360_6132">
                        <rect width="32" height="32" fill="white" transform="translate(0 0.5)" />
                    </clipPath>
                </defs>
            </svg>
        ),
        description: "featuresData.0"
    },
    {
        icon: (
            <svg width="30" height="31" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" clipRule="evenodd" d="M3.46761 16.1018C3.83791 16.4725 3.83755 17.0733 3.46679 17.4435L3.27224 17.6379C3.10604 17.8039 3.10604 18.0725 3.27224 18.2385C3.43906 18.4052 3.71001 18.4052 3.87684 18.2385L6.017 16.1009C6.38775 15.7307 6.98849 15.731 7.3588 16.1018C7.7291 16.4725 7.72873 17.0733 7.35798 17.4435L5.21783 19.581C4.31013 20.4877 2.83895 20.4877 1.93125 19.581C1.02292 18.6739 1.02292 17.2025 1.93125 16.2953L2.12581 16.1009C2.49656 15.7307 3.0973 15.731 3.46761 16.1018ZM9.60831 17.362C9.97884 17.7327 9.97884 18.3334 9.60831 18.7039L6.9303 21.3819C6.55977 21.7524 5.95904 21.7524 5.5885 21.3819C5.21797 21.0113 5.21797 20.4107 5.5885 20.04L8.26651 17.362C8.63704 16.9915 9.23779 16.9915 9.60831 17.362ZM13.1198 20.8692C13.4902 21.2398 13.4902 21.8405 13.1198 22.211L10.4601 24.8707C10.0895 25.2412 9.48879 25.2412 9.11826 24.8707C8.74773 24.5002 8.74773 23.8994 9.11826 23.5289L11.778 20.8692C12.1485 20.4987 12.7493 20.4987 13.1198 20.8692ZM9.12149 21.37C9.48787 21.7447 9.48119 22.3453 9.10656 22.7117L6.94385 24.8268C6.56922 25.1932 5.96851 25.1864 5.60214 24.8118C5.23575 24.4372 5.24244 23.8365 5.61706 23.4702L7.77978 21.355C8.1544 20.9887 8.75511 20.9954 9.12149 21.37ZM14.3514 23.1475C14.7218 23.5183 14.7214 24.119 14.3506 24.4894L12.2104 26.6269C12.0442 26.7929 12.0442 27.0616 12.2104 27.2276C12.3772 27.3942 12.6482 27.3942 12.815 27.2276L13.0096 27.0332C13.3804 26.6629 13.9811 26.6633 14.3514 27.034C14.7218 27.4048 14.7214 28.0056 14.3506 28.3758L14.156 28.5702C13.2484 29.4767 11.7771 29.4767 10.8695 28.5702C9.96111 27.6629 9.96111 26.1915 10.8695 25.2843L13.0096 23.1468C13.3804 22.7764 13.9811 22.7768 14.3514 23.1475Z" fill="white" />
                <path d="M13.558 7.26133L10.8235 9.98771C10.3208 10.4888 9.8597 10.9485 9.49556 11.3641C9.26173 11.6309 9.02804 11.923 8.83009 12.2458L8.80344 12.2192C8.75286 12.1688 8.72755 12.1435 8.70215 12.1189C8.22684 11.6577 7.66774 11.291 7.05476 11.0385C7.02201 11.025 6.98876 11.0118 6.92228 10.9855L6.51511 10.8241C5.96353 10.6056 5.81653 9.89549 6.23624 9.47705C7.44079 8.27616 8.88703 6.83431 9.58499 6.54473C10.2005 6.28933 10.8655 6.20435 11.5068 6.29913C12.0944 6.38596 12.6503 6.6878 13.558 7.26133Z" fill="white" />
                <path d="M18.2266 21.6164C18.4469 21.8401 18.5933 21.998 18.7256 22.1669C18.9002 22.3896 19.0563 22.6263 19.1923 22.8743C19.3454 23.1535 19.4644 23.4518 19.7022 24.0484C19.8958 24.534 20.5389 24.6624 20.9126 24.2899L21.0029 24.1998C22.2074 22.9989 23.6537 21.557 23.9441 20.8611C24.2003 20.2475 24.2856 19.5845 24.1904 18.9451C24.1033 18.3594 23.8007 17.8053 23.2256 16.9004L20.4818 19.6359C19.9678 20.1484 19.4964 20.6184 19.0702 20.9865C18.8147 21.2071 18.5352 21.4276 18.2266 21.6164Z" fill="white" />
                <path fillRule="evenodd" clipRule="evenodd" d="M19.3766 18.4592L25.6636 12.1911C26.5691 11.2884 27.0218 10.837 27.2602 10.2631C27.4987 9.68912 27.4987 9.05081 27.4987 7.77421V7.16432C27.4987 5.20124 27.4987 4.2197 26.887 3.60985C26.2753 3 25.2908 3 23.3218 3H22.7101C21.4296 3 20.7893 3 20.2137 3.23774C19.638 3.47549 19.1853 3.92684 18.2798 4.82955L11.9927 11.0977C10.9347 12.1525 10.2787 12.8066 10.0247 13.4382C9.94443 13.6379 9.9043 13.8352 9.9043 14.0424C9.9043 14.9047 10.6004 15.5989 11.9927 16.987L12.1799 17.1735L14.3718 14.9489C14.6845 14.6316 15.1951 14.6277 15.5123 14.9404C15.8296 15.253 15.8335 15.7636 15.5208 16.0809L13.3221 18.3124L13.4695 18.4592C14.8618 19.8474 15.558 20.5414 16.423 20.5414C16.6142 20.5414 16.7971 20.5075 16.981 20.4397C17.6297 20.2009 18.2918 19.5406 19.3766 18.4592ZM22.3302 11.0982C21.5146 11.9113 20.1922 11.9113 19.3767 11.0982C18.5611 10.285 18.5611 8.96669 19.3767 8.15355C20.1922 7.34041 21.5146 7.34041 22.3302 8.15355C23.1458 8.96669 23.1458 10.285 22.3302 11.0982Z" fill="white" />
            </svg>
        ),
        description: "featuresData.1"
    },
    {
        icon: (
            <svg width="23" height="28" viewBox="0 0 23 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.45473 12.2473C7.71362 15.0255 10.4088 16.2348 11.7466 16.2348C12.6261 16.2348 14.0926 15.7115 15.3518 14.5637C14.8914 14.5958 14.3993 14.6121 13.8735 14.6121C13.7501 14.6121 13.6206 14.611 13.4823 14.6088C13.2172 14.813 12.8888 14.9281 12.5481 14.9281H11.4277C10.5834 14.9281 9.89647 14.2412 9.89647 13.3969C9.89647 12.5525 10.5834 11.8656 11.4277 11.8656H12.5481C12.9529 11.8656 13.3314 12.0234 13.6122 12.2955C13.7055 12.2965 13.7974 12.297 13.8877 12.297C15.334 12.297 16.2872 12.1643 16.9107 12.0043C16.8298 11.8075 16.7858 11.5922 16.7858 11.3674V7.485C16.7858 7.08113 16.9357 6.69399 17.1949 6.39492C17.0565 5.31821 16.8055 4.4962 16.4308 3.88984C15.6588 2.64029 14.1703 2.05835 11.7466 2.05835C9.73588 2.05835 8.3474 2.47574 7.50186 3.33434C6.84643 3.99994 6.46661 4.94967 6.28906 6.38401C6.55408 6.68421 6.70735 7.0753 6.70735 7.485V11.3674C6.70742 11.6904 6.61458 11.9919 6.45473 12.2473Z" fill="white" />
                <path d="M3.69984 12.2839H5.04388C5.54986 12.2839 5.96038 11.8735 5.96038 11.3673V7.48498C5.96038 7.14677 5.7767 6.8521 5.50422 6.6933C5.80434 3.51426 7.03111 1.31132 11.7465 1.31132C14.4544 1.31132 16.1448 2.00597 17.0662 3.49723C17.6024 4.36503 17.8603 5.48588 17.9808 6.69808C17.7126 6.858 17.5326 7.15006 17.5326 7.48491V11.3673C17.5326 11.8037 17.8379 12.1681 18.2464 12.2605C17.654 12.648 16.4274 13.0438 13.8875 13.0438C13.6818 13.0438 13.4674 13.0412 13.2441 13.0358C13.1133 12.7845 12.851 12.6125 12.5481 12.6125H11.4277C10.9947 12.6125 10.6434 12.9636 10.6434 13.3968C10.6434 13.8299 10.9947 14.1811 11.4277 14.1811H12.5481C12.8098 14.1811 13.041 14.0524 13.1834 13.8554C13.4193 13.8614 13.6502 13.8651 13.8734 13.8651C16.7363 13.865 18.5562 13.3764 19.2933 12.4069C19.3247 12.3656 19.3533 12.3245 19.3793 12.2839H19.7934C20.2994 12.2839 20.7098 11.8735 20.7098 11.3673V7.48498C20.7098 6.97886 20.2995 6.56849 19.7934 6.56849H19.2878C19.1456 5.1039 18.8303 3.85711 18.1818 2.80781C17.0145 0.918434 14.9094 0 11.7464 0C8.58332 0 6.47838 0.918434 5.31099 2.80789C4.66257 3.85718 4.34744 5.10382 4.20515 6.56857H3.69977C3.19357 6.56857 2.7832 6.97886 2.7832 7.48506V11.3674C2.7832 11.8735 3.19365 12.2839 3.69984 12.2839Z" fill="white" />
                <path d="M22.8268 23.7003C22.522 21.8055 21.8947 19.3648 20.6365 18.4976C19.7785 17.906 16.7833 16.3057 15.5043 15.6224L15.4775 15.608C15.3311 15.5298 15.1525 15.5455 15.022 15.6479C14.351 16.1746 13.6165 16.5294 12.8388 16.7024C12.7014 16.733 12.5885 16.8308 12.5386 16.9624L11.7461 19.0509L10.9537 16.9624C10.9037 16.8307 10.7909 16.733 10.6534 16.7024C9.8758 16.5294 9.14111 16.1746 8.47007 15.6479C8.33965 15.5454 8.16098 15.5298 8.01466 15.608C6.74994 16.2837 3.71596 17.9176 2.85937 18.4951C1.41039 19.4713 0.777583 23.0024 0.665468 23.7004C0.654338 23.7696 0.660762 23.8404 0.684141 23.9064C0.735979 24.0528 2.06508 27.4905 11.7461 27.4905C21.4269 27.4905 22.7561 24.0528 22.808 23.9065C22.8315 23.8403 22.8379 23.7694 22.8268 23.7003ZM18.3192 21.5244H14.4849V20.7169H18.3192V21.5244Z" fill="white" />
            </svg>
        ),
        description: "featuresData.2"
    },
    {
        icon: (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 0L14.7732 1.60691H17.987L19.594 4.40605L22.3931 6.01296V9.22678L24 12L22.3931 14.7732V17.987L19.594 19.594L17.987 22.3931H14.7732L12 24L9.22678 22.3931H6.01296L4.40605 19.594L1.60691 17.987V14.7732L0 12L1.60691 9.22678V6.01296L4.40605 4.40605L6.01296 1.60691H9.22678L12 0Z" fill="white" />
                <path d="M11.9991 20.7865C7.15241 20.7865 3.21289 16.8469 3.21289 12.0003C3.21289 7.15364 7.15241 3.21411 11.9991 3.21411C16.8457 3.21411 20.7852 7.15364 20.7852 12.0003C20.7852 16.8469 16.8457 20.7865 11.9991 20.7865ZM11.9991 3.96573C7.5671 3.96573 3.96451 7.56832 3.96451 12.0003C3.96451 16.4323 7.5671 20.0348 11.9991 20.0348C16.431 20.0348 20.0336 16.4323 20.0336 12.0003C20.0336 7.56832 16.431 3.96573 11.9991 3.96573Z" fill="#16171A" />
                <path d="M11.0424 15.2656L7.23251 12.2332C6.92149 11.974 6.86966 11.5334 7.12884 11.2224C7.38802 10.9114 7.82862 10.8596 8.13964 11.1187L11.4053 13.7364L15.7595 8.86387C16.0187 8.57878 16.4852 8.55286 16.7703 8.81204C17.0554 9.07122 17.0813 9.53774 16.8221 9.82284L12.0273 15.1878C11.7681 15.4729 11.3275 15.4989 11.0424 15.2656Z" fill="#16171A" />
            </svg>
        ),
        description: "featuresData.3"
    }
];

const WhyChooseUs = ({ t }) => {
    return (
        <>
            <section className="w-full">
                <div className="max-w-[1400px] mx-auto py-12 px-4 md:px-8 bg-white">
                    <div className="max-w-5xl mx-auto text-center">
                        <Typography
                            variant="h2"
                            className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                            {t('why_choose_ztechengineering.part1')}
                            <span className="text-secondary">
                                {t('why_choose_ztechengineering.part2')}
                            </span>
                            {t('why_choose_ztechengineering.part3')}
                        </Typography>
                        <p className="text-gray-600 text-sm md:text-base mb-8 max-w-3xl mx-auto">
                            {t('with_expertise')}
                        </p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                            {whyChooseData.map((item) => (
                                <Card
                                    key={item.id}
                                    className="bg-white rounded-lg border border-gray-100 shadow-md hover:shadow-lg transition-shadow duration-300 p-0 flex flex-col items-center gap-y-4"
                                >
                                    <img
                                        src={item.image}
                                        alt={t(item.title)}
                                        className="w-full h-48 object-cover rounded-md"
                                    />
                                    <Typography
                                        variant="h3"
                                        className="text-gray-800 font-semibold text-sm text-center p-4">
                                        {t(item.title)}
                                    </Typography>
                                </Card>
                            ))}
                        </div>
                        <p className="text-gray-600 text-sm md:text-base mt-8 max-w-3xl mx-auto">
                            {t('additional_services')}
                        </p>
                    </div>
                </div>
            </section>
            <section
                className="max-w-[1400px] mx-auto text-white py-16 px-4 bg-white">
                <div
                    style={{
                        backgroundImage: "url('/images/home/<USER>')",
                        backgroundRepeat: "no-repeat",
                        backgroundSize: "cover",
                        backgroundPosition: "center"
                    }}
                    className="w-full p-10 rounded-3xl ">
                    <div className="max-w-5xl mx-auto text-center">
                        <Typography variant="h1" className="md:max-w-3xl md:text-3xl text-2xl mx-auto font-bold mb-4">
                            {t('boost_your_business')}
                        </Typography>
                        <p className="text-base mb-12 max-w-3xl mx-auto">
                            {t('invest_in_reliable_hosting')}
                        </p>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 p-6 ">
                            {features.map((feature, index) => (
                                <div
                                    key={index}
                                    className="flex flex-col gap-y-4 justify-start items-start bg-gradient-to-b from-gray-900 to-gray-[#16171A] border border-black text-white p-2 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
                                >
                                    <div className="border-l-2 border-secondary">
                                        <div className="text-4xl ml-2 p-1 rounded-full bg-white bg-opacity-35" >
                                            {feature.icon}
                                        </div>
                                    </div>
                                    <p className="text-lg text-left text-white font-semibold">{t(feature.description)}</p>
                                </div>
                            ))}
                        </div>

                        <div className="flex flex-col gap-y-6 items-center justify-center">
                            <p className="text-base w-fit mx-auto my-6 px-6 bg-white bg-opacity-35 md:rounded-full rounded-2xl py-3">
                                {t('contact_title')}
                            </p>
                            <Link href="#contact-nous"
                                className="flex mx-auto p-2 hover:bg-secondary bg-white bg-opacity-35 items-center justify-between w-fit gap-x-5 text-white rounded-full">
                                <span className='text-base font-inter p-1'>{t('contact_description')}</span>
                                <span className='p-2 bg-black rounded-full border-[0.69px] border-[#2C52F7]'
                                    style={{ boxShadow: '0px 0px 19.61px 7.54px rgba(66, 99, 242, 0.39)' }}
                                >
                                    <BsArrowUpRight className='text-white' />
                                </span>
                            </Link>
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
};

export default WhyChooseUs;
