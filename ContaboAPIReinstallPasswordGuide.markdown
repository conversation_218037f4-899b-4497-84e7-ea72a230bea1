# Contabo API Reinstall Password Management Guide

This guide provides clear instructions for securely managing passwords in the Contabo API `reinstallInstance` action using the Secrets Management API. It ensures you use a `secretId` instead of plain text passwords, as required by Contabo's API for VPS reinstallation.

## 1. Store Password in Secrets Management API
To securely store a password and obtain its `secretId`, use the Secrets Management API.

**Endpoint**: `POST https://api.contabo.com/v1/secrets`

**Headers**:
- `Authorization: Bearer <access_token>`
- `x-request-id: <UUID4>` (e.g., `51A87ECD-754E-4104-9C54-D01AD0F83406`, generate using a UUID library)
- `Content-Type: application/json`

**Request Body**:
```json
{
  "name": "my-root-password",
  "type": "password",
  "value": "MySecurePassword123"
}
```

**Example Request**:
```bash
curl -X POST "https://api.contabo.com/v1/secrets" \
-H "Authorization: Bearer <access_token>" \
-H "x-request-id: 51A87ECD-754E-4104-9C54-D01AD0F83406" \
-H "Content-Type: application/json" \
-d '{"name":"my-root-password","type":"password","value":"MySecurePassword123"}'
```

**Response** (HTTP 201):
```json
{
  "data": [
    {
      "secretId": 123,
      "name": "my-root-password",
      "type": "password"
    }
  ],
  "_links": {
    "self": "/v1/secrets/123"
  }
}
```

Save the `secretId` (e.g., `123`) for use in the reinstall action.

## 2. Use `secretId` in Reinstall Instance Action
For the `reinstallInstance` action, set the `rootPassword` field to the `secretId`, not the plain password.

**Endpoint**: `POST https://api.contabo.com/v1/compute/instances/{instanceId}/reinstall`

**Request Body**:
```json
{
  "imageId": "3f184ab8-a600-4e7c-8c9b-3413e21a3752",
  "sshKeys": [123, 125],
  "rootPassword": 123,
  "userData": "#cloud-config\nuser: root\nssh_pwauth: true",
  "defaultUser": "root"
}
```

**Important**:
- Correct: `"rootPassword": 123` (integer `secretId`)
- Incorrect: `"rootPassword": "MySecurePassword123"` (plain text password)

## 3. Verify Your Reinstall Implementation
To ensure your `reinstallInstance` implementation is correct:
- Check that `rootPassword` is an integer (`secretId`), not a string (plain password).
- If you are sending plain passwords, update your code to:
  1. Store the password using `POST /v1/secrets`.
  2. Use the returned `secretId` in the `rootPassword` field.

## 4. Additional Notes
- **List Secrets**: Use `GET https://api.contabo.com/v1/secrets` to retrieve stored secrets and their `secretId`s.
- **Error Handling**: Ensure the `secretId` is valid. If the request fails (e.g., 400 or 404), verify the secret exists using the GET endpoint.
- **Documentation**: Refer to [Contabo API Secrets Management](https://api.contabo.com/#tag/Secrets) for further details.

This guide ensures secure password handling for the Contabo API `reinstallInstance` action, aligning with Contabo's security requirements.