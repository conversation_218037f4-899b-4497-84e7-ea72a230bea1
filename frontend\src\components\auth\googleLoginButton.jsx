import React from "react";
import { GoogleLogin } from "react-google-login";

const GoogleLoginButton = () => {
    const handleLoginSuccess = async (response) => {
        const tokenId = response.tokenId;

        // Send token to the backend to authenticate and get the JWT token
        const res = await fetch("/auth/google/callback", {
            method: "GET", // Use GET to trigger the callback route
            headers: {
                Authorization: `Bearer ${tokenId}`,
            },
        });

        if (res.ok) {
            // Optionally handle a successful response, such as redirecting to the homepage
            window.location.href = "/";
        } else {
            // Handle error (e.g. display a message to the user)
            console.log("Google authentication failed");
        }
    };

    const handleLoginFailure = (error) => {
        console.log("Google login failed", error);
    };

    return (
        <div>
            <GoogleLogin
                clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}
                buttonText="Login with Google"
                onSuccess={handleLoginSuccess}
                onFailure={handleLoginFailure}
                cookiePolicy="single_host_origin"
                responseType="code"
                isSignedIn={true}
            />
        </div>
    );
};

export default GoogleLoginButton;
