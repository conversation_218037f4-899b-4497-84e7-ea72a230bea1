import { Typography } from "@material-tailwind/react";
import Link from "next/link";
import { BsArrowUpRight } from "react-icons/bs";
import MobileCarousel from "./mobileCarousel";
import { Jim_Nightshade } from "next/font/google";
import {
  CLOUDSERVERsvg,
  INFOGERANCEsvg,
  SERVERSsvg,
  SSLCERTIFICATIONsvg,
} from "../../icons/svgIcons";
import { Target } from "lucide-react";

const jimNightshade = Jim_Nightshade({
  weight: "400",
  subsets: ["latin"],
});

const SecondarySericesCard = ({ iconSrc, title, description, url, t }) => (
  <div
    className={`group relative text-start flex flex-col border-[0.97px] shadow-md rounded-lg p-4 border-secondary 
        overflow-hidden transition-all duration-500 ease-in-out text-gray-800 hover:text-white bg-white`}
  >
    <div
      className={`absolute inset-0 bg-gradient-secondary scale-y-0 origin-bottom transition-transform duration-500 ease-in-out group-hover:scale-y-100`}
    ></div>

    <div className="relative z-10">
      <div className="bg-white flex justify-start mb-4 w-fit p-2 rounded-lg">
        {iconSrc}
      </div>
      <div className="flex-grow h-fit">
        <Typography className="font-semibold mb-2 text-lg font-inter">
          {t(title)}
        </Typography>
        <p className="font-inter">{t(description)}</p>
      </div>

      <Link
        href={url}
        className="text-blue-600 group-hover:text-white font-medium mt-4 flex items-center gap-1 text-xs uppercase transition-all duration-300"
      >
        <span className="group-hover:underline">{t("voir_plus")} {t(title)}</span>
        <BsArrowUpRight className="text-sm transition-transform duration-300 group-hover:translate-x-1" />
      </Link>
    </div>
  </div>
);

const SecondarySericesData = [
  {
    iconSrcold: "/images/home/<USER>",
    iconSrc: <CLOUDSERVERsvg />,
    url: "/cloud-security",
    title: "cloud_security.title",
    description: "cloud_security.description",
  },
  {
    iconSrc: <INFOGERANCEsvg />,
    url: "/managed-services",
    title: "cloud_management.title",
    description: "cloud_management.description",
  },
  {
    iconSrc: <SSLCERTIFICATIONsvg />,
    url: "/ssl",
    title: "ssl.title",
    description: "ssl.description",
  },
  {
    iconSrc: <SERVERSsvg width={36} />,
    url: "/hosting/dedicated",
    title: "servers.title",
    description: "servers.description",
  },
];

const OtherServices = ({ t }) => {
  return (
    <div className="bg-gradient-to-b from-transparent to-white w-full">
      <div className="max-w-[1400px] mx-auto text-center mt-4 py-10 items-center rounded">
        <div className="flex flex-col items-center justify-center">
        <Typography variant="h1" className="flex items-center gap-1 text-sm font-inter bg-[#5956E9] w-fit p-2 px-3 text-white font-normal rounded-full shadow-md">
          <Target className="w-4 h-4" />
          {t("our_services")}
        </Typography>
        <Typography
          variant="h3"
          className="md:text-[26px] text-2xl font-normal mt-2 md:w-1/2 w-full px-2 md:px-0 mx-auto"
        >
          {t("boost_security_performance")}
        </Typography>
        <Typography
          className={`md:text-3xl text-xl py-2 md:py-0 font-light italic text-blue-900 ${jimNightshade.className}`}
        >
          {t("information_technology")}
        </Typography>
        </div>
        

        <div className="hidden md:flex justify-between gap-0 m-0 md:gap-6 md:p-4 p-0 mx-auto">
          {SecondarySericesData.map((card, index) => (
            <SecondarySericesCard
              key={index}
              iconSrc={card.iconSrc}
              title={card.title}
              description={card.description}
              url={card.url}
              t={t}
            />
          ))}
        </div>
        <MobileCarousel SecondarySericesData={SecondarySericesData} t={t} />
      </div>
    </div>
  );
};

export default OtherServices;
