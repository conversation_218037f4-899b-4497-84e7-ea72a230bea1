import { <PERSON>R<PERSON>, PhoneIcon } from "lucide-react";
import Link from "next/link";

const CTAButtons = ({ t }) => (
  <div className="flex flex-col sm:flex-row gap-4">
    <Link
      href="#contact-nous"
      className="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-indigo-600 rounded-full hover:bg-indigo-700 transition-all duration-300"
    >
      {t("request_quote")}
      <ArrowRight className="w-5 h-5 ml-2" />
    </Link>
    <Link
      href="tel:+212662841605"
      className="inline-flex items-center px-6 py-3 text-base font-medium text-indigo-600 border-2 border-indigo-600 rounded-full hover:bg-indigo-50 transition-all duration-300"
    >
      <PhoneIcon className="w-5 h-5 mr-2" />
      +212 662 841 605
    </Link>
  </div>
);

export default CTAButtons;