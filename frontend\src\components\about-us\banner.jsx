import Image from 'next/image'
import React from 'react'
import imgIntro from "/public/images/about-us-banner.webp";


function Banner({ t }) {
    return (
        <div
            className='w-full'>
            <div className="max-w-[1400px] mx-auto flex flex-col md:flex-row justify-between md:gap-x-10 gap-y-10 items-center mb-10 md:mb-0">
                <div className="flex flex-col gap-y-6 mx-auto md:w-1/2 mt-8 md:mt-0">
                    <p className='bg-secondary text-white rounded-3xl px-4 py-2 border border-secondary text-base w-fit mx-auto'>
                        {t('title')}
                    </p>
                    <p className='text-center text-black md:max-w-2xl text-base mx-auto'>
                        {t('description')}
                    </p>
                </div>
                <div className="">
                    {/* <img
                        src="/images/about-us-banner.webp"
                        alt="À propos de nous"
                    /> */}
                    <Image
                        src={imgIntro}
                        alt="À propos de nous"
                        width={500}
                        height={700}
                        quality={100}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        placeholder='blur'
                        priority
                    />
                </div>
            </div>
        </div>
    )
}

export default Banner