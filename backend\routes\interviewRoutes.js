const express = require('express');
const router = express.Router();
const interviewController = require('../controllers/interviewController');
const { adminMiddleware } = require('../midelwares/authorization');

// Apply admin authentication middleware to all routes
router.use(adminMiddleware);

// Interview routes
router.post('/', interviewController.createInterview);
router.get('/', interviewController.getInterviews);
router.get('/:id', interviewController.getInterviewById);
router.put('/:id', interviewController.updateInterview);
router.delete('/:id', interviewController.deleteInterview);

// Get interviews for a specific application
router.get('/application/:applicationId', interviewController.getApplicationInterviews);

module.exports = router;
