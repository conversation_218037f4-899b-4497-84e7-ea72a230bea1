import { X, AlertCircle, Plus, XCircle, Globe, Languages } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import './styles.css';

function JobFormModal({
  showForm,
  setShowForm,
  formData,
  setFormData,
  handleSubmit,
  editingId,
  setEditingId,
  handleArrayInput,
  addArrayItem,
  removeArrayItem,
  error
}) {
  const [activeLanguage, setActiveLanguage] = useState('en');
  const [showBothLanguages, setShowBothLanguages] = useState(false);

  if (!showForm) return null;

  // Animation variants for modal
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: { opacity: 1, scale: 1, transition: { duration: 0.2 } },
    exit: { opacity: 0, scale: 0.95, transition: { duration: 0.15 } }
  };

  // Initialize multilingual fields if they don't exist
  const ensureMultilingualFields = () => {
    const updatedFormData = { ...formData };
    
    // Convert simple fields to multilingual objects if needed
    if (typeof updatedFormData.title === 'string') {
      updatedFormData.title = { en: updatedFormData.title, fr: '' };
    }
    if (typeof updatedFormData.description === 'string') {
      updatedFormData.description = { en: updatedFormData.description, fr: '' };
    }
    
    // Convert array fields to multilingual objects if needed
    ['requirements', 'responsibilities', 'benefits'].forEach(field => {
      if (Array.isArray(updatedFormData[field]) && !updatedFormData[field][0]?.hasOwnProperty('en')) {
        updatedFormData[field] = updatedFormData[field].map(item => 
          typeof item === 'string' ? { en: item, fr: '' } : item
        );
      }
    });
    
    return updatedFormData;
  };

  // Handle language toggle
  const toggleLanguage = () => {
    if (showBothLanguages) {
      setShowBothLanguages(false);
      return;
    }
    setActiveLanguage(activeLanguage === 'en' ? 'fr' : 'en');
    setFormData(ensureMultilingualFields());
  };

  // Toggle showing both languages
  const toggleBothLanguages = () => {
    setShowBothLanguages(!showBothLanguages);
    setFormData(ensureMultilingualFields());
  };

  // Handle text input for multilingual fields
  const handleMultilingualInput = (field, value, lang = activeLanguage) => {
    setFormData({
      ...formData,
      [field]: {
        ...formData[field],
        [lang]: value
      }
    });
  };

  // Handle array input for multilingual fields
  const handleMultilingualArrayInput = (field, index, value, lang = activeLanguage) => {
    const newArray = [...formData[field]];
    newArray[index] = {
      ...newArray[index],
      [lang]: value
    };
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  // Add array item for multilingual fields
  const addMultilingualArrayItem = (field) => {
    setFormData({
      ...formData,
      [field]: [...formData[field], { en: '', fr: '' }]
    });
  };

  // Render input field for both languages or single language
  const renderMultilingualInput = (field, placeholder, isTextarea = false) => {
    if (showBothLanguages) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="flex items-center mb-2">
              <span className="text-xs font-semibold bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">EN</span>
              <label className="block text-sm font-medium text-gray-700">
                {placeholder.en}
              </label>
            </div>
            {isTextarea ? (
              <textarea
                rows={5}
                value={typeof formData[field] === 'object' ? formData[field].en || '' : ''}
                onChange={(e) => handleMultilingualInput(field, e.target.value, 'en')}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                placeholder={`${placeholder.en}...`}
              ></textarea>
            ) : (
              <input
                type="text"
                value={typeof formData[field] === 'object' ? formData[field].en || '' : ''}
                onChange={(e) => handleMultilingualInput(field, e.target.value, 'en')}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                placeholder={`${placeholder.en}...`}
              />
            )}
          </div>
          <div>
            <div className="flex items-center mb-2">
              <span className="text-xs font-semibold bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">FR</span>
              <label className="block text-sm font-medium text-gray-700">
                {placeholder.fr}
              </label>
            </div>
            {isTextarea ? (
              <textarea
                rows={5}
                value={typeof formData[field] === 'object' ? formData[field].fr || '' : ''}
                onChange={(e) => handleMultilingualInput(field, e.target.value, 'fr')}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                placeholder={`${placeholder.fr}...`}
              ></textarea>
            ) : (
              <input
                type="text"
                value={typeof formData[field] === 'object' ? formData[field].fr || '' : ''}
                onChange={(e) => handleMultilingualInput(field, e.target.value, 'fr')}
                className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                placeholder={`${placeholder.fr}...`}
              />
            )}
          </div>
        </div>
      );
    }

    return isTextarea ? (
      <textarea
        required
        rows={5}
        value={typeof formData[field] === 'object' ? formData[field][activeLanguage] || '' : formData[field] || ''}
        onChange={(e) => handleMultilingualInput(field, e.target.value)}
        className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400"
        placeholder={activeLanguage === 'en' ? placeholder.en : placeholder.fr}
      ></textarea>
    ) : (
      <input
        type="text"
        required
        value={typeof formData[field] === 'object' ? formData[field][activeLanguage] || '' : formData[field] || ''}
        onChange={(e) => handleMultilingualInput(field, e.target.value)}
        className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400"
        placeholder={activeLanguage === 'en' ? placeholder.en : placeholder.fr}
      />
    );
  };

  // Render array input field for both languages or single language
  const renderMultilingualArrayInput = (field, index, placeholders) => {
    if (showBothLanguages) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
          <div className="flex items-center space-x-3">
            <span className="text-xs font-semibold bg-blue-100 text-blue-800 px-2 py-1 rounded">EN</span>
            <input
              type="text"
              value={typeof formData[field][index] === 'object' ? formData[field][index].en || '' : ''}
              onChange={(e) => handleMultilingualArrayInput(field, index, e.target.value, 'en')}
              className="flex-grow px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
              placeholder={`${placeholders.en} ${index + 1}`}
            />
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-xs font-semibold bg-blue-100 text-blue-800 px-2 py-1 rounded">FR</span>
            <input
              type="text"
              value={typeof formData[field][index] === 'object' ? formData[field][index].fr || '' : ''}
              onChange={(e) => handleMultilingualArrayInput(field, index, e.target.value, 'fr')}
              className="flex-grow px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
              placeholder={`${placeholders.fr} ${index + 1}`}
            />
            {formData[field].length > 1 && (
              <button
                type="button"
                onClick={() => removeArrayItem(field, index)}
                className="p-2 rounded-full hover:bg-red-50 transition-colors duration-200"
                aria-label={`Remove ${field} item`}
              >
                <XCircle className="h-5 w-5 text-red-500 hover:text-red-700" />
              </button>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-3 mb-3">
        <input
          type="text"
          value={typeof formData[field][index] === 'object' ? formData[field][index][activeLanguage] || '' : formData[field][index] || ''}
          onChange={(e) => handleMultilingualArrayInput(field, index, e.target.value)}
          className="flex-grow px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
          placeholder={activeLanguage === 'en' ? `${placeholders.en} ${index + 1}` : `${placeholders.fr} ${index + 1}`}
        />
        {formData[field].length > 1 && (
          <button
            type="button"
            onClick={() => removeArrayItem(field, index)}
            className="p-2 rounded-full hover:bg-red-50 transition-colors duration-200"
            aria-label={`Remove ${field} item`}
          >
            <XCircle className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        )}
      </div>
    );
  };

  return (
    <AnimatePresence>
      {showForm && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 p-4 backdrop-blur-sm"
        >
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className="bg-white p-8 rounded-2xl shadow-2xl max-h-[90vh] overflow-y-auto w-full max-w-3xl sm:max-w-2xl"
          >
            <div className="flex justify-between items-center mb-6 border-b border-gray-200 pb-4">
              <h3 className="text-2xl font-bold text-gray-900">
                {editingId ? 'Edit Job Listing' : 'Create New Job Listing'}
              </h3>
              <div className="flex items-center gap-4">
                <button
                  onClick={toggleBothLanguages}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-lg ${showBothLanguages ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 hover:bg-gray-200'} transition-colors`}
                  title="Toggle dual language mode"
                >
                  <Languages className="h-4 w-4" />
                  <span className="font-medium">EN/FR</span>
                </button>
                {!showBothLanguages && (
                  <button
                    onClick={toggleLanguage}
                    className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                  >
                    <Globe className="h-4 w-4" />
                    <span className="font-medium">{activeLanguage === 'en' ? 'English' : 'Français'}</span>
                  </button>
                )}
                <button
                  onClick={() => {
                    setShowForm(false);
                    setFormData({
                      title: '',
                      department: '',
                      location: '',
                      type: 'full-time',
                      description: '',
                      requirements: [''],
                      responsibilities: [''],
                      benefits: [''],
                      salary_range: null,
                      status: 'draft'
                    });
                  }}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
                  aria-label="Close modal"
                >
                  <X className="h-5 w-5 text-gray-500 hover:text-gray-700" />
                </button>
              </div>
            </div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 text-red-700 p-4 rounded-lg flex items-center mb-6 border border-red-100"
              >
                <AlertCircle className="h-5 w-5 mr-3 text-red-600" />
                <span className="text-sm font-medium">{error}</span>
              </motion.div>
            )}

            <div className="mb-6 bg-blue-50 p-4 rounded-lg border border-blue-100">
              <p className="text-sm text-blue-700">
                {showBothLanguages 
                  ? 'You are editing both languages simultaneously. All fields will show English and French inputs.'
                  : activeLanguage === 'en' 
                    ? 'You are currently editing in English. Toggle to French to add translations or use EN/FR mode to edit both languages at once.' 
                    : 'Vous modifiez actuellement en français. Basculez vers l\'anglais pour ajouter des traductions ou utilisez le mode EN/FR pour modifier les deux langues à la fois.'}
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {!showBothLanguages ? (activeLanguage === 'en' ? 'Job Title' : 'Titre du poste') : 'Job Title / Titre du poste'} <span className="text-red-500">*</span>
                  </label>
                  {renderMultilingualInput('title', {en: 'e.g. Senior Software Engineer', fr: 'ex. Ingénieur Logiciel Senior'})}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {!showBothLanguages ? (activeLanguage === 'en' ? 'Department' : 'Département') : 'Department / Département'} <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.department}
                    onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                    className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400"
                    placeholder={!showBothLanguages ? (activeLanguage === 'en' ? "e.g. Engineering" : "ex. Ingénierie") : "e.g. Engineering / Ingénierie"}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {!showBothLanguages ? (activeLanguage === 'en' ? 'Location' : 'Lieu') : 'Location / Lieu'} <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400"
                    placeholder={!showBothLanguages ? (activeLanguage === 'en' ? "e.g. Casablanca or Remote" : "ex. Casablanca ou Télétravail") : "e.g. Casablanca or Remote / Casablanca ou Télétravail"}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {activeLanguage === 'en' ? 'Employment Type' : 'Type d\'emploi'}
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                    className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                  >
                    <option value="full-time">{activeLanguage === 'en' ? 'Full Time' : 'Temps plein'}</option>
                    <option value="part-time">{activeLanguage === 'en' ? 'Part Time' : 'Temps partiel'}</option>
                    <option value="contract">{activeLanguage === 'en' ? 'Contract' : 'Contrat'}</option>
                    <option value="internship">{activeLanguage === 'en' ? 'Internship' : 'Stage'}</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {activeLanguage === 'en' ? 'Description' : 'Description'} <span className="text-red-500">*</span>
                </label>
                {renderMultilingualInput('description', {en: 'Describe the role and its key objectives...', fr: 'Décrivez le rôle et ses objectifs principaux...'}, true)}
              </div>

              {/* Requirements */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {activeLanguage === 'en' ? 'Requirements' : 'Prérequis'}
                </label>
                {formData.requirements.map((req, index) => (
                  renderMultilingualArrayInput('requirements', index, {en: `Requirement ${index + 1}`, fr: `Prérequis ${index + 1}`})
                ))}
                <button
                  type="button"
                  onClick={() => addMultilingualArrayItem('requirements')}
                  className="mt-3 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200"
                >
                  <Plus className="h-4 w-4 mr-1" /> {activeLanguage === 'en' ? 'Add Requirement' : 'Ajouter un prérequis'}
                </button>
              </div>

              {/* Responsibilities */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {activeLanguage === 'en' ? 'Responsibilities' : 'Responsabilités'}
                </label>
                {formData.responsibilities.map((resp, index) => (
                  renderMultilingualArrayInput('responsibilities', index, {en: `Responsibility ${index + 1}`, fr: `Responsabilité ${index + 1}`})
                ))}
                <button
                  type="button"
                  onClick={() => addMultilingualArrayItem('responsibilities')}
                  className="mt-3 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200"
                >
                  <Plus className="h-4 w-4 mr-1" /> {activeLanguage === 'en' ? 'Add Responsibility' : 'Ajouter une responsabilité'}
                </button>
              </div>

              {/* Benefits */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {activeLanguage === 'en' ? 'Benefits' : 'Avantages'}
                </label>
                {formData.benefits.map((benefit, index) => (
                  renderMultilingualArrayInput('benefits', index, {en: `Benefit ${index + 1}`, fr: `Avantage ${index + 1}`})
                ))}
                <button
                  type="button"
                  onClick={() => addMultilingualArrayItem('benefits')}
                  className="mt-3 inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors duration-200"
                >
                  <Plus className="h-4 w-4 mr-1" /> {activeLanguage === 'en' ? 'Add Benefit' : 'Ajouter un avantage'}
                </button>
              </div>

              {/* Salary Range */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {activeLanguage === 'en' ? 'Min Salary' : 'Salaire minimum'}
                  </label>
                  <input
                    type="number"
                    value={formData.salary_range?.min || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      salary_range: { ...formData.salary_range, min: parseFloat(e.target.value) || null }
                    })}
                    className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400"
                    placeholder={activeLanguage === 'en' ? "e.g. 60000" : "ex. 60000"}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {activeLanguage === 'en' ? 'Max Salary' : 'Salaire maximum'}
                  </label>
                  <input
                    type="number"
                    value={formData.salary_range?.max || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      salary_range: { ...formData.salary_range, max: parseFloat(e.target.value) || null }
                    })}
                    className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900 placeholder-gray-400"
                    placeholder={activeLanguage === 'en' ? "e.g. 80000" : "ex. 80000"}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {activeLanguage === 'en' ? 'Currency' : 'Devise'}
                  </label>
                  <select
                    value={formData.salary_range?.currency || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      salary_range: { ...formData.salary_range, currency: e.target.value }
                    })}
                    className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                  >
                    <option value="">Select Currency</option>
                    <option value="MAD">MAD</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="CAD">CAD</option>
                  </select>
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 text-gray-900"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingId(null);
                    setFormData({
                      title: '',
                      department: '',
                      location: '',
                      type: 'full-time',
                      description: '',
                      requirements: [''],
                      responsibilities: [''],
                      benefits: [''],
                      salary_range: null,
                      status: 'draft'
                    });
                  }}
                  className="px-5 py-2.5 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-5 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300 disabled:cursor-not-allowed"
                  disabled={!formData.title || !formData.department || !formData.location || !formData.description}
                >
                  {editingId ? 'Update Job' : 'Create Job'}
                </button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default JobFormModal;
