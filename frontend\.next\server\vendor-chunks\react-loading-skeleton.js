"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-loading-skeleton";
exports.ids = ["vendor-chunks/react-loading-skeleton"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-loading-skeleton/dist/skeleton.css":
/*!***************************************************************!*\
  !*** ./node_modules/react-loading-skeleton/dist/skeleton.css ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b6d9984fa191\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbG9hZGluZy1za2VsZXRvbi9kaXN0L3NrZWxldG9uLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvcmVhY3QtbG9hZGluZy1za2VsZXRvbi9kaXN0L3NrZWxldG9uLmNzcz82MjgzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjZkOTk4NGZhMTkxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-loading-skeleton/dist/skeleton.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-loading-skeleton/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-loading-skeleton/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SkeletonTheme: () => (/* binding */ SkeletonTheme),\n/* harmony export */   \"default\": () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ SkeletonTheme,default auto */ \n/**\n * @internal\n */ const SkeletonThemeContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({});\n/* eslint-disable react/no-array-index-key */ const defaultEnableAnimation = true;\n// For performance & cleanliness, don't add any inline styles unless we have to\nfunction styleOptionsToCssProperties({ baseColor, highlightColor, width, height, borderRadius, circle, direction, duration, enableAnimation = defaultEnableAnimation, customHighlightBackground }) {\n    const style = {};\n    if (direction === \"rtl\") style[\"--animation-direction\"] = \"reverse\";\n    if (typeof duration === \"number\") style[\"--animation-duration\"] = `${duration}s`;\n    if (!enableAnimation) style[\"--pseudo-element-display\"] = \"none\";\n    if (typeof width === \"string\" || typeof width === \"number\") style.width = width;\n    if (typeof height === \"string\" || typeof height === \"number\") style.height = height;\n    if (typeof borderRadius === \"string\" || typeof borderRadius === \"number\") style.borderRadius = borderRadius;\n    if (circle) style.borderRadius = \"50%\";\n    if (typeof baseColor !== \"undefined\") style[\"--base-color\"] = baseColor;\n    if (typeof highlightColor !== \"undefined\") style[\"--highlight-color\"] = highlightColor;\n    if (typeof customHighlightBackground === \"string\") style[\"--custom-highlight-background\"] = customHighlightBackground;\n    return style;\n}\nfunction Skeleton({ count = 1, wrapper: Wrapper, className: customClassName, containerClassName, containerTestId, circle = false, style: styleProp, ...originalPropsStyleOptions }) {\n    var _a, _b, _c;\n    const contextStyleOptions = react__WEBPACK_IMPORTED_MODULE_0__.useContext(SkeletonThemeContext);\n    const propsStyleOptions = {\n        ...originalPropsStyleOptions\n    };\n    // DO NOT overwrite style options from the context if `propsStyleOptions`\n    // has properties explicity set to undefined\n    for (const [key, value] of Object.entries(originalPropsStyleOptions)){\n        if (typeof value === \"undefined\") {\n            delete propsStyleOptions[key];\n        }\n    }\n    // Props take priority over context\n    const styleOptions = {\n        ...contextStyleOptions,\n        ...propsStyleOptions,\n        circle\n    };\n    // `styleProp` has the least priority out of everything\n    const style = {\n        ...styleProp,\n        ...styleOptionsToCssProperties(styleOptions)\n    };\n    let className = \"react-loading-skeleton\";\n    if (customClassName) className += ` ${customClassName}`;\n    const inline = (_a = styleOptions.inline) !== null && _a !== void 0 ? _a : false;\n    const elements = [];\n    const countCeil = Math.ceil(count);\n    for(let i = 0; i < countCeil; i++){\n        let thisStyle = style;\n        if (countCeil > count && i === countCeil - 1) {\n            // count is not an integer and we've reached the last iteration of\n            // the loop, so add a \"fractional\" skeleton.\n            //\n            // For example, if count is 3.5, we've already added 3 full\n            // skeletons, so now we add one more skeleton that is 0.5 times the\n            // original width.\n            const width = (_b = thisStyle.width) !== null && _b !== void 0 ? _b : \"100%\"; // 100% is the default since that's what's in the CSS\n            const fractionalPart = count % 1;\n            const fractionalWidth = typeof width === \"number\" ? width * fractionalPart : `calc(${width} * ${fractionalPart})`;\n            thisStyle = {\n                ...thisStyle,\n                width: fractionalWidth\n            };\n        }\n        const skeletonSpan = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n            className: className,\n            style: thisStyle,\n            key: i\n        }, \"‌\");\n        if (inline) {\n            elements.push(skeletonSpan);\n        } else {\n            // Without the <br />, the skeleton lines will all run together if\n            // `width` is specified\n            elements.push(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                key: i\n            }, skeletonSpan, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"br\", null)));\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: containerClassName,\n        \"data-testid\": containerTestId,\n        \"aria-live\": \"polite\",\n        \"aria-busy\": (_c = styleOptions.enableAnimation) !== null && _c !== void 0 ? _c : defaultEnableAnimation\n    }, Wrapper ? elements.map((el, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Wrapper, {\n            key: i\n        }, el)) : elements);\n}\nfunction SkeletonTheme({ children, ...styleOptions }) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SkeletonThemeContext.Provider, {\n        value: styleOptions\n    }, children);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-loading-skeleton/dist/index.js\n");

/***/ })

};
;