#!/usr/bin/env node

/**
 * Script to find VPS orders specifically
 * Usage: node findVPSOrders.js [limit]
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

// Import models
const Order = require('../models/Order');
const SubOrder = require('../models/SubOrder');
const User = require('../models/User');
const Package = require('../models/Package');

// Connect to database
async function connectDB() {
  try {
    // Use local MongoDB with zn_ztech database
    const localMongoUri = 'mongodb://localhost:27017/zn_ztech';
    
    await mongoose.connect(localMongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to local MongoDB (zn_ztech database)');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    console.error('Make sure MongoDB is running locally on port 27017');
    process.exit(1);
  }
}

// Function to find VPS orders
async function findVPSOrders(limit = 20) {
  try {
    console.log(`🖥️  Searching for VPS orders...\n`);
    
    // Find orders with VPS suborders
    const vpsOrders = await Order.find()
      .populate('user', 'firstName lastName email')
      .populate({
        path: 'subOrders',
        populate: { path: 'package', model: 'Package' }
      })
      .sort({ createdAt: -1 })
      .limit(limit * 3); // Get more orders to filter through

    // Filter orders that have VPS suborders
    const ordersWithVPS = vpsOrders.filter(order => 
      order.subOrders.some(subOrder => subOrder.vps !== null && subOrder.vps !== undefined)
    );

    if (ordersWithVPS.length === 0) {
      console.log('❌ No VPS orders found in the database');
      console.log('\n💡 To create a VPS order:');
      console.log('   1. Go to your frontend VPS configuration page');
      console.log('   2. Configure a VPS and add to cart');
      console.log('   3. Place the order (but don\'t pay yet)');
      console.log('   4. Then use the payment scripts to test provisioning');
      return;
    }

    console.log(`Found ${ordersWithVPS.length} VPS orders:\n`);
    console.log('═'.repeat(120));

    ordersWithVPS.slice(0, limit).forEach((order, index) => {
      const paidIcon = order.isPaid ? '✅' : '❌';
      
      console.log(`${index + 1}. 🖥️  ${order.identifiant} ${paidIcon}`);
      console.log(`   ID: ${order._id}`);
      console.log(`   User: ${order.user.firstName} ${order.user.lastName} (${order.user.email})`);
      console.log(`   Status: ${order.status} | Paid: ${order.isPaid} | Price: ${order.totalPrice} ${order.currency || 'MAD'}`);
      console.log(`   Created: ${order.createdAt.toLocaleString()}`);
      
      if (order.isPaid && order.datePaid) {
        console.log(`   Paid Date: ${order.datePaid.toLocaleString()}`);
      }
      
      if (order.transactionId) {
        console.log(`   Transaction: ${order.transactionId}`);
      }

      // Show VPS suborders info
      const vpsSubOrders = order.subOrders.filter(subOrder => subOrder.vps);
      console.log(`   VPS SubOrders (${vpsSubOrders.length}):`);
      
      vpsSubOrders.forEach((subOrder, subIndex) => {
        const packageName = subOrder.package?.name || 'Unknown Package';
        console.log(`     ${subIndex + 1}. ${packageName} - ${subOrder.status}`);
        
        if (subOrder.vps) {
          console.log(`        Provider: ${subOrder.vps.provider || 'N/A'}`);
          console.log(`        Plan ID: ${subOrder.vps.planId || 'N/A'}`);
          console.log(`        VPS Status: ${subOrder.vps.status || 'N/A'}`);
          console.log(`        Region: ${subOrder.vps.region || 'N/A'}`);
          console.log(`        OS: ${subOrder.vps.operatingSystem || 'N/A'}`);
          console.log(`        Display Name: ${subOrder.vps.displayName || 'N/A'}`);
          
          if (subOrder.vps.ipAddress) {
            console.log(`        IP Address: ${subOrder.vps.ipAddress}`);
          }
          if (subOrder.vps.providerInstanceId) {
            console.log(`        Provider Instance ID: ${subOrder.vps.providerInstanceId}`);
          }
          if (subOrder.vps.rootPassword) {
            console.log(`        Root Password: ${subOrder.vps.rootPassword}`);
          }
        }
      });
      
      console.log('─'.repeat(120));
    });

    console.log('\n💡 To test VPS provisioning with these orders:');
    console.log('   node markOrderAsPaid.js <ORDER_ID_OR_IDENTIFIANT>');
    console.log('   node quickPayOrder.js <ORDER_ID_OR_IDENTIFIANT>');
    
    console.log('\n🖥️  = VPS Order | ✅ = Paid | ❌ = Unpaid');

  } catch (error) {
    console.error('❌ Error finding VPS orders:', error);
    return false;
  }
}

// Main execution
async function main() {
  const limit = parseInt(process.argv[2]) || 10;

  if (limit < 1 || limit > 50) {
    console.error('❌ Limit must be between 1 and 50');
    process.exit(1);
  }

  await connectDB();
  
  await findVPSOrders(limit);
  
  // Close database connection
  await mongoose.connection.close();
  console.log('\n🔌 Database connection closed');
}

// Handle script execution
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
}

module.exports = { findVPSOrders };
