'use client'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { BsArrowUpRight } from 'react-icons/bs'
import landingHeroImg from "/public/images/landing/banner-landing-a.webp";

function Banner({ t }) {
  return (
    <div
      className="w-full bg-custom-gradient"
      style={{
        backgroundImage: "url('/images/landing/landing-banner.webp')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "center"
      }}>

      <div className="max-w-[1400px] mx-auto text-white xl:py-20 py-10 flex flex-col items-center justify-center">
        <div className="flex flex-col gap-y-6">
          <Typography
            variant='h1'
            className="text-center text-3xl 2xl:text-4xl font-extrabold font-inter max-w-xl mx-auto my-2">
            {t('a_title')}
          </Typography>
          <p className="text-[15px] 2xl:text-base text-center max-w-3xl">
            {t('a_desc')}
          </p>
          <div className="block mx-auto w-fit rounded-full bg-opacity-30 bg-gray-500 px-1 py-1">
            <Link
              href="#contact-nous"
              className="flex items-center justify-between w-fit gap-x-5 text-white rounded-full">
              <span className='text-base xl:text-base font-inter p-1'>{t('request_quote')}</span>
              <span className='p-2 bg-black rounded-full border-[0.69px] border-[#2C52F7]'
                style={{ boxShadow: '0px 0px 19.61px 7.54px rgba(66, 99, 242, 0.39)' }}
              >
                <BsArrowUpRight className='text-white' />
              </span>
            </Link>
          </div>
        </div>

        <div className="mx-auto mt-10 overflow-clip max-w-5xl  max-h-[350px] rounded-3xl">
          <Image
            src={landingHeroImg}
            alt="Engineer working on server"
            width={900}
            height={400}
            quality={100}
            className="rounded-3xl shadow-lg object-cover"
            priority
          />
        </div>
      </div>
    </div>
  )
}

export default Banner