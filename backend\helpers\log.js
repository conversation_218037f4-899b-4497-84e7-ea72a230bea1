const { createLogger, transports, format } = require("winston");
const logger = createLogger({
  format: format.combine(
    format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
    format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`)
  ),
  transports: [
    new transports.File({
      filename: "./logs/logs1.log",
      json: false,
      maxsize: 5242880,
      maxFiles: 5,
    }),
    new transports.Console(),
  ],
});

const printLog = (type, message, date) => {
  if (type == "info") {
    if (date) {
      logger.info(message + "-" + date);
    } else {
      const date = new Date();
      logger.info(
        message +
          " " +
          date.toLocaleDateString() +
          ":" +
          date.toLocaleTimeString()
      );
    }
  }
  if (type == "warn") {
    if (date) {
      logger.info(
        "warn:-" +
          message +
          " " +
          date.toLocaleDateString() +
          ":" +
          date.toLocaleTimeString()
      );
    } else {
      const date = new Date();
      logger.info(
        "warn:-" +
          message +
          " " +
          date.toLocaleDateString() +
          ":" +
          date.toLocaleTimeString()
      );
    }
  }
};

module.exports = {
  printLog,
};
