import apiService from '../lib/apiService';

const paymentService = {
    initiatePayment: (data) => apiService.post('/payment', data, { withCredentials: true }),

    getPaymentHistory: (type = 'payment_history') => 
        apiService.get(`/paymentHistory/history?type=${type}`, { withCredentials: true }),

    getPaymentDetails: (paymentId) => 
        apiService.get(`/paymentHistory/${paymentId}`, { withCredentials: true }),
};

export default paymentService;
