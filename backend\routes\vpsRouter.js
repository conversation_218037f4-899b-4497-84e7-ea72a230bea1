/**
 * VPS Routes
 * Defines all VPS-related API endpoints
 */

const express = require("express");
const vpsRouter = express.Router();

// Import controllers
const {
  getPlans,
  getPlanDetails,
  createOrder,
  getUserOrders,
  getUserInstances,
  controlInstance,
  getInstanceDetails,
  confirmPayment,
  getProviders,
  getInstanceStats,
  createSnapshot,
  reinstallInstance,
  getImages,
  getSubOrderDetails,
  // User management methods
  getProviderUsers,
  getProviderUserDetails,
  createProviderUser,
  updateProviderUser,
  deleteProviderUser,
  generateUserClientSecret,
  resetProviderUserPassword,
  // Data centers
  getDataCenters,
  getRegions,
  // Object storage
  getObjectStorages,
  getObjectStorageDetails,
  createObjectStorage,
  updateObjectStorage,
  cancelObjectStorage,
  resizeObjectStorage,
  getObjectStorageStats,
  // Secrets management
  getSecrets,
  createSecret,
  updateSecret,
  deleteSecret,
  // Tags management
  getTags,
  createTag,
  updateTag,
  deleteTag,
  assignTag,
  unassignTag,
  // Private networks
  getPrivateNetworks,
  createPrivateNetwork,
  updatePrivateNetwork,
  deletePrivateNetwork,
  assignInstanceToNetwork,
  unassignInstanceFromNetwork,
  // VIPs (Virtual IPs)
  getVips,
  createVip,
  updateVip,
  deleteVip,
  // Snapshots
  getSnapshots,
  updateSnapshot,
  deleteSnapshot,
  rollbackSnapshot,
  // Audits
  getAudits,
  // VPS Actions
  executeVPSAction,
  cancelVPSInstance,
  checkRescueStatus,
  getRescueImages,
  // VPS Snapshots
  getVPSSnapshots,
  createVPSSnapshot,
  renameVPSSnapshot,
  rollbackVPSSnapshot,
  deleteVPSSnapshot,
  // VPS Password Reset
  resetVPSPassword,
  // VPS Reinstall
  reinstallVPS,
  // VPS Images and Applications
  getAvailableImages,
  getAvailableApplications
} = require('../controllers/vpsController');

// Import middleware
const { fetchUserMiddleware } = require("../midelwares/authorization");
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");
const {
  createVPSOrderValidator,
  controlInstanceValidator,
  validateQueryProvider,
  validateInstanceId,
  validatePlanId,
  confirmPaymentValidator,
  createSnapshotValidator,
  reinstallInstanceValidator,
  validateVPSConfig,
  rateLimitVPSOperations,
} = require("../midelwares/requests/vpsRequest");

// Apply common middleware
vpsRouter.use(asyncBackFrontEndLang);

/**
 * Public routes (no authentication required)
 */

// Get available VPS plans
vpsRouter.get("/plans", validateQueryProvider, getPlans);

// Get VPS plan details
vpsRouter.get(
  "/plans/:planId",
  validatePlanId,
  validateQueryProvider,
  getPlanDetails
);

// Get supported providers
vpsRouter.get("/providers", getProviders);

// Get available OS images
vpsRouter.get('/images',
  validateQueryProvider,
  getImages
);

// Get available applications
vpsRouter.get('/applications',
  getAvailableApplications
);

// Get data centers
vpsRouter.get("/data-centers", validateQueryProvider, getDataCenters);

// Get regions
vpsRouter.get("/regions", validateQueryProvider, getRegions);

/**
 * Protected routes (authentication required)
 */

// Create VPS order
vpsRouter.post(
  "/order",
  fetchUserMiddleware,
  createVPSOrderValidator,
  validateVPSConfig,
  rateLimitVPSOperations,
  createOrder
);

// Get user's VPS orders
vpsRouter.get("/orders", fetchUserMiddleware, getUserOrders);

// Get VPS suborder details
vpsRouter.get(
  "/suborders/:subOrderId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for ID validation
  getSubOrderDetails
);

// Get user's VPS instances (temporairement sans auth pour les tests)
vpsRouter.get(
  "/instances",
  fetchUserMiddleware, // Commenté temporairement pour les tests
  getUserInstances
);

// Execute VPS action (start, stop, restart, console, rescue)
vpsRouter.post(
  "/instances/:instanceId/actions/:action",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  executeVPSAction
);

// Cancel VPS instance
vpsRouter.post(
  "/instances/:instanceId/cancel",
  fetchUserMiddleware,
  validateInstanceId,
  rateLimitVPSOperations,
  cancelVPSInstance
);

// Check rescue status
vpsRouter.get(
  "/instances/:instanceId/rescue-status",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  checkRescueStatus
);

// Get rescue images
vpsRouter.get(
  "/rescue-images",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  getRescueImages
);

// VPS Snapshots routes
vpsRouter.get("/instances/:instanceId/snapshots", getVPSSnapshots);
vpsRouter.post("/instances/:instanceId/snapshots", createVPSSnapshot);
vpsRouter.put(
  "/instances/:instanceId/snapshots/:snapshotId/rename",
  renameVPSSnapshot
);
vpsRouter.post(
  "/instances/:instanceId/snapshots/:snapshotId/rollback",
  rollbackVPSSnapshot
);
vpsRouter.delete(
  "/instances/:instanceId/snapshots/:snapshotId",
  deleteVPSSnapshot
);

// VPS Snapshots routes
// Get VPS snapshots
vpsRouter.get(
  "/instances/:instanceId/snapshots",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  getVPSSnapshots
);

// Create VPS snapshot
vpsRouter.post(
  "/instances/:instanceId/snapshots",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  createVPSSnapshot
);

// Rename VPS snapshot
vpsRouter.put(
  "/instances/:instanceId/snapshots/:snapshotId/rename",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  renameVPSSnapshot
);

// Rollback VPS snapshot
vpsRouter.post(
  "/instances/:instanceId/snapshots/:snapshotId/rollback",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  rollbackVPSSnapshot
);

// Delete VPS snapshot
vpsRouter.delete(
  "/instances/:instanceId/snapshots/:snapshotId",
  // fetchUserMiddleware, // Commenté temporairement pour les tests
  deleteVPSSnapshot
);

// Get specific VPS instance details
vpsRouter.get(
  "/instances/:instanceId",
  fetchUserMiddleware,
  validateInstanceId,
  getInstanceDetails
);

// Control VPS instance (start, stop, restart)
vpsRouter.post(
  "/instances/:instanceId/control",
  fetchUserMiddleware,
  validateInstanceId,
  controlInstanceValidator,
  rateLimitVPSOperations,
  controlInstance
);

// Get VPS instance statistics
vpsRouter.get(
  "/instances/:instanceId/stats",
  fetchUserMiddleware,
  validateInstanceId,
  getInstanceStats
);

// Create VPS snapshot
vpsRouter.post(
  "/instances/:instanceId/snapshot",
  fetchUserMiddleware,
  validateInstanceId,
  createSnapshotValidator,
  rateLimitVPSOperations,
  createSnapshot
);

// Reinstall VPS instance
vpsRouter.post(
  "/instances/:instanceId/reinstall",
  fetchUserMiddleware,
  validateInstanceId,
  reinstallInstanceValidator,
  rateLimitVPSOperations,
  reinstallVPS
);

// Reset password for VPS instance
vpsRouter.post('/instances/:instanceId/reset-password',
  fetchUserMiddleware,
  validateInstanceId,
  rateLimitVPSOperations,
  resetVPSPassword
);

// Confirm payment for VPS order
vpsRouter.post(
  "/payment/confirm",
  fetchUserMiddleware,
  confirmPaymentValidator,
  confirmPayment
);

/**
 * Admin routes (admin authentication required)
 * User management routes for VPS providers
 */

// Get provider users (admin only)
vpsRouter.get(
  "/users",
  fetchUserMiddleware,
  validateQueryProvider,
  getProviderUsers
);

// Get provider user details (admin only)
vpsRouter.get(
  "/users/:userId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for userId validation
  validateQueryProvider,
  getProviderUserDetails
);

// Create provider user (admin only)
vpsRouter.post(
  "/users",
  fetchUserMiddleware,
  validateQueryProvider,
  createProviderUser
);

// Update provider user (admin only)
vpsRouter.patch(
  "/users/:userId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for userId validation
  validateQueryProvider,
  updateProviderUser
);

// Delete provider user (admin only)
vpsRouter.delete(
  "/users/:userId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for userId validation
  validateQueryProvider,
  deleteProviderUser
);

// Generate client secret for user (admin only)
vpsRouter.put(
  "/users/:userId/client-secret",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for userId validation
  validateQueryProvider,
  generateUserClientSecret
);

// Reset user password (admin only)
vpsRouter.post(
  "/users/:userId/reset-password",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for userId validation
  validateQueryProvider,
  resetProviderUserPassword
);

// ==================== Object Storage Routes ====================

// Get object storages
vpsRouter.get(
  "/object-storages",
  fetchUserMiddleware,
  validateQueryProvider,
  getObjectStorages
);

// Get object storage details
vpsRouter.get(
  "/object-storages/:objectStorageId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for objectStorageId validation
  validateQueryProvider,
  getObjectStorageDetails
);

// Create object storage
vpsRouter.post(
  "/object-storages",
  fetchUserMiddleware,
  validateQueryProvider,
  createObjectStorage
);

// Update object storage
vpsRouter.patch(
  "/object-storages/:objectStorageId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for objectStorageId validation
  validateQueryProvider,
  updateObjectStorage
);

// Cancel object storage
vpsRouter.patch(
  "/object-storages/:objectStorageId/cancel",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for objectStorageId validation
  validateQueryProvider,
  cancelObjectStorage
);

// Resize object storage
vpsRouter.post(
  "/object-storages/:objectStorageId/resize",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for objectStorageId validation
  validateQueryProvider,
  resizeObjectStorage
);

// Get object storage statistics
vpsRouter.get(
  "/object-storages/:objectStorageId/stats",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for objectStorageId validation
  validateQueryProvider,
  getObjectStorageStats
);

// ==================== Secrets Management Routes ====================

// Get secrets
vpsRouter.get(
  "/secrets",
  fetchUserMiddleware,
  validateQueryProvider,
  getSecrets
);

// Create secret
vpsRouter.post(
  "/secrets",
  fetchUserMiddleware,
  validateQueryProvider,
  createSecret
);

// Update secret
vpsRouter.patch(
  "/secrets/:secretId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for secretId validation
  validateQueryProvider,
  updateSecret
);

// Delete secret
vpsRouter.delete(
  "/secrets/:secretId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for secretId validation
  validateQueryProvider,
  deleteSecret
);

// ==================== Tags Management Routes ====================

// Get tags
vpsRouter.get("/tags", fetchUserMiddleware, validateQueryProvider, getTags);

// Create tag
vpsRouter.post("/tags", fetchUserMiddleware, validateQueryProvider, createTag);

// Update tag
vpsRouter.patch(
  "/tags/:tagId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for tagId validation
  validateQueryProvider,
  updateTag
);

// Delete tag
vpsRouter.delete(
  "/tags/:tagId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for tagId validation
  validateQueryProvider,
  deleteTag
);

// Assign tag to resource
vpsRouter.post(
  "/tags/:tagId/assign",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for tagId validation
  validateQueryProvider,
  assignTag
);

// Unassign tag from resource
vpsRouter.delete(
  "/tags/:tagId/assign",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for tagId validation
  validateQueryProvider,
  unassignTag
);

// ==================== Private Networks Routes ====================

// Get private networks
vpsRouter.get(
  "/private-networks",
  fetchUserMiddleware,
  validateQueryProvider,
  getPrivateNetworks
);

// Create private network
vpsRouter.post(
  "/private-networks",
  fetchUserMiddleware,
  validateQueryProvider,
  createPrivateNetwork
);

// Update private network
vpsRouter.patch(
  "/private-networks/:privateNetworkId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for privateNetworkId validation
  validateQueryProvider,
  updatePrivateNetwork
);

// Delete private network
vpsRouter.delete(
  "/private-networks/:privateNetworkId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for privateNetworkId validation
  validateQueryProvider,
  deletePrivateNetwork
);

// Assign instance to private network
vpsRouter.post(
  "/private-networks/:privateNetworkId/instances/:instanceId",
  fetchUserMiddleware,
  validateQueryProvider,
  assignInstanceToNetwork
);

// Unassign instance from private network
vpsRouter.delete(
  "/private-networks/:privateNetworkId/instances/:instanceId",
  fetchUserMiddleware,
  validateQueryProvider,
  unassignInstanceFromNetwork
);

// ==================== VIPs (Virtual IPs) Routes ====================

// Get VIPs
vpsRouter.get("/vips", fetchUserMiddleware, validateQueryProvider, getVips);

// Create VIP
vpsRouter.post("/vips", fetchUserMiddleware, validateQueryProvider, createVip);

// Update VIP
vpsRouter.patch(
  "/vips/:vipId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for vipId validation
  validateQueryProvider,
  updateVip
);

// Delete VIP
vpsRouter.delete(
  "/vips/:vipId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for vipId validation
  validateQueryProvider,
  deleteVip
);

// ==================== Snapshots Routes ====================

// Get snapshots
vpsRouter.get(
  "/snapshots",
  fetchUserMiddleware,
  validateQueryProvider,
  getSnapshots
);

// Update snapshot
vpsRouter.patch(
  "/snapshots/:snapshotId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for snapshotId validation
  validateQueryProvider,
  updateSnapshot
);

// Delete snapshot
vpsRouter.delete(
  "/snapshots/:snapshotId",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for snapshotId validation
  validateQueryProvider,
  deleteSnapshot
);

// Rollback to snapshot
vpsRouter.post(
  "/snapshots/:snapshotId/rollback",
  fetchUserMiddleware,
  validateInstanceId, // Reuse for snapshotId validation
  validateQueryProvider,
  rollbackSnapshot
);

// ==================== Audits Routes ====================

// Get audits
vpsRouter.get("/audits", fetchUserMiddleware, validateQueryProvider, getAudits);

module.exports = vpsRouter;
