export const locales = ['en', 'fr', 'ar'];
export const defaultLocale = 'fr';

// This is used by the middleware and the plugin
export default function getI18nConfig(param) {
  return {
    locales,
    defaultLocale,
    // Load messages based on locale
    messages: async (locale) => {
      try {
        return (await import(`./messages/${locale}.json`)).default;
      } catch (error) {
        console.error(`Failed to load messages for locale: ${locale}`, error);
        // Fallback to default locale if messages can't be loaded
        return (await import(`./messages/${defaultLocale}.json`)).default;
      }
    }
  };
}
