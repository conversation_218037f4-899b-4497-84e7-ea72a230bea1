/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./src/styles/globals.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(33 150 243 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(33 150 243 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.15 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #eeeeee; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Roboto, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #bdbdbd; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #bdbdbd; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.\!container {
  width: 100% !important;
}
.container {
  width: 100%;
}
@media (min-width: 475px) {

  .\!container {
    max-width: 475px !important;
  }

  .container {
    max-width: 475px;
  }
}
@media (min-width: 640px) {

  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}
@media (min-width: 1600px) {

  .\!container {
    max-width: 1600px !important;
  }

  .container {
    max-width: 1600px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.visible {
  visibility: visible;
}
.\!invisible {
  visibility: hidden !important;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.\!absolute {
  position: absolute !important;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-0 {
  bottom: -0px;
}
.-bottom-1 {
  bottom: -0.25rem;
}
.-bottom-10 {
  bottom: -2.5rem;
}
.-bottom-4 {
  bottom: -1rem;
}
.-bottom-40 {
  bottom: -10rem;
}
.-bottom-\[1\.9rem\] {
  bottom: -1.9rem;
}
.-left-1 {
  left: -0.25rem;
}
.-left-40 {
  left: -10rem;
}
.-right-0 {
  right: -0px;
}
.-right-1 {
  right: -0.25rem;
}
.-right-3 {
  right: -0.75rem;
}
.-right-4 {
  right: -1rem;
}
.-right-40 {
  right: -10rem;
}
.-right-9 {
  right: -2.25rem;
}
.-right-\[1px\] {
  right: -1px;
}
.-right-\[80px\] {
  right: -80px;
}
.-top-1 {
  top: -0.25rem;
}
.-top-1\.5 {
  top: -0.375rem;
}
.-top-2 {
  top: -0.5rem;
}
.-top-2\.5 {
  top: -0.625rem;
}
.-top-3 {
  top: -0.75rem;
}
.-top-4 {
  top: -1rem;
}
.-top-40 {
  top: -10rem;
}
.-top-5 {
  top: -5px;
}
.-top-\[14px\] {
  top: -14px;
}
.-top-\[18px\] {
  top: -18px;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1\/4 {
  bottom: 25%;
}
.bottom-12 {
  bottom: 3rem;
}
.bottom-2 {
  bottom: 0.5rem;
}
.bottom-3 {
  bottom: 0.75rem;
}
.bottom-36 {
  bottom: 9rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-40 {
  bottom: 10rem;
}
.bottom-44 {
  bottom: 11rem;
}
.bottom-8 {
  bottom: 8px;
}
.bottom-\[14\%\] {
  bottom: 14%;
}
.bottom-\[230px\] {
  bottom: 230px;
}
.bottom-\[3px\] {
  bottom: 3px;
}
.bottom-\[4\%\] {
  bottom: 4%;
}
.bottom-\[58px\] {
  bottom: 58px;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\.5 {
  left: 0.375rem;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/3 {
  left: 33.333333%;
}
.left-1\/4 {
  left: 25%;
}
.left-2 {
  left: 0.5rem;
}
.left-2\/4 {
  left: 50%;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-40 {
  left: 10rem;
}
.left-52 {
  left: 13rem;
}
.left-7 {
  left: 1.75rem;
}
.left-\[14\%\] {
  left: 14%;
}
.left-\[2\%\] {
  left: 2%;
}
.left-\[30\%\] {
  left: 30%;
}
.left-\[45\%\] {
  left: 45%;
}
.left-\[60\%\] {
  left: 60%;
}
.left-\[calc\(50\%\+13rem\)\] {
  left: calc(50% + 13rem);
}
.left-\[calc\(50\%\+18\.5rem\)\] {
  left: calc(50% + 18.5rem);
}
.left-\[calc\(50\%\+24rem\)\] {
  left: calc(50% + 24rem);
}
.left-\[calc\(50\%\+7\.5rem\)\] {
  left: calc(50% + 7.5rem);
}
.left-\[calc\(50\%-13rem\)\] {
  left: calc(50% - 13rem);
}
.left-\[calc\(50\%-18\.5rem\)\] {
  left: calc(50% - 18.5rem);
}
.left-\[calc\(50\%-24rem\)\] {
  left: calc(50% - 24rem);
}
.left-\[calc\(50\%-7\.5rem\)\] {
  left: calc(50% - 7.5rem);
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-1\/2 {
  right: 50%;
}
.right-1\/4 {
  right: 25%;
}
.right-12 {
  right: 3rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-5 {
  right: 5px;
}
.right-52 {
  right: 13rem;
}
.right-7 {
  right: 1.75rem;
}
.right-\[14\%\] {
  right: 14%;
}
.right-\[2\%\] {
  right: 2%;
}
.right-\[38px\] {
  right: 38px;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-1\/3 {
  top: 33.333333%;
}
.top-1\/4 {
  top: 25%;
}
.top-10 {
  top: 2.5rem;
}
.top-14 {
  top: 3.5rem;
}
.top-16 {
  top: 4rem;
}
.top-2 {
  top: 0.5rem;
}
.top-2\/4 {
  top: 50%;
}
.top-20 {
  top: 5rem;
}
.top-24 {
  top: 6rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-40 {
  top: 10rem;
}
.top-52 {
  top: 13rem;
}
.top-8 {
  top: 8px;
}
.top-\[100\%\] {
  top: 100%;
}
.top-\[14\%\] {
  top: 14%;
}
.top-\[25\%\] {
  top: 25%;
}
.top-\[34\%\] {
  top: 34%;
}
.top-\[4\%\] {
  top: 4%;
}
.top-\[45\%\] {
  top: 45%;
}
.top-\[45px\] {
  top: 45px;
}
.top-\[50\%\] {
  top: 50%;
}
.top-\[70\%\] {
  top: 70%;
}
.top-full {
  top: 100%;
}
.-z-10 {
  z-index: -10;
}
.-z-50 {
  z-index: -50;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[1001\] {
  z-index: 1001;
}
.z-\[1002\] {
  z-index: 1002;
}
.z-\[1\] {
  z-index: 1;
}
.z-\[2\] {
  z-index: 2;
}
.z-\[60\] {
  z-index: 60;
}
.z-\[9995\] {
  z-index: 9995;
}
.z-\[9999\] {
  z-index: 9999;
}
.z-\[999\] {
  z-index: 999;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-full {
  grid-column: 1 / -1;
}
.row-auto {
  grid-row: auto;
}
.row-span-1 {
  grid-row: span 1 / span 1;
}
.m-0 {
  margin: 0px;
}
.m-0\.5 {
  margin: 0.125rem;
}
.m-1 {
  margin: 0.25rem;
}
.m-4 {
  margin: 1rem;
}
.m-6 {
  margin: 1.5rem;
}
.m-auto {
  margin: auto;
}
.-mx-1\.5 {
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-my-1\.5 {
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
}
.-my-2 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.mx-px {
  margin-left: 1px;
  margin-right: 1px;
}
.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-32 {
  margin-top: 8rem;
  margin-bottom: 8rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.-mb-px {
  margin-bottom: -1px;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-mr-14 {
  margin-right: -3.5rem;
}
.-mt-6 {
  margin-top: -1.5rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-\[400px\] {
  margin-bottom: 400px;
}
.mb-auto {
  margin-bottom: auto;
}
.ml-0 {
  margin-left: 0px;
}
.ml-0\.5 {
  margin-left: 0.125rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-11 {
  margin-left: 2.75rem;
}
.ml-16 {
  margin-left: 4rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-5 {
  margin-left: 1.25rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-64 {
  margin-left: 16rem;
}
.ml-8 {
  margin-left: 2rem;
}
.ml-\[18px\] {
  margin-left: 18px;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.mr-12 {
  margin-right: 3rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-5 {
  margin-right: 1.25rem;
}
.mr-auto {
  margin-right: auto;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-28 {
  margin-top: 7rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-40 {
  margin-top: 10rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[-30px\] {
  margin-top: -30px;
}
.mt-auto {
  margin-top: auto;
}
.mt-px {
  margin-top: 1px;
}
.box-border {
  box-sizing: border-box;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.size-2 {
  width: 0.5rem;
  height: 0.5rem;
}
.\!h-auto {
  height: auto !important;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-28 {
  height: 7rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-3\/4 {
  height: 75%;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-44 {
  height: 11rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-60 {
  height: 15rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[105px\] {
  height: 105px;
}
.h-\[10rem\] {
  height: 10rem;
}
.h-\[110px\] {
  height: 110px;
}
.h-\[120px\] {
  height: 120px;
}
.h-\[195px\] {
  height: 195px;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[230px\] {
  height: 230px;
}
.h-\[250px\] {
  height: 250px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[30px\] {
  height: 30px;
}
.h-\[350px\] {
  height: 350px;
}
.h-\[360px\] {
  height: 360px;
}
.h-\[400px\] {
  height: 400px;
}
.h-\[40px\] {
  height: 40px;
}
.h-\[440px\] {
  height: 440px;
}
.h-\[450px\] {
  height: 450px;
}
.h-\[500px\] {
  height: 500px;
}
.h-\[550px\] {
  height: 550px;
}
.h-\[58px\] {
  height: 58px;
}
.h-\[630px\] {
  height: 630px;
}
.h-\[66\.666vh\] {
  height: 66.666vh;
}
.h-\[74px\] {
  height: 74px;
}
.h-\[770px\] {
  height: 770px;
}
.h-\[90px\] {
  height: 90px;
}
.h-\[calc\(100vh-4rem\)\] {
  height: calc(100vh - 4rem);
}
.h-auto {
  height: auto;
}
.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.h-full {
  height: 100%;
}
.h-max {
  height: -moz-max-content;
  height: max-content;
}
.h-screen {
  height: 100vh;
}
.max-h-0 {
  max-height: 0px;
}
.max-h-32 {
  max-height: 8rem;
}
.max-h-40 {
  max-height: 10rem;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-60 {
  max-height: 15rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[100vh\] {
  max-height: 100vh;
}
.max-h-\[2000px\] {
  max-height: 2000px;
}
.max-h-\[32px\] {
  max-height: 32px;
}
.max-h-\[350px\] {
  max-height: 350px;
}
.max-h-\[400px\] {
  max-height: 400px;
}
.max-h-\[40px\] {
  max-height: 40px;
}
.max-h-\[48px\] {
  max-height: 48px;
}
.max-h-\[500px\] {
  max-height: 500px;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.max-h-none {
  max-height: none;
}
.max-h-screen {
  max-height: 100vh;
}
.min-h-\[1000px\] {
  min-height: 1000px;
}
.min-h-\[100px\] {
  min-height: 100px;
}
.min-h-\[100vh\] {
  min-height: 100vh;
}
.min-h-\[12px\] {
  min-height: 12px;
}
.min-h-\[150px\] {
  min-height: 150px;
}
.min-h-\[24px\] {
  min-height: 24px;
}
.min-h-\[3\.7rem\] {
  min-height: 3.7rem;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[330px\] {
  min-height: 330px;
}
.min-h-\[40px\] {
  min-height: 40px;
}
.min-h-\[48px\] {
  min-height: 48px;
}
.min-h-\[50vh\] {
  min-height: 50vh;
}
.min-h-\[550px\] {
  min-height: 550px;
}
.min-h-\[70vh\] {
  min-height: 70vh;
}
.min-h-\[80vh\] {
  min-height: 80vh;
}
.min-h-\[90vh\] {
  min-height: 90vh;
}
.min-h-\[91vh\] {
  min-height: 91vh;
}
.min-h-\[calc\(100vh-4rem\)\] {
  min-height: calc(100vh - 4rem);
}
.min-h-full {
  min-height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.\!w-10 {
  width: 2.5rem !important;
}
.\!w-64 {
  width: 16rem !important;
}
.w-0\.5 {
  width: 0.125rem;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/12 {
  width: 8.333333%;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-1\/4 {
  width: 25%;
}
.w-1\/6 {
  width: 16.666667%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-11\/12 {
  width: 91.666667%;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-2\/5 {
  width: 40%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-3\/5 {
  width: 60%;
}
.w-32 {
  width: 8rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-4\/5 {
  width: 80%;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-60 {
  width: 15rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[100px\] {
  width: 100px;
}
.w-\[110px\] {
  width: 110px;
}
.w-\[150px\] {
  width: 150px;
}
.w-\[16px\] {
  width: 16px;
}
.w-\[17px\] {
  width: 17px;
}
.w-\[180px\] {
  width: 180px;
}
.w-\[190px\] {
  width: 190px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[21rem\] {
  width: 21rem;
}
.w-\[22px\] {
  width: 22px;
}
.w-\[240px\] {
  width: 240px;
}
.w-\[25px\] {
  width: 25px;
}
.w-\[25rem\] {
  width: 25rem;
}
.w-\[290px\] {
  width: 290px;
}
.w-\[300px\] {
  width: 300px;
}
.w-\[330px\] {
  width: 330px;
}
.w-\[340px\] {
  width: 340px;
}
.w-\[350px\] {
  width: 350px;
}
.w-\[400px\] {
  width: 400px;
}
.w-\[40px\] {
  width: 40px;
}
.w-\[45\%\] {
  width: 45%;
}
.w-\[46px\] {
  width: 46px;
}
.w-\[50\%\] {
  width: 50%;
}
.w-\[500px\] {
  width: 500px;
}
.w-\[55px\] {
  width: 55px;
}
.w-\[58px\] {
  width: 58px;
}
.w-\[600px\] {
  width: 600px;
}
.w-\[70\%\] {
  width: 70%;
}
.w-\[74px\] {
  width: 74px;
}
.w-\[80\%\] {
  width: 80%;
}
.w-\[90\%\] {
  width: 90%;
}
.w-\[90px\] {
  width: 90px;
}
.w-\[95\%\] {
  width: 95%;
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.w-px {
  width: 1px;
}
.w-screen {
  width: 100vw;
}
.\!min-w-0 {
  min-width: 0px !important;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-32 {
  min-width: 8rem;
}
.min-w-48 {
  min-width: 12rem;
}
.min-w-80 {
  min-width: 20rem;
}
.min-w-\[100vw\] {
  min-width: 100vw;
}
.min-w-\[110px\] {
  min-width: 110px;
}
.min-w-\[120px\] {
  min-width: 120px;
}
.min-w-\[12px\] {
  min-width: 12px;
}
.min-w-\[180px\] {
  min-width: 180px;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-\[20px\] {
  min-width: 20px;
}
.min-w-\[240px\] {
  min-width: 240px;
}
.min-w-\[24px\] {
  min-width: 24px;
}
.min-w-\[40px\] {
  min-width: 40px;
}
.min-w-\[48px\] {
  min-width: 48px;
}
.min-w-\[60px\] {
  min-width: 60px;
}
.min-w-\[72px\] {
  min-width: 72px;
}
.min-w-\[80\%\] {
  min-width: 80%;
}
.min-w-\[80px\] {
  min-width: 80px;
}
.min-w-\[90\%\] {
  min-width: 90%;
}
.min-w-\[95\%\] {
  min-width: 95%;
}
.min-w-full {
  min-width: 100%;
}
.min-w-max {
  min-width: -moz-max-content;
  min-width: max-content;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[1000px\] {
  max-width: 1000px;
}
.max-w-\[100vw\] {
  max-width: 100vw;
}
.max-w-\[1200px\] {
  max-width: 1200px;
}
.max-w-\[1350px\] {
  max-width: 1350px;
}
.max-w-\[1400px\] {
  max-width: 1400px;
}
.max-w-\[140px\] {
  max-width: 140px;
}
.max-w-\[1800px\] {
  max-width: 1800px;
}
.max-w-\[1900px\] {
  max-width: 1900px;
}
.max-w-\[230px\] {
  max-width: 230px;
}
.max-w-\[300px\] {
  max-width: 300px;
}
.max-w-\[32px\] {
  max-width: 32px;
}
.max-w-\[360px\] {
  max-width: 360px;
}
.max-w-\[400px\] {
  max-width: 400px;
}
.max-w-\[40px\] {
  max-width: 40px;
}
.max-w-\[48px\] {
  max-width: 48px;
}
.max-w-\[80\%\] {
  max-width: 80%;
}
.max-w-\[800px\] {
  max-width: 800px;
}
.max-w-\[85\%\] {
  max-width: 85%;
}
.max-w-\[90\%\] {
  max-width: 90%;
}
.max-w-\[90rem\] {
  max-width: 90rem;
}
.max-w-\[90vw\] {
  max-width: 90vw;
}
.max-w-\[95\%\] {
  max-width: 95%;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-screen-2xl {
  max-width: 1536px;
}
.max-w-screen-lg {
  max-width: 1024px;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.\!shrink-0 {
  flex-shrink: 0 !important;
}
.shrink {
  flex-shrink: 1;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.basis-1\/2 {
  flex-basis: 50%;
}
.basis-full {
  flex-basis: 100%;
}
.table-auto {
  table-layout: auto;
}
.table-fixed {
  table-layout: fixed;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-bottom {
  transform-origin: bottom;
}
.origin-top-left {
  transform-origin: top left;
}
.-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-2\/4 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-\[26rem\] {
  --tw-translate-x: -26rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-\[30rem\] {
  --tw-translate-x: -30rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/4 {
  --tw-translate-y: -25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-2\/4 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-8 {
  --tw-translate-y: -2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/4 {
  --tw-translate-x: 25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-2\/4 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-3 {
  --tw-translate-x: 0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-6 {
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-8 {
  --tw-translate-x: 2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[1px\] {
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[26rem\] {
  --tw-translate-x: 26rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[30rem\] {
  --tw-translate-x: 30rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2\/4 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-1px\] {
  --tw-translate-y: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-24 {
  --tw-translate-x: -6rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-32 {
  --tw-translate-y: -8rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-32 {
  --tw-translate-x: 8rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-24 {
  --tw-translate-y: 6rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-7 {
  --tw-translate-x: 1.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-2 {
  --tw-rotate: -2deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-2 {
  --tw-rotate: 2deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-skew-y-2 {
  --tw-skew-y: -2deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.0\] {
  --tw-scale-x: 1.0;
  --tw-scale-y: 1.0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.8\] {
  --tw-scale-x: 1.8;
  --tw-scale-y: 1.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-0 {
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-y-0 {
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-y-\[-1\] {
  --tw-scale-y: -1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.animate-\[fadeIn_0\.2s_ease-out\] {
  animation: fadeIn 0.2s ease-out;
}
@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
@keyframes paint-from-bottom {

  0% {
    transform: scaleY(0);
    transform-origin: bottom;
  }

  100% {
    transform: scaleY(1);
    transform-origin: bottom;
  }
}
.animate-paint-from-bottom {
  animation: paint-from-bottom 0.5s ease-in-out;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-grab {
  cursor: grab;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}
.\!resize-none {
  resize: none !important;
}
.resize-none {
  resize: none;
}
.resize-y {
  resize: vertical;
}
.\!resize {
  resize: both !important;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-disc {
  list-style-type: disc;
}
.list-none {
  list-style-type: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.grid-rows-8 {
  grid-template-rows: repeat(8, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.place-items-center {
  place-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.justify-evenly {
  justify-content: space-evenly;
}
.justify-items-start {
  justify-items: start;
}
.justify-items-center {
  justify-items: center;
}
.gap-0 {
  gap: 0px;
}
.gap-0\.5 {
  gap: 0.125rem;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-\[10px\] {
  gap: 10px;
}
.gap-\[35px\] {
  gap: 35px;
}
.gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}
.gap-x-10 {
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}
.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-5 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-0 {
  row-gap: 0px;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-10 {
  row-gap: 2.5rem;
}
.gap-y-12 {
  row-gap: 3rem;
}
.gap-y-16 {
  row-gap: 4rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.gap-y-3 {
  row-gap: 0.75rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.gap-y-5 {
  row-gap: 1.25rem;
}
.gap-y-6 {
  row-gap: 1.5rem;
}
.gap-y-8 {
  row-gap: 2rem;
}
.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-x > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-amber-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(255 179 0 / var(--tw-divide-opacity, 1));
}
.divide-blue-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(30 136 229 / var(--tw-divide-opacity, 1));
}
.divide-blue-gray-50 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(236 239 241 / var(--tw-divide-opacity, 1));
}
.divide-blue-gray-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(84 110 122 / var(--tw-divide-opacity, 1));
}
.divide-brown-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(109 76 65 / var(--tw-divide-opacity, 1));
}
.divide-cyan-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(0 172 193 / var(--tw-divide-opacity, 1));
}
.divide-deep-orange-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(244 81 30 / var(--tw-divide-opacity, 1));
}
.divide-deep-purple-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(94 53 177 / var(--tw-divide-opacity, 1));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-divide-opacity, 1));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-divide-opacity, 1));
}
.divide-gray-800 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(66 66 66 / var(--tw-divide-opacity, 1));
}
.divide-green-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(67 160 71 / var(--tw-divide-opacity, 1));
}
.divide-indigo-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(57 73 171 / var(--tw-divide-opacity, 1));
}
.divide-light-blue-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(3 155 229 / var(--tw-divide-opacity, 1));
}
.divide-light-green-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(124 179 66 / var(--tw-divide-opacity, 1));
}
.divide-lime-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(192 202 51 / var(--tw-divide-opacity, 1));
}
.divide-orange-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(251 140 0 / var(--tw-divide-opacity, 1));
}
.divide-pink-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(216 27 96 / var(--tw-divide-opacity, 1));
}
.divide-purple-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(142 36 170 / var(--tw-divide-opacity, 1));
}
.divide-red-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 57 53 / var(--tw-divide-opacity, 1));
}
.divide-teal-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(0 137 123 / var(--tw-divide-opacity, 1));
}
.divide-yellow-600 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(253 216 53 / var(--tw-divide-opacity, 1));
}
.self-start {
  align-self: flex-start;
}
.self-end {
  align-self: flex-end;
}
.self-center {
  align-self: center;
}
.self-stretch {
  align-self: stretch;
}
.justify-self-end {
  justify-self: end;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-clip {
  overflow: clip;
}
.\!overflow-visible {
  overflow: visible !important;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.scroll-smooth {
  scroll-behavior: smooth;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-normal {
  white-space: normal;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-line {
  white-space: pre-line;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.text-nowrap {
  text-wrap: nowrap;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.\!rounded-full {
  border-radius: 9999px !important;
}
.\!rounded-lg {
  border-radius: 0.5rem !important;
}
.\!rounded-none {
  border-radius: 0px !important;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[38\.85px\] {
  border-radius: 38.85px;
}
.rounded-\[7px\] {
  border-radius: 7px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.\!rounded-l-none {
  border-top-left-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
}
.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-b-xl {
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-l {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.rounded-r {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.rounded-r-full {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}
.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.rounded-r-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-bl-3xl {
  border-bottom-left-radius: 1.5rem;
}
.rounded-bl-lg {
  border-bottom-left-radius: 0.5rem;
}
.rounded-br-3xl {
  border-bottom-right-radius: 1.5rem;
}
.rounded-br-lg {
  border-bottom-right-radius: 0.5rem;
}
.rounded-br-none {
  border-bottom-right-radius: 0px;
}
.rounded-tl-3xl {
  border-top-left-radius: 1.5rem;
}
.rounded-tl-none {
  border-top-left-radius: 0px;
}
.rounded-tl-xl {
  border-top-left-radius: 0.75rem;
}
.rounded-tr-3xl {
  border-top-right-radius: 1.5rem;
}
.rounded-tr-xl {
  border-top-right-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border-\[0\.69px\] {
  border-width: 0.69px;
}
.border-\[0\.97px\] {
  border-width: 0.97px;
}
.border-x {
  border-left-width: 1px;
  border-right-width: 1px;
}
.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-0 {
  border-bottom-width: 0px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-\[2px\] {
  border-bottom-width: 2px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0px;
}
.border-r-2 {
  border-right-width: 2px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-0 {
  border-top-width: 0px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.\!border-black {
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}
.\!border-gray-300 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1)) !important;
}
.\!border-white {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}
.border-\[\#05144B33\] {
  border-color: #05144B33;
}
.border-\[\#2C52F7\] {
  --tw-border-opacity: 1;
  border-color: rgb(44 82 247 / var(--tw-border-opacity, 1));
}
.border-\[\#497EF7\] {
  --tw-border-opacity: 1;
  border-color: rgb(73 126 247 / var(--tw-border-opacity, 1));
}
.border-\[\#497ef7\]\/30 {
  border-color: rgb(73 126 247 / 0.3);
}
.border-\[\#606AF5\] {
  --tw-border-opacity: 1;
  border-color: rgb(96 106 245 / var(--tw-border-opacity, 1));
}
.border-\[\#606af5\] {
  --tw-border-opacity: 1;
  border-color: rgb(96 106 245 / var(--tw-border-opacity, 1));
}
.border-\[\#AEAEAF\] {
  --tw-border-opacity: 1;
  border-color: rgb(174 174 175 / var(--tw-border-opacity, 1));
}
.border-\[\#C2C2C259\] {
  border-color: #C2C2C259;
}
.border-\[\#DADADA\] {
  --tw-border-opacity: 1;
  border-color: rgb(218 218 218 / var(--tw-border-opacity, 1));
}
.border-\[\#F2F4FB\] {
  --tw-border-opacity: 1;
  border-color: rgb(242 244 251 / var(--tw-border-opacity, 1));
}
.border-\[\#F7AF2A\] {
  --tw-border-opacity: 1;
  border-color: rgb(247 175 42 / var(--tw-border-opacity, 1));
}
.border-\[\#FFFFFF7D\] {
  border-color: #FFFFFF7D;
}
.border-\[\#aeaeaf\] {
  --tw-border-opacity: 1;
  border-color: rgb(174 174 175 / var(--tw-border-opacity, 1));
}
.border-\[\#e0e7ff\] {
  --tw-border-opacity: 1;
  border-color: rgb(224 231 255 / var(--tw-border-opacity, 1));
}
.border-amber-200 {
  --tw-border-opacity: 1;
  border-color: rgb(255 224 130 / var(--tw-border-opacity, 1));
}
.border-amber-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}
.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(187 222 251 / var(--tw-border-opacity, 1));
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(144 202 249 / var(--tw-border-opacity, 1));
}
.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(100 181 246 / var(--tw-border-opacity, 1));
}
.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(66 165 245 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(30 136 229 / var(--tw-border-opacity, 1));
}
.border-blue-800 {
  --tw-border-opacity: 1;
  border-color: rgb(21 101 192 / var(--tw-border-opacity, 1));
}
.border-blue-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(207 216 220 / var(--tw-border-opacity, 1));
}
.border-blue-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}
.border-blue-gray-50 {
  --tw-border-opacity: 1;
  border-color: rgb(236 239 241 / var(--tw-border-opacity, 1));
}
.border-blue-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}
.border-brown-500 {
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}
.border-cyan-500 {
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}
.border-deep-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}
.border-deep-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity, 1));
}
.border-gray-200\/50 {
  border-color: rgb(238 238 238 / 0.5);
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}
.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(189 189 189 / var(--tw-border-opacity, 1));
}
.border-gray-50 {
  --tw-border-opacity: 1;
  border-color: rgb(250 250 250 / var(--tw-border-opacity, 1));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(158 158 158 / var(--tw-border-opacity, 1));
}
.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(117 117 117 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(97 97 97 / var(--tw-border-opacity, 1));
}
.border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}
.border-gray-900\/25 {
  border-color: rgb(33 33 33 / 0.25);
}
.border-green-100 {
  --tw-border-opacity: 1;
  border-color: rgb(200 230 201 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(165 214 167 / var(--tw-border-opacity, 1));
}
.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(129 199 132 / var(--tw-border-opacity, 1));
}
.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(102 187 106 / var(--tw-border-opacity, 1));
}
.border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}
.border-indigo-100 {
  --tw-border-opacity: 1;
  border-color: rgb(197 202 233 / var(--tw-border-opacity, 1));
}
.border-indigo-200 {
  --tw-border-opacity: 1;
  border-color: rgb(159 168 218 / var(--tw-border-opacity, 1));
}
.border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}
.border-indigo-600 {
  --tw-border-opacity: 1;
  border-color: rgb(57 73 171 / var(--tw-border-opacity, 1));
}
.border-light-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}
.border-light-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}
.border-lime-500 {
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}
.border-orange-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 224 178 / var(--tw-border-opacity, 1));
}
.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(255 204 128 / var(--tw-border-opacity, 1));
}
.border-orange-300 {
  --tw-border-opacity: 1;
  border-color: rgb(255 183 77 / var(--tw-border-opacity, 1));
}
.border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}
.border-pink-500 {
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}
.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(11 45 106 / var(--tw-border-opacity, 1));
}
.border-purple-100 {
  --tw-border-opacity: 1;
  border-color: rgb(225 190 231 / var(--tw-border-opacity, 1));
}
.border-purple-200 {
  --tw-border-opacity: 1;
  border-color: rgb(206 147 216 / var(--tw-border-opacity, 1));
}
.border-purple-300 {
  --tw-border-opacity: 1;
  border-color: rgb(186 104 200 / var(--tw-border-opacity, 1));
}
.border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}
.border-purple-600 {
  --tw-border-opacity: 1;
  border-color: rgb(142 36 170 / var(--tw-border-opacity, 1));
}
.border-red-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 205 210 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(239 154 154 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(229 115 115 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(239 83 80 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}
.border-secondary {
  --tw-border-opacity: 1;
  border-color: rgb(73 126 247 / var(--tw-border-opacity, 1));
}
.border-teal-500 {
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/80 {
  border-color: rgb(255 255 255 / 0.8);
}
.border-yellow-100 {
  --tw-border-opacity: 1;
  border-color: rgb(255 249 196 / var(--tw-border-opacity, 1));
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(255 245 157 / var(--tw-border-opacity, 1));
}
.border-yellow-300 {
  --tw-border-opacity: 1;
  border-color: rgb(255 241 118 / var(--tw-border-opacity, 1));
}
.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgb(255 238 88 / var(--tw-border-opacity, 1));
}
.border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}
.border-gray-400\/30 {
  border-color: rgb(189 189 189 / 0.3);
}
.border-green-400\/30 {
  border-color: rgb(102 187 106 / 0.3);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-orange-400 {
  --tw-border-opacity: 1;
  border-color: rgb(255 167 38 / var(--tw-border-opacity, 1));
}
.\!border-t-blue-gray-200 {
  --tw-border-opacity: 1 !important;
  border-top-color: rgb(176 190 197 / var(--tw-border-opacity, 1)) !important;
}
.\!border-t-transparent {
  border-top-color: transparent !important;
}
.border-b-blue-gray-100 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(207 216 220 / var(--tw-border-opacity, 1));
}
.border-l-transparent {
  border-left-color: transparent;
}
.border-r-transparent {
  border-right-color: transparent;
}
.border-t-blue-gray-100 {
  --tw-border-opacity: 1;
  border-top-color: rgb(207 216 220 / var(--tw-border-opacity, 1));
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-\[\#090416\] {
  --tw-bg-opacity: 1;
  background-color: rgb(9 4 22 / var(--tw-bg-opacity, 1));
}
.bg-\[\#3968d8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(57 104 216 / var(--tw-bg-opacity, 1));
}
.bg-\[\#497EF70A\] {
  background-color: #497EF70A;
}
.bg-\[\#497EF70F\] {
  background-color: #497EF70F;
}
.bg-\[\#497EF7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(73 126 247 / var(--tw-bg-opacity, 1));
}
.bg-\[\#497ef7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(73 126 247 / var(--tw-bg-opacity, 1));
}
.bg-\[\#5176F130\] {
  background-color: #5176F130;
}
.bg-\[\#5956E9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(89 86 233 / var(--tw-bg-opacity, 1));
}
.bg-\[\#606AF566\] {
  background-color: #606AF566;
}
.bg-\[\#606AF5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(96 106 245 / var(--tw-bg-opacity, 1));
}
.bg-\[\#606af5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(96 106 245 / var(--tw-bg-opacity, 1));
}
.bg-\[\#6e6e6e11\] {
  background-color: #6e6e6e11;
}
.bg-\[\#E0E3FF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(224 227 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F2F4FB\] {
  --tw-bg-opacity: 1;
  background-color: rgb(242 244 251 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F5F6F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 246 248 / var(--tw-bg-opacity, 1));
}
.bg-\[\#F7AF2A0A\] {
  background-color: #F7AF2A0A;
}
.bg-\[\#F7AF2A1C\] {
  background-color: #F7AF2A1C;
}
.bg-\[\#F7AF2A\] {
  --tw-bg-opacity: 1;
  background-color: rgb(247 175 42 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FAFBFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 251 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#FFFCF6\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 252 246 / var(--tw-bg-opacity, 1));
}
.bg-\[\#e3eafd\] {
  --tw-bg-opacity: 1;
  background-color: rgb(227 234 253 / var(--tw-bg-opacity, 1));
}
.bg-\[\#ebf1fe\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 241 254 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f1f5ff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 255 / var(--tw-bg-opacity, 1));
}
.bg-\[\#f5f5f5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.bg-\[\#fef6e8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(254 246 232 / var(--tw-bg-opacity, 1));
}
.bg-amber-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 236 179 / var(--tw-bg-opacity, 1));
}
.bg-amber-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 248 225 / var(--tw-bg-opacity, 1));
}
.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 193 7 / var(--tw-bg-opacity, 1));
}
.bg-amber-500\/10 {
  background-color: rgb(255 193 7 / 0.1);
}
.bg-amber-500\/20 {
  background-color: rgb(255 193 7 / 0.2);
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(187 222 251 / var(--tw-bg-opacity, 1));
}
.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(144 202 249 / var(--tw-bg-opacity, 1));
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(100 181 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(66 165 245 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(227 242 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-50\/50 {
  background-color: rgb(227 242 253 / 0.5);
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(33 150 243 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/10 {
  background-color: rgb(33 150 243 / 0.1);
}
.bg-blue-500\/20 {
  background-color: rgb(33 150 243 / 0.2);
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 136 229 / var(--tw-bg-opacity, 1));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(25 118 210 / var(--tw-bg-opacity, 1));
}
.bg-blue-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(207 216 220 / var(--tw-bg-opacity, 1));
}
.bg-blue-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}
.bg-blue-gray-50\/50 {
  background-color: rgb(236 239 241 / 0.5);
}
.bg-blue-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 125 139 / var(--tw-bg-opacity, 1));
}
.bg-blue-gray-500\/10 {
  background-color: rgb(96 125 139 / 0.1);
}
.bg-blue-gray-500\/20 {
  background-color: rgb(96 125 139 / 0.2);
}
.bg-brown-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(121 85 72 / var(--tw-bg-opacity, 1));
}
.bg-brown-500\/10 {
  background-color: rgb(121 85 72 / 0.1);
}
.bg-brown-500\/20 {
  background-color: rgb(121 85 72 / 0.2);
}
.bg-current {
  background-color: currentColor;
}
.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 188 212 / var(--tw-bg-opacity, 1));
}
.bg-cyan-500\/10 {
  background-color: rgb(0 188 212 / 0.1);
}
.bg-cyan-500\/20 {
  background-color: rgb(0 188 212 / 0.2);
}
.bg-dark-green {
  --tw-bg-opacity: 1;
  background-color: rgb(58 126 115 / var(--tw-bg-opacity, 1));
}
.bg-deep-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 87 34 / var(--tw-bg-opacity, 1));
}
.bg-deep-orange-500\/10 {
  background-color: rgb(255 87 34 / 0.1);
}
.bg-deep-orange-500\/20 {
  background-color: rgb(255 87 34 / 0.2);
}
.bg-deep-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(103 58 183 / var(--tw-bg-opacity, 1));
}
.bg-deep-purple-500\/10 {
  background-color: rgb(103 58 183 / 0.1);
}
.bg-deep-purple-500\/20 {
  background-color: rgb(103 58 183 / 0.2);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}
.bg-gray-100\/80 {
  background-color: rgb(245 245 245 / 0.8);
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 224 224 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(158 158 158 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(117 117 117 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 66 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(33 33 33 / var(--tw-bg-opacity, 1));
}
.bg-gray-900\/10 {
  background-color: rgb(33 33 33 / 0.1);
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(200 230 201 / var(--tw-bg-opacity, 1));
}
.bg-green-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(165 214 167 / var(--tw-bg-opacity, 1));
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(102 187 106 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(232 245 233 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(76 175 80 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/10 {
  background-color: rgb(76 175 80 / 0.1);
}
.bg-green-500\/20 {
  background-color: rgb(76 175 80 / 0.2);
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 160 71 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(197 202 233 / var(--tw-bg-opacity, 1));
}
.bg-indigo-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(159 168 218 / var(--tw-bg-opacity, 1));
}
.bg-indigo-200\/10 {
  background-color: rgb(159 168 218 / 0.1);
}
.bg-indigo-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(232 234 246 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(63 81 181 / var(--tw-bg-opacity, 1));
}
.bg-indigo-500\/10 {
  background-color: rgb(63 81 181 / 0.1);
}
.bg-indigo-500\/20 {
  background-color: rgb(63 81 181 / 0.2);
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(57 73 171 / var(--tw-bg-opacity, 1));
}
.bg-indigo-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 35 126 / var(--tw-bg-opacity, 1));
}
.bg-light-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(3 169 244 / var(--tw-bg-opacity, 1));
}
.bg-light-blue-500\/10 {
  background-color: rgb(3 169 244 / 0.1);
}
.bg-light-blue-500\/20 {
  background-color: rgb(3 169 244 / 0.2);
}
.bg-light-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(139 195 74 / var(--tw-bg-opacity, 1));
}
.bg-light-green-500\/10 {
  background-color: rgb(139 195 74 / 0.1);
}
.bg-light-green-500\/20 {
  background-color: rgb(139 195 74 / 0.2);
}
.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(205 220 57 / var(--tw-bg-opacity, 1));
}
.bg-lime-500\/10 {
  background-color: rgb(205 220 57 / 0.1);
}
.bg-lime-500\/20 {
  background-color: rgb(205 220 57 / 0.2);
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 224 178 / var(--tw-bg-opacity, 1));
}
.bg-orange-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 167 38 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 243 224 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 152 0 / var(--tw-bg-opacity, 1));
}
.bg-orange-500\/10 {
  background-color: rgb(255 152 0 / 0.1);
}
.bg-orange-500\/20 {
  background-color: rgb(255 152 0 / 0.2);
}
.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 140 0 / var(--tw-bg-opacity, 1));
}
.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(233 30 99 / var(--tw-bg-opacity, 1));
}
.bg-pink-500\/10 {
  background-color: rgb(233 30 99 / 0.1);
}
.bg-pink-500\/20 {
  background-color: rgb(233 30 99 / 0.2);
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(11 45 106 / var(--tw-bg-opacity, 1));
}
.bg-primary\/10 {
  background-color: rgb(11 45 106 / 0.1);
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(225 190 231 / var(--tw-bg-opacity, 1));
}
.bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(206 147 216 / var(--tw-bg-opacity, 1));
}
.bg-purple-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(171 71 188 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 229 245 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 39 176 / var(--tw-bg-opacity, 1));
}
.bg-purple-500\/10 {
  background-color: rgb(156 39 176 / 0.1);
}
.bg-purple-500\/20 {
  background-color: rgb(156 39 176 / 0.2);
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(142 36 170 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 205 210 / var(--tw-bg-opacity, 1));
}
.bg-red-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 154 154 / var(--tw-bg-opacity, 1));
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 83 80 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 238 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 67 54 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/10 {
  background-color: rgb(244 67 54 / 0.1);
}
.bg-red-500\/20 {
  background-color: rgb(244 67 54 / 0.2);
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 57 53 / var(--tw-bg-opacity, 1));
}
.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(73 126 247 / var(--tw-bg-opacity, 1));
}
.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(178 223 219 / var(--tw-bg-opacity, 1));
}
.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 242 241 / var(--tw-bg-opacity, 1));
}
.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 150 136 / var(--tw-bg-opacity, 1));
}
.bg-teal-500\/10 {
  background-color: rgb(0 150 136 / 0.1);
}
.bg-teal-500\/20 {
  background-color: rgb(0 150 136 / 0.2);
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/15 {
  background-color: rgb(255 255 255 / 0.15);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 196 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 253 231 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 59 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500\/10 {
  background-color: rgb(255 235 59 / 0.1);
}
.bg-yellow-500\/20 {
  background-color: rgb(255 235 59 / 0.2);
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 216 53 / var(--tw-bg-opacity, 1));
}
.bg-yellow-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 192 45 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/20 {
  background-color: rgb(158 158 158 / 0.2);
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}
.bg-opacity-0 {
  --tw-bg-opacity: 0;
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}
.bg-opacity-30 {
  --tw-bg-opacity: 0.3;
}
.bg-opacity-35 {
  --tw-bg-opacity: 0.35;
}
.bg-opacity-40 {
  --tw-bg-opacity: 0.4;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-60 {
  --tw-bg-opacity: 0.6;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}
.bg-\[radial-gradient\(\#e5e7eb_1px\2c transparent_1px\)\] {
  background-image: radial-gradient(#e5e7eb 1px,transparent 1px);
}
.bg-custom-gradient {
  background-image: linear-gradient(to right, #0b2d6a 0%, #0b2d6a 25%, #070310 50%, #180640 75%, #180640 100%);
}
.bg-gradient-secondary {
  background-image: linear-gradient(270deg, #2A76F6 0%, #1243AA 100%);
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.from-\[\#000f38\] {
  --tw-gradient-from: #000f38 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 15 56 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#0B2D6A\] {
  --tw-gradient-from: #0B2D6A var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(11 45 106 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#1a2352\] {
  --tw-gradient-from: #1a2352 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(26 35 82 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#497EF7\] {
  --tw-gradient-from: #497EF7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(73 126 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#F5F6FF8C\] {
  --tw-gradient-from: #F5F6FF8C var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#FFFFFF\] {
  --tw-gradient-from: #FFFFFF var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#f1f5ff\] {
  --tw-gradient-from: #f1f5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(241 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-\[\#f8faff\] {
  --tw-gradient-from: #f8faff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-600 {
  --tw-gradient-from: #ffb300 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 179 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/40 {
  --tw-gradient-from: rgb(0 0 0 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100 {
  --tw-gradient-from: #bbdefb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(187 222 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50 {
  --tw-gradient-from: #e3f2fd var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(227 242 253 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50\/0 {
  --tw-gradient-from: rgb(227 242 253 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(227 242 253 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #2196f3 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(33 150 243 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #1e88e5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 136 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-800 {
  --tw-gradient-from: #1565c0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 101 192 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-900 {
  --tw-gradient-from: #0d47a1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(13 71 161 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-900\/20 {
  --tw-gradient-from: rgb(13 71 161 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(13 71 161 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-gray-600 {
  --tw-gradient-from: #546e7a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(84 110 122 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-brown-600 {
  --tw-gradient-from: #6d4c41 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(109 76 65 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-600 {
  --tw-gradient-from: #00acc1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 172 193 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-deep-orange-600 {
  --tw-gradient-from: #f4511e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(244 81 30 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-deep-purple-600 {
  --tw-gradient-from: #5e35b1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(94 53 177 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50 {
  --tw-gradient-from: #fafafa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 250 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-500 {
  --tw-gradient-from: #9e9e9e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(158 158 158 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-600 {
  --tw-gradient-from: #757575 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(117 117 117 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-900 {
  --tw-gradient-from: #212121 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(33 33 33 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-50 {
  --tw-gradient-from: #e8f5e9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(232 245 233 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500 {
  --tw-gradient-from: #4caf50 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(76 175 80 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600 {
  --tw-gradient-from: #43a047 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(67 160 71 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-50 {
  --tw-gradient-from: #e8eaf6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(232 234 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-600 {
  --tw-gradient-from: #3949ab var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(57 73 171 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-light-blue-600 {
  --tw-gradient-from: #039be5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(3 155 229 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-light-green-600 {
  --tw-gradient-from: #7cb342 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 179 66 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-lime-600 {
  --tw-gradient-from: #c0ca33 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 202 51 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-50 {
  --tw-gradient-from: #fff3e0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 243 224 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500 {
  --tw-gradient-from: #ff9800 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 152 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-600 {
  --tw-gradient-from: #fb8c00 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(251 140 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-600 {
  --tw-gradient-from: #d81b60 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(216 27 96 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50 {
  --tw-gradient-from: #f3e5f5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 229 245 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #9c27b0 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(156 39 176 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600 {
  --tw-gradient-from: #8e24aa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(142 36 170 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-600 {
  --tw-gradient-from: #e53935 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(229 57 53 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-600 {
  --tw-gradient-from: #00897b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 137 123 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #ffffff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-500 {
  --tw-gradient-from: #ffeb3b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 235 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-600 {
  --tw-gradient-from: #fdd835 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(253 216 53 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-\[\#A2ABFF5E\] {
  --tw-gradient-to: rgb(162 171 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #A2ABFF5E var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-\[\#D7E0FDC6\] {
  --tw-gradient-to: rgb(215 224 253 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #D7E0FDC6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/20 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-50\/0 {
  --tw-gradient-to: rgb(227 242 253 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(227 242 253 / 0) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-indigo-50 {
  --tw-gradient-to: rgb(232 234 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #e8eaf6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-indigo-600 {
  --tw-gradient-to: rgb(57 73 171 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #3949ab var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ffffff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-500 {
  --tw-gradient-to: rgb(244 67 54 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f44336 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[\#00093d\] {
  --tw-gradient-to: #00093d var(--tw-gradient-to-position);
}
.to-\[\#0a1c4c\] {
  --tw-gradient-to: #0a1c4c var(--tw-gradient-to-position);
}
.to-\[\#423FD9\] {
  --tw-gradient-to: #423FD9 var(--tw-gradient-to-position);
}
.to-\[\#E7F5FB\] {
  --tw-gradient-to: #E7F5FB var(--tw-gradient-to-position);
}
.to-\[\#FFFFFF\] {
  --tw-gradient-to: #FFFFFF var(--tw-gradient-to-position);
}
.to-amber-400 {
  --tw-gradient-to: #ffca28 var(--tw-gradient-to-position);
}
.to-blue-100 {
  --tw-gradient-to: #bbdefb var(--tw-gradient-to-position);
}
.to-blue-200 {
  --tw-gradient-to: #90caf9 var(--tw-gradient-to-position);
}
.to-blue-400 {
  --tw-gradient-to: #42a5f5 var(--tw-gradient-to-position);
}
.to-blue-50 {
  --tw-gradient-to: #e3f2fd var(--tw-gradient-to-position);
}
.to-blue-50\/20 {
  --tw-gradient-to: rgb(227 242 253 / 0.2) var(--tw-gradient-to-position);
}
.to-blue-600 {
  --tw-gradient-to: #1e88e5 var(--tw-gradient-to-position);
}
.to-blue-700 {
  --tw-gradient-to: #1976d2 var(--tw-gradient-to-position);
}
.to-blue-900 {
  --tw-gradient-to: #0d47a1 var(--tw-gradient-to-position);
}
.to-blue-gray-400 {
  --tw-gradient-to: #78909c var(--tw-gradient-to-position);
}
.to-brown-400 {
  --tw-gradient-to: #8d6e63 var(--tw-gradient-to-position);
}
.to-cyan-400 {
  --tw-gradient-to: #26c6da var(--tw-gradient-to-position);
}
.to-deep-orange-400 {
  --tw-gradient-to: #ff7043 var(--tw-gradient-to-position);
}
.to-deep-purple-400 {
  --tw-gradient-to: #7e57c2 var(--tw-gradient-to-position);
}
.to-gray-100 {
  --tw-gradient-to: #f5f5f5 var(--tw-gradient-to-position);
}
.to-gray-400 {
  --tw-gradient-to: #bdbdbd var(--tw-gradient-to-position);
}
.to-gray-600 {
  --tw-gradient-to: #757575 var(--tw-gradient-to-position);
}
.to-gray-700 {
  --tw-gradient-to: #616161 var(--tw-gradient-to-position);
}
.to-gray-800 {
  --tw-gradient-to: #424242 var(--tw-gradient-to-position);
}
.to-green-100 {
  --tw-gradient-to: #c8e6c9 var(--tw-gradient-to-position);
}
.to-green-400 {
  --tw-gradient-to: #66bb6a var(--tw-gradient-to-position);
}
.to-green-600 {
  --tw-gradient-to: #43a047 var(--tw-gradient-to-position);
}
.to-indigo-400 {
  --tw-gradient-to: #5c6bc0 var(--tw-gradient-to-position);
}
.to-indigo-50 {
  --tw-gradient-to: #e8eaf6 var(--tw-gradient-to-position);
}
.to-indigo-500 {
  --tw-gradient-to: #3f51b5 var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #3949ab var(--tw-gradient-to-position);
}
.to-indigo-900 {
  --tw-gradient-to: #1a237e var(--tw-gradient-to-position);
}
.to-light-blue-400 {
  --tw-gradient-to: #29b6f6 var(--tw-gradient-to-position);
}
.to-light-green-400 {
  --tw-gradient-to: #9ccc65 var(--tw-gradient-to-position);
}
.to-lime-400 {
  --tw-gradient-to: #d4e157 var(--tw-gradient-to-position);
}
.to-orange-100 {
  --tw-gradient-to: #ffe0b2 var(--tw-gradient-to-position);
}
.to-orange-400 {
  --tw-gradient-to: #ffa726 var(--tw-gradient-to-position);
}
.to-orange-600 {
  --tw-gradient-to: #fb8c00 var(--tw-gradient-to-position);
}
.to-pink-400 {
  --tw-gradient-to: #ec407a var(--tw-gradient-to-position);
}
.to-pink-50 {
  --tw-gradient-to: #fce4ec var(--tw-gradient-to-position);
}
.to-purple-100 {
  --tw-gradient-to: #e1bee7 var(--tw-gradient-to-position);
}
.to-purple-400 {
  --tw-gradient-to: #ab47bc var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #8e24aa var(--tw-gradient-to-position);
}
.to-purple-700 {
  --tw-gradient-to: #7b1fa2 var(--tw-gradient-to-position);
}
.to-red-400 {
  --tw-gradient-to: #ef5350 var(--tw-gradient-to-position);
}
.to-red-50 {
  --tw-gradient-to: #ffebee var(--tw-gradient-to-position);
}
.to-red-700 {
  --tw-gradient-to: #d32f2f var(--tw-gradient-to-position);
}
.to-teal-400 {
  --tw-gradient-to: #26a69a var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white {
  --tw-gradient-to: #ffffff var(--tw-gradient-to-position);
}
.to-yellow-400 {
  --tw-gradient-to: #ffee58 var(--tw-gradient-to-position);
}
.to-yellow-600 {
  --tw-gradient-to: #fdd835 var(--tw-gradient-to-position);
}
.to-pink-500 {
  --tw-gradient-to: #e91e63 var(--tw-gradient-to-position);
}
.to-red-500 {
  --tw-gradient-to: #f44336 var(--tw-gradient-to-position);
}
.to-purple-50 {
  --tw-gradient-to: #f3e5f5 var(--tw-gradient-to-position);
}
.bg-cover {
  background-size: cover;
}
.bg-clip-border {
  background-clip: border-box;
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.bg-center {
  background-position: center;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.fill-yellow-400 {
  fill: #ffee58;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.object-\[center_2\%\] {
  -o-object-position: center 2%;
     object-position: center 2%;
}
.object-center {
  -o-object-position: center;
     object-position: center;
}
.object-left {
  -o-object-position: left;
     object-position: left;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-9 {
  padding: 2.25rem;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.px-14 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}
.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-px {
  padding-left: 1px;
  padding-right: 1px;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.\!pr-7 {
  padding-right: 1.75rem !important;
}
.\!pr-9 {
  padding-right: 2.25rem !important;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-1\.5 {
  padding-bottom: 0.375rem;
}
.pb-10 {
  padding-bottom: 2.5rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pl-0 {
  padding-left: 0px;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-7 {
  padding-left: 1.75rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-0 {
  padding-right: 0px;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-24 {
  padding-right: 6rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-0\.5 {
  padding-top: 0.125rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-24 {
  padding-top: 6rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.pt-\[9px\] {
  padding-top: 9px;
}
.pt-px {
  padding-top: 1px;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-start {
  text-align: start;
}
.align-middle {
  vertical-align: middle;
}
.font-inter {
  font-family: var(--font-inter);
}
.font-jim_ngihtshade {
  font-family: var(--font-jim-night-shade);
}
.font-outfit {
  font-family: var(--font-outfit);
}
.font-poppins {
  font-family: var(--font-poppins);
}
.font-roboto {
  font-family: var(--font-roboto);
}
.font-sans {
  font-family: Roboto, sans-serif;
}
.\!text-\[11px\] {
  font-size: 11px !important;
}
.\!text-lg {
  font-size: 1.125rem !important;
}
.text-2xl {
  font-size: 1.5rem;
}
.text-3xl {
  font-size: 1.875rem;
}
.text-4xl {
  font-size: 2.25rem;
}
.text-5xl {
  font-size: 3rem;
}
.text-\[11px\] {
  font-size: 11px;
}
.text-\[12px\] {
  font-size: 12px;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-\[15px\] {
  font-size: 15px;
}
.text-\[20px\] {
  font-size: 20px;
}
.text-\[28px\] {
  font-size: 28px;
}
.text-base {
  font-size: 1rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-sm {
  font-size: .875rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-xs {
  font-size: .75rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-extralight {
  font-weight: 200;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.font-thin {
  font-weight: 100;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.normal-case {
  text-transform: none;
}
.italic {
  font-style: italic;
}
.\!leading-tight {
  line-height: 1.25 !important;
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-\[1\.3\] {
  line-height: 1.3;
}
.leading-\[3\.75\] {
  line-height: 3.75;
}
.leading-\[4\.1\] {
  line-height: 4.1;
}
.leading-\[4\.25\] {
  line-height: 4.25;
}
.leading-\[4\.875\] {
  line-height: 4.875;
}
.leading-none {
  line-height: 1;
}
.leading-normal {
  line-height: 1.5;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-snug {
  line-height: 1.375;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-normal {
  letter-spacing: 0em;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.\!text-black {
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) !important;
}
.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}
.text-\[\#05144B\] {
  --tw-text-opacity: 1;
  color: rgb(5 20 75 / var(--tw-text-opacity, 1));
}
.text-\[\#212121\] {
  --tw-text-opacity: 1;
  color: rgb(33 33 33 / var(--tw-text-opacity, 1));
}
.text-\[\#333333\] {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity, 1));
}
.text-\[\#37436F\] {
  --tw-text-opacity: 1;
  color: rgb(55 67 111 / var(--tw-text-opacity, 1));
}
.text-\[\#4571b6\] {
  --tw-text-opacity: 1;
  color: rgb(69 113 182 / var(--tw-text-opacity, 1));
}
.text-\[\#576484\] {
  --tw-text-opacity: 1;
  color: rgb(87 100 132 / var(--tw-text-opacity, 1));
}
.text-\[\#606AF5\] {
  --tw-text-opacity: 1;
  color: rgb(96 106 245 / var(--tw-text-opacity, 1));
}
.text-\[\#606af5\] {
  --tw-text-opacity: 1;
  color: rgb(96 106 245 / var(--tw-text-opacity, 1));
}
.text-\[\#6A7292\] {
  --tw-text-opacity: 1;
  color: rgb(106 114 146 / var(--tw-text-opacity, 1));
}
.text-\[\#939393\] {
  --tw-text-opacity: 1;
  color: rgb(147 147 147 / var(--tw-text-opacity, 1));
}
.text-\[\#F4BA07\] {
  --tw-text-opacity: 1;
  color: rgb(244 186 7 / var(--tw-text-opacity, 1));
}
.text-\[\#cad2e7\] {
  --tw-text-opacity: 1;
  color: rgb(202 210 231 / var(--tw-text-opacity, 1));
}
.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(255 193 7 / var(--tw-text-opacity, 1));
}
.text-amber-600 {
  --tw-text-opacity: 1;
  color: rgb(255 179 0 / var(--tw-text-opacity, 1));
}
.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(255 160 0 / var(--tw-text-opacity, 1));
}
.text-amber-800 {
  --tw-text-opacity: 1;
  color: rgb(255 143 0 / var(--tw-text-opacity, 1));
}
.text-amber-900 {
  --tw-text-opacity: 1;
  color: rgb(255 111 0 / var(--tw-text-opacity, 1));
}
.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(187 222 251 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(144 202 249 / var(--tw-text-opacity, 1));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(100 181 246 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(66 165 245 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(33 150 243 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(30 136 229 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(25 118 210 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(21 101 192 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(13 71 161 / var(--tw-text-opacity, 1));
}
.text-blue-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(144 164 174 / var(--tw-text-opacity, 1));
}
.text-blue-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(120 144 156 / var(--tw-text-opacity, 1));
}
.text-blue-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}
.text-blue-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(69 90 100 / var(--tw-text-opacity, 1));
}
.text-blue-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(38 50 56 / var(--tw-text-opacity, 1));
}
.text-brown-500 {
  --tw-text-opacity: 1;
  color: rgb(121 85 72 / var(--tw-text-opacity, 1));
}
.text-brown-700 {
  --tw-text-opacity: 1;
  color: rgb(93 64 55 / var(--tw-text-opacity, 1));
}
.text-brown-900 {
  --tw-text-opacity: 1;
  color: rgb(62 39 35 / var(--tw-text-opacity, 1));
}
.text-current {
  color: currentColor;
}
.text-cyan-500 {
  --tw-text-opacity: 1;
  color: rgb(0 188 212 / var(--tw-text-opacity, 1));
}
.text-cyan-700 {
  --tw-text-opacity: 1;
  color: rgb(0 151 167 / var(--tw-text-opacity, 1));
}
.text-cyan-900 {
  --tw-text-opacity: 1;
  color: rgb(0 96 100 / var(--tw-text-opacity, 1));
}
.text-deep-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(255 87 34 / var(--tw-text-opacity, 1));
}
.text-deep-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(230 74 25 / var(--tw-text-opacity, 1));
}
.text-deep-orange-900 {
  --tw-text-opacity: 1;
  color: rgb(191 54 12 / var(--tw-text-opacity, 1));
}
.text-deep-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(103 58 183 / var(--tw-text-opacity, 1));
}
.text-deep-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(81 45 168 / var(--tw-text-opacity, 1));
}
.text-deep-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(49 27 146 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(238 238 238 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(224 224 224 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(189 189 189 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(158 158 158 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(117 117 117 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(97 97 97 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(66 66 66 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(33 33 33 / var(--tw-text-opacity, 1));
}
.text-green-100 {
  --tw-text-opacity: 1;
  color: rgb(200 230 201 / var(--tw-text-opacity, 1));
}
.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(102 187 106 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(76 175 80 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(67 160 71 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(56 142 60 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(46 125 50 / var(--tw-text-opacity, 1));
}
.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(27 94 32 / var(--tw-text-opacity, 1));
}
.text-indigo-400 {
  --tw-text-opacity: 1;
  color: rgb(92 107 192 / var(--tw-text-opacity, 1));
}
.text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(63 81 181 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(57 73 171 / var(--tw-text-opacity, 1));
}
.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(48 63 159 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(40 53 147 / var(--tw-text-opacity, 1));
}
.text-indigo-900 {
  --tw-text-opacity: 1;
  color: rgb(26 35 126 / var(--tw-text-opacity, 1));
}
.text-inherit {
  color: inherit;
}
.text-light-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(3 169 244 / var(--tw-text-opacity, 1));
}
.text-light-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(2 136 209 / var(--tw-text-opacity, 1));
}
.text-light-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(1 87 155 / var(--tw-text-opacity, 1));
}
.text-light-green-500 {
  --tw-text-opacity: 1;
  color: rgb(139 195 74 / var(--tw-text-opacity, 1));
}
.text-light-green-700 {
  --tw-text-opacity: 1;
  color: rgb(104 159 56 / var(--tw-text-opacity, 1));
}
.text-light-green-900 {
  --tw-text-opacity: 1;
  color: rgb(51 105 30 / var(--tw-text-opacity, 1));
}
.text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(205 220 57 / var(--tw-text-opacity, 1));
}
.text-lime-700 {
  --tw-text-opacity: 1;
  color: rgb(175 180 43 / var(--tw-text-opacity, 1));
}
.text-lime-900 {
  --tw-text-opacity: 1;
  color: rgb(130 119 23 / var(--tw-text-opacity, 1));
}
.text-orange-100 {
  --tw-text-opacity: 1;
  color: rgb(255 224 178 / var(--tw-text-opacity, 1));
}
.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(255 167 38 / var(--tw-text-opacity, 1));
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(255 152 0 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(251 140 0 / var(--tw-text-opacity, 1));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(245 124 0 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(239 108 0 / var(--tw-text-opacity, 1));
}
.text-orange-900 {
  --tw-text-opacity: 1;
  color: rgb(230 81 0 / var(--tw-text-opacity, 1));
}
.text-pink-500 {
  --tw-text-opacity: 1;
  color: rgb(233 30 99 / var(--tw-text-opacity, 1));
}
.text-pink-700 {
  --tw-text-opacity: 1;
  color: rgb(194 24 91 / var(--tw-text-opacity, 1));
}
.text-pink-900 {
  --tw-text-opacity: 1;
  color: rgb(136 14 79 / var(--tw-text-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: rgb(11 45 106 / var(--tw-text-opacity, 1));
}
.text-purple-100 {
  --tw-text-opacity: 1;
  color: rgb(225 190 231 / var(--tw-text-opacity, 1));
}
.text-purple-400 {
  --tw-text-opacity: 1;
  color: rgb(171 71 188 / var(--tw-text-opacity, 1));
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(156 39 176 / var(--tw-text-opacity, 1));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(142 36 170 / var(--tw-text-opacity, 1));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(123 31 162 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(106 27 154 / var(--tw-text-opacity, 1));
}
.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(74 20 140 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(239 83 80 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(244 67 54 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(229 57 53 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(211 47 47 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(198 40 40 / var(--tw-text-opacity, 1));
}
.text-red-900 {
  --tw-text-opacity: 1;
  color: rgb(183 28 28 / var(--tw-text-opacity, 1));
}
.text-secondary {
  --tw-text-opacity: 1;
  color: rgb(73 126 247 / var(--tw-text-opacity, 1));
}
.text-teal-500 {
  --tw-text-opacity: 1;
  color: rgb(0 150 136 / var(--tw-text-opacity, 1));
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(0 137 123 / var(--tw-text-opacity, 1));
}
.text-teal-700 {
  --tw-text-opacity: 1;
  color: rgb(0 121 107 / var(--tw-text-opacity, 1));
}
.text-teal-800 {
  --tw-text-opacity: 1;
  color: rgb(0 105 92 / var(--tw-text-opacity, 1));
}
.text-teal-900 {
  --tw-text-opacity: 1;
  color: rgb(0 77 64 / var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-100 {
  --tw-text-opacity: 1;
  color: rgb(255 249 196 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(255 238 88 / var(--tw-text-opacity, 1));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(255 235 59 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(253 216 53 / var(--tw-text-opacity, 1));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(251 192 45 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(249 168 37 / var(--tw-text-opacity, 1));
}
.text-yellow-900 {
  --tw-text-opacity: 1;
  color: rgb(245 127 23 / var(--tw-text-opacity, 1));
}
.text-gray-100 {
  --tw-text-opacity: 1;
  color: rgb(245 245 245 / var(--tw-text-opacity, 1));
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-opacity-100 {
  --tw-text-opacity: 1;
}
.text-opacity-50 {
  --tw-text-opacity: 0.5;
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(189 189 189 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(189 189 189 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-10 {
  opacity: 0.1;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-90 {
  opacity: 0.9;
}
.mix-blend-multiply {
  mix-blend-mode: multiply;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_2px_black\] {
  --tw-shadow: 0 0 2px black;
  --tw-shadow-colored: 0 0 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_70px_70px_-15px_rgb\(1\2c 0\2c 0\2c 0\.2\)\] {
  --tw-shadow: 0 70px 70px -15px rgb(1,0,0,0.2);
  --tw-shadow-colored: 0 70px 70px -15px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_8px_30px_rgba\(0\2c 0\2c 0\2c 0\.08\)\] {
  --tw-shadow: 0 8px 30px rgba(0,0,0,0.08);
  --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-custom-heavy {
  --tw-shadow: 1px 1px 27.1px 1px rgba(0, 0, 0, 0.09);
  --tw-shadow-colored: 1px 1px 27.1px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-custom-light {
  --tw-shadow: 0px 0px 4.72px 0px rgba(0, 0, 0, 0.19);
  --tw-shadow-colored: 0px 0px 4.72px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 rgb(0, 0 / 0, 0);
  --tw-shadow-colored: 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-amber-500\/20 {
  --tw-shadow-color: rgb(255 193 7 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-amber-500\/40 {
  --tw-shadow-color: rgb(255 193 7 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/20 {
  --tw-shadow-color: rgb(33 150 243 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/40 {
  --tw-shadow-color: rgb(33 150 243 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-600\/20 {
  --tw-shadow-color: rgb(30 136 229 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-gray-500\/10 {
  --tw-shadow-color: rgb(96 125 139 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-gray-500\/20 {
  --tw-shadow-color: rgb(96 125 139 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-gray-500\/40 {
  --tw-shadow-color: rgb(96 125 139 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-gray-900\/10 {
  --tw-shadow-color: rgb(38 50 56 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-brown-500\/20 {
  --tw-shadow-color: rgb(121 85 72 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-brown-500\/40 {
  --tw-shadow-color: rgb(121 85 72 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-cyan-500\/20 {
  --tw-shadow-color: rgb(0 188 212 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-cyan-500\/40 {
  --tw-shadow-color: rgb(0 188 212 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-deep-orange-500\/20 {
  --tw-shadow-color: rgb(255 87 34 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-deep-orange-500\/40 {
  --tw-shadow-color: rgb(255 87 34 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-deep-purple-500\/20 {
  --tw-shadow-color: rgb(103 58 183 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-deep-purple-500\/40 {
  --tw-shadow-color: rgb(103 58 183 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-gray-900\/10 {
  --tw-shadow-color: rgb(33 33 33 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-gray-900\/20 {
  --tw-shadow-color: rgb(33 33 33 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/20 {
  --tw-shadow-color: rgb(76 175 80 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/40 {
  --tw-shadow-color: rgb(76 175 80 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-indigo-500\/20 {
  --tw-shadow-color: rgb(63 81 181 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-indigo-500\/40 {
  --tw-shadow-color: rgb(63 81 181 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-indigo-500\/50 {
  --tw-shadow-color: rgb(63 81 181 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-light-blue-500\/20 {
  --tw-shadow-color: rgb(3 169 244 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-light-blue-500\/40 {
  --tw-shadow-color: rgb(3 169 244 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-light-green-500\/20 {
  --tw-shadow-color: rgb(139 195 74 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-light-green-500\/40 {
  --tw-shadow-color: rgb(139 195 74 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-lime-500\/20 {
  --tw-shadow-color: rgb(205 220 57 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-lime-500\/40 {
  --tw-shadow-color: rgb(205 220 57 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-orange-500\/20 {
  --tw-shadow-color: rgb(255 152 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-orange-500\/40 {
  --tw-shadow-color: rgb(255 152 0 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-pink-500\/20 {
  --tw-shadow-color: rgb(233 30 99 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-pink-500\/40 {
  --tw-shadow-color: rgb(233 30 99 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/20 {
  --tw-shadow-color: rgb(156 39 176 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/40 {
  --tw-shadow-color: rgb(156 39 176 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-red-500\/20 {
  --tw-shadow-color: rgb(244 67 54 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-red-500\/40 {
  --tw-shadow-color: rgb(244 67 54 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-teal-500\/20 {
  --tw-shadow-color: rgb(0 150 136 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-teal-500\/40 {
  --tw-shadow-color: rgb(0 150 136 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-yellow-500\/20 {
  --tw-shadow-color: rgb(255 235 59 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-yellow-500\/40 {
  --tw-shadow-color: rgb(255 235 59 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.outline-0 {
  outline-width: 0px;
}
.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-indigo-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 81 181 / var(--tw-ring-opacity, 1));
}
.ring-secondary {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(73 126 247 / var(--tw-ring-opacity, 1));
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-\[0_0_10px_rgba\(0\2c 255\2c 255\2c 0\.5\)\] {
  --tw-drop-shadow: drop-shadow(0 0 10px rgba(0,255,255,0.5));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-xl {
  --tw-drop-shadow: drop-shadow(0 20px 13px rgb(0 0 0 / 0.03)) drop-shadow(0 8px 5px rgb(0 0 0 / 0.08));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-2xl {
  --tw-backdrop-blur: blur(40px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-saturate-200 {
  --tw-backdrop-saturate: saturate(2);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-1000 {
  transition-duration: 1000ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.content-\[\'\'\] {
  --tw-content: '';
  content: var(--tw-content);
}
.\[-webkit-appearance\:none\] {
  -webkit-appearance: none;
}
.\[background-size\:16px_16px\] {
  background-size: 16px 16px;
}
.\[mask-image\:radial-gradient\(ellipse_50\%_50\%_at_50\%_50\%\2c \#000_70\%\2c transparent_100\%\)\] {
  -webkit-mask-image: radial-gradient(ellipse 50% 50% at 50% 50%,#000 70%,transparent 100%);
          mask-image: radial-gradient(ellipse 50% 50% at 50% 50%,#000 70%,transparent 100%);
}

body {
  padding: 0;
  margin: 0;
}

.swiper {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
}

.swiper-slide {
  background-position: center;
  background-size: cover;
  width: 300px;
  /* Adjust width as needed */
  height: 100%;
  /* border: 1px solid black; */

  /* Adjust height as needed */
}

.swiper-slide img {
  display: block;
  width: 100%;
}

/* Add this in your CSS file */
.swiper-pagination-bullet {
  background-color: #d1d5db;
  /* gray-300 */
  opacity: 1;
  transition: width 0.3s ease;
}

.swiper-pagination-bullet-active {
  width: 16px;
  background-color: #3b82f6;
  /* blue-500 */
}

.hide-scrollbar {
  -ms-overflow-style: none;
  /* For Internet Explorer */
  scrollbar-width: none;
  /* For Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
  /* For Chrome, Safari, and Opera */
}

/* slick swiper customization */
/* Override slick dots styles */
.slick-dots {
  /* bottom: -25px; */
  /* Adjust position of the dots */
}

.slick-dots li {
  /* margin: 0 5px; */
}

.slick-dots li button::before {
  content: '';
  /* Remove default dot */
  display: inline-block;
  width: 20px;
  /* Length of the hashed line */
  height: 6px !important;
  /* Thickness of the hashed line */
  background-color: white;
  color: white;
  /* White color for the line */
  opacity: 0.5;
  /* Initial opacity */
  transition: opacity 0.3s ease;
}

.slick-dots li.slick-active button::before {
  opacity: 1;
  /* Full opacity for active line */
}

/* Optional: Center the hashed line */
.slick-dots li button {
  padding: 0;
}

/* Small Scrollbar */
.scrollbar-small {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: #cbd5e1 #f8fafc;
  /* Scrollbar and track colors */
}

.scrollbar-small::-webkit-scrollbar {
  width: 6px;
  /* Scrollbar width */
  height: 6px;
  /* Scrollbar height */
}

.scrollbar-small::-webkit-scrollbar-track {
  background: #f8fafc;
  /* Track background color */
  border-radius: 10px;
  /* Rounded corners */
}

.scrollbar-small::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  /* Scrollbar thumb color */
  border-radius: 10px;
  /* Rounded corners */
  border: 2px solid #f8fafc;
  /* Padding around the thumb */
}

.scrollbar-small::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
  /* Thumb color on hover */
}

/* Custom Scrollbar with #497ef7 Color */
.scrollbar-custom {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: #497ef7 #f8fafc;
  /* Thumb and track colors for Firefox */
}

/* Webkit Browsers */
.scrollbar-custom::-webkit-scrollbar {
  width: 8px;
  /* Scrollbar width */
  height: 8px;
  /* Scrollbar height */
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: #f8fafc;
  /* Track background color */
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: #497ef7;
  /* Scrollbar thumb color */
  border-radius: 10px;
  /* Rounded corners for the thumb */
  border: 2px solid #f8fafc;
  /* Padding around the thumb */
}

.scrollbar-custom::-webkit-scrollbar-thumb:hover {
  background-color: #3b6ac1;
  /* Slightly darker thumb on hover */
}

/* From Uiverse.io by JaydipPrajapati1910 */
.customButton {
  --width: 250px;
  --height: 45px;
  --tooltip-height: 35px;
  --tooltip-width: 90px;
  --gap-between-tooltip-to-button: 10px;
  width: var(--width);
  height: var(--height);
  position: relative;
  text-align: center;
  border-radius: 0.45em;
  transition: background 0.3s;
}

.customButton::before {
  position: absolute;
  content: attr(data-tooltip);
  width: var(--tooltip-width);
  height: var(--tooltip-height);
  font-size: 0.9rem;
  border-radius: .25em;
  line-height: var(--tooltip-height);
  bottom: calc(var(--height) + var(--gap-between-tooltip-to-button) + 10px);
  left: calc(50% - var(--tooltip-width) / 2);
}

.customButton::after {
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-top-color: yellow;
  left: calc(50% - 10px);
  bottom: calc(100% + var(--gap-between-tooltip-to-button) - 10px);
}

.customButton::after,
.button::before {
  opacity: 0;
  visibility: hidden;
  transition: all 0.5s;
}

.customText {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-wrapper,
.text,
.icon {
  overflow: hidden;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  color: #fff;
}

.customText {
  top: 0
}

.customText,
.icon {
  transition: top 0.5s;
}

.icon {
  color: #fff;
  top: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon svg {
  width: 24px;
  height: 24px;
}

.customButton:hover {
  background: #000f38;
}

.customButton:hover .customText {
  top: -140%;
}

.customButton:hover .icon {
  top: 0;
}

.customButton:hover:before,
.customButton:hover:after {
  opacity: 1;
  visibility: visible;
}

/* Chatbot specific styles - these are minimal since we're using Tailwind classes directly */
.chatbot-container {
  position: fixed;
  bottom: 24px;
  right: 90px; /* Increased right spacing to create distance from WhatsApp button */
  z-index: 1000;
  display: flex;
  flex-direction: column;
  pointer-events: none;
}

.chatbot-container > div {
  pointer-events: auto;
}

/* Animation for the chatbot window */
@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Apply animation to chat window */
.chatbot-container > div:first-child {
  animation: slideInUp 0.3s ease-out;
}

.hero-gradient {
  background: linear-gradient(
    135deg,
    #0f172a 0%,
    #1e3a8a 50%,
    #1e40af 100%
  );
  position: relative;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  background: url('/noise.png') repeat;
  opacity: 0.1;
  mix-blend-mode: overlay;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(-1deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

.placeholder\:text-blue-gray-300::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(144 164 174 / var(--tw-text-opacity, 1));
}

.placeholder\:text-blue-gray-300::placeholder {
  --tw-text-opacity: 1;
  color: rgb(144 164 174 / var(--tw-text-opacity, 1));
}

.placeholder\:opacity-0::-moz-placeholder {
  opacity: 0;
}

.placeholder\:opacity-0::placeholder {
  opacity: 0;
}

.placeholder\:opacity-100::-moz-placeholder {
  opacity: 1;
}

.placeholder\:opacity-100::placeholder {
  opacity: 1;
}

.before\:pointer-events-none::before {
  content: var(--tw-content);
  pointer-events: none;
}

.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}

.before\:left-2\/4::before {
  content: var(--tw-content);
  left: 50%;
}

.before\:top-2\/4::before {
  content: var(--tw-content);
  top: 50%;
}

.before\:mr-1::before {
  content: var(--tw-content);
  margin-right: 0.25rem;
}

.before\:mt-\[6\.5px\]::before {
  content: var(--tw-content);
  margin-top: 6.5px;
}

.before\:box-border::before {
  content: var(--tw-content);
  box-sizing: border-box;
}

.before\:block::before {
  content: var(--tw-content);
  display: block;
}

.before\:h-1\.5::before {
  content: var(--tw-content);
  height: 0.375rem;
}

.before\:h-10::before {
  content: var(--tw-content);
  height: 2.5rem;
}

.before\:h-12::before {
  content: var(--tw-content);
  height: 3rem;
}

.before\:w-10::before {
  content: var(--tw-content);
  width: 2.5rem;
}

.before\:w-12::before {
  content: var(--tw-content);
  width: 3rem;
}

.before\:w-2\.5::before {
  content: var(--tw-content);
  width: 0.625rem;
}

.before\:-translate-x-2\/4::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:-translate-y-2\/4::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:rounded-tl-md::before {
  content: var(--tw-content);
  border-top-left-radius: 0.375rem;
}

.before\:border-l::before {
  content: var(--tw-content);
  border-left-width: 1px;
}

.before\:border-l-2::before {
  content: var(--tw-content);
  border-left-width: 2px;
}

.before\:border-t::before {
  content: var(--tw-content);
  border-top-width: 1px;
}

.before\:border-t-2::before {
  content: var(--tw-content);
  border-top-width: 2px;
}

.before\:\!border-black::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.before\:\!border-blue-gray-200::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1)) !important;
}

.before\:\!border-white::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}

.before\:border-amber-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.before\:border-black::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.before\:border-blue-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}

.before\:border-blue-gray-200::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}

.before\:border-blue-gray-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}

.before\:border-brown-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}

.before\:border-cyan-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}

.before\:border-deep-orange-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}

.before\:border-deep-purple-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}

.before\:border-gray-900::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}

.before\:border-green-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.before\:border-indigo-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.before\:border-light-blue-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}

.before\:border-light-green-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}

.before\:border-lime-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}

.before\:border-orange-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}

.before\:border-pink-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}

.before\:border-purple-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.before\:border-red-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.before\:border-teal-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}

.before\:border-transparent::before {
  content: var(--tw-content);
  border-color: transparent;
}

.before\:border-white::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.before\:border-yellow-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}

.before\:border-l-transparent::before {
  content: var(--tw-content);
  border-left-color: transparent;
}

.before\:border-t-transparent::before {
  content: var(--tw-content);
  border-top-color: transparent;
}

.before\:bg-blue-gray-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(96 125 139 / var(--tw-bg-opacity, 1));
}

.before\:opacity-0::before {
  content: var(--tw-content);
  opacity: 0;
}

.before\:transition-all::before {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:transition-opacity::before {
  content: var(--tw-content);
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.before\:content-none::before {
  --tw-content: none;
  content: var(--tw-content);
}

.after\:pointer-events-none::after {
  content: var(--tw-content);
  pointer-events: none;
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:-bottom-0::after {
  content: var(--tw-content);
  bottom: -0px;
}

.after\:-bottom-1::after {
  content: var(--tw-content);
  bottom: -0.25rem;
}

.after\:-bottom-1\.5::after {
  content: var(--tw-content);
  bottom: -0.375rem;
}

.after\:-bottom-2\.5::after {
  content: var(--tw-content);
  bottom: -0.625rem;
}

.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}

.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}

.after\:ml-1::after {
  content: var(--tw-content);
  margin-left: 0.25rem;
}

.after\:mt-\[6\.5px\]::after {
  content: var(--tw-content);
  margin-top: 6.5px;
}

.after\:box-border::after {
  content: var(--tw-content);
  box-sizing: border-box;
}

.after\:block::after {
  content: var(--tw-content);
  display: block;
}

.after\:h-1\.5::after {
  content: var(--tw-content);
  height: 0.375rem;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-2\.5::after {
  content: var(--tw-content);
  width: 0.625rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}

.after\:flex-grow::after {
  content: var(--tw-content);
  flex-grow: 1;
}

.after\:scale-x-0::after {
  content: var(--tw-content);
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:scale-x-100::after {
  content: var(--tw-content);
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:rounded-tr-md::after {
  content: var(--tw-content);
  border-top-right-radius: 0.375rem;
}

.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-b-2::after {
  content: var(--tw-content);
  border-bottom-width: 2px;
}

.after\:border-r::after {
  content: var(--tw-content);
  border-right-width: 1px;
}

.after\:border-r-2::after {
  content: var(--tw-content);
  border-right-width: 2px;
}

.after\:border-t::after {
  content: var(--tw-content);
  border-top-width: 1px;
}

.after\:border-t-2::after {
  content: var(--tw-content);
  border-top-width: 2px;
}

.after\:\!border-black::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.after\:\!border-blue-gray-200::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1)) !important;
}

.after\:\!border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}

.after\:border-amber-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.after\:border-black::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.after\:border-blue-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}

.after\:border-blue-gray-200::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}

.after\:border-blue-gray-50::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(236 239 241 / var(--tw-border-opacity, 1));
}

.after\:border-blue-gray-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}

.after\:border-brown-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}

.after\:border-cyan-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}

.after\:border-deep-orange-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}

.after\:border-deep-purple-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}

.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}

.after\:border-gray-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(158 158 158 / var(--tw-border-opacity, 1));
}

.after\:border-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}

.after\:border-green-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.after\:border-indigo-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.after\:border-light-blue-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}

.after\:border-light-green-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}

.after\:border-lime-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}

.after\:border-orange-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}

.after\:border-pink-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}

.after\:border-purple-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.after\:border-red-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.after\:border-teal-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}

.after\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}

.after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.after\:border-yellow-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}

.after\:border-r-transparent::after {
  content: var(--tw-content);
  border-right-color: transparent;
}

.after\:border-t-transparent::after {
  content: var(--tw-content);
  border-top-color: transparent;
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:transition-transform::after {
  content: var(--tw-content);
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:duration-300::after {
  content: var(--tw-content);
  transition-duration: 300ms;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.after\:content-none::after {
  --tw-content: none;
  content: var(--tw-content);
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.last\:border-0:last-child {
  border-width: 0px;
}

.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}

.last\:pb-0:last-child {
  padding-bottom: 0px;
}

.even\:bg-blue-gray-50\/50:nth-child(even) {
  background-color: rgb(236 239 241 / 0.5);
}

.checked\:border-amber-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.checked\:border-blue-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}

.checked\:border-blue-gray-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}

.checked\:border-brown-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}

.checked\:border-cyan-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}

.checked\:border-deep-orange-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}

.checked\:border-deep-purple-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}

.checked\:border-gray-900:checked {
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}

.checked\:border-green-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.checked\:border-indigo-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.checked\:border-light-blue-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}

.checked\:border-light-green-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}

.checked\:border-lime-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}

.checked\:border-orange-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}

.checked\:border-pink-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}

.checked\:border-purple-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.checked\:border-red-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.checked\:border-teal-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}

.checked\:border-yellow-500:checked {
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}

.checked\:bg-amber-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(255 193 7 / var(--tw-bg-opacity, 1));
}

.checked\:bg-blue-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(33 150 243 / var(--tw-bg-opacity, 1));
}

.checked\:bg-blue-gray-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(96 125 139 / var(--tw-bg-opacity, 1));
}

.checked\:bg-brown-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(121 85 72 / var(--tw-bg-opacity, 1));
}

.checked\:bg-cyan-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(0 188 212 / var(--tw-bg-opacity, 1));
}

.checked\:bg-deep-orange-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(255 87 34 / var(--tw-bg-opacity, 1));
}

.checked\:bg-deep-purple-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(103 58 183 / var(--tw-bg-opacity, 1));
}

.checked\:bg-gray-900:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(33 33 33 / var(--tw-bg-opacity, 1));
}

.checked\:bg-green-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(76 175 80 / var(--tw-bg-opacity, 1));
}

.checked\:bg-indigo-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(63 81 181 / var(--tw-bg-opacity, 1));
}

.checked\:bg-light-blue-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(3 169 244 / var(--tw-bg-opacity, 1));
}

.checked\:bg-light-green-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(139 195 74 / var(--tw-bg-opacity, 1));
}

.checked\:bg-lime-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(205 220 57 / var(--tw-bg-opacity, 1));
}

.checked\:bg-orange-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(255 152 0 / var(--tw-bg-opacity, 1));
}

.checked\:bg-pink-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(233 30 99 / var(--tw-bg-opacity, 1));
}

.checked\:bg-purple-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(156 39 176 / var(--tw-bg-opacity, 1));
}

.checked\:bg-red-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(244 67 54 / var(--tw-bg-opacity, 1));
}

.checked\:bg-teal-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(0 150 136 / var(--tw-bg-opacity, 1));
}

.checked\:bg-yellow-500:checked {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 59 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-amber-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 193 7 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-blue-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 150 243 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-blue-gray-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(96 125 139 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-brown-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(121 85 72 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-cyan-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(0 188 212 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-deep-orange-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 87 34 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-deep-purple-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(103 58 183 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-gray-900:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 33 33 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-green-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(76 175 80 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-indigo-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(63 81 181 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-light-blue-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(3 169 244 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-light-green-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(139 195 74 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-lime-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(205 220 57 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-orange-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 152 0 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-pink-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(233 30 99 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-purple-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(156 39 176 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-red-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(244 67 54 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-teal-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(0 150 136 / var(--tw-bg-opacity, 1));
}

.checked\:before\:bg-yellow-500:checked::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 59 / var(--tw-bg-opacity, 1));
}

.placeholder-shown\:border:-moz-placeholder-shown {
  border-width: 1px;
}

.placeholder-shown\:border:placeholder-shown {
  border-width: 1px;
}

.placeholder-shown\:border-blue-gray-200:-moz-placeholder-shown {
  --tw-border-opacity: 1;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-blue-gray-200:placeholder-shown {
  --tw-border-opacity: 1;
  border-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-green-500:-moz-placeholder-shown {
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-green-500:placeholder-shown {
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-red-500:-moz-placeholder-shown {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-red-500:placeholder-shown {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-t-blue-gray-200:-moz-placeholder-shown {
  --tw-border-opacity: 1;
  border-top-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-t-blue-gray-200:placeholder-shown {
  --tw-border-opacity: 1;
  border-top-color: rgb(176 190 197 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-t-green-500:-moz-placeholder-shown {
  --tw-border-opacity: 1;
  border-top-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-t-green-500:placeholder-shown {
  --tw-border-opacity: 1;
  border-top-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-t-red-500:-moz-placeholder-shown {
  --tw-border-opacity: 1;
  border-top-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.placeholder-shown\:border-t-red-500:placeholder-shown {
  --tw-border-opacity: 1;
  border-top-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.focus-within\:outline-none:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-blue-600:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 136 229 / var(--tw-ring-opacity, 1));
}

.focus-within\:ring-offset-2:focus-within {
  --tw-ring-offset-width: 2px;
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-rotate-3:hover {
  --tw-rotate: -3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:rotate-3:hover {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:rounded-lg:hover {
  border-radius: 0.5rem;
}

.hover\:rounded-md:hover {
  border-radius: 0.375rem;
}

.hover\:border-b-2:hover {
  border-bottom-width: 2px;
}

.hover\:border-\[\#497ef7\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(73 126 247 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(100 181 246 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(30 136 229 / var(--tw-border-opacity, 1));
}

.hover\:border-blue-900:hover {
  --tw-border-opacity: 1;
  border-color: rgb(13 71 161 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(224 224 224 / var(--tw-border-opacity, 1));
}

.hover\:border-green-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(129 199 132 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.hover\:border-indigo-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(48 63 159 / var(--tw-border-opacity, 1));
}

.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(11 45 106 / var(--tw-border-opacity, 1));
}

.hover\:border-purple-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(186 104 200 / var(--tw-border-opacity, 1));
}

.hover\:border-red-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.hover\:border-secondary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(73 126 247 / var(--tw-border-opacity, 1));
}

.hover\:border-transparent:hover {
  border-color: transparent;
}

.hover\:border-orange-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 183 77 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#3968d8\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(57 104 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#4679ff\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(70 121 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-\[\#497ef7\]\/5:hover {
  background-color: rgb(73 126 247 / 0.05);
}

.hover\:bg-amber-500\/10:hover {
  background-color: rgb(255 193 7 / 0.1);
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 222 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(144 202 249 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(100 181 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 165 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(227 242 253 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(33 150 243 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-500\/10:hover {
  background-color: rgb(33 150 243 / 0.1);
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 136 229 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(25 118 210 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 101 192 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(207 216 220 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-gray-50\/30:hover {
  background-color: rgb(236 239 241 / 0.3);
}

.hover\:bg-blue-gray-500\/10:hover {
  background-color: rgb(96 125 139 / 0.1);
}

.hover\:bg-brown-500\/10:hover {
  background-color: rgb(121 85 72 / 0.1);
}

.hover\:bg-cyan-500\/10:hover {
  background-color: rgb(0 188 212 / 0.1);
}

.hover\:bg-deep-orange-500\/10:hover {
  background-color: rgb(255 87 34 / 0.1);
}

.hover\:bg-deep-purple-500\/10:hover {
  background-color: rgb(103 58 183 / 0.1);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100\/45:hover {
  background-color: rgb(245 245 245 / 0.45);
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(238 238 238 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200\/50:hover {
  background-color: rgb(238 238 238 / 0.5);
}

.hover\:bg-gray-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(224 224 224 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(189 189 189 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(117 117 117 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(97 97 97 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(33 33 33 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-900\/10:hover {
  background-color: rgb(33 33 33 / 0.1);
}

.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(200 230 201 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(165 214 167 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-500\/10:hover {
  background-color: rgb(76 175 80 / 0.1);
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 160 71 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(56 142 60 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(232 234 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-indigo-500\/10:hover {
  background-color: rgb(63 81 181 / 0.1);
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(48 63 159 / var(--tw-bg-opacity, 1));
}

.hover\:bg-light-blue-500\/10:hover {
  background-color: rgb(3 169 244 / 0.1);
}

.hover\:bg-light-green-500\/10:hover {
  background-color: rgb(139 195 74 / 0.1);
}

.hover\:bg-lime-500\/10:hover {
  background-color: rgb(205 220 57 / 0.1);
}

.hover\:bg-orange-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 243 224 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-500\/10:hover {
  background-color: rgb(255 152 0 / 0.1);
}

.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(251 140 0 / var(--tw-bg-opacity, 1));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 124 0 / var(--tw-bg-opacity, 1));
}

.hover\:bg-pink-500\/10:hover {
  background-color: rgb(233 30 99 / 0.1);
}

.hover\:bg-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(11 45 106 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/5:hover {
  background-color: rgb(11 45 106 / 0.05);
}

.hover\:bg-purple-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(225 190 231 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(206 147 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 39 176 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-500\/10:hover {
  background-color: rgb(156 39 176 / 0.1);
}

.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(123 31 162 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 205 210 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 154 154 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 238 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 67 54 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-500\/10:hover {
  background-color: rgb(244 67 54 / 0.1);
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 57 53 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(211 47 47 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(73 126 247 / var(--tw-bg-opacity, 1));
}

.hover\:bg-teal-500\/10:hover {
  background-color: rgb(0 150 136 / 0.1);
}

.hover\:bg-transparent:hover {
  background-color: transparent;
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}

.hover\:bg-yellow-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 196 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 245 157 / var(--tw-bg-opacity, 1));
}

.hover\:bg-yellow-500\/10:hover {
  background-color: rgb(255 235 59 / 0.1);
}

.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(251 192 45 / var(--tw-bg-opacity, 1));
}

.hover\:bg-purple-50\/50:hover {
  background-color: rgb(243 229 245 / 0.5);
}

.hover\:bg-blue-50\/50:hover {
  background-color: rgb(227 242 253 / 0.5);
}

.hover\:bg-orange-50\/50:hover {
  background-color: rgb(255 243 224 / 0.5);
}

.hover\:bg-green-50\/50:hover {
  background-color: rgb(232 245 233 / 0.5);
}

.hover\:bg-orange-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 224 178 / var(--tw-bg-opacity, 1));
}

.hover\:bg-opacity-30:hover {
  --tw-bg-opacity: 0.3;
}

.hover\:bg-opacity-75:hover {
  --tw-bg-opacity: 0.75;
}

.hover\:bg-opacity-80:hover {
  --tw-bg-opacity: 0.8;
}

.hover\:from-blue-700:hover {
  --tw-gradient-from: #1976d2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(25 118 210 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-indigo-700:hover {
  --tw-gradient-from: #303f9f var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(48 63 159 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-\[\#606AF5\]:hover {
  --tw-gradient-to: #606AF5 var(--tw-gradient-to-position);
}

.hover\:to-blue-800:hover {
  --tw-gradient-to: #1565c0 var(--tw-gradient-to-position);
}

.hover\:to-indigo-700:hover {
  --tw-gradient-to: #303f9f var(--tw-gradient-to-position);
}

.hover\:text-\[\#5d87fb\]:hover {
  --tw-text-opacity: 1;
  color: rgb(93 135 251 / var(--tw-text-opacity, 1));
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-200:hover {
  --tw-text-opacity: 1;
  color: rgb(144 202 249 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(33 150 243 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(30 136 229 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgb(25 118 210 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(21 101 192 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(13 71 161 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(38 50 56 / var(--tw-text-opacity, 1));
}

.hover\:text-deep-orange-100:hover {
  --tw-text-opacity: 1;
  color: rgb(255 204 188 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(224 224 224 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(117 117 117 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(97 97 97 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(66 66 66 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(33 33 33 / var(--tw-text-opacity, 1));
}

.hover\:text-green-700:hover {
  --tw-text-opacity: 1;
  color: rgb(56 142 60 / var(--tw-text-opacity, 1));
}

.hover\:text-green-800:hover {
  --tw-text-opacity: 1;
  color: rgb(46 125 50 / var(--tw-text-opacity, 1));
}

.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(27 94 32 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-600:hover {
  --tw-text-opacity: 1;
  color: rgb(57 73 171 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-700:hover {
  --tw-text-opacity: 1;
  color: rgb(48 63 159 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-800:hover {
  --tw-text-opacity: 1;
  color: rgb(40 53 147 / var(--tw-text-opacity, 1));
}

.hover\:text-indigo-900:hover {
  --tw-text-opacity: 1;
  color: rgb(26 35 126 / var(--tw-text-opacity, 1));
}

.hover\:text-light-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(3 169 244 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-700:hover {
  --tw-text-opacity: 1;
  color: rgb(245 124 0 / var(--tw-text-opacity, 1));
}

.hover\:text-orange-900:hover {
  --tw-text-opacity: 1;
  color: rgb(230 81 0 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(11 45 106 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-400:hover {
  --tw-text-opacity: 1;
  color: rgb(171 71 188 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-600:hover {
  --tw-text-opacity: 1;
  color: rgb(142 36 170 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-700:hover {
  --tw-text-opacity: 1;
  color: rgb(123 31 162 / var(--tw-text-opacity, 1));
}

.hover\:text-purple-800:hover {
  --tw-text-opacity: 1;
  color: rgb(106 27 154 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(244 67 54 / var(--tw-text-opacity, 1));
}

.hover\:text-red-700:hover {
  --tw-text-opacity: 1;
  color: rgb(211 47 47 / var(--tw-text-opacity, 1));
}

.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(198 40 40 / var(--tw-text-opacity, 1));
}

.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(183 28 28 / var(--tw-text-opacity, 1));
}

.hover\:text-secondary:hover {
  --tw-text-opacity: 1;
  color: rgb(73 126 247 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-700:hover {
  --tw-text-opacity: 1;
  color: rgb(251 192 45 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-75:hover {
  opacity: 0.75;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_15px_35px_rgba\(0\2c 0\2c 0\2c 0\.12\)\]:hover {
  --tw-shadow: 0 15px 35px rgba(0,0,0,0.12);
  --tw-shadow-colored: 0 15px 35px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-\[0_8px_30px_rgb\(0\2c 0\2c 0\2c 0\.12\)\]:hover {
  --tw-shadow: 0 8px 30px rgb(0,0,0,0.12);
  --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-none:hover {
  --tw-shadow: 0 0 rgb(0, 0 / 0, 0);
  --tw-shadow-colored: 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-amber-500\/40:hover {
  --tw-shadow-color: rgb(255 193 7 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-blue-500\/25:hover {
  --tw-shadow-color: rgb(33 150 243 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-blue-500\/40:hover {
  --tw-shadow-color: rgb(33 150 243 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-blue-gray-500\/20:hover {
  --tw-shadow-color: rgb(96 125 139 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-blue-gray-500\/40:hover {
  --tw-shadow-color: rgb(96 125 139 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-brown-500\/40:hover {
  --tw-shadow-color: rgb(121 85 72 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-cyan-500\/40:hover {
  --tw-shadow-color: rgb(0 188 212 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-deep-orange-500\/40:hover {
  --tw-shadow-color: rgb(255 87 34 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-deep-purple-500\/40:hover {
  --tw-shadow-color: rgb(103 58 183 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-gray-900\/20:hover {
  --tw-shadow-color: rgb(33 33 33 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-green-500\/40:hover {
  --tw-shadow-color: rgb(76 175 80 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-indigo-500\/40:hover {
  --tw-shadow-color: rgb(63 81 181 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-light-blue-500\/40:hover {
  --tw-shadow-color: rgb(3 169 244 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-light-green-500\/40:hover {
  --tw-shadow-color: rgb(139 195 74 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-lime-500\/40:hover {
  --tw-shadow-color: rgb(205 220 57 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-orange-500\/40:hover {
  --tw-shadow-color: rgb(255 152 0 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-pink-500\/40:hover {
  --tw-shadow-color: rgb(233 30 99 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-purple-500\/40:hover {
  --tw-shadow-color: rgb(156 39 176 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-red-500\/40:hover {
  --tw-shadow-color: rgb(244 67 54 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-teal-500\/40:hover {
  --tw-shadow-color: rgb(0 150 136 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-yellow-500\/40:hover {
  --tw-shadow-color: rgb(255 235 59 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:brightness-110:hover {
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.hover\:before\:opacity-10:hover::before {
  content: var(--tw-content);
  opacity: 0.1;
}

.focus\:scale-110:focus {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.focus\:rounded-l-none:focus {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

.focus\:border-2:focus {
  border-width: 2px;
}

.focus\:\!border-black:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.focus\:\!border-blue-500:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1)) !important;
}

.focus\:\!border-indigo-500:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1)) !important;
}

.focus\:\!border-white:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}

.focus\:border-amber-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.focus\:border-black:focus {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}

.focus\:border-blue-gray-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}

.focus\:border-blue-gray-900:focus {
  --tw-border-opacity: 1;
  border-color: rgb(38 50 56 / var(--tw-border-opacity, 1));
}

.focus\:border-brown-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}

.focus\:border-cyan-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}

.focus\:border-deep-orange-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}

.focus\:border-deep-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}

.focus\:border-gray-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(158 158 158 / var(--tw-border-opacity, 1));
}

.focus\:border-gray-900:focus {
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}

.focus\:border-green-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.focus\:border-indigo-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.focus\:border-light-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}

.focus\:border-light-green-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}

.focus\:border-lime-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}

.focus\:border-orange-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}

.focus\:border-pink-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}

.focus\:border-purple-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.focus\:border-teal-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.focus\:border-white:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.focus\:border-yellow-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}

.focus\:\!border-t-gray-900:focus {
  --tw-border-opacity: 1 !important;
  border-top-color: rgb(33 33 33 / var(--tw-border-opacity, 1)) !important;
}

.focus\:border-t-transparent:focus {
  border-top-color: transparent;
}

.focus\:bg-blue-gray-50:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}

.focus\:bg-red-500\/10:focus {
  background-color: rgb(244 67 54 / 0.1);
}

.focus\:bg-transparent:focus {
  background-color: transparent;
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.focus\:bg-opacity-80:focus {
  --tw-bg-opacity: 0.8;
}

.focus\:text-blue-gray-500:focus {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.focus\:text-blue-gray-900:focus {
  --tw-text-opacity: 1;
  color: rgb(38 50 56 / var(--tw-text-opacity, 1));
}

.focus\:opacity-\[0\.85\]:focus {
  opacity: 0.85;
}

.focus\:shadow-none:focus {
  --tw-shadow: 0 0 rgb(0, 0 / 0, 0);
  --tw-shadow-colored: 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline-0:focus {
  outline-width: 0px;
}

.focus\:\!ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}

.focus\:ring:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:\!ring-indigo-200:focus {
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(159 168 218 / var(--tw-ring-opacity, 1)) !important;
}

.focus\:ring-\[\#497ef7\]\/40:focus {
  --tw-ring-color: rgb(73 126 247 / 0.4);
}

.focus\:ring-amber-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 224 130 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(144 202 249 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(33 150 243 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-gray-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(176 190 197 / var(--tw-ring-opacity, 1));
}

.focus\:ring-brown-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(188 170 164 / var(--tw-ring-opacity, 1));
}

.focus\:ring-cyan-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(128 222 234 / var(--tw-ring-opacity, 1));
}

.focus\:ring-deep-orange-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 171 145 / var(--tw-ring-opacity, 1));
}

.focus\:ring-deep-purple-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(179 157 219 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(238 238 238 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 224 224 / var(--tw-ring-opacity, 1));
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(158 158 158 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(165 214 167 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(76 175 80 / var(--tw-ring-opacity, 1));
}

.focus\:ring-green-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(67 160 71 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(159 168 218 / var(--tw-ring-opacity, 1));
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(63 81 181 / var(--tw-ring-opacity, 1));
}

.focus\:ring-light-blue-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(129 212 250 / var(--tw-ring-opacity, 1));
}

.focus\:ring-light-green-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(197 225 165 / var(--tw-ring-opacity, 1));
}

.focus\:ring-lime-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(230 238 156 / var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 204 128 / var(--tw-ring-opacity, 1));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 152 0 / var(--tw-ring-opacity, 1));
}

.focus\:ring-pink-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 143 177 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(206 147 216 / var(--tw-ring-opacity, 1));
}

.focus\:ring-purple-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 39 176 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 154 154 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 67 54 / var(--tw-ring-opacity, 1));
}

.focus\:ring-red-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 57 53 / var(--tw-ring-opacity, 1));
}

.focus\:ring-teal-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(128 203 196 / var(--tw-ring-opacity, 1));
}

.focus\:ring-white\/50:focus {
  --tw-ring-color: rgb(255 255 255 / 0.5);
}

.focus\:ring-yellow-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 245 157 / var(--tw-ring-opacity, 1));
}

.focus\:ring-yellow-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 216 53 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:placeholder\:opacity-100:focus::-moz-placeholder {
  opacity: 1;
}

.focus\:placeholder\:opacity-100:focus::placeholder {
  opacity: 1;
}

.active\:scale-100:active {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:bg-amber-500\/30:active {
  background-color: rgb(255 193 7 / 0.3);
}

.active\:bg-blue-500\/30:active {
  background-color: rgb(33 150 243 / 0.3);
}

.active\:bg-blue-gray-50:active {
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}

.active\:bg-blue-gray-500\/30:active {
  background-color: rgb(96 125 139 / 0.3);
}

.active\:bg-brown-500\/30:active {
  background-color: rgb(121 85 72 / 0.3);
}

.active\:bg-cyan-500\/30:active {
  background-color: rgb(0 188 212 / 0.3);
}

.active\:bg-deep-orange-500\/30:active {
  background-color: rgb(255 87 34 / 0.3);
}

.active\:bg-deep-purple-500\/30:active {
  background-color: rgb(103 58 183 / 0.3);
}

.active\:bg-gray-900\/20:active {
  background-color: rgb(33 33 33 / 0.2);
}

.active\:bg-green-500\/30:active {
  background-color: rgb(76 175 80 / 0.3);
}

.active\:bg-indigo-500\/30:active {
  background-color: rgb(63 81 181 / 0.3);
}

.active\:bg-light-blue-500\/30:active {
  background-color: rgb(3 169 244 / 0.3);
}

.active\:bg-light-green-500\/30:active {
  background-color: rgb(139 195 74 / 0.3);
}

.active\:bg-lime-500\/30:active {
  background-color: rgb(205 220 57 / 0.3);
}

.active\:bg-orange-500\/30:active {
  background-color: rgb(255 152 0 / 0.3);
}

.active\:bg-pink-500\/30:active {
  background-color: rgb(233 30 99 / 0.3);
}

.active\:bg-purple-500\/30:active {
  background-color: rgb(156 39 176 / 0.3);
}

.active\:bg-red-500\/30:active {
  background-color: rgb(244 67 54 / 0.3);
}

.active\:bg-teal-500\/30:active {
  background-color: rgb(0 150 136 / 0.3);
}

.active\:bg-transparent:active {
  background-color: transparent;
}

.active\:bg-white\/30:active {
  background-color: rgb(255 255 255 / 0.3);
}

.active\:bg-yellow-500\/30:active {
  background-color: rgb(255 235 59 / 0.3);
}

.active\:bg-opacity-80:active {
  --tw-bg-opacity: 0.8;
}

.active\:text-blue-gray-500:active {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.active\:text-blue-gray-900:active {
  --tw-text-opacity: 1;
  color: rgb(38 50 56 / var(--tw-text-opacity, 1));
}

.active\:opacity-\[0\.85\]:active {
  opacity: 0.85;
}

.active\:shadow-none:active {
  --tw-shadow: 0 0 rgb(0, 0 / 0, 0);
  --tw-shadow-colored: 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:resize-none:disabled {
  resize: none;
}

.disabled\:border-0:disabled {
  border-width: 0px;
}

.disabled\:bg-blue-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(100 181 246 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-blue-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(66 165 245 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-blue-gray-50:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(236 239 241 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-100:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));
}

.disabled\:bg-gray-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(224 224 224 / var(--tw-bg-opacity, 1));
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:shadow-none:disabled {
  --tw-shadow: 0 0 rgb(0, 0 / 0, 0);
  --tw-shadow-colored: 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group[open] .group-open\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:w-\[110\%\] {
  width: 110%;
}

.group:hover .group-hover\:-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-y-100 {
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(25 118 210 / var(--tw-border-opacity, 1));
}

.group:hover .group-hover\:bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(187 222 251 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(30 136 229 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(25 118 210 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(158 158 158 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:underline {
  text-decoration-line: underline;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.peer:checked ~ .peer-checked\:translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:border-amber-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-blue-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-brown-500 {
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-cyan-500 {
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-deep-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-deep-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-indigo-500 {
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-light-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-light-green-500 {
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-lime-500 {
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-pink-500 {
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-purple-500 {
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-teal-500 {
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:border-yellow-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 136 229 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(67 160 71 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(251 140 0 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:opacity-100 {
  opacity: 1;
}

.peer:checked ~ .peer-checked\:before\:bg-amber-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 193 7 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-blue-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 150 243 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-blue-gray-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(96 125 139 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-brown-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(121 85 72 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-cyan-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(0 188 212 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-deep-orange-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 87 34 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-deep-purple-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(103 58 183 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-gray-900::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 33 33 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-green-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(76 175 80 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-indigo-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(63 81 181 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-light-blue-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(3 169 244 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-light-green-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(139 195 74 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-lime-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(205 220 57 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-orange-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 152 0 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-pink-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(233 30 99 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-purple-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(156 39 176 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-red-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(244 67 54 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-teal-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(0 150 136 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:before\:bg-yellow-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 235 59 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:text-sm {
  font-size: .875rem;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:text-sm {
  font-size: .875rem;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:leading-\[3\.75\] {
  line-height: 3.75;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:leading-\[3\.75\] {
  line-height: 3.75;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:leading-\[4\.1\] {
  line-height: 4.1;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:leading-\[4\.1\] {
  line-height: 4.1;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:leading-\[4\.25\] {
  line-height: 4.25;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:leading-\[4\.25\] {
  line-height: 4.25;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:leading-\[4\.875\] {
  line-height: 4.875;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:leading-\[4\.875\] {
  line-height: 4.875;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:leading-tight {
  line-height: 1.25;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:leading-tight {
  line-height: 1.25;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:text-blue-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:text-blue-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(76 175 80 / var(--tw-text-opacity, 1));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(76 175 80 / var(--tw-text-opacity, 1));
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(244 67 54 / var(--tw-text-opacity, 1));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(244 67 54 / var(--tw-text-opacity, 1));
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:before\:border-transparent::before {
  content: var(--tw-content);
  border-color: transparent;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:before\:border-transparent::before {
  content: var(--tw-content);
  border-color: transparent;
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:after\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:after\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}

.peer:focus ~ .peer-focus\:text-\[11px\] {
  font-size: 11px;
}

.peer:focus ~ .peer-focus\:text-sm {
  font-size: .875rem;
}

.peer:focus ~ .peer-focus\:leading-tight {
  line-height: 1.25;
}

.peer:focus ~ .peer-focus\:\!text-black {
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(255 193 7 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(33 150 243 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-blue-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-brown-500 {
  --tw-text-opacity: 1;
  color: rgb(121 85 72 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-cyan-500 {
  --tw-text-opacity: 1;
  color: rgb(0 188 212 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-deep-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(255 87 34 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-deep-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(103 58 183 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(33 33 33 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(76 175 80 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-indigo-500 {
  --tw-text-opacity: 1;
  color: rgb(63 81 181 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-light-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(3 169 244 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-light-green-500 {
  --tw-text-opacity: 1;
  color: rgb(139 195 74 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-lime-500 {
  --tw-text-opacity: 1;
  color: rgb(205 220 57 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(255 152 0 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-pink-500 {
  --tw-text-opacity: 1;
  color: rgb(233 30 99 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(156 39 176 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(244 67 54 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-teal-500 {
  --tw-text-opacity: 1;
  color: rgb(0 150 136 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(255 235 59 / var(--tw-text-opacity, 1));
}

.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus ~ .peer-focus\:ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 181 246 / var(--tw-ring-opacity, 1));
}

.peer:focus ~ .peer-focus\:ring-blue-600\/20 {
  --tw-ring-color: rgb(30 136 229 / 0.2);
}

.peer:focus ~ .peer-focus\:ring-green-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(129 199 132 / var(--tw-ring-opacity, 1));
}

.peer:focus ~ .peer-focus\:ring-orange-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 183 77 / var(--tw-ring-opacity, 1));
}

.peer:focus ~ .peer-focus\:before\:border-l-2::before {
  content: var(--tw-content);
  border-left-width: 2px;
}

.peer:focus ~ .peer-focus\:before\:border-t-2::before {
  content: var(--tw-content);
  border-top-width: 2px;
}

.peer:focus ~ .peer-focus\:before\:\!border-amber-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-black::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-blue-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-blue-gray-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-brown-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-cyan-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-deep-orange-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-deep-purple-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-gray-900::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-green-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-indigo-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-light-blue-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-light-green-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-lime-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-orange-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-pink-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-purple-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-red-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-teal-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-white::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:\!border-yellow-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:before\:border-green-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:before\:border-red-500::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:scale-x-100::after {
  content: var(--tw-content);
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:after\:border-r-2::after {
  content: var(--tw-content);
  border-right-width: 2px;
}

.peer:focus ~ .peer-focus\:after\:border-t-2::after {
  content: var(--tw-content);
  border-top-width: 2px;
}

.peer:focus ~ .peer-focus\:after\:\!border-amber-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-black::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-blue-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-blue-gray-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-brown-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-cyan-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-deep-orange-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-deep-purple-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-green-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-indigo-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-light-blue-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-light-green-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-lime-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-orange-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-pink-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-purple-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-red-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-teal-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:\!border-yellow-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1)) !important;
}

.peer:focus ~ .peer-focus\:after\:border-amber-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 193 7 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-black::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-blue-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(33 150 243 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-blue-gray-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(96 125 139 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-brown-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(121 85 72 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-cyan-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 188 212 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-deep-orange-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 87 34 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-deep-purple-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(103 58 183 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-gray-900::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(33 33 33 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-green-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(76 175 80 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-indigo-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(63 81 181 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-light-blue-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(3 169 244 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-light-green-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(139 195 74 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-lime-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(205 220 57 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-orange-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 152 0 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-pink-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(233 30 99 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-purple-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(156 39 176 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-red-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(244 67 54 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-teal-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(0 150 136 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.peer:focus ~ .peer-focus\:after\:border-yellow-500::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 235 59 / var(--tw-border-opacity, 1));
}

.peer:disabled ~ .peer-disabled\:text-blue-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(120 144 156 / var(--tw-text-opacity, 1));
}

.peer:disabled ~ .peer-disabled\:text-transparent {
  color: transparent;
}

.peer:disabled ~ .peer-disabled\:before\:border-transparent::before {
  content: var(--tw-content);
  border-color: transparent;
}

.peer:disabled ~ .peer-disabled\:after\:border-transparent::after {
  content: var(--tw-content);
  border-color: transparent;
}

.peer:disabled:-moz-placeholder-shown ~ .peer-disabled\:peer-placeholder-shown\:text-blue-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

.peer:disabled:placeholder-shown ~ .peer-disabled\:peer-placeholder-shown\:text-blue-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(96 125 139 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {

  .sm\:static {
    position: static;
  }

  .sm\:-right-9 {
    right: -2.25rem;
  }

  .sm\:top-8 {
    top: 8px;
  }

  .sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-12 {
    margin-bottom: 3rem;
  }

  .sm\:mb-16 {
    margin-bottom: 4rem;
  }

  .sm\:mb-2 {
    margin-bottom: 0.5rem;
  }

  .sm\:mb-24 {
    margin-bottom: 6rem;
  }

  .sm\:mb-28 {
    margin-bottom: 7rem;
  }

  .sm\:mb-4 {
    margin-bottom: 1rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:mb-8 {
    margin-bottom: 2rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:ml-5 {
    margin-left: 1.25rem;
  }

  .sm\:ml-auto {
    margin-left: auto;
  }

  .sm\:mr-2 {
    margin-right: 0.5rem;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mt-24 {
    margin-top: 6rem;
  }

  .sm\:mt-3 {
    margin-top: 0.75rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-12 {
    height: 3rem;
  }

  .sm\:h-14 {
    height: 3.5rem;
  }

  .sm\:h-4 {
    height: 1rem;
  }

  .sm\:h-5 {
    height: 1.25rem;
  }

  .sm\:h-6 {
    height: 1.5rem;
  }

  .sm\:h-8 {
    height: 2rem;
  }

  .sm\:h-80 {
    height: 20rem;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-1\/3 {
    width: 33.333333%;
  }

  .sm\:w-1\/4 {
    width: 25%;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-12 {
    width: 3rem;
  }

  .sm\:w-2\/3 {
    width: 66.666667%;
  }

  .sm\:w-28 {
    width: 7rem;
  }

  .sm\:w-4 {
    width: 1rem;
  }

  .sm\:w-40 {
    width: 10rem;
  }

  .sm\:w-5 {
    width: 1.25rem;
  }

  .sm\:w-6 {
    width: 1.5rem;
  }

  .sm\:w-64 {
    width: 16rem;
  }

  .sm\:w-8 {
    width: 2rem;
  }

  .sm\:w-80 {
    width: 20rem;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-\[320px\] {
    width: 320px;
  }

  .sm\:w-\[380px\] {
    width: 380px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:flex-1 {
    flex: 1 1 0%;
  }

  .sm\:flex-auto {
    flex: 1 1 auto;
  }

  .sm\:flex-none {
    flex: none;
  }

  .sm\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-0 {
    gap: 0px;
  }

  .sm\:gap-3 {
    gap: 0.75rem;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .sm\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .sm\:self-auto {
    align-self: auto;
  }

  .sm\:rounded-none {
    border-radius: 0px;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-3 {
    padding: 0.75rem;
  }

  .sm\:p-4 {
    padding: 1rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:pt-4 {
    padding-top: 1rem;
  }

  .sm\:text-right {
    text-align: right;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
  }

  .sm\:text-base {
    font-size: 1rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
  }

  .sm\:text-sm {
    font-size: .875rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
  }
}

@media (min-width: 768px) {

  .md\:absolute {
    position: absolute;
  }

  .md\:sticky {
    position: sticky;
  }

  .md\:-bottom-0 {
    bottom: -0px;
  }

  .md\:-bottom-12 {
    bottom: -3rem;
  }

  .md\:left-1\/2 {
    left: 50%;
  }

  .md\:left-\[42\%\] {
    left: 42%;
  }

  .md\:left-\[50\%\] {
    left: 50%;
  }

  .md\:left-\[calc\(45\%\+14rem\)\] {
    left: calc(45% + 14rem);
  }

  .md\:left-\[calc\(45\%\+20rem\)\] {
    left: calc(45% + 20rem);
  }

  .md\:left-\[calc\(45\%\+26rem\)\] {
    left: calc(45% + 26rem);
  }

  .md\:left-\[calc\(45\%\+8rem\)\] {
    left: calc(45% + 8rem);
  }

  .md\:left-\[calc\(45\%-12\.5rem\)\] {
    left: calc(45% - 12.5rem);
  }

  .md\:left-\[calc\(45\%-18rem\)\] {
    left: calc(45% - 18rem);
  }

  .md\:left-\[calc\(45\%-23\.5rem\)\] {
    left: calc(45% - 23.5rem);
  }

  .md\:left-\[calc\(45\%-7rem\)\] {
    left: calc(45% - 7rem);
  }

  .md\:top-28 {
    top: 7rem;
  }

  .md\:top-8 {
    top: 8px;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:m-0 {
    margin: 0px;
  }

  .md\:m-auto {
    margin: auto;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:my-10 {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }

  .md\:my-auto {
    margin-top: auto;
    margin-bottom: auto;
  }

  .md\:-mt-52 {
    margin-top: -13rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-14 {
    margin-bottom: 3.5rem;
  }

  .md\:mb-16 {
    margin-bottom: 4rem;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-52 {
    margin-bottom: 13rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:ml-24 {
    margin-left: 6rem;
  }

  .md\:ml-\[250px\] {
    margin-left: 250px;
  }

  .md\:mr-5 {
    margin-right: 1.25rem;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-20 {
    margin-top: 5rem;
  }

  .md\:mt-24 {
    margin-top: 6rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:mt-auto {
    margin-top: auto;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-table {
    display: inline-table;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-10 {
    height: 2.5rem;
  }

  .md\:h-12 {
    height: 3rem;
  }

  .md\:h-14 {
    height: 3.5rem;
  }

  .md\:h-16 {
    height: 4rem;
  }

  .md\:h-20 {
    height: 5rem;
  }

  .md\:h-24 {
    height: 6rem;
  }

  .md\:h-32 {
    height: 8rem;
  }

  .md\:h-4 {
    height: 1rem;
  }

  .md\:h-48 {
    height: 12rem;
  }

  .md\:h-5 {
    height: 1.25rem;
  }

  .md\:h-6 {
    height: 1.5rem;
  }

  .md\:h-\[26rem\] {
    height: 26rem;
  }

  .md\:h-\[450px\] {
    height: 450px;
  }

  .md\:h-\[465px\] {
    height: 465px;
  }

  .md\:h-\[500px\] {
    height: 500px;
  }

  .md\:h-\[80vh\] {
    height: 80vh;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:max-h-\[87vh\] {
    max-height: 87vh;
  }

  .md\:min-h-\[500px\] {
    min-height: 500px;
  }

  .md\:min-h-\[87vh\] {
    min-height: 87vh;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-1\/4 {
    width: 25%;
  }

  .md\:w-10 {
    width: 2.5rem;
  }

  .md\:w-12 {
    width: 3rem;
  }

  .md\:w-14 {
    width: 3.5rem;
  }

  .md\:w-16 {
    width: 4rem;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-2\/5 {
    width: 40%;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-24 {
    width: 6rem;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-3\/5 {
    width: 60%;
  }

  .md\:w-4 {
    width: 1rem;
  }

  .md\:w-4\/5 {
    width: 80%;
  }

  .md\:w-5 {
    width: 1.25rem;
  }

  .md\:w-5\/6 {
    width: 83.333333%;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:w-64 {
    width: 16rem;
  }

  .md\:w-96 {
    width: 24rem;
  }

  .md\:w-\[250px\] {
    width: 250px;
  }

  .md\:w-\[300px\] {
    width: 300px;
  }

  .md\:w-\[380px\] {
    width: 380px;
  }

  .md\:w-\[40\%\] {
    width: 40%;
  }

  .md\:w-\[450px\] {
    width: 450px;
  }

  .md\:w-\[465px\] {
    width: 465px;
  }

  .md\:w-\[540px\] {
    width: 540px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:w-max {
    width: -moz-max-content;
    width: max-content;
  }

  .md\:min-w-\[60\%\] {
    min-width: 60%;
  }

  .md\:min-w-\[66\.666667\%\] {
    min-width: 66.666667%;
  }

  .md\:min-w-\[75\%\] {
    min-width: 75%;
  }

  .md\:min-w-\[83\.333333\%\] {
    min-width: 83.333333%;
  }

  .md\:max-w-2xl {
    max-width: 42rem;
  }

  .md\:max-w-3xl {
    max-width: 48rem;
  }

  .md\:max-w-5xl {
    max-width: 64rem;
  }

  .md\:max-w-\[60\%\] {
    max-width: 60%;
  }

  .md\:max-w-\[66\.666667\%\] {
    max-width: 66.666667%;
  }

  .md\:max-w-\[75\%\] {
    max-width: 75%;
  }

  .md\:max-w-\[83\.333333\%\] {
    max-width: 83.333333%;
  }

  .md\:max-w-\[90\%\] {
    max-width: 90%;
  }

  .md\:max-w-lg {
    max-width: 32rem;
  }

  .md\:max-w-screen-lg {
    max-width: 1024px;
  }

  .md\:max-w-xl {
    max-width: 36rem;
  }

  .md\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .md\:basis-1\/2 {
    flex-basis: 50%;
  }

  .md\:basis-3\/4 {
    flex-basis: 75%;
  }

  .md\:-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-10 {
    --tw-translate-y: 2.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-6 {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-end {
    align-items: flex-end;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-normal {
    justify-content: normal;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:justify-evenly {
    justify-content: space-evenly;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-32 {
    gap: 8rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:gap-x-0 {
    -moz-column-gap: 0px;
         column-gap: 0px;
  }

  .md\:gap-x-10 {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }

  .md\:gap-x-2 {
    -moz-column-gap: 0.5rem;
         column-gap: 0.5rem;
  }

  .md\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  .md\:gap-x-6 {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }

  .md\:gap-y-4 {
    row-gap: 1rem;
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .md\:overflow-hidden {
    overflow: hidden;
  }

  .md\:overflow-x-auto {
    overflow-x: auto;
  }

  .md\:rounded-2xl {
    border-radius: 1rem;
  }

  .md\:rounded-full {
    border-radius: 9999px;
  }

  .md\:rounded-xl {
    border-radius: 0.75rem;
  }

  .md\:border {
    border-width: 1px;
  }

  .md\:bg-\[\#F2F4FB\] {
    --tw-bg-opacity: 1;
    background-color: rgb(242 244 251 / var(--tw-bg-opacity, 1));
  }

  .md\:bg-gradient-primary {
    background-image: linear-gradient(0deg, #FFFFFF, #FFFFFF), linear-gradient(135.8deg, #3D71E9 7.07%, #59A7F7 101.34%);
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-16 {
    padding: 4rem;
  }

  .md\:p-3 {
    padding: 0.75rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-5 {
    padding: 1.25rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .md\:px-32 {
    padding-left: 8rem;
    padding-right: 8rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-14 {
    padding-top: 3.5rem;
    padding-bottom: 3.5rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-start {
    text-align: start;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
  }

  .md\:text-\[26px\] {
    font-size: 26px;
  }

  .md\:text-base {
    font-size: 1rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
  }

  .md\:text-sm {
    font-size: .875rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
  }

  .md\:font-medium {
    font-weight: 500;
  }

  .md\:shadow-inner {
    --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .md\:shadow-sm {
    --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .md\:hover\:scale-105:hover {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

@media (min-width: 1024px) {

  .lg\:static {
    position: static;
  }

  .lg\:sticky {
    position: sticky;
  }

  .lg\:top-20 {
    top: 5rem;
  }

  .lg\:top-\[37px\] {
    top: 37px;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-16 {
    margin-bottom: 4rem;
  }

  .lg\:mb-32 {
    margin-bottom: 8rem;
  }

  .lg\:mb-36 {
    margin-bottom: 9rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-1\.5 {
    margin-top: 0.375rem;
  }

  .lg\:mt-32 {
    margin-top: 8rem;
  }

  .lg\:mt-\[-30px\] {
    margin-top: -30px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[50px\] {
    height: 50px;
  }

  .lg\:h-\[80px\] {
    height: 80px;
  }

  .lg\:max-h-\[calc\(100vh-6rem\)\] {
    max-height: calc(100vh - 6rem);
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-2\/4 {
    width: 50%;
  }

  .lg\:w-2\/5 {
    width: 40%;
  }

  .lg\:w-3\/4 {
    width: 75%;
  }

  .lg\:w-3\/5 {
    width: 60%;
  }

  .lg\:w-4\/5 {
    width: 80%;
  }

  .lg\:w-\[150px\] {
    width: 150px;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:min-w-\[40\%\] {
    min-width: 40%;
  }

  .lg\:min-w-\[50\%\] {
    min-width: 50%;
  }

  .lg\:min-w-\[60\%\] {
    min-width: 60%;
  }

  .lg\:min-w-\[75\%\] {
    min-width: 75%;
  }

  .lg\:max-w-\[40\%\] {
    max-width: 40%;
  }

  .lg\:max-w-\[50\%\] {
    max-width: 50%;
  }

  .lg\:max-w-\[60\%\] {
    max-width: 60%;
  }

  .lg\:max-w-\[75\%\] {
    max-width: 75%;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-md {
    max-width: 28rem;
  }

  .lg\:flex-shrink {
    flex-shrink: 1;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:overflow-hidden {
    overflow: hidden;
  }

  .lg\:p-1 {
    padding: 0.25rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .lg\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .lg\:pl-20 {
    padding-left: 5rem;
  }

  .lg\:pl-8 {
    padding-left: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-10 {
    padding-top: 2.5rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
  }

  .lg\:text-6xl {
    font-size: 4rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
  }
}

@media (min-width: 1280px) {

  .xl\:mb-12 {
    margin-bottom: 3rem;
  }

  .xl\:mt-10 {
    margin-top: 2.5rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:h-10 {
    height: 2.5rem;
  }

  .xl\:h-12 {
    height: 3rem;
  }

  .xl\:h-8 {
    height: 2rem;
  }

  .xl\:w-10 {
    width: 2.5rem;
  }

  .xl\:w-12 {
    width: 3rem;
  }

  .xl\:w-2\/5 {
    width: 40%;
  }

  .xl\:w-3\/5 {
    width: 60%;
  }

  .xl\:w-8 {
    width: 2rem;
  }

  .xl\:w-full {
    width: 100%;
  }

  .xl\:max-w-6xl {
    max-width: 72rem;
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:gap-y-10 {
    row-gap: 2.5rem;
  }

  .xl\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .xl\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .xl\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .xl\:pb-20 {
    padding-bottom: 5rem;
  }

  .xl\:pt-10 {
    padding-top: 2.5rem;
  }

  .xl\:text-right {
    text-align: right;
  }

  .xl\:text-4xl {
    font-size: 2.25rem;
  }

  .xl\:text-6xl {
    font-size: 4rem;
  }

  .xl\:text-base {
    font-size: 1rem;
  }

  .xl\:text-lg {
    font-size: 1.125rem;
  }

  .xl\:text-sm {
    font-size: .875rem;
  }

  .xl\:text-xl {
    font-size: 1.25rem;
  }
}

@media (min-width: 1536px) {

  .\32xl\:mt-16 {
    margin-top: 4rem;
  }

  .\32xl\:block {
    display: block;
  }

  .\32xl\:w-1\/3 {
    width: 33.333333%;
  }

  .\32xl\:w-1\/4 {
    width: 25%;
  }

  .\32xl\:w-2\/3 {
    width: 66.666667%;
  }

  .\32xl\:w-2\/5 {
    width: 40%;
  }

  .\32xl\:w-3\/4 {
    width: 75%;
  }

  .\32xl\:w-3\/5 {
    width: 60%;
  }

  .\32xl\:min-w-\[25\%\] {
    min-width: 25%;
  }

  .\32xl\:min-w-\[33\.333333\%\] {
    min-width: 33.333333%;
  }

  .\32xl\:min-w-\[40\%\] {
    min-width: 40%;
  }

  .\32xl\:min-w-\[60\%\] {
    min-width: 60%;
  }

  .\32xl\:min-w-\[75\%\] {
    min-width: 75%;
  }

  .\32xl\:max-w-7xl {
    max-width: 80rem;
  }

  .\32xl\:max-w-\[25\%\] {
    max-width: 25%;
  }

  .\32xl\:max-w-\[33\.333333\%\] {
    max-width: 33.333333%;
  }

  .\32xl\:max-w-\[40\%\] {
    max-width: 40%;
  }

  .\32xl\:max-w-\[60\%\] {
    max-width: 60%;
  }

  .\32xl\:max-w-\[75\%\] {
    max-width: 75%;
  }

  .\32xl\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .\32xl\:text-4xl {
    font-size: 2.25rem;
  }

  .\32xl\:text-5xl {
    font-size: 3rem;
  }

  .\32xl\:text-base {
    font-size: 1rem;
  }

  .\32xl\:text-lg {
    font-size: 1.125rem;
  }

  .\32xl\:text-xl {
    font-size: 1.25rem;
  }
}

@media (min-width: 475px) {

  .xs\:h-\[400px\] {
    height: 400px;
  }

  .xs\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1600px) {

  .\33xl\:w-\[500px\] {
    width: 500px;
  }

  .\33xl\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .\33xl\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .\33xl\:text-5xl {
    font-size: 3rem;
  }

  .\33xl\:text-base {
    font-size: 1rem;
  }
}

.\[\&\:\:-moz-range-thumb\]\:relative::-moz-range-thumb {
  position: relative;
}

.\[\&\:\:-moz-range-thumb\]\:z-20::-moz-range-thumb {
  z-index: 20;
}

.\[\&\:\:-moz-range-thumb\]\:-mt-1::-moz-range-thumb {
  margin-top: -0.25rem;
}

.\[\&\:\:-moz-range-thumb\]\:-mt-\[3px\]::-moz-range-thumb {
  margin-top: -3px;
}

.\[\&\:\:-moz-range-thumb\]\:h-2\.5::-moz-range-thumb {
  height: 0.625rem;
}

.\[\&\:\:-moz-range-thumb\]\:h-3\.5::-moz-range-thumb {
  height: 0.875rem;
}

.\[\&\:\:-moz-range-thumb\]\:h-5::-moz-range-thumb {
  height: 1.25rem;
}

.\[\&\:\:-moz-range-thumb\]\:w-2\.5::-moz-range-thumb {
  width: 0.625rem;
}

.\[\&\:\:-moz-range-thumb\]\:w-3\.5::-moz-range-thumb {
  width: 0.875rem;
}

.\[\&\:\:-moz-range-thumb\]\:w-5::-moz-range-thumb {
  width: 1.25rem;
}

.\[\&\:\:-moz-range-thumb\]\:appearance-none::-moz-range-thumb {
  -moz-appearance: none;
       appearance: none;
}

.\[\&\:\:-moz-range-thumb\]\:rounded-full::-moz-range-thumb {
  border-radius: 9999px;
}

.\[\&\:\:-moz-range-thumb\]\:border-0::-moz-range-thumb {
  border-width: 0px;
}

.\[\&\:\:-moz-range-thumb\]\:bg-white::-moz-range-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.\[\&\:\:-moz-range-thumb\]\:ring-2::-moz-range-thumb {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.\[\&\:\:-moz-range-thumb\]\:ring-current::-moz-range-thumb {
  --tw-ring-color: currentColor;
}

.\[\&\:\:-moz-range-thumb\]\:\[-webkit-appearance\:none\]::-moz-range-thumb {
  -webkit-appearance: none;
}

.\[\&\:\:-moz-range-track\]\:h-full::-moz-range-track {
  height: 100%;
}

.\[\&\:\:-moz-range-track\]\:rounded-full::-moz-range-track {
  border-radius: 9999px;
}

.\[\&\:\:-moz-range-track\]\:bg-blue-gray-100::-moz-range-track {
  --tw-bg-opacity: 1;
  background-color: rgb(207 216 220 / var(--tw-bg-opacity, 1));
}

.\[\&\:\:-webkit-slider-runnable-track\]\:h-full::-webkit-slider-runnable-track {
  height: 100%;
}

.\[\&\:\:-webkit-slider-runnable-track\]\:rounded-full::-webkit-slider-runnable-track {
  border-radius: 9999px;
}

.\[\&\:\:-webkit-slider-runnable-track\]\:bg-blue-gray-100::-webkit-slider-runnable-track {
  --tw-bg-opacity: 1;
  background-color: rgb(207 216 220 / var(--tw-bg-opacity, 1));
}

.\[\&\:\:-webkit-slider-thumb\]\:relative::-webkit-slider-thumb {
  position: relative;
}

.\[\&\:\:-webkit-slider-thumb\]\:z-20::-webkit-slider-thumb {
  z-index: 20;
}

.\[\&\:\:-webkit-slider-thumb\]\:-mt-1::-webkit-slider-thumb {
  margin-top: -0.25rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:-mt-\[3px\]::-webkit-slider-thumb {
  margin-top: -3px;
}

.\[\&\:\:-webkit-slider-thumb\]\:h-2\.5::-webkit-slider-thumb {
  height: 0.625rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:h-3\.5::-webkit-slider-thumb {
  height: 0.875rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:h-5::-webkit-slider-thumb {
  height: 1.25rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:w-2::-webkit-slider-thumb {
  width: 0.5rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:w-2\.5::-webkit-slider-thumb {
  width: 0.625rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:w-3::-webkit-slider-thumb {
  width: 0.75rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:w-3\.5::-webkit-slider-thumb {
  width: 0.875rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:w-5::-webkit-slider-thumb {
  width: 1.25rem;
}

.\[\&\:\:-webkit-slider-thumb\]\:appearance-none::-webkit-slider-thumb {
  -webkit-appearance: none;
          appearance: none;
}

.\[\&\:\:-webkit-slider-thumb\]\:rounded-full::-webkit-slider-thumb {
  border-radius: 9999px;
}

.\[\&\:\:-webkit-slider-thumb\]\:border-0::-webkit-slider-thumb {
  border-width: 0px;
}

.\[\&\:\:-webkit-slider-thumb\]\:bg-white::-webkit-slider-thumb {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.\[\&\:\:-webkit-slider-thumb\]\:ring-2::-webkit-slider-thumb {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.\[\&\:\:-webkit-slider-thumb\]\:ring-current::-webkit-slider-thumb {
  --tw-ring-color: currentColor;
}

.\[\&\:\:-webkit-slider-thumb\]\:\[-webkit-appearance\:none\]::-webkit-slider-thumb {
  -webkit-appearance: none;
}
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./node_modules/react-toastify/dist/ReactToastify.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
:root {
  --toastify-color-light: #fff;
  --toastify-color-dark: #121212;
  --toastify-color-info: #3498db;
  --toastify-color-success: #07bc0c;
  --toastify-color-warning: #f1c40f;
  --toastify-color-error: #e74c3c;
  --toastify-color-transparent: rgba(255, 255, 255, 0.7);
  --toastify-icon-color-info: var(--toastify-color-info);
  --toastify-icon-color-success: var(--toastify-color-success);
  --toastify-icon-color-warning: var(--toastify-color-warning);
  --toastify-icon-color-error: var(--toastify-color-error);
  --toastify-toast-width: 320px;
  --toastify-toast-offset: 16px;
  --toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));
  --toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));
  --toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));
  --toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));
  --toastify-toast-background: #fff;
  --toastify-toast-min-height: 64px;
  --toastify-toast-max-height: 800px;
  --toastify-toast-bd-radius: 6px;
  --toastify-font-family: sans-serif;
  --toastify-z-index: 9999;
  --toastify-text-color-light: #757575;
  --toastify-text-color-dark: #fff;
  --toastify-text-color-info: #fff;
  --toastify-text-color-success: #fff;
  --toastify-text-color-warning: #fff;
  --toastify-text-color-error: #fff;
  --toastify-spinner-color: #616161;
  --toastify-spinner-color-empty-area: #e0e0e0;
  --toastify-color-progress-light: linear-gradient(
    to right,
    #4cd964,
    #5ac8fa,
    #007aff,
    #34aadc,
    #5856d6,
    #ff2d55
  );
  --toastify-color-progress-dark: #bb86fc;
  --toastify-color-progress-info: var(--toastify-color-info);
  --toastify-color-progress-success: var(--toastify-color-success);
  --toastify-color-progress-warning: var(--toastify-color-warning);
  --toastify-color-progress-error: var(--toastify-color-error);
  --toastify-color-progress-bgo: 0.2;
}

.Toastify__toast-container {
  z-index: var(--toastify-z-index);
  -webkit-transform: translate3d(0, 0, var(--toastify-z-index));
  position: fixed;
  padding: 4px;
  width: var(--toastify-toast-width);
  box-sizing: border-box;
  color: #fff;
}
.Toastify__toast-container--top-left {
  top: var(--toastify-toast-top);
  left: var(--toastify-toast-left);
}
.Toastify__toast-container--top-center {
  top: var(--toastify-toast-top);
  left: 50%;
  transform: translateX(-50%);
}
.Toastify__toast-container--top-right {
  top: var(--toastify-toast-top);
  right: var(--toastify-toast-right);
}
.Toastify__toast-container--bottom-left {
  bottom: var(--toastify-toast-bottom);
  left: var(--toastify-toast-left);
}
.Toastify__toast-container--bottom-center {
  bottom: var(--toastify-toast-bottom);
  left: 50%;
  transform: translateX(-50%);
}
.Toastify__toast-container--bottom-right {
  bottom: var(--toastify-toast-bottom);
  right: var(--toastify-toast-right);
}

@media only screen and (max-width : 480px) {
  .Toastify__toast-container {
    width: 100vw;
    padding: 0;
    left: env(safe-area-inset-left);
    margin: 0;
  }
  .Toastify__toast-container--top-left, .Toastify__toast-container--top-center, .Toastify__toast-container--top-right {
    top: env(safe-area-inset-top);
    transform: translateX(0);
  }
  .Toastify__toast-container--bottom-left, .Toastify__toast-container--bottom-center, .Toastify__toast-container--bottom-right {
    bottom: env(safe-area-inset-bottom);
    transform: translateX(0);
  }
  .Toastify__toast-container--rtl {
    right: env(safe-area-inset-right);
    left: initial;
  }
}
.Toastify__toast {
  --y: 0;
  position: relative;
  touch-action: none;
  min-height: var(--toastify-toast-min-height);
  box-sizing: border-box;
  margin-bottom: 1rem;
  padding: 8px;
  border-radius: var(--toastify-toast-bd-radius);
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  max-height: var(--toastify-toast-max-height);
  font-family: var(--toastify-font-family);
  cursor: default;
  direction: ltr;
  /* webkit only issue #791 */
  z-index: 0;
  overflow: hidden;
}
.Toastify__toast--stacked {
  position: absolute;
  width: 100%;
  transform: translate3d(0, var(--y), 0) scale(var(--s));
  transition: transform 0.3s;
}
.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body, .Toastify__toast--stacked[data-collapsed] .Toastify__close-button {
  transition: opacity 0.1s;
}
.Toastify__toast--stacked[data-collapsed=false] {
  overflow: visible;
}
.Toastify__toast--stacked[data-collapsed=true]:not(:last-child) > * {
  opacity: 0;
}
.Toastify__toast--stacked:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: calc(var(--g) * 1px);
  bottom: 100%;
}
.Toastify__toast--stacked[data-pos=top] {
  top: 0;
}
.Toastify__toast--stacked[data-pos=bot] {
  bottom: 0;
}
.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before {
  transform-origin: top;
}
.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before {
  transform-origin: bottom;
}
.Toastify__toast--stacked:before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  transform: scaleY(3);
  z-index: -1;
}
.Toastify__toast--rtl {
  direction: rtl;
}
.Toastify__toast--close-on-click {
  cursor: pointer;
}
.Toastify__toast-body {
  margin: auto 0;
  flex: 1 1 auto;
  padding: 6px;
  display: flex;
  align-items: center;
}
.Toastify__toast-body > div:last-child {
  word-break: break-word;
  flex: 1;
}
.Toastify__toast-icon {
  margin-inline-end: 10px;
  width: 20px;
  flex-shrink: 0;
  display: flex;
}

.Toastify--animate {
  animation-fill-mode: both;
  animation-duration: 0.5s;
}

.Toastify--animate-icon {
  animation-fill-mode: both;
  animation-duration: 0.3s;
}

@media only screen and (max-width : 480px) {
  .Toastify__toast {
    margin-bottom: 0;
    border-radius: 0;
  }
}
.Toastify__toast-theme--dark {
  background: var(--toastify-color-dark);
  color: var(--toastify-text-color-dark);
}
.Toastify__toast-theme--light {
  background: var(--toastify-color-light);
  color: var(--toastify-text-color-light);
}
.Toastify__toast-theme--colored.Toastify__toast--default {
  background: var(--toastify-color-light);
  color: var(--toastify-text-color-light);
}
.Toastify__toast-theme--colored.Toastify__toast--info {
  color: var(--toastify-text-color-info);
  background: var(--toastify-color-info);
}
.Toastify__toast-theme--colored.Toastify__toast--success {
  color: var(--toastify-text-color-success);
  background: var(--toastify-color-success);
}
.Toastify__toast-theme--colored.Toastify__toast--warning {
  color: var(--toastify-text-color-warning);
  background: var(--toastify-color-warning);
}
.Toastify__toast-theme--colored.Toastify__toast--error {
  color: var(--toastify-text-color-error);
  background: var(--toastify-color-error);
}

.Toastify__progress-bar-theme--light {
  background: var(--toastify-color-progress-light);
}
.Toastify__progress-bar-theme--dark {
  background: var(--toastify-color-progress-dark);
}
.Toastify__progress-bar--info {
  background: var(--toastify-color-progress-info);
}
.Toastify__progress-bar--success {
  background: var(--toastify-color-progress-success);
}
.Toastify__progress-bar--warning {
  background: var(--toastify-color-progress-warning);
}
.Toastify__progress-bar--error {
  background: var(--toastify-color-progress-error);
}
.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info, .Toastify__progress-bar-theme--colored.Toastify__progress-bar--success, .Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning, .Toastify__progress-bar-theme--colored.Toastify__progress-bar--error {
  background: var(--toastify-color-transparent);
}

.Toastify__close-button {
  color: #fff;
  background: transparent;
  outline: none;
  border: none;
  padding: 0;
  cursor: pointer;
  opacity: 0.7;
  transition: 0.3s ease;
  align-self: flex-start;
  z-index: 1;
}
.Toastify__close-button--light {
  color: #000;
  opacity: 0.3;
}
.Toastify__close-button > svg {
  fill: currentColor;
  height: 16px;
  width: 14px;
}
.Toastify__close-button:hover, .Toastify__close-button:focus {
  opacity: 1;
}

@keyframes Toastify__trackProgress {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}
.Toastify__progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--toastify-z-index);
  opacity: 0.7;
  transform-origin: left;
  border-bottom-left-radius: var(--toastify-toast-bd-radius);
}
.Toastify__progress-bar--animated {
  animation: Toastify__trackProgress linear 1 forwards;
}
.Toastify__progress-bar--controlled {
  transition: transform 0.2s;
}
.Toastify__progress-bar--rtl {
  right: 0;
  left: initial;
  transform-origin: right;
  border-bottom-left-radius: initial;
  border-bottom-right-radius: var(--toastify-toast-bd-radius);
}
.Toastify__progress-bar--wrp {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  border-bottom-left-radius: var(--toastify-toast-bd-radius);
}
.Toastify__progress-bar--wrp[data-hidden=true] {
  opacity: 0;
}
.Toastify__progress-bar--bg {
  opacity: var(--toastify-color-progress-bgo);
  width: 100%;
  height: 100%;
}

.Toastify__spinner {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: var(--toastify-spinner-color-empty-area);
  border-right-color: var(--toastify-spinner-color);
  animation: Toastify__spin 0.65s linear infinite;
}

@keyframes Toastify__bounceInRight {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    transform: translate3d(10px, 0, 0);
  }
  90% {
    transform: translate3d(-5px, 0, 0);
  }
  to {
    transform: none;
  }
}
@keyframes Toastify__bounceOutRight {
  20% {
    opacity: 1;
    transform: translate3d(-20px, var(--y), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(2000px, var(--y), 0);
  }
}
@keyframes Toastify__bounceInLeft {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0);
  }
  75% {
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    transform: translate3d(5px, 0, 0);
  }
  to {
    transform: none;
  }
}
@keyframes Toastify__bounceOutLeft {
  20% {
    opacity: 1;
    transform: translate3d(20px, var(--y), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(-2000px, var(--y), 0);
  }
}
@keyframes Toastify__bounceInUp {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0);
  }
  75% {
    transform: translate3d(0, 10px, 0);
  }
  90% {
    transform: translate3d(0, -5px, 0);
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes Toastify__bounceOutUp {
  20% {
    transform: translate3d(0, calc(var(--y) - 10px), 0);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, calc(var(--y) + 20px), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes Toastify__bounceInDown {
  from, 60%, 75%, 90%, to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0);
  }
  75% {
    transform: translate3d(0, -10px, 0);
  }
  90% {
    transform: translate3d(0, 5px, 0);
  }
  to {
    transform: none;
  }
}
@keyframes Toastify__bounceOutDown {
  20% {
    transform: translate3d(0, calc(var(--y) - 10px), 0);
  }
  40%, 45% {
    opacity: 1;
    transform: translate3d(0, calc(var(--y) + 20px), 0);
  }
  to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
  }
}
.Toastify__bounce-enter--top-left, .Toastify__bounce-enter--bottom-left {
  animation-name: Toastify__bounceInLeft;
}
.Toastify__bounce-enter--top-right, .Toastify__bounce-enter--bottom-right {
  animation-name: Toastify__bounceInRight;
}
.Toastify__bounce-enter--top-center {
  animation-name: Toastify__bounceInDown;
}
.Toastify__bounce-enter--bottom-center {
  animation-name: Toastify__bounceInUp;
}

.Toastify__bounce-exit--top-left, .Toastify__bounce-exit--bottom-left {
  animation-name: Toastify__bounceOutLeft;
}
.Toastify__bounce-exit--top-right, .Toastify__bounce-exit--bottom-right {
  animation-name: Toastify__bounceOutRight;
}
.Toastify__bounce-exit--top-center {
  animation-name: Toastify__bounceOutUp;
}
.Toastify__bounce-exit--bottom-center {
  animation-name: Toastify__bounceOutDown;
}

@keyframes Toastify__zoomIn {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
@keyframes Toastify__zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    transform: translate3d(0, var(--y), 0) scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
.Toastify__zoom-enter {
  animation-name: Toastify__zoomIn;
}

.Toastify__zoom-exit {
  animation-name: Toastify__zoomOut;
}

@keyframes Toastify__flipIn {
  from {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
  }
  60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    transform: perspective(400px);
  }
}
@keyframes Toastify__flipOut {
  from {
    transform: translate3d(0, var(--y), 0) perspective(400px);
  }
  30% {
    transform: translate3d(0, var(--y), 0) perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    transform: translate3d(0, var(--y), 0) perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
.Toastify__flip-enter {
  animation-name: Toastify__flipIn;
}

.Toastify__flip-exit {
  animation-name: Toastify__flipOut;
}

@keyframes Toastify__slideInRight {
  from {
    transform: translate3d(110%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}
@keyframes Toastify__slideInLeft {
  from {
    transform: translate3d(-110%, 0, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}
@keyframes Toastify__slideInUp {
  from {
    transform: translate3d(0, 110%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}
@keyframes Toastify__slideInDown {
  from {
    transform: translate3d(0, -110%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, var(--y), 0);
  }
}
@keyframes Toastify__slideOutRight {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(110%, var(--y), 0);
  }
}
@keyframes Toastify__slideOutLeft {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(-110%, var(--y), 0);
  }
}
@keyframes Toastify__slideOutDown {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(0, 500px, 0);
  }
}
@keyframes Toastify__slideOutUp {
  from {
    transform: translate3d(0, var(--y), 0);
  }
  to {
    visibility: hidden;
    transform: translate3d(0, -500px, 0);
  }
}
.Toastify__slide-enter--top-left, .Toastify__slide-enter--bottom-left {
  animation-name: Toastify__slideInLeft;
}
.Toastify__slide-enter--top-right, .Toastify__slide-enter--bottom-right {
  animation-name: Toastify__slideInRight;
}
.Toastify__slide-enter--top-center {
  animation-name: Toastify__slideInDown;
}
.Toastify__slide-enter--bottom-center {
  animation-name: Toastify__slideInUp;
}

.Toastify__slide-exit--top-left, .Toastify__slide-exit--bottom-left {
  animation-name: Toastify__slideOutLeft;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}
.Toastify__slide-exit--top-right, .Toastify__slide-exit--bottom-right {
  animation-name: Toastify__slideOutRight;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}
.Toastify__slide-exit--top-center {
  animation-name: Toastify__slideOutUp;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}
.Toastify__slide-exit--bottom-center {
  animation-name: Toastify__slideOutDown;
  animation-timing-function: ease-in;
  animation-duration: 0.3s;
}

@keyframes Toastify__spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/*# sourceMappingURL=ReactToastify.css.map */
