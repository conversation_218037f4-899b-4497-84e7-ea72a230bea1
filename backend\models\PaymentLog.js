const mongoose = require("mongoose");

const paymentLogSchema = new mongoose.Schema({
  orderId: { type: String, required: true },
  transactionId: { type: String, required: true },
  status: { type: String, required: true },
  rawData: { type: Object, required: true }, // Stores full callback payload
  timestamp: { type: Date, default: Date.now }, // Auto timestamping
});

const PaymentLog = mongoose.model("PaymentLog", paymentLogSchema);
module.exports = PaymentLog;
