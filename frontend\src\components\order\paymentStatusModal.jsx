import React from "react";
import { Dialog, DialogBody } from "@material-tailwind/react";

function PaymentStatusModal({ status, orderId, onClose, t }) {
  const statusConfig = {
    success: {
      iconPath: "M5 13l4 4L19 7",
      bgColor: "bg-green-200 text-green-600",
      title: t("payment_successful"),
      message: t("thank_you_for_your_purchase"),
      buttonColor: "bg-green-600 hover:bg-green-700",
    },
    failed: {
      iconPath: "M6 18L18 6M6 6l12 12",
      bgColor: "bg-red-200 text-red-600",
      title: t("payment_failed"),
      message: t("payment_issue"),
      buttonColor: "bg-red-600 hover:bg-red-700",
    },
    canceled: {
      iconPath: "M6 18L18 6M6 6l12 12",
      bgColor: "bg-gray-200 text-gray-600",
      title: t("payment_canceled"),
      message: t("your_payment_was_canceled"),
      buttonColor: "bg-gray-600 hover:bg-gray-700",
    },
  };

  const config = statusConfig[status] || statusConfig.failed; // Default to "failed" if unknown status

  return (
    <Dialog
      open={!!status}
      handler={onClose}
      className="bg-transparent shadow-none border-none"
    >
      <DialogBody className="flex items-center justify-center bg-white shadow-lg max-w-sm m-auto rounded-lg p-6">
        <div className="relative mx-auto text-center">
          {/* Status Icon */}
          <div
            className={`flex items-center justify-center w-16 h-16 mx-auto rounded-full ${config.bgColor} bg-opacity-20`}
          >
            <svg
              className="h-8 w-8"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d={config.iconPath}
              />
            </svg>
          </div>

          {/* Title */}
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            {config.title}
          </h2>

          {/* Message */}
          <p className="mt-2 text-sm text-gray-600">{config.message}</p>

          {/* Buttons */}
          <div className="mt-6 flex flex-col space-y-3">
            {(status === "failed" || status === "canceled") && (
              <a
                href="/client/cart"
                className="block w-full px-4 py-2 text-center text-white bg-secondary rounded-lg hover:bg-yellow-700"
              >
                {t("back_to_cart")}
              </a>
            )}
            <button
              onClick={onClose}
              className={`w-full px-4 py-2 text-white rounded-lg ${config.buttonColor}`}
            >
              {t("close")}
            </button>
          </div>
        </div>
      </DialogBody>
    </Dialog>
  );
}

export default PaymentStatusModal;
