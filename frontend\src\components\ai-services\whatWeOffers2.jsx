import { ArrowLeftIcon, ArrowRightIcon } from "@heroicons/react/24/solid";
import { Typography } from "@material-tailwind/react";
import React, { useState } from "react";
import { useIsMobile } from "../../app/hook/useIsMobile";
import Image from "next/image";
import { BsCheck } from "react-icons/bs";

const WhatWeOffers2 = ({ offersData, t }) => {
    const isMobile = useIsMobile();
    const [currentPage, setCurrentPage] = useState(0);

    const itemsPerPage = isMobile ? 2 : 10;
    const totalPages = Math.ceil(offersData?.length / itemsPerPage);

    const handleNext = () => {
        if (currentPage < totalPages - 1) {
            setCurrentPage((prevPage) => prevPage + 1);
        }
    };

    const handlePrev = () => {
        if (currentPage > 0) {
            setCurrentPage((prevPage) => prevPage - 1);
        }
    };

    const currentItems = offersData?.slice(
        currentPage * itemsPerPage,
        (currentPage + 1) * itemsPerPage
    );

    return (
        <div className="max-w-[1400px] mx-auto bg-[#F2F4FB] md:p-10 w-full rounded-md">
            <div className="relative w-full max-w-5xl 2xl:max-w-7xl mx-auto p-6">
                <Typography
                    variant="h2"
                    className="text-3xl font-semibold text-center mb-6 font-outfit"
                >
                    {t("ai_offers_title")}
                </Typography>

                <div className="hidden md:grid gap-6">
                    {/* First Row: 3 Items */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {currentItems?.slice(0, 3).map((item, index) => (
                            <OfferCard key={index} item={item} t={t} />
                        ))}
                    </div>

                    {/* Second Row: 2 Items with Image in the Middle */}
                    <div className="relative grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
                        <OfferCard item={currentItems[3]} t={t} />
                        <div className="hidden md:flex h-[195px]">
                            <Image
                                src="/images/ai/ai-services.jpg"
                                alt="Meaningful"
                                className="w-full object-cover mx-auto rounded-lg"
                                width={800}
                                height={195}
                                sizes="(max-width: 768px) 100vw, 50vw"
                                quality={100}
                            />
                        </div>
                        <OfferCard item={currentItems[4]} t={t} />
                    </div>

                    {/* Third Row: 3 Items */}
                    <div className="relative grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                        <OfferCard item={currentItems[8]} t={t} />
                        <OfferCard item={currentItems[9]} t={t} />
                    </div>

                    {/* Fourth Row: 2 Items with Image in the Middle */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {currentItems?.slice(5, 8).map((item, index) => (
                            <OfferCard key={index} item={item} t={t} />
                        ))}
                    </div>
                </div>
                <div className="grid md:hidden grid-cols-1 gap-6">
                    {currentItems?.map((item, index) => (
                        <OfferCard key={index} item={item} t={t} />
                    ))}
                </div>
                {/* Pagination - Only for Mobile */}
                {isMobile && (
                    <div className="p-5 relative w-[150px] mx-auto flex justify-between items-center">
                        <button
                            onClick={handlePrev}
                            className={`flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage <= 0 ? "bg-gray-400" : "bg-secondary hover:bg-blue-500"
                                }`}
                            disabled={currentPage <= 0}
                        >
                            <ArrowLeftIcon width={20} />
                        </button>

                        <button
                            onClick={handleNext}
                            className={`flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage >= totalPages - 1
                                ? "bg-gray-400"
                                : "bg-secondary hover:bg-blue-500"
                                }`}
                            disabled={currentPage >= totalPages - 1}
                        >
                            <ArrowRightIcon width={20} />
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

// OfferCard Component for Reusability
const OfferCard = ({ item, t }) => (
    <div className="flex flex-col items-center text-center bg-white shadow-inner border p-4 rounded-lg">
        {/* Icon */}
        <div className={`w-12 h-12 mb-2 flex items-center justify-center rounded-full select-none ${item?.icon ? "flex" : "hidden"}`}>
            <span className="text-gray-500 font-bold text-lg">
                {item?.isImg ? (
                    <img
                        src={item?.icon}
                        alt="icon"
                        className="w-[40px] h-[40px] object-contain"
                    />
                ) : item?.icon ? (
                    item?.icon
                ) : null}
            </span>
        </div>
        {/* Title */}
        <Typography
            variant="h3"
            className="font-medium text-lg mb-2 font-outfit md:w-4/5"
        >
            {t(item?.title)}
        </Typography>
        {/* Description */}
        {item?.description && (
            <p className="text-gray-600 text-sm mb-2 font-inter">
                {t(item?.description)}
            </p>
        )}
        {/* Sub Items */}
        {item?.items && item?.items.length > 0 && (
            <ul className="text-left text-sm text-gray-500 font-inter">
                {item.items.map((subItem, idx) => (
                    <li className="flex gap-x-2 items-start justify-start" key={idx}>
                        <span className="text-xl leading-none text-secondary">
                            <BsCheck />
                        </span>
                        <span>{t(subItem)}</span>
                    </li>
                ))}
            </ul>
        )}
    </div>
);

export default WhatWeOffers2;
