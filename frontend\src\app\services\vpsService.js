import apiService from "../lib/apiService";
import { BACKEND_URL } from "../config/constant";

/**
 * VPS Service
 * Handles all VPS-related API calls to the backend
 * Follows the same pattern as other services in the application
 */
const vpsService = {
  // ==================== PUBLIC ENDPOINTS ====================

  /**
   * Get available VPS plans
   * @param {string} provider - Optional provider filter (e.g., 'contabo')
   * @returns {Promise} API response with VPS plans
   */
  getPlans: (provider = "") => {
    const params = provider ? { provider } : {};
    return apiService.get("/api/vps/plans", params);
  },

  /**
   * Get specific VPS plan details
   * @param {string} planId - Plan ID
   * @param {string} provider - Optional provider filter
   * @returns {Promise} API response with plan details
   */
  getPlanDetails: (planId, provider = "") => {
    const params = provider ? { provider } : {};
    return apiService.get(`/vps/plans/${planId}`, params);
  },

  /**
   * Get supported VPS providers
   * @returns {Promise} API response with supported providers
   */
  getProviders: () => {
    return apiService.get("/api/vps/providers");
  },

  /**
   * Get available OS images
   * @param {string} provider - Optional provider filter
   * @returns {Promise} API response with OS images
   */
  getImages: (provider = "") => {
    const params = provider ? { provider } : {};
    return apiService.get("/api/vps/images", params);
  },

  /**
   * Get available regions/locations
   * @param {string} provider - Optional provider filter
   * @returns {Promise} API response with regions
   */
  getRegions: async (provider = "") => {
    const url = provider
      ? `/api/vps/regions?provider=${provider}`
      : "/api/vps/regions";

    // Utiliser fetch directement pour éviter les intercepteurs d'authentification
    try {
      const response = await fetch(`${BACKEND_URL}${url}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { data: data.data, success: data.success };
    } catch (error) {
      console.error("Error fetching VPS regions:", error);
      throw error;
    }
  },

  // ==================== PROTECTED ENDPOINTS ====================

  /**
   * Create a new VPS order
   * @param {Object} orderData - Order configuration
   * @param {string} orderData.planId - VPS plan ID
   * @param {string} orderData.provider - Provider name (e.g., 'contabo')
   * @param {string} orderData.region - Data center region
   * @param {string} orderData.operatingSystem - OS image ID
   * @param {string} orderData.displayName - Instance display name
   * @param {string} orderData.billingCycle - Billing cycle (monthly, yearly)
   * @param {Object} orderData.billingInfo - Billing information
   * @returns {Promise} API response with order details
   */
  createOrder: (orderData) => {
    return apiService.post("/api/vps/order", orderData, {
      withCredentials: true,
    });
  },

  /**
   * Get user's VPS orders
   * @returns {Promise} API response with user's VPS orders
   */
  getUserOrders: () => {
    return apiService.get("/api/vps/orders", { withCredentials: true });
  },

  /**
   * Get VPS suborder details
   * @param {string} subOrderId - Suborder ID
   * @returns {Promise} API response with suborder details
   */
  getSubOrderDetails: (subOrderId) => {
    return apiService.get(`/api/vps/suborders/${subOrderId}`, { withCredentials: true });
  },

  /**
   * Get user's VPS instances
   * @returns {Promise} API response with user's VPS instances
   */
  getUserInstances: () => {
    return apiService.get("/api/vps/instances", { withCredentials: true });
  },

  /**
   * Get specific VPS instance details
   * @param {string} instanceId - Instance ID
   * @returns {Promise} API response with instance details
   */
  getInstanceDetails: (instanceId) => {
    return apiService.get(`/vps/instances/${instanceId}`, {
      withCredentials: true,
    });
  },

  // ==================== INSTANCE CONTROL ====================

  /**
   * Control VPS instance (start, stop, restart, reset-password)
   * @param {string} instanceId - Instance ID
   * @param {string} action - Action to perform ('start', 'stop', 'restart', 'reset-password')
   * @returns {Promise} API response with operation result
   */
  controlInstance: (instanceId, action) => {
    return apiService.post(
      `/vps/instances/${instanceId}/control`,
      { action },
      { withCredentials: true }
    );
  },

  /**
   * Start VPS instance
   * @param {string} instanceId - Instance ID
   * @returns {Promise} API response
   */
  startInstance: (instanceId) => {
    return vpsService.controlInstance(instanceId, "start");
  },

  /**
   * Stop VPS instance
   * @param {string} instanceId - Instance ID
   * @returns {Promise} API response
   */
  stopInstance: (instanceId) => {
    return vpsService.controlInstance(instanceId, "stop");
  },

  /**
   * Restart VPS instance
   * @param {string} instanceId - Instance ID
   * @returns {Promise} API response
   */
  restartInstance: (instanceId) => {
    return vpsService.controlInstance(instanceId, "restart");
  },

  /**
   * Reset VPS instance password
   * @param {string} instanceId - Instance ID
   * @returns {Promise} API response
   */
  resetInstancePassword: (instanceId) => {
    return vpsService.controlInstance(instanceId, "reset-password");
  },

  // ==================== MONITORING & STATS ====================

  /**
   * Get VPS instance statistics
   * @param {string} instanceId - Instance ID
   * @returns {Promise} API response with instance stats
   */
  getInstanceStats: (instanceId) => {
    return apiService.get(`/vps/instances/${instanceId}/stats`, {
      withCredentials: true,
    });
  },

  // ==================== SNAPSHOTS & BACKUPS ====================

  /**
   * Create VPS snapshot
   * @param {string} instanceId - Instance ID
   * @param {Object} snapshotData - Snapshot configuration
   * @param {string} snapshotData.name - Snapshot name
   * @param {string} snapshotData.description - Optional description
   * @returns {Promise} API response with snapshot details
   */
  createSnapshot: (instanceId, snapshotData) => {
    return apiService.post(
      `/vps/instances/${instanceId}/snapshot`,
      snapshotData,
      { withCredentials: true }
    );
  },

  // ==================== REINSTALL & RECOVERY ====================

  /**
   * Reinstall VPS instance with new OS
   * @param {string} instanceId - Instance ID
   * @param {Object} reinstallData - Reinstall configuration
   * @param {string} reinstallData.imageId - New OS image ID
   * @param {string} reinstallData.userData - Optional cloud-init user data
   * @returns {Promise} API response with reinstall details
   */
  reinstallInstance: (instanceId, reinstallData) => {
    return apiService.post(
      `/vps/instances/${instanceId}/reinstall`,
      reinstallData,
      { withCredentials: true }
    );
  },

  // ==================== PAYMENT ====================

  /**
   * Confirm payment for VPS order
   * @param {Object} paymentData - Payment confirmation data
   * @param {string} paymentData.orderId - Order ID
   * @param {string} paymentData.paymentMethod - Payment method
   * @param {Object} paymentData.paymentDetails - Payment details
   * @returns {Promise} API response with payment confirmation
   */
  confirmPayment: (paymentData) => {
    return apiService.post("/api/vps/payment/confirm", paymentData, {
      withCredentials: true,
    });
  },

  /**
   * Execute VPS action (start, stop, restart, console, rescue)
   * @param {string} instanceId - VPS instance ID
   * @param {string} action - Action to perform
   * @param {Object} options - Action options (optional)
   * @returns {Promise} API response
   */
  executeVPSAction: (instanceId, action, options = {}) => {
    return apiService.post(
      `/api/vps/instances/${instanceId}/actions/${action}`,
      options,
      { withCredentials: true }
    );
  },

  /**
   * Start rescue system for VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} rescueOptions - Rescue system options (optional)
   * @returns {Promise} API response
   */
  startRescueSystem: (instanceId, rescueOptions = {}) => {
    return vpsService.executeVPSAction(instanceId, "rescue");
  },

  /**
   * Cancel VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} cancellationData - Cancellation details
   * @param {string} cancellationData.reason - Reason for cancellation
   * @param {string} cancellationData.feedback - Additional feedback (optional)
   * @param {string} cancellationData.targetDate - Target cancellation date (optional)
   * @returns {Promise} API response
   */
  cancelInstance: (instanceId, cancellationData) => {
    return apiService.post(
      `/vps/instances/${instanceId}/cancel`,
      cancellationData,
      { withCredentials: true }
    );
  },

  /**
   * Check rescue status for VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise} API response
   */
  checkRescueStatus: (instanceId) => {
    return apiService.get(`/api/vps/instances/${instanceId}/rescue-status`, {
      withCredentials: true,
    });
  },

  /**
   * Get available rescue system images
   * @returns {Promise} API response
   */
  getRescueImages: () => {
    return apiService.get("/api/vps/rescue-images", { withCredentials: true });
  },

  /**
   * Get Cloud-Init status for a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise} API response
   */
  getCloudInitStatus: (instanceId) => {
    return apiService.get(`/api/vps/instances/${instanceId}/cloud-init`, { withCredentials: true });
  },

  /**
   * Reset password for VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} resetData - Password reset parameters
   * @param {string} resetData.rootPassword - New root password
   * @param {Array<number>} resetData.sshKeys - Optional SSH key secretIds
   * @param {string} resetData.userData - Optional cloud-init config
   * @returns {Promise} API response
   */
  resetVPSPassword: (instanceId, resetData) => {
    return apiService.post(`/api/vps/instances/${instanceId}/reset-password`, resetData, { withCredentials: true });
  },

  /**
   * Reinstall VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {Object} reinstallData - Reinstallation parameters
   * @returns {Promise} API response
   */
  reinstallVPS: (instanceId, reinstallData) => {
    return apiService.post(`/api/vps/instances/${instanceId}/reinstall`, reinstallData, { withCredentials: true });
  },

  /**
   * Get VPS instance status (for verification after reinstall)
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise} API response with instance status
   */
  getInstanceStatus: (instanceId) => {
    return apiService.get(`/api/vps/instances/${instanceId}/status`, { withCredentials: true });
  },

  /**
   * Get available images for VPS
   * @returns {Promise} API response
   */
  getAvailableImages: () => {
    return apiService.get('/api/vps/images', { withCredentials: true });
  },

  /**
   * Get available applications for VPS
   * @returns {Promise} API response
   */
  getAvailableApplications: () => {
    return apiService.get('/api/vps/applications', { withCredentials: true });
  },



  /**
   * Get VPS snapshots
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise} API response
   */
  getVPSSnapshots: (instanceId) => {
    return apiService.get(`/api/vps/instances/${instanceId}/snapshots`, {
      withCredentials: true,
    });
  },

  /**
   * Create VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} name - Snapshot name
   * @param {string} description - Snapshot description
   * @returns {Promise} API response
   */
  createVPSSnapshot: (instanceId, name, description) => {
    return apiService.post(
      `/api/vps/instances/${instanceId}/snapshots`,
      { name, description },
      { withCredentials: true }
    );
  },

  /**
   * Rename VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @param {string} name - New snapshot name
   * @param {string} description - New snapshot description
   * @returns {Promise} API response
   */
  renameVPSSnapshot: (instanceId, snapshotId, name, description) => {
    return apiService.put(
      `/api/vps/instances/${instanceId}/snapshots/${snapshotId}/rename`,
      { name, description },
      { withCredentials: true }
    );
  },

  /**
   * Rollback VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @returns {Promise} API response
   */
  rollbackVPSSnapshot: (instanceId, snapshotId) => {
    return apiService.post(
      `/api/vps/instances/${instanceId}/snapshots/${snapshotId}/rollback`,
      {},
      { withCredentials: true }
    );
  },

  /**
   * Delete VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotId - Snapshot ID
   * @returns {Promise} API response
   */
  deleteVPSSnapshot: (instanceId, snapshotId) => {
    return apiService.delete(
      `/api/vps/instances/${instanceId}/snapshots/${snapshotId}`,
      { withCredentials: true }
    );
  },

  // ==================== UTILITY METHODS ====================

  /**
   * Get VPS plan by ID from plans list
   * @param {Array} plans - Array of VPS plans
   * @param {string} planId - Plan ID to find
   * @returns {Object|null} Found plan or null
   */
  findPlanById: (plans, planId) => {
    return plans.find((plan) => plan.id === planId) || null;
  },

  /**
   * Format VPS price for display
   * @param {number} price - Price in cents or base unit
   * @param {string} currency - Currency code (default: 'MAD')
   * @returns {string} Formatted price string
   */
  formatPrice: (price, currency = "MAD") => {
    return new Intl.NumberFormat("fr-MA", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  },

  /**
   * Get status color for VPS instance
   * @param {string} status - Instance status
   * @returns {string} Tailwind color class
   */
  getStatusColor: (status) => {
    const statusColors = {
      running: "text-green-600",
      stopped: "text-red-600",
      starting: "text-yellow-600",
      stopping: "text-orange-600",
      restarting: "text-blue-600",
      installing: "text-purple-600",
      error: "text-red-800",
      unknown: "text-gray-600",
    };
    return statusColors[status] || statusColors.unknown;
  },

  /**
   * Get status icon for VPS instance
   * @param {string} status - Instance status
   * @returns {string} Icon name or component
   */
  getStatusIcon: (status) => {
    const statusIcons = {
      running: "play-circle",
      stopped: "stop-circle",
      starting: "loader",
      stopping: "pause-circle",
      restarting: "refresh-cw",
      installing: "download",
      error: "alert-circle",
      unknown: "help-circle",
    };
    return statusIcons[status] || statusIcons.unknown;
  },

  /**
   * Add VPS to cart with configuration
   * @param {Object} vpsConfig - VPS configuration
   * @returns {Promise<Object>} Cart response
   */
  addVPSToCart: async (vpsConfig) => {
    try {
      // Find the VPS package that matches the plan
      const vpsPackage = await vpsService.findVPSPackage(vpsConfig.planId);

      const cartData = {
        packageId: vpsPackage._id,
        quantity: 1,
        period: vpsConfig.period || 1,
        customConfiguration: {
          planId: vpsConfig.planId,
          provider: vpsConfig.provider,
          region: vpsConfig.region,
          operatingSystem: vpsConfig.operatingSystem,
          displayName: vpsConfig.displayName,
          sshKeys: vpsConfig.sshKeys || [],
          userData: vpsConfig.userData || "",
          addons: vpsConfig.addons || {},
          // Include plan specifications for reference
          cpu: vpsConfig.cpu,
          ram: vpsConfig.ram,
          storage: vpsConfig.storage,
          bandwidth: vpsConfig.bandwidth,
        },
      };

      const response = await apiClient.post("/cart/add", cartData);
      return response.data;
    } catch (error) {
      console.error("Failed to add VPS to cart:", error.message);
      throw error;
    }
  },

  /**
   * Find VPS package by plan ID
   * @param {string} planId - Contabo plan ID (V91, V92, etc.)
   * @returns {Promise<Object>} VPS package
   */
  findVPSPackage: async (planId) => {
    try {
      // Get all VPS packages
      const response = await apiClient.get("/packages", {
        params: { brandName: "Contabo" },
      });

      const vpsPackages = response.data || [];

      // Find package that matches the Contabo plan ID
      const matchingPackage = vpsPackages.find(
        (pkg) => pkg.vpsConfig && pkg.vpsConfig.providerProductId === planId
      );

      if (matchingPackage) {
        return matchingPackage;
      } else {
        // If no specific package found, use the first VPS package
        const firstVPSPackage = vpsPackages.find(
          (pkg) => pkg.brand && pkg.brand.name === "Contabo"
        );

        if (firstVPSPackage) {
          return firstVPSPackage;
        } else {
          throw new Error(
            `No VPS package found for plan ${planId}. Please run the VPS setup script.`
          );
        }
      }
    } catch (error) {
      console.error("Error finding VPS package:", error);
      throw error;
    }
  },
};

export default vpsService;
