"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  Button,
  Input,
  Select,
  Option,
  Textarea,
  Spinner,
  Chip,
} from "@material-tailwind/react";
import {
  MessageSquare,
  Search,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Mail,
  Trash2,
} from "lucide-react";
import { adminService } from "../../services/adminService";
import { useAuth } from "../../context/AuthContext";
import { toast } from "react-toastify";
import ConfirmationModal from "../../../components/admin/ConfirmationModal";
import Link from "next/link";

// Skeleton Loader Component
const SkeletonLoader = () => (
  <div className="space-y-4" role="status" aria-label="Loading tickets">
    {[...Array(3)].map((_, index) => (
      <Card key={index} className="bg-white shadow-sm animate-pulse">
        <CardBody className="p-6">
          <div className="flex flex-col md:flex-row justify-between items-start gap-4">
            <div className="flex-1 space-y-3">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="flex flex-wrap gap-2">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-4 bg-gray-200 rounded w-32"></div>
              </div>
            </div>
            <div className="h-10 w-32 bg-gray-200 rounded"></div>
          </div>
        </CardBody>
      </Card>
    ))}
  </div>
);

export default function SupportManagement() {
  const { user } = useAuth();

  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedPriority, setSelectedPriority] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [resolutionComment, setResolutionComment] = useState("");
  const [resolvingTicketId, setResolvingTicketId] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [ticketToResolve, setTicketToResolve] = useState(null);

  // Memoized fetch tickets function
  const fetchTickets = useCallback(async () => {
    try {
      setLoading(true);
      const response = await adminService.getAllTikcets();
      setTickets(response.data.tickets);
    } catch (err) {
      toast.error("Failed to fetch tickets");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTickets();
  }, [fetchTickets]);

  // Memoized filtered tickets
  const filteredTickets = useMemo(() => {
    return tickets.filter((ticket) => {
      const matchesSearch =
        ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ticket.creator.email.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus =
        selectedStatus === "all" || ticket.status === selectedStatus;
      const matchesPriority =
        selectedPriority === "all" || ticket.priority === selectedPriority;

      return matchesSearch && matchesStatus && matchesPriority;
    });
  }, [tickets, searchQuery, selectedStatus, selectedPriority]);
  console.log("🚀 ~ filteredTickets ~ filteredTickets:", filteredTickets);

  // Handle status change with confirmation for 'resolved'
  const handleStatusChange = useCallback(
    async (ticketId, newStatus) => {
      try {
        if (newStatus === "resolved" && !resolutionComment) {
          setResolvingTicketId(ticketId);
          return;
        }

        const resolvedBy = user?._id;
        const payload = {
          status: newStatus,
          resolutionComment: newStatus === "resolved" ? resolutionComment : "",
          resolvedBy: newStatus === "resolved" ? resolvedBy : "",
        };

        const response = await adminService.updateTicketStatus(
          ticketId,
          payload
        );

        if (response.status === 200 || response.status === 201) {
          setTickets((prev) =>
            prev.map((ticket) =>
              ticket._id === ticketId ? response.data : ticket
            )
          );
          setResolvingTicketId(null);
          setResolutionComment("");
          toast.success("Ticket status updated successfully");
        }
      } catch (err) {
        console.error("Error updating ticket status:", err);
        toast.error("Failed to update ticket status");
      }
    },
    [user, resolutionComment]
  );

  // Open confirmation modal
  const openConfirmationModal = useCallback((ticketId) => {
    setTicketToResolve(ticketId);
    setIsModalOpen(true);
  }, []);

  // Handle confirmation
  const handleConfirmResolve = useCallback(() => {
    if (ticketToResolve) {
      handleStatusChange(ticketToResolve, "resolved");
    }
    setIsModalOpen(false);
    setTicketToResolve(null);
  }, [ticketToResolve, handleStatusChange]);

  // Close modal
  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setTicketToResolve(null);
    setResolutionComment("");
    setResolvingTicketId(null);
  }, []);

  return (
    <div className="container mx-auto p-6 min-h-screen bg-gray-50">
      <div className="flex justify-between items-center mb-6">
        <Typography
          variant="h2"
          className="flex items-center gap-3 text-gray-900"
        >
          <MessageSquare className="h-8 w-8 text-blue-600" />
          Support Tickets
        </Typography>
      </div>

      {/* Search and Filter Bar */}
      <Card className="mb-6 shadow-md p-4 bg-white rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            type="text"
            placeholder="Search tickets..."
            label="Search tickets"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            icon={<Search className="h-5 w-5 text-gray-400" />}
            className="w-full border-gray-200 focus:border-blue-500"
            containerProps={{ className: "bg-gray-50 rounded-lg" }}
          />
          <Select
            value={selectedStatus}
            onChange={(value) => setSelectedStatus(value)}
            label="Status"
            className="w-full bg-gray-50 rounded-lg border-gray-200"
            menuProps={{ className: "bg-white shadow-lg" }}
          >
            <Option value="all">All Status</Option>
            <Option value="open">Open</Option>
            <Option value="in_progress">In Progress</Option>
            <Option value="resolved">Resolved</Option>
            <Option value="closed">Closed</Option>
          </Select>
          <Select
            value={selectedPriority}
            onChange={(value) => setSelectedPriority(value)}
            label="Priority"
            className="w-full bg-gray-50 rounded-lg border-gray-200"
            menuProps={{ className: "bg-white shadow-lg" }}
          >
            <Option value="all">All Priority</Option>
            <Option value="low">Low</Option>
            <Option value="medium">Medium</Option>
            <Option value="high">High</Option>
            <Option value="urgent">Urgent</Option>
          </Select>
        </div>
      </Card>

      {/* Ticket List */}
      <Card className="bg-white shadow-lg rounded-lg">
        <CardBody className="p-0">
          {filteredTickets.length > 0 ? (
            <div className="divide-y divide-gray-100">
              {filteredTickets.map((ticket) => (
                <div
                  key={ticket._id}
                  className="p-6 hover:bg-gray-50 transition-colors duration-200 ease-in-out"
                  role="article"
                >
                  <div className="flex flex-col md:flex-row justify-between items-start gap-6">
                    <div className="flex-1 flex items-start gap-4">
                      {getStatusIcon(ticket.status)}
                      <div className="w-full">
                        <Link href={`/admin/support/${ticket._id}`}>
                          <Typography
                            variant="h5"
                            className="font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors"
                          >
                            {ticket.subject}
                          </Typography>
                        </Link>
                        <Typography className="text-gray-600 mb-3 line-clamp-2">
                          {ticket.message}
                        </Typography>
                        <div className="flex flex-wrap gap-3 text-sm items-center">
                          <Chip
                            value={ticket.priority}
                            className={`${getPriorityColor(
                              ticket.priority
                            )} font-medium capitalize`}
                            size="sm"
                          />
                          <span className="text-gray-500 flex items-center gap-1">
                            <Clock size={14} className="text-gray-400" />
                            {new Date(ticket.createdAt).toLocaleDateString(
                              "fr-FR"
                            )}
                          </span>
                          <span className="text-gray-500 flex items-center gap-1">
                            <Mail size={14} className="text-gray-400" />
                            {ticket.creator?.firstName}{" "}
                            {ticket.creator?.lastName} ({ticket.creator?.email})
                          </span>
                        </div>
                        {ticket.resolutionComment && (
                          <div className="mt-4 bg-blue-50 p-4 rounded-lg border border-blue-100">
                            <Typography
                              variant="small"
                              className="font-medium text-blue-700 mb-1"
                            >
                              Resolution:
                            </Typography>
                            <Typography className="text-blue-600 text-sm">
                              {ticket.resolutionComment}
                            </Typography>
                          </div>
                        )}
                        {resolvingTicketId === ticket._id && (
                          <div className="mt-4 space-y-4 animate-fade-in">
                            {/* TODO: Add grammar auto auto correction */}
                            <Textarea
                              value={resolutionComment}
                              onChange={(e) =>
                                setResolutionComment(e.target.value)
                              }
                              placeholder="Enter resolution details..."
                              className="w-full bg-gray-50 border-gray-200 focus:border-blue-500"
                              rows={3}
                              spellCheck
                            />
                            <div className="flex justify-end gap-4">
                              <Button
                                variant="outlined"
                                color="gray"
                                onClick={() => {
                                  setResolvingTicketId(null);
                                  setResolutionComment("");
                                }}
                                className="hover:bg-gray-100"
                              >
                                Cancel
                              </Button>
                              <Button
                                color="green"
                                onClick={() =>
                                  openConfirmationModal(ticket._id)
                                }
                                disabled={!resolutionComment.trim()}
                                className="hover:bg-green-700 transition-colors"
                              >
                                Resolve Ticket
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Select
                        value={ticket.status}
                        onChange={(value) =>
                          handleStatusChange(ticket._id, value)
                        }
                        className="w-40 bg-gray-50 rounded-lg  border-gray-200"
                        labelProps={{ className: "hidden" }}
                        menuProps={{ className: "bg-white shadow-lg" }}
                      >
                        <Option value="open">Open</Option>
                        <Option value="in_progress">In Progress</Option>
                        <Option value="resolved">Resolved</Option>
                        <Option value="closed">Closed</Option>
                        <Option value="deleted">Deleted</Option>
                      </Select>
                      {ticket.status !== "deleted" && (
                        <Button
                          variant="text"
                          color="red"
                          size="sm"
                          onClick={() =>
                            handleStatusChange(ticket._id, "deleted")
                          }
                          className="hover:bg-red-50 p-2"
                          aria-label="Delete ticket"
                        >
                          <Trash2 size={18} />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center">
              <Typography className="text-gray-500 text-lg">
                No tickets found
              </Typography>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleConfirmResolve}
        message="Are you sure you want to resolve this ticket?"
        title="Confirm Resolution"
        confirmButtonColor="green"
      />
    </div>
  );
}

// Reusable getStatusIcon function
const getStatusIcon = (status) => {
  const icons = {
    resolved: <CheckCircle className="h-6 w-6 text-green-500" />,
    closed: <XCircle className="h-6 w-6 text-red-500" />,
    in_progress: <Clock className="h-6 w-6 text-yellow-500" />,
    open: <AlertCircle className="h-6 w-6 text-blue-500" />,
    deleted: <Trash2 className="h-6 w-6 text-gray-500" />,
  };
  return icons[status] || icons.open;
};

// Reusable getPriorityColor function
const getPriorityColor = (priority) => {
  const colors = {
    urgent: "bg-red-100 text-red-800",
    high: "bg-orange-100 text-orange-800",
    medium: "bg-yellow-100 text-yellow-800",
    low: "bg-green-100 text-green-800",
  };
  return colors[priority] || colors.low;
};

// Custom fade-in animation
const styles = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
`;

if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
