const ProductStatus = require("../constants/enums/poduct-status");
const Category = require("../models/Category");

// Create a new category
exports.createCategory = async (req, res) => {
  try {
    const category = new Category(req.body);
    await category.save();
    res.status(201).json(category);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Get all categories
exports.getCategories = async (req, res) => {
  try {
    const categories = await Category.find();
    res.status(200).json(categories);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

exports.getCategory = async (req, res) => {
  try {
    console.log("Getting category...", req.params.name);
    const category = await Category.findOne({ name: req.params.name }).populate(
      {
        path: "brands",
        model: "Brand",
        populate: {
          path: "packages",
          model: "Package",
          match: { status: ProductStatus.PUBLISHED }, // Only get published packages
          populate: {
            path: "specifications",
            model: "Specification",
          },
        },
      }
    );

    if (!category) throw new Error("Category not found");
    res.status(200).json(category);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
};

// Update a category
exports.updateCategory = async (req, res) => {
  try {
    const category = await Category.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    });
    res.status(200).json(category);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// Delete a category
exports.deleteCategory = async (req, res) => {
  try {
    await Category.findByIdAndDelete(req.params.id);
    res.status(200).json({ message: "Category deleted successfully" });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
