/**
 * VPS Request Validation Middleware
 * Validates VPS-related requests using express-validator
 */

const { body, param, query } = require('express-validator');

/**
 * Validation for creating VPS order
 */
const createVPSOrderValidator = [
  body('planId')
    .notEmpty()
    .withMessage('Plan ID is required')
    .isString()
    .withMessage('Plan ID must be a string'),

  body('provider')
    .optional()
    .isIn(['contabo', 'digitalocean', 'vultr', 'linode'])
    .withMessage('Invalid provider. Supported providers: contabo, digitalocean, vultr, linode'),

  body('region')
    .notEmpty()
    .withMessage('Region is required')
    .isString()
    .withMessage('Region must be a string'),

  body('operatingSystem')
    .notEmpty()
    .withMessage('Operating system is required')
    .isIn(['ubuntu-22.04', 'ubuntu-20.04', 'debian-11', 'centos-8', 'windows-2019'])
    .withMessage('Invalid operating system'),

  body('displayName')
    .notEmpty()
    .withMessage('Display name is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Display name must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9-_]+$/)
    .withMessage('Display name can only contain letters, numbers, hyphens, and underscores'),

  body('billingCycle')
    .optional()
    .isIn(['hourly', 'monthly', 'yearly'])
    .withMessage('Invalid billing cycle. Supported: hourly, monthly, yearly'),

  // Billing information validation
  body('billingInfo.name')
    .notEmpty()
    .withMessage('Billing name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Billing name must be between 2 and 100 characters'),

  body('billingInfo.email')
    .notEmpty()
    .withMessage('Billing email is required')
    .isEmail()
    .withMessage('Invalid email format'),

  body('billingInfo.phone')
    .optional()
    .isMobilePhone()
    .withMessage('Invalid phone number format'),

  body('billingInfo.address')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Address must not exceed 255 characters'),

  body('billingInfo.country')
    .optional()
    .isLength({ min: 2, max: 2 })
    .withMessage('Country must be a 2-letter country code'),

  body('billingInfo.isCompany')
    .optional()
    .isBoolean()
    .withMessage('isCompany must be a boolean'),

  body('billingInfo.companyName')
    .if(body('billingInfo.isCompany').equals(true))
    .notEmpty()
    .withMessage('Company name is required for company accounts')
    .isLength({ min: 2, max: 100 })
    .withMessage('Company name must be between 2 and 100 characters'),

  body('billingInfo.companyICE')
    .if(body('billingInfo.isCompany').equals(true))
    .optional()
    .matches(/^\d{15}$/)
    .withMessage('Company ICE must be 15 digits'),

  // SSH Keys validation (optional)
  body('sshKeys')
    .optional()
    .isArray()
    .withMessage('SSH keys must be an array'),

  body('sshKeys.*')
    .optional()
    .isString()
    .withMessage('Each SSH key must be a string')
    .isLength({ min: 100, max: 2000 })
    .withMessage('SSH key length is invalid')
];

/**
 * Validation for VPS instance control actions
 */
const controlInstanceValidator = [
  param('instanceId')
    .notEmpty()
    .withMessage('Instance ID is required')
    .isString()
    .withMessage('Instance ID must be a string'),

  body('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['start', 'stop', 'restart', 'reset-password'])
    .withMessage('Invalid action. Supported actions: start, stop, restart, reset-password')
];

/**
 * Validation for getting plan details
 */
const getPlanDetailsValidator = [
  param('planId')
    .notEmpty()
    .withMessage('Plan ID is required')
    .isString()
    .withMessage('Plan ID must be a string'),

  query('provider')
    .optional()
    .isIn(['contabo', 'digitalocean', 'vultr', 'linode'])
    .withMessage('Invalid provider')
];

/**
 * Validation for getting VPS plans
 */
const getPlansValidator = [
  query('provider')
    .optional()
    .isIn(['contabo', 'digitalocean', 'vultr', 'linode'])
    .withMessage('Invalid provider')
];

/**
 * Validation for getting user orders/instances
 */
const getUserResourcesValidator = [
  query('status')
    .optional()
    .isString()
    .withMessage('Status must be a string')
];

/**
 * Validation for payment confirmation
 */
const confirmPaymentValidator = [
  body('orderId')
    .notEmpty()
    .withMessage('Order ID is required')
    .isString()
    .withMessage('Order ID must be a string'),

  body('paymentId')
    .notEmpty()
    .withMessage('Payment ID is required')
    .isString()
    .withMessage('Payment ID must be a string'),

  body('transactionId')
    .notEmpty()
    .withMessage('Transaction ID is required')
    .isString()
    .withMessage('Transaction ID must be a string')
];

/**
 * Validation for getting instance details
 */
const getInstanceDetailsValidator = [
  param('instanceId')
    .notEmpty()
    .withMessage('Instance ID is required')
    .isString()
    .withMessage('Instance ID must be a string')
];

/**
 * Validation for getting instance stats
 */
const getInstanceStatsValidator = [
  param('instanceId')
    .notEmpty()
    .withMessage('Instance ID is required')
    .isString()
    .withMessage('Instance ID must be a string')
];

/**
 * Validation for creating snapshot
 */
const createSnapshotValidator = [
  param('instanceId')
    .notEmpty()
    .withMessage('Instance ID is required')
    .isString()
    .withMessage('Instance ID must be a string'),

  body('snapshotName')
    .optional()
    .isString()
    .withMessage('Snapshot name must be a string')
    .isLength({ min: 3, max: 50 })
    .withMessage('Snapshot name must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9-_]+$/)
    .withMessage('Snapshot name can only contain letters, numbers, hyphens, and underscores')
];

/**
 * Validation for reinstalling instance
 */
const reinstallInstanceValidator = [
  param('instanceId')
    .notEmpty()
    .withMessage('Instance ID is required')
    .isString()
    .withMessage('Instance ID must be a string'),

  body('imageId')
    .notEmpty()
    .withMessage('Image ID is required')
    .isString()
    .withMessage('Image ID must be a string')
];

/**
 * Validation for getting images
 */
const getImagesValidator = [
  query('provider')
    .optional()
    .isIn(['contabo', 'digitalocean', 'vultr', 'linode'])
    .withMessage('Invalid provider')
];

/**
 * Custom validation for VPS configuration
 */
const validateVPSConfig = (req, res, next) => {
  const { planId, region, operatingSystem, provider } = req.body;
  
  // Custom validation logic can be added here
  // For example, checking if the combination of plan, region, and OS is valid
  
  // Region validation based on provider
  const providerRegions = {
    contabo: ['EU', 'US-EAST', 'US-WEST', 'ASIA'],
    digitalocean: ['nyc1', 'nyc3', 'ams3', 'sgp1', 'lon1', 'fra1'],
    vultr: ['ewr', 'ord', 'dfw', 'sea', 'lax', 'atl', 'ams', 'lhr', 'fra', 'sjc'],
    linode: ['us-east', 'us-west', 'eu-west', 'ap-south', 'ap-northeast']
  };

  const selectedProvider = provider || 'contabo';
  const validRegions = providerRegions[selectedProvider];

  if (validRegions && !validRegions.includes(region)) {
    return res.status(400).json({
      success: false,
      message: `Invalid region for ${selectedProvider}. Valid regions: ${validRegions.join(', ')}`
    });
  }

  next();
};

/**
 * Rate limiting validation for VPS operations
 */
const rateLimitVPSOperations = (req, res, next) => {
  // This can be enhanced with Redis-based rate limiting
  // For now, it's a placeholder for future implementation
  next();
};

module.exports = {
  createVPSOrderValidator,
  controlInstanceValidator,
  getPlanDetailsValidator,
  getPlansValidator,
  getUserResourcesValidator,
  confirmPaymentValidator,
  getInstanceDetailsValidator,
  getInstanceStatsValidator,
  createSnapshotValidator,
  reinstallInstanceValidator,
  getImagesValidator,
  validateVPSConfig,
  rateLimitVPSOperations
};
