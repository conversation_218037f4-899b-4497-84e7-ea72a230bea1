import apiService from '../lib/apiService';

// Storage keys
const STORAGE_KEY_SESSION_ID = 'ztech_chatbot_session_id';
const STORAGE_KEY_MESSAGES = 'ztech_chatbot_messages';

// Initialize session ID from localStorage if available
let currentSessionId = null;

// Helper function to safely access localStorage (handles SSR)
const safeLocalStorage = {
  getItem: (key) => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(key);
    }
    return null;
  },
  setItem: (key, value) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(key, value);
    }
  },
  removeItem: (key) => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(key);
    }
  }
};

// Initialize from localStorage
if (typeof window !== 'undefined') {
  currentSessionId = safeLocalStorage.getItem(STORAGE_KEY_SESSION_ID);
  // if (currentSessionId) {
  //   console.log('[CHATBOT DEBUG] Loaded session ID from localStorage:', currentSessionId);
  // }
}

const ChatbotService = {
  sendMessage: async (messages) => {
    try {
      // Include the session ID if we have one
      const payload = {
        messages,
        sessionId: currentSessionId
      };

      const response = await apiService.post('/chatbot', payload, { withCredentials: true });

      // Store the session ID from the response
      if (response.data.sessionId) {
        currentSessionId = response.data.sessionId;

        // Save session ID to localStorage
        safeLocalStorage.setItem(STORAGE_KEY_SESSION_ID, currentSessionId);
      }

      // Save updated messages to localStorage
      if (response.choices && response.choices.length > 0) {
        const updatedMessages = [...messages, response.choices[0].message];
        safeLocalStorage.setItem(STORAGE_KEY_MESSAGES, JSON.stringify(updatedMessages));
      }

      return response.data;
    } catch (error) {
      console.error('ChatbotService error:', error);
      throw error;
    }
  },

  getInitialMessages: () => {
    return [
      {
        role: 'system',
        content: `You are an AI assistant for ZTech Engineering, a Moroccan technology company specializing in web development, cloud services, hosting, and cybersecurity. Your name is "ZTech Assistant". Be helpful, friendly, and professional. Provide concise answers focused on ZTech's services.`
      }
    ];
  },

  // Clear the current session
  clearSession: async () => {
    try {
      if (currentSessionId) {
        await apiService.delete(`/chatbot/session/${currentSessionId}`);
        currentSessionId = null;

        // Clear localStorage
        safeLocalStorage.removeItem(STORAGE_KEY_SESSION_ID);
        safeLocalStorage.removeItem(STORAGE_KEY_MESSAGES);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error clearing session:', error);
      // Still clear localStorage even if API call fails
      safeLocalStorage.removeItem(STORAGE_KEY_SESSION_ID);
      safeLocalStorage.removeItem(STORAGE_KEY_MESSAGES);
      return false;
    }
  },

  // Get the current session ID
  getSessionId: () => {
    return currentSessionId;
  },

  // Get saved messages from localStorage
  getSavedMessages: () => {
    const savedMessagesJson = safeLocalStorage.getItem(STORAGE_KEY_MESSAGES);

    if (savedMessagesJson) {
      try {
        const savedMessages = JSON.parse(savedMessagesJson);
        return savedMessages;
      } catch (error) {
        console.error('Error parsing saved messages:', error);
        return null;
      }
    }
    return null;
  },

  // Save messages to localStorage
  saveMessages: (messages) => {
    if (messages && messages.length > 0) {
      try {
        const messagesJson = JSON.stringify(messages);
        safeLocalStorage.setItem(STORAGE_KEY_MESSAGES, messagesJson);
      } catch (error) {
        console.error('Error saving messages to localStorage:', error);
      }
    }
  }
};

export default ChatbotService;