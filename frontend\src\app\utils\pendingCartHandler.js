import cartService from '@/app/services/cartService';
import { toast } from 'react-toastify';

/**
 * Checks for and adds any pending cart items stored in localStorage
 * @param {Function} setCartCount - Function to update cart count in the UI
 * @returns {Promise<boolean>} - True if a pending item was added, false otherwise
 */
export const handlePendingCartItem = async (setCartCount) => {
  try {
    // Check if there's a pending cart item in localStorage
    const pendingItemJson = localStorage.getItem('pendingCartItem');
    if (!pendingItemJson) return false;

    // Parse the pending item
    const pendingItem = JSON.parse(pendingItemJson);

    // Add the item to the cart
    const response = await cartService.addItemToCart({
      packageId: pendingItem.packageId,
      quantity: 1,
      period: pendingItem.period || 12, // Use stored period or default to yearly (12 months)
    });

    // Update cart count
    setCartCount(response.data.cart.cartCount);

    // Show success message
    toast.success(`${pendingItem.packageName} added to cart!`);

    // Remove the pending item from localStorage
    localStorage.removeItem('pendingCartItem');

    return true;
  } catch (error) {
    console.error('Error handling pending cart item:', error);

    // Only show error if it's not an auth error (which would be handled elsewhere)
    if (error.response?.status !== 401) {
      toast.error('Failed to add pending item to cart');
    }

    return false;
  }
};
