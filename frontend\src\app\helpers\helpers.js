import { Cookies } from "react-cookie";
import { BACKEND_URL } from "../config/constant";
import { AlertCircle, CheckCircle, Clock, XCircle } from "lucide-react";

const cookies = new Cookies();

export const AccountNotVerifiedCode = 2001;

// export const generateObjectFromHeaders = (headers) => {
//     const obj = {};
//     headers.map((header) => header.name).map((h) => obj[h] = '')
//     return obj
// }

export const generateObjectFromHeaders = (headers) => {
  const obj = {};
  headers
    .map((header) => (header.parent ? header.parent + "." : "") + header.name)
    .forEach((h) => {
      const splitArr = h.split(".");

      if (splitArr.length > 1) {
        if (!obj[splitArr[0]]) obj[splitArr[0]] = {};
        obj[splitArr[0]][splitArr[1]] = "";
      } else obj[h] = "";
    });
  return obj;
};

export const getTypeFromHeader = (headers, attrName) => {
  const res = headers.filter((header) => header.name == attrName);
  if (res.length) return res[0].type;
  return null;
};

export const isAuthenticated = () => {
  return cookies.get("token");
};

export const getUser = () => {
  if (cookies.get("user")) {
    return cookies.get("user");
  }
  return null;
};

export const getRole = () => {
  if (cookies.get("role")) {
    return cookies.get("role");
  }
  return null;
};

export const clearAllCookies = async () => {
  try {
    const allCookies = cookies.getAll(); // Get all cookies
    Object.keys(allCookies).forEach((cookieName) => {
      cookies.remove(cookieName, { path: "/" });
    });
    console.log("ALL Cookies are cleared");
  } catch (error) {
    console.log(error);
  }
};

export const setServerMedia = (imageUrl) => {
  if (!imageUrl) return imageUrl;
  if (imageUrl.indexOf("https://") != -1 || imageUrl.indexOf("http://") != -1)
    return imageUrl;
  // return "http://localhost:5000" + imageUrl;
  return BACKEND_URL + imageUrl;
};

export const setUserCookies = async (user) => {
  await cookies.remove("user", { path: "/" });
  await cookies.remove("Language", { path: "/" });
  await cookies.remove("role", { path: "/" });

  // Setting cookies with a global path
  await cookies.set("user", JSON.stringify(user), { path: "/" });
  await cookies.set("Language", user.favoriteLang, { path: "/" });
  await cookies.set("role", user.role, { path: "/" });
};

export const getPercent = (regularPrice, sellingPrice) => {
  return Math.ceil((100 * (regularPrice - sellingPrice)) / regularPrice);
};

export const getFormatNotificationTime = (t, createdAt) => {
  const currentDate = new Date();
  const createdDate = new Date(createdAt);

  const timeDifference = currentDate - createdDate;
  const seconds = Math.floor(timeDifference / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30);

  if (months > 0) {
    return `${months} ${t("months")}${months > 1 ? t("plural") : ""} ${t(
      "ago"
    )}`;
  } else if (days > 0) {
    return `${days} ${t("day")}${days > 1 ? t("plural") : ""} ${t("ago")}`;
  } else if (hours > 0) {
    return `${hours} ${t("hour")}${hours > 1 ? t("plural") : ""} ${t("ago")}`;
  } else if (minutes > 0) {
    return `${minutes} ${t("minute")}${minutes > 1 ? t("plural") : ""} ${t(
      "ago"
    )}`;
  } else {
    return `${t("just_now")}`;
  }
};

export function arrayToMatrix(arr, columns) {
  const matrix = [];
  for (let i = 0; i < arr.length; i += columns) {
    matrix.push(arr.slice(i, i + columns));
  }
  return matrix;
}

export const formatDate = (t, inputDate) => {
  const currentDate = new Date();
  const date = new Date(inputDate);

  // Check if it's today
  if (
    date.getDate() === currentDate.getDate() &&
    date.getMonth() === currentDate.getMonth() &&
    date.getFullYear() === currentDate.getFullYear()
  ) {
    // Display time (hours and minutes) for today
    return `${date.getHours()}:${String(date.getMinutes()).padStart(2, "0")}`;
  }

  // Check if it's yesterday
  const yesterday = new Date(currentDate);
  yesterday.setDate(currentDate.getDate() - 1);

  if (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  ) {
    // Display 'Yesterday' and time for yesterday
    return `${t("yesterday")}, ${date.getHours()}:${String(
      date.getMinutes()
    ).padStart(2, "0")}`;
  }

  // Display the full date for other days
  return `${
    date.getMonth() + 1
  }/${date.getDate()}/${date.getFullYear()}, ${date.getHours()}:${String(
    date.getMinutes()
  ).padStart(2, "0")}`;
};
export function truncateString(str, maxLength) {
  if (str.length > maxLength) {
    return str.slice(0, maxLength) + "...";
  } else {
    return str;
  }
}

export const sanitizeInput = (input, inputType) => {
  let sanitizedInput;
  if (inputType === "BillToName") {
    // Allow only letters (including Arabic and French) and spaces
    sanitizedInput = input.replace(/[^\p{L}\s]/gu, "");
  } else if (inputType === "address") {
    // Allow only letters (including Arabic and French), numbers, spaces, and specified punctuation
    sanitizedInput = input.replace(/[^\p{L}0-9\s\-.,'_]/gu, "");
  } else {
    sanitizedInput = input.replace(/[^\w\.\-@]/g, "");
  }
  return sanitizedInput;
};

export const getDateDropdown = (createdAt) => {
  const currentDate = new Date();
  const creationDate = new Date(createdAt);

  const getYears = () => {
    const startYear = creationDate.getFullYear();
    const currentYear = currentDate.getFullYear();
    const years = [];

    for (let year = startYear; year <= currentYear; year++) {
      years.push(year);
    }

    return years;
  };

  const getMonths = (year) => {
    const months = [];
    const startYear = creationDate.getFullYear();
    const currentYear = currentDate.getFullYear();

    const startMonth = startYear === year ? creationDate.getMonth() + 1 : 1;
    const endMonth = year === currentYear ? currentDate.getMonth() + 1 : 12;

    for (let month = startMonth; month <= endMonth; month++) {
      months.push(month);
    }

    return months;
  };

  return { getYears, getMonths };
};

export const getMonthName = (monthNumber, lang = "default") => {
  const date = new Date();
  date.setMonth(monthNumber - 1); // JavaScript months are 0-based (0 = January, 11 = December)
  return date.toLocaleString(lang, { month: "long" });
};

export const maskRIB = (rib) => {
  // if (!rib) return '';
  // const firstPart = rib.slice(0, 3);
  // const lastPart = rib.slice(-4);
  // return (
  //     <>
  //         <span className="mr-1">{firstPart}</span>
  //         <span className="">• • • • • •</span>
  //         <span className="ml-1">{lastPart}</span>
  //     </>
  // );

  if (!rib) return "";
  const firstPart = rib.slice(0, 6); // Adjust slicing if needed
  const lastPart = rib.slice(-4);
  return `${firstPart} • • • • • • ${lastPart}`;
};

export function getColorBasedOnState(state) {
  switch (state) {
    case "NOT_VERIFIED":
      return "gray";
    case "VERIFIED":
      return "green";
    case "SUSPENDED":
      return "yellow";
    case "BLOCKED":
      return "red";
    default:
      return "gray";
  }
}

// Helper function to get status icon
export const getStatusIcon = (status) => {
  switch (status) {
    case "resolved":
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case "closed":
      return <XCircle className="h-5 w-5 text-red-500" />;
    case "in_progress":
      return <Clock className="h-5 w-5 text-yellow-500" />;
    default:
      return <AlertCircle className="h-5 w-5 text-blue-500" />;
  }
};

// Helper function to get priority color
export const getPriorityColor = (priority) => {
  switch (priority) {
    case "urgent":
      return "bg-red-100 text-red-800";
    case "high":
      return "bg-orange-100 text-orange-800";
    case "medium":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-green-100 text-green-800";
  }
};

export const roundThis = (value) => {
  return Math.round(Number(value));
};

export const calculatePrice = (plan, billingPeriod) => {
  const basePrice = plan.price;
  const yearlyDiscount =
    plan.discounts?.find((d) => d.period === 12)?.percentage || 0;
  const monthlyDiscount =
    plan.discounts?.find((d) => d.period === 1)?.percentage || 0;

  if (billingPeriod === "yearly") {
    const discountedYearlyTotal = basePrice * (1 - yearlyDiscount / 100);
    return {
      price: discountedYearlyTotal,
      regularPrice: basePrice,
    };
  } else {
    return {
      price: basePrice * (1 - monthlyDiscount / 100),
      regularPrice: basePrice,
    };
  }
};

export const getFormattedPlans = (hostingPacks, billingPeriod) => {
  return hostingPacks.map((plan) => {
    const pricing = calculatePrice(plan, billingPeriod);
    return {
      ...plan,
      price: pricing.price.toFixed(2),
      regularPrice: pricing.regularPrice.toFixed(2),
    };
  });
};

export const getMaxDiscount = (hostingPacks) => {
  return hostingPacks.reduce((max, plan) => {
    const yearlyDiscount =
      plan.discounts?.find((d) => d.period === 12)?.percentage || 0;
    return Math.max(max, yearlyDiscount);
  }, 0);
};

export const getLocalizedContent = (content, field, locale) => {
  if (!content || !field) return ""; // Ensure content and field are defined
  const localizedField = locale === "fr" ? `${field}_fr` : field;
  return content[localizedField] ?? content[field] ?? ""; // Fallback if localized field is missing
};
