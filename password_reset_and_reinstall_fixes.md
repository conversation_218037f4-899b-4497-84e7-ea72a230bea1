# VPS Password Reset and Reinstall Fixes

## Issues Fixed

### 1. Password Reset Authentication Failure
**Problem**: Password reset via API appeared successful but new password didn't work for login.

**Root Cause**: Contabo API requires `rootPassword` to be a secret ID (number), not plain text. Our implementation was correct, but there might be timing issues or API inconsistencies.

**Solutions Implemented**:
- ✅ **Enhanced Debugging**: Added comprehensive logging to track secret creation and API calls
- ✅ **Secret Verification**: Verify secret is created correctly before using it
- ✅ **Processing Delay**: Added 3-second delay after secret creation to ensure it's processed
- ✅ **Error Handling**: Enhanced error logging with detailed response information

### 2. Reinstall Secret Selection Feature
**Problem**: Users couldn't reuse existing password secrets during reinstall.

**Solution**: Added secret selection functionality that allows users to:
- ✅ **Select Existing Secrets**: Choose from previously stored password secrets
- ✅ **Create New Secrets**: Enter new passwords that get stored as secrets
- ✅ **Automatic Handling**: System automatically detects secret IDs vs plain text passwords

## Implementation Details

### Enhanced Password Reset (`resetPassword`)

<augment_code_snippet path="backend/services/providers/ContaboProvider.js" mode="EXCERPT">
````javascript
async resetPassword(instanceId, resetData) {
  try {
    console.log(`🔑 Resetting password for VPS instance: ${instanceId}`);
    
    let passwordSecretId;
    
    if (typeof resetData.rootPassword === 'number') {
      // Already a secretId
      passwordSecretId = resetData.rootPassword;
    } else {
      // Create secret with verification and delay
      const secretName = `VPS-${instanceId}-Password-Reset-${Date.now()}`;
      passwordSecretId = await this.createPasswordSecret(resetData.rootPassword, secretName);
      
      // Verify secret creation
      const secrets = await this.getSecrets();
      const createdSecret = secrets.find(s => s.secretId === passwordSecretId);
      console.log(`🔍 Secret verification:`, { found: !!createdSecret });
      
      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    const response = await this.makeRequest("POST", 
      `/compute/instances/${instanceId}/actions/resetPassword`,
      { rootPassword: passwordSecretId }
    );
    
    return { success: true, passwordSecretId, data: response };
  } catch (error) {
    console.error(`❌ Password reset failed:`, error);
    throw error;
  }
}
````
</augment_code_snippet>

### Enhanced Reinstall with Secret Selection (`reinstallInstance`)

<augment_code_snippet path="backend/controllers/vpsController.js" mode="EXCERPT">
````javascript
async reinstallInstance(req, res) {
  try {
    const { instanceId } = req.params;
    const { 
      imageId, 
      customImageId, 
      rootPassword,      // New plain text password
      passwordSecretId,  // Existing secret ID
      sshKeys, 
      userData 
    } = req.body;
    
    // Handle password selection
    let finalPassword = null;
    if (passwordSecretId) {
      // User selected existing secret
      finalPassword = passwordSecretId;
      console.log(`🔑 Using existing secret: ${passwordSecretId}`);
    } else if (rootPassword) {
      // User provided new password
      finalPassword = rootPassword;
      console.log(`🔐 Using new password (will create secret)`);
    }
    
    const result = await provider.reinstallVPS(instanceId, {
      imageId,
      customImageId,
      rootPassword: finalPassword,
      sshKeys,
      userData
    });
    
    res.json({ success: true, data: result });
  } catch (error) {
    res.status(400).json({ success: false, message: error.message });
  }
}
````
</augment_code_snippet>

### Secrets Management API

<augment_code_snippet path="backend/controllers/vpsController.js" mode="EXCERPT">
````javascript
// Get all secrets
async getSecrets(req, res) {
  const result = await contaboProvider.getSecrets();
  res.json({ success: true, data: result });
}

// Create new secret
async createSecret(req, res) {
  const { name, value, type } = req.body;
  const result = await contaboProvider.createSecret(name, value, type);
  res.status(201).json({ success: true, data: result });
}
````
</augment_code_snippet>

## API Endpoints

### Secrets Management
- `GET /api/vps/secrets` - List all stored secrets
- `POST /api/vps/secrets` - Create new secret
- `PATCH /api/vps/secrets/:secretId` - Update secret
- `DELETE /api/vps/secrets/:secretId` - Delete secret

### Enhanced Reinstall
- `POST /api/vps/instances/:instanceId/reinstall`

**Request Body**:
```json
{
  "imageId": "ubuntu-22.04",
  "rootPassword": "NewPassword123",     // OR
  "passwordSecretId": 456,              // Use existing secret
  "sshKeys": [123, 124],               // Optional
  "userData": "#cloud-config..."        // Optional
}
```

## Frontend Integration

### Secret Selection UI
```javascript
// Get available secrets for selection
const secrets = await fetch('/api/vps/secrets').then(r => r.json());
const passwordSecrets = secrets.data.filter(s => s.type === 'password');

// Display in dropdown/select
<select name="passwordSecretId">
  <option value="">Create new password</option>
  {passwordSecrets.map(secret => (
    <option value={secret.secretId}>{secret.name}</option>
  ))}
</select>

// Show password input only if no secret selected
{!selectedSecretId && (
  <input type="password" name="rootPassword" placeholder="Enter new password" />
)}
```

## Testing the Fixes

### 1. Test Password Reset with Debug Logs
```bash
# Check logs for detailed debugging information
tail -f logs/app.log | grep "🔑\|🔐\|📤\|📥\|❌"
```

### 2. Test Secret Selection in Reinstall
```bash
# Test with existing secret
curl -X POST /api/vps/instances/12345/reinstall \
  -H "Content-Type: application/json" \
  -d '{"imageId": "ubuntu-22.04", "passwordSecretId": 456}'

# Test with new password
curl -X POST /api/vps/instances/12345/reinstall \
  -H "Content-Type: application/json" \
  -d '{"imageId": "ubuntu-22.04", "rootPassword": "NewPassword123"}'
```

## Troubleshooting

### If Password Reset Still Fails
1. **Check Debug Logs**: Look for secret creation and verification logs
2. **Verify Secret**: Manually check if secret was created in Contabo
3. **Try Longer Delay**: Increase the 3-second delay if needed
4. **Use Reinstall**: As a workaround, use reinstall with same OS and new password

### If Secret Selection Doesn't Work
1. **Check Secrets API**: Verify `/api/vps/secrets` returns data
2. **Validate Secret IDs**: Ensure secret IDs are numbers, not strings
3. **Check Permissions**: Verify user has access to secrets

## Next Steps

1. **Monitor Password Reset**: Watch logs to see if enhanced debugging reveals the issue
2. **User Feedback**: Get user confirmation that reinstall with secret selection works
3. **Documentation**: Update user documentation with secret selection feature
4. **Optimization**: Consider caching secrets to reduce API calls
