import React from 'react';
import { Input, Typography } from '@material-tailwind/react';

const CompanyInfoForm = ({ formData, handleInputChange, errors, tClient }) => {
  return (
    <div className="mt-6 space-y-6">
      <Typography variant="h2" className="text-xl font-semibold text-gray-800">
        {tClient('company_info')}
      </Typography>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Input
            type="text"
            name="companyICE"
            label={tClient('company_ice')}
            value={formData.companyICE || ''}
            onChange={handleInputChange}
          />
          {errors.companyICE && <p className="text-red-500 text-sm">{errors.companyICE}</p>}
        </div>
        <div>
          <Input
            type="email"
            name="companyEmail"
            label={tClient('company_email')}
            value={formData.companyEmail || ''}
            onChange={handleInputChange}
          />
          {errors.companyEmail && <p className="text-red-500 text-sm">{errors.companyEmail}</p>}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Input
            type="text"
            name="companyPhone"
            label={tClient('company_phone')}
            value={formData.companyPhone || ''}
            onChange={handleInputChange}
          />
          {errors.companyPhone && <p className="text-red-500 text-sm">{errors.companyPhone}</p>}
        </div>
        <div>
          <Input
            type="text"
            name="companyAddress"
            label={tClient('company_address')}
            value={formData.companyAddress || ''}
            onChange={handleInputChange}
          />
          {errors.companyAddress && <p className="text-red-500 text-sm">{errors.companyAddress}</p>}
        </div>
      </div>
      {/* Company Logo Upload can be added here if needed */}
    </div>
  );
};

export default CompanyInfoForm;
