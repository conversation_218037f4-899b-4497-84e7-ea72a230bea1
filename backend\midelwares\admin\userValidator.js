const Joi = require('joi');
const AccountRole = require('../../constants/enums/account-role');
const AccountState = require('../../constants/enums/account-state');

exports.addUserValidator = async (req, res, next) => {
    console.log("req.body: ", req.body);

    // Define the Joi schema for adding a user
    const addUserSchema = Joi.object({
        firstName: Joi.string()
            .regex(/^\S/) // Ensure no leading whitespace
            .min(3)
            .required()
            .label('First Name'),
        lastName: Joi.string()
            .regex(/^\S/) // Ensure no leading whitespace
            .min(3)
            .required()
            .label('Last Name'),
        email: Joi.string()
            .email()
            .required()
            .label('Email'),
        password: Joi.string()
            .min(8)
            .required()
            .label('Password'),
        role: Joi.string()
            .valid(AccountRole.Customer, AccountRole.Admin, AccountRole.Vendor) // Only allow 'user' or 'admin'
            .default(AccountRole.Customer)
            .label('Role'),
        state: Joi.string()
            .valid(AccountState.NOT_VERIFIED, AccountState.VERIFIED, AccountState.BLOCKED, AccountState.SUSPENDED) // Only allow 'active' or 'inactive'
            .default(AccountState.NOT_VERIFIED)
            .label('State'),
    });

    // Validate the request body against the schema
    const result = addUserSchema.validate(req.body, {
        abortEarly: false, // Return all validation errors, not just the first one
    });

    // If validation fails, return a structured error response
    if (result.error) {
        // Extract the error details from the result
        const errors = result.error.details;

        // Map the errors to a structured format
        const errorArray = errors.map((error) => {
            return {
                key: error.context.key, // Field name
                msg: error.message.replace(/"/g, ''), // Remove quotes from the error message
            };
        });

        // Return a 400 Bad Request response with the validation errors
        const errMsg = 'Validation failed.';
        return res.status(400).json({
            message: errMsg,
            errors: errorArray,
        });
    }

    // If validation succeeds, proceed to the next middleware or route handler
    next();
};
exports.updateUserValidator = async (req, res, next) => {
    console.log("req.body: ", req.body);

    // Define the Joi schema for updating a user
    const updateUserSchema = Joi.object({
        firstName: Joi.string()
            .regex(/^\S/) // Ensure no leading whitespace
            .min(3)
            .optional() // Fields are optional for updates
            .label('First Name'),
        lastName: Joi.string()
            .regex(/^\S/) // Ensure no leading whitespace
            .min(3)
            .optional() // Fields are optional for updates
            .label('Last Name'),
        email: Joi.string()
            .email()
            .optional() // Fields are optional for updates
            .label('Email'),
        password: Joi.string()
            .min(8)
            .optional() // Fields are optional for updates
            .label('Password'),
        role: Joi.string()
            .valid(AccountRole.Customer, AccountRole.Admin, AccountRole.Vendor) // Only allow specific roles
            .optional() // Fields are optional for updates
            .label('Role'),
        state: Joi.string()
            .valid(AccountState.NOT_VERIFIED, AccountState.VERIFIED, AccountState.BLOCKED, AccountState.SUSPENDED, AccountState.DELETED) // Only allow specific states
            .optional() // Fields are optional for updates
            .label('State'),
    }).min(1); // Ensure at least one field is provided for the update

    // Validate the request body against the schema
    const result = updateUserSchema.validate(req.body, {
        abortEarly: false, // Return all validation errors, not just the first one
    });

    // If validation fails, return a structured error response
    if (result.error) {
        // Extract the error details from the result
        const errors = result.error.details;

        // Map the errors to a structured format
        const errorArray = errors.map((error) => {
            return {
                key: error.context.key, // Field name
                msg: error.message.replace(/"/g, ''), // Remove quotes from the error message
            };
        });

        // Return a 400 Bad Request response with the validation errors
        const errMsg = 'Validation failed.';
        return res.status(400).json({
            message: errMsg,
            errors: errorArray,
        });
    }

    // If validation succeeds, proceed to the next middleware or route handler
    next();
};