const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const paymentSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    order: { type: Schema.Types.ObjectId, ref: 'Order', required: true },
    invoiceId: { type: String, required: true, unique: true }, // Keep if needed
    paymentId: { type: String, required: true, unique: true }, // Keep if needed
    paymentMethod: { type: String, required: true },
    paymentDate: { type: Date, required: true },
    status: { type: String, required: true, enum: ['completed', 'pending', 'failed', 'refunded'] },
    transactionId: { type: String },
}, { timestamps: true });

// Add indexes for frequently queried fields
paymentSchema.index({ user: 1, createdAt: -1 });
paymentSchema.index({ paymentId: 1 }, { unique: true });
paymentSchema.index({ invoiceId: 1 }, { unique: true });
paymentSchema.index({ order: 1 });
paymentSchema.index({ status: 1 });

// Add middleware to prevent accidental deletions
paymentSchema.pre('remove', function(next) {
    // Add logic to prevent deletion if payment is completed
    if (this.status === 'pending' || this.status === 'completed' || this.status === 'refunded') {
        next(new Error('Cannot delete completed payments'));
    }
    next();
});

const Payment = mongoose.model('Payment', paymentSchema);
module.exports = Payment;
