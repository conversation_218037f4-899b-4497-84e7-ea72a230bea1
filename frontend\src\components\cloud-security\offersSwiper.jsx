// import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/solid';
// import { Typography } from '@material-tailwind/react';
// import React, { useState } from 'react';

// const OffersSwiper = ({ offersData, t }) => {
//     const [currentPage, setCurrentPage] = useState(0);

//     const itemsPerPage = 6;
//     const totalPages = Math.ceil(offersData.length / itemsPerPage);

//     const handleNext = () => {
//         if (currentPage < totalPages - 1) {
//             setCurrentPage((prevPage) => prevPage + 1);
//         }
//     };

//     const handlePrev = () => {
//         if (currentPage > 0) {
//             setCurrentPage((prevPage) => prevPage - 1);
//         }
//     };

//     const currentItems = offersData.slice(
//         currentPage * itemsPerPage,
//         (currentPage + 1) * itemsPerPage
//     );

//     return (
//         <div className="bg-[#F2F4FB] p-10 w-full rounded-md">
//             <div className="relative w-full max-w-5xl 2xl:max-w-7xl mx-auto p-6 ">
//                 <Typography variant='h2' className="text-center text-2xl font-bold mb-6 font-outfit">
//                     {t('cloud_security_offering')}
//                 </Typography>
//                 <div className="">
//                     <div className="grid grid-cols-3 gap-4">
//                         {currentItems.map((item, index) => (
//                             <div
//                                 key={index}
//                                 className="flex flex-col items-center text-center bg-white shadow-inner border p-4 rounded-lg "
//                             >
//                                 <div className="w-12 h-12 mb-2 flex items-center justify-center rounded-full">
//                                     <span className="text-gray-500 font-bold text-lg">
//                                         {/* {index + 1 + currentPage * itemsPerPage} */}
//                                         {item.isImg ? (
//                                             <img
//                                                 src={item.icon}
//                                                 alt={index + 1 + currentPage * itemsPerPage}
//                                                 className="w-[40px]"
//                                             />
//                                         ) : (
//                                             item.icon
//                                         )}
//                                     </span>
//                                 </div>
//                                 <h3 className="font-medium text-lg mb-2 font-outfit md:w-4/5">{t(item.title)}</h3>
//                                 {item.description && (
//                                     <p className="text-gray-600 text-sm mb-2 font-inter">{t(item.description)}</p>
//                                 )}
//                                 {item.items && item.items.length > 0 && (
//                                     <ul className="text-left text-sm text-gray-500 list-disc list-inside font-inter">
//                                         {item.items.map((subItem, idx) => (
//                                             <li key={idx}>{t(subItem)}</li>
//                                         ))}
//                                     </ul>
//                                 )}
//                             </div>
//                         ))}
//                     </div>

//                     <div className='p-5 relative w-[150px] mx-auto'>
//                         <>
//                             <button
//                                 onClick={handleNext}
//                                 className={`absolute bottom-0 right-0 flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage >= (totalPages - 1) ? " bg-gray-400 " : " bg-secondary hover:bg-blue-500"}`}
//                                 disabled={currentPage >= totalPages - 1}
//                             >
//                                 <ArrowRightIcon width={20} />
//                             </button>

//                             <button
//                                 onClick={handlePrev}
//                                 className={`absolute bottom-0 left-0 flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage <= 0 ? " bg-gray-400" : " bg-secondary hover:bg-blue-500"}`}
//                                 disabled={currentPage <= 0}
//                             >
//                                 <ArrowLeftIcon width={20} />
//                             </button>
//                         </>
//                     </div>

//                 </div>
//             </div>
//         </div>
//     );
// };

// export default OffersSwiper;









// import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/solid';
// import { Typography } from '@material-tailwind/react';
// import React, { useState } from 'react';

// const OffersSwiper = ({ offersData, t }) => {
//     const [currentPage, setCurrentPage] = useState(0);

//     const itemsPerPage = 6;
//     const totalPages = Math.ceil(offersData.length / itemsPerPage);

//     const handleNext = () => {
//         if (currentPage < totalPages - 1) {
//             setCurrentPage((prevPage) => prevPage + 1);
//         }
//     };

//     const handlePrev = () => {
//         if (currentPage > 0) {
//             setCurrentPage((prevPage) => prevPage - 1);
//         }
//     };

//     const currentItems = offersData.slice(
//         currentPage * itemsPerPage,
//         (currentPage + 1) * itemsPerPage
//     );

//     return (
//         <div className="bg-[#F2F4FB] md:p-10 w-full rounded-md">
//             <div className="relative w-full border md:max-w-5xl 2xl:max-w-7xl mx-auto p-6">
//                 <Typography variant="h2" className="text-center text-2xl font-bold mb-6 font-outfit">
//                     {t('cloud_security_offering')}
//                 </Typography>
//                 <div>
//                     <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//                         {currentItems.map((item, index) => (
//                             <div
//                                 key={index}
//                                 className="flex flex-col items-center text-center bg-white shadow-inner border p-4 rounded-lg"
//                             >
//                                 <div className="w-12 h-12 mb-2 flex items-center justify-center rounded-full">
//                                     <span className="text-gray-500 font-bold text-lg">
//                                         {item.isImg ? (
//                                             <img
//                                                 src={item.icon}
//                                                 alt={index + 1 + currentPage * itemsPerPage}
//                                                 className="w-[40px]"
//                                             />
//                                         ) : (
//                                             item.icon
//                                         )}
//                                     </span>
//                                 </div>
//                                 <h3 className="font-medium text-lg mb-2 font-outfit md:w-4/5">{t(item.title)}</h3>
//                                 {item.description && (
//                                     <p className="text-gray-600 text-sm mb-2 font-inter">{t(item.description)}</p>
//                                 )}
//                                 {item.items && item.items.length > 0 && (
//                                     <ul className="text-left text-sm text-gray-500 list-disc list-inside font-inter">
//                                         {item.items.map((subItem, idx) => (
//                                             <li key={idx}>{t(subItem)}</li>
//                                         ))}
//                                     </ul>
//                                 )}
//                             </div>
//                         ))}
//                     </div>

//                     <div className="p-5 relative w-[150px] mx-auto flex justify-between items-center">
//                         <button
//                             onClick={handlePrev}
//                             className={`flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage <= 0 ? 'bg-gray-400' : 'bg-secondary hover:bg-blue-500'
//                                 }`}
//                             disabled={currentPage <= 0}
//                         >
//                             <ArrowLeftIcon width={20} />
//                         </button>

//                         <button
//                             onClick={handleNext}
//                             className={`flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage >= totalPages - 1 ? 'bg-gray-400' : 'bg-secondary hover:bg-blue-500'
//                                 }`}
//                             disabled={currentPage >= totalPages - 1}
//                         >
//                             <ArrowRightIcon width={20} />
//                         </button>
//                     </div>
//                 </div>
//             </div>
//         </div>
//     );
// };

// export default OffersSwiper;








// OffersSwiper Component
import { ArrowLeftIcon, ArrowRightIcon } from "@heroicons/react/24/solid";
import { Typography } from "@material-tailwind/react";
import React, { useState } from "react";
import { useIsMobile } from "../../app/hook/useIsMobile";
import Image from "next/image";

const OffersSwiper = ({ offersData, t }) => {
    const isMobile = useIsMobile();
    const [currentPage, setCurrentPage] = useState(0);

    const itemsPerPage = isMobile ? 2 : 6; // Adjust items per page based on screen size
    const totalPages = Math.ceil(offersData.length / itemsPerPage);

    const handleNext = () => {
        if (currentPage < totalPages - 1) {
            setCurrentPage((prevPage) => prevPage + 1);
        }
    };

    const handlePrev = () => {
        if (currentPage > 0) {
            setCurrentPage((prevPage) => prevPage - 1);
        }
    };

    const currentItems = offersData.slice(
        currentPage * itemsPerPage,
        (currentPage + 1) * itemsPerPage
    );

    return (
        <div className="max-w-[1400px] mx-auto bg-[#F2F4FB] md:p-10 w-full rounded-md">
            <div className="relative w-full max-w-5xl 2xl:max-w-7xl mx-auto p-6">
                <Typography
                    variant="h2"
                    className="text-center text-2xl font-bold mb-6 font-outfit"
                >
                    {t("cloud_security_offering")}
                </Typography>
                <div>
                    <div
                        className={`grid gap-4 ${isMobile ? "grid-cols-1" : "grid-cols-3"
                            }`}
                    >
                        {currentItems.map((item, index) => (
                            <div
                                key={index}
                                className="flex flex-col items-center text-center bg-white shadow-inner border p-4 rounded-lg"
                            >
                                <div className="w-12 h-12 mb-2 flex items-center justify-center rounded-full">
                                    <span className="text-gray-500 font-bold text-lg">
                                        {item.isImg ? (
                                            <Image
                                                src={item.icon}
                                                alt={
                                                    index +
                                                    1 +
                                                    currentPage * itemsPerPage
                                                }
                                                width={40}
                                                height={40}
                                                className="w-[40px]"
                                            />
                                        ) : (
                                            item.icon
                                        )}
                                    </span>
                                </div>
                                <h3 className="font-medium text-lg mb-2 font-outfit md:w-4/5">
                                    {t(item.title)}
                                </h3>
                                {item.description && (
                                    <p className="text-gray-600 text-sm mb-2 font-inter">
                                        {t(item.description)}
                                    </p>
                                )}
                                {item.items && item.items.length > 0 && (
                                    <ul className="text-left text-sm text-gray-500 list-disc list-inside font-inter">
                                        {item.items.map((subItem, idx) => (
                                            <li key={idx}>{t(subItem)}</li>
                                        ))}
                                    </ul>
                                )}
                            </div>
                        ))}
                    </div>

                    <div className="p-5 relative w-[150px] mx-auto flex justify-between items-center">
                        <button
                            onClick={handlePrev}
                            className={`flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage <= 0
                                ? "bg-gray-400"
                                : "bg-secondary hover:bg-blue-500"
                                }`}
                            disabled={currentPage <= 0}
                        >
                            <ArrowLeftIcon width={20} />
                        </button>

                        <button
                            onClick={handleNext}
                            className={`flex items-center justify-center text-white px-4 py-1 rounded-sm ${currentPage >= totalPages - 1
                                ? "bg-gray-400"
                                : "bg-secondary hover:bg-blue-500"
                                }`}
                            disabled={currentPage >= totalPages - 1}
                        >
                            <ArrowRightIcon width={20} />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default OffersSwiper;
