'use client'
import React, { useMemo } from 'react'
import { Typography } from '@material-tailwind/react'
import Section from '../home/<USER>';
import { motion } from "framer-motion";
import <PERSON><PERSON> from "lottie-react";
import cloudAnimation from "src/assets/cloud-animation.json";
import { CloudFog } from 'lucide-react';
import CTAButtons from '../home/<USER>';


function ManagedServicesIntro({ t, animations }) {
    const heroContent = useMemo(
        () => ({
          title: t("cloud_management_title"),
          slogan: t("cloud_management_slogan"),
          optimize: t("cloud_management_optimize"),
          description: t("cloud_management_description"),
          quote: t("request_quote"),
        }),
        [t]
      );
    return (
        <Section className="relative overflow-hidden">
        {/* Full-Width Hero Content Container */}
        <div className="relative z-10 flex flex-col lg:flex-row justify-between px-4 sm:px-6 lg:px-8">
          {/* Left Column: Text Content */}
          <motion.div
            className="text-left space-y-10 max-w-7xl"
            variants={animations.fadeInLeft}
            initial="hidden"
            animate="visible"
          >
            {/* Tagline */}
            <Typography
              variant="small"
              className="inline-flex items-center gap-2 text-indigo-500 font-inter py-2 px-4 rounded-full font-light tracking-widest uppercase border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <CloudFog className="w-5 h-5" />
              {heroContent.title}
            </Typography>

            {/* Main Slogan */}
            <Typography
              variant="h1"
              className="text-2xl sm:text-4xl lg:text-5xl font-bold leading-tight tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500"
            >
              {heroContent.slogan}
            </Typography>

            {/* Subtext and Description */}
            <div className="space-y-6">
              <Typography
                variant="lead"
                className="text-lg sm:text-xl font-inter text-gray-700 font-medium"
              >
                {heroContent.optimize}
              </Typography>
              <Typography
                variant="paragraph"
                className="text-base font-inter text-gray-600"
              >
                {heroContent.description}
              </Typography>
            </div>
            <CTAButtons t={t} />
          </motion.div>

          {/* Right Column: Animation */}
          <motion.div
            className="relative max-w-md lg:max-w-lg w-full mt-8 lg:mt-0"
            variants={animations.fadeInRight}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.4 }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="relative">
              <div className="absolute inset-0 rounded-full blur-3xl bg-indigo-200 opacity-20" />
              <Lottie
                animationData={cloudAnimation}
                loop={true}
                autoplay={true}
                className="w-full h-auto drop-shadow-xl"
              />
            </div>
          </motion.div>
        </div>

        {/* Background Decoration */}
        <div className="absolute bottom-0 left-0 w-full h-24 transform -skew-y-2 z-0">
          <div className="w-full h-full bg-white/10 backdrop-blur-sm" />
        </div>
      </Section>
    )
}

export default ManagedServicesIntro;