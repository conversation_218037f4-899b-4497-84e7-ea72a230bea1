'use client'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { BsArrowRight } from 'react-icons/bs'
import imgIntro from "/public/images/landing/landing-b-banner.webp";

function Banner({ t }) {
  return (
    <div
      className="w-full bg-white"
      style={{
        backgroundImage: "url('/images/landing/landing-b-hero-bg.svg')",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "center"
      }}>
      <div className="max-w-[1400px] mx-auto xl:pb-20 xl:pt-10 py-10 flex flex-col items-center justify-center">
        <div className="flex flex-col gap-y-2 items-center justify-center">
          <Link
            href='#contact-nous'
            className='rounded-full bg-[#5176F130] w-fit py-2 px-2 text-sm text-secondary text-center'
          >
            {t('quote_call_to_action2')}
          </Link>

          <Typography
            variant='h1'
            className="text-center text-3xl 2xl:text-4xl font-extrabold font-inter max-w-xl mx-auto">
            {t('solutions_title')}
          </Typography>
          <Typography
            variant='h1'
            className="text-center text-3xl 2xl:text-4xl font-extrabold font-inter max-w-xl mx-auto text-secondary">
            {t('performance_and_security')}
          </Typography>
          <p className="text-[15px] 2xl:text-base text-center max-w-5xl">
            {t('company_description2')}
          </p>
          <div className="block mx-auto w-fit px-2 py-1 mt-4 hover:rounded-md border-[#497EF7] text-black border-b hover:bg-secondary hover:text-white">
            <Link
              href="#contact-nous"
              className="flex items-center justify-between w-fit gap-x-5">
              <span className='text-sm xl:text-sm font-inter'>
                {t('quote_request')}
              </span>
              <BsArrowRight className='w-4' />
            </Link>
          </div>
        </div>

        <div className="mx-auto mt-0 overflow-clip w-full">
          <Image
            src={imgIntro}
            alt="Engineer working on server"
            width={900}
            height={500}
            sizes="(max-width: 768px) 100vw, 50vw"
            className="object-cover m-auto min-h-[330px]"
            placeholder='blur'
            priority
          />
        </div>
      </div>
    </div>
  )
}

export default Banner;