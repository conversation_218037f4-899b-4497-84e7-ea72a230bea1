'use client';
import React from "react";
import { Typography, Card, CardBody } from "@material-tailwind/react";

const AnnouncementsPage = () => {
  const announcements = [
    {
      title: "New Feature Release",
      description: "We've added a new feature to improve your experience!",
      date: "March 15, 2023",
    },
    {
      title: "Scheduled Maintenance",
      description: "Our servers will be down for maintenance tonight.",
      date: "April 10, 2023",
    },
  ];

  return (
    <div>
      <Typography variant="h4" className="mb-4 text-gray-900 font-bold">
        Announcements
      </Typography>
      <div className="space-y-4">
        {announcements.map((announcement) => (
          <Card key={announcement.title} className="p-4 bg-white shadow-md rounded-md">
            <Typography variant="h6" className="font-medium text-gray-900">
              {announcement.title}
            </Typography>
            <Typography className="text-gray-700 mt-2">
              {announcement.description}
            </Typography>
            <Typography variant="small" className="text-xs text-gray-500 mt-2">
              {announcement.date}
            </Typography>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default AnnouncementsPage;