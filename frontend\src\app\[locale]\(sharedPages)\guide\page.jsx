"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import Banner from "@/components/guide/banner";
import OurSolutionsGuide from "@/components/guide/ourSolutionsGuide";
import HostingAdvantages from "@/components/guide/hostingAdvantages";
import WhyChooseUs from "@/components/guide/whyChooseUs";
import ContactForm2 from "@/components/shared/contactForm2";

function Guide() {
  const t = useTranslations("guide");

  const [data, setData] = useState({
    page: "Guide",
    offerName: "No offer selected",
    id: 0,
    url: "/guide",
  });

  return (
    <div>
      <Banner t={t} />
      <OurSolutionsGuide t={t} />
      <HostingAdvantages t={t} />
      <WhyChooseUs t={t} />
      <ContactForm2 data={data} setData={setData} />
    </div>
  );
}

export default Guide;
