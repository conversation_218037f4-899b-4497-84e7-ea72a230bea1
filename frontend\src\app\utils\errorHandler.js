// utils/errorHandler.js
export function handleApiErrors(err) {
    const errorResponse = err.response?.data;

    if (errorResponse?.errors) {
        // Process field-specific errors
        const fieldErrors = {};
        errorResponse.errors.forEach((error) => {
            fieldErrors[error.key] = error.msg;
        });
        return { fieldErrors };
    }

    // Process global error
    const globalError = errorResponse?.message || 'An unexpected error occurred.';
    return { globalError };
}
