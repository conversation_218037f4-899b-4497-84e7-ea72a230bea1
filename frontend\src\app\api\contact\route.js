import nodemailer from "nodemailer";
import { NextResponse } from "next/server";
import <PERSON><PERSON> from "joi";

// <PERSON><PERSON> schema for server-side validation
const contactSchema = Joi.object({
  fullName: Joi.string().required().messages({
    "any.required": "Le nom complet est requis.",
    "string.empty": "Le nom complet ne peut pas être vide.",
  }),
  email: Joi.string().email().required().messages({
    "any.required": "L'email est requis.",
    "string.email": "L'email n'est pas valide.",
  }),
  phone: Joi.string()
    .pattern(/^\+?\d+$/)
    .required()
    .messages({
      "any.required": "Le numéro de téléphone est requis.",
      "string.pattern.base": "Le numéro de téléphone est invalide.",
    }),
  message: Joi.string().required().messages({
    "any.required": "Le message est requis.",
  }),
});

export async function POST(req) {
  try {
    const body = await req.json();

    // Validate the reCAPTCHA token
    const { recaptchaToken, data, ...formData } = body;
    if (!recaptchaToken) {
      return NextResponse.json(
        { success: false, error: "reCAPTCHA token is missing." },
        { status: 400 }
      );
    }

    const recaptchaResponse = await fetch(
      `https://www.google.com/recaptcha/api/siteverify`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          secret: process.env.RECAPTCHA_SECRET_KEY,
          response: recaptchaToken,
        }),
      }
    );

    const recaptchaResult = await recaptchaResponse.json();
    if (!recaptchaResult.success) {
      return NextResponse.json(
        { success: false, error: "reCAPTCHA validation failed." },
        { status: 400 }
      );
    }

    const { error } = contactSchema.validate(formData, { abortEarly: false });
    if (error) {
      const errors = error.details.map((err) => err.message);
      return NextResponse.json({ success: false, errors }, { status: 400 });
    }
    const { fullName, email, phone, message } = formData;
    const { EMAIL_HOST, EMAIL_PORT, EMAIL_USER, EMAIL_PASSWORD } = process.env;

    // Ensure environment variables are loaded
    if (!EMAIL_HOST || !EMAIL_PORT || !EMAIL_USER || !EMAIL_PASSWORD) {
      // console.error("Missing email configuration in environment variables.");
      return NextResponse.json(
        { success: false, error: "Email configuration is incomplete." },
        { status: 500 }
      );
    }

    // Configure Nodemailer transporter
    const transporter = nodemailer.createTransport({
      host: EMAIL_HOST,
      port: parseInt(EMAIL_PORT, 10), // Ensure the port is a number
      secure: EMAIL_PORT === "465", // Use secure for port 465
      auth: {
        user: EMAIL_USER,
        pass: EMAIL_PASSWORD,
      },
    });

    // Test the connection (optional for debugging)
    // await transporter.verify();
    // console.log("Transporter is successfully verified.");

    const mailOptions = {
      from: `"Contact Form" <${EMAIL_USER}>`,
      to: EMAIL_USER,
      subject: `New Contact Form Submission from ${fullName}`,
      html: `
        <div style="font-family: Arial, sans-serif; color: #333; line-height: 1.6;">
            <h2 style="color: #555;">New Contact Form Submission</h2>

            ${
              data
                ? `
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Full Name:</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${fullName}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Client Email:</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${email}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Client Phone Number</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${
                              phone || "---"
                            }</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Client Message</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${message}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Page Name:</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${
                              data.page
                            }</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Offer Name:</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${
                              data.offerName
                            }</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Page URL:</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${
                              data.url
                            }</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Date & Time</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${new Date().toLocaleString()}</td>
                        </tr>
                    </table>
                    `
                : ""
            }
            <p style="margin-top: 20px;">Best regards,<br>Ztech Engineering</p>
        </div>
    `,
    };

    // Send the email
    const info = await transporter.sendMail(mailOptions);
    console.log("Email sent successfully", info);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error while sending email");
    return NextResponse.json(
      {
        success: false,
        error: "Failed to send email. Please try again later.",
      },
      { status: 500 }
    );
  }
}
