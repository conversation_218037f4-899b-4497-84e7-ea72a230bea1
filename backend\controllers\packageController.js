const Package = require("../models/Package");
const ProductStatus = require("../constants/enums/poduct-status");

// Get all packages
exports.getPackages = async (req, res) => {
  try {
    const { brandName } = req.query;
    let query = { status: ProductStatus.PUBLISHED };

    const packages = await Package.find(query)
      .populate({
        path: "brand",
        match: brandName
          ? { name: { $regex: new RegExp(brandName, "i") } }
          : {},
      })
      .populate("specifications")
      .sort({ price: 1 }); // 1 for ascending order (cheaper first)

    // Filter out packages where brand is null (didn't match the brand name)
    const filteredPackages = brandName
      ? packages.filter((package) => package.brand !== null)
      : packages;

    res.status(200).json(filteredPackages);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};
