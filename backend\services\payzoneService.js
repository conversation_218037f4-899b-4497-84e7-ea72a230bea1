const PaymentLog = require("../models/PaymentLog");
const axios = require("axios");
const crypto = require("crypto");
require("dotenv").config();

const PAYZONE_URL = process.env.PAYZONE_URL;

// ✅ Generate SHA256 Signature
const generateSignature = (secretKey, payload) => {
  return crypto
    .createHash("sha256") // ✅ SHA-256 Hashing (NOT HMAC)
    .update(secretKey + payload, "utf8") // ✅ Concatenation as per Payzone docs
    .digest("hex"); // ✅ Generate hex output
};

const verifyCallbackSignature = (
  notificationKey,
  receivedBody,
  receivedSignature
) => {
  const calculatedSignature = crypto
    .createHmac("sha256", notificationKey) // ✅ HMAC-SHA256
    .update(receivedBody, "utf8")
    .digest("hex")
    .toUpperCase();
  return calculatedSignature === receivedSignature.toUpperCase();
};

// ✅ Create Paywall (Initiate Payment)
exports.createPaywall = async (data) => {
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const payload = {
      merchantAccount: process.env.PAYZONE_MERCHANT_ACCOUNT,
      timestamp,
      skin: "abb",
      customerId: data.customerId || "user-12345",
      customerCountry: "MA",
      customerLocale: "fr_FR",
      customerStreetAddress: data.address,
      customerName: data.BillToName,
      customerEmail: data.email,
      chargeId: timestamp,
      orderId: data.orderId || "order123",
      currency: "MAD",
      price: data.amount || "0",
      description: data.description || " ",
      mode: "DEEP_LINK",
      paymentMethod: "CREDIT_CARD",
      successUrl: `${process.env.BACKEND_URL}/payment/success`,
      failureUrl: `${process.env.BACKEND_URL}/payment/failed`,
      callbackUrl: `${process.env.BACKEND_URL}/payment/callback`,
      flowCompletionUrl: `${process.env.FRONTEND_URL}/client/cart`,
      cancelUrl: `${process.env.BACKEND_URL}/payment/cancel`,
    };
    // savePaymentProfile: true,
    // recurrence: {
    //   type: "INITIAL",
    // },

    const jsonPayload = JSON.stringify(payload);
    const signature = generateSignature(
      process.env.PAYZONE_SECRET_KEY,
      jsonPayload
    );

    return { payzoneUrl: PAYZONE_URL, payload: jsonPayload, signature };
  } catch (error) {
    throw new Error("Failed to create payment session");
  }
};

// ✅ Process Payzone Callback (Verify Payment)
exports.processCallback = async (req) => {
  console.log("req.headers ::: => ", req.headers);
  const receivedSignature = req.headers["x-callback-signature"];

  const isitmatched = verifyCallbackSignature(
    process.env.PAYZONE_NOTIFICATION_KEY,
    req.rawBody,
    receivedSignature
  );
  console.log("🚀 ~ exports.createPaywall= ~ isitmatched:", isitmatched);

  const isitmatchedwithbody = verifyCallbackSignature(
    process.env.PAYZONE_NOTIFICATION_KEY,
    JSON.stringify(req.body),
    receivedSignature
  );
  console.log(
    "🚀 ~ exports.createPaywall= ~ isitmatchedwithbody:",
    isitmatchedwithbody
  );

  return isitmatched;
};

exports.logPaymentStatus = async (orderId, transactionId, status, rawData) => {
  try {
    await PaymentLog.create({
      orderId,
      transactionId,
      status,
      rawData,
    });

    console.log(`📌 Payment Log Saved: orderId=${orderId}, status=${status}`);
  } catch (error) {
    console.error("🚨 Error logging payment:", error.message);
  }
};

exports.getPaymentHistory = async (orderId) => {
  try {
    return await PaymentLog.find({ orderId }).sort({ timestamp: 1 });
  } catch (error) {
    console.error("🚨 Error fetching payment history:", error.message);
    throw error;
  }
};

// ✅ 3️⃣ Get Transaction Details
exports.getTransaction = async (id) => {
  const response = await axios.get(`${PAYZONE_URL}/${id}`, {
    headers: {
      "X-MerchantAccount": process.env.PAYZONE_MERCHANT_ACCOUNT,
      "X-CallerName": process.env.PAYZONE_CALLER_NAME,
      "Content-Type": "application/json",
    },
  });
  return response.data;
};

// ✅ 4️⃣ Capture Payment
exports.capturePayment = async (id, amount) => {
  const response = await axios.post(`${PAYZONE_URL}/${id}`, {
    command: "SETTLE",
    amount,
  });
  return response.data;
};

// ✅ 5️⃣ Cancel Payment
exports.cancelPayment = async (id) => {
  const response = await axios.post(`${PAYZONE_URL}/${id}`, {
    command: "AUTH_REVERSAL",
  });
  return response.data;
};

// ✅ 6️⃣ Refund Payment
exports.refundPayment = async (id, amount) => {
  const response = await axios.post(`${PAYZONE_URL}/${id}`, {
    command: "REFUND",
    amount,
  });
  return response.data;
};

exports.createAutoRenewalPaywall = async (data) => {
  try {
    const timestamp = Math.floor(Date.now() / 1000);
    const renewalPayload = {
      merchantAccount: process.env.PAYZONE_MERCHANT_ACCOUNT,
      timestamp,
      skin: "abb",
      customerId: data.customerId || "user-12345",
      customerCountry: "MA",
      customerLocale: "fr_FR",
      customerStreetAddress: data.address,
      customerName: data.BillToName,
      customerEmail: data.email,
      chargeId: timestamp,
      orderId: data.orderId || "order123",
      currency: "MAD",
      price: data.amount || "0",
      description: data.description || "Test Product",
      mode: "DEEP_LINK",
      paymentMethod: "CREDIT_CARD",
      successUrl: `${process.env.BACKEND_URL}/payment/success`,
      failureUrl: `${process.env.BACKEND_URL}/payment/failed`,
      callbackUrl: `${process.env.BACKEND_URL}/payment/callback`,
      flowCompletionUrl: `${process.env.FRONTEND_URL}/cart`,
      cancelUrl: `${process.env.BACKEND_URL}/payment/cancel`,

      // Use stored payment profile
      recurrence: { type: "RENEWAL" },
      storedPaymentProfileId: data.storedPaymentProfileId, // Retrieved from initial charge
    };

    const jsonRenewalPayload = JSON.stringify(renewalPayload);
    const signature = generateSignature(
      process.env.PAYZONE_SECRET_KEY,
      jsonRenewalPayload
    );

    return { payzoneUrl: PAYZONE_URL, payload: jsonRenewalPayload, signature };
  } catch (error) {
    throw new Error("Failed to create payment session");
  }
};
