import { Document, Page, View, Text, Image, StyleSheet } from '@react-pdf/renderer';
import { formatDate } from '../../app/utils/dateFormatter';

const styles = StyleSheet.create({
  page: { padding: 30, fontSize: 10, color: '#333' },
  header: { flexDirection: 'row', marginBottom: 20 },
  logo: { width: 120, height: 'auto' },
  headerRight: { flex: 1, alignItems: 'flex-end' },
  bold: { fontWeight: 'bold' },
  companyDetails: { marginTop: 10, fontSize: 9, color: '#666' },

  // Enhanced billing section
  billingSection: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#f8f9fa'
  },
  billingTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2d3748'
  },
  billingInfo: {
    marginBottom: 3, // Added spacing between billing info lines
  },
  billingRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 10,
  },
  billingItem: {
    flexDirection: 'row',
    marginBottom: 5,
    width: '50%',
    padding: 4,
    backgroundColor: '#f1f5f9',
    marginRight: 0,
    borderRadius: 4,
    marginTop: 4,
    flexWrap: 'wrap',
    minHeight: 24,
  },
  billingLabel: {
    fontSize: 9,
    color: '#4a5568',
    fontWeight: 'bold',
    width: 80,
    marginRight: 5,
  },
  billingValue: {
    fontSize: 9,
    flex: 1,
    flexWrap: 'wrap',
  },

  // Enhanced table styles
  table: { marginBottom: 0 }, // Changed from 30 to 0
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f1f5f9',
    paddingVertical: 8,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#cbd5e1'
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 0.5,
    borderBottomColor: '#e2e8f0',
    paddingVertical: 8,
    paddingHorizontal: 5
  },
  description: { flex: 3 },
  price: { flex: 1, textAlign: 'right' },
  quantity: { flex: 1, textAlign: 'right' },
  period: { flex: 1.5, textAlign: 'right' },
  discount: { flex: 1, textAlign: 'right' },
  amount: { flex: 1, textAlign: 'right' },

  // Service details
  servicePeriod: {
    fontSize: 8,
    color: '#64748b',
    marginTop: 4
  },
  serviceQuantity: {
    fontSize: 8,
    color: '#64748b'
  },

  // Enhanced totals section
  totalsSection: {
    marginLeft: 'auto',
    width: '40%',
    marginTop: 0, // Changed from 20 to 0
    borderTopWidth: 1,
    borderTopColor: '#cbd5e1',
    paddingTop: 10
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5
  },
  grandTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#cbd5e1',
    fontWeight: 'bold'
  },

  // Status styles
  status: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 5
  },
  statusPaid: {
    backgroundColor: '#dcfce7',
    color: '#166534'
  },
  statusPending: {
    backgroundColor: '#fff7ed',
    color: '#9a3412'
  },

  // Company information styles
  companySection: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    backgroundColor: '#f8f9fa'
  },
  companyTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#2d3748'
  },
  companyInfo: {
    fontSize: 9,
    marginBottom: 3,
    display: 'flex',
    flexDirection: 'row'
  },
  companyLabel: {
    fontSize: 9,
    fontWeight: 'bold',
    marginRight: 5,
    width: 80
  },

  // Signature section
  signatureSection: {
    marginTop: 20,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center'
  },
  signatureImage: {
    width: 150,
    height: 50
  },
  signatureText: {
    fontSize: 9,
    color: '#4a5568',
    marginTop: 5,
    textAlign: 'center'
  }
});

const InvoiceTemplate = ({ payment, t }) => (
  <Document>
    <Page size="A4" style={styles.page} wrap>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Image src="/images/home/<USER>" alt="Invoice template" style={styles.logo} />
          <View style={styles.companyDetails}>
            <Text style={styles.bold}>ZTechEngineering</Text>
            <Text>N° 5 Premier Étage Place Idriss II</Text>
            <Text>Boulevard Kennedy Safi, Maroc</Text>
            <Text><EMAIL></Text>
            <Text>Tel: +*********** 605</Text>
          </View>
        </View>

        <View style={styles.headerRight}>
          <Text style={[styles.bold, { fontSize: 20, marginBottom: 10 }]}>{t('invoice.title')}</Text>
          <Text><Text style={styles.bold}>{t('invoice.number')}:</Text> {payment.paymentId}</Text>
          <Text><Text style={styles.bold}>{t('invoice.date')}:</Text> {formatDate(payment.paymentDate)}</Text>

          {/* <View style={[
            styles.status,
            payment.status === 'PAID' ? styles.statusPaid : styles.statusPending
          ]}>
            <Text>{payment.status}</Text>
          </View> */}
        </View>
      </View>

      {/* Billing Information */}
      <View style={styles.billingSection}>
        <Text style={styles.billingTitle}>{t('invoice.billed_to')}</Text>

        <View style={styles.billingRow}>
          <View style={styles.billingItem}>
            <Text style={styles.billingLabel}>{t('full_name')}:</Text>
            <Text style={[styles.bold, styles.billingValue]}>{payment.billingInfo.BillToName}</Text>
          </View>
          <View style={styles.billingItem}>
            <Text style={styles.billingLabel}>{t('email')}:</Text>
            <Text style={styles.billingValue}>{payment.billingInfo.email}</Text>
          </View>
          <View style={styles.billingItem}>
            <Text style={styles.billingLabel}>{t('phone')}:</Text>
            <Text style={styles.billingValue}>{payment.billingInfo.phone}</Text>
          </View>
          <View style={[styles.billingItem, { width: '100%' }]}>
            <Text style={styles.billingLabel}>{t('address')}:</Text>
            <Text style={styles.billingValue}>{payment.billingInfo.address}</Text>
          </View>
          <View style={styles.billingItem}>
            <Text style={styles.billingLabel}>{t('country')}:</Text>
            <Text style={styles.billingValue}>{payment.billingInfo.country}</Text>
          </View>
        </View>

        {/* Company Information (conditionally rendered) */}
        {payment.billingInfo.isCompany && (
          <View style={styles.companySection}>
            <Text style={styles.companyTitle}>{t('company_info')}</Text>
            <View style={styles.billingRow}>
              {payment.billingInfo.companyICE && (
                <View style={styles.billingItem}>
                  <Text style={styles.billingLabel}>{t('ice')}:</Text>
                  <Text style={styles.billingValue}>{payment.billingInfo.companyICE}</Text>
                </View>
              )}
              {payment.billingInfo.companyEmail && (
                <View style={styles.billingItem}>
                  <Text style={styles.billingLabel}>{t('email')}:</Text>
                  <Text style={styles.billingValue}>{payment.billingInfo.companyEmail}</Text>
                </View>
              )}
              {payment.billingInfo.companyPhone && (
                <View style={styles.billingItem}>
                  <Text style={styles.billingLabel}>{t('phone')}:</Text>
                  <Text style={styles.billingValue}>{payment.billingInfo.companyPhone}</Text>
                </View>
              )}
              {payment.billingInfo.companyAddress && (
                <View style={[styles.billingItem, { width: '100%' }]}>
                  <Text style={styles.billingLabel}>{t('address')}:</Text>
                  <Text style={styles.billingValue}>{payment.billingInfo.companyAddress}</Text>
                </View>
              )}
            </View>
          </View>
        )}
      </View>

      {/* Services Table */}
      <View style={styles.table}>
        <View style={styles.tableHeader}>
          <Text style={[styles.bold, styles.description]}>{t('invoice.name')}</Text>
          <Text style={[styles.bold, styles.quantity]}>{t('invoice.quantity')}</Text>
          <Text style={[styles.bold, styles.period]}>{t('invoice.period')}</Text>
          <Text style={[styles.bold, styles.price]}>{t('invoice.price')}</Text>
          <Text style={[styles.bold, styles.discount]}>{t('invoice.discount')}</Text>
          <Text style={[styles.bold, styles.amount]}>{t('invoice.amount')}</Text>
        </View>

        {payment.services.map((service, index) => (
          <View key={index} style={styles.tableRow}>
            <View style={styles.description}>
              <Text style={styles.bold}>{service.name}</Text>
              <Text style={styles.servicePeriod}>
                {formatDate(payment.paymentDate)} - {formatDate(new Date(payment.paymentDate).setMonth(new Date(payment.paymentDate).getMonth() + service.period))}
              </Text>
            </View>
            <Text style={styles.quantity}>{service.quantity}</Text>
            <Text style={styles.period}>{service.period} {service.period > 1 ? t('months') : t('month')}</Text>
            <Text style={styles.price}>{service.price.toFixed(2)}</Text>
            <Text style={styles.discount}>{(service.discount || 0).toFixed(2)}</Text>
            <Text style={styles.amount}>
              {((service.price * service.quantity) - (service.discount || 0)).toFixed(2)}
            </Text>
          </View>
        ))}
      </View>

      {/* Totals Section */}
      <View style={styles.totalsSection}>
        <View style={styles.totalRow}>
          <Text>{t('invoice.subtotal')}:</Text>
          <Text>{payment.subTotal.toFixed(2)} {payment.currency}</Text>
        </View>
        <View style={styles.totalRow}>
          <Text>{t('invoice.vat')}:</Text>
          <Text>{payment.taxAmount.toFixed(2)} {payment.currency}</Text>
        </View>
        <View style={styles.grandTotal}>
          <Text>{t('invoice.total')}:</Text>
          <Text>{payment.totalPrice.toFixed(2)} {payment.currency}</Text>
        </View>
      </View>

      {/* Signature Section */}
      <View style={styles.signatureSection}>
        <View>
          <Image src="/images/payment-invoice/signature.jpeg" alt="Payment Invoice" style={styles.signatureImage} />
        </View>
      </View>

      {/* Footer Note */}
      {/* <Text style={{ fontSize: 8, color: '#666', marginTop: 10, textAlign: 'center' }}>
        {t('invoice.thank_you') || 'Thank you for your business'}
      </Text> */}
    </Page>
  </Document>
);

export default InvoiceTemplate;
