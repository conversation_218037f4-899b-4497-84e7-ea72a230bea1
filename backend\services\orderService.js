const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const Cart = require("../models/Cart");
const OrderStatus = require("../constants/enums/order-status");
const { processPayment } = require("../controllers/paymentController");
const {
  sendOrderSummaryEmail,
  sendNewOrderAdminAlert,
} = require("../routes/sendEmail/sendEmail");

// ✅ Mark Order as Paid (Process Successful Payment)
exports.markOrderAsPaid = async (oid, transactionId) => {
  try {
    console.log("🚀 Processing Successful Payment");

    const order = await Order.findByIdAndUpdate(
      oid,
      {
        status: OrderStatus.PROCESSING,
        datePaid: new Date(),
        isPaid: true,
        transactionId,
        isPaymentProcessed: true,
      },
      { new: true }
    )
      .populate("user")
      .populate({
        path: "subOrders",
        populate: { path: "package", model: "Package" },
      });

    await SubOrder.updateMany(
      { _id: { $in: order.subOrders } },
      { status: OrderStatus.PROCESSING }
    );

    await processPayment({
      userId: order.user._id,
      orderId: oid,
      paymentMethod: order.paymentMethod,
      status: OrderStatus.COMPLETED,
      transactionId,
    });

    await sendOrderSummaryEmail(order);
    await sendNewOrderAdminAlert(order);
    await Cart.findOneAndDelete({ user: order.user._id });

    // Trigger VPS provisioning for VPS orders
    try {
      const VPSService = require("./vpsService");
      const vpsService = new VPSService();

      for (const subOrder of order.subOrders) {
        if (subOrder.vps) {
          console.log(
            `🖥️  Starting VPS provisioning for subOrder: ${subOrder.identifiant}`
          );
          await vpsService.provisionVPS(order, subOrder);
        }
      }
    } catch (vpsError) {
      console.error("❌ Error during VPS provisioning:", vpsError.message);
      // Don't fail the entire payment process if VPS provisioning fails
    }

    console.log("✅ Order processed successfully:", order);
    return true;
  } catch (error) {
    console.error("🚨 Error in markOrderAsPaid:", error);
    return false;
  }
};

// ❌ Mark Order as Failed (Process Failed Payment)
exports.markOrderAsFailed = async (oid, transactionId) => {
  try {
    console.log("🚨 Processing Failed Payment");

    const order = await Order.findByIdAndUpdate(
      oid,
      { status: OrderStatus.FAILED, transactionId, isPaymentProcessed: true },
      { new: true }
    );

    await SubOrder.updateMany(
      { _id: { $in: order.subOrders } },
      { status: OrderStatus.FAILED }
    );

    console.log("❌ Order marked as failed:", order);
  } catch (error) {
    console.error("🚨 Error in markOrderAsFailed:", error);
  }
};

// ❌ Mark Order as Pending (Process Failed Payment)
exports.markOrderAsPending = async (oid, transactionId) => {
  try {
    console.log("🚨 Processing Pending Payment");

    const order = await Order.findByIdAndUpdate(
      oid,
      { status: OrderStatus.PENDING, transactionId, isPaymentProcessed: true },
      { new: true }
    );

    await SubOrder.updateMany(
      { _id: { $in: order.subOrders } },
      { status: OrderStatus.PENDING }
    );

    console.log("❌ Order marked as PENDING:", order);
  } catch (error) {
    console.error("🚨 Error in markOrderAsPENDING:", error);
  }
};
