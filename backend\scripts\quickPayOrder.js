#!/usr/bin/env node

/**
 * Quick script to mark an order as paid without confirmation prompts
 * Usage: node quickPayOrder.js <orderId>
 * Example: node quickPayOrder.js 67890abcdef123456789
 */

const mongoose = require("mongoose");
const dotenv = require("dotenv");
const path = require("path");

// Load environment variables
dotenv.config({ path: path.join(__dirname, "../.env") });

// Import models and services
const Order = require("../models/Order");
const User = require("../models/User");
const Package = require("../models/Package");
const SubOrder = require("../models/SubOrder");
const orderService = require("../services/orderService");

// Connect to database
async function connectDB() {
  try {
    // Use local MongoDB with zn_tech database
    const localMongoUri = "mongodb://localhost:27017/zn_ztech";

    await mongoose.connect(localMongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("✅ Connected to local MongoDB (zn_ztech database)");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    console.error("Make sure MongoDB is running locally on port 27017");
    process.exit(1);
  }
}

// Quick function to mark order as paid
async function quickPayOrder(orderId) {
  try {
    console.log(`🔍 Processing order: ${orderId}`);

    // Find order by ID or identifiant
    let order = await Order.findById(orderId);
    if (!order) {
      order = await Order.findOne({ identifiant: orderId });
    }

    if (!order) {
      console.error(`❌ Order not found: ${orderId}`);
      return false;
    }

    // Generate a test transaction ID
    const testTransactionId = `QUICK_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 6)}`;

    console.log(`💳 Marking order as paid...`);

    // Use the orderService to mark as paid
    const success = await orderService.markOrderAsPaid(
      order._id,
      testTransactionId
    );

    if (success) {
      console.log(`✅ Order ${order.identifiant} marked as paid!`);
      console.log(`   Transaction ID: ${testTransactionId}`);
      return true;
    } else {
      console.error("❌ Failed to mark order as paid");
      return false;
    }
  } catch (error) {
    console.error("❌ Error:", error.message);
    return false;
  }
}

// Main execution
async function main() {
  const orderId = process.argv[2];

  if (!orderId) {
    console.error("❌ Please provide an order ID");
    console.log("Usage: node quickPayOrder.js <orderId>");
    process.exit(1);
  }

  await connectDB();

  const success = await quickPayOrder(orderId);

  // Close database connection
  await mongoose.connection.close();

  if (!success) {
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Script execution failed:", error);
    process.exit(1);
  });
}

module.exports = { quickPayOrder };
