'use client';

import React, { useState } from 'react';
import { RefreshCw, CheckCircle, AlertCircle, Clock, Server, Wifi } from 'lucide-react';
import vpsService from '../services/vpsService';

const VPSStatusChecker = ({ instanceId, instanceName, onStatusUpdate }) => {
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheck, setLastCheck] = useState(null);
  const [status, setStatus] = useState(null);
  const [error, setError] = useState(null);

  const checkStatus = async () => {
    if (!instanceId) return;

    setIsChecking(true);
    setError(null);

    try {
      console.log(`🔍 Vérification du statut pour l'instance ${instanceId}`);
      
      const response = await vpsService.getInstanceStatus(instanceId);
      console.log('📋 Réponse du statut:', response);

      if (response.data && response.data.success) {
        const instanceData = response.data.data;
        setStatus(instanceData);
        setLastCheck(new Date());
        
        // Notifier le parent du changement de statut
        if (onStatusUpdate) {
          onStatusUpdate(instanceData);
        }
      } else {
        throw new Error(response.data?.message || 'Impossible de récupérer le statut');
      }

    } catch (error) {
      console.error('❌ Erreur lors de la vérification du statut:', error);
      setError(error.message || 'Erreur lors de la vérification');
    } finally {
      setIsChecking(false);
    }
  };

  const getStatusIcon = (statusValue) => {
    if (!statusValue) return <Server className="w-5 h-5 text-gray-400" />;
    
    const statusLower = statusValue.toLowerCase();
    
    switch (statusLower) {
      case 'running':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'stopped':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'installing':
      case 'provisioning':
        return <Clock className="w-5 h-5 text-blue-500" />;
      default:
        return <Server className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusMessage = (statusValue) => {
    if (!statusValue) return 'Statut inconnu';
    
    const statusLower = statusValue.toLowerCase();
    
    switch (statusLower) {
      case 'running':
        return 'En cours d\'exécution - Réinstallation terminée ✅';
      case 'stopped':
        return 'Arrêté - Peut nécessiter un redémarrage';
      case 'installing':
      case 'provisioning':
        return 'Réinstallation en cours... ⏳';
      case 'error':
        return 'Erreur détectée - Vérifiez le panel Contabo';
      default:
        return `Statut: ${statusValue}`;
    }
  };

  const getStatusColor = (statusValue) => {
    if (!statusValue) return 'text-gray-500';
    
    const statusLower = statusValue.toLowerCase();
    
    switch (statusLower) {
      case 'running':
        return 'text-green-600';
      case 'stopped':
        return 'text-red-600';
      case 'installing':
      case 'provisioning':
        return 'text-blue-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <Server className="w-5 h-5 mr-2 text-blue-600" />
          Vérification du Statut VPS
        </h3>
        
        <button
          onClick={checkStatus}
          disabled={isChecking}
          className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
          {isChecking ? 'Vérification...' : 'Vérifier'}
        </button>
      </div>

      {/* Informations de l'instance */}
      <div className="mb-4 p-3 bg-gray-50 rounded-md">
        <p className="text-sm text-gray-600">
          <strong>Instance:</strong> {instanceName || instanceId}
        </p>
        <p className="text-sm text-gray-600">
          <strong>ID:</strong> {instanceId}
        </p>
      </div>

      {/* Statut actuel */}
      {status && (
        <div className="mb-4 p-3 border rounded-md">
          <div className="flex items-center mb-2">
            {getStatusIcon(status.status)}
            <span className={`ml-2 font-medium ${getStatusColor(status.status)}`}>
              {getStatusMessage(status.status)}
            </span>
          </div>
          
          {status.ip && (
            <div className="flex items-center text-sm text-gray-600 mb-1">
              <Wifi className="w-4 h-4 mr-2" />
              <span>IP: {status.ip}</span>
            </div>
          )}
          
          {status.region && (
            <div className="text-sm text-gray-600">
              <strong>Région:</strong> {status.region}
            </div>
          )}
        </div>
      )}

      {/* Recommandations */}
      {status && status.status && (
        <div className="mb-4 p-3 bg-blue-50 rounded-md">
          <h4 className="font-medium text-blue-900 mb-2">Recommandations:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            {status.status.toLowerCase() === 'running' && (
              <>
                <li>• Testez la connexion SSH avec le nouveau mot de passe</li>
                <li>• Vérifiez que le bon OS est installé</li>
                <li>• Configurez vos applications si nécessaire</li>
              </>
            )}
            {(status.status.toLowerCase() === 'installing' || status.status.toLowerCase() === 'provisioning') && (
              <>
                <li>• Attendez la fin de l'installation (5-15 minutes)</li>
                <li>• Vérifiez à nouveau dans quelques minutes</li>
              </>
            )}
            {status.status.toLowerCase() === 'stopped' && (
              <>
                <li>• Démarrez l'instance si nécessaire</li>
                <li>• Vérifiez les logs dans le panel Contabo</li>
              </>
            )}
          </ul>
        </div>
      )}

      {/* Test de connectivité */}
      {status && status.ip && status.status?.toLowerCase() === 'running' && (
        <div className="mb-4 p-3 bg-green-50 rounded-md">
          <h4 className="font-medium text-green-900 mb-2">Test de Connectivité:</h4>
          <div className="text-sm text-green-800">
            <p className="mb-1">
              <strong>Ping:</strong> <code className="bg-white px-2 py-1 rounded">ping {status.ip}</code>
            </p>
            <p>
              <strong>SSH:</strong> <code className="bg-white px-2 py-1 rounded">ssh root@{status.ip}</code>
            </p>
          </div>
        </div>
      )}

      {/* Erreur */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 font-medium">Erreur</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      )}

      {/* Dernière vérification */}
      {lastCheck && (
        <div className="text-xs text-gray-500 text-center">
          Dernière vérification: {lastCheck.toLocaleString()}
        </div>
      )}
    </div>
  );
};

export default VPSStatusChecker;
