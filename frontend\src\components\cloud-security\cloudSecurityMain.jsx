'use client'
import { Typography } from '@material-tailwind/react'
import React from 'react'
import { BLUECHECKsvg } from '../../icons/svgIcons'
import { BsAmazon, BsCheck, BsGoogle } from 'react-icons/bs'
import OffersSwiper2 from './offersSwiper2'
import sharedHostingImg from "/public/images/services/shared-hosting.png";
import secondaryCloudImg from "/public/images/services/secondary-cloud.png";
import Image from 'next/image'
import CTAButtons from '../home/<USER>';
import cloudSecurityImage from '/public/images/cloud-security/cloud-security.jpg'
import { motion } from "framer-motion";
import Section from '../home/<USER>'



const checkItems = [
    {
        text: "checkItems.0.text",
        strongText: "checkItems.0.strongText"
    },
    {
        text: "checkItems.1.text",
        strongText: "checkItems.1.strongText"
    },
    {
        text: "checkItems.2.text",
        strongText: "checkItems.2.strongText"
    },
    {
        text: "checkItems.3.text",
        strongText: "checkItems.3.strongText"
    },
];

const offersDataNew = [
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.0.title",
        description: "offersData.0.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.1.title",
        description: "offersData.1.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.3.title",
        description: "offersData.3.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.6.title",
        description: "offersData.6.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.2.title",
        description: "offersData.2.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.4.title",
        description: "offersData.4.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.5.title",
        description: "offersData.5.description",
        items: []
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offersData.7.title",
        description: "offersData.7.description",
        items: []
    },
    // {
    //     icon: "/images/ai/check.png",
    //     isImg: true,
    //     title: "offersData.8.title",
    //     description: "",
    //     items: [
    //         "offersData.8.items.0",
    //         "offersData.8.items.1",
    //         "offersData.8.items.2",
    //         "offersData.8.items.3",
    //     ]
    // }
];

const ofensiveDefService = {
    title: 'ofensiveDefService.title',
    itemsData: [
        {
            icon: "/images/ai/Hacker.gif",
            isImg: true,
            title: "ofensiveDefService.itemsData.0.title",
            description: "ofensiveDefService.itemsData.0.description",
        },
        {
            icon: "/images/ai/red-team.gif",
            isImg: true,
            title: "ofensiveDefService.itemsData.1.title",
            description: "ofensiveDefService.itemsData.1.description",
        },
        {
            icon: "/images/ai/blue-team.gif",
            isImg: true,
            title: "ofensiveDefService.itemsData.2.title",
            description: "ofensiveDefService.itemsData.2.description",
        },
        {
            icon: "/images/ai/Team.gif",
            isImg: true,
            title: "ofensiveDefService.itemsData.3.title",
            description: "ofensiveDefService.itemsData.3.description",
        }
    ]
}


const awsOffersData = [
    {
        // icon: "/images/ai/main-image.png",
        icon: <BsAmazon size={30} />,
        isImg: false,
        title: "awsOffersData.0.title",
        items: [
            "awsOffersData.0.items.0",
            "awsOffersData.0.items.1",
            "awsOffersData.0.items.2",
        ]
    },
    {
        icon: <BsGoogle size={30} />,
        isImg: false,
        title: "awsOffersData.1.title",
        items: [
            "awsOffersData.1.items.0",
            "awsOffersData.1.items.1",
            "awsOffersData.1.items.2",
        ]
    },
    {
        // icon: <BsMicrosoft size={30} />,
        icon: "/images/ai/azure.png",
        isImg: true,
        title: "awsOffersData.2.title",
        items: [
            "awsOffersData.2.items.0",
            "awsOffersData.2.items.1",
            "awsOffersData.2.items.2",
        ]
    },
    {
        icon: "/images/ai/operating.png",
        isImg: true,
        title: "awsOffersData.3.title",
        items: [
            "awsOffersData.3.items2.0",
            "awsOffersData.3.items2.1",
            "awsOffersData.3.items2.2",
        ]
    },
];

function CloudSecurityMain({ t, animations }) {
    return (
        <Section>
            <div className='flex flex-col gap-y-5 p-0'>
                <div className="max-w-[1400px] mx-auto flex rounded-2xl md:p-10 p-2">
                    <motion.div
                        variants={animations.fadeInUp}
                        initial="hidden"
                        animate="visible"
                        className="gradient-secondary flex md:flex-row flex-col rounded-2xl md:px-8 md:py-20 py-5 gap-x-10 shadow-inner relative bg-cover bg-no-repeat bg-center"
                        style={{
                            // backgroundImage: "url('/images/services/bg-img.svg')",
                            backgroundImage: "url('/images/background_lines.svg'), linear-gradient(100.13deg, #497EF7 0.96%, #020060 98.7%)",
                            backgroundRepeat: "no-repeat",
                            backgroundSize: "cover",
                            backgroundPosition: "center"
                        }}
                    >
                        <div className="block text-white md:w-2/3 md:py-8 relative text-center">
                            <Typography
                                variant="h1"
                                className="md:text-5xl text-2xl font-inter font-bold"
                            >
                                {t('whyChooseCloudTitle')}
                            </Typography>
                            <p className="my-5 md:p-0 px-2 md:text-lg text-base">
                                {t('whyChooseCloudDescription')}
                            </p>
                        </div>

                        <div className="rounded-xl relative flex justify-center items-center md:w-1/3 w-2/3 mx-auto">
                            <Image
                                // src="/images/services/cloud-security-img2.jpg"
                                src={secondaryCloudImg}
                                alt="img"
                                className="mx-auto w-full"
                            />
                        </div>
                    </motion.div>
                </div>

                <div className="flex flex-col items-center justify-center bg-[#F2F4FB] pb-4">
                    <OffersSwiper2 offersData={offersDataNew} t={t} />



                    <div className="bg-white py-10 w-full">
                        <Typography
                            variant='h2'
                            className="text-center text-xl font-semibold md:mb-8 mb-4 font-outfit">
                            {t(ofensiveDefService.title)}
                        </Typography>

                        <div className="max-w-[1400px] mx-auto flex md:flex-row flex-col justify-between lg:py-2 gap-y-2 gap-x-4 px-2 md:px-0">
                            {ofensiveDefService.itemsData.map((offer, index) => {
                                const hover = index === 0
                                    ? "hover:bg-blue-50"
                                    : index === 1
                                        ? "hover:bg-red-500 hover:bg-opacity-30"
                                        : index === 2
                                            ? "hover:bg-[#4679ff] hover:bg-opacity-30"
                                            : "hover:bg-purple-500 hover:bg-opacity-30";
                                return (
                                    <div
                                        key={index}
                                        className={`${hover} flex  items-start gap-x-2 p-2 md:py-2 md:px-2 md:w-1/4 rounded-md border `}
                                    >
                                        <div className="flex-shrink-0 py-2 flex items-center justify-center rounded-full my-auto">
                                            {offer.isImg ? (
                                                <Image
                                                    src={offer.icon}
                                                    alt={t(offer.title)}
                                                    width={40}
                                                    height={40}
                                                    className="w-[40px]"
                                                />
                                            ) : (
                                                offer.icon
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-y-1">
                                            <Typography
                                                variant='h3'
                                                className="text-base font-semibold font-outfit text-gray-800">{t(offer.title)}</Typography>
                                            <p className="text-sm text-gray-600">{t(offer.description)}</p>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </div>

                    <div className="max-w-[1400px] mx-auto p-6 w-full">
                        <Typography variant='h2' className="text-center text-xl font-semibold mb-8 font-outfit">
                            {t('whatWeOfferAWSTitle')}
                        </Typography>

                        <div className="flex flex-row md:flex-nowrap gap-6 max-w-7xl mx-auto overflow-x-auto px-4">
                            {awsOffersData.map((item, index) => (
                                <div
                                    key={index}
                                    className="flex-shrink-0 lg:flex-shrink w-fit sm:w-1/2 md:w-1/3 lg:w-1/4 bg-white shadow-md rounded-md p-4 border border-gray-400 text-center"
                                >
                                    <div className="w-full flex items-center justify-center text-4xl text-blue-500 mb-4">
                                        <span className="border-[#F2F4FB] rounded-md p-0">
                                            {item.isImg ? (
                                                <Image
                                                    src={item.icon}
                                                    alt={t(item.title)}
                                                    width={40}
                                                    height={40}
                                                    className="w-[40px] select-none"
                                                />
                                            ) : (
                                                item.icon
                                            )}
                                        </span>
                                    </div>
                                    <Typography variant='h3' className="font-medium text-lg mb-2">{t(item.title)}</Typography>
                                    {item.items && item.items.length > 0 && (
                                        <ul className="text-left text-sm text-gray-500 font-inter flex flex-col gap-y-1">
                                            {item.items.map((subItem, idx) => (
                                                <li className="flex gap-x-2 items-start justify-start" key={idx}>
                                                    <span className="text-xl leading-none text-secondary">
                                                        <BsCheck />
                                                    </span>
                                                    <span>{t(subItem)}</span>
                                                </li>
                                            ))}
                                        </ul>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <div className='max-w-[1400px] mx-auto  bg-transparent relative text-center flex flex-col items-center justify-center gap-y-2 p-0 overflow-hidden w-full'>
                    <div className="flex flex-col md:flex-row justify-between mt-0 md:p-10 p-2">
                        <div className="basis-1/2 p-4 text-left flex flex-col gap-y-6">
                            <Typography
                                variant='h2'
                                className='text-black font-bold font-outfit md:text-3xl  text-2xl capitalize'>
                                {t('whyChooseZtechForCloud')}
                            </Typography>
                            <Typography
                                variant='h6'
                                className='text-black font-medium font-inter md:text-xl text-base capitalize my-4'>
                                {t('whyChooseZtech')}
                            </Typography>
                            <div className="flex flex-col gap-y-4">
                                {checkItems.map((item, index) => (
                                    <div key={index} className='flex gap-x-1'>
                                        <span><BLUECHECKsvg /></span>
                                        <p className=' font-inter'>
                                            <span className='font-semibold text-lg'>
                                                {t(item.strongText)}
                                            </span>
                                            {t(item.text)}
                                        </p>
                                    </div>
                                ))}
                            </div>
                           <CTAButtons t={t} />
                        </div>
                        <div className="w-[45%]">
                            <Image
                                src={cloudSecurityImage}
                                alt="cloud vps"
                                className='p-2 m-auto hidden md:block'
                            />
                        </div>
                    </div>
                </div>

                
            </div>
        </Section >
    )
}

export default CloudSecurityMain