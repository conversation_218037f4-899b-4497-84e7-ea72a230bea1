"use client";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter, useParams } from "next/navigation"; // Add useParams
import { ChevronRight, Home, FileText, Receipt } from "lucide-react";
import paymentService from "../../../services/paymentService";
import { formatDate } from "../../../utils/dateFormatter";
import { getLocalizedContent } from "@/app/helpers/helpers";

export default function PaymentHistoryPage() {
  const t = useTranslations("client");
  const ht = useTranslations("hosting");
  const params = useParams(); // Add this
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("payment_history");
  const router = useRouter();

  const handleTabChange = (value) => {
    setActiveTab(value);
  };

  const getEmptyMessage = () => {
    return activeTab === "refund_history"
      ? t("no_refunds_found")
      : t("no_payments_found");
  };

  // Add new translation keys for empty state
  const getEmptyTitle = () => {
    return activeTab === "refund_history"
      ? t("you_dont_have_any_refunds")
      : t("you_dont_have_any_payments");
  };

  // Add translation keys for the updated UI
  const getBreadcrumbLastItem = () => {
    return activeTab === "refund_history" ? t("refund_history") : t("paid");
  };

  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setLoading(true);
        const res = await paymentService.getPaymentHistory(activeTab);
        if (res.data?.payments) {
          setPayments(res.data.payments);
        } else {
          setPayments([]);
        }
      } catch (error) {
        console.error(
          "Error fetching payments:",
          error?.response?.data || error.message
        );
        setPayments([]);
      } finally {
        setLoading(false);
      }
    };
    fetchPayments();
  }, [activeTab]);

  const data = [
    { label: t("payment_history"), value: "payment_history" },
    { label: t("refund_history"), value: "refund_history" },
  ];

  if (loading) {
    return (
      <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gray-50">
        {/* Header with breadcrumb navigation in the same line as title with separator */}
        <div className="mb-0 flex flex-col md:flex-row md:items-center bg-white p-4 rounded-xl shadow-sm border border-gray-200">
          <Typography
            variant="h1"
            className="text-2xl font-bold text-black mb-2 md:mb-0"
          >
            {activeTab === "refund_history"
              ? t("refund_history")
              : t("payment_history")}
          </Typography>

          <div className="hidden md:flex items-center">
            <div className="mx-3 h-5 w-px bg-gray-300"></div>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Home className="h-4 w-4" />
              <span
                className="cursor-pointer hover:text-blue-600 transition-colors duration-200"
                onClick={() => router.push("/")}
              >
                {t("home")}
              </span>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-600">{t("payment_history")}</span>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-800 font-medium">
                {getBreadcrumbLastItem()}
              </span>
            </div>
          </div>

          {/* Mobile breadcrumb */}
          <div className="flex md:hidden items-center gap-2 text-sm text-gray-600">
            <Home className="h-4 w-4" />
            <span
              className="cursor-pointer hover:text-blue-600 transition-colors duration-200"
              onClick={() => router.push("/")}
            >
              {t("home")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-600">{t("payment_history")}</span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-800 font-medium">
              {getBreadcrumbLastItem()}
            </span>
          </div>
        </div>

        {/* Tabs - Redesigned to match Hostinger UI */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6 overflow-hidden">
          <div className="flex border-b">
            {data.map(({ label, value }) => (
              <button
                key={value}
                onClick={() => handleTabChange(value)}
                className={`px-6 py-4 text-base font-medium relative transition-colors duration-200 ${
                  activeTab === value
                    ? "text-blue-600"
                    : "text-gray-600 hover:text-gray-800"
                }`}
              >
                {label}
                {activeTab === value && (
                  <div className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"></div>
                )}
              </button>
            ))}
          </div>

          {/* Desktop Skeleton */}
          <div className="hidden md:block">
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="grid grid-cols-6 gap-4">
                {[...Array(6)].map((_, index) => (
                  <div
                    key={index}
                    className="h-6 bg-gray-200 rounded animate-pulse"
                  ></div>
                ))}
              </div>
            </div>
            <div className="p-4">
              {[...Array(5)].map((_, rowIndex) => (
                <div
                  key={rowIndex}
                  className="grid grid-cols-6 gap-4 mb-4 pb-4 border-b border-gray-100 last:border-b-0 last:mb-0 last:pb-0"
                >
                  {[...Array(6)].map((_, colIndex) => (
                    <div
                      key={colIndex}
                      className="h-4 bg-gray-100 rounded animate-pulse"
                    ></div>
                  ))}
                </div>
              ))}
            </div>
          </div>

          {/* Mobile Skeleton */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 md:hidden">
            {[...Array(4)].map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-4"
              >
                <div className="space-y-4">
                  <div className="h-4 bg-gray-100 rounded animate-pulse w-3/4"></div>
                  <div className="space-y-2">
                    {[...Array(4)].map((_, i) => (
                      <div
                        key={i}
                        className="h-4 bg-gray-100 rounded animate-pulse"
                      ></div>
                    ))}
                  </div>
                  <div className="flex justify-center">
                    <div className="h-8 bg-gray-100 rounded animate-pulse w-24"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gray-50">
      {/* Header with breadcrumb navigation in the same line as title with separator */}
      <div className="mb-0 flex flex-col md:flex-row md:items-center bg-white p-4 rounded-t-xl shadow-sm border border-gray-200">
        <Typography
          variant="h1"
          className="text-2xl font-bold text-black mb-2 md:mb-0"
        >
          {activeTab === "refund_history"
            ? t("refund_history")
            : t("payment_history")}
        </Typography>

        <div className="hidden md:flex items-center">
          <div className="mx-3 h-5 w-px bg-gray-300"></div>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Home className="h-4 w-4" />
            <span
              className="cursor-pointer hover:text-blue-600 transition-colors duration-200"
              onClick={() => router.push("/")}
            >
              {t("home")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-600">{t("payment_history")}</span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-800 font-medium">
              {getBreadcrumbLastItem()}
            </span>
          </div>
        </div>

        {/* Mobile breadcrumb */}
        <div className="flex md:hidden items-center gap-2 text-sm text-gray-600">
          <Home className="h-4 w-4" />
          <span
            className="cursor-pointer hover:text-blue-600 transition-colors duration-200"
            onClick={() => router.push("/")}
          >
            {t("home")}
          </span>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-600">{t("payment_history")}</span>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-800 font-medium">
            {getBreadcrumbLastItem()}
          </span>
        </div>
      </div>

      {/* Tabs - Redesigned to match Hostinger UI */}
      <div className="bg-white rounded-b-xl shadow-sm border border-gray-200 mb-6 overflow-hidden">
        <div className="flex border-b">
          {data.map(({ label, value }) => (
            <button
              key={value}
              onClick={() => handleTabChange(value)}
              className={`px-6 py-4 text-base font-medium relative transition-colors duration-200 ${
                activeTab === value
                  ? "text-blue-600"
                  : "text-gray-600 hover:text-gray-800"
              }`}
            >
              {label}
              {activeTab === value && (
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-600"></div>
              )}
            </button>
          ))}
        </div>

        {payments.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20 px-4">
            <div className="w-24 h-24 mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <Receipt className="h-12 w-12 text-gray-400" />
            </div>
            <Typography className="text-xl font-semibold text-gray-800 mb-4">
              {getEmptyTitle()}
            </Typography>
            <div className="flex items-center mt-2 bg-blue-50 p-4 rounded-lg border border-blue-100">
              <Typography className="text-gray-700">
                {t("learn_more_about_our")}
              </Typography>
              <Button
                variant="text"
                color="blue"
                className="p-1 ml-1 flex items-center gap-1"
                onClick={() => router.push("/hosting")}
              >
                {t("services")}
                <FileText className="w-4 h-4 text-blue-600" />
              </Button>
            </div>
          </div>
        ) : (
          <div>
            {/* Table View (Desktop and Larger Screens) */}
            <div className="hidden md:block">
              <table className="w-full text-left">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="p-4 font-semibold text-gray-800">
                      {activeTab === "refund_history"
                        ? t("refund_id")
                        : t("payment_id")}
                    </th>
                    <th className="p-4 font-semibold text-gray-800">{t("invoice_id")}</th>
                    <th className="p-4 font-semibold text-gray-800">{t("service")}</th>
                    <th className="p-4 font-semibold text-gray-800">{t("paid_at")}</th>
                    <th className="p-4 font-semibold text-gray-800 text-center">{t("amount")}</th>
                    <th className="p-4 font-semibold text-gray-800 sr-only">{t("details")}</th>
                  </tr>
                </thead>
                <tbody>
                  {payments.map((payment, index) => (
                    <tr
                      key={payment.paymentId}
                      className={`bg-white border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                        index === payments.length - 1 ? "border-b-0" : ""
                      }`}
                      onClick={() =>
                        router.push(`/client/payments/${payment.paymentId}`)
                      }
                    >
                      <td className="p-4 text-gray-900 font-medium">
                        {payment.paymentId}
                      </td>
                      <td className="p-4 text-gray-700">{payment.invoiceId}</td>
                      <td className="p-4 text-gray-700">
                        {payment.services.map((s, index) => (
                          <span key={index}>
                            {index > 0 && (
                              <>
                                {", "}
                                <br />
                              </>
                            )}
                            {getLocalizedContent(
                              s,
                              "name",
                              params.locale || "en"
                            )}
                          </span>
                        ))}
                      </td>
                      <td className="p-4 text-gray-700">
                        {formatDate(payment.paymentDate)}
                      </td>
                      <td className="p-4 text-gray-900 font-medium">
                        {payment.totalPrice.toFixed(2)} {payment.currency}
                      </td>
                      <td className="p-4 text-right">
                        <Button
                          variant="text"
                          color="blue"
                          className="flex items-center gap-2 hover:bg-blue-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(
                              `/client/payments/${payment.paymentId}`
                            );
                          }}
                        >
                          <span className="sr-only">{t("details")}</span>
                          <ChevronRight className="h-5 w-5" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Card View (Mobile and Tablet Screens) */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 md:hidden">
              {payments.map((payment) => (
                <Card
                  key={payment.paymentId}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
                  onClick={() =>
                    router.push(`/client/payments/${payment.paymentId}`)
                  }
                >
                  <CardBody className="p-4">
                    <div className="space-y-4">
                      <div className="flex flex-col sm:flex-row justify-between items-start">
                        <div>
                          <Typography className="text-sm font-semibold text-gray-600">
                            {activeTab === "refund_history"
                              ? t("refund_id")
                              : t("payment_id")}
                          </Typography>
                          <Typography className="text-base text-gray-900 font-medium">
                            {payment.paymentId}
                          </Typography>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div>
                          <Typography className="text-sm font-semibold text-gray-600">
                            {t("invoice_id")}
                          </Typography>
                          <Typography className="text-base text-gray-700">
                            {payment.invoiceId}
                          </Typography>
                        </div>
                        <div>
                          <Typography className="text-sm font-semibold text-gray-600">
                            {t("paid_at")}
                          </Typography>
                          <Typography className="text-base text-gray-700">
                            {formatDate(payment.paymentDate)}
                          </Typography>
                        </div>
                        <div className="col-span-1 sm:col-span-2">
                          <Typography className="text-sm font-semibold text-gray-600">
                            {t("service")}
                          </Typography>
                          <Typography className="text-base text-gray-700">
                            {payment.services
                              .map((s) =>
                                getLocalizedContent(
                                  s,
                                  "name",
                                  params.locale || "en"
                                )
                              )
                              .join(", ")}
                          </Typography>
                        </div>
                        <div className="col-span-1 sm:col-span-2">
                          <Typography className="text-sm font-semibold text-gray-600">
                            {t("amount")}
                          </Typography>
                          <Typography className="text-base text-gray-900 font-semibold">
                            {payment.totalPrice.toFixed(2)} {payment.currency}
                          </Typography>
                        </div>
                      </div>
                      <div className="flex justify-center">
                        <Button
                          variant="outlined"
                          color="blue"
                          className="text-blue-600 bg-white border-blue-200 hover:bg-blue-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(
                              `/client/payments/${payment.paymentId}`
                            );
                          }}
                        >
                          {t("details")}
                        </Button>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
