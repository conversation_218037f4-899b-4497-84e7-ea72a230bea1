import { Lock<PERSON>con, BadgeCheckIcon, ClockIcon } from "lucide-react";
import { useTranslations } from "next-intl";

const SSLFeatures = () => {
  const t = useTranslations("SSL2");
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
      {[
        {
          icon: <LockIcon className="h-6 w-6 text-green-600 mb-4" />,
          title: t("features.encryption.title"),
          description: t("features.encryption.description"),
        },
        {
          icon: <BadgeCheckIcon className="h-6 w-6 text-green-600 mb-4" />,
          title: t("features.seal.title"),
          description: t("features.seal.description"),
        },
        {
          icon: <ClockIcon className="h-6 w-6 text-green-600 mb-4" />,
          title: t("features.issuance.title"),
          description: t("features.issuance.description"),
        },
      ].map((feature) => (
        <div
          key={feature.title}
          className="bg-gray-50 rounded-xl p-6 text-center"
        >
          <div className="flex justify-center">{feature.icon}</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {feature.title}
          </h3>
          <p className="text-gray-600">{feature.description}</p>
        </div>
      ))}
    </div>
  );
};

export default SSLFeatures;
