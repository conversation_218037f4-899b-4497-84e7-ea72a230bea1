/**
 * <PERSON><PERSON><PERSON> to test VPS APIs
 * Tests the dynamic data endpoints for OS images and regions
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5002/api';

// Test function
const testVPSAPIs = async () => {
  console.log('🧪 Testing VPS APIs...\n');

  try {
    // Test VPS Plans
    console.log('1. Testing VPS Plans...');
    const plansResponse = await axios.get(`${BASE_URL}/vps/plans`);
    console.log(`✅ VPS Plans: ${plansResponse.data.data?.length || 0} plans found`);
    if (plansResponse.data.data?.length > 0) {
      console.log(`   First plan: ${plansResponse.data.data[0].name} (${plansResponse.data.data[0].id})`);
    }
    console.log('');

    // Test OS Images
    console.log('2. Testing OS Images...');
    const imagesResponse = await axios.get(`${BASE_URL}/vps/images?provider=contabo`);
    console.log(`✅ OS Images: ${imagesResponse.data.data?.length || 0} images found`);
    if (imagesResponse.data.data?.length > 0) {
      console.log(`   First image: ${imagesResponse.data.data[0].name} (${imagesResponse.data.data[0].imageId})`);
      console.log(`   OS Types available: ${[...new Set(imagesResponse.data.data.map(img => img.osType))].join(', ')}`);
    }
    console.log('');

    // Test Regions
    console.log('3. Testing Regions...');
    const regionsResponse = await axios.get(`${BASE_URL}/vps/regions?provider=contabo`);
    console.log(`✅ Regions: ${regionsResponse.data.data?.length || 0} regions found`);
    if (regionsResponse.data.data?.length > 0) {
      console.log(`   Available regions:`);
      regionsResponse.data.data.forEach(region => {
        console.log(`   - ${region.name} (${region.id}) - ${region.city}, ${region.country}`);
      });
    }
    console.log('');

    // Test Providers
    console.log('4. Testing Providers...');
    const providersResponse = await axios.get(`${BASE_URL}/vps/providers`);
    console.log(`✅ Providers: ${providersResponse.data.data?.length || 0} providers found`);
    if (providersResponse.data.data?.length > 0) {
      console.log(`   Available providers: ${providersResponse.data.data.join(', ')}`);
    }
    console.log('');

    // Test VPS Packages from database
    console.log('5. Testing VPS Packages from Database...');
    const packagesResponse = await axios.get(`${BASE_URL}/packages/get-packages?brandName=VPS%20Hosting`);
    console.log(`✅ VPS Packages: ${packagesResponse.data?.length || 0} packages found`);
    if (packagesResponse.data?.length > 0) {
      console.log(`   Database packages:`);
      packagesResponse.data.forEach(pkg => {
        console.log(`   - ${pkg.name} (${pkg._id}) - ${pkg.price} MAD`);
        if (pkg.vpsConfig) {
          console.log(`     Provider: ${pkg.vpsConfig.provider}, Product ID: ${pkg.vpsConfig.providerProductId}`);
        } else {
          console.log(`     ⚠️  No VPS configuration found`);
        }
      });
    }
    console.log('');

    console.log('🎉 All API tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - VPS Plans: ${plansResponse.data.data?.length || 0}`);
    console.log(`   - OS Images: ${imagesResponse.data.data?.length || 0}`);
    console.log(`   - Regions: ${regionsResponse.data.data?.length || 0}`);
    console.log(`   - Providers: ${providersResponse.data.data?.length || 0}`);
    console.log(`   - Database Packages: ${packagesResponse.data?.length || 0}`);

  } catch (error) {
    console.error('❌ Error testing APIs:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    console.log('\n💡 Make sure the backend server is running on port 5000');
  }
};

// Run the test
if (require.main === module) {
  testVPSAPIs();
}

module.exports = { testVPSAPIs };
