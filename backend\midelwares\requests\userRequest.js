const Joi = require("joi");
const { getDefaultOptions } = require("./sharedDefaultOptions");
const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;

exports.setConfigAccountValidatorOLD = (req, res, next) => {
  const { password, firstName, lastName } = req.body;
  console.log("setConfigAccountValidator: ", req.body.isOAuth);
  const configAccountSchema = Joi.object({
    // identifiant: Joi.string().min(5).required().label(req.t('attributes.identifier')),
    password: Joi.string()
      .min(8)
      .required()
      .label(req.t("attributes.password")),
    firstName: Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.first_name")),
    lastName: Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.last_name")),
  });

  const result = configAccountSchema.validate(
    { password, firstName, lastName },
    { abortEarly: false, ...getDefaultOptions(req) }
  );
  if (result.error) {
    const errors = result.error.details;
    const errorArray = errors.map((error) => {
      return { key: error.context.key, msg: error.message };
    });
    const errMsg = "Validation failed.";
    return res.status(400).json({ message: errMsg, errors: errorArray });
  }
  next();
};

exports.setConfigAccountValidator = (req, res, next) => {
  const { password, firstName, lastName, isOAuth } = req.body;
  console.log("setConfigAccountValidator: ", isOAuth);

  const configAccountSchema = Joi.object({
    password: Joi.when("$isOAuth", {
      is: false, // If isOAuth is false, require password validation
      then: Joi.string().min(8).required().label(req.t("attributes.password")),
      otherwise: Joi.forbidden(), // If isOAuth is true, ignore password
    }),
    firstName: Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.first_name")),
    lastName: Joi.string()
      .min(3)
      .required()
      .label(req.t("attributes.last_name")),
    isOAuth: Joi.boolean().required(),
  });

  // ✅ Conditionally remove password if isOAuth is true
  const validationData = { isOAuth, lastName, firstName };
  if (!isOAuth) {
    validationData.password = password;
  }

  const result = configAccountSchema.validate(validationData, {
    abortEarly: false,
    context: { isOAuth: validationData.isOAuth }, // Add this line
    ...getDefaultOptions(req),
  });

  if (result.error) {
    const errors = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));
    return res.status(400).json({ message: "Validation failed.", errors });
  }

  next();
};

exports.changePasswordValidator = (req, res, next) => {
  const { password, newPassword, repeatNewPassword, isOAuth } = req.body;
  console.log(
    "changePasswordValidator....:",
    password,
    newPassword,
    repeatNewPassword,
    isOAuth
  );

  // Base schema for fields that are always required
  const baseSchema = {
    id: Joi.string().required(),
    newPassword: Joi.string()
      .min(8)
      .required()
      .label(req.t("attributes.new_password")),
    repeatNewPassword: Joi.any()
      .valid(Joi.ref("newPassword"))
      .required()
      .label(req.t("attributes.password_confirmation")),
    isOAuth: Joi.boolean().required(),
  };

  // Create schema with conditional password requirement
  const configAccountSchema = Joi.object({
    ...baseSchema,
    password: Joi.when(Joi.ref("isOAuth"), {
      is: false, // If isOAuth is false, require password
      then: Joi.string().min(8).required().label(req.t("attributes.password")),
      otherwise: Joi.string().allow("").optional(), // For OAuth users setting password first time
    }),
  });

  // ✅ Conditionally require password validation based on isOAuth
  const validationData = {
    id: req.body.id,
    newPassword,
    repeatNewPassword,
    isOAuth,
  };

  if (!isOAuth) {
    validationData.password = password;
  }

  const result = configAccountSchema.validate(validationData, {
    abortEarly: false,
    ...getDefaultOptions(req),
  });

  if (result.error) {
    const errors = result.error.details;

    const errorArray = errors.map((error) => {
      if (error.context.key === "repeatNewPassword") {
        error.message = error.message.replace(
          "ref:newPassword",
          req.t("attributes.new_password").toLowerCase()
        );
      }
      return { key: error.context.key, msg: error.message };
    });

    const errMsg = "Validation failed.";
    return res.status(400).json({ message: errMsg, errors: errorArray });
  }

  next();
};

exports.changeEmailValidator = async (req, res, next) => {
  const BACKEND_LANG = req.cookies.BACKEND_LANG || "en"; // Provide a default locale if missing
  req.i18n.changeLanguage(BACKEND_LANG);

  const { password, newEmail, isOAuth } = req.body;
  console.log("changeEmailValidator....:", password, newEmail, isOAuth);

  const changeEmailSchema = Joi.object({
    password: Joi.when(Joi.ref("isOAuth"), {
      is: false,
      then: Joi.string().min(8).required().label(req.t("attributes.password")),
      otherwise: Joi.forbidden(),
    }),
    newEmail: Joi.string()
      .email()
      .required()
      .label(req.t("attributes.new_email")),
    isOAuth: Joi.boolean().required(),
  });

  // const result = changeEmailSchema.validate(
  //     { password, newEmail, isOAuth },
  //     {
  //         abortEarly: false,
  //         context: { isOAuth },
  //         ...getDefaultOptions(req),
  //     }
  // );

  // ✅ Conditionally remove password if isOAuth is true
  const validationData = { newEmail, isOAuth };
  if (!isOAuth) {
    validationData.password = password;
  }

  const result = changeEmailSchema.validate(validationData, {
    abortEarly: false,
    ...getDefaultOptions(req),
  });

  console.log("Validation result: ", result);
  if (result.error) {
    const errors = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));

    return res.status(400).json({
      message: `Validation failed. ${errors.length} error(s) found.`,
      errors,
    });
  }

  next();
};

exports.billingInfoValidator = async (req, res, next) => {
  console.log("req.body: ", req.body);

  // Base schema for common fields
  const baseSchema = {
    isCompany: Joi.boolean().default(false).label(req.t("attributes.is_company")),
    BillToName: Joi.string()
      .regex(/^\S/)
      .min(5)
      .required()
      .label(req.t("attributes.full_name")),
    email: Joi.string()
      .email()
      .regex(emailRegex)
      .required()
      .label(req.t("attributes.email"))
      .messages({
        "string.pattern.base": "Please enter a valid email address.",
        "string.empty": "Email address is required.",
        "any.required": "Email address is required.",
      }),
    phone: Joi.string()
      .regex(/^\+?\d{10,}$/)
      .required()
      .label(req.t("attributes.phone"))
      .messages({
        "string.pattern.base":
          "Phone number must contain only digits and can start with '+'. It must have at least 10 digits.",
        "string.empty": "Phone number is required.",
        "any.required": "Phone number is required.",
      }),
    country: Joi.string()
      .regex(/^\S/)
      .min(3)
      .required()
      .label(req.t("attributes.country")),
    address: Joi.string()
      .regex(/^\S/)
      .min(6)
      .required()
      .label(req.t("attributes.address")),
    // Allow userId to be passed in the request
    userId: Joi.string().optional(),
  };

  // Company-specific schema
  const companySchema = {
    companyICE: Joi.when('isCompany', {
      is: true,
      then: Joi.string().pattern(/^\d{15}$/).required().label(req.t("attributes.company_ice"))
        .messages({
          'string.pattern.base': req.t("attributes.company_ice") + ' ' + req.t("must_be_15_digits")
        }),
      otherwise: Joi.optional()
    }),
    companyAddress: Joi.when('isCompany', {
      is: true,
      then: Joi.string().min(6).required().label(req.t("attributes.company_address")),
      otherwise: Joi.optional()
    }),
    companyPhone: Joi.when('isCompany', {
      is: true,
      then: Joi.string().regex(/^\+?\d{10,}$/).required().label(req.t("attributes.company_phone")),
      otherwise: Joi.optional()
    }),
    companyEmail: Joi.when('isCompany', {
      is: true,
      then: Joi.string().email().required().label(req.t("attributes.company_email")),
      otherwise: Joi.optional()
    }),
    companyLogo: Joi.optional().label(req.t("attributes.company_logo")),
  };

  // Combine base and company schemas
  const registerSchema = Joi.object({
    ...baseSchema,
    ...companySchema
  });

  const result = registerSchema.validate(req.body, {
    abortEarly: false,
    ...getDefaultOptions(req),
  });

  if (result.error) {
    // Extract error details and create user-friendly messages
    const errorArray = result.error.details.map((error) => ({
      key: error.context.key,
      msg: error.message,
    }));

    return res.status(400).json({
      message: "Validation failed.",
      errors: errorArray,
    });
  }
  next();
};
