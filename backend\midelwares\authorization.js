const expressJWT = require("express-jwt");
const jwt = require("jsonwebtoken");
const User = require("../models/User");
const AccountState = require("../constants/enums/account-state");
const { ObjectId } = require("bson");
const { isProd } = require("../constants/constant");
const { getCookieConfig } = require("../helpers/helpers");

// Middleware to require user to sign in (check if JWT token exists)
exports.requireSignIn = expressJWT({
  secret: process.env.JWT_SECRET,
  algorithms: ["HS256"],
  userProperty: "auth",
});

// Middleware to fetch user from token in cookies
exports.fetchUserMiddleware = async (req, res, next) => {
  const token = req.cookies.token; // Retrieve the token from the cookie
  console.log("logging fetchUserMiddleware coo : ", req.cookies);

  if (!token) {
    return res.status(201).send("Token not found, please login.");
  }

  try {
    // Verify the token and attach decoded user to request object
    const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
    req.user = { _id: decodedToken.id };
    console.log("Decoded token: req.user: ", req.user);
    next();
  } catch (error) {
    console.error("Token verification failed:", error);
    return res
      .status(403)
      .send("Invalid or expired token, please login again.");
  }
};

// Middleware to check if the current user can modify the requested resource
exports.canHandleIt = async (req, res, next) => {
  console.log("req next:", req.cookies.token);
  const token = req.cookies.token; // Retrieve the token from the cookie

  if (!token) {
    return res.status(401).send("Token not found, please login.");
  }

  // Verify the token and attach decoded user to request object
  const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
  const currentUser = decodedToken.id;
  const userId = req.body.id;
  console.log("userId:", userId);
  console.log("canHandleIt?? :", currentUser === userId);
  if (currentUser === userId) {
    return next(); // Proceed if current user is the same as user to modify
  }
  return res
    .status(403)
    .send("You don't have permission to perform this action.");
};

// Middleware to ensure that the authenticated user is verified
exports.authUserIsVerified = async (req, res, next) => {
  try {
    // Retrieve user from database by ID
    const userFound = await User.findOne({ _id: req.auth.id });

    if (!userFound) {
      return res.status(404).send("User not found.");
    }

    // Check if the user's state is VERIFIED
    if (userFound.state === AccountState.VERIFIED) {
      return next(); // Proceed if user is verified
    }

    return res.status(403).send("User is not verified.");
  } catch (error) {
    console.error("Error fetching user:", error);
    return res.status(500).send("Internal Server Error");
  }
};

exports.cartUserMiddeware = async (req, res, next) => {
  const token = req.cookies.token; // Retrieve the token from the cookie

  if (!token) {
    console.log("in cartUserMiddeware, no token: ");
    //generate a look a like mongo id and assign it to  req.user.id and add to it that user status is guest,authenticated-false
    req.user = {
      id: "must be usnuque roandom look like mongo id",
      status: "guest",
      authenticated: false,
    };
    return next(); // Proceed if user is logged in (guest)
  }

  try {
    console.log("in cartUserMiddeware: user is Logged in");
    // Verify the token and attach decoded user to request object
    const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decodedToken;
    console.log("Decoded token:", decodedToken);
    next();
  } catch (error) {
    console.error("Token verification failed:", error);
    return res
      .status(403)
      .send("Invalid or expired token, please login again.");
  }
};

exports.checkUserOrRefreshToken = async (req, res, next) => {
  const { token, refreshToken, guest_token } = req.cookies;
  console.log("Inside checkUserOrRefreshToken ...", guest_token);

  // If access token exists, verify it
  if (token) {
    try {
      if (!req.user) {
        const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
        req.user = { _id: decodedToken.id, authenticated: true }; // Attach user info to req
        // console.log("Access token verified:", decodedToken);
      }
      return next();
    } catch (error) {
      if (error.name === "TokenExpiredError") {
        console.log("Access token expired. Checking refresh token...");
      } else {
        console.error("Invalid access token:", error);
      }
      // Proceed to handle refresh token
    }
  }

  // Handle refresh token or guest assignment
  handleRefreshTokenOrGuest(req, res, next, refreshToken, guest_token);
};

// Helper function to handle refresh token or assign guest status
const handleRefreshTokenOrGuest = (
  req,
  res,
  next,
  refreshToken,
  guest_token
) => {
  // If refresh token is available, try to verify it
  if (refreshToken) {
    try {
      const decodedRefreshToken = jwt.verify(
        refreshToken,
        process.env.REFRESH_TOKEN_SECRET
      );
      console.log("Refresh token verified:", decodedRefreshToken);

      // Generate a new access token
      const newToken = jwt.sign(
        { id: decodedRefreshToken.id, email: decodedRefreshToken.email },
        process.env.JWT_SECRET,
        { expiresIn: "1h" } // New token expiration time
      );

      // Attach the new access token to cookies
      res.cookie("token", newToken, {
        ...getCookieConfig(isProd),
        expires: new Date(Date.now() + 3600000), // 1 hour
      });

      // Attach user info to the request if it's not already set
      if (!req.user) {
        req.user = { _id: decodedRefreshToken.id, authenticated: true };
      }
      console.log("New access token issued using refresh token.");
      return next();
    } catch (error) {
      console.error("Refresh token verification failed:", error);
    }
  }

  // Handle guest user assignment if refresh token is not valid
  handleGuestUser(req, res, next, guest_token);
};

// Helper function to assign a guest user if no valid token exists
const handleGuestUser = (req, res, next, guest_token) => {
  if (!guest_token) {
    console.log(
      "No guest token found. Redirecting to generate a guest token..."
    );
    // Trigger page refresh or redirect to generate guest token
    res.status(401).json({
      message: "Guest token missing. Please refresh the page to continue.",
    });
    return;
  }

  try {
    const decodedGuestToken = jwt.verify(guest_token, process.env.JWT_SECRET);
    console.log("Guest token verified:", decodedGuestToken);

    req.user = {
      _id: decodedGuestToken.id,
      status: "Guest",
      authenticated: false,
    };

    return next(); // Proceed with guest user
  } catch (error) {
    console.error("Invalid guest token:", error);
    res.status(401).json({
      message: "Invalid guest token. Please refresh the page to continue.",
    });
  }
};

exports.adminMiddleware = async (req, res, next) => {
  const token = req.cookies.token; // Retrieve the token from the cookie

  if (!token) {
    return res.status(401).send("Token not found, please login.");
  }

  // Verify the token and attach decoded user to request object
  const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
  const currentUser = decodedToken.id;
  console.log("currentUser:", currentUser);

  try {
    // Retrieve user from database by ID
    const userFound = await User.findOne({ _id: currentUser });

    if (!userFound) {
      return res.status(404).send("User not found.");
    }

    // Set req.user for downstream use (e.g., logging)
    req.user = {
      _id: userFound._id,
      email: userFound.email,
      role: userFound.role,
      firstName: userFound.firstName,
      lastName: userFound.lastName
    };

    // Check if the user's role is ADMIN
    if (userFound.role === "ADMIN") {
      return next(); // Proceed if user is an admin
    }

    return res.status(403).send("User is not an admin.");
  } catch (error) {
    console.error("Error fetching user:", error);
    return res.status(500).send("Internal Server Error");
  }
};
