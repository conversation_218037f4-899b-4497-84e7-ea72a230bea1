import apiService from "../lib/apiService";

export const brandService = {
    getAllBrands: () => apiService.get('/brand/get-brands', { withCredentials: true }),//not used yet
    // getAllSpecs: () => apiService.get("/admin/specs", { withCredentials: true }),
    // createBrand: (data) => apiService.post('/brand/create-brand', data, { withCredentials: true }),
    // getBrandById: (brandId) => apiService.get(`/brand/get-brand/${brandId}`, { withCredentials: true }),
    // updateBrand: (brandId, data) => apiService.put(`/brand/update-brand/${brandId}`, data, { withCredentials: true }),
    // deleteBrand: (brandId) => apiService.delete(`/brand/delete-brand/${brandId}`, { withCredentials: true }),
};