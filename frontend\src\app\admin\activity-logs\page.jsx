"use client";
import React, { useEffect, useState, useCallback } from "react";
import {
  Card,
  CardBody,
  Typography,
  Spinner,
  Select,
  Option,
  Button,
  Tooltip,
  IconButton,
  Chip,
  Input,
  Badge,
  Accordion,
  AccordionHeader,
  AccordionBody,
} from "@material-tailwind/react";
import {
  Activity,
  Calendar,
  FileText,
  Package,
  ShoppingCart,
  Users,
  Settings,
  Database,
  Info,
  RefreshCcw,
} from "lucide-react";
import { adminService } from "../../services/adminService";
import { toast } from "react-toastify";
import {
  Eye,
  Filter,
  Search,
  AlertCircle,
  Clock,
  User,
  X,
  Tag,
  Layers,
  ChevronDown,
} from "lucide-react";
import {
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
} from "@material-tailwind/react";
import { formatLogForDisplay } from "../../utils/logFormatter";
import useDebounce from "../../hook/useDebounce";
import * as XLSX from "xlsx";
import { FaFileExcel } from "react-icons/fa";

export default function AdminActivityLogs() {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const ITEMS_PER_PAGE_OPTIONS = [5, 10, 25, 50, 100];

  const [showFilters, setShowFilters] = useState(false);
  const [adminFilter, setAdminFilter] = useState("");
  const [actionFilter, setActionFilter] = useState("");
  const [targetModelFilter, setTargetModelFilter] = useState("");
  const [dateRange, setDateRange] = useState({ startDate: "", endDate: "" });
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [availableAdmins, setAvailableAdmins] = useState([]);
  const [availableActions, setAvailableActions] = useState([]);
  const [availableTargetModels, setAvailableTargetModels] = useState([]);

  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedLogDetails, setSelectedLogDetails] = useState(null);
  const [selectedLogTitle, setSelectedLogTitle] = useState("");

  const fetchLogs = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        page: currentPage,
        limit: itemsPerPage,
      };

      if (adminFilter) params.adminId = adminFilter;
      if (actionFilter) params.action = actionFilter;
      if (targetModelFilter) params.targetModel = targetModelFilter;
      if (dateRange.startDate) params.startDate = dateRange.startDate;
      if (dateRange.endDate) params.endDate = dateRange.endDate;
      if (debouncedSearchQuery) params.search = debouncedSearchQuery;

      console.log("Fetching logs with params:", params);

      const response = await adminService.getAdminActivityLogs(params);
      console.log("Response from server:", response.data);
      setLogs(response.data.logs || []);
      setTotalPages(response.data.pagination?.totalPages || 1);
      setTotalItems(response.data.pagination?.totalItems || 0);
    } catch (err) {
      console.error("Error fetching logs:", err);
      toast.error("Failed to fetch admin activity logs");
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    itemsPerPage,
    adminFilter,
    actionFilter,
    targetModelFilter,
    dateRange,
    debouncedSearchQuery,
  ]);

  const fetchFilters = useCallback(async () => {
    try {
      const response = await adminService.getActivityLogFilters();
      console.log(response);
      setAvailableAdmins(response.data.admins || []);
      setAvailableActions(response.data.actions || []);
      setAvailableTargetModels(response.data.targetModels || []);
      console.log("Filters fetched:", response.data);
    } catch (err) {
      console.error("Failed to fetch filters:", err);
    }
  }, []);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  useEffect(() => {
    fetchFilters();
  }, [fetchFilters]);

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const clearFilters = () => {
    setAdminFilter("");
    setActionFilter("");
    setTargetModelFilter("");
    setDateRange({ startDate: "", endDate: "" });
    setSearchQuery("");
    setCurrentPage(1);
  };

  const [openAccordion, setOpenAccordion] = useState(1);

  const handleAccordionOpen = (value) => {
    setOpenAccordion(openAccordion === value ? 0 : value);
  };

  const handleShowDetails = (
    details,
    title = "Log Details",
    fullLog = null
  ) => {
    if (fullLog) {
      setSelectedLogDetails(formatLogForDisplay(fullLog));
    } else {
      setSelectedLogDetails(details);
    }
    setSelectedLogTitle(title);
    setShowDetailsModal(true);
    setOpenAccordion(1);
  };

  const handleCloseModal = () => {
    setShowDetailsModal(false);
  };

  const getActionColor = (action) => {
    if (action.includes("CREATE")) return "green";
    if (action.includes("UPDATE") || action.includes("MODIFY")) return "amber";
    if (action.includes("DELETE")) return "red";
    if (action.includes("VIEW")) return "blue";
    if (action.includes("LOGIN") || action.includes("LOGOUT")) return "purple";
    if (action.includes("EXPORT") || action.includes("IMPORT")) return "cyan";
    if (action.includes("CHANGE_STATUS")) return "orange";
    return "blue-gray";
  };

  const getTargetModelIcon = (model) => {
    switch (model?.toUpperCase()) {
      case "USER":
        return <User className="h-4 w-4 text-blue-500" />;
      case "PACKAGE":
        return <Package className="h-4 w-4 text-purple-500" />;
      case "ORDER":
      case "SUBORDER":
        return <ShoppingCart className="h-4 w-4 text-green-500" />;
      case "DASHBOARD":
        return <Activity className="h-4 w-4 text-blue-600" />;
      case "SETTINGS":
        return <Settings className="h-4 w-4 text-gray-600" />;
      case "AUTH":
        return <User className="h-4 w-4 text-indigo-500" />;
      case "TICKET":
      case "SUPPORT":
        return <FileText className="h-4 w-4 text-amber-500" />;
      default:
        return <Database className="h-4 w-4 text-blue-gray-500" />;
    }
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const getActionDescription = (action) => {
    return action.replace(/_/g, " ").toLowerCase();
  };

  // Refresh logs
const handleRefresh = () => {
  fetchLogs();
  toast.success("Logs refreshed!");
};

// Download logs as Excel
const handleDownloadExcel = () => {
  if (!logs.length) {
    toast.error("No logs to export!");
    return;
  }
  // Prepare user-friendly data for Excel
  const data = logs.map((log) => ({
    "Date & Time": formatDate(log.timestamp),
    "Admin": log.admin
      ? `${log.admin.firstName || ""} ${log.admin.lastName || ""}`.trim()
      : "Unknown",
    "Admin Email": log.admin?.email || "",
    "Action": log.action,
    "Target Model": log.targetModel,
    "Target": typeof log.target === "object"
      ? JSON.stringify(log.target)
      : log.target,
    "Details": typeof log.details === "object"
      ? JSON.stringify(log.details)
      : log.details,
    // Add more fields as needed, but avoid IDs and technical fields
  }));

  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Activity Logs");
  XLSX.writeFile(workbook, "activity_logs.xlsx");
  toast.success("Excel file downloaded!");
};

  return (
    <div className="container mx-auto px-4 py-6 min-h-screen bg-gray-50">
      <div className="flex items-center gap-3 mb-6">
        <Activity className="h-8 w-8 text-blue-600" />
        <Typography variant="h2" className="text-gray-900">
          Admin Activity Logs
        </Typography>
        {/* Refresh and Download Buttons */}
        <Button
          variant="outlined"
          color="blue"
          size="sm"
          className="ml-auto flex items-center gap-2"
          onClick={handleRefresh}
          disabled={loading}
        >
          <RefreshCcw className="h-4 w-4" />
          Refresh
        </Button>
        <Button
          variant="gradient"
          color="green"
          size="sm"
          className="flex items-center gap-2"
          onClick={handleDownloadExcel}
          disabled={loading || !logs.length}
        >
          <FaFileExcel className="h-4 w-4" />
          Download Excel
        </Button>
      </div>

      <Card className="shadow-lg border border-gray-100 rounded-xl overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="relative w-full md:w-96">
              <Input
                type="text"
                label="Search logs"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setCurrentPage(1);
                }}
                className="pr-10"
                icon={<Search className="h-4 w-4" />}
              />
            </div>

            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outlined"
                    color="blue-gray"
                    className="flex items-center gap-2"
                    onClick={() => setShowFilters(!showFilters)}
                    disabled={loading}
                  >
                    <Filter className="h-4 w-4" />
                    Filters
                    {(adminFilter ||
                      actionFilter ||
                      targetModelFilter ||
                      dateRange.startDate ||
                      dateRange.endDate) && (
                      <Chip
                        value={
                          (adminFilter ? 1 : 0) +
                          (actionFilter ? 1 : 0) +
                          (targetModelFilter ? 1 : 0) +
                          (dateRange.startDate || dateRange.endDate ? 1 : 0)
                        }
                        size="sm"
                        color="blue"
                        className="h-5 w-5 p-0 min-w-[20px]"
                      />
                    )}
                  </Button>
                  
                  {(adminFilter ||
                    actionFilter ||
                    targetModelFilter ||
                    dateRange.startDate ||
                    dateRange.endDate) && (
                    <Button
                      variant="text"
                      color="red"
                      size="sm"
                      onClick={clearFilters}
                      disabled={loading}
                      className="flex items-center gap-1"
                    >
                      <X className="h-3.5 w-3.5" />
                      Clear
                    </Button>
                  )}
                </div>

                {/* Enhanced Filters Modal */}
                {showFilters && (
                  <>
                    <div 
                      className="fixed inset-0 bg-black/10 backdrop-blur-sm z-40"
                      onClick={() => setShowFilters(false)}
                    />
                    
                    <div className="fixed inset-0 flex items-center justify-center z-50">
                      <Card className="w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
                        <div className="sticky top-0 bg-white z-10 p-4 border-b border-gray-200 flex justify-between items-center">
                          <Typography variant="h5" color="blue-gray">
                            Filter Logs
                          </Typography>
                          <IconButton
                            variant="text"
                            size="sm"
                            onClick={() => setShowFilters(false)}
                          >
                            <X className="h-5 w-5" />
                          </IconButton>
                        </div>
                        
                        <div className="p-4 space-y-4">
                          {/* Admin Filter */}
                          <div>
                            <Typography variant="small" className="mb-2 font-medium text-gray-700">
                              Admin
                            </Typography>
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <User className="h-4 w-4 text-blue-600" />
                              </div>
                              <select
                                value={adminFilter}
                                onChange={(e) => {
                                  setAdminFilter(e.target.value);
                                  setCurrentPage(1);
                                }}
                                className="w-full pl-10 pr-10 py-2.5 bg-white border border-gray-300 text-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm transition-all duration-200 appearance-none"
                              >
                                <option value="">All Admins</option>
                                {availableAdmins.map((admin) => (
                                  <option key={admin._id} value={admin._id}>
                                    {`${admin.firstName} ${admin.lastName}`}
                                  </option>
                                ))}
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              </div>
                            </div>
                          </div>

                          {/* Action Filter */}
                          <div>
                            <Typography variant="small" className="mb-2 font-medium text-gray-700">
                              Action
                            </Typography>
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <Activity className="h-4 w-4 text-amber-600" />
                              </div>
                              <select
                                value={actionFilter}
                                onChange={(e) => {
                                  setActionFilter(e.target.value);
                                  setCurrentPage(1);
                                }}
                                className="w-full pl-10 pr-10 py-2.5 bg-white border border-gray-300 text-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm transition-all duration-200 appearance-none"
                              >
                                <option value="">All Actions</option>
                                {availableActions.map((action) => (
                                  <option key={action} value={action}>
                                    {action}
                                  </option>
                                ))}
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              </div>
                            </div>
                          </div>

                          {/* Target Model Filter */}
                          <div>
                            <Typography variant="small" className="mb-2 font-medium text-gray-700">
                              Target Model
                            </Typography>
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                {getTargetModelIcon(targetModelFilter)}
                              </div>
                              <select
                                value={targetModelFilter}
                                onChange={(e) => {
                                  setTargetModelFilter(e.target.value);
                                  setCurrentPage(1);
                                }}
                                className="w-full pl-10 pr-10 py-2.5 bg-white border border-gray-300 text-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm transition-all duration-200 appearance-none"
                              >
                                
                                <option value="">All Models</option>
                                {availableTargetModels.map((model) => (
                                  <option key={model} value={model} className="flex items-center gap-1">
                                    
                                    {model}
                                  </option>
                                ))}
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              </div>
                            </div>
                          </div>

                          {/* Date Range */}
                          <div>
                            <Typography variant="small" className="mb-2 font-medium text-gray-700">
                              Date Range
                            </Typography>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <Input
                                  type="date"
                                  label="Start Date"
                                  value={dateRange.startDate}
                                  onChange={(e) => {
                                    setDateRange((prev) => ({
                                      ...prev,
                                      startDate: e.target.value,
                                    }));
                                    setCurrentPage(1);
                                  }}
                                  icon={<Calendar className="h-4 w-4" />}
                                  className="bg-gray-50"
                                />
                              </div>
                              <div>
                                <Input
                                  type="date"
                                  label="End Date"
                                  value={dateRange.endDate}
                                  onChange={(e) => {
                                    setDateRange((prev) => ({
                                      ...prev,
                                      endDate: e.target.value,
                                    }));
                                    setCurrentPage(1);
                                  }}
                                  icon={<Calendar className="h-4 w-4" />}
                                  className="bg-gray-50"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="sticky bottom-0 bg-white p-4 border-t border-gray-200 flex justify-between">
                          <Button
                            variant="text"
                            color="red"
                            onClick={clearFilters}
                            className="px-4"
                          >
                            Clear All
                          </Button>
                          <Button
                            color="blue"
                            onClick={() => setShowFilters(false)}
                            className="px-6"
                          >
                            Apply Filters
                          </Button>
                        </div>
                      </Card>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {(adminFilter ||
            actionFilter ||
            targetModelFilter ||
            dateRange.startDate ||
            dateRange.endDate) && (
            <div className="mt-4 flex flex-wrap items-center gap-2">
              <Typography variant="small" className="font-medium text-gray-600 mr-2 flex items-center">
                <Tag className="h-3.5 w-3.5 mr-1.5" />
                Active Filters:
              </Typography>
              {adminFilter && (
                <Chip
                  value={`Admin: ${availableAdmins.find(a => a._id === adminFilter)?.firstName || adminFilter}`}
                  onClose={() => setAdminFilter("")}
                  variant="ghost"
                  color="blue"
                  className="rounded-full px-3 py-1.5"
                />
              )}
              {actionFilter && (
                <Chip
                  value={`Action: ${actionFilter}`}
                  onClose={() => setActionFilter("")}
                  variant="ghost"
                  color="blue"
                  className="rounded-full px-3 py-1.5"
                />
              )}
              {targetModelFilter && (
                <Chip
                  value={`Model: ${targetModelFilter}`}
                  onClose={() => setTargetModelFilter("")}
                  variant="ghost"
                  color="blue"
                  className="rounded-full px-3 py-1.5"
                />
              )}
              {(dateRange.startDate || dateRange.endDate) && (
                <Chip
                  value={`Date: ${dateRange.startDate || "Any"} - ${dateRange.endDate || "Any"}`}
                  onClose={() => setDateRange({ startDate: "", endDate: "" })}
                  variant="ghost"
                  color="blue"
                  className="rounded-full px-3 py-1.5"
                />
              )}
            </div>
          )}
        </div>

        {/* Logs Table */}
        {loading ? (
          <div className="flex justify-center items-center py-16">
            <div className="flex flex-col items-center">
              <Spinner className="h-10 w-10 text-blue-600 mb-4" />
              <Typography color="blue-gray" className="font-medium">
                Loading activity logs...
              </Typography>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Admin
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Target
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Details
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {logs.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="text-center py-12 text-gray-500">
                      <div className="flex flex-col items-center gap-3">
                        <AlertCircle className="h-10 w-10 text-gray-400" />
                        <Typography variant="h6" className="text-gray-600">
                          No activity logs found
                        </Typography>
                        {(adminFilter ||
                          actionFilter ||
                          targetModelFilter ||
                          dateRange.startDate ||
                          dateRange.endDate ||
                          searchQuery) && (
                          <Button
                            variant="outlined"
                            color="blue"
                            size="sm"
                            className="mt-2"
                            onClick={clearFilters}
                          >
                            Clear all filters
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ) : (
                  logs.map((log) => (
                    <tr
                      key={log._id}
                      className="hover:bg-gray-50 transition-colors duration-150"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <Typography variant="small" className="text-gray-700">
                            {formatDate(log.timestamp)}
                          </Typography>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-9 w-9 rounded-full bg-blue-50 flex items-center justify-center">
                            <User className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            {log.admin ? (
                              <>
                                <Typography variant="small" className="font-medium text-gray-900">
                                  {`${log.admin.firstName} ${log.admin.lastName}`}
                                </Typography>
                                <Typography variant="small" className="text-xs text-gray-500">
                                  {log.admin.email}
                                </Typography>
                              </>
                            ) : (
                              <Typography variant="small" className="text-gray-500">
                                System
                              </Typography>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Chip
                          value={getActionDescription(log.action)}
                          size="sm"
                          variant="ghost"
                          color={getActionColor(log.action)}
                          className="font-medium capitalize"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div className="max-w-xs">
                          <div className="flex items-center gap-2 mb-1">
                            {getTargetModelIcon(log.targetModel)}
                            <Typography variant="small" className="font-medium text-gray-700">
                              {log.targetModel}
                            </Typography>
                          </div>

                          {log.target && typeof log.target === "object" ? (
                            <div className="text-xs text-gray-600 mt-1 space-y-1">
                              {Object.entries(log.target)
                                .slice(0, 2)
                                .map(([key, value]) => (
                                  <div key={key} className="flex items-start">
                                    <span className="font-medium mr-1 capitalize">
                                      {key.replace(/([A-Z])/g, " $1").trim()}:
                                    </span>
                                    <span className="truncate">
                                      {typeof value === "object"
                                        ? JSON.stringify(value).substring(0, 30) + "..."
                                        : String(value).substring(0, 30) + "..."}
                                    </span>
                                  </div>
                                ))}
                              {Object.keys(log.target).length > 2 && (
                                <Typography
                                  variant="small"
                                  className="text-blue-500 cursor-pointer hover:underline"
                                  onClick={() =>
                                    handleShowDetails(
                                      log.target,
                                      "Target Details",
                                      { ...log, details: log.target }
                                    )
                                  }
                                >
                                  + {Object.keys(log.target).length - 2} more fields
                                </Typography>
                              )}
                            </div>
                          ) : log.target ? (
                            <Typography variant="small" className="text-gray-600 truncate">
                              {String(log.target)}
                            </Typography>
                          ) : null}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Tooltip content="View details">
                          <IconButton
                            variant="text"
                            color="blue-gray"
                            size="sm"
                            onClick={() =>
                              handleShowDetails(
                                log.details,
                                "Activity Details",
                                log
                              )
                            }
                            className="rounded-full hover:bg-blue-50"
                          >
                            <Eye className="h-4 w-4" />
                          </IconButton>
                        </Tooltip>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            {/* Pagination Controls */}
            <div className="flex flex-col md:flex-row items-center justify-between p-4 border-t border-gray-200 gap-4">
              <div className="flex items-center gap-4">
                <Select
                  label="Items per page"
                  value={itemsPerPage.toString()}
                  onChange={handleItemsPerPageChange}
                  className="min-w-[120px]"
                >
                  {ITEMS_PER_PAGE_OPTIONS.map((option) => (
                    <Option key={option} value={option.toString()}>
                      {option}
                    </Option>
                  ))}
                </Select>
                <Typography variant="small" className="font-normal text-gray-600">
                  Page {currentPage} of {totalPages} • {totalItems} items
                </Typography>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outlined"
                  size="sm"
                  onClick={handlePrevPage}
                  disabled={currentPage === 1 || loading}
                  className="flex items-center gap-2 px-4"
                >
                  Previous
                </Button>
                <Button
                  variant="outlined"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages || loading}
                  className="flex items-center gap-2 px-4"
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>

       {/* Details Modal */}
       <Dialog open={showDetailsModal} handler={handleCloseModal} size="md">
        <DialogHeader className="flex items-center justify-between">
          <Typography variant="h5">{selectedLogTitle}</Typography>
          <IconButton
            variant="text"
            color="blue-gray"
            onClick={handleCloseModal}
            className="rounded-full"
          >
            <X className="h-5 w-5" />
          </IconButton>
        </DialogHeader>
        <DialogBody divider className="p-0">
          <div className="p-4 bg-gray-50 rounded-lg m-4 overflow-y-auto max-h-96">
            {selectedLogDetails ? (
              typeof selectedLogDetails === "object" &&
              Object.keys(selectedLogDetails).some(
                (key) =>
                  typeof selectedLogDetails[key] === "object" &&
                  selectedLogDetails[key] !== null
              ) ? (
                // Formatted log with sections
                <div className="space-y-4">
                  {Object.entries(selectedLogDetails).map(
                    ([section, content], index) => (
                      <Accordion
                        key={section}
                        open={openAccordion === index + 1}
                        icon={
                          <ChevronDown
                            className={`h-4 w-4 transition-transform ${
                              openAccordion === index + 1 ? "rotate-180" : ""
                            }`}
                          />
                        }
                      >
                        <AccordionHeader
                          onClick={() => handleAccordionOpen(index + 1)}
                          className="text-base font-medium border-b-0 py-2 px-4 bg-blue-50 rounded-t-lg"
                        >
                          {section}
                        </AccordionHeader>
                        <AccordionBody className="py-2 px-4 bg-white rounded-b-lg border border-gray-200 border-t-0">
                          <div className="space-y-2">
                            {typeof content === "object" && content !== null ? (
                              Object.entries(content).map(([key, value]) => (
                                <div
                                  key={key}
                                  className="flex flex-col sm:flex-row sm:items-start border-b border-gray-100 pb-2 last:border-0 last:pb-0"
                                >
                                  <div className="font-medium text-gray-700 sm:w-1/3 mb-1 sm:mb-0">
                                    {key}:
                                  </div>
                                  <div className="text-gray-600 sm:w-2/3 break-words">
                                    {typeof value === "object" && value !== null
                                      ? JSON.stringify(value)
                                      : String(value)}
                                  </div>
                                </div>
                              ))
                            ) : (
                              <div className="text-gray-600">
                                {String(content)}
                              </div>
                            )}
                          </div>
                        </AccordionBody>
                      </Accordion>
                    )
                  )}
                </div>
              ) : (
                // Fallback to JSON for unformatted details
                <div className="bg-white p-4 rounded border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-3 text-blue-gray-500">
                    <Info className="h-5 w-5" />
                    <Typography variant="small">
                      Technical details shown below
                    </Typography>
                  </div>
                  <pre className="text-xs overflow-x-auto">
                    {JSON.stringify(selectedLogDetails, null, 2)}
                  </pre>
                </div>
              )
            ) : (
              <div className="text-center py-8 text-gray-500">
                No details available
              </div>
            )}
          </div>
        </DialogBody>
        <DialogFooter className="flex justify-end gap-2">
          <Button variant="text" color="blue-gray" onClick={handleCloseModal}>
            Close
          </Button>
          <Button
            variant="filled"
            color="blue"
            onClick={() => {
              // Copy to clipboard
              let textToCopy;

              // Format the details for copying
              if (
                typeof selectedLogDetails === "object" &&
                Object.keys(selectedLogDetails).some(
                  (key) =>
                    typeof selectedLogDetails[key] === "object" &&
                    selectedLogDetails[key] !== null
                )
              ) {
                // Format the structured data
                textToCopy = Object.entries(selectedLogDetails)
                  .map(([section, content]) => {
                    const sectionText = `=== ${section} ===\n`;

                    if (typeof content === "object" && content !== null) {
                      const contentText = Object.entries(content)
                        .map(
                          ([key, value]) =>
                            `${key}: ${
                              typeof value === "object"
                                ? JSON.stringify(value)
                                : value
                            }`
                        )
                        .join("\n");
                      return sectionText + contentText;
                    }

                    return sectionText + content;
                  })
                  .join("\n\n");
              } else {
                // Fallback to JSON
                textToCopy = JSON.stringify(selectedLogDetails, null, 2);
              }

              navigator.clipboard
                .writeText(textToCopy)
                .then(() => toast.success("Details copied to clipboard"))
                .catch(() => toast.error("Failed to copy details"));
            }}
            className="flex items-center gap-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={2}
              stroke="currentColor"
              className="h-4 w-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
              />
            </svg>
            Copy
          </Button>
        </DialogFooter>
      </Dialog>
    </div>
  );
}


