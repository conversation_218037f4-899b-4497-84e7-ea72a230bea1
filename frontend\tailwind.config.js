/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');
const withMT = require("@material-tailwind/react/utils/withMT");

module.exports = withMT({
  content: [
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      direction: ['rtl', 'ltr'],
      backgroundImage: {
        'gradient-primary': 'linear-gradient(0deg, #FFFFFF, #FFFFFF), linear-gradient(135.8deg, #3D71E9 7.07%, #59A7F7 101.34%)',
        'gradient-secondary': 'linear-gradient(270deg, #2A76F6 0%, #1243AA 100%)',
        'custom-gradient': 'linear-gradient(to right, #0b2d6a 0%, #0b2d6a 25%, #070310 50%, #180640 75%, #180640 100%)',
        'gradient-cloud-maroc': 'linear-gradient(90deg, #092654 0%, #140537 100%)',

      },
      colors: {
        'custom-dark': '#5412E8',
        'dark-green': '#3A7E73',
        primary: {
          DEFAULT: "#0b2d6a",
          '100': "#497EF7",
        },
        secondary: {
          DEFAULT: "#497ef7",
        },
        tertiary: {
          DEFAULT: "#0b2d6a",
        },
      },
      fontSize: {
        'xsxs': '.65rem',
        'xs': '.75rem',
        'sm': '.875rem',
        'tiny': '.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
        '6xl': '4rem',
        '7xl': '5rem',
        'responsive': 'calc(10px + 1vw)',
      },
      fontFamily: {
        inter: ['var(--font-inter)'],
        roboto: ['var(--font-roboto)'],
        poppins: ['var(--font-poppins)'],
        jim_ngihtshade: ['var(--font-jim-night-shade)'],
        outfit: ['var(--font-outfit)'],
      },
      inset: {
        '5': '5px',
        '8': '8px'
      },
      boxShadow: {
        "custom": '0 3px 10px grey',
        'customGreen': '0px 0px 1px 3px rgba(84, 177, 117, 0.30)',
        'custom-light': '0px 0px 4.72px 0px rgba(0, 0, 0, 0.19)',
        'custom-heavy': '1px 1px 27.1px 1px rgba(0, 0, 0, 0.09)',
      },

      animation: {
        shake: 'shake 0.25s ease-in-out 2',
        'paint-from-bottom': 'paint-from-bottom 0.5s ease-in-out',
      },
      keyframes: {
        shake: {
          '0%': { transform: 'translate(0, 0) rotate(0deg)' },
          '25%': { transform: ' translate(5px, 5px) rotate(2deg)' },
          '50%': { transform: 'translate(0, 0) rotate(0eg)' },
          '75%': { transform: 'translate(-5px, 5px) rotate(-2deg)' },
          '100%': { transform: 'translate(0, 0) rotate(0deg)' },
        },
        'paint-from-bottom': {
          '0%': { transform: 'scaleY(0)', transformOrigin: 'bottom' },
          '100%': { transform: 'scaleY(1)', transformOrigin: 'bottom' },
        },
      },
    },
    screens: {
      'xs': '475px',
      ...defaultTheme.screens,
      '3xl': '1600px',

    }
  },
  plugins: [require('tailwind-scrollbar-hide')],
})
