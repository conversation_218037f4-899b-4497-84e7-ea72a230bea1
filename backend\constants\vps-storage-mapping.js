/**
 * Static mapping of VPS plan names to their Contabo storage type variants
 * This mapping is based on Contabo's product catalog and should remain static
 * unless Contabo changes their product IDs or introduces new storage types
 *
 * Updated to use single storageDescription field (e.g., "75 GB NVMe")
 */

const VPS_STORAGE_MAPPING = {
  // CLOUD VPS 10 variants
  "CLOUD VPS 10": [
    {
      productId: "V91",
      storageDescription: "75 GB NVMe",
      isDefault: true,
      additionalPrice: 0 // Base price, no additional cost
    },
    {
      productId: "V92",
      storageDescription: "150 GB SSD",
      isDefault: false,
      additionalPrice: 0 // Same price tier, different storage
    },
    {
      productId: "V93",
      storageDescription: "300 GB Storage",
      isDefault: false,
      additionalPrice: 0 // Same price tier, more storage
    }
  ],

  // CLOUD VPS 20 variants
  "CLOUD VPS 20": [
    {
      productId: "V94",
      storageDescription: "100 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    },
    {
      productId: "V95",
      storageDescription: "200 GB SSD",
      isDefault: false,
      additionalPrice: 0
    },
    {
      productId: "V96",
      storageDescription: "400 GB Storage",
      isDefault: false,
      additionalPrice: 0
    }
  ],

  // CLOUD VPS 30 variants
  "CLOUD VPS 30": [
    {
      productId: "V97",
      storageDescription: "200 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    },
    {
      productId: "V98",
      storageDescription: "400 GB SSD",
      isDefault: false,
      additionalPrice: 0
    },
    {
      productId: "V99",
      storageDescription: "1000 GB NVMe",
      isDefault: false,
      additionalPrice: 0
    }
  ],

  // CLOUD VPS 40 variants
  "CLOUD VPS 40": [
    {
      productId: "V100",
      storageDescription: "250 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    },
    {
      productId: "V101",
      storageDescription: "500 GB SSD",
      isDefault: false,
      additionalPrice: 0
    },
    {
      productId: "V102",
      storageDescription: "1200 GB NVMe",
      isDefault: false,
      additionalPrice: 0
    }
  ],

  // CLOUD VPS 50 variants
  "CLOUD VPS 50": [
    {
      productId: "V103",
      storageDescription: "300 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    },
    {
      productId: "V104",
      storageDescription: "600 GB SSD",
      isDefault: false,
      additionalPrice: 0
    },
    {
      productId: "V105",
      storageDescription: "1400 GB SSD",
      isDefault: false,
      additionalPrice: 0
    }
  ],

  // CLOUD VPS 60 variants
  "CLOUD VPS 60": [
    {
      productId: "V106",
      storageDescription: "350 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    },
    {
      productId: "V107",
      storageDescription: "700 GB SSD",
      isDefault: false,
      additionalPrice: 0
    }
    // Note: V105 from table seems to be for VPS 50 Storage, no V108 listed for VPS 60 Storage
  ],

  // VDS variants (Dedicated Server variants)
  "VDS S": [
    {
      productId: "V8",
      storageDescription: "180 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    }
  ],

  "VDS M": [
    {
      productId: "V9",
      storageDescription: "240 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    }
  ],

  "VDS L": [
    {
      productId: "V10",
      storageDescription: "360 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    }
  ],

  "VDS XL": [
    {
      productId: "V11",
      storageDescription: "480 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    }
  ],

  "VDS XXL": [
    {
      productId: "V16",
      storageDescription: "720 GB NVMe",
      isDefault: true,
      additionalPrice: 0
    }
  ]
};

/**
 * Get storage types for a specific VPS plan
 * @param {string} planName - The VPS plan name (e.g., "CLOUD VPS 10")
 * @returns {Array} Array of storage type objects or empty array if not found
 */
function getStorageTypesForPlan(planName) {
  return VPS_STORAGE_MAPPING[planName] || [];
}

/**
 * Get default storage type for a specific VPS plan
 * @param {string} planName - The VPS plan name
 * @returns {Object|null} Default storage type object or null if not found
 */
function getDefaultStorageType(planName) {
  const storageTypes = getStorageTypesForPlan(planName);
  return storageTypes.find(storage => storage.isDefault) || storageTypes[0] || null;
}

/**
 * Get product ID for a specific plan and storage description
 * @param {string} planName - The VPS plan name
 * @param {string} storageDescription - The storage description ("75 GB NVMe", "150 GB SSD", etc.)
 * @returns {string|null} Product ID or null if not found
 */
function getProductIdForStorage(planName, storageDescription) {
  const storageTypes = getStorageTypesForPlan(planName);
  const storage = storageTypes.find(s => s.storageDescription === storageDescription);
  return storage ? storage.productId : null;
}

/**
 * Check if a plan has multiple storage options
 * @param {string} planName - The VPS plan name
 * @returns {boolean} True if plan has multiple storage options
 */
function hasMultipleStorageOptions(planName) {
  return getStorageTypesForPlan(planName).length > 1;
}

/**
 * Get all available VPS plan names
 * @returns {Array} Array of all VPS plan names
 */
function getAllPlanNames() {
  return Object.keys(VPS_STORAGE_MAPPING);
}

module.exports = {
  VPS_STORAGE_MAPPING,
  getStorageTypesForPlan,
  getDefaultStorageType,
  getProductIdForStorage,
  hasMultipleStorageOptions,
  getAllPlanNames
};
