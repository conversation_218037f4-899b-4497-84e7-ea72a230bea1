const express = require('express');
const paymentRouter = express.Router();
// const { protect } = require('../middleware/authMiddleware');
const { fetchUserMiddleware } = require('../midelwares/authorization');
const { asyncBackFrontEndLang } = require('../midelwares/sharedMidd');


const { 
    getPaymentHistory,
    getPaymentDetails
} = require('../controllers/paymentController');

paymentRouter.get('/history',asyncBackFrontEndLang, fetchUserMiddleware, getPaymentHistory);
paymentRouter.get('/:paymentId', asyncBackFrontEndLang,fetchUserMiddleware, getPaymentDetails);

module.exports = paymentRouter;
