import { NextResponse } from "next/server";
import { locales } from '../i18n';
import createMiddleware from 'next-intl/middleware';

// Create the intl middleware for locale routes
const intlMiddleware = createMiddleware({
  locales: locales,
  defaultLocale: 'fr',
  localePrefix: 'always'
});

export default function middleware(request) {
  const pathname = request.nextUrl.pathname;
  
  // Check if this is an admin route
  if (pathname.startsWith('/admin')) {
    // Extract cookies
    const token = request.cookies.get("token")?.value;
    const role = request.cookies.get("role")?.value;
    
    // Protect admin routes
    if (!token || role !== "ADMIN") {
      return NextResponse.redirect(
        new URL(
          !token
            ? `/auth/login?redirect=${encodeURIComponent(pathname)}`
            : "/404",
          request.url
        )
      );
    }
    return NextResponse.next();
  }
  
  // For non-admin routes, use the intl middleware
  return intlMiddleware(request);
}

// Update the matcher to properly handle both admin and localized routes
export const config = {
  matcher: [
    // Match all paths except api, _next, static files, etc.
    "/((?!api|_next|.*\\..*).*)",
    // Match specific locale paths
    "/(ar|en|fr)/:path*",
  ],
};
