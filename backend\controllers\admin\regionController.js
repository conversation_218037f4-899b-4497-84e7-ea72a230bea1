const VPSRegion = require('../../models/VPSRegion');
const Package = require('../../models/Package');
const adminLogger = require('../../utils/adminLogger');
const VPSProviderFactory = require('../../services/providers/VPSProviderFactory');

// Get all regions
const getAllRegions = async (req, res) => {
  try {
    console.log('=== GET ALL REGIONS REQUEST (ADMIN) ===');
    const regions = await VPSRegion.find()
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('pricing.packageId', 'name price')
      .sort({ displayOrder: 1, name: 1 });

    console.log(`Found ${regions.length} regions for admin panel`);
    if (regions.length > 0) {
      console.log('First region sample:', {
        id: regions[0]._id,
        regionId: regions[0].regionId,
        name: regions[0].name,
        pricingCount: regions[0].pricing?.length
      });
    } else {
      console.log('No regions found in database - this might be why frontend shows empty');
    }

    const response = {
      success: true,
      data: regions
    };

    console.log('Sending response with', regions.length, 'regions');
    res.status(200).json(response);
  } catch (error) {
    console.error('Error fetching regions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching regions',
      error: error.message
    });
  }
};

// Get single region
const getRegion = async (req, res) => {
  try {
    const region = await VPSRegion.findById(req.params.id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('pricing.packageId', 'name price');

    if (!region) {
      return res.status(404).json({
        success: false,
        message: 'Region not found'
      });
    }

    res.status(200).json({
      success: true,
      data: region
    });
  } catch (error) {
    console.error('Error fetching region:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching region',
      error: error.message
    });
  }
};



// Update region
const updateRegion = async (req, res) => {
  try {
    const region = await VPSRegion.findById(req.params.id);
    if (!region) {
      return res.status(404).json({
        success: false,
        message: 'Region not found'
      });
    }

    const {
      regionId,
      name,
      country,
      countryCode,
      continent,
      city,
      flag,
      description,
      status,
      isPopular,
      displayOrder,
      pricing,
      datacenterProvider,
      networkSpeed,
      uptime,
      coordinates,
      features,
      availableServices
    } = req.body;

    // Check if regionId already exists (excluding current region)
    if (regionId && regionId.toLowerCase() !== region.regionId) {
      const existingRegion = await VPSRegion.findOne({
        regionId: regionId.toLowerCase(),
        _id: { $ne: req.params.id }
      });
      if (existingRegion) {
        return res.status(400).json({
          success: false,
          message: 'Region ID already exists'
        });
      }
    }

    // Validate and populate pricing with package names
    let validatedPricing = [];
    if (pricing && Array.isArray(pricing)) {
      for (const price of pricing) {
        const vpsPackage = await Package.findById(price.packageId);
        if (vpsPackage) {
          validatedPricing.push({
            packageId: price.packageId,
            packageName: vpsPackage.name,
            additionalPrice: price.additionalPrice || 0
          });
        }
      }
    }

    // Store old values for logging
    const oldValues = {
      regionId: region.regionId,
      name: region.name,
      status: region.status
    };

    // Update fields
    if (regionId !== undefined) region.regionId = regionId;
    if (name !== undefined) region.name = name;
    if (country !== undefined) region.country = country;
    if (countryCode !== undefined) region.countryCode = countryCode;
    if (continent !== undefined) region.continent = continent;
    if (city !== undefined) region.city = city;
    if (flag !== undefined) region.flag = flag;
    if (description !== undefined) region.description = description;
    if (status !== undefined) region.status = status;
    if (isPopular !== undefined) region.isPopular = isPopular;
    if (displayOrder !== undefined) region.displayOrder = displayOrder;
    if (validatedPricing.length > 0) region.pricing = validatedPricing;
    if (datacenterProvider !== undefined) region.datacenterProvider = datacenterProvider;
    if (networkSpeed !== undefined) region.networkSpeed = networkSpeed;
    if (uptime !== undefined) region.uptime = uptime;
    if (coordinates !== undefined) region.coordinates = coordinates;
    if (features !== undefined) region.features = features;
    if (availableServices !== undefined) region.availableServices = availableServices;
    
    region.updatedBy = req.user.id;

    await region.save();

    // Log admin activity
    await adminLogger.logUpdate(req.user.id, 'Region', region._id, {
      oldValues,
      newValues: {
        regionId: region.regionId,
        name: region.name,
        status: region.status
      }
    });

    const populatedRegion = await VPSRegion.findById(region._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .populate('pricing.packageId', 'name price');

    res.status(200).json({
      success: true,
      data: populatedRegion,
      message: 'Region updated successfully'
    });
  } catch (error) {
    console.error('Error updating region:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating region',
      error: error.message
    });
  }
};

// Delete region
const deleteRegion = async (req, res) => {
  try {
    const region = await VPSRegion.findById(req.params.id);
    if (!region) {
      return res.status(404).json({
        success: false,
        message: 'Region not found'
      });
    }

    // Log admin activity before deletion
    await adminLogger.logDelete(req.user.id, 'VPSRegion', region._id, {
      regionId: region.regionId,
      name: region.name,
      country: region.country
    });

    await VPSRegion.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Region deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting region:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting region',
      error: error.message
    });
  }
};

// Get regions for specific package (public endpoint)
const getRegionsForPackage = async (req, res) => {
  try {
    const { packageId } = req.params;

    const regions = await VPSRegion.find({
      status: 'active',
      'pricing.packageId': packageId
    })
    .select('regionId name country countryCode continent city flag description isPopular displayOrder pricing coordinates features')
    .populate('pricing.packageId', 'name price')
    .sort({ displayOrder: 1, name: 1 });

    res.status(200).json({
      success: true,
      data: regions
    });
  } catch (error) {
    console.error('Error fetching regions for package:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching regions for package',
      error: error.message
    });
  }
};

// Create a new region
const createRegion = async (req, res) => {
  try {
    console.log('=== CREATE REGION REQUEST ===');
    console.log('Request body:', req.body);
    console.log('User:', req.user);

    const { regionId, packageId, additionalPrice } = req.body;

    console.log('💾 BACKEND: Saving VPS region pricing:', {
      regionId,
      packageId,
      additionalPrice,
      userId: req.user._id
    });

    // Validation
    if (!regionId || !packageId || additionalPrice === undefined) {
      console.log('❌ Validation failed:', { regionId, packageId, additionalPrice });
      return res.status(400).json({
        success: false,
        message: 'regionId, packageId, and additionalPrice are required'
      });
    }

    // Get package info
    const package = await Package.findById(packageId);
    if (!package) {
      return res.status(404).json({
        success: false,
        message: 'Package not found'
      });
    }

    // Check if region already exists
    let region = await VPSRegion.findOne({ regionId });

    if (region) {
      // SOLUTION DÉFINITIVE: Recréer complètement le pricing pour éviter les conflits
      console.log(`🔧 FIXING region pricing for ${region.name}`);
      console.log(`🔍 Current pricing entries:`, region.pricing.map(p => ({
        packageId: p.packageId.toString(),
        packageName: p.packageName,
        additionalPrice: p.additionalPrice
      })));

      // Get all VPS packages
      const allVPSPackages = await Package.find({
        name: { $in: ['CLOUD VPS 10', 'CLOUD VPS 20'] }
      }).select('_id name');

      console.log(`📦 All VPS packages:`, allVPSPackages.map(p => ({ id: p._id.toString(), name: p.name })));

      // Create new pricing array with exactly one entry per VPS package
      const newPricing = [];

      for (const vpsPackage of allVPSPackages) {
        const vpsPackageId = vpsPackage._id.toString();
        const targetPackageId = packageId.toString();

        if (vpsPackageId === targetPackageId) {
          // This is the package being updated
          newPricing.push({
            packageId: vpsPackage._id,
            packageName: vpsPackage.name,
            additionalPrice: additionalPrice
          });
          console.log(`✅ Updated ${vpsPackage.name}: ${additionalPrice} MAD`);
        } else {
          // Keep existing price for other packages
          const existingPricing = region.pricing.find(p =>
            p.packageId.toString() === vpsPackageId
          );
          const existingPrice = existingPricing ? existingPricing.additionalPrice : 0;

          newPricing.push({
            packageId: vpsPackage._id,
            packageName: vpsPackage.name,
            additionalPrice: existingPrice
          });
          console.log(`✅ Kept ${vpsPackage.name}: ${existingPrice} MAD`);
        }
      }

      // Replace pricing completely
      region.pricing = newPricing;
      region.updatedBy = req.user._id;
    } else {
      // Create new region - fetch real data from Contabo API
      let regionData = {
        regionId,
        name: regionId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        country: 'Unknown',
        countryCode: 'XX',
        continent: 'Unknown',
        city: 'Unknown',
        flag: '🌍'
      };

      try {
        // Try to get real region data from Contabo API
        const contaboProvider = VPSProviderFactory.createProvider('contabo');
        const contaboRegions = await contaboProvider.getRegions();
        const matchingRegion = contaboRegions.find(r => r.regionSlug === regionId);

        if (matchingRegion) {
          regionData = {
            regionId,
            name: matchingRegion.regionName || regionData.name,
            country: matchingRegion.dataCenters?.[0]?.country || 'Unknown',
            countryCode: matchingRegion.dataCenters?.[0]?.countryCode || 'XX',
            continent: matchingRegion.dataCenters?.[0]?.continent || 'Unknown',
            city: matchingRegion.dataCenters?.[0]?.city || 'Unknown',
            flag: matchingRegion.dataCenters?.[0]?.flag || '🌍'
          };
          console.log('Found matching Contabo region data:', regionData);
        }
      } catch (error) {
        console.warn('Could not fetch region data from Contabo API:', error.message);
      }

      region = new VPSRegion({
        ...regionData,
        status: 'active',
        pricing: [{
          packageId,
          packageName: package.name,
          additionalPrice
        }],
        createdBy: req.user._id
      });
    }

    // VÉRIFICATION FINALE: Supprimer les doublons potentiels
    const uniquePricing = [];
    const seenPackageIds = new Set();

    for (const pricing of region.pricing) {
      const pId = pricing.packageId.toString();
      if (!seenPackageIds.has(pId)) {
        seenPackageIds.add(pId);
        uniquePricing.push(pricing);
        console.log(`   ✅ Keeping pricing for ${pricing.packageName}: ${pricing.additionalPrice} MAD`);
      } else {
        console.log(`   🗑️ Removing duplicate pricing for ${pricing.packageName}`);
      }
    }

    region.pricing = uniquePricing;

    await region.save();
    console.log('Region saved successfully:', region._id);
    console.log(`Final pricing entries: ${region.pricing.length}`);

    // Populate the response
    await region.populate('pricing.packageId', 'name price');
    await region.populate('createdBy', 'firstName lastName email');
    await region.populate('updatedBy', 'firstName lastName email');

    console.log('Sending success response for region:', {
      id: region._id,
      regionId: region.regionId,
      name: region.name,
      pricingCount: region.pricing?.length
    });

    res.status(201).json({
      success: true,
      data: region,
      message: 'Region pricing updated successfully'
    });

  } catch (error) {
    console.error('Error creating/updating region:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating/updating region',
      error: error.message
    });
  }
};

// Get all active regions (public endpoint)
const getActiveRegions = async (req, res) => {
  try {
    console.log('=== GET ACTIVE REGIONS REQUEST ===');
    const regions = await VPSRegion.find({
      $or: [
        { status: 'active' },
        { status: { $exists: false } }, // Include regions without status for backward compatibility
        { status: null },
        { status: undefined }
      ]
    })
      .select('regionId name country countryCode continent city flag description isPopular displayOrder pricing coordinates features')
      .populate('pricing.packageId', 'name price')
      .sort({ displayOrder: 1, name: 1 });

    console.log(`Found ${regions.length} active regions`);

    res.status(200).json({
      success: true,
      data: regions
    });
  } catch (error) {
    console.error('Error fetching active regions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching active regions',
      error: error.message
    });
  }
};

module.exports = {
  getAllRegions,
  getRegion,
  createRegion,
  updateRegion,
  deleteRegion,
  getRegionsForPackage,
  getActiveRegions
};
