"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-intl/dist/_IntlProvider.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-intl/dist/_IntlProvider.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./development/_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsS0FBeUIsRUFBYyxFQUUxQyxNQUFNO0lBQ0xDLDZJQUF5QjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzP2E4NmIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9fSW50bFByb3ZpZGVyLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvX0ludGxQcm92aWRlci5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/_useLocale.js":
/*!**************************************************!*\
  !*** ./node_modules/use-intl/dist/_useLocale.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./development/_useLocale.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsS0FBeUIsRUFBYyxFQUUxQyxNQUFNO0lBQ0xDLHVJQUF5QjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzPzE2MWEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vcHJvZHVjdGlvbi9fdXNlTG9jYWxlLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvX3VzZUxvY2FsZS5qcycpO1xufVxuIl0sIm5hbWVzIjpbInByb2Nlc3MiLCJtb2R1bGUiLCJleHBvcnRzIiwicmVxdWlyZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js":
/*!************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst IntlContext = /*#__PURE__*/ React.createContext(undefined);\nexports.IntlContext = IntlContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLElBQUlBLFFBQVFDLG1CQUFPQSxDQUFDO0FBRXBCLE1BQU1DLGNBQWMsV0FBVyxHQUFFRixNQUFNRyxhQUFhLENBQUNDO0FBRXJEQyxtQkFBbUIsR0FBR0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvSW50bENvbnRleHQtQktmc256QnguanM/NTc2MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG5cbmNvbnN0IEludGxDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQodW5kZWZpbmVkKTtcblxuZXhwb3J0cy5JbnRsQ29udGV4dCA9IEludGxDb250ZXh0O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwicmVxdWlyZSIsIkludGxDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInVuZGVmaW5lZCIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_IntlProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction IntlProvider(_ref) {\n    let { children, defaultTranslationValues, formats, getMessageFallback, locale, messages, now, onError, timeZone } = _ref;\n    // The formatter cache is released when the locale changes. For\n    // long-running apps with a persistent `IntlProvider` at the root,\n    // this can reduce the memory footprint (e.g. in React Native).\n    const cache = React.useMemo(()=>{\n        return initializeConfig.createCache();\n    }, [\n        locale\n    ]);\n    const formatters = React.useMemo(()=>initializeConfig.createIntlFormatters(cache), [\n        cache\n    ]);\n    // Memoizing this value helps to avoid triggering a re-render of all\n    // context consumers in case the configuration didn't change. However,\n    // if some of the non-primitive values change, a re-render will still\n    // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n    // itself, because the `children` typically change on every render.\n    // There's some burden on the consumer side if it's important to reduce\n    // re-renders, put that's how React works.\n    // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n    const value = React.useMemo(()=>({\n            ...initializeConfig.initializeConfig({\n                locale,\n                defaultTranslationValues,\n                formats,\n                getMessageFallback,\n                messages,\n                now,\n                onError,\n                timeZone\n            }),\n            formatters,\n            cache\n        }), [\n        cache,\n        defaultTranslationValues,\n        formats,\n        formatters,\n        getMessageFallback,\n        locale,\n        messages,\n        now,\n        onError,\n        timeZone\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(IntlContext.IntlContext.Provider, {\n        value: value\n    }, children);\n}\nexports.IntlProvider = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js":
/*!***********************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\nfunction useIntlContext() {\n    const context = React.useContext(IntlContext.IntlContext);\n    if (!context) {\n        throw new Error(\"No intl context found. Have you configured the provider? See https://next-intl-docs.vercel.app/docs/usage/configuration#client-server-components\");\n    }\n    return context;\n}\nfunction useLocale() {\n    return useIntlContext().locale;\n}\nexports.useIntlContext = useIntlContext;\nexports.useLocale = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLTBSbDl1UjgyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsUUFBUUMsbUJBQU9BLENBQUM7QUFDcEIsSUFBSUMsY0FBY0QsbUJBQU9BLENBQUM7QUFFMUIsU0FBU0U7SUFDUCxNQUFNQyxVQUFVSixNQUFNSyxVQUFVLENBQUNILFlBQVlBLFdBQVc7SUFDeEQsSUFBSSxDQUFDRSxTQUFTO1FBQ1osTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Y7QUFDVDtBQUVBLFNBQVNHO0lBQ1AsT0FBT0osaUJBQWlCSyxNQUFNO0FBQ2hDO0FBRUFDLHNCQUFzQixHQUFHTjtBQUN6Qk0saUJBQWlCLEdBQUdGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2RldmVsb3BtZW50L191c2VMb2NhbGUtMFJsOXVSODIuanM/MWM5YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG52YXIgSW50bENvbnRleHQgPSByZXF1aXJlKCcuL0ludGxDb250ZXh0LUJLZnNuekJ4LmpzJyk7XG5cbmZ1bmN0aW9uIHVzZUludGxDb250ZXh0KCkge1xuICBjb25zdCBjb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChJbnRsQ29udGV4dC5JbnRsQ29udGV4dCk7XG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcignTm8gaW50bCBjb250ZXh0IGZvdW5kLiBIYXZlIHlvdSBjb25maWd1cmVkIHRoZSBwcm92aWRlcj8gU2VlIGh0dHBzOi8vbmV4dC1pbnRsLWRvY3MudmVyY2VsLmFwcC9kb2NzL3VzYWdlL2NvbmZpZ3VyYXRpb24jY2xpZW50LXNlcnZlci1jb21wb25lbnRzJyApO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufVxuXG5mdW5jdGlvbiB1c2VMb2NhbGUoKSB7XG4gIHJldHVybiB1c2VJbnRsQ29udGV4dCgpLmxvY2FsZTtcbn1cblxuZXhwb3J0cy51c2VJbnRsQ29udGV4dCA9IHVzZUludGxDb250ZXh0O1xuZXhwb3J0cy51c2VMb2NhbGUgPSB1c2VMb2NhbGU7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJyZXF1aXJlIiwiSW50bENvbnRleHQiLCJ1c2VJbnRsQ29udGV4dCIsImNvbnRleHQiLCJ1c2VDb250ZXh0IiwiRXJyb3IiLCJ1c2VMb2NhbGUiLCJsb2NhbGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale.js":
/*!**************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _useLocale = __webpack_require__(/*! ./_useLocale-0Rl9uR82.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUFBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBRTdELElBQUlDLGFBQWFDLG1CQUFPQSxDQUFDO0FBQ3pCQSxtQkFBT0EsQ0FBQztBQUNSQSxtQkFBT0EsQ0FBQztBQUlSSCxpQkFBaUIsR0FBR0UsV0FBV0UsU0FBUyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzPzUwZjIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgX3VzZUxvY2FsZSA9IHJlcXVpcmUoJy4vX3VzZUxvY2FsZS0wUmw5dVI4Mi5qcycpO1xucmVxdWlyZSgncmVhY3QnKTtcbnJlcXVpcmUoJy4vSW50bENvbnRleHQtQktmc256QnguanMnKTtcblxuXG5cbmV4cG9ydHMudXNlTG9jYWxlID0gX3VzZUxvY2FsZS51c2VMb2NhbGU7XG4iXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJfdXNlTG9jYWxlIiwicmVxdWlyZSIsInVzZUxvY2FsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n    let { messages, namespace, ...rest } = _ref;\n    // The `namespacePrefix` is part of the type system.\n    // See the comment in the function invocation.\n    messages = messages[namespacePrefix];\n    namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n    return createFormatter.createBaseTranslator({\n        ...rest,\n        messages,\n        namespace\n    });\n}\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */ function createTranslator(_ref) {\n    let { _cache = initializeConfig.createCache(), _formatters = initializeConfig.createIntlFormatters(_cache), getMessageFallback = initializeConfig.defaultGetMessageFallback, messages, namespace, onError = initializeConfig.defaultOnError, ...rest } = _ref;\n    // We have to wrap the actual function so the type inference for the optional\n    // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n    // The prefix (\"!\") is arbitrary.\n    return createTranslatorImpl({\n        ...rest,\n        onError,\n        cache: _cache,\n        formatters: _formatters,\n        getMessageFallback,\n        // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n        messages: {\n            \"!\": messages\n        },\n        namespace: namespace ? \"!.\".concat(namespace) : \"!\"\n    }, \"!\");\n}\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar IntlMessageFormat__default = /*#__PURE__*/ _interopDefault(IntlMessageFormat);\nfunction setTimeZoneInFormats(formats, timeZone) {\n    if (!formats) return formats;\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    return Object.keys(formats).reduce((acc, key)=>{\n        acc[key] = {\n            timeZone,\n            ...formats[key]\n        };\n        return acc;\n    }, {});\n}\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */ function convertFormatsToIntlMessageFormat(formats, timeZone) {\n    const formatsWithTimeZone = timeZone ? {\n        ...formats,\n        dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n    } : formats;\n    const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n    const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n    const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n    const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n    return {\n        ...formatsWithTimeZone,\n        date: {\n            ...defaultDateFormats,\n            ...formatsWithTimeZone.dateTime\n        },\n        time: {\n            ...defaultTimeFormats,\n            ...formatsWithTimeZone.dateTime\n        }\n    };\n}\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n    const getMessageFormat = initializeConfig.memoFn(function() {\n        return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n            formatters: intlFormatters,\n            ...arguments.length <= 3 ? undefined : arguments[3]\n        });\n    }, cache.message);\n    return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n    const fullKey = initializeConfig.joinPath(namespace, key);\n    if (!messages) {\n        throw new Error(\"No messages available at `\".concat(namespace, \"`.\"));\n    }\n    let message = messages;\n    key.split(\".\").forEach((part)=>{\n        const next = message[part];\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (part == null || next == null) {\n            throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\"));\n        }\n        message = next;\n    });\n    return message;\n}\nfunction prepareTranslationValues(values) {\n    if (Object.keys(values).length === 0) return undefined;\n    // Workaround for https://github.com/formatjs/formatjs/issues/1467\n    const transformedValues = {};\n    Object.keys(values).forEach((key)=>{\n        let index = 0;\n        const value = values[key];\n        let transformed;\n        if (typeof value === \"function\") {\n            transformed = (chunks)=>{\n                const result = value(chunks);\n                return /*#__PURE__*/ React.isValidElement(result) ? /*#__PURE__*/ React.cloneElement(result, {\n                    key: key + index++\n                }) : result;\n            };\n        } else {\n            transformed = value;\n        }\n        transformedValues[key] = transformed;\n    });\n    return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n    let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n    try {\n        if (!messages) {\n            throw new Error(\"No messages were configured on the provider.\");\n        }\n        const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (!retrievedMessages) {\n            throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\"));\n        }\n        return retrievedMessages;\n    } catch (error) {\n        const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        onError(intlError);\n        return intlError;\n    }\n}\nfunction getPlainMessage(candidate, values) {\n    if (values) return undefined;\n    const unescapedMessage = candidate.replace(/'([{}])/gi, \"$1\");\n    // Placeholders can be in the message if there are default values,\n    // or if the user has forgotten to provide values. In the latter\n    // case we need to compile the message to receive an error.\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n        return unescapedMessage;\n    }\n    return undefined;\n}\nfunction createBaseTranslator(config) {\n    const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n    return createBaseTranslatorImpl({\n        ...config,\n        messagesOrError\n    });\n}\nfunction createBaseTranslatorImpl(_ref) {\n    let { cache, defaultTranslationValues, formats: globalFormats, formatters, getMessageFallback = initializeConfig.defaultGetMessageFallback, locale, messagesOrError, namespace, onError, timeZone } = _ref;\n    const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n    function getFallbackFromErrorAndNotify(key, code, message) {\n        const error = new initializeConfig.IntlError(code, message);\n        onError(error);\n        return getMessageFallback({\n            error,\n            key,\n            namespace\n        });\n    }\n    function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        let message;\n        try {\n            message = resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n        if (typeof message === \"object\") {\n            let code, errorMessage;\n            if (Array.isArray(message)) {\n                code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl-docs.vercel.app/docs/usage/messages#arrays-of-messages\");\n                }\n            } else {\n                code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl-docs.vercel.app/docs/usage/messages#structuring-messages\");\n                }\n            }\n            return getFallbackFromErrorAndNotify(key, code, errorMessage);\n        }\n        let messageFormat;\n        // Hot path that avoids creating an `IntlMessageFormat` instance\n        const plainMessage = getPlainMessage(message, values);\n        if (plainMessage) return plainMessage;\n        // Lazy init the message formatter for better tree\n        // shaking in case message formatting is not used.\n        if (!formatters.getMessageFormat) {\n            formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n        }\n        try {\n            messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n                ...globalFormats,\n                ...formats\n            }, timeZone), {\n                formatters: {\n                    ...formatters,\n                    getDateTimeFormat (locales, options) {\n                        // Workaround for https://github.com/formatjs/formatjs/issues/4279\n                        return formatters.getDateTimeFormat(locales, {\n                            timeZone,\n                            ...options\n                        });\n                    }\n                }\n            });\n        } catch (error) {\n            const thrownError = error;\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + (\"originalMessage\" in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : \"\"));\n        }\n        try {\n            const formattedMessage = messageFormat.format(// @ts-expect-error `intl-messageformat` expects a different format\n            // for rich text elements since a recent minor update. This\n            // needs to be evaluated in detail, possibly also in regards\n            // to be able to format to parts.\n            prepareTranslationValues({\n                ...defaultTranslationValues,\n                ...values\n            }));\n            if (formattedMessage == null) {\n                throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\"));\n            }\n            // Limit the function signature to return strings or React elements\n            return /*#__PURE__*/ React.isValidElement(formattedMessage) || // Arrays of React elements\n            Array.isArray(formattedMessage) || typeof formattedMessage === \"string\" ? formattedMessage : String(formattedMessage);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n        }\n    }\n    function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        const result = translateBaseFn(key, values, formats);\n        if (typeof result !== \"string\") {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\", \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\"));\n        }\n        return result;\n    }\n    translateFn.rich = translateBaseFn;\n    // Augment `translateBaseFn` to return plain strings\n    translateFn.markup = (key, values, formats)=>{\n        const result = translateBaseFn(key, // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n        // of `RichTranslationValues` but TypeScript isn't smart enough here.\n        values, formats);\n        // When only string chunks are provided to the parser, only\n        // strings should be returned here. Note that we need a runtime\n        // check for this since rich text values could be accidentally\n        // inherited from `defaultTranslationValues`.\n        if (typeof result !== \"string\") {\n            const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n            onError(error);\n            return getMessageFallback({\n                error,\n                key,\n                namespace\n            });\n        }\n        return result;\n    };\n    translateFn.raw = (key)=>{\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        try {\n            return resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n    };\n    translateFn.has = (key)=>{\n        if (hasMessagesError) {\n            return false;\n        }\n        try {\n            resolvePath(locale, messagesOrError, key, namespace);\n            return true;\n        } catch (_unused) {\n            return false;\n        }\n    };\n    return translateFn;\n}\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */ function resolveNamespace(namespace, namespacePrefix) {\n    return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + \".\").length);\n}\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n    second: SECOND,\n    seconds: SECOND,\n    minute: MINUTE,\n    minutes: MINUTE,\n    hour: HOUR,\n    hours: HOUR,\n    day: DAY,\n    days: DAY,\n    week: WEEK,\n    weeks: WEEK,\n    month: MONTH,\n    months: MONTH,\n    quarter: QUARTER,\n    quarters: QUARTER,\n    year: YEAR,\n    years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n    const absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return \"second\";\n    } else if (absValue < HOUR) {\n        return \"minute\";\n    } else if (absValue < DAY) {\n        return \"hour\";\n    } else if (absValue < WEEK) {\n        return \"day\";\n    } else if (absValue < MONTH) {\n        return \"week\";\n    } else if (absValue < YEAR) {\n        return \"month\";\n    }\n    return \"year\";\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n    // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n    // will include fractions like '2.1 hours ago'.\n    return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n    let { _cache: cache = initializeConfig.createCache(), _formatters: formatters = initializeConfig.createIntlFormatters(cache), formats, locale, now: globalNow, onError = initializeConfig.defaultOnError, timeZone: globalTimeZone } = _ref;\n    function applyTimeZone(options) {\n        var _options;\n        if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n            if (globalTimeZone) {\n                options = {\n                    ...options,\n                    timeZone: globalTimeZone\n                };\n            } else {\n                onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#time-zone\"));\n            }\n        }\n        return options;\n    }\n    function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n        let options;\n        if (typeof formatOrOptions === \"string\") {\n            const formatName = formatOrOptions;\n            options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n            if (!options) {\n                const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\"));\n                onError(error);\n                throw error;\n            }\n        } else {\n            options = formatOrOptions;\n        }\n        return options;\n    }\n    function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n        let options;\n        try {\n            options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n        } catch (_unused) {\n            return getFallback();\n        }\n        try {\n            return formatter(options);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return getFallback();\n        }\n    }\n    function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */ value, /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).format(value);\n        }, ()=>String(value));\n    }\n    function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */ start, /** If a number is supplied, this is interpreted as a UTC timestamp. */ end, /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n        }, ()=>[\n                dateTime(start),\n                dateTime(end)\n            ].join(\" – \"));\n    }\n    function number(value, formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, (options)=>formatters.getNumberFormat(locale, options).format(value), ()=>String(value));\n    }\n    function getGlobalNow() {\n        if (globalNow) {\n            return globalNow;\n        } else {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#now\"));\n            return new Date();\n        }\n    }\n    function relativeTime(/** The date time that needs to be formatted. */ date, /** The reference point in time to which `date` will be formatted in relation to.  */ nowOrOptions) {\n        try {\n            let nowDate, unit;\n            const opts = {};\n            if (nowOrOptions instanceof Date || typeof nowOrOptions === \"number\") {\n                nowDate = new Date(nowOrOptions);\n            } else if (nowOrOptions) {\n                if (nowOrOptions.now != null) {\n                    nowDate = new Date(nowOrOptions.now);\n                } else {\n                    nowDate = getGlobalNow();\n                }\n                unit = nowOrOptions.unit;\n                opts.style = nowOrOptions.style;\n                // @ts-expect-error -- Types are slightly outdated\n                opts.numberingSystem = nowOrOptions.numberingSystem;\n            }\n            if (!nowDate) {\n                nowDate = getGlobalNow();\n            }\n            const dateDate = new Date(date);\n            const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n            if (!unit) {\n                unit = resolveRelativeTimeUnit(seconds);\n            }\n            // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n            // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n            // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n            // not desired, as the given dates might cross a threshold were the\n            // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n            // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n            // case. By using `always` we can ensure correct output. The only exception\n            // is the formatting of times <1 second as \"now\".\n            opts.numeric = unit === \"second\" ? \"auto\" : \"always\";\n            const value = calculateRelativeTimeValue(seconds, unit);\n            return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return String(date);\n        }\n    }\n    function list(value, formatOrOptions) {\n        const serializedValue = [];\n        const richValues = new Map();\n        // `formatToParts` only accepts strings, therefore we have to temporarily\n        // replace React elements with a placeholder ID that can be used to retrieve\n        // the original value afterwards.\n        let index = 0;\n        for (const item of value){\n            let serializedItem;\n            if (typeof item === \"object\") {\n                serializedItem = String(index);\n                richValues.set(serializedItem, item);\n            } else {\n                serializedItem = String(item);\n            }\n            serializedValue.push(serializedItem);\n            index++;\n        }\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list, // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n        (options)=>{\n            const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map((part)=>part.type === \"literal\" ? part.value : richValues.get(part.value) || part.value);\n            if (richValues.size > 0) {\n                return result;\n            } else {\n                return result.join(\"\");\n            }\n        }, ()=>String(value));\n    }\n    return {\n        dateTime,\n        number,\n        relativeTime,\n        list,\n        dateTimeRange\n    };\n}\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar core = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/use-intl/dist/development/core.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar react = __webpack_require__(/*! ./react.js */ \"(ssr)/./node_modules/use-intl/dist/development/react.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-0Rl9uR82.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createTranslator = core.createTranslator;\nexports.createFormatter = createFormatter.createFormatter;\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useFormatter = react.useFormatter;\nexports.useMessages = react.useMessages;\nexports.useNow = react.useNow;\nexports.useTimeZone = react.useTimeZone;\nexports.useTranslations = react.useTranslations;\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nfunction _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n        value: t,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n    }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != typeof i) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n}\nlet IntlErrorCode = /*#__PURE__*/ function(IntlErrorCode) {\n    IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n    IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n    IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n    IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n    IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n    IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n    IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n    return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n    constructor(code, originalMessage){\n        let message = code;\n        if (originalMessage) {\n            message += \": \" + originalMessage;\n        }\n        super(message);\n        _defineProperty(this, \"code\", void 0);\n        _defineProperty(this, \"originalMessage\", void 0);\n        this.code = code;\n        if (originalMessage) {\n            this.originalMessage = originalMessage;\n        }\n    }\n}\nfunction joinPath() {\n    for(var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++){\n        parts[_key] = arguments[_key];\n    }\n    return parts.filter(Boolean).join(\".\");\n}\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */ function defaultGetMessageFallback(props) {\n    return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n    console.error(error);\n}\nfunction createCache() {\n    return {\n        dateTime: {},\n        number: {},\n        message: {},\n        relativeTime: {},\n        pluralRules: {},\n        list: {},\n        displayNames: {}\n    };\n}\nfunction createMemoCache(store) {\n    return {\n        create () {\n            return {\n                get (key) {\n                    return store[key];\n                },\n                set (key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction memoFn(fn, cache) {\n    return fastMemoize.memoize(fn, {\n        cache: createMemoCache(cache),\n        strategy: fastMemoize.strategies.variadic\n    });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n    return memoFn(function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return new ConstructorFn(...args);\n    }, cache);\n}\nfunction createIntlFormatters(cache) {\n    const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n    const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n    const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n    const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n    const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n    const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n    return {\n        getDateTimeFormat,\n        getNumberFormat,\n        getPluralRules,\n        getRelativeTimeFormat,\n        getListFormat,\n        getDisplayNames\n    };\n}\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n    Object.entries(messages).forEach((_ref)=>{\n        let [key, messageOrMessages] = _ref;\n        if (key.includes(\".\")) {\n            let keyLabel = key;\n            if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n            invalidKeyLabels.push(keyLabel);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (messageOrMessages != null && typeof messageOrMessages === \"object\") {\n            validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n        }\n    });\n}\nfunction validateMessages(messages, onError) {\n    const invalidKeyLabels = [];\n    validateMessagesSegment(messages, invalidKeyLabels);\n    if (invalidKeyLabels.length > 0) {\n        onError(new IntlError(IntlErrorCode.INVALID_KEY, 'Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid '.concat(invalidKeyLabels.length === 1 ? \"key\" : \"keys\", \": \").concat(invalidKeyLabels.join(\", \"), '\\n\\nIf you\\'re migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \"lodash\";\\n\\nconst input = {\\n  \"one.one\": \"1.1\",\\n  \"one.two\": \"1.2\",\\n  \"two.one.one\": \"2.1.1\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \"one\": {\\n//     \"one\": \"1.1\",\\n//     \"two\": \"1.2\"\\n//   },\\n//   \"two\": {\\n//     \"one\": {\\n//       \"one\": \"2.1.1\"\\n//     }\\n//   }\\n// }\\n')));\n    }\n}\n/**\n * Enhances the incoming props with defaults.\n */ function initializeConfig(_ref) {\n    let { getMessageFallback, messages, onError, ...rest } = _ref;\n    const finalOnError = onError || defaultOnError;\n    const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n    {\n        if (messages) {\n            validateMessages(messages, finalOnError);\n        }\n    }\n    return {\n        ...rest,\n        messages,\n        onError: finalOnError,\n        getMessageFallback: finalGetMessageFallback\n    };\n}\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-0Rl9uR82.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nlet hasWarnedForMissingTimezone = false;\nconst isServer = \"undefined\" === \"undefined\";\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n    const { cache, defaultTranslationValues, formats: globalFormats, formatters, getMessageFallback, locale, onError, timeZone } = _useLocale.useIntlContext();\n    // The `namespacePrefix` is part of the type system.\n    // See the comment in the hook invocation.\n    const allMessages = allMessagesPrefixed[namespacePrefix];\n    const namespace = createFormatter.resolveNamespace(namespacePrefixed, namespacePrefix);\n    if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        hasWarnedForMissingTimezone = true;\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"There is no `timeZone` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl-docs.vercel.app/docs/configuration#time-zone\"));\n    }\n    const translate = React.useMemo(()=>createFormatter.createBaseTranslator({\n            cache,\n            formatters,\n            getMessageFallback,\n            messages: allMessages,\n            defaultTranslationValues,\n            namespace,\n            onError,\n            formats: globalFormats,\n            locale,\n            timeZone\n        }), [\n        cache,\n        formatters,\n        getMessageFallback,\n        allMessages,\n        defaultTranslationValues,\n        namespace,\n        onError,\n        globalFormats,\n        locale,\n        timeZone\n    ]);\n    return translate;\n}\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */ function useTranslations(namespace) {\n    const context = _useLocale.useIntlContext();\n    const messages = context.messages;\n    // We have to wrap the actual hook so the type inference for the optional\n    // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n    // The prefix (\"!\") is arbitrary.\n    return useTranslationsImpl({\n        \"!\": messages\n    }, // @ts-expect-error\n    namespace ? \"!.\".concat(namespace) : \"!\", \"!\");\n}\nfunction getNow() {\n    return new Date();\n}\n/**\n * Reading the current date via `new Date()` in components should be avoided, as\n * it causes components to be impure and can lead to flaky tests. Instead, this\n * hook can be used.\n *\n * By default, it returns the time when the component mounts. If `updateInterval`\n * is specified, the value will be updated based on the interval.\n *\n * You can however also return a static value from this hook, if you\n * configure the `now` parameter on the context provider. Note however,\n * that if `updateInterval` is configured in this case, the component\n * will initialize with the global value, but will afterwards update\n * continuously based on the interval.\n *\n * For unit tests, this can be mocked to a constant value. For end-to-end\n * testing, an environment parameter can be passed to the `now` parameter\n * of the provider to mock this to a static value.\n */ function useNow(options) {\n    const updateInterval = options === null || options === void 0 ? void 0 : options.updateInterval;\n    const { now: globalNow } = _useLocale.useIntlContext();\n    const [now, setNow] = React.useState(globalNow || getNow());\n    React.useEffect(()=>{\n        if (!updateInterval) return;\n        const intervalId = setInterval(()=>{\n            setNow(getNow());\n        }, updateInterval);\n        return ()=>{\n            clearInterval(intervalId);\n        };\n    }, [\n        globalNow,\n        updateInterval\n    ]);\n    return updateInterval == null && globalNow ? globalNow : now;\n}\nfunction useTimeZone() {\n    return _useLocale.useIntlContext().timeZone;\n}\nfunction useMessages() {\n    const context = _useLocale.useIntlContext();\n    if (!context.messages) {\n        throw new Error(\"No messages found. Have you configured them correctly? See https://next-intl-docs.vercel.app/docs/configuration#messages\");\n    }\n    return context.messages;\n}\nfunction useFormatter() {\n    const { formats, formatters, locale, now: globalNow, onError, timeZone } = _useLocale.useIntlContext();\n    return React.useMemo(()=>createFormatter.createFormatter({\n            formats,\n            locale,\n            now: globalNow,\n            onError,\n            timeZone,\n            _formatters: formatters\n        }), [\n        formats,\n        formatters,\n        globalNow,\n        locale,\n        onError,\n        timeZone\n    ]);\n}\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useLocale = _useLocale.useLocale;\nexports.useFormatter = useFormatter;\nexports.useMessages = useMessages;\nexports.useNow = useNow;\nexports.useTimeZone = useTimeZone;\nexports.useTranslations = useTranslations;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/use-intl/dist/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./development/index.js */ \"(ssr)/./node_modules/use-intl/dist/development/index.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLElBQUlBLEtBQXlCLEVBQWMsRUFFMUMsTUFBTTtJQUNMQyw2SEFBeUI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvaW5kZXguanM/N2IxMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL2luZGV4LmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGV2ZWxvcG1lbnQvaW5kZXguanMnKTtcbn1cbiJdLCJuYW1lcyI6WyJwcm9jZXNzIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(rsc)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n    let { messages, namespace, ...rest } = _ref;\n    // The `namespacePrefix` is part of the type system.\n    // See the comment in the function invocation.\n    messages = messages[namespacePrefix];\n    namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n    return createFormatter.createBaseTranslator({\n        ...rest,\n        messages,\n        namespace\n    });\n}\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */ function createTranslator(_ref) {\n    let { _cache = initializeConfig.createCache(), _formatters = initializeConfig.createIntlFormatters(_cache), getMessageFallback = initializeConfig.defaultGetMessageFallback, messages, namespace, onError = initializeConfig.defaultOnError, ...rest } = _ref;\n    // We have to wrap the actual function so the type inference for the optional\n    // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n    // The prefix (\"!\") is arbitrary.\n    return createTranslatorImpl({\n        ...rest,\n        onError,\n        cache: _cache,\n        formatters: _formatters,\n        getMessageFallback,\n        // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n        messages: {\n            \"!\": messages\n        },\n        namespace: namespace ? \"!.\".concat(namespace) : \"!\"\n    }, \"!\");\n}\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar IntlMessageFormat__default = /*#__PURE__*/ _interopDefault(IntlMessageFormat);\nfunction setTimeZoneInFormats(formats, timeZone) {\n    if (!formats) return formats;\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    return Object.keys(formats).reduce((acc, key)=>{\n        acc[key] = {\n            timeZone,\n            ...formats[key]\n        };\n        return acc;\n    }, {});\n}\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */ function convertFormatsToIntlMessageFormat(formats, timeZone) {\n    const formatsWithTimeZone = timeZone ? {\n        ...formats,\n        dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n    } : formats;\n    const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n    const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n    const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n    const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n    return {\n        ...formatsWithTimeZone,\n        date: {\n            ...defaultDateFormats,\n            ...formatsWithTimeZone.dateTime\n        },\n        time: {\n            ...defaultTimeFormats,\n            ...formatsWithTimeZone.dateTime\n        }\n    };\n}\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n    const getMessageFormat = initializeConfig.memoFn(function() {\n        return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n            formatters: intlFormatters,\n            ...arguments.length <= 3 ? undefined : arguments[3]\n        });\n    }, cache.message);\n    return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n    const fullKey = initializeConfig.joinPath(namespace, key);\n    if (!messages) {\n        throw new Error(\"No messages available at `\".concat(namespace, \"`.\"));\n    }\n    let message = messages;\n    key.split(\".\").forEach((part)=>{\n        const next = message[part];\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (part == null || next == null) {\n            throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\"));\n        }\n        message = next;\n    });\n    return message;\n}\nfunction prepareTranslationValues(values) {\n    if (Object.keys(values).length === 0) return undefined;\n    // Workaround for https://github.com/formatjs/formatjs/issues/1467\n    const transformedValues = {};\n    Object.keys(values).forEach((key)=>{\n        let index = 0;\n        const value = values[key];\n        let transformed;\n        if (typeof value === \"function\") {\n            transformed = (chunks)=>{\n                const result = value(chunks);\n                return /*#__PURE__*/ React.isValidElement(result) ? /*#__PURE__*/ React.cloneElement(result, {\n                    key: key + index++\n                }) : result;\n            };\n        } else {\n            transformed = value;\n        }\n        transformedValues[key] = transformed;\n    });\n    return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n    let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n    try {\n        if (!messages) {\n            throw new Error(\"No messages were configured on the provider.\");\n        }\n        const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (!retrievedMessages) {\n            throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\"));\n        }\n        return retrievedMessages;\n    } catch (error) {\n        const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        onError(intlError);\n        return intlError;\n    }\n}\nfunction getPlainMessage(candidate, values) {\n    if (values) return undefined;\n    const unescapedMessage = candidate.replace(/'([{}])/gi, \"$1\");\n    // Placeholders can be in the message if there are default values,\n    // or if the user has forgotten to provide values. In the latter\n    // case we need to compile the message to receive an error.\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n        return unescapedMessage;\n    }\n    return undefined;\n}\nfunction createBaseTranslator(config) {\n    const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n    return createBaseTranslatorImpl({\n        ...config,\n        messagesOrError\n    });\n}\nfunction createBaseTranslatorImpl(_ref) {\n    let { cache, defaultTranslationValues, formats: globalFormats, formatters, getMessageFallback = initializeConfig.defaultGetMessageFallback, locale, messagesOrError, namespace, onError, timeZone } = _ref;\n    const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n    function getFallbackFromErrorAndNotify(key, code, message) {\n        const error = new initializeConfig.IntlError(code, message);\n        onError(error);\n        return getMessageFallback({\n            error,\n            key,\n            namespace\n        });\n    }\n    function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        let message;\n        try {\n            message = resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n        if (typeof message === \"object\") {\n            let code, errorMessage;\n            if (Array.isArray(message)) {\n                code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl-docs.vercel.app/docs/usage/messages#arrays-of-messages\");\n                }\n            } else {\n                code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n                {\n                    errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl-docs.vercel.app/docs/usage/messages#structuring-messages\");\n                }\n            }\n            return getFallbackFromErrorAndNotify(key, code, errorMessage);\n        }\n        let messageFormat;\n        // Hot path that avoids creating an `IntlMessageFormat` instance\n        const plainMessage = getPlainMessage(message, values);\n        if (plainMessage) return plainMessage;\n        // Lazy init the message formatter for better tree\n        // shaking in case message formatting is not used.\n        if (!formatters.getMessageFormat) {\n            formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n        }\n        try {\n            messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n                ...globalFormats,\n                ...formats\n            }, timeZone), {\n                formatters: {\n                    ...formatters,\n                    getDateTimeFormat (locales, options) {\n                        // Workaround for https://github.com/formatjs/formatjs/issues/4279\n                        return formatters.getDateTimeFormat(locales, {\n                            timeZone,\n                            ...options\n                        });\n                    }\n                }\n            });\n        } catch (error) {\n            const thrownError = error;\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + (\"originalMessage\" in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : \"\"));\n        }\n        try {\n            const formattedMessage = messageFormat.format(// @ts-expect-error `intl-messageformat` expects a different format\n            // for rich text elements since a recent minor update. This\n            // needs to be evaluated in detail, possibly also in regards\n            // to be able to format to parts.\n            prepareTranslationValues({\n                ...defaultTranslationValues,\n                ...values\n            }));\n            if (formattedMessage == null) {\n                throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\"));\n            }\n            // Limit the function signature to return strings or React elements\n            return /*#__PURE__*/ React.isValidElement(formattedMessage) || // Arrays of React elements\n            Array.isArray(formattedMessage) || typeof formattedMessage === \"string\" ? formattedMessage : String(formattedMessage);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n        }\n    }\n    function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */ key, /** Key value pairs for values to interpolate into the message. */ values, /** Provide custom formats for numbers, dates and times. */ formats) {\n        const result = translateBaseFn(key, values, formats);\n        if (typeof result !== \"string\") {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : \"messages\", \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\"));\n        }\n        return result;\n    }\n    translateFn.rich = translateBaseFn;\n    // Augment `translateBaseFn` to return plain strings\n    translateFn.markup = (key, values, formats)=>{\n        const result = translateBaseFn(key, // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n        // of `RichTranslationValues` but TypeScript isn't smart enough here.\n        values, formats);\n        // When only string chunks are provided to the parser, only\n        // strings should be returned here. Note that we need a runtime\n        // check for this since rich text values could be accidentally\n        // inherited from `defaultTranslationValues`.\n        if (typeof result !== \"string\") {\n            const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n            onError(error);\n            return getMessageFallback({\n                error,\n                key,\n                namespace\n            });\n        }\n        return result;\n    };\n    translateFn.raw = (key)=>{\n        if (hasMessagesError) {\n            // We have already warned about this during render\n            return getMessageFallback({\n                error: messagesOrError,\n                key,\n                namespace\n            });\n        }\n        const messages = messagesOrError;\n        try {\n            return resolvePath(locale, messages, key, namespace);\n        } catch (error) {\n            return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n        }\n    };\n    translateFn.has = (key)=>{\n        if (hasMessagesError) {\n            return false;\n        }\n        try {\n            resolvePath(locale, messagesOrError, key, namespace);\n            return true;\n        } catch (_unused) {\n            return false;\n        }\n    };\n    return translateFn;\n}\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */ function resolveNamespace(namespace, namespacePrefix) {\n    return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + \".\").length);\n}\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n    second: SECOND,\n    seconds: SECOND,\n    minute: MINUTE,\n    minutes: MINUTE,\n    hour: HOUR,\n    hours: HOUR,\n    day: DAY,\n    days: DAY,\n    week: WEEK,\n    weeks: WEEK,\n    month: MONTH,\n    months: MONTH,\n    quarter: QUARTER,\n    quarters: QUARTER,\n    year: YEAR,\n    years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n    const absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return \"second\";\n    } else if (absValue < HOUR) {\n        return \"minute\";\n    } else if (absValue < DAY) {\n        return \"hour\";\n    } else if (absValue < WEEK) {\n        return \"day\";\n    } else if (absValue < MONTH) {\n        return \"week\";\n    } else if (absValue < YEAR) {\n        return \"month\";\n    }\n    return \"year\";\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n    // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n    // will include fractions like '2.1 hours ago'.\n    return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n    let { _cache: cache = initializeConfig.createCache(), _formatters: formatters = initializeConfig.createIntlFormatters(cache), formats, locale, now: globalNow, onError = initializeConfig.defaultOnError, timeZone: globalTimeZone } = _ref;\n    function applyTimeZone(options) {\n        var _options;\n        if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n            if (globalTimeZone) {\n                options = {\n                    ...options,\n                    timeZone: globalTimeZone\n                };\n            } else {\n                onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#time-zone\"));\n            }\n        }\n        return options;\n    }\n    function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n        let options;\n        if (typeof formatOrOptions === \"string\") {\n            const formatName = formatOrOptions;\n            options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n            if (!options) {\n                const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\"));\n                onError(error);\n                throw error;\n            }\n        } else {\n            options = formatOrOptions;\n        }\n        return options;\n    }\n    function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n        let options;\n        try {\n            options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n        } catch (_unused) {\n            return getFallback();\n        }\n        try {\n            return formatter(options);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return getFallback();\n        }\n    }\n    function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */ value, /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).format(value);\n        }, ()=>String(value));\n    }\n    function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */ start, /** If a number is supplied, this is interpreted as a UTC timestamp. */ end, /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */ formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, (options)=>{\n            options = applyTimeZone(options);\n            return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n        }, ()=>[\n                dateTime(start),\n                dateTime(end)\n            ].join(\" – \"));\n    }\n    function number(value, formatOrOptions) {\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, (options)=>formatters.getNumberFormat(locale, options).format(value), ()=>String(value));\n    }\n    function getGlobalNow() {\n        if (globalNow) {\n            return globalNow;\n        } else {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#now\"));\n            return new Date();\n        }\n    }\n    function relativeTime(/** The date time that needs to be formatted. */ date, /** The reference point in time to which `date` will be formatted in relation to.  */ nowOrOptions) {\n        try {\n            let nowDate, unit;\n            const opts = {};\n            if (nowOrOptions instanceof Date || typeof nowOrOptions === \"number\") {\n                nowDate = new Date(nowOrOptions);\n            } else if (nowOrOptions) {\n                if (nowOrOptions.now != null) {\n                    nowDate = new Date(nowOrOptions.now);\n                } else {\n                    nowDate = getGlobalNow();\n                }\n                unit = nowOrOptions.unit;\n                opts.style = nowOrOptions.style;\n                // @ts-expect-error -- Types are slightly outdated\n                opts.numberingSystem = nowOrOptions.numberingSystem;\n            }\n            if (!nowDate) {\n                nowDate = getGlobalNow();\n            }\n            const dateDate = new Date(date);\n            const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n            if (!unit) {\n                unit = resolveRelativeTimeUnit(seconds);\n            }\n            // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n            // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n            // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n            // not desired, as the given dates might cross a threshold were the\n            // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n            // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n            // case. By using `always` we can ensure correct output. The only exception\n            // is the formatting of times <1 second as \"now\".\n            opts.numeric = unit === \"second\" ? \"auto\" : \"always\";\n            const value = calculateRelativeTimeValue(seconds, unit);\n            return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n        } catch (error) {\n            onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n            return String(date);\n        }\n    }\n    function list(value, formatOrOptions) {\n        const serializedValue = [];\n        const richValues = new Map();\n        // `formatToParts` only accepts strings, therefore we have to temporarily\n        // replace React elements with a placeholder ID that can be used to retrieve\n        // the original value afterwards.\n        let index = 0;\n        for (const item of value){\n            let serializedItem;\n            if (typeof item === \"object\") {\n                serializedItem = String(index);\n                richValues.set(serializedItem, item);\n            } else {\n                serializedItem = String(item);\n            }\n            serializedValue.push(serializedItem);\n            index++;\n        }\n        return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list, // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n        (options)=>{\n            const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map((part)=>part.type === \"literal\" ? part.value : richValues.get(part.value) || part.value);\n            if (richValues.size > 0) {\n                return result;\n            } else {\n                return result.join(\"\");\n            }\n        }, ()=>String(value));\n    }\n    return {\n        dateTime,\n        number,\n        relativeTime,\n        list,\n        dateTimeRange\n    };\n}\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\nfunction _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n        value: t,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n    }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != typeof i) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n}\nlet IntlErrorCode = /*#__PURE__*/ function(IntlErrorCode) {\n    IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n    IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n    IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n    IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n    IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n    IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n    IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n    return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n    constructor(code, originalMessage){\n        let message = code;\n        if (originalMessage) {\n            message += \": \" + originalMessage;\n        }\n        super(message);\n        _defineProperty(this, \"code\", void 0);\n        _defineProperty(this, \"originalMessage\", void 0);\n        this.code = code;\n        if (originalMessage) {\n            this.originalMessage = originalMessage;\n        }\n    }\n}\nfunction joinPath() {\n    for(var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++){\n        parts[_key] = arguments[_key];\n    }\n    return parts.filter(Boolean).join(\".\");\n}\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */ function defaultGetMessageFallback(props) {\n    return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n    console.error(error);\n}\nfunction createCache() {\n    return {\n        dateTime: {},\n        number: {},\n        message: {},\n        relativeTime: {},\n        pluralRules: {},\n        list: {},\n        displayNames: {}\n    };\n}\nfunction createMemoCache(store) {\n    return {\n        create () {\n            return {\n                get (key) {\n                    return store[key];\n                },\n                set (key, value) {\n                    store[key] = value;\n                }\n            };\n        }\n    };\n}\nfunction memoFn(fn, cache) {\n    return fastMemoize.memoize(fn, {\n        cache: createMemoCache(cache),\n        strategy: fastMemoize.strategies.variadic\n    });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n    return memoFn(function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        return new ConstructorFn(...args);\n    }, cache);\n}\nfunction createIntlFormatters(cache) {\n    const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n    const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n    const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n    const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n    const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n    const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n    return {\n        getDateTimeFormat,\n        getNumberFormat,\n        getPluralRules,\n        getRelativeTimeFormat,\n        getListFormat,\n        getDisplayNames\n    };\n}\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n    Object.entries(messages).forEach((_ref)=>{\n        let [key, messageOrMessages] = _ref;\n        if (key.includes(\".\")) {\n            let keyLabel = key;\n            if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n            invalidKeyLabels.push(keyLabel);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n        if (messageOrMessages != null && typeof messageOrMessages === \"object\") {\n            validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n        }\n    });\n}\nfunction validateMessages(messages, onError) {\n    const invalidKeyLabels = [];\n    validateMessagesSegment(messages, invalidKeyLabels);\n    if (invalidKeyLabels.length > 0) {\n        onError(new IntlError(IntlErrorCode.INVALID_KEY, 'Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid '.concat(invalidKeyLabels.length === 1 ? \"key\" : \"keys\", \": \").concat(invalidKeyLabels.join(\", \"), '\\n\\nIf you\\'re migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \"lodash\";\\n\\nconst input = {\\n  \"one.one\": \"1.1\",\\n  \"one.two\": \"1.2\",\\n  \"two.one.one\": \"2.1.1\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \"one\": {\\n//     \"one\": \"1.1\",\\n//     \"two\": \"1.2\"\\n//   },\\n//   \"two\": {\\n//     \"one\": {\\n//       \"one\": \"2.1.1\"\\n//     }\\n//   }\\n// }\\n')));\n    }\n}\n/**\n * Enhances the incoming props with defaults.\n */ function initializeConfig(_ref) {\n    let { getMessageFallback, messages, onError, ...rest } = _ref;\n    const finalOnError = onError || defaultOnError;\n    const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n    {\n        if (messages) {\n            validateMessages(messages, finalOnError);\n        }\n    }\n    return {\n        ...rest,\n        messages,\n        onError: finalOnError,\n        getMessageFallback: finalGetMessageFallback\n    };\n}\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ })

};
;