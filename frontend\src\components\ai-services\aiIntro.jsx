'use client'
import React from 'react'
import { Typography } from '@material-tailwind/react'
import { motion } from "framer-motion";
import AiServices from 'src/assets/ai-service.json'
import { Brain, Zap, LockIcon, BrainCircuit } from "lucide-react";
import <PERSON><PERSON> from 'lottie-react';
import Section from '../home/<USER>';

function AiIntro({ t, animations }) {
    const features = [
        {
            icon: <Brain className="w-6 h-6" />,
            title: t('feature1.title'),
            description: t('feature1.description')
        },
        {
            icon: <Zap className="w-6 h-6" />,
            title: t('feature2.title'),
            description: t('feature2.description')
        },
        {
            icon: <LockIcon className="w-6 h-6" />,
            title: t('feature3.title'),
            description: t('feature3.description')
        }
    ];

    return (
        <Section className="relative overflow-hidden">
            <div className="">
                <div className="relative z-10 flex items-center flex-col lg:flex-row justify-between">
                    {/* Text Content */}
                    <motion.div
                        variants={animations?.fadeInLeft}
                        initial="hidden"
                        animate="visible"
                        className="space-y-8 lg:w-1/2"
                    >
                        <div className="space-y-4">
                            <Typography
                                variant="small"
                                color="blue"
                                className="inline-flex items-center gap-2 font-inter text-indigo-500 py-2 px-4 rounded-full font-light tracking-widest uppercase border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-300"
                            >
                                <BrainCircuit className="w-6 h-6" />
                                {t('title')}
                            </Typography>
                            <Typography
                                variant="h1"
                                className="text-2xl sm:text-4xl lg:text-5xl font-bold leading-tight tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500"
                            >
                                {t('subtitle')}
                            </Typography>
                            <Typography
                                variant="paragraph"
                                className="text-base font-inter text-gray-600"
                            >
                                {t('details')}
                            </Typography>
                        </div>

                        {/* Features Grid */}
                        <div className="grid md:grid-cols-3 gap-6">
                            {features.map((feature, index) => (
                                <div key={index} className="p-4 rounded-xl bg-white shadow-md hover:shadow-lg transition-shadow">
                                    <div className="text-blue-500 mb-3">{feature.icon}</div>
                                    <Typography variant="h6" className="mb-2">
                                        {feature.title}
                                    </Typography>
                                    <Typography variant="small" className="text-gray-600">
                                        {feature.description}
                                    </Typography>
                                </div>
                            ))}
                        </div>
                    </motion.div>

                    {/* Image Section */}
                    <motion.div
                        variants={animations?.fadeInRight}
                        initial="hidden"
                        animate="visible"
                        className="relative w-full md:w-[40%] mx-auto mt-8 lg:mt-0"
                    >
                        <div className="absolute inset-0 rounded-full blur-3xl bg-indigo-200 opacity-20" />
                       <Lottie 
                        animationData={AiServices}
                        loop
                        autoplay
                        className="w-full h-full"
                       />
                    </motion.div>
                </div>
            </div>
        </Section>
    )
}

export default AiIntro;