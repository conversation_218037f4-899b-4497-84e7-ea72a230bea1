const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const messageSchema = new Schema({
  role: { 
    type: String, 
    required: true,
    enum: ['user', 'assistant', 'system']
  },
  content: { 
    type: String, 
    required: true 
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  }
});

const chatConversationSchema = new Schema({
  sessionId: { 
    type: String, 
    required: true, 
    unique: true 
  },
  userInfo: {
    ip: { type: String },
    userAgent: { type: String },
    userId: { type: Schema.Types.ObjectId, ref: 'User' }
  },
  messages: [messageSchema],
  lastActivity: { 
    type: Date, 
    default: Date.now 
  },
  status: { 
    type: String, 
    enum: ['active', 'closed'], 
    default: 'active' 
  }
}, { 
  timestamps: true 
});

const ChatConversation = mongoose.model('ChatConversation', chatConversationSchema);
module.exports = ChatConversation;
