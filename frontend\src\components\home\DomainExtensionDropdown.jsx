import { Select, Option } from "@material-tailwind/react";

const DomainExtensionDropdown = ({ extension, setExtension }) => {
  const extensions = [".ma", ".com", ".org", ".net"];

  return (
    <div className="relative">
    <Select
      value={extension}
      onChange={(value) => setExtension(value)}
      className="!border-gray-300 !rounded-lg focus:!border-indigo-500 focus:!ring-2 focus:!ring-indigo-200" // Added w-24
      labelProps={{
        className: "hidden",
      }}
      containerProps={{
        className: "min-w-32",
      }}
      menuProps={{
        className: "z-[60] mt-1 border border-gray-200 rounded-lg shadow-lg", // Ensure dropdown appears above other elements
      }}
      arrowProps={{
        className: "text-gray-500 hover:text-indigo-600", // Customize dropdown arrow
      }}
    >
      {extensions.map((ext) => (
        <Option
          key={ext}
          value={ext}
          className={`hover:bg-indigo-50 ${
            extension === ext ? "bg-indigo-100 text-indigo-700 font-medium" : ""
          }`}
        >
          {ext}
        </Option>
      ))}
    </Select>
    </div>
  );
};

export default DomainExtensionDropdown;