"use client";
import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Card,
  CardBody,
  Typography,
  Input,
  Select,
  Option,
  Button,
  Textarea,
  Spinner,
} from "@material-tailwind/react";
import ticketService from "../../../../services/ticketService";
import { toast } from "react-toastify";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "../../../../context/AuthContext";
import { setServerMedia } from "../../../../helpers/helpers";
import { useTranslations } from "next-intl";
import { FaFileUpload } from "react-icons/fa";
import { ExternalLink } from "lucide-react";

// Constants for validation and configuration
const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
const MAX_FILES = 4;
const ALLOWED_FILE_TYPES = ["image/png", "image/jpeg", "image/jpg"];

// Reusable Form Input Component with Error Handling
const FormInput = ({
  label,
  type = "text",
  name,
  value,
  onChange,
  containerProps,
  disabled,
  error,
}) => (
  <div className="space-y-1">
    <Input
      label={label}
      type={type}
      name={name}
      value={value}
      onChange={onChange}
      containerProps={containerProps}
      disabled={disabled}
      error={!!error}
      className="rounded-md shadow-sm"
    />
    {error && <p className="text-red-500 text-xs">{error}</p>}
  </div>
);

// Reusable Select Component with Error Handling
const FormSelect = ({ label, name, value, onChange, options, error }) => (
  <div className="space-y-1">
    <Select
      label={label}
      value={value}
      onChange={(val) => onChange(name, val)}
      error={!!error}
      className="rounded-md shadow-sm"
    >
      {options.map((option) => (
        <Option key={option.value} value={option.value}>
          {option.label}
        </Option>
      ))}
    </Select>
    {error && <p className="text-red-500 text-xs">{error}</p>}
  </div>
);

// Reusable Textarea Component with Error Handling
const FormTextarea = ({
  label,
  name,
  value,
  onChange,
  containerProps,
  error,
}) => (
  <div className="space-y-1">
    <Textarea
      label={label}
      rows={8}
      name={name}
      value={value}
      onChange={onChange}
      containerProps={containerProps}
      error={!!error}
      className="rounded-md shadow-sm"
    />
    {error && <p className="text-red-500 text-xs">{error}</p>}
  </div>
);

// Reusable Image Preview Component
const ImagePreview = ({ images, onRemove, label, isExisting = false }) => {
  const t = useTranslations("client");
  return images.length > 0 ? (
    <>
      <p className="text-sm font-medium text-gray-700">{label}</p>
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
        {images.map((image, index) => (
          <div
            key={`${isExisting ? "existing" : "new"}-${index}`}
            className="relative"
          >
            <img
              src={
                isExisting ? setServerMedia(image) : URL.createObjectURL(image)
              }
              alt={`${label} ${index}`}
              className="w-full h-24 object-cover rounded-md shadow-md"
            />
            <button
              type="button"
              onClick={() => onRemove(index)}
              className="absolute top-1 right-1 bg-red-500 text-white text-xs font-bold py-1 px-2 rounded-full hover:bg-red-600 transition"
            >
              X
            </button>
          </div>
        ))}
      </div>
    </>
  ) : null;
};

const AddTicketPage = () => {
  const t = useTranslations("client");
  const fileInputRef = useRef(null);
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isEdit = searchParams.get("edit") === "true";
  const ticketId = searchParams.get("id");

  const [formData, setFormData] = useState({
    subject: "",
    service: "none",
    priority: "medium",
    message: "",
    newImages: [],
    existingImages: [],
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Fetch ticket data for edit mode
  useEffect(() => {
    if (isEdit && ticketId) {
      const fetchTicket = async () => {
        setLoading(true);
        try {
          const response = await ticketService.getTicketById(ticketId);
          if (response.status === 200) {
            const ticket = response.data.ticket;
            setFormData({
              subject: ticket.subject,
              service: ticket.service,
              priority: ticket.priority,
              message: ticket.message,
              newImages: [],
              existingImages: ticket.images || [],
            });
          }
        } catch (error) {
          console.error("Error fetching ticket:", error);
          toast.error(t("fetch_ticket_error"));
        } finally {
          setLoading(false);
        }
      };
      fetchTicket();
    }
  }, [isEdit, ticketId, t]);

  // Form validation
  const validateForm = useCallback(() => {
    const newErrors = {};
    const { subject, service, message } = formData;

    if (!subject.trim()) newErrors.subject = t("subject_required");
    if (service === "none") newErrors.service = t("service_required");
    if (!message.trim()) newErrors.message = t("message_required");

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, t]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    const formDataToSend = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (key === "newImages") {
        value.forEach((image) => formDataToSend.append("newImages", image));
      } else if (key === "existingImages" && isEdit && value.length > 0) {
        formDataToSend.append("existingImages", JSON.stringify(value));
      } else if (key !== "existingImages") {
        formDataToSend.append(key, value);
      }
    });

    try {
      const response = isEdit
        ? await ticketService.updateTicket(ticketId, formDataToSend)
        : await ticketService.createTicket(formDataToSend);
      console.log("🚀 ~ handleSubmit ~ response:", response);

      if (response.status === 200 || response.status === 201) {
        toast.success(t(isEdit ? "ticket_updated" : "ticket_created"));
        router.push("/client/support/tickets");
      }
    } catch (error) {
      console.error("Error submitting ticket:", error);
      toast.error(t(isEdit ? "update_ticket_error" : "submit_ticket_error"));
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes and clear errors on change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  // Handle image uploads
  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    console.log(files)

    if (files.length + formData.newImages.length + formData.existingImages.length > MAX_FILES) {
      toast.error(t("max_files_exceeded", { max: MAX_FILES }));
      e.target.value = "";
      return;
    }

    const invalidFiles = files.filter(
      (file) =>
        !ALLOWED_FILE_TYPES.includes(file.type) || file.size > MAX_FILE_SIZE
    );

    if (invalidFiles.length > 0) {
      invalidFiles.forEach((file) => {
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
          toast.error(t("invalid_file_type", { name: file.name }));
        } else {
          toast.error(t("file_too_large", { name: file.name }));
        }
      });
      e.target.value = "";
      return;
    }

    setFormData((prev) => ({
      ...prev,
      newImages: [...prev.newImages, ...files],
    }));
    e.target.value = "";
  };

  const handleNewImageRemove = (index) =>
    setFormData((prev) => ({
      ...prev,
      newImages: prev.newImages.filter((_, i) => i !== index),
    }));

  const handleExistingImageRemove = (index) =>
    setFormData((prev) => ({
      ...prev,
      existingImages: prev.existingImages.filter((_, i) => i !== index),
    }));

  const handleButtonClick = () => fileInputRef.current.click();

  return (
    <div className="container mx-auto py-6">
      <Typography className="flex items-center gap-4 mb-6 text-gray-900 text-xl font-inter">
        <ExternalLink className="w-8 h-8" />
        {isEdit ? t("update_support_ticket") : t("open_new_support_ticket")}
      </Typography>
      <Card className="max-w-4xl mx-auto bg-white shadow-lg rounded-lg">
        <CardBody className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormInput
                label={t("name")}
                name="name"
                value={`${user?.firstName} ${user?.lastName}`}
                containerProps={{ className: "min-w-[72px]" }}
                disabled
              />
              <FormInput
                label={t("email_address")}
                name="email"
                value={user?.email}
                containerProps={{ className: "min-w-[72px]" }}
                disabled
              />
            </div>
            <FormInput
              label={t("subject")}
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              containerProps={{ className: "min-w-[72px]" }}
              error={errors.subject}
            />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormSelect
                label={t("related_service")}
                name="service"
                value={formData.service}
                onChange={handleSelectChange}
                options={[
                  { value: "none", label: t("none") },
                  { value: "hosting", label: t("hosting") },
                  { value: "domain", label: t("domain") },
                ]}
                error={errors.service}
              />
              <FormSelect
                label={t("priority")}
                name="priority"
                value={formData.priority}
                onChange={handleSelectChange}
                options={[
                  { value: "low", label: t("low") },
                  { value: "medium", label: t("medium") },
                  { value: "high", label: t("high") },
                  { value: "urgent", label: t("urgent") },
                ]}
              />
            </div>
            <FormTextarea
              label={t("message")}
              name="message"
              value={formData.message}
              onChange={handleChange}
              containerProps={{ className: "min-w-[72px]" }}
              error={errors.message}
            />
            <div className="space-y-4">
              <label className="block text-sm font-medium text-gray-700">
                {t("upload_images")}{" "}
                <span className="text-gray-500 text-xs">
                  ({t("max_files", { max: MAX_FILES })})
                </span>
              </label>
              <button
                type="button"
                onClick={handleButtonClick}
                className="flex items-center justify-center w-40 py-2 px-4 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 transition"
              >
                <FaFileUpload className="mr-2" />
                {t("upload")}
              </button>
              <input
                type="file"
                multiple
                accept={ALLOWED_FILE_TYPES.join(",")}
                onChange={handleImageChange}
                ref={fileInputRef}
                className="hidden"
              />
              <ImagePreview
                images={formData.existingImages}
                onRemove={handleExistingImageRemove}
                label={t("existing_images")}
                isExisting
              />
              <ImagePreview
                images={formData.newImages}
                onRemove={handleNewImageRemove}
                label={t("new_images")}
              />
            </div>
            <Button
              type="submit"
              className="w-full bg-blue-500 hover:bg-blue-600 transition flex items-center justify-center"
              disabled={loading}
            >
              {loading ? <Spinner className="mr-2 h-4 w-4" /> : null}
              {t("submit")}
            </Button>
          </form>
        </CardBody>
      </Card>
    </div>
  );
};

export default AddTicketPage;
