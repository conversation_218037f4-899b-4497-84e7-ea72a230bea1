const { default: mongoose } = require("mongoose");
const User = require("../models/User");
const Otp = require("../models/Otp");

const fs = require("fs").promises;
const path = require("path");

// const PaymentMethod = require("../constants/enums/vendor-payment-method");
const crypto = require("crypto");
const { sendEmail } = require("../routes/sendEmail/sendEmail");
const {
  getEmailOtpVerificationTemplate,
} = require("../routes/sendEmail/emailTemplates");
const { getTranslation } = require("../helpers/helpers");
const AccountState = require("../constants/enums/account-state");

exports.getProfile = async (req, res) => {
  try {
    const userId = req.user._id;
    console.log("userId: " + userId);
    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }

    // First get the user without sensitive fields
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Use the hasPassword method from the model
    const hasPassword = await user.hasPassword();
    console.log("User has password:", hasPassword);

    // Create a user object without sensitive data
    const userData = user.toObject();
    // delete userData.hashed_password;
    // delete userData.salt;

    return res.status(200).json({
      ...userData,
      hasPassword,
    });
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.setImageOfProfile = async (req, res) => {
  try {
    const { id } = req.body;
    console.log("Setting image of profile:", id);

    const profile = await User.findById(id);
    if (!profile) {
      return res.status(404).send({ message: "User not found" });
    }

    const uploadedFile = req.file;
    if (!uploadedFile) {
      return res.status(400).send({ message: "No file uploaded" });
    }

    // const filePath = `public/avatars/${id}.${uploadedFile.originalname.split('.').pop()}`;

    const fileExtension = path.extname(uploadedFile.originalname);
    const uniqueName = `${Date.now()}-${crypto.randomBytes(4).toString("hex")}`;
    const filePath = `public/avatars/${uniqueName}${fileExtension}`;

    console.log("Uploading...", filePath);

    // Save the new file
    await fs.writeFile(filePath, uploadedFile.buffer);

    const fileUrl = filePath.replace("public", "");

    const data = {
      type: "avatar",
      fileUrl,
    };

    return res.send({ message: "Image successfully updated", data });
  } catch (error) {
    console.error("Error setting profile image:", error);
    return res.status(500).send({ message: "INTERNAL SERVER ERROR" });
  }
};

exports.setConfigAccount = async (req, res) => {
  try {
    const { id, isOAuth, password, firstName, lastName, photo } = req.body;
    console.log(password, isOAuth, firstName, lastName, photo);

    // Find the user by ID (without sensitive fields first)
    const user = await User.findById(id);
    if (!user) {
      return res.status(400).send({ message: "NOT FOUND => PR!1" });
    }

    // Check if the OAuth user has a password using the method
    const hasPassword = await user.hasPassword();

    // For non-OAuth users or OAuth users who have already set a password, verify current password
    if (!isOAuth || (isOAuth && hasPassword)) {
      const isFound = await user.authenticate(password);
      console.log("isFound: and isOAuth:", isFound, isOAuth);
      if (!isFound) {
        return res.status(400).send({
          errors: [{ key: "password", msg: req.t("current_password") }],
        });
      }
    } else {
      console.log(
        "OAuth user without password - no password verification needed"
      );
    }

    // Update first name and last name
    if (firstName && firstName != user.firstName) user.firstName = firstName;
    if (lastName && lastName != user.lastName) user.lastName = lastName;

    if (photo && user.photo != photo) {
      if (
        user.photo &&
        !user.photo.startsWith("https://") &&
        user.photo != "/images/user-default-avatar.svg"
      ) {
        await fs.unlink(`public${user.photo}`).catch((err) => {
          console.warn("Error deleting old file  cvx4893:", err.message);
        });
      }
      user.photo = photo;
    }

    // Save the updated user data
    const userModify = await user.save();

    return res.status(200).send({
      success: true,
      message: "User info has been modified successfully",
      data: userModify,
    });
  } catch (error) {
    console.log(error);
    if (error.code == 11000) {
      return res.status(400).send({
        errors: [
          {
            key: "identifiant",
            msg: "Identifiant is used by another account",
          },
        ],
      });
    }
    return res.status(500).send("INTERNAL SERVER ERROR");
  }
};

exports.changePassword = async (req, res) => {
  try {
    const { password, newPassword, repeatNewPassword } = req.body;
    console.log(
      "change password....:",
      password,
      newPassword,
      repeatNewPassword
    );

    if (newPassword != repeatNewPassword) {
      return res.status(400).send({
        errors: [{ key: "newPassword", msg: req.t("password_match_new") }],
      });
    }

    // Fetch user without explicitly selecting hashed_password
    const user = await User.findById(req.body.id);

    if (!user) {
      return res.status(400).send({ message: "NOT FOUND => PR!1" });
    }

    // For non-OAuth users or OAuth users who have already set a password, verify current password
    if (!user.isOAuth || (user.isOAuth && (await user.hasPassword()))) {
      const isFound = await user.authenticate(password);
      console.log("isFound::", isFound);

      if (!isFound) {
        return res.status(400).send({
          errors: [{ key: "password", msg: req.t("current_password") }],
        });
      }
    }

    // Set the new password
    user.password = newPassword;
    await user.save();

    return res.send({
      message: "Password changed successfully",
      user: {
        ...user.toObject(),
        hasPassword: true,
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send("INTERNAL SERVER ERROR");
  }
};

const sendOtpCode = async (userId, newEmail, BACKEND_LANG) => {
  try {
    const otpCode = crypto.randomInt(100000, 999999).toString();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000);
    console.log("otpCode: ", otpCode);
    console.log("expiresAt: ", expiresAt);

    const otp = new Otp({
      user: userId,
      otpCode,
      reason: "Email Change Verification",
      expiresAt,
      toBeVerified: newEmail,
    });

    await otp.save();
    console.log("BACKEND_LANG : ", BACKEND_LANG);

    const data = {
      mailto: newEmail,
      subject: "email_verification",
      favoriteLang: BACKEND_LANG,
    };
    const emailTemplate = getEmailOtpVerificationTemplate(
      BACKEND_LANG,
      otpCode
    );
    await sendEmail(data, emailTemplate);
    return {
      success: true,
      expiresAt,
    };
  } catch (error) {
    console.error(error);
  }
};

exports.changeEmail = async (req, res) => {
  try {
    const { password, newEmail, isOAuth } = req.body;
    const BACKEND_LANG = req.cookies.BACKEND_LANG;
    console.log("BACKEND_LANG : ", req.cookies);
    console.log("Next_Local: ", BACKEND_LANG);

    // Fetch the user with the given ID
    const user = await User.findById(req.body.id);

    if (!user) {
      return res.status(400).send({ message: "NOT FOUND => PR!1" });
    }

    // Check if the new email is already in use
    const foundUser = await User.findOne({ email: newEmail });
    if (foundUser) {
      return res.status(400).send({
        errors: [
          {
            key: "newEmail",
            msg: getTranslation("this_email_is_already_in_use", BACKEND_LANG),
          },
        ],
      });
    }

    // Check if the OAuth user has a password using the model method
    const hasPassword = await user.hasPassword();

    // For non-OAuth users or OAuth users who have already set a password, verify current password
    if (!isOAuth || (user.isOAuth && hasPassword)) {
      const isFound = await user.authenticate(password);
      console.log("Password verification result:", isFound);

      if (!isFound) {
        return res.status(400).send({
          errors: [
            {
              key: "password",
              msg: getTranslation("current_password", BACKEND_LANG),
            },
          ],
        });
      }
    }

    // Send OTP code to the new email
    const { success, expiresAt } = await sendOtpCode(
      user?._id,
      newEmail,
      BACKEND_LANG
    );
    console.log("success: ", success);
    if (success) {
      return res.send({
        success: true,
        expiresAt,
        successMsg: getTranslation("otp_resent_successfully", BACKEND_LANG),
      });
    } else {
      return res.status(500).send("INTERNAL SERVER ERROR");
    }
  } catch (error) {
    console.error(error);
    return res.status(500).send("INTERNAL SERVER ERROR");
  }
};

exports.verifyOtp = async (req, res) => {
  try {
    // const userId = req.auth.id;
    const { otpCode, id } = req.body;
    const BACKEND_LANG = req.cookies.BACKEND_LANG;
    console.log("verifyOtp: ", otpCode, id);
    // Proceed to update the user's email
    const user = await User.findById(id);

    if (!user) {
      console.log("user not found");
      return res.status(400).send({ message: "User not found" });
    }

    const otpRecord = await Otp.findOne({
      user: id,
      otpCode,
      expiresAt: { $gt: new Date(Date.now()) },
      status: "pending",
    });

    if (!otpRecord) {
      return res.status(200).send({
        success: false,
        errorMsg: getTranslation("invalid_or_expired_otp", BACKEND_LANG),
      });
    }

    user.email = otpRecord.toBeVerified;
    user.state = AccountState.VERIFIED;
    await user.save();

    // Mark OTP as verified
    otpRecord.status = AccountState.VERIFIED;
    await otpRecord.save();

    // await Otp.deleteOne({ _id: otpRecord._id });

    return res.status(200).send({
      success: true,
      successMsg: getTranslation("email_changed_successfully", BACKEND_LANG),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send("INTERNAL SERVER ERROR");
  }
};

exports.resendOtpCode = async (req, res) => {
  try {
    // const userId = req.auth.id;
    const { newEmail, id } = req.body;
    const BACKEND_LANG = req.cookies.BACKEND_LANG;

    // Fetch user with hashed_password and salt fields
    const user = await User.findById(id);

    if (!user) {
      return res.status(400).send({ message: "NOT FOUND => PR!1" });
    }

    await sendOtpCode(user?._id, newEmail, BACKEND_LANG);
    // Create an OTP record
    return res.send({
      success: true,
      successMsg: getTranslation("otp_resent_successfully", BACKEND_LANG),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send("INTERNAL SERVER ERROR");
  }
};

exports.removeAccount = async (req, res) => {
  try {
    const { password } = req.body;
    const user = await User.findById(req.body.id);
    if (password.length == 0) {
      return res.status(400).send({
        errors: [{ key: "password", msg: req.t("password_empty_error") }],
      });
    }
    if (!user || !user.authenticate(password)) {
      return res.status(400).send({
        errors: [{ key: "password", msg: req.t("current_password") }],
      });
    }
    // remove account

    return res.send({ message: req.t("delete_data") });
  } catch (error) {
    console.log(error);
    return res.status(500).send(req.t("server_error"));
  }
};

// Controller function to update user's favorite language
exports.updateUserLanguage = async (req, res) => {
  try {
    const { userId, favoriteLang } = req.body;

    // Validate input
    if (!userId || !favoriteLang) {
      return res
        .status(400)
        .json({ message: "User ID and favorite language are required." });
    }

    // Find the user by ID and update their favorite language
    const user = await User.findByIdAndUpdate(
      userId,
      { favoriteLang },
      { new: true } // Return the updated document
    );

    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    res
      .status(200)
      .json({ message: "Favorite language updated successfully.", user });
  } catch (error) {
    console.error("Error updating favorite language:", error);
    res.status(500).json({ message: "Internal server error." });
  }
};

exports.updatePaymentMethod = async (req, res) => {
  try {
    const { userId, paymentMethodType, paymentMethod } = req.body;

    // Validate userId
    if (!userId || !/^[a-fA-F0-9]{24}$/.test(userId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    // Validate paymentMethod
    if (
      paymentMethodType &&
      ![PaymentMethod.BANK_ACCOUNT, PaymentMethod.MTO_ACCOUNT].includes(
        paymentMethodType
      )
    ) {
      return res.status(400).json({ error: "Invalid payment method" });
    }

    // Find and update the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Assuming paymentMethod and paymentMethodId are provided in the request
    if (paymentMethodType && paymentMethod) {
      try {
        // Use the helper function to set the payment method
        user.setPaymentMethod(paymentMethodType, paymentMethod);
      } catch (error) {
        // Handle error if an invalid payment method type is provided
        throw new Error(`Failed to set payment method: ${error.message}`);
      }
    } else {
      console.log("The payment method or payment method type is not specified");
    }

    await user.save();

    res
      .status(200)
      .json({ message: "Payment method updated successfully", user });
  } catch (error) {
    console.error("Error updating payment method:", error);
    res.status(500).json({ error: "Server error" });
  }
};

// Controller function to get the current payment method
exports.getCurrentPaymentMethod = async (req, res) => {
  try {
    // Extract userId from request query
    const { userId } = req.query;

    // Validate userId
    if (!userId || !/^[a-fA-F0-9]{24}$/.test(userId)) {
      return res.status(400).json({ error: "Invalid user ID" });
    }

    // Find the user first
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Populate paymentMethod based on paymentMethodType
    const populatedUser = await User.findById(userId).populate({
      path: "paymentMethod",
      model: user.paymentMethodType, // Dynamically choose the model (BankAccount or MtoAccount)
    });

    // Return the user's payment method
    res.status(200).json({
      paymentMethodType: populatedUser.paymentMethodType,
      paymentMethod: populatedUser.paymentMethod,
    });
  } catch (error) {
    console.error("Error retrieving payment method:", error);
    res.status(500).json({ error: "Server error" });
  }
};

// Get billing info
exports.getBillingInfo = async (req, res) => {
  const { userId } = req.params;
  try {
    const user = await User.findById(userId).select("billingInfo");
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json(user.billingInfo);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "An unexpected server error occurred" });
  }
};

// Update billing info
exports.updateBillingInfo = async (req, res) => {
  // Get userId from either req.user or req.body
  const userId = req.user?._id || req.body.userId;
  const billingInfo = req.body;
  console.log("billingInfo: ", billingInfo);
  console.log("userId: ", userId);

  // Remove userId from billingInfo if it exists
  if (billingInfo.userId) {
    delete billingInfo.userId;
  }

  try {
    const user = await User.findByIdAndUpdate(
      userId,
      { billingInfo },
      { new: true, runValidators: true }
    ).select("billingInfo");

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json(user.billingInfo);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "An unexpected server error occurred" });
  }
};

// Delete billing info
exports.deleteBillingInfo = async (req, res) => {
  const { userId } = req.params;
  try {
    const user = await User.findByIdAndUpdate(
      userId,
      { $unset: { billingInfo: 1 } },
      { new: true }
    ).select("billingInfo");

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.status(200).json({ message: "Billing info deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "An unexpected server error occurred" });
  }
};
