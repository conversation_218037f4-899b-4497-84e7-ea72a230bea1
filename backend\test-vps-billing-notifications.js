/**
 * Test script for VPS Billing Notifications
 * Creates test VPS instances with upcoming billing dates and tests the notification system
 */

const mongoose = require("mongoose");
require("dotenv").config();

// Import models
const VPSInstance = require("./models/VPSInstance");
const User = require("./models/User");
const NotificationSetting = require("./models/NotificationSetting");
const Order = require("./models/Order");
const SubOrder = require("./models/SubOrder");

// Import the VPS billing service
const vpsBillingService = require("./services/vpsBillingNotificationService");

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ Error connecting to MongoDB:", error);
    process.exit(1);
  }
}

async function createTestUser() {
  try {
    // Check if test user already exists
    let testUser = await User.findOne({
      email: "<EMAIL>",
    });

    if (!testUser) {
      testUser = new User({
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        password: "hashedpassword123",
        role: "CUSTOMER",
        state: "VERIFIED",
        isVerified: true,
      });
      await testUser.save();
      console.log("✅ Created test user");
    } else {
      console.log("✅ Test user already exists");
    }

    return testUser;
  } catch (error) {
    console.error("❌ Error creating test user:", error);
    throw error;
  }
}

async function createTestOrders(userId) {
  try {
    // Clean up existing test orders
    await Order.deleteMany({
      orderNumber: { $in: ["TEST-ORDER-1", "TEST-ORDER-2", "TEST-ORDER-3"] },
    });
    await SubOrder.deleteMany({
      orderNumber: {
        $in: ["TEST-SUBORDER-1", "TEST-SUBORDER-2", "TEST-SUBORDER-3"],
      },
    });

    // Create test orders
    const testOrders = [];
    const testSubOrders = [];

    for (let i = 1; i <= 3; i++) {
      const order = new Order({
        orderNumber: `TEST-ORDER-${i}`,
        user: userId,
        status: "completed",
        totalAmount: 46 * i,
        currency: "MAD",
        paymentMethod: "stripe",
        paymentStatus: "paid",
      });
      await order.save();
      testOrders.push(order);

      const subOrder = new SubOrder({
        orderNumber: `TEST-SUBORDER-${i}`,
        order: order._id,
        user: userId,
        status: "completed",
        price: 46 * i,
        period: 1,
      });
      await subOrder.save();
      testSubOrders.push(subOrder);
    }

    console.log(
      `✅ Created ${testOrders.length} test orders and ${testSubOrders.length} test suborders`
    );
    return { orders: testOrders, subOrders: testSubOrders };
  } catch (error) {
    console.error("❌ Error creating test orders:", error);
    throw error;
  }
}

async function createTestVPSInstances(userId, orders, subOrders) {
  try {
    // Clean up existing test instances
    await VPSInstance.deleteMany({
      instanceId: { $in: ["test-vps-1", "test-vps-2", "test-vps-3"] },
    });

    const now = new Date();

    // Create VPS instances with different billing dates
    const testInstances = [
      {
        instanceId: "test-vps-1",
        user: userId,
        displayName: "Test VPS 1 - Due Tomorrow",
        config: {
          name: "Test VPS 1",
          provider: "contabo",
          planId: "V91",
          region: "EU-WEST-1",
          operatingSystem: "ubuntu-22.04",
          specifications: {
            cpu: 1,
            ram: 4,
            storage: 75,
            bandwidth: 32000,
          },
        },
        status: "running",
        billing: {
          monthlyPrice: 46,
          currency: "MAD",
          billingCycle: "monthly",
          nextBillingDate: new Date(now.getTime() + 24 * 60 * 60 * 1000), // Tomorrow
          lastBilledDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        },
      },
      {
        instanceId: "test-vps-2",
        user: userId,
        displayName: "Test VPS 2 - Due in 3 Days",
        config: {
          name: "Test VPS 2",
          provider: "contabo",
          planId: "V92",
          region: "EU-WEST-1",
          operatingSystem: "ubuntu-22.04",
          specifications: {
            cpu: 2,
            ram: 8,
            storage: 150,
            bandwidth: 32000,
          },
        },
        status: "running",
        billing: {
          monthlyPrice: 92,
          currency: "MAD",
          billingCycle: "monthly",
          nextBillingDate: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000), // In 3 days
          lastBilledDate: new Date(now.getTime() - 27 * 24 * 60 * 60 * 1000), // 27 days ago
        },
      },
      {
        instanceId: "test-vps-3",
        user: userId,
        displayName: "Test VPS 3 - Due in 7 Days",
        config: {
          name: "Test VPS 3",
          provider: "contabo",
          planId: "V93",
          region: "EU-WEST-1",
          operatingSystem: "ubuntu-22.04",
          specifications: {
            cpu: 4,
            ram: 16,
            storage: 300,
            bandwidth: 32000,
          },
        },
        status: "running",
        billing: {
          monthlyPrice: 184,
          currency: "MAD",
          billingCycle: "monthly",
          nextBillingDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // In 7 days
          lastBilledDate: new Date(now.getTime() - 23 * 24 * 60 * 60 * 1000), // 23 days ago
        },
      },
    ];

    const createdInstances = await VPSInstance.insertMany(testInstances);
    console.log(`✅ Created ${createdInstances.length} test VPS instances`);

    // Display the instances
    createdInstances.forEach((instance) => {
      const daysUntilBilling = Math.ceil(
        (instance.billing.nextBillingDate - now) / (24 * 60 * 60 * 1000)
      );
      console.log(
        `   - ${instance.displayName}: ${instance.billing.monthlyPrice} ${instance.billing.currency}, due in ${daysUntilBilling} day(s)`
      );
    });

    return createdInstances;
  } catch (error) {
    console.error("❌ Error creating test VPS instances:", error);
    throw error;
  }
}

async function setupBillingNotificationSettings() {
  try {
    // Create or update VPS billing notification settings
    const settings = await NotificationSetting.findOneAndUpdate(
      { type: "vps_billing" },
      {
        type: "vps_billing",
        enabled: true,
        daysBefore: [7, 3, 1],
        methods: {
          email: true,
          inApp: true,
        },
        schedule: {
          frequency: "daily",
        },
      },
      { upsert: true, new: true }
    );

    console.log("✅ VPS billing notification settings configured");
    console.log(`   - Enabled: ${settings.enabled}`);
    console.log(`   - Days before: ${settings.daysBefore.join(", ")}`);
    console.log(
      `   - Email: ${settings.methods.email}, In-App: ${settings.methods.inApp}`
    );

    return settings;
  } catch (error) {
    console.error("❌ Error setting up billing notification settings:", error);
    throw error;
  }
}

async function testBillingNotifications() {
  try {
    console.log("\n🧪 Testing VPS Billing Notification System");
    console.log("==========================================");

    // Connect to database
    await connectToDatabase();

    // Create test user
    const testUser = await createTestUser();

    // Create test VPS instances
    const testInstances = await createTestVPSInstances(testUser._id);

    // Setup billing notification settings
    await setupBillingNotificationSettings();

    console.log("\n🔔 Triggering manual billing notification check...");

    // Trigger the billing notification check
    await vpsBillingService.triggerManualCheck();

    console.log("\n✅ Test completed successfully!");
    console.log(
      "\n📧 Check your email and in-app notifications for the test notifications."
    );
    console.log(
      "💡 You should receive notifications for VPS instances due in 1, 3, and 7 days."
    );
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("\n🔌 Database connection closed");
  }
}

async function cleanupTestData() {
  try {
    console.log("\n🧹 Cleaning up test data...");

    await connectToDatabase();

    // Remove test VPS instances
    const deletedVPS = await VPSInstance.deleteMany({
      instanceId: { $in: ["test-vps-1", "test-vps-2", "test-vps-3"] },
    });
    console.log(`✅ Deleted ${deletedVPS.deletedCount} test VPS instances`);

    // Remove test user
    const deletedUser = await User.deleteOne({
      email: "<EMAIL>",
    });
    console.log(`✅ Deleted ${deletedUser.deletedCount} test user`);

    console.log("✅ Cleanup completed");
  } catch (error) {
    console.error("❌ Cleanup failed:", error);
  } finally {
    await mongoose.connection.close();
  }
}

// Check command line arguments
const args = process.argv.slice(2);

if (args.includes("--cleanup")) {
  cleanupTestData();
} else {
  testBillingNotifications();
}
