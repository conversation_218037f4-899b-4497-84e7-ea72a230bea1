const RegionPrice = require('../../models/Region');
const Package = require('../../models/Package');
const adminLogger = require('../../utils/adminLogger');

// Obtenir tous les prix des régions avec les détails des packages
const getAllRegionPrices = async (req, res) => {
  try {
    const regionPrices = await RegionPrice.find()
      .populate('packageId', 'name price category')
      .sort({ regionId: 1, packageId: 1 });

    // Grouper par région pour un affichage plus facile
    const groupedByRegion = {};
    regionPrices.forEach(rp => {
      if (!groupedByRegion[rp.regionId]) {
        groupedByRegion[rp.regionId] = [];
      }
      groupedByRegion[rp.regionId].push({
        id: rp._id,
        packageId: rp.packageId._id,
        packageName: rp.packageId.name,
        basePrice: rp.packageId.price,
        additionalPrice: rp.price,
        totalPrice: rp.packageId.price + rp.price,
        category: rp.packageId.category
      });
    });

    res.status(200).json({
      success: true,
      data: groupedByRegion,
      total: regionPrices.length
    });
  } catch (error) {
    console.error('Error fetching region prices:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des prix des régions',
      error: error.message
    });
  }
};

// Obtenir les prix pour une région spécifique
const getRegionPrices = async (req, res) => {
  try {
    const { regionId } = req.params;
    
    const regionPrices = await RegionPrice.find({ regionId })
      .populate('packageId', 'name price category')
      .sort({ packageId: 1 });

    if (!regionPrices.length) {
      return res.status(404).json({
        success: false,
        message: 'Aucun prix trouvé pour cette région'
      });
    }

    const formattedPrices = regionPrices.map(rp => ({
      id: rp._id,
      packageId: rp.packageId._id,
      packageName: rp.packageId.name,
      basePrice: rp.packageId.price,
      additionalPrice: rp.price,
      totalPrice: rp.packageId.price + rp.price,
      category: rp.packageId.category
    }));

    res.status(200).json({
      success: true,
      regionId,
      data: formattedPrices
    });
  } catch (error) {
    console.error('Error fetching region prices:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des prix de la région',
      error: error.message
    });
  }
};

// Créer ou mettre à jour un prix de région
const setRegionPrice = async (req, res) => {
  try {
    const { packageId, regionId, price } = req.body;

    // Validation
    if (!packageId || !regionId || price === undefined) {
      return res.status(400).json({
        success: false,
        message: 'packageId, regionId et price sont requis'
      });
    }

    // Vérifier que le package existe
    const packageExists = await Package.findById(packageId);
    if (!packageExists) {
      return res.status(404).json({
        success: false,
        message: 'Package non trouvé'
      });
    }

    // Créer ou mettre à jour le prix
    const regionPrice = await RegionPrice.findOneAndUpdate(
      { packageId, regionId },
      { price: parseFloat(price) },
      { 
        new: true, 
        upsert: true,
        runValidators: true
      }
    ).populate('packageId', 'name price category');

    // Log admin activity
    await adminLogger.logCreate(req.user.id, 'RegionPrice', regionPrice._id, {
      packageId,
      regionId,
      price: parseFloat(price),
      packageName: packageExists.name
    });

    res.status(200).json({
      success: true,
      message: 'Prix de région mis à jour avec succès',
      data: {
        id: regionPrice._id,
        packageId: regionPrice.packageId._id,
        packageName: regionPrice.packageId.name,
        regionId: regionPrice.regionId,
        price: regionPrice.price,
        totalPrice: regionPrice.packageId.price + regionPrice.price
      }
    });
  } catch (error) {
    console.error('Error setting region price:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du prix',
      error: error.message
    });
  }
};

// Supprimer un prix de région
const deleteRegionPrice = async (req, res) => {
  try {
    const { id } = req.params;

    const regionPrice = await RegionPrice.findById(id)
      .populate('packageId', 'name');

    if (!regionPrice) {
      return res.status(404).json({
        success: false,
        message: 'Prix de région non trouvé'
      });
    }

    // Log admin activity before deletion
    await adminLogger.logDelete(req.user.id, 'RegionPrice', regionPrice._id, {
      packageId: regionPrice.packageId._id,
      packageName: regionPrice.packageId.name,
      regionId: regionPrice.regionId,
      price: regionPrice.price
    });

    await RegionPrice.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Prix de région supprimé avec succès'
    });
  } catch (error) {
    console.error('Error deleting region price:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression du prix',
      error: error.message
    });
  }
};

// Obtenir toutes les régions disponibles (liste unique)
const getAvailableRegions = async (req, res) => {
  try {
    const regions = await RegionPrice.distinct('regionId');
    
    res.status(200).json({
      success: true,
      data: regions.sort()
    });
  } catch (error) {
    console.error('Error fetching available regions:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des régions',
      error: error.message
    });
  }
};

// Obtenir tous les packages disponibles
const getAvailablePackages = async (req, res) => {
  try {
    const packages = await Package.find({ status: 'active' })
      .select('name price category')
      .sort({ category: 1, name: 1 });
    
    res.status(200).json({
      success: true,
      data: packages
    });
  } catch (error) {
    console.error('Error fetching available packages:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des packages',
      error: error.message
    });
  }
};

module.exports = {
  getAllRegionPrices,
  getRegionPrices,
  setRegionPrice,
  deleteRegionPrice,
  getAvailableRegions,
  getAvailablePackages
};
