import { getSingleBlogPostBySlug } from "@/app/lib/notion";
import BlogPostContent from "@/components/blog/blogPostContent";

export default async function BlogPost({ params }) {
  const { slug } = params;

  try {
    const post = await getSingleBlogPostBySlug(slug);

    if (!post) {
      return <div>Post not found</div>;
    }

    return (
      <article className="max-w-[1400px] mx-auto min-h-screen">
        <BlogPostContent post={post} />
      </article>
    );
  } catch (error) {
    return <div>Error fetching post:</div>;
  }
}
