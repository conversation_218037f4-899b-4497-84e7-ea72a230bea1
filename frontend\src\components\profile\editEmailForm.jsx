import React, { useEffect, useState, useRef } from "react";
import { Input, Button, Typography } from "@material-tailwind/react";
import profileService from "../../app/services/profileService";
import { useRouter } from "next/navigation";
import InputOneTimePassword from "./inputOneTimePassword";
import { useAuth } from "../../app/context/AuthContext";
import { FaFacebook } from "react-icons/fa";
import { toast } from "react-toastify";

const EditEmailForm = ({ t }) => {
  const [formData, setFormData] = useState({
    id: "",
    newEmail: "",
    password: "",
    isOAuth: false,
    isOAuthWithoutPassword: false,
  });
  const [otp, setOtp] = useState(Array(6).fill(""));
  const [step, setStep] = useState("emailUpdate");
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    const getUserProfile = async () => {
      try {
        const userProfileRes = await profileService.getProfile();
        const userData = userProfileRes.data;

        // Calculate if this is an OAuth user without password
        const isOAuthWithoutPassword =
          userData.isOAuth && !userData.hasPassword;
        console.log("isOAuth:", userData?.isOAuth);
        console.log("hasPassword:", userData?.hasPassword);
        console.log("isOAuthWithoutPassword:", isOAuthWithoutPassword);

        setFormData((prev) => ({
          ...prev,
          id: userData?._id,
          isOAuth: userData?.isOAuth || false,
          isOAuthWithoutPassword: isOAuthWithoutPassword,
        }));
      } catch (error) {
        console.error("Error fetching profile:", error);
      }
    };

    getUserProfile();
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
    setErrors((prevErrors) => ({ ...prevErrors, [name]: "" }));
  };

  // Add form validation
  const validateForm = () => {
    let isValid = true;
    const newErrors = {};

    // Always validate email
    if (!formData.newEmail?.trim()) {
      newErrors.newEmail = t(
        "new_email_required",
        "New email address is required"
      );
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.newEmail)) {
      newErrors.newEmail = t(
        "invalid_email",
        "Please enter a valid email address"
      );
      isValid = false;
    }

    // Validate password only if not OAuth without password
    if (!formData.isOAuthWithoutPassword && !formData.password?.trim()) {
      newErrors.password = t(
        "current_password_required",
        "Current password is required"
      );
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmitEmail = async (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Create data object to send
      const dataToSend = {
        ...formData,
        isOAuth: formData.isOAuth, // Keep original isOAuth value
        password: formData.isOAuthWithoutPassword ? "" : formData.password,
      };

      // Remove the helper field we added
      delete dataToSend.isOAuthWithoutPassword;

      const response = await profileService.changeEmail(dataToSend);
      console.log("Email updated response:", response);
      setStep("otpVerification");
    } catch (error) {
      console.error("Error changing email:", error);
      if (error.response?.data?.errors) {
        const serverErrors = error.response.data.errors.reduce((acc, curr) => {
          acc[curr.key] = curr.msg;
          return acc;
        }, {});
        setErrors(serverErrors);
      } else if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error(
          t("email_change_failed", "Failed to change email. Please try again.")
        );
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Typography
        variant="h2"
        className="text-2xl font-semibold text-gray-800 mb-6"
      >
        {t("change_email")}
      </Typography>

      {step === "emailUpdate" && (
        <form onSubmit={handleSubmitEmail} className="space-y-6">
          <div>
            <Input
              type="email"
              name="newEmail"
              label={t("new_email")}
              value={formData.newEmail}
              onChange={handleInputChange}
            />
            {errors.newEmail && (
              <p className="text-red-500 text-sm">{errors.newEmail}</p>
            )}
          </div>

          {/* Show password field if not OAuth without password */}
          {!formData.isOAuthWithoutPassword && (
            <div>
              <Input
                type="password"
                name="password"
                label={t("password")}
                value={formData.password}
                onChange={handleInputChange}
              />
              {errors.password && (
                <p className="text-red-500 text-sm">{errors.password}</p>
              )}
            </div>
          )}

          {/* Show OAuth provider info if user is registered with OAuth */}
          {formData.isOAuth && (
            <div className="flex items-center gap-x-4 mb-4">
              <div className="flex items-center gap-x-2 bg-gray-100 p-2 rounded-md w-fit">
                {user?.socialMediaData?.provider === "google" && (
                  <img
                    src="/images/google.png"
                    alt="Google"
                    className="w-6 h-6"
                  />
                )}
                {user?.socialMediaData?.provider === "facebook" && (
                  <FaFacebook color="#1976d2" width={100} fontSize={30} />
                )}
                <p className="text-sm text-gray-700">
                  {t("this_account_is_linked_by", "This account is linked by")}{" "}
                  {user?.socialMediaData?.provider}
                </p>
              </div>
            </div>
          )}

          <div className="flex flex-row gap-x-10">
            <Button
              type="submit"
              disabled={loading}
              className="bg-secondary font-normal font-poppins rounded-md"
            >
              {loading
                ? t("updating", "Updating...")
                : t("update_email", "Update Email")}
            </Button>
            <Button
              onClick={() => router.push("/client/profile")}
              className="bg-gray-50 text-gray-800 hover:shadow-sm border-gray-200 px-10 border shadow-none font-medium font-poppins rounded-md"
            >
              {t("back", "Back")}
            </Button>
          </div>
        </form>
      )}

      {step === "otpVerification" && (
        <InputOneTimePassword
          userId={user?._id}
          t={t}
          newEmail={formData?.newEmail}
        />
      )}
    </div>
  );
};

export default EditEmailForm;
