const Notification = require('../models/Notification');

// Get all notifications for a user
exports.getUserNotifications = async (req, res) => {
  try {
    const userId = req.user._id; // Get userId from authenticated user
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const unreadOnly = req.query.unreadOnly === 'true';
    const skip = (page - 1) * limit;

    // Create a base query for user notifications
    const baseQuery = {
      userType: 'client',
      userId: userId
    };

    // Add filter for unread notifications if requested
    if (unreadOnly) {
      baseQuery.isRead = false;
    }

    // Get notifications with pagination
    const notifications = await Notification.find(baseQuery)
      .sort({ createdAt: -1 }) // Sort by newest first
      .skip(skip)
      .limit(limit);

    // Get total count of unread notifications for the badge
    const unreadCount = await Notification.countDocuments({
      userType: 'client',
      userId: userId,
      isRead: false
    });

    // Get total count for pagination
    const totalCount = await Notification.countDocuments(baseQuery);

    return res.status(200).json({
      success: true,
      notifications,
      unreadCount,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch notifications',
      error: error.message
    });
  }
};

// Mark a notification as read
exports.markNotificationAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    // Find the notification and ensure it belongs to the current user
    const notification = await Notification.findOne({
      _id: notificationId,
      userId: userId,
      userType: 'client'
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found or does not belong to you'
      });
    }

    // Update the notification
    notification.isRead = true;
    await notification.save();

    return res.status(200).json({
      success: true,
      message: 'Notification marked as read',
      notification
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read',
      error: error.message
    });
  }
};

// Mark all notifications as read
exports.markAllNotificationsAsRead = async (req, res) => {
  try {
    const userId = req.user._id;

    // Update all unread notifications for this user
    const result = await Notification.updateMany(
      {
        userId: userId,
        userType: 'client',
        isRead: false
      },
      {
        $set: { isRead: true }
      }
    );

    return res.status(200).json({
      success: true,
      message: 'All notifications marked as read',
      count: result.modifiedCount
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to mark all notifications as read',
      error: error.message
    });
  }
};