'use client'
import React from 'react'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image';
import imgIntro from "/public/images/services/dedicated-servers.png";


function ServersIntro({ t }) {
    return (
        <div className="w-full bg-gradient-to-b from-white via-indigo-50 to-white">
            <div className="max-w-[1400px] mx-auto w-full flex flex-col items-center pt-8 pb-0 px-4 text-center">
                <Typography
                    variant='h2'
                    className="text-secondary text-sm font-medium">
                    {t('dedicated_servers')}
                </Typography>
                <Typography
                    variant='h1'
                    className="md:text-4xl text-3xl md:max-w-3xl mx-auto font-poppins font-medium text-primary mt-0 mb-2">
                    {t('performance_security_dedicated_servers')}
                </Typography>
                <p className="text-secondary text-base font-medium mb-0">
                    {t('high_performance_dedicated_servers')}
                </p>

                <div className="md:w-2/5 rounded-xl mx-auto my-4">
                    {/* <img
                        // loading="lazy"
                        src="/images/services/servers-banner.png"
                        alt="infogerance"
                        className='w-full h-[300px] m-auto rounded-2xl'
                    /> */}
                    <Image
                        // src="/images/services/servers-banner.png"
                        src={imgIntro}
                        alt="infogerance"
                        width={800}
                        height={600}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        className='w-full m-auto rounded-2xl'
                        placeholder='blur'
                        priority
                    />

                </div>

                <p className="text-gray-900 max-w-4xl text-lg mx-auto">
                    {t('dedicated_servers_description')}
                </p>
            </div>
        </div>
    )
}

export default ServersIntro;