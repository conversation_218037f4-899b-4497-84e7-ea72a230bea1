const express = require('express');
const { createCategory, getCategories, getCategory, updateCategory, deleteCategory } = require('../controllers/categoryController');
const categoryRouter = express.Router();

categoryRouter.post('/create-category', createCategory);
categoryRouter.get('/get-categories', getCategories);
categoryRouter.get('/get-category/:name', getCategory);
categoryRouter.put('/update-category/:id', updateCategory);
categoryRouter.delete('/delete-category/:id', deleteCategory);

module.exports = categoryRouter;
