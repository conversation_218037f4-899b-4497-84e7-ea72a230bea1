import "../../styles/globals.css";
import "react-toastify/dist/ReactToastify.css";
import { config } from "@fortawesome/fontawesome-svg-core";
import "@fortawesome/fontawesome-svg-core/styles.css";

import { NextIntlClientProvider } from "next-intl";
import dynamic from "next/dynamic";
import { ToastContainer } from "react-toastify";
import { Inter, Poppins, Roboto } from "next/font/google";
import Header from "../../components/home/<USER>";
import FloatingButtonsContainer from "../../components/shared/FloatingButtonsContainer";
// Disable auto-adding of Font Awesome CSS
config.autoAddCss = false;

// Dynamically import components for better performance
// const Header = dynamic(() => import('../../components/home/<USER>'), { ssr: false });
const Footer2 = dynamic(() => import("../../components/shared/footer2"), {
  ssr: false,
});
const WhatsAppFloatingButton = dynamic(
  () => import("../../components/shared/whatsAppFloatingButton"),
  { ssr: false }
);


// Load only essential fonts
const inter = Inter({
  subsets: ["latin"],
  weight: ["400", "500", "900"],
  variable: "--font-inter",
  display: "swap",
});
const roboto = Roboto({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-roboto",
  display: "swap",
});
const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-poppins",
  display: "swap",
});



export default async function LocaleLayout({ children, params: { locale } }) {
  const messages = (await import(`../../../messages/${locale}.json`)).default;

  return (
    <NextIntlClientProvider messages={messages}>
      <div
        id="LocaleLayout"
        className={`${inter.variable} ${roboto.variable} ${poppins.variable} bg-white relative flex flex-col min-h-screen h-full w-full font-inter mx-auto border`}
      >
        <Header />
        <div className="relative w-full h-full mx-auto mt-1">
          {/* Lazy Load Toast Notifications */}
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />

          <FloatingButtonsContainer />

          {children}
        </div>
        <Footer2 />
      </div>
    </NextIntlClientProvider>
  );
}
