# Testing Scripts for VPS Order Processing

This directory contains scripts to help test VPS order processing and provisioning workflows.

## Scripts Overview

### 1. `listRecentOrders.js` - List Recent Orders
Lists the most recent orders in the system to help you find order IDs for testing.

**Usage:**
```bash
node listRecentOrders.js [limit]
```

**Examples:**
```bash
# List 20 most recent orders (default)
node listRecentOrders.js

# List 10 most recent orders
node listRecentOrders.js 10

# List 50 most recent orders
node listRecentOrders.js 50
```

**Output includes:**
- Order ID and identifiant
- User information
- Payment status
- VPS indicators (🖥️ for VPS orders, 📦 for regular orders)
- SubOrder details including VPS configuration

### 2. `markOrderAsPaid.js` - Mark Order as Paid (Interactive)
Marks an order as paid with confirmation prompts and detailed status information.

**Usage:**
```bash
node markOrderAsPaid.js <orderId>
```

**Examples:**
```bash
# Using MongoDB ObjectId
node markOrderAsPaid.js 67890abcdef123456789

# Using order identifiant
node markOrderAsPaid.js ORD-2024-001
```

**Features:**
- Shows detailed order information before processing
- Asks for confirmation if order is already paid
- Displays VPS provisioning status after payment
- Waits to show updated VPS status (IP address, provider instance ID, etc.)

### 3. `quickPayOrder.js` - Quick Pay Order (No Prompts)
Quickly marks an order as paid without confirmation prompts. Ideal for batch testing.

**Usage:**
```bash
node quickPayOrder.js <orderId>
```

**Examples:**
```bash
# Quick payment processing
node quickPayOrder.js 67890abcdef123456789
node quickPayOrder.js ORD-2024-001
```

**Features:**
- No confirmation prompts
- Fast execution
- Minimal output
- Perfect for automated testing

## Testing VPS Provisioning Workflow

### Step 1: Find an Order to Test
```bash
node listRecentOrders.js 10
```

Look for orders with the 🖥️ icon (VPS orders) that are not yet paid (❌).

### Step 2: Mark Order as Paid
```bash
# For detailed information and confirmation
node markOrderAsPaid.js <ORDER_ID>

# For quick testing
node quickPayOrder.js <ORDER_ID>
```

### Step 3: Monitor VPS Provisioning
The `markOrderAsPaid.js` script will automatically show VPS provisioning status after payment. You can also check the database or application logs to see the provisioning progress.

## Expected VPS Provisioning Flow

When an order is marked as paid:

1. **Order Status Changes:**
   - Order status: `PENDING` → `PROCESSING`
   - Order `isPaid`: `false` → `true`
   - Order `datePaid`: set to current timestamp
   - Order `transactionId`: set to generated test transaction ID

2. **SubOrder Status Changes:**
   - SubOrder status: `PENDING` → `PROCESSING`

3. **VPS Provisioning Starts:**
   - VPS status: `PENDING` → `PROVISIONING`
   - VPS provider API is called to create the instance
   - VPS status: `PROVISIONING` → `ACTIVE` (if successful)

4. **VPS Instance Data Updated:**
   - `providerInstanceId`: Set from provider response
   - `ipAddress`: Set from provider response
   - `ipv6Address`: Set from provider response (if available)
   - `rootPassword`: Generated secure password
   - `provisionedAt`: Timestamp when provisioning completed
   - `activatedAt`: Timestamp when VPS became active

## Environment Requirements

Make sure your `.env` file contains:
- `MONGODB_URI` or `DB_URI`: MongoDB connection string
- VPS provider API credentials (Contabo, DigitalOcean, etc.)

## Troubleshooting

### Common Issues:

1. **"Order not found"**
   - Check if the order ID or identifiant is correct
   - Use `listRecentOrders.js` to find valid order IDs

2. **"MongoDB connection error"**
   - Verify your `.env` file has the correct database connection string
   - Ensure MongoDB is running and accessible

3. **VPS provisioning fails**
   - Check VPS provider API credentials in `.env`
   - Verify provider API endpoints are accessible
   - Check application logs for detailed error messages

4. **Permission errors**
   - Ensure the scripts have execute permissions: `chmod +x *.js`
   - Run with Node.js: `node scriptName.js` instead of `./scriptName.js`

## Notes

- All scripts generate test transaction IDs in the format: `TEST_timestamp_randomstring`
- The scripts are safe to run multiple times on the same order
- VPS provisioning is asynchronous - it may take a few moments to complete
- Check your VPS provider's dashboard to verify instances are actually created

## Example Workflow

```bash
# 1. List recent orders to find a VPS order
node listRecentOrders.js 5

# 2. Copy an unpaid VPS order ID from the output
# 3. Mark it as paid and watch the provisioning
node markOrderAsPaid.js ORD-2024-VPS-001

# 4. The script will show VPS provisioning progress
# 5. Check your VPS provider dashboard to confirm instance creation
```
