const ProductStatus = require("../constants/enums/poduct-status");
const Cart = require("../models/Cart");
const Favorite = require("../models/Favorite");
const { Product } = require("../models/Product");
const { Section } = require("../models/Section");
const User = require("../models/User");
const mongoose = require('mongoose');


// exports.toFavorite = async (req, res) => {
//     const { productID } = req.params;
//     const userId = req.auth.id;
//     try {
//         if (!mongoose.Types.ObjectId.isValid(userId)) {
//             return res.status(404).send({ message: "Unauthorized" })
//         }
//         const userFound = await User.findOne({ '_id': userId });

//         if (!userFound) {
//             return res.status(404).send({ message: "Unauthorized" })
//         }

//         const productFound = await Product.findOne(
//             { '_id': productID, }
//         );

//         if (!productFound) {
//             return res.status(404).send({ message: "product not found" })
//         }


//         if (productFound.status !== ProductStatus.PUBLISHED) {
//             await Favorite.findOneAndRemove({ product: productID, user: userId });
//             return res.status(404).send({ message: "Unauthorized ... Please refresh the page" })
//         }

//         const isFvorite = await Favorite.findOne({ product: productID, user: userId });
//         if (!isFvorite) {
//             const favorite = new Favorite({ product: productID, user: userId });
//             await favorite.save();
//         } else {
//             // Remove the existing favorite
//             await Favorite.findOneAndRemove({ product: productID, user: userId });
//         }

//         return res.send({ message: "this productID " + productID + " added to favorite" });
//     } catch (error) {
//         console.log(error);
//         return res.status(500).send({ message: "Internal Server error" })
//     }
// }

// sectionId


exports.toFavorite = async (req, res) => {
    const { productID } = req.params;
    const userId = req.auth.id;

    try {
        // Validate user ID
        if (!mongoose.Types.ObjectId.isValid(userId)) {
            return res.status(404).send({ message: "Unauthorized" });
        }

        // Check if user exists
        const userFound = await User.findById(userId);
        if (!userFound) {
            return res.status(404).send({ message: "Unauthorized" });
        }

        // Check if product exists and is published
        const productFound = await Product.findById(productID);
        if (!productFound) {
            return res.status(200).send({ message: "Product not found" });
        }

        // Handle product status
        if (productFound.status !== ProductStatus.PUBLISHED) {
            await Favorite.findOneAndRemove({ product: productID, user: userId });
            return res.status(200).send({ message: "Unauthorized ... Please refresh the page" });
        }

        // Toggle favorite status
        const existingFavorite = await Favorite.findOne({ product: productID, user: userId });
        if (existingFavorite) {
            // Remove existing favorite
            await Favorite.findOneAndRemove({ product: productID, user: userId });
            return res.send({ message: "Product removed from favorites" });
        } else {
            // Add new favorite
            const favorite = new Favorite({ product: productID, user: userId });
            await favorite.save();
            return res.send({ message: "Product added to favorites" });
        }
    } catch (error) {
        console.error("Error in toFavorite:", error);
        return res.status(500).send({ message: "Internal Server error" });
    }
};

exports.getFavrites = async (req, res) => {
    const { sectionId } = req.query;
    let wishList = [];
    if (!mongoose.Types.ObjectId.isValid(req.auth.id)) {
        return res.status(400).send({ message: "Invalid ID format" });
    }

    const userFound = await User.findById(req.auth.id)
        .populate({
            path: 'favorites',
            populate: {
                path: 'product',
                model: 'Product',
                populate: [
                    { path: 'category', model: 'Category' },
                    {
                        path: 'creator',
                        model: 'User',
                        populate: [
                            { path: 'comments', model: 'Comment' }
                        ]
                    }
                ]
            }
        });

    if (!userFound) {
        return res.status(404).send({ message: 'User not found' });
    }

    // console.log("userFound", userFound);

    // if (sectionId) {
    //     const section = await Section.findById(sectionId);
    //     wishList = await Promise.all(
    //         userFound.favorites
    //             .filter((prF) => {
    //                 const isCategoryInSection = section.categories.some((cat) =>
    //                     cat._id.equals(new mongoose.Types.ObjectId(prF.product.category._id))
    //                 );
    //                 return isCategoryInSection;
    //             })
    //             .map(async (prF) => {
    //                 const productObject = prF.product.toObject();
    //                 const isInCart = await Cart.findOne({
    //                     product: productObject._id,
    //                     user: req.auth.id,
    //                 });

    //                 return {
    //                     ...productObject,
    //                     isFavorite: true,
    //                     creator: {
    //                         ...productObject.creator,
    //                         nbComments: productObject.creator.comments ? productObject.creator.comments.length : 0,
    //                         rate:
    //                             productObject.creator.comments ? productObject.creator.comments.reduce(
    //                                 (sum, comment) => sum + comment.rate,
    //                                 0
    //                             ) / productObject.creator.comments.length : 0,
    //                     },
    //                     isInCart: !!isInCart,
    //                 };
    //             })
    //     );

    // } else {
    //     wishList = await Promise.all(userFound.favorites.map(async (prF) => {
    //         const productObject = prF.product.toObject();
    //         const isInCart = await Cart.findOne({ product: productObject._id, user: req.auth.id });

    //         return {
    //             ...productObject,
    //             isFavorite: true,
    //             creator: {
    //                 ...productObject.creator,
    //                 nbComments: productObject.creator.comments ? productObject.creator.comments.length : 0,
    //                 rate: productObject.creator.comments ? productObject.creator.comments.reduce((sum, comment) => sum + comment.rate, 0) / productObject.creator.comments.length : 0
    //             },
    //             isInCart: !!isInCart  // Assuming you want to set isInCart to true if it's in the cart
    //         };
    //     }));
    // }


    if (sectionId) {
        const section = await Section.findById(sectionId);

        // Process wishList with the section's categories
        wishList = await Promise.all(
            userFound.favorites
                .filter((prF) => {
                    const isCategoryInSection = section.categories.some((cat) =>
                        cat._id.equals(new mongoose.Types.ObjectId(prF.product.category._id))
                    );
                    return isCategoryInSection;
                })
                .map(async (prF) => {
                    const productObject = prF.product.toObject();

                    const isInCart = await Cart.findOne({
                        product: productObject._id,
                        user: req.auth.id,
                    });

                    // Await both nbRate and nbComments
                    const nbRate = await productObject.creator.nbRate;
                    const nbComments = await productObject.creator.nbComments;
                    console.log(`nbRate: ${nbRate}, nbComments: ${nbComments}`);

                    return {
                        ...productObject,
                        isFavorite: true,
                        isInCart: !!isInCart,
                        creator: {
                            ...productObject.creator,
                            nbRate,
                            nbComments,
                        },
                    };
                })
        );
    } else {
        // Process wishList without the section categories filter
        wishList = await Promise.all(
            userFound.favorites.map(async (prF) => {
                const productObject = prF.product.toObject();

                const isInCart = await Cart.findOne({
                    product: productObject._id,
                    user: req.auth.id,
                });

                // Await nbRate and nbComments
                const nbRate = await productObject.creator.nbRate;
                const nbComments = await productObject.creator.nbComments;
                console.log(`nbRate: ${nbRate}, nbComments: ${nbComments}`);
                return {
                    ...productObject,
                    isFavorite: true,
                    isInCart: !!isInCart,
                    creator: {
                        ...productObject.creator,
                        nbRate,
                        nbComments,
                    },
                };
            })
        );
    }


    return res.send({ message: 'wishList retreived correctly', data: wishList });
}

exports.removeProductsFromFavorites = async (productIDs, userId = null) => {
    try {
        // Ensure `productIDs` is an array, if not, convert it to an array
        if (!Array.isArray(productIDs)) {
            productIDs = [productIDs];
        }

        // Validate product IDs (ensure they are valid ObjectIds)
        productIDs = productIDs.filter(id => mongoose.Types.ObjectId.isValid(id));
        if (productIDs.length === 0) {
            return { message: "No valid product IDs provided" };
        }

        // Build the query object
        let query = { product: { $in: productIDs } };

        // If a `userId` is provided, add it to the query to delete only from that user's favorites
        if (userId) {
            query.user = userId;
        }

        // Remove products from the user's favorites
        await Favorite.deleteMany(query);

        return { message: `Successfully removed products ${productIDs.join(", ")} from ${userId ? "user's" : "all users'"} favorites` };
    } catch (error) {
        console.error("Error removing products from favorites:", error);
        return { message: "Internal Server Error", error };
    }
};
