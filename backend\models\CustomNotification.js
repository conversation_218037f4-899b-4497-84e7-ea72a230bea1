const mongoose = require('mongoose');

const customNotificationSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  scheduledDateTime: {
    type: Date,
    required: true
  },
  targetAudience: {
    type: String,
    enum: ['all', 'verified', 'unverified'],
    default: 'all'
  },
  emailEnabled: {
    type: Boolean,
    default: true
  },
  inAppEnabled: {
    type: Boolean,
    default: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'sent', 'failed', 'cancelled'],
    default: 'scheduled'
  },
  sentAt: {
    type: Date
  },
  sentCount: {
    type: Number,
    default: 0
  },
  failedCount: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  errorMessage: {
    type: String
  }
}, {
  timestamps: true
});

// Index for efficient querying
customNotificationSchema.index({ scheduledDateTime: 1, status: 1 });
customNotificationSchema.index({ createdBy: 1 });

// Virtual for checking if notification is due
customNotificationSchema.virtual('isDue').get(function() {
  return this.scheduledDateTime <= new Date() && this.status === 'scheduled';
});

// Method to mark as sent
customNotificationSchema.methods.markAsSent = function(sentCount = 0, failedCount = 0) {
  this.status = 'sent';
  this.sentAt = new Date();
  this.sentCount = sentCount;
  this.failedCount = failedCount;
  return this.save();
};

// Method to mark as failed
customNotificationSchema.methods.markAsFailed = function(errorMessage) {
  this.status = 'failed';
  this.errorMessage = errorMessage;
  return this.save();
};

// Static method to find due notifications
customNotificationSchema.statics.findDueNotifications = function() {
  return this.find({
    scheduledDateTime: { $lte: new Date() },
    status: 'scheduled'
  }).populate('createdBy', 'firstName lastName email');
};

module.exports = mongoose.model('CustomNotification', customNotificationSchema);
