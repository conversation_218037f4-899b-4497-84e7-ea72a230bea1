const { default: mongoose } = require("mongoose");
const Favorite = require("../models/Favorite");
const Cart = require("../models/Cart");
const { Product } = require("../models/Product");
const User = require("../models/User");
const AccountState = require("../constants/enums/account-state");
const AccountRole = require("../constants/enums/account-role");
const PaymentMethod = require("../constants/enums/vendor-payment-method");
const BankAccount = require("../models/BankAccount");
const MtoAccount = require("../models/MtoAccount");
const { Inquiry } = require("../models/Inquery");
const InquiryState = require("../constants/enums/inquiry-state");
const { scheduledPaymentDate } = require("../helpers/helpers");
const PaymentStatus = require("../constants/enums/payment-status");
const cron = require('node-cron');


exports.counts = async (req, res) => {
    try {
        const { id } = req.auth;
        const favorites = await Favorite.countDocuments({ user: new mongoose.Types.ObjectId(id) })
        const cart = await Cart.countDocuments({ user: new mongoose.Types.ObjectId(id) })
        return res.send({ message: "Counts successfully", data: { favorites, cart } });
    } catch (error) {
        console.log(error);
        return res.status(500).send("Server internal error");
    }
}

exports.getAllVendors = async (req, res) => {
    try {
        const distinctCreators = await Product.distinct('creator');
        const vendors = await User.find({ _id: { $in: distinctCreators } })
            .populate('nbRate')
            .populate('nbComments');
        const arr = [];
        await Promise.all(vendors.map(async (vendor, index) => {
            const rate = await vendor.nbRate;
            const nbComments = await vendor.nbComments;
            arr.push(
                {
                    ...vendor.toObject(),
                    rate,
                    nbComments
                }
            )
        }))

        return res.send({ message: "get vendors successfully", data: [arr] });
    } catch (error) {
        console.log(error);
        return res.status(500).send("Server internal error");
    }
}

exports.getStatistics = async (req, res) => {
    return res.send({
        message: "get statistics successfully", data: {
            verifiedUser: await User.countDocuments({ state: AccountState.Enabled }),
            verifiedVendor: await User.countDocuments({ role: AccountRole.Vendor }),
            orders: 0,
            articles: 0
        }
    });
}

exports.updateInquiryWithPaymentMethod = async (req) => {
    try {
        const { inquiryId, userId, motifMessage } = req.body;

        // Validate input
        if (!inquiryId || !userId || !/^[a-fA-F0-9]{24}$/.test(userId) || !/^[a-fA-F0-9]{24}$/.test(inquiryId)) {
            return { status: 400, data: { error: 'Invalid inquiry ID or user ID' } };
        }

        // Find the user
        const user = await User.findById(userId);
        if (!user) {
            return { status: 404, data: { error: 'User not found' } };
        }

        const paymentMethodType = user.paymentMethodType;
        const paymentMethodId = user.paymentMethod;

        if (!paymentMethodType || !paymentMethodId) {
            return {
                status: 400, data: {
                    error: 'No payment method found for this user',
                    message: 'Please set a payment method so that we can process your payment.',
                    redirectTo: '/versement' // URL for setting the payment method
                }
            };
        }

        // Determine the model based on paymentMethodType
        let paymentMethodInstance;
        if (paymentMethodType === PaymentMethod.BANK_ACCOUNT) {
            paymentMethodInstance = await BankAccount.findById(paymentMethodId);
        } else if (paymentMethodType === PaymentMethod.MTO_ACCOUNT) {
            paymentMethodInstance = await MtoAccount.findById(paymentMethodId);
        } else {
            return { status: 400, data: { error: 'Invalid payment method type' } };
        }

        if (!paymentMethodInstance) {
            return { status: 404, data: { error: 'Payment method not found' } };
        }

        // Mark the payment method as used
        paymentMethodInstance.used = true;
        await paymentMethodInstance.save();

        // Find the inquiry and update it with the user's payment method
        const inquiry = await Inquiry.findById(inquiryId);
        if (!inquiry) {
            return { status: 404, data: { error: 'Inquiry not found' } };
        }

        inquiry.state.push({
            value: InquiryState.COMPLETED,
            date: new Date(),
            motif: motifMessage,
        });

        // Update the inquiry with the user's payment method
        inquiry.setPaymentMethod(paymentMethodType, paymentMethodInstance._id);


        // Schedule the payment date if the payment method exists
        if (paymentMethodInstance) {
            const paymentDate = scheduledPaymentDate(); // Your function to return the payment date
            console.log('paymentDate', paymentDate);

            inquiry.paymentStatus.push({
                value: PaymentStatus.SCHEDULED,
                date: paymentDate // Add the payment date for the vendor's earnings page
            });
        }

        await inquiry.save();

        return { status: 200, data: { message: 'Inquiry updated with user\'s payment method', inquiry } };
    } catch (error) {
        console.error('Error updating inquiry with payment method:', error);
        return { status: 500, data: { error: 'Server error' } };
    }
};



// exports.scheduleOneTimeJob = (date, jobFunction) => {
//     // Validate parameters
//     if (!(date instanceof Date) || typeof jobFunction !== 'function') {
//         throw new Error('Invalid parameters. Expected a Date and a Function.');
//     }

//     // Check if the date is in the future
//     const now = new Date();
//     if (date <= now) {
//         throw new Error('Scheduled date must be in the future.');
//     }

//     // Calculate cron expression from the date
//     const cronExpression = `${date.getSeconds()} ${date.getMinutes()} ${date.getHours()} ${date.getDate()} ${date.getMonth() + 1} *`;

//     // Schedule the job
//     const job = cron.schedule(cronExpression, () => {
//         jobFunction();
//         job.stop(); // Stop the job after execution since it's one-time
//     });

//     console.log(`Job scheduled for ${date}`);
// };
// const cron = require('node-cron');

exports.scheduleOneTimeJob = (date, jobFunction, ...params) => {
    // Validate parameters
    if (!(date instanceof Date) || typeof jobFunction !== 'function') {
        throw new Error('Invalid parameters. Expected a Date and a Function.');
    }

    // Check if the date is in the future
    const now = new Date();
    if (date <= now) {
        throw new Error('Scheduled date must be in the future.');
    }

    // Calculate cron expression (without seconds)
    const cronExpression = `${date.getMinutes()} ${date.getHours()} ${date.getDate()} ${date.getMonth() + 1} *`;

    // Schedule the job
    const job = cron.schedule(cronExpression, () => {
        jobFunction(...params); // Pass the params to the job function
        job.stop(); // Stop the job after execution since it's one-time
    });

    console.log(`Job scheduled for ${date}`);
};
