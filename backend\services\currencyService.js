/**
 * Currency Conversion Service
 * Handles USD to MAD conversion using CurrencyAPI
 * Includes caching and fallback mechanisms for reliability
 */

const axios = require('axios');

class CurrencyService {
  constructor() {
    this.apiKey = process.env.CURRENCY_API_KEY;
    this.apiUrl = process.env.CURRENCY_API_URL;
    this.cache = {
      rate: null,
      lastUpdated: null,
      ttl: 60 * 60 * 1000 // 1 hour cache TTL
    };
    // Fallback rate in case API fails (approximate current rate)
    this.fallbackRate = 10.0; // 1 USD = 10 MAD (fallback)
  }

  /**
   * Get current USD to MAD exchange rate
   * @returns {Promise<number>} Exchange rate (1 USD = X MAD)
   */
  async getUsdToMadRate() {
    try {
      // Check cache first
      if (this.isCacheValid()) {
        console.log('[CURRENCY] Using cached exchange rate:', this.cache.rate);
        return this.cache.rate;
      }

      console.log('[CURRENCY] Fetching fresh exchange rate from CurrencyAPI...');
      
      const response = await axios.get(this.apiUrl, {
        params: {
          apikey: this.apiKey,
          currencies: 'MAD',
          base_currency: 'USD'
        },
        timeout: 10000 // 10 second timeout
      });

      if (response.data && response.data.data && response.data.data.MAD) {
        const rate = response.data.data.MAD.value;
        
        // Update cache
        this.cache.rate = rate;
        this.cache.lastUpdated = new Date();
        
        console.log(`[CURRENCY] Fresh exchange rate fetched: 1 USD = ${rate} MAD`);
        return rate;
      } else {
        throw new Error('Invalid response format from CurrencyAPI');
      }

    } catch (error) {
      console.error('[CURRENCY] Error fetching exchange rate:', error.message);
      
      // Use cached rate if available, even if expired
      if (this.cache.rate) {
        console.log(`[CURRENCY] Using expired cached rate due to API error: ${this.cache.rate}`);
        return this.cache.rate;
      }
      
      // Use fallback rate as last resort
      console.log(`[CURRENCY] Using fallback rate due to API error: ${this.fallbackRate}`);
      return this.fallbackRate;
    }
  }

  /**
   * Convert USD amount to MAD
   * @param {number} usdAmount - Amount in USD
   * @returns {Promise<number>} Amount in MAD (rounded UP to whole number)
   */
  async convertUsdToMad(usdAmount) {
    try {
      if (!usdAmount || isNaN(usdAmount)) {
        throw new Error('Invalid USD amount provided');
      }

      const rate = await this.getUsdToMadRate();
      const madAmount = usdAmount * rate;

      // Round UP to whole number (no decimals)
      const roundedAmount = Math.ceil(madAmount);

      console.log(`[CURRENCY] Converted $${usdAmount} USD to ${roundedAmount} MAD (rate: ${rate}) - rounded UP`);
      return roundedAmount;

    } catch (error) {
      console.error('[CURRENCY] Error converting USD to MAD:', error.message);
      throw error;
    }
  }

  /**
   * Convert multiple USD prices to MAD
   * @param {Array<number>} usdPrices - Array of USD prices
   * @returns {Promise<Array<number>>} Array of MAD prices
   */
  async convertMultipleUsdToMad(usdPrices) {
    try {
      const rate = await this.getUsdToMadRate();
      
      return usdPrices.map(usdPrice => {
        if (!usdPrice || isNaN(usdPrice)) {
          console.warn(`[CURRENCY] Invalid USD price: ${usdPrice}, skipping conversion`);
          return 0;
        }
        
        const madPrice = usdPrice * rate;
        return Math.ceil(madPrice); // Round UP to whole number
      });

    } catch (error) {
      console.error('[CURRENCY] Error converting multiple USD prices:', error.message);
      throw error;
    }
  }

  /**
   * Check if cached exchange rate is still valid
   * @returns {boolean} True if cache is valid
   */
  isCacheValid() {
    if (!this.cache.rate || !this.cache.lastUpdated) {
      return false;
    }
    
    const now = new Date();
    const cacheAge = now - this.cache.lastUpdated;
    
    return cacheAge < this.cache.ttl;
  }

  /**
   * Clear the exchange rate cache
   */
  clearCache() {
    this.cache.rate = null;
    this.cache.lastUpdated = null;
    console.log('[CURRENCY] Exchange rate cache cleared');
  }

  /**
   * Get cache status for debugging
   * @returns {Object} Cache status information
   */
  getCacheStatus() {
    return {
      hasRate: !!this.cache.rate,
      rate: this.cache.rate,
      lastUpdated: this.cache.lastUpdated,
      isValid: this.isCacheValid(),
      ageMinutes: this.cache.lastUpdated 
        ? Math.round((new Date() - this.cache.lastUpdated) / (1000 * 60))
        : null
    };
  }

  /**
   * Test the currency API connection
   * @returns {Promise<Object>} Test result
   */
  async testConnection() {
    try {
      console.log('[CURRENCY] Testing CurrencyAPI connection...');
      
      const rate = await this.getUsdToMadRate();
      
      return {
        success: true,
        rate: rate,
        message: `Successfully connected to CurrencyAPI. Current rate: 1 USD = ${rate} MAD`,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Failed to connect to CurrencyAPI',
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = CurrencyService;
