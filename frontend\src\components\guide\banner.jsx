'use client'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import { BsArrowUpRight } from 'react-icons/bs'
import imgIntro from "/public/images/guide-banner2.png";

function Banner({ t }) {
    return (
        <div
            className='w-full md:py-16 p-0'
            style={{
                background: 'linear-gradient(259.92deg, #497EF7 -2.21%, #5956E9 90.11%)',
            }}>
            <div className="max-w-[1400px] mx-auto flex flex-col md:flex-row justify-center items-center py-10 px-5">
                <div className="flex flex-col gap-y-6 mx-auto md:w-1/2">
                    <p className='bg-white bg-opacity-30  text-white rounded-3xl px-2 py-1 text-sm w-fit flex gap-x-2'>
                        <span className='font-medium bg-secondary px-2 py-1 rounded-3xl my-auto'>
                            {t('guide')}
                        </span>
                        <span className='my-auto'>{t('web_hosting_guide')}</span>
                    </p>
                    <Typography
                        className='text-white'
                        variant='h2'>
                        {t('title')}
                    </Typography>
                    <p className='text-white md:max-w-2xl text-base mx-auto'>
                        {t('description')}
                        <br />
                        <br />
                        {t('content')}
                    </p>
                    <div className="hidden md:block mt-5 w-fit rounded-full bg-opacity-30 bg-white px-1 py-0.5">
                        <Link
                            href="#contact-nous"
                            className="flex items-center justify-between w-fit gap-x-5 text-white rounded-full">
                            <span className='text-base font-inter p-1'>
                                {t('contact')}
                            </span>
                            <span className='p-2 bg-white rounded-full border-[0.69px] border-[#2C52F7]'
                                style={{ boxShadow: '0px 0px 19.61px 7.54px rgba(66, 99, 242, 0.39)' }}
                            >
                                <BsArrowUpRight className='text-black' />
                            </span>
                        </Link>
                    </div>
                </div>
                <div className="md:w-1/2">
                    {/* <img
                        src="/images/guide-banner2.png"
                        alt="guide banner"
                        className='rounded-3xl mx-auto'
                    /> */}
                    <Image
                        src={imgIntro}
                        alt="guide banner"
                        width={500}
                        height={500}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        className="rounded-3xl mx-auto"
                        placeholder='blur'
                        priority
                    />
                </div>
            </div>
        </div>
    )
}

export default Banner