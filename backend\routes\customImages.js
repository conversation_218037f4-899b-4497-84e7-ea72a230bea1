const express = require('express');
const router = express.Router();
const CustomImageService = require('../services/CustomImageService');

const customImageService = new CustomImageService();

/**
 * @route POST /api/custom-images
 * @desc Create a new custom image
 * @access Public
 */
router.post('/', async (req, res) => {
  try {
    console.log('📝 POST /api/custom-images - Creating custom image');
    console.log('Request body:', req.body);

    const { url, name, osType, version, description } = req.body;

    // Validation
    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'Image URL is required'
      });
    }

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Image name is required'
      });
    }

    if (!version) {
      return res.status(400).json({
        success: false,
        message: 'Image version is required'
      });
    }

    const imageData = {
      url,
      name,
      osType: osType || 'Linux',
      version,
      description: description || ''
    };

    const result = await customImageService.createCustomImage(imageData);

    res.json({
      success: true,
      data: result.data,
      message: result.message
    });

  } catch (error) {
    console.error('❌ Error creating custom image:', error.message);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route GET /api/custom-images
 * @desc Get all custom images
 * @access Public
 */
router.get('/', async (req, res) => {
  try {
    console.log('📋 GET /api/custom-images - Fetching custom images');

    const result = await customImageService.getCustomImages();

    res.json({
      success: true,
      data: result.data,
      message: result.message
    });

  } catch (error) {
    console.error('❌ Error fetching custom images:', error.message);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route GET /api/custom-images/:id
 * @desc Get custom image details
 * @access Public
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🔍 GET /api/custom-images/${id} - Getting custom image details`);

    const result = await customImageService.getCustomImageDetails(id);

    res.json({
      success: true,
      data: result.data,
      message: result.message
    });

  } catch (error) {
    console.error('❌ Error getting custom image details:', error.message);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route PUT /api/custom-images/:id
 * @desc Update a custom image
 * @access Public
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`✏️ PUT /api/custom-images/${id} - Updating custom image`);
    console.log('Request body:', req.body);

    const result = await customImageService.updateCustomImage(id, req.body);

    res.json({
      success: true,
      data: result.data,
      message: result.message
    });

  } catch (error) {
    console.error('❌ Error updating custom image:', error.message);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @route DELETE /api/custom-images/:id
 * @desc Delete a custom image
 * @access Public
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🗑️ DELETE /api/custom-images/${id} - Deleting custom image`);

    const result = await customImageService.deleteCustomImage(id);

    res.json({
      success: true,
      message: result.message
    });

  } catch (error) {
    console.error('❌ Error deleting custom image:', error.message);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
