'use client';
import React from 'react';
import Slider from 'react-slick';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Typography } from '@material-tailwind/react';

function Testimonials2() {
    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 5000,
        arrows: false,
    };

    const reviews = [
        {
            id: 1,
            text: "Ztechengineering a boosté notre visibilité en ligne et sécurisé nos données. Un partenaire incontournable !",
            name: "<PERSON>",
            location: "Casablanca",
            rating: 5,
            image: "https://via.placeholder.com/50", // Replace with actual user image URL
        },
        {
            id: 2,
            text: "Leur service est impressionnant. Toujours disponibles et prêts à aider avec des solutions efficaces !",
            name: "<PERSON>",
            location: "Rabat",
            rating: 5,
            image: "https://via.placeholder.com/50", // Replace with actual user image URL
        },
        {
            id: 3,
            text: "Ils ont transformé notre site web en un outil moderne et optimisé. Je recommande fortement !",
            name: "<PERSON><PERSON><PERSON> <PERSON><PERSON>",
            location: "Tanger",
            rating: 4,
            image: "https://via.placeholder.com/50", // Replace with actual user image URL
        },
        {
            id: 4,
            text: "Un support client exceptionnel. Ils ont répondu rapidement à toutes nos préoccupations !",
            name: "Noura T.",
            location: "Marrakech",
            rating: 5,
            image: "https://via.placeholder.com/50", // Replace with actual user image URL
        },
    ];

    return (
        <div className="bg-gradient-to-b from-blue-800 to-blue-600 text-white py-16 px-4">
            <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-3xl font-semibold mb-4">Témoignages Clients</h2>
                <div className="flex flex-col items-center text-black mx-auto p-10">
                    <Typography variant="h5" className="font-bold text-white">
                        Excellent
                    </Typography>
                    <div className="flex items-center">
                        <svg
                            width="140"
                            height="30"
                            viewBox="0 0 163 30"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.00463867 29.9772H29.9345V0.0742188H0.00463867V29.9772Z" fill="#F4BA07" />
                            <path d="M25.519 12.7966L8.54311 25.1175L11.0202 17.5028L4.53589 12.7966H12.5509L15.0274 5.1814L17.504 12.7966H25.519ZM15.0281 20.4119L19.6642 19.4376L21.5114 25.1175L15.0281 20.4119Z" fill="white" />
                            <path d="M33.0793 29.9772H63.0092V0.0742188H33.0793V29.9772Z" fill="#F4BA07" />
                            <path d="M33.0793 29.9772H48.0443V0.0742188H33.0793V29.9772Z" fill="#F4BA07" />
                            <path d="M48.4024 20.2956L52.3779 19.4372L54.2167 25.2462L47.9821 20.5972L41.5045 25.2462L44.0186 17.6038L37.4368 12.8806H45.5722L48.0853 5.23767L50.5995 12.8806H58.7343L48.4024 20.2956Z" fill="white" />
                            <path d="M66.177 29.9772H96.1069V0.0742188H66.177V29.9772Z" fill="#F4BA07" />
                            <path d="M66.177 29.9772H81.1419V0.0742188H66.177V29.9772Z" fill="#F4BA07" />
                            <path d="M91.6919 12.7966L74.716 25.1175L77.1931 17.5028L70.7087 12.7966H78.7237L81.2003 5.1814L83.6769 12.7966L91.6919 12.7966ZM81.2009 20.4119L85.8371 19.4376L87.6842 25.1175L81.2009 20.4119Z" fill="white" />
                            <path d="M99.2742 29.9772H129.204V0.0742188H99.2742V29.9772Z" fill="#F4BA07" />
                            <path d="M99.2742 29.9772H114.239V0.0742188H99.2742V29.9772Z" fill="#F4BA07" />
                            <path d="M124.788 12.7966L107.813 25.1175L110.289 17.5028L103.805 12.7966H111.82L114.296 5.1814L116.773 12.7966L124.788 12.7966ZM114.297 20.4119L118.933 19.4376L120.78 25.1175L114.297 20.4119Z" fill="white" />
                            <path d="M132.349 29.9772H162.279V0.0742188H132.349V29.9772Z" fill="#DCDCE6" />
                            <path d="M132.349 29.9772H147.314V0.0742188H132.349V29.9772Z" fill="#F4BA07" />
                            <path d="M157.863 12.7966L140.888 25.1175L143.364 17.5028L136.88 12.7966H144.895L147.371 5.1814L149.848 12.7966H157.863ZM147.372 20.4119L152.008 19.4376L153.855 25.1175L147.372 20.4119Z" fill="white" />
                        </svg>
                    </div>
                    <Typography className="text-xs text-gray-600">Based on 103 reviews</Typography>
                    <img
                        src="/images/home/<USER>"
                        alt="Google"
                        className="w-16 mt-2"
                    />
                </div>
                <p className="text-sm mb-4">Basé sur 205 avis</p>
                <div className="mb-12">
                    <Slider {...settings}>
                        {reviews.map((review) => (
                            <div key={review.id} className="flex flex-col items-center px-4">
                                <div className="text-4xl text-gray-300 mb-4">“</div>
                                <p className="text-lg italic text-center max-w-2xl mb-6">
                                    {review.text}
                                </p>
                                <div className="flex items-center space-x-4">
                                    <img
                                        src={review.image}
                                        alt={review.name}
                                        className="w-14 h-14 rounded-full border-2 border-blue-300"
                                    />
                                    <div className="text-left">
                                        <h4 className="text-xl font-semibold">{review.name}</h4>
                                        <p className="text-sm text-blue-200">{review.location}</p>
                                        <div className="flex mt-2 text-yellow-400">
                                            {Array.from({ length: review.rating }).map((_, i) => (
                                                <span key={i}>⭐</span>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </Slider>
                </div>
            </div>
        </div>
    );
}

export default Testimonials2;
