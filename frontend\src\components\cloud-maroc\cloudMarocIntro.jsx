'use client'
import React, { useMemo } from 'react'
import { Typography } from '@material-tailwind/react'
import { motion } from "framer-motion";
import { Cloud } from 'lucide-react';
import Lottie from "lottie-react";
import cloudAnimation from "src/assets/cloud-maroc.json"; // Replace with your actual Lottie file path
import Section from '../home/<USER>';
import CTAButtons from '../home/<USER>';

function CloudMarocIntro({ t }) {
    // Memoize the translated content
    const heroContent = useMemo(
        () => ({
            title: t("cloud_maroc.title"),
            slogan: t("cloud_maroc.slogan"),
            description: t("cloud_maroc.description"),
        }),
        [t]
    );

    // Animation variants
    const animations = {
        fadeInLeft: {
            hidden: { opacity: 0, x: -50 },
            visible: { opacity: 1, x: 0, transition: { duration: 0.8 } }
        },
        fadeInRight: {
            hidden: { opacity: 0, x: 50 },
            visible: { opacity: 1, x: 0, transition: { duration: 0.8 } }
        }
    };

    return (
        <Section className="relative flex overflow-hidden">
            {/* Main Content Container */}
            <div className="relative z-10 flex flex-col lg:flex-row justify-between px-4 sm:px-6 lg:px-8">
                {/* Left Column: Text Content */}
                <motion.div
                    className="text-left space-y-8 max-w-7xl"
                    variants={animations.fadeInLeft}
                    initial="hidden"
                    animate="visible"
                >
                    {/* Tagline */}
                    <Typography
                        variant="small"
                        className="inline-flex items-center gap-2 text-indigo-500 font-inter py-2 px-4 rounded-full font-light tracking-widest uppercase border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-300"
                    >
                        <Cloud className="w-5 h-5" />
                        {heroContent.title}
                    </Typography>

                    {/* Main Slogan */}
                    <Typography
                        variant="h1"
                        className="text-2xl sm:text-4xl lg:text-5xl font-bold leading-tight tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500"
                    >
                        {heroContent.slogan}
                    </Typography>

                    {/* Description */}
                    <Typography
                        variant="paragraph"
                        className="text-base font-inter text-gray-600"
                    >
                        {heroContent.description}
                    </Typography>
                    <CTAButtons t={t} />
                </motion.div>

                {/* Right Column: Lottie Animation */}
                <motion.div
                    className="relative max-w-md lg:max-w-lg w-full mt-8 lg:mt-0"
                    variants={animations.fadeInRight}
                    initial="hidden"
                    animate="visible"
                    transition={{ delay: 0.4 }}
                    whileHover={{ scale: 1.02 }}
                >
                    <div className="relative mx-auto">
                        <div className="absolute inset-0 rounded-full blur-3xl bg-indigo-200 opacity-20" />
                        <Lottie
                            animationData={cloudAnimation}
                            loop={true}
                            autoplay={true}
                            className="w-full h-auto drop-shadow-xl"
                        />
                    </div>
                </motion.div>
            </div>

            {/* Background Decoration */}
        <div className="absolute bottom-0 left-0 w-full h-24 transform -skew-y-2 z-0">
          <div className="w-full h-full bg-white/10 backdrop-blur-sm" />
        </div>
        </Section>
    )
}

export default CloudMarocIntro;