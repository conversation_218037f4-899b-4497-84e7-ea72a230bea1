import { Card, Typography } from '@material-tailwind/react';
import { DOLLARTICKETsvg, H24svg, ICON6svg, MAPLOCATIONsvg, PERSONWITHSTARSsvg, SUPPORT2svg } from '../../icons/svgIcons';
import React from 'react';

const featuresData = [
    {
        icon: <ICON6svg width={40} height={40} />,
        title: 'featuresData.0.title',
        description: 'featuresData.0.description',
    },
    {
        icon: <MAPLOCATIONsvg />,
        title: 'featuresData.1.title',
        description: 'featuresData.1.description',
    },
    {
        icon: <SUPPORT2svg />,
        title: 'featuresData.2.title',
        description: 'featuresData.2.description',
    },
    {
        icon: <H24svg />,
        title: 'featuresData.3.title',
        description: 'featuresData.3.description',
    },
    {
        icon: <PERSONWITHSTARSsvg />,
        title: 'featuresData.4.title',
        description: 'featuresData.4.description',
    },
    {
        icon: <DOLLARTICKETsvg />,
        title: 'featuresData.5.title',
        description: 'featuresData.5.description',
    },
];

const WhyChooseUs = ({ t }) => {
    return (
        <section className="bg-white">
            <div className="max-w-[1400px] mx-auto px-6 py-10 md:py-14">
                <h2 className="text-2xl md:text-3xl font-bold text-center mb-8">
                    {t('whyChoose')}
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {featuresData.map((feature, index) => (
                        <Card
                            key={index}
                            className="group  relative p-6 bg-white rounded-lg shadow-md border overflow-hidden transition-all duration-500 ease-in-out"
                        >
                            {/* Gradient Background */}
                            <div
                                className={`absolute inset-0 bg-gradient-secondary scale-y-0 origin-bottom transition-transform duration-500 ease-in-out group-hover:scale-y-100`}
                            ></div>
                            <div className="flex flex-col items-start gap-y-4 z-10 relative">
                                <div className="text-secondary group-hover:bg-white rounded-full p-1 text-2xl flex-shrink-0">{feature.icon}</div>
                                <div>
                                    <Typography
                                        variant='h3'
                                        className="font-semibold text-lg mb-2 group-hover:text-white">
                                        {t(feature.title)}
                                    </Typography>
                                    <p className="text-gray-600 group-hover:text-white">
                                        {t(feature.description)}
                                    </p>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default WhyChooseUs;
