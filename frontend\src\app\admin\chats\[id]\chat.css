.chat-message-content {
  line-height: 1.5;
}

.chat-message-content ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.chat-message-content ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.chat-message-content p {
  margin-bottom: 0.5rem;
}

.chat-message-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.chat-message-content b,
.chat-message-content strong {
  font-weight: 600;
}

.chat-message-content i,
.chat-message-content em {
  font-style: italic;
}

.chat-message-content h1,
.chat-message-content h2,
.chat-message-content h3,
.chat-message-content h4,
.chat-message-content h5,
.chat-message-content h6 {
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.chat-message-content pre {
  background-color: #f3f4f6;
  padding: 0.5rem;
  border-radius: 0.25rem;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.chat-message-content code {
  font-family: monospace;
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.chat-message-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 0.5rem 0;
  color: #6b7280;
}

.chat-message-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5rem 0;
}

.chat-message-content th,
.chat-message-content td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

.chat-message-content th {
  background-color: #f3f4f6;
  font-weight: 600;
}

.chat-message-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.25rem;
  margin: 0.5rem 0;
}

.chat-message-content hr {
  border: 0;
  border-top: 1px solid #e5e7eb;
  margin: 1rem 0;
}
