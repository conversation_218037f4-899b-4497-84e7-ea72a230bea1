import { PhoneIcon } from "@heroicons/react/24/solid";
import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import whatsappLogo from "/public/images/home/<USER>";
import chatWithUsMobile from "/public/images/home/<USER>";
import chatwithus from "/public/images/home/<USER>";

function ChatWithUs({ t }) {
  return (
    <div className="w-full m-auto p-0  max-w-[1800px] mx-auto">
      <div className="bg-gradient-secondary text-white rounded-xl items-center p-2">
        <div className="flex flex-col lg:flex-row items-center lg:items-start gap-0 p-3 md:p-10">
          <div className="lg:w-1/2 my-auto font-inter gap-y-10">
            <Image
              src={whatsappLogo}
              alt="whatsApp"
              width={55}
              height={55}
              className="w-[55px] mb-5 hidden md:block"
            />
            <Typography
              variant="h1"
              className="text-3xl 2xl:text-5xl mb-4 font-poppins font-light"
            >
              {t("chat_with_us")}
            </Typography>
            <p className="text-gray-300 mb-6 text-lg 2xl:text-xl">
              {t("need_help")}
            </p>
            <div className="items-center space-x-4 mb-4 hidden md:flex">
              <Link
                href="tel:+212 662841605"
                className="2xl:text-xl flex items-center border border-[#FFFFFF7D] py-2 px-4 rounded-full text-white font-medium"
              >
                <i className="fab fa-whatsapp mr-2"></i> +212 662841605
              </Link>
              <Link
                href="https://wa.me/212662841605"
                className="2xl:text-xl underline cursor-pointer text-white"
              >
                {t("contact_us")}
              </Link>
            </div>
          </div>

          <div className="lg:w-1/2 relative mx-auto w-full md:h-full overflow-hidden rounded-3xl">
            <Image
              src={chatwithus}
              alt="Customer Service Team"
              className="hidden md:block rounded-lg w-full lg:w-auto h-[400px] mx-auto mb-10"
            />
            <div className="relative rounded-3xl">
              <div className="absolute w-full h-full bg-black bg-opacity-25"></div>
              <Image
                src={chatWithUsMobile}
                alt="Customer Service Team"
                className="block md:hidden rounded-3xl w-full lg:w-auto  mx-auto mb-10"
              />
            </div>
            <Image
              src="/images/home/<USER>"
              alt="whatsApp"
              width={55}
              height={55}
              className="w-[55px] absolute md:hidden top-3 right-4"
            />
            <div className="z-30 absolute md:-bottom-0 -bottom-0  right-0 left-0 w-fit mx-auto bg-dark-green rounded-lg px-4 py-1 md:py-2 flex items-center space-x-2">
              <Image
                src="/images/home/<USER>"
                alt="Satisfied customers"
                width={80}
                height={80}
                className="w-20 h-10 rounded-full"
              />
              <div>
                <p className="text-sm font-light font-poppins">230</p>
                <p className="text-xs text-white">{t("satisfied_customers")}</p>
                <div className="text-[#F4BA07] h-fit p-0 text-center font-extrabold">
                  ★★★★★
                </div>
              </div>
            </div>
          </div>

          <div className="w-full items-center gap-y-4 mb-4 md:hidden flex flex-col mt-10">
            <Link
              href="tel:+212662841605"
              className="w-full gap-x-2 font-inter text-lg text-center justify-center flex items-center border border-[#FFFFFF7D] py-2 px-4 rounded-full text-white font-medium"
            >
              <PhoneIcon className="w-5 h-5" />
              +212 662841605
            </Link>
            <Link
              href="https://wa.me/212662841605"
              className="underline cursor-pointer font-inter text-lg text-white"
            >
              {t("contact_us")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChatWithUs;
