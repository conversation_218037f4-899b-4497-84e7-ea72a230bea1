'use client';

import { Typography } from "@material-tailwind/react";

export default function BlogPostContent({ post }) {
    const renderBlock = (block) => {
        switch (block.type) {
            case 'heading_1':
                return (
                    <Typography
                        variant="h1"
                        key={block.id}
                        className="py-2 text-gray-900 text-3xl font-extrabold">
                        {block.content}
                    </Typography>
                );
            case 'heading_2':
                return (
                    <Typography
                        variant="h2"
                        key={block.id}
                        className="py-2 text-gray-900 text-2xl font-bold">
                        {block.content}
                    </Typography>
                );
            case 'heading_3':
                return (
                    <Typography
                        variant="h3"
                        key={block.id}
                        className="py-2 text-gray-900 text-xl font-semibold">
                        {block.content}
                    </Typography>
                );
            case 'paragraph':
                return block.content ? (
                    <p
                        key={block.id}
                        className="text-base text-gray-800 mb-4 md:max-w-[90%]">
                        {block.content}
                    </p>
                ) : null;
            case 'image':
                return block.content ? (
                    <img
                        key={block.id}
                        src={block.content}
                        alt={block.id}
                        className="rounded shadow-md w-full max-h-[500px] my-6 mx-auto object-cover"
                    />
                ) : null;
            case 'bulleted_list_item':
                return (
                    <ul key={block.id} className="list-disc pl-5 mb-4 md:max-w-[90%]">
                        <li>{block.content}</li>
                    </ul>
                );
            case 'numbered_list_item':
                return (
                    <ol key={block.id} className="list-disc pl-5 mb-4 md:max-w-[90%]">
                        <li>{block.content}</li>
                    </ol>
                );
            case 'divider':
                return <hr key={block.id} className="my-4" />;
            case 'column_list':
                return (
                    <div key={block.id} className="flex flex-col md:flex-row gap-x-10 my-2">
                        {block.columns.map((column) => (
                            <div key={column.id} className="w-full md:w-1/2 flex flex-col justify-center text-center mx-auto">
                                {column.children.map((child) => renderBlock(child))}
                            </div>
                        ))}
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <>
            <div className="md:px-32 p-5 pt-0 bg-gray-50 border">
                <div className="w-full mb-6">
                    <img
                        src={post.contentBlocks[0].content}
                        alt={post.title}
                        className="rounded max-h-[400px] w-fit mx-auto"
                    />
                </div>
                {post.contentBlocks.slice(1).map((block) => renderBlock(block))}
            </div>
        </>
    );
}
