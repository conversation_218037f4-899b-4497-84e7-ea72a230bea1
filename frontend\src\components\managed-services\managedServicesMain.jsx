'use client'
import { Typography } from '@material-tailwind/react';
import React from 'react';
import Image from 'next/image';
import Section from '../home/<USER>';
import { motion } from "framer-motion";
import { CheckCircle } from 'lucide-react';
import CTAButtons from '../home/<USER>';
import infogeranceAdvantageImg from "/public/images/services/infogerance-advantage.png";

const FEATURES = Object.freeze([
    {
      icon: <CheckCircle className="w-6 h-6 text-indigo-600" />,
      title: "features.0.title",
      description: "features.0.description",
    },
    {
      icon: <CheckCircle className="w-6 h-6 text-indigo-600" />,
      title: "features.1.title",
      description: "features.1.description",
    },
    {
      icon: <CheckCircle className="w-6 h-6 text-indigo-600" />,
      title: "features.2.title",
      description: "features.2.description",
    },
]);
function ManagedServicesMain({ t, animations }) {
    return (
        <Section bgColor="bg-white">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={animations.fadeInUp}
        >
          <Image
            src={infogeranceAdvantageImg}
            alt="Infogerance Advantage"
            width={900}
            height={900}
            sizes="(max-width: 768px) 100vw, 50vw"
            className="w-full hidden md:block h-auto rounded-xl"
            quality={85}
            loading="lazy"
          />
          <div className="space-y-6">
            <Typography
              variant="h2"
              className="text-3xl md:text-4xl font-semibold font-inter text-gray-900"
            >
              {t("cloud_advantages_title")}
            </Typography>
            <Typography
              variant="lead"
              className="text-lg font-inter text-gray-600"
            >
              {t("cloud_advantages_intro")}
            </Typography>
            <div className="space-y-4">
              {FEATURES.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  className="flex items-start space-x-3"
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={animations.fadeInUp}
                  transition={{ delay: index * 0.1 }}
                >
                  <span className="text-indigo-600 font-bold text-xl">
                    {feature.icon}
                  </span>
                  <p className="text-gray-700">
                    <span className="font-semibold font-inter text-gray-900">
                      {t(feature.title)}
                    </span>{" "}
                    - {t(feature.description)}
                  </p>
                </motion.div>
              ))}
            </div>
            <CTAButtons t={t} />
          </div>
          <Image
            src={infogeranceAdvantageImg}
            alt="Infogerance Advantage"
            width={900}
            height={900}
            sizes="(max-width: 768px) 100vw, 50vw"
            className="w-full block md:hidden h-auto rounded-xl"
            quality={85}
            loading="lazy"
          />
        </motion.div>
      </Section>
    )
}

export default ManagedServicesMain;