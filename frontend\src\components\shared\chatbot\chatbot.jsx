"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import ChatMessage from "./chatMessage";
import { ArrowUpIcon, XMarkIcon } from "@heroicons/react/24/solid";
import { useTranslations } from "next-intl";

const Chatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      role: "assistant",
      content: "Hello! I'm ZTech's AI assistant. How can I help you today?",
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const pathname = usePathname();
  const t = useTranslations("shared");

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Don't render on admin pages - MOVED BELOW ALL HOOKS
  if (pathname.includes("/admin") || pathname.includes("/client")) {
    return null;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (inputValue.trim() === "" || isLoading) return;

    const userMessage = { role: "user", content: inputValue };
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    try {
      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer 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",
        },
        body: JSON.stringify({
          model: "google/gemini-2.5-pro-preview", // Updated to the latest available Gemini model
          messages: [
            {
              role: "system",
              content: "You are a helpful assistant for ZTech Engineering, a company that specializes in web development, cloud hosting, managed services, servers, SSL, and AI services. Provide concise, helpful answers to customer queries."
            },
            ...messages.slice(-6), // Include only the last few messages for context
            userMessage,
          ],
        }),
      });

      const data = await response.json();
      const assistantMessage = {
        role: "assistant",
        content: data.choices[0].message.content,
      };
      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Error:", error);
      setMessages((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "Sorry, I'm having trouble connecting right now. Please try again later or contact us directly.",
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="fixed bottom-36 right-5 z-50 flex flex-col items-end">
      {isOpen && (
        <div className="mb-4 w-[330px] sm:w-[380px] h-[450px] bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col">
          {/* Chat header */}
          <div className="bg-indigo-900 text-white p-4 rounded-t-lg flex justify-between items-center">
            <div className="flex items-center">
              <Image
                src="/images/home/<USER>"
                alt="ZTech Logo"
                width={30}
                height={30}
                className="mr-2"
              />
              <h3 className="font-semibold">{t('chatbot_title') || 'ZTech Assistant'}</h3>
            </div>
            <button
              onClick={toggleChat}
              className="text-white hover:text-gray-300 transition"
              aria-label="Close chat"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Chat messages */}
          <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
            {messages.map((message, index) => (
              <ChatMessage key={index} message={message} />
            ))}
            {isLoading && (
              <div className="flex items-center space-x-2 py-2">
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: "0.4s" }}></div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input area */}
          <form onSubmit={handleSubmit} className="p-3 border-t border-gray-200 flex">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={t('chatbot_placeholder') || "Type your message..."}
              className="flex-1 border rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={isLoading || inputValue.trim() === ""}
              className={`bg-indigo-600 text-white rounded-r-lg px-4 py-2 flex items-center justify-center ${
                isLoading || inputValue.trim() === ""
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-indigo-700"
              }`}
            >
              <ArrowUpIcon className="h-4 w-4" />
            </button>
          </form>
        </div>
      )}

      {/* Chat toggle button */}
      <button
        onClick={toggleChat}
        className={`w-14 h-14 transition-all flex items-center justify-center rounded-full shadow-lg ${
          isOpen ? "bg-red-500 hover:bg-red-600" : "bg-white hover:bg-gray-100"
        }`}
        title="Chat with us"
      >
        {isOpen ? (
          <XMarkIcon className="h-6 w-6 text-white" />
        ) : (
          <Image
            src="/images/home/<USER>"
            alt="ZTech Chat"
            width={32}
            height={32}
          />
        )}
      </button>
    </div>
  );
};

export default Chatbot;
