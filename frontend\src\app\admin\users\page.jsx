"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  Typography,
  Input,
  Select,
  Option,
  Button,
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
  Chip,
  Radio,
} from "@material-tailwind/react";
import {
  MagnifyingGlassIcon,
  TrashIcon,
  PencilIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";
import { AccountState } from "../../config/AccountState";
import { AccountRole } from "../../config/AccountRole";
import { adminService } from "../../services/adminService";
import { toast } from "react-toastify";
import { Mail, ShieldBan, User, Users } from "lucide-react";
import useDebounce from "@/app/hook/useDebounce";
import Image from "next/image";
import { setServerMedia } from "@/app/helpers/helpers";

const ITEMS_PER_PAGE_OPTIONS = [5, 10, 25, 50];
const ROLE_OPTIONS = ["all", ...Object.values(AccountRole)];
const STATE_OPTIONS = ["all", ...Object.values(AccountState)];

const STATE_STYLES = {
  [AccountState.NOT_VERIFIED]: { bg: "bg-yellow-100", text: "text-yellow-800" },
  [AccountState.VERIFIED]: { bg: "bg-green-100", text: "text-green-800" },
  [AccountState.BANNED]: { bg: "bg-red-100", text: "text-red-800" },
  [AccountState.SUSPENDED]: { bg: "bg-orange-100", text: "text-orange-800" },
  [AccountState.DELETED]: { bg: "bg-gray-100", text: "text-gray-800" },
  unknown: { bg: "bg-gray-200", text: "text-gray-700" },
};

export default function AdminManagement() {
  const [admins, setAdmins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [roleFilter, setRoleFilter] = useState("all");
  const [stateFilter, setStateFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isNewAdmin, setIsNewAdmin] = useState(true);
  const [editingAdminId, setEditingAdminId] = useState(null);
  const [newAdmin, setNewAdmin] = useState({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    role: AccountRole.Customer,
    state: AccountState.NOT_VERIFIED,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [adminToDelete, setAdminToDelete] = useState(null);
  const [deleteType, setDeleteType] = useState("soft");

  useEffect(() => {
    fetchAdmins();
  }, [
    currentPage,
    itemsPerPage,
    roleFilter,
    stateFilter,
    debouncedSearchQuery,
  ]);

  const fetchAdmins = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        role: roleFilter !== "all" ? roleFilter : undefined,
        state: stateFilter !== "all" ? stateFilter : undefined,
        search: debouncedSearchQuery || undefined,
      };
      const response = await adminService.getAllUsers(params);
      if (response.status === 200) {
        setAdmins(response.data.data);
        setTotalPages(response.data.pagination.totalPages);
        setTotalItems(response.data.pagination.totalItems);
      }
    } catch (error) {
      console.error("Error fetching users data:", error);
      setError(error.response?.data?.error || "Failed to fetch admins");
      toast.error(error.response?.data?.error || "Failed to fetch admins");
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    itemsPerPage,
    roleFilter,
    stateFilter,
    debouncedSearchQuery,
  ]);

  const handleAddAdmin = async (e) => {
    e.preventDefault();
    try {
      const response = await adminService.addUser(newAdmin);
      if (response.status === 201) {
        toast.success("Admin added successfully");
        resetForm();
        setIsModalOpen(false);
        fetchAdmins();
      }
    } catch (err) {
      const errorMessage = err.response?.data?.error || "Failed to add admin";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  const handleEditAdmin = async (e) => {
    e.preventDefault();
    try {
      const updateData = { ...newAdmin };
      if (!updateData.password) {
        delete updateData.password;
      }
      const response = await adminService.updateUser(
        editingAdminId,
        updateData
      );
      if (response.status === 200) {
        toast.success("Admin updated successfully");
        resetForm();
        setIsModalOpen(false);
        fetchAdmins();
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.error || "Failed to update admin";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  };

  const handleDeleteClick = (id) => {
    setAdminToDelete(id);
    setDeleteModalOpen(true);
    setDeleteType("soft");
  };

  const handleDeleteAdmin = async () => {
    if (!adminToDelete) return;

    try {
      let response;
      if (deleteType === "soft") {
        const adminToSoftDelete = admins.find(
          (admin) => admin._id === adminToDelete
        );
        if (!adminToSoftDelete) throw new Error("Admin not found");

        const updateData = {
          ...adminToSoftDelete,
          state: AccountState.DELETED,
          password: undefined,
        };
        response = await adminService.updateUser(adminToDelete, updateData);
      } else {
        response = await adminService.deleteUser(adminToDelete);
      }

      if (response.status === 200) {
        toast.success(
          `Admin ${
            deleteType === "soft" ? "soft" : "permanently"
          } deleted successfully`
        );
        fetchAdmins();
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.error || `Failed to ${deleteType} delete admin`;
      toast.error(errorMessage);
    } finally {
      setDeleteModalOpen(false);
      setAdminToDelete(null);
      setDeleteType("soft");
    }
  };

  const handleEditClick = (admin) => {
    setNewAdmin({
      email: admin.email,
      password: "",
      firstName: admin.firstName,
      lastName: admin.lastName,
      role: admin.role,
      state: admin.state,
    });
    setEditingAdminId(admin._id);
    setIsNewAdmin(false);
    setIsModalOpen(true);
  };

  const resetForm = () => {
    setNewAdmin({
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      role: AccountRole.Customer,
      state: AccountState.NOT_VERIFIED,
    });
    setEditingAdminId(null);
    setIsNewAdmin(true);
    setError(null);
  };

  const getStateStyle = (state) => STATE_STYLES[state] || STATE_STYLES.unknown;

  return (
    <div className="space-y-6 p-6 h-[770px]">
      <Typography variant="h2" color="blue-gray" className="mb-4 flex items-center gap-1">
        <Users className="h-10 w-10" />
        Users Management
      </Typography>

      <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
        <div className="w-full md:w-1/3">
          <Input
            label="Search by Name or Email"
            icon={<MagnifyingGlassIcon className="h-5 w-5" />}
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setCurrentPage(1);
            }}
          />
        </div>
        <div className="w-full md:w-1/3">
          <Select
            label="Filter by Role"
            value={roleFilter}
            onChange={(value) => {
              setRoleFilter(value);
              setCurrentPage(1);
            }}
          >
            {ROLE_OPTIONS.map((role) => (
              <Option key={role} value={role}>
                {role === "all" ? "ALL" : role}
              </Option>
            ))}
          </Select>
        </div>
        <div className="w-full md:w-1/3">
          <Select
            label="Filter by State"
            value={stateFilter}
            onChange={(value) => {
              setStateFilter(value);
              setCurrentPage(1);
            }}
          >
            {STATE_OPTIONS.map((state) => (
              <Option key={state} value={state}>
                {state === "all" ? "ALL" : state}
              </Option>
            ))}
          </Select>
        </div>
        <Button
          color="blue"
          className="flex items-center gap-2"
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
        >
          <PlusIcon className="h-5 w-5" /> Add
        </Button>
      </div>

      <Card className="w-full overflow-hidden">
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full min-w-max table-auto text-left">
                <thead>
                  <tr>
                    {["User", "Email", "Role", "Status", "Registered", "Actions"].map(
                      (head) => (
                        <th
                          key={head}
                          className="border-b border-blue-gray-100 bg-blue-gray-50/50 p-4"
                        >
                          <Typography
                            variant="small"
                            color="blue-gray"
                            className="font-semibold leading-none opacity-70"
                          >
                            {head}
                          </Typography>
                        </th>
                      )
                    )}
                  </tr>
                </thead>
                <tbody>
                  {admins.length > 0 ? (
                    admins.map(
                      ({
                        photo,
                        _id,
                        firstName,
                        lastName,
                        email,
                        role,
                        state,
                        createdAt
                      }) => (
                        <tr
                          key={_id}
                          className="border-b border-blue-gray-50 hover:bg-blue-gray-50/30 transition-colors"
                        >
                          <td className="p-4">
                            <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full overflow-hidden bg-blue-gray-50/50 flex items-center justify-center">
                                {/* <User className="h-5 w-5 text-blue-gray-500" /> */}
                                {photo ? (
                                  <img
                                    src={setServerMedia(photo)}
                                    alt={`${firstName} ${lastName}`}
                                    className="h-full w-full object-cover"
                                    onError={(e) => {
                                      e.target.onerror = null;
                                      e.target.src =
                                        "/images/user-default-avatar.svg";
                                    }}
                                  />
                                ) : (
                                  <User className="h-5 w-5 text-blue-gray-500" />
                                )}
                              </div>
                              <div>
                                <Typography
                                  variant="small"
                                  color="blue-gray"
                                  className="font-semibold"
                                >
                                  {`${firstName} ${lastName}`}
                                </Typography>
                                <Typography
                                  variant="small"
                                  color="blue-gray"
                                  className="text-xs font-normal opacity-70"
                                >
                                  ID: {_id.substring(0, 8)}...
                                </Typography>
                              </div>
                            </div>
                          </td>
                          <td className="p-4">
                            <Typography
                              variant="small"
                              color="blue-gray"
                              className="flex items-center gap-1 font-normal text-gray-600"
                            >
                              <Mail className="w-3 h-3" />
                              {email}
                            </Typography>
                          </td>
                          <td className="p-4">
                            <div className="w-max">
                              <Chip
                                size="sm"
                                variant="ghost"
                                value={role}
                                color={
                                  role === AccountRole.Admin
                                    ? "blue"
                                    : role === AccountRole.Vendor
                                    ? "purple"
                                    : "gray"
                                }
                                className="font-medium capitalize"
                              />
                            </div>
                          </td>
                          <td className="p-4">
                            <div className="w-max">
                              <Chip
                                size="sm"
                                value={state}
                                className={`${getStateStyle(state).bg} ${
                                  getStateStyle(state).text
                                } font-medium rounded-full px-3 py-1 text-xs`}
                              />
                            </div>
                          </td>
                          <td>
                            <Typography
                              variant="small"
                              color="blue-gray"
                              className="font-normal"
                            >
                              {new Date(createdAt).toLocaleDateString("fr-FR")}
                            </Typography>
                          </td>
                          <td className="p-4">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="text"
                                color="blue"
                                size="sm"
                                onClick={() =>
                                  handleEditClick({
                                    _id,
                                    firstName,
                                    lastName,
                                    email,
                                    role,
                                    state,
                                  })
                                }
                                className="flex items-center gap-1 rounded-md p-2"
                              >
                                <PencilIcon className="h-4 w-4" />
                                <span className="hidden sm:inline">Edit</span>
                              </Button>
                              <Button
                                variant="text"
                                color="red"
                                size="sm"
                                onClick={() => handleDeleteClick(_id)}
                                className="flex items-center gap-1 rounded-md p-2"
                              >
                                <TrashIcon className="h-4 w-4" />
                                <span className="hidden sm:inline">Delete</span>
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )
                    )
                  ) : (
                    <tr>
                      <td colSpan={5} className="p-8 text-center">
                        <div className="flex flex-col items-center justify-center">
                          <User className="h-12 w-12 text-blue-gray-300 mb-2" />
                          <Typography
                            color="blue-gray"
                            className="font-normal opacity-70"
                          >
                            No users found
                          </Typography>
                          <Typography
                            color="blue-gray"
                            className="text-sm font-normal opacity-50"
                          >
                            Try adjusting your search or filters
                          </Typography>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            <div className="flex items-center justify-between p-4 border-t border-blue-gray-50">
              <div className="flex items-center gap-4">
                <Select
                  label="Items per page"
                  value={itemsPerPage.toString()}
                  onChange={(value) => {
                    setItemsPerPage(parseInt(value));
                    setCurrentPage(1);
                  }}
                  containerProps={{ className: "min-w-[120px]" }}
                >
                  {ITEMS_PER_PAGE_OPTIONS.map((option) => (
                    <Option key={option} value={option.toString()}>
                      {option}
                    </Option>
                  ))}
                </Select>
                <Typography
                  variant="small"
                  color="blue-gray"
                  className="font-normal"
                >
                  Page {currentPage} of {totalPages} ({totalItems} items)
                </Typography>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outlined"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1 || loading}
                  className="flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2}
                    stroke="currentColor"
                    className="h-4 w-4"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
                    />
                  </svg>
                  Previous
                </Button>
                <Button
                  variant="outlined"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages || loading}
                  className="flex items-center gap-2"
                >
                  Next
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2}
                    stroke="currentColor"
                    className="h-4 w-4"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                    />
                  </svg>
                </Button>
              </div>
            </div>
          </>
        )}
      </Card>

      <Dialog open={isModalOpen} handler={() => setIsModalOpen(false)}>
        <DialogHeader>
          {isNewAdmin ? "Add New Admin" : "Edit Admin"}
        </DialogHeader>
        <DialogBody divider>
          {error && (
            <div className="flex justify-center gap-1 font-semibold items-center p-4 my-4 text-white bg-red-200">
              <ShieldBan className="w-6 h-6" />
              {error}
            </div>
          )}
          <form className="space-y-4">
            <Input
              label="Email"
              type="email"
              value={newAdmin.email}
              onChange={(e) =>
                setNewAdmin({ ...newAdmin, email: e.target.value })
              }
              required
            />
            <Input
              label="Password"
              type="password"
              value={newAdmin.password}
              onChange={(e) =>
                setNewAdmin({ ...newAdmin, password: e.target.value })
              }
              required={isNewAdmin}
              placeholder={
                isNewAdmin ? "Enter password" : "Leave blank to keep unchanged"
              }
            />
            <div className="flex flex-col md:flex-row gap-4">
              <div className="w-full md:w-1/2">
                <Input
                  label="First Name"
                  value={newAdmin.firstName}
                  onChange={(e) =>
                    setNewAdmin({ ...newAdmin, firstName: e.target.value })
                  }
                  required
                />
              </div>
              <div className="w-full md:w-1/2">
                <Input
                  label="Last Name"
                  value={newAdmin.lastName}
                  onChange={(e) =>
                    setNewAdmin({ ...newAdmin, lastName: e.target.value })
                  }
                  required
                />
              </div>
            </div>
            <Select
              label="Role"
              value={newAdmin.role}
              onChange={(value) => setNewAdmin({ ...newAdmin, role: value })}
            >
              {Object.values(AccountRole).map((role) => (
                <Option key={role} value={role}>
                  {role}
                </Option>
              ))}
            </Select>
            <Select
              label="State"
              value={newAdmin.state}
              onChange={(value) => setNewAdmin({ ...newAdmin, state: value })}
            >
              {Object.values(AccountState).map((state) => (
                <Option key={state} value={state}>
                  {state}
                </Option>
              ))}
            </Select>
          </form>
        </DialogBody>
        <DialogFooter>
          <Button
            variant="text"
            color="gray"
            onClick={() => {
              setIsModalOpen(false);
              resetForm();
            }}
            className="mr-2"
          >
            Cancel
          </Button>
          <Button
            variant="gradient"
            color="blue"
            onClick={isNewAdmin ? handleAddAdmin : handleEditAdmin}
            disabled={loading}
          >
            {isNewAdmin ? "Add" : "Save"}
          </Button>
        </DialogFooter>
      </Dialog>

      <Dialog open={deleteModalOpen} handler={() => setDeleteModalOpen(false)}>
        <DialogHeader>Confirm Delete</DialogHeader>
        <DialogBody>
          <div className="space-y-4">
            <Typography>Please select delete type for this admin:</Typography>
            <div className="flex gap-4">
              <Radio
                name="deleteType"
                label="Soft Delete (Mark as deleted)"
                value="soft"
                checked={deleteType === "soft"}
                onChange={() => setDeleteType("soft")}
              />
              <Radio
                name="deleteType"
                label="Hard Delete (Permanent)"
                value="hard"
                checked={deleteType === "hard"}
                onChange={() => setDeleteType("hard")}
              />
            </div>
            <Typography color="gray" className="text-sm">
              {deleteType === "soft"
                ? "Soft delete will mark the admin as deleted but keep their data."
                : "Hard delete will permanently remove the admin and their data."}
            </Typography>
          </div>
        </DialogBody>
        <DialogFooter>
          <Button
            variant="text"
            color="gray"
            onClick={() => {
              setDeleteModalOpen(false);
              setAdminToDelete(null);
              setDeleteType("soft");
            }}
            className="mr-2"
          >
            Cancel
          </Button>
          <Button
            variant="gradient"
            color="red"
            onClick={handleDeleteAdmin}
            disabled={loading}
          >
            Confirm Delete
          </Button>
        </DialogFooter>
      </Dialog>
    </div>
  );
}
