"use client"
import { Typography } from '@material-tailwind/react'
import { useTranslations } from 'next-intl'
import Head from 'next/head'
import Link from 'next/link'
import React from 'react'

export default function NotFoundPage() {
    const t = useTranslations('404');

    return (
        <>
            <Head>
                <title>{t("404_page_not_found")}</title>
                <meta name="robots" content="noindex, nofollow" />
            </Head>
            <div className="flex flex-col items-center justify-center min-h-screen bg-white">
                <div className=" mb-auto flex flex-col gap-y-2 items-center justify-center ">
                    <img
                        className="w-[300px] 3xl:w-[500px] m-auto"
                        loading="lazy"
                        alt="page not found"
                        src="/images/404.svg" />
                    <Typography
                        variant="h1"
                        className="text-4xl max-w-3xl text-center font-jim_ngihtshade text-secondary">
                        {t("oops_message")}
                    </Typography>
                    <p
                        className="text-lg max-w-3xl text-center font-outfit">
                        {t("error_message")}
                        <br />
                        {t("suggestion")}
                    </p>
                    <Link href="/"
                        name='back_to_home'
                        className="px-6 py-2 bg-[#497ef7] text-white font-medium font-outfit rounded-md hover:bg-primary hover:text-white transition duration-300"
                        passHref
                    >
                        {t("back_to_home")}
                    </Link>
                </div>
            </div>
        </>
    )
}