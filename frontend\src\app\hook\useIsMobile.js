"use client";
import { useState, useEffect } from 'react';

export function useIsMobile() {
  // Initialize with null to avoid hydration mismatch
  const [isMobile, setIsMobile] = useState(null);
  
  useEffect(() => {
    // Set initial value
    setIsMobile(window.innerWidth < 768);
    
    // Create handler for window resize
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Return false during SSR, then the actual value after hydration
  return isMobile === null ? false : isMobile;
}