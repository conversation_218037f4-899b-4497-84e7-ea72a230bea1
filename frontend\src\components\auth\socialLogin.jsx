import { BACKEND_URL } from "@/app/config/constant";
import { <PERSON><PERSON> } from "@material-tailwind/react";
import React from "react";

const SocialMediaLogin = ({ t, title }) => {
  const handleSocialMediaSignIn = async (method) => {
    window.location.href = `${BACKEND_URL}/auth/${method}`;
  };

  return (
    <div className="flex flex-col items-center w-full max-w-sm mx-auto space-y-4">
      <h2 className="text-2xl font-medium">{t(title)}</h2>
      <div className="flex gap-x-6">
        <Button
          onClick={() => handleSocialMediaSignIn("google")}
          size="lg"
          variant="outlined"
          className="relative flex items-center justify-center w-20 h-12 p-0 border-gray-300 bg-white transition-all duration-200 hover:bg-gray-50"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
          >
            <path
              fill="#4285F4"
              d="M20.64 12.2045c0-.6381-.0573-1.2518-.1636-1.8409H12v3.4814h4.8436c-.2086 1.125-.8427 2.0782-1.7959 2.7164v2.2581h2.9087c1.7018-1.5668 2.6836-3.874 2.6836-6.615z"
            ></path>
            <path
              fill="#34A853"
              d="M12 21c2.43 0 4.4673-.806 5.9564-2.1805l-2.9087-2.2581c-.8059.54-1.8368.859-3.0477.859-2.344 0-4.3282-1.5831-5.036-3.7104H3.9574v2.3318C5.4382 18.9832 8.4818 21 12 21z"
            ></path>
            <path
              fill="#FBBC05"
              d="M6.964 13.71c-.18-.54-.2822-1.1168-.2822-1.71s.1023-1.17.2823-1.71V7.9582H3.9573A8.9965 8.9965 0 0 0 3 12c0 1.4523.3477 2.8268.9573 4.0418L6.964 13.71z"
            ></path>
            <path
              fill="#EA4335"
              d="M12 6.5795c1.3214 0 2.5077.4541 3.4405 1.346l2.5813-2.5814C16.4632 3.8918 14.426 3 12 3 8.4818 3 5.4382 5.0168 3.9573 7.9582L6.964 10.29C7.6718 8.1627 9.6559 6.5795 12 6.5795z"
            ></path>
          </svg>
        </Button>

        <Button
          onClick={() => handleSocialMediaSignIn("facebook")}
          size="lg"
          color="blue"
          className="relative flex items-center justify-center w-20 h-12 p-0 transition-all duration-200 hover:bg-blue-700"
        >
          <svg
            xmlns="https://www.w3.org/2000/svg"
            viewBox="0 0 1365.3 1365.3"
            height="24"
            width="24"
          >
            <path
              fill="#fff"
              d="M1365.3 682.7A682.7 682.7 0 10576 1357V880H402.7V682.7H576V532.3c0-171.1 102-265.6 257.9-265.6 74.6 0 152.8 13.3 152.8 13.3v168h-86.1c-84.8 0-111.3 52.6-111.3 106.6v128h189.4L948.4 880h-159v477a682.8 682.8 0 00576-674.3"
            />
          </svg>
        </Button>

        <Button
          onClick={() => handleSocialMediaSignIn("github")}
          size="lg"
          className="relative flex items-center justify-center w-20 h-12 p-0 bg-gray-800 transition-all duration-200 hover:bg-gray-900"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="#fff"
          >
            <path d="M12 2a10 10 0 00-3.16 19.5c.5.09.68-.22.68-.48v-1.7c-2.77.6-3.36-1.3-3.36-1.3-.45-1.15-1.1-1.45-1.1-1.45-.9-.62.07-.6.07-.6 1 .07 1.52 1 1.52 1a2.27 2.27 0 003.08.88 2.3 2.3 0 01.68-1.43c-2.22-.26-4.55-1.12-4.55-5a3.9 3.9 0 011-2.72 3.63 3.63 0 01.1-2.68s.84-.28 2.75 1a9.68 9.68 0 015 0c1.91-1.3 2.75-1 2.75-1a3.63 3.63 0 01.1 2.68 3.9 3.9 0 011 2.72c0 3.88-2.33 4.74-4.55 5a2.53 2.53 0 01.72 1.95v2.9c0 .27.18.58.69.48A10 10 0 0012 2" />
          </svg>
        </Button>
      </div>
    </div>
  );
};

export default SocialMediaLogin;
