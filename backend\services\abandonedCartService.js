/**
 * Service for handling abandoned cart notifications
 */
const Cart = require('../models/Cart');
const User = require('../models/User');
const Notification = require('../models/Notification');
const NotificationSetting = require('../models/NotificationSetting');
// Email sending functionality
const { sendAbandonedCartEmail } = require('../routes/sendEmail/sendEmail');
const cron = require('node-cron');

// Cache for the socket service instance and cron job
let socketServiceInstance = null;
let cronJob = null;

/**
 * Find abandoned carts based on notification settings
 * @returns {Promise<Array>} - Array of abandoned carts with user data
 */
const findAbandonedCarts = async () => {
  try {
    console.log('[ABANDONED CART] Looking for abandoned carts...');

    // Get notification settings for abandoned cart
    let setting = await NotificationSetting.findOne({ type: 'abandoned_cart' });

    // Use default settings if none exist
    if (!setting) {
      setting = {
        enabled: true,
        timeThreshold: 20, // Default to 20 seconds for testing
        emailEnabled: true, // Now enabled by default
        inAppEnabled: true
      };
      console.log('[ABANDONED CART] Using default settings (not found in database)');
    } else {
      console.log(`[ABANDONED CART] Using settings from database: threshold=${setting.timeThreshold}s, enabled=${setting.enabled}, emailEnabled=${setting.emailEnabled}`);
    }

    // If notifications are disabled, return empty array
    if (!setting.enabled) {
      console.log('[ABANDONED CART] Notifications are disabled, skipping check');
      return [];
    }

    // Calculate the cutoff time based on the time threshold
    const cutoffTime = new Date();
    cutoffTime.setSeconds(cutoffTime.getSeconds() - setting.timeThreshold);

    console.log(`[ABANDONED CART] Cutoff time: ${cutoffTime.toISOString()}`);
    console.log(`[ABANDONED CART] Current time: ${new Date().toISOString()}`);

    // First, let's see how many carts exist in total
    const totalCarts = await Cart.countDocuments({});
    const cartsWithItems = await Cart.countDocuments({
      items: { $exists: true, $ne: [] },
      cartCount: { $gt: 0 }
    });

    console.log(`[ABANDONED CART] Total carts in database: ${totalCarts}`);
    console.log(`[ABANDONED CART] Carts with items: ${cartsWithItems}`);

    // Find carts that have items and haven't been updated since the cutoff time
    const abandonedCarts = await Cart.find({
      items: { $exists: true, $ne: [] }, // Cart has items
      updatedAt: { $lt: cutoffTime }, // Cart was last updated more than the threshold time ago
      cartCount: { $gt: 0 } // Cart has at least one item
    }).populate({
      path: 'user',
      select: 'firstName lastName email favoriteLang'
    }).populate({
      path: 'items.package',
      select: 'name name_fr price'
    });

    console.log(`[ABANDONED CART] Found ${abandonedCarts.length} abandoned carts (threshold: ${setting.timeThreshold}s)`);

    // Log details about each abandoned cart for debugging
    abandonedCarts.forEach((cart, index) => {
      console.log(`[ABANDONED CART] Cart ${index + 1}: ID=${cart._id}, User=${cart.user?.email || 'No user'}, Items=${cart.cartCount}, LastUpdated=${cart.updatedAt}`);
    });

    // Return the carts along with the settings
    return {
      carts: abandonedCarts,
      settings: setting
    };
  } catch (error) {
    console.error('[ABANDONED CART] Error finding abandoned carts:', error);
    return { carts: [], settings: null };
  }
};

/**
 * Send notifications for abandoned carts
 * @param {Object} data - Object containing carts and settings
 * @returns {Promise<void>}
 */
const sendAbandonedCartNotifications = async (data) => {
  try {
    const { carts, settings } = data;

    if (!carts || carts.length === 0) {
      console.log('[ABANDONED CART] No carts to send notifications for');
      return;
    }

    if (!settings) {
      console.log('[ABANDONED CART] No settings available, using defaults');
    }

    console.log(`[ABANDONED CART] Sending notifications for ${carts.length} carts`);

    // Use settings or defaults
    const emailEnabled = settings ? settings.emailEnabled : false;
    const inAppEnabled = settings ? settings.inAppEnabled : true;
    const notificationTitle = settings ? settings.notificationTitle : 'Your Cart is Waiting';
    let notificationMessage = settings ? settings.notificationMessage : 'You have {{cartCount}} item(s) in your cart. Complete your purchase!';

    for (const cart of carts) {
      const user = cart.user;

      if (!user || !user.email) {
        console.log(`[ABANDONED CART] Skipping cart ${cart._id} - no valid user`);
        continue;
      }

      // Send email notification if enabled
      if (emailEnabled) {
        try {
          // Calculate correct total price including periods and discounts (same logic as Cart model)
          const subtotal = cart.items.reduce(
            (acc, item) =>
              acc + item.price * item.quantity * item.period - item.discount,
            0
          );

          // Calculate total discount for display
          const totalDiscount = cart.items.reduce((acc, item) => acc + item.discount, 0);

          // Apply tax rate (20% as used in frontend)
          const TAX_RATE = 0.2;
          const taxAmount = subtotal * TAX_RATE;
          const totalWithTax = subtotal + taxAmount;

          // Prepare cart data for the email template
          const cartData = {
            items: cart.items,
            subtotal: subtotal,
            totalDiscount: totalDiscount,
            taxAmount: taxAmount,
            totalPrice: totalWithTax, // Total including tax
            cartCount: cart.cartCount
          };

          // Send email using the dedicated abandoned cart email function
          await sendAbandonedCartEmail(user, cartData);

          console.log(`[ABANDONED CART] Email sent to ${user.email}`);
        } catch (emailError) {
          console.error(`[ABANDONED CART] Failed to send email to ${user.email}:`, emailError);
        }
      } else {
        console.log(`[ABANDONED CART] Email sending skipped for ${user.email} (disabled in settings)`);
      }

      // Send in-app notification if enabled
      if (inAppEnabled) {
        try {
          if (global.io) {
            const socketService = require('./socketService')(global.io);

            // Replace placeholders in the message
            const message = notificationMessage.replace('{{cartCount}}', cart.cartCount);

            const notification = {
              type: 'abandoned_cart',
              title: notificationTitle,
              message: message,
              link: '/client/cart',
            };

            await socketService.notifyUser(notification, user._id);
            console.log(`[ABANDONED CART] In-app notification sent to user ${user._id}`);
          }
        } catch (notificationError) {
          console.error(`[ABANDONED CART] Failed to send in-app notification to ${user._id}:`, notificationError);
        }
      } else {
        console.log(`[ABANDONED CART] In-app notification skipped for ${user._id} (disabled in settings)`);
      }
    }
  } catch (error) {
    console.error('[ABANDONED CART] Error sending notifications:', error);
  }
};

/**
 * Process abandoned carts - find them and send notifications
 * @returns {Promise<void>}
 */
const processAbandonedCarts = async () => {
  try {
    console.log('[ABANDONED CART] Starting abandoned cart processing...');

    // Find abandoned carts with settings
    const result = await findAbandonedCarts();

    if (!result.carts || result.carts.length === 0) {
      console.log('[ABANDONED CART] No abandoned carts found');
      return;
    }

    // Send notifications for abandoned carts
    await sendAbandonedCartNotifications(result);

    console.log('[ABANDONED CART] Abandoned cart processing completed');
  } catch (error) {
    console.error('[ABANDONED CART] Error processing abandoned carts:', error);
  }
};

/**
 * Stop the current cron job if it exists
 */
const stopCronJob = () => {
  if (cronJob) {
    console.log('[ABANDONED CART] Stopping existing cron job');
    cronJob.stop();
    cronJob = null;
  }
};

/**
 * Start the abandoned cart cron job based on settings
 * @returns {Promise<Object>} - The cron job object
 */
const startAbandonedCartCronJob = async () => {
  try {
    // Stop any existing cron job
    stopCronJob();

    // Get notification settings for abandoned cart
    let setting = await NotificationSetting.findOne({ type: 'abandoned_cart' });

    // Use default settings if none exist
    if (!setting) {
      setting = {
        enabled: true,
        timeThreshold: 20, // Default to 20 seconds for testing
        cronSchedule: '* * * * *', // Every minute for testing
        emailEnabled: true, // Now enabled by default
        inAppEnabled: true
      };
      console.log('[ABANDONED CART] Using default cron schedule (not found in database)');

      // Create the setting in the database
      try {
        await NotificationSetting.create({
          type: 'abandoned_cart',
          enabled: true,
          timeThreshold: 20,
          cronSchedule: '* * * * *',
          emailEnabled: true, // Now enabled by default
          inAppEnabled: true,
          notificationTitle: 'Your Cart is Waiting',
          notificationMessage: 'You have {{cartCount}} item(s) in your cart. Complete your purchase!'
        });
        console.log('[ABANDONED CART] Created default notification settings in database');
      } catch (createError) {
        console.error('[ABANDONED CART] Error creating default notification settings:', createError);
      }
    }

    // If notifications are disabled, don't start the cron job
    if (!setting.enabled) {
      console.log('[ABANDONED CART] Abandoned cart notifications are disabled, not starting cron job');
      return null;
    }

    // Validate cron expression before scheduling
    if (!cron.validate(setting.cronSchedule)) {
      console.error(`[ABANDONED CART] Invalid cron expression: ${setting.cronSchedule}`);
      throw new Error(`Invalid cron expression: ${setting.cronSchedule}`);
    }

    // Schedule the job using the cron schedule from settings
    cronJob = cron.schedule(setting.cronSchedule, async () => {
      console.log(`[ABANDONED CART] Running abandoned cart cron job - checking for carts inactive for ${setting.timeThreshold}+ seconds`);
      await processAbandonedCarts();
    }, {
      scheduled: true,
      timezone: "UTC"
    });

    console.log(`[ABANDONED CART] Abandoned cart cron job scheduled with pattern: ${setting.cronSchedule}`);
    console.log(`[ABANDONED CART] Cron job status: ${cronJob ? 'active' : 'failed'}`);
    return cronJob;
  } catch (error) {
    console.error('[ABANDONED CART] Error starting abandoned cart cron job:', error);
    return null;
  }
};

/**
 * Get the current status of the cron job
 * @returns {Object} - Status information about the cron job
 */
const getCronJobStatus = () => {
  return {
    isRunning: cronJob ? true : false,
    cronJob: cronJob ? 'active' : 'inactive'
  };
};

module.exports = {
  findAbandonedCarts,
  sendAbandonedCartNotifications,
  processAbandonedCarts,
  startAbandonedCartCronJob,
  stopCronJob,
  getCronJobStatus
};
