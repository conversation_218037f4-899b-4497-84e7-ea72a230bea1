import apiService from '../lib/apiService';

const jobService = {

    getAllJobs: () => apiService.get('/jobs', { withCredentials: true }),

    getJobById: (id) => apiService.get(`/jobs/${id}`, { withCredentials: true }),

    applyForJob: (jobId, applicationData) => apiService.post(`/jobs/${jobId}/apply`, applicationData, { withCredentials: true }),

    // Get user's application for a specific job
    getUserApplication: (jobId) => apiService.get(`/jobs/${jobId}/my-application`, { withCredentials: true }),

    // Get user's application interviews
    getUserApplicationInterviews: (jobId) => apiService.get(`/jobs/${jobId}/my-interviews`, { withCredentials: true })

}

export default jobService;