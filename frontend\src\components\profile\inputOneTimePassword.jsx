'use client'
import React, { useState } from "react";
import { Input, Typography, Button } from "@material-tailwind/react";
import { useRouter } from "next/navigation";
import profileService from "../../app/services/profileService";
import { toast } from "react-toastify";

function InputOneTimePassword({ userId, t, newEmail }) {
    const inputRefs = React.useRef([]);
    const [otp, setOtp] = React.useState(Array(6).fill(""));
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState();

    const router = useRouter();

    const handleChange = (index, value) => {
        const newOtp = [...otp];
        newOtp[index] = value.replace(/[^0-9]/g, "");
        setOtp(newOtp);
        console.log("OTP : ", otp.length);
        if (value && index < inputRefs.current.length - 1) {
            inputRefs.current[index + 1].focus();
        }
    };

    const handleOtpSubmit = async () => {
        setLoading(true);
        setError(null);

        const otpCode = otp.join('');
        if (otpCode.length !== 6 || otp.some((digit) => digit === '')) {
            setError(t('enter_valid_otp'));
            setLoading(false);
            return;
        }

        try {
            const response = await profileService.verifyOtp({ id: userId, otpCode });
            console.log("OTP verified response:", response.data);
            if (!response.data.success) {
                toast.error(t('failed_to_verify_otp'));
                setError(response.data.errorMsg || t('failed_to_verify_otp'));
            } else {
                // alert("OTP Verified Successfully");
                toast.success(t('otp_verified_successfully'));
                // setTimeout(function () {
                router.push("/client/profile");
                // }, 2000);
                // router.push("/client/profile");
            }
        } catch (error) {
            setError("An error occurred. Please try again.");
            console.error("OTP Verification Error:", error);
        } finally {
            setLoading(false);
        }
    };

    function handleBackspace(event, index) {
        if (event.key === "Backspace" && !event.target.value && index > 0) {
            console.log(inputRefs.current[index - 1]);
            inputRefs.current[index - 1].focus();
        }
    }

    return (
        <div className="w-full max-w-sm m-auto">
            <Typography
                variant="small"
                color="blue-gray"
                className="flex flex-col items-center justify-center text-center font-medium"
            >
                <span>{t('enter_otp_code')}</span>
                <span className="font-semibold text-xs">{newEmail}</span>
            </Typography>

            <div className="my-4 flex items-center justify-center gap-2">
                {otp.map((digit, index) => (
                    <React.Fragment key={index}>
                        <Input
                            type="text"
                            maxLength={1}
                            className="!w-10 appearance-none !border-t-blue-gray-200 text-center !text-lg placeholder:text-blue-gray-300 placeholder:opacity-100 focus:!border-t-gray-900"
                            labelProps={{
                                className: "before:content-none after:content-none",
                            }}
                            containerProps={{
                                className: "!min-w-0 !w-10 !shrink-0",
                            }}
                            value={digit}
                            onChange={(e) => handleChange(index, e.target.value)}
                            onKeyDown={(e) => handleBackspace(e, index)}
                            inputRef={(el) => (inputRefs.current[index] = el)}
                        />
                        {index === 2 && <span className="text-2xl text-slate-700">-</span>}
                    </React.Fragment>
                ))}
            </div>
            <div className="">
                {error && (<p className="text-red-500 text-xs">{error}</p>)}
            </div>
            <div className="flex justify-evenly py-3 ">
                <Button
                    onClick={() => handleOtpSubmit()}
                    disabled={loading}
                    className="bg-secondary  px-8 py-2 text-white font-normal font-poppins rounded-md shadow-sm hover:shadow-md">
                    {loading ? t('verifying') : t('verify_code')}
                </Button>
                <Button
                    onClick={() => router.push('/client/profile')}
                    disabled={loading}
                    className="bg-gray-100 px-8 py-2 shadow-sm text-gray-900 hover:bg-gray-200 border border-gray-200 font-normal font-poppins rounded-md hover:shadow-md">
                    {t('cancel')}
                </Button>
            </div>

            <Typography
                variant="small"
                className="text-center font-normal text-blue-gray-500 text-sm"
            >
                {t('did_not_receive_code')}
                <span className="font-bold mx-2">
                    {t('resend')}
                </span>
            </Typography>
        </div>
    );
}
export default InputOneTimePassword;