const Notification = require('../../models/Notification');

// Get all notifications for an admin (or all admin notifications)
exports.getAdminNotifications = async (req, res) => {
  try {
    const adminId = req.user._id; // Assuming adminId is available from auth middleware
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Create a base query that matches either:
    // 1. Notifications specifically for this admin (with adminId)
    // 2. General admin notifications (without specific adminId)
    const baseQuery = {
      userType: 'admin',
      $or: [
        { adminId: adminId },
        { adminId: { $exists: false } },
        { adminId: null }
      ]
    };

    // Create the query for fetching notifications
    const query = { ...baseQuery };
    if (req.query.unreadOnly === 'true') {
      query.isRead = false;
    }

    // Fetch notifications
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Count total notifications
    const totalNotifications = await Notification.countDocuments(query);

    // Count unread notifications
    const unreadCount = await Notification.countDocuments({
      ...baseQuery,
      isRead: false
    });

    console.log(`Fetched ${notifications.length} notifications for admin ${adminId}, unread count: ${unreadCount}`);

    res.status(200).json({
      notifications,
      totalPages: Math.ceil(totalNotifications / limit),
      currentPage: page,
      totalNotifications,
      unreadCount
    });
  } catch (error) {
    console.error('Error fetching admin notifications:', error);
    res.status(500).json({ error: 'Failed to fetch notifications' });
  }
};

// Mark a notification as read
exports.markNotificationAsRead = async (req, res) => {
  try {
    const notificationId = req.params.notificationId;
    const adminId = req.user._id;

    // Create a query that allows an admin to mark a notification as read if:
    // 1. It's specifically for this admin (with matching adminId)
    // 2. It's a general admin notification (without specific adminId)
    const query = {
      _id: notificationId,
      userType: 'admin',
      $or: [
        { adminId: adminId },
        { adminId: { $exists: false } },
        { adminId: null }
      ]
    };

    const notification = await Notification.findOneAndUpdate(
      query,
      { isRead: true },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found or not authorized' });
    }

    console.log(`Notification ${notificationId} marked as read by admin ${adminId}`);
    res.status(200).json({ message: 'Notification marked as read', notification });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ error: 'Failed to mark notification as read' });
  }
};

// Mark all notifications as read for an admin
exports.markAllNotificationsAsRead = async (req, res) => {
  try {
    const adminId = req.user._id;

    // Create a query that matches either:
    // 1. Notifications specifically for this admin (with adminId)
    // 2. General admin notifications (without specific adminId)
    const query = {
      userType: 'admin',
      isRead: false,
      $or: [
        { adminId: adminId },
        { adminId: { $exists: false } },
        { adminId: null }
      ]
    };

    const result = await Notification.updateMany(
      query,
      { isRead: true }
    );

    console.log(`Marked ${result.modifiedCount} notifications as read for admin ${adminId}`);

    res.status(200).json({
      message: 'All notifications marked as read',
      count: result.modifiedCount
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ error: 'Failed to mark all notifications as read' });
  }
};