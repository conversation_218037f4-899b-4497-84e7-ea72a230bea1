"use client"

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

const WhatsAppFloatingButton = () => {
    const pathname = usePathname();

    if(pathname.includes('/admin')) return null;

    return (
        <div className="">
            <Link
                href="https://wa.me/212662841605"
                target="_blank"
                rel="noopener noreferrer"
                className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center transition-all hover:bg-gray-100"
                title="Contact us on WhatsApp"
            >
                <Image
                    src="/images/home/<USER>"
                    alt="WhatsApp"
                    width={28}
                    height={28}
                />
            </Link>
        </div>
    );
};

export default WhatsAppFloatingButton;
