/**
 * <PERSON><PERSON>t to update the storage mapping format to use storageDescription instead of separate type and diskSize
 */

const fs = require('fs');
const path = require('path');

const mappingFilePath = path.join(__dirname, '../constants/vps-storage-mapping.js');

// Read the current file
let content = fs.readFileSync(mappingFilePath, 'utf8');

// Replace the old format with new format using regex
content = content.replace(
  /{\s*type:\s*"([^"]+)",\s*productId:\s*"([^"]+)",\s*diskSize:\s*"([^"]+)",\s*isDefault:\s*(true|false),\s*additionalPrice:\s*(\d+)[^}]*}/g,
  (match, type, productId, diskSize, isDefault, additionalPrice) => {
    return `{
      productId: "${productId}",
      storageDescription: "${diskSize}",
      isDefault: ${isDefault},
      additionalPrice: ${additionalPrice}
    }`;
  }
);

// Write the updated content back
fs.writeFileSync(mappingFilePath, content, 'utf8');

console.log('✅ Storage mapping format updated successfully!');
console.log('📝 Updated format: productId + storageDescription instead of type + diskSize');
