"use client";

import { <PERSON>, CardB<PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Chip } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter, useParams } from "next/navigation";
import { ArrowLeft, Download, CheckCircle, Home, ChevronRight, Receipt, FileText } from "lucide-react";
import paymentService from "../../../../services/paymentService";
import { formatDate } from "../../../../utils/dateFormatter";
import { PDFDownloadLink, Document, Page, Text } from "@react-pdf/renderer";
import InvoiceTemplate from '../../../../../components/pdf/InvoiceTemplate';
import { getLocalizedContent } from "@/app/helpers/helpers";
console.log("🚀 ~ getLocalizedContent:", getLocalizedContent);

// Replace the MyDocument component with:
const MyDocument = ({ payment, t }) => (
  <InvoiceTemplate payment={payment} t={t} />
);

export default function PaymentDetailsPage() {
  const t = useTranslations("client");
  const ht = useTranslations('hosting');
  const [payment, setPayment] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const params = useParams();

  useEffect(() => {
    const fetchPaymentDetails = async () => {
      try {
        const res = await paymentService.getPaymentDetails(params.paymentId);
        if (res.data?.payment) {
          setPayment(res.data.payment);
        }
      } catch (error) {
        console.error("Error fetching payment details:", error?.response?.data || error.message);
      } finally {
        setLoading(false);
      }
    };
    fetchPaymentDetails();
  }, [params.paymentId]);

  const calculateEndDate = (startDate, periodInMonths) => {
    const date = new Date(startDate);
    date.setMonth(date.getMonth() + parseInt(periodInMonths));
    return formatDate(date);
  };

  const getStatusColor = (status) => {
    switch(status?.toLowerCase()) {
      case 'active':
      case 'completed':
        return 'green';
      case 'pending':
        return 'amber';
      case 'cancelled':
      case 'failed':
        return 'red';
      default:
        return 'blue';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gray-50">
        <div className="mb-0 flex flex-col md:flex-row md:items-center bg-white p-4 rounded-t-xl shadow-sm border border-gray-200">
          <Typography variant="h1" className="text-2xl font-bold text-black mb-2 md:mb-0">
            {t("payment_details")}
          </Typography>

          <div className="hidden md:flex items-center">
            <div className="mx-3 h-5 w-px bg-gray-300"></div>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Home className="h-4 w-4" />
              <span className="cursor-pointer hover:text-blue-600 transition-colors duration-200" onClick={() => router.push('/')}>
                {t("home")}
              </span>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-600">
                {t("payment_history")}
              </span>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-800 font-medium">
                {t("payment_details")}
              </span>
            </div>
          </div>

          {/* Mobile breadcrumb */}
          <div className="flex md:hidden items-center gap-2 text-sm text-gray-600">
            <Home className="h-4 w-4" />
            <span className="cursor-pointer hover:text-blue-600 transition-colors duration-200" onClick={() => router.push('/')}>
              {t("home")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-600">
              {t("payment_history")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-800 font-medium">
              {t("payment_details")}
            </span>
          </div>
        </div>

        <div className="bg-white rounded-b-xl shadow-sm border-x border-b border-gray-200 p-6">
          <div className="flex items-center mb-6">
            <div className="h-6 bg-gray-200 rounded animate-pulse w-1/4"></div>
          </div>

          <div className="space-y-6">
            <div>
              <div className="h-6 bg-gray-200 rounded animate-pulse w-1/6 mb-4"></div>
              <div className="grid grid-cols-6 gap-4 border-b border-gray-200 pb-4">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="h-6 bg-gray-200 rounded animate-pulse"></div>
                ))}
              </div>
              <div className="py-4">
                {[...Array(3)].map((_, rowIndex) => (
                  <div key={rowIndex} className="grid grid-cols-6 gap-4 mb-4">
                    {[...Array(6)].map((_, colIndex) => (
                      <div key={colIndex} className="h-4 bg-gray-100 rounded animate-pulse"></div>
                    ))}
                  </div>
                ))}
              </div>
            </div>

            <div>
              <div className="h-6 bg-gray-200 rounded animate-pulse w-1/6 mb-4"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="flex justify-between">
                    <div className="h-4 bg-gray-100 rounded animate-pulse w-1/4"></div>
                    <div className="h-4 bg-gray-100 rounded animate-pulse w-1/6"></div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center">
              <div className="h-10 bg-gray-200 rounded animate-pulse w-48"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gray-50">
        <div className="mb-0 flex flex-col md:flex-row md:items-center bg-white p-4 rounded-t-xl shadow-sm border border-gray-200">
          <Typography variant="h1" className="text-2xl font-bold text-black mb-2 md:mb-0">
            {t("payment_details")}
          </Typography>

          <div className="hidden md:flex items-center">
            <div className="mx-3 h-5 w-px bg-gray-300"></div>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Home className="h-4 w-4" />
              <span className="cursor-pointer hover:text-blue-600 transition-colors duration-200" onClick={() => router.push('/')}>
                {t("home")}
              </span>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-600">
                {t("payment_history")}
              </span>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-800 font-medium">
                {t("payment_details")}
              </span>
            </div>
          </div>

          {/* Mobile breadcrumb */}
          <div className="flex md:hidden items-center gap-2 text-sm text-gray-600">
            <Home className="h-4 w-4" />
            <span className="cursor-pointer hover:text-blue-600 transition-colors duration-200" onClick={() => router.push('/')}>
              {t("home")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-600">
              {t("payment_history")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-800 font-medium">
              {t("payment_details")}
            </span>
          </div>
        </div>

        <div className="bg-white rounded-b-xl shadow-sm border-x border-b border-gray-200 p-8 flex flex-col items-center justify-center">
          <div className="w-24 h-24 mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <Receipt className="h-12 w-12 text-gray-400" />
          </div>
          <Typography className="text-xl font-semibold text-gray-800 mb-4">
            {t("payment_not_found")}
          </Typography>
          <Button
            variant="outlined"
            color="blue"
            className="mt-4 flex items-center gap-2"
            onClick={() => router.push('/client/payment-history')}
          >
            <ArrowLeft className="h-4 w-4" />
            {t("back_to_payment_history")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 sm:p-6 md:p-8 bg-gray-50">
      {/* Header with breadcrumb */}
      <div className="mb-0 flex flex-col md:flex-row md:items-center bg-white p-4 rounded-t-xl shadow-sm border border-gray-200">
        <Typography variant="h1" className="text-2xl font-bold text-black mb-2 md:mb-0">
          {t("payment_details")}
        </Typography>

        <div className="hidden md:flex items-center">
          <div className="mx-3 h-5 w-px bg-gray-300"></div>

          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Home className="h-4 w-4" />
            <span className="cursor-pointer hover:text-blue-600 transition-colors duration-200" onClick={() => router.push('/')}>
              {t("home")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-600">
              {t("payment_history")}
            </span>
            <ChevronRight className="h-4 w-4" />
            <span className="text-gray-800 font-medium">
              {t("payment_details")}
            </span>
          </div>
        </div>

        {/* Mobile breadcrumb */}
        <div className="flex md:hidden items-center gap-2 text-sm text-gray-600">
          <Home className="h-4 w-4" />
          <span className="cursor-pointer hover:text-blue-600 transition-colors duration-200" onClick={() => router.push('/')}>
            {t("home")}
          </span>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-600">
            {t("payment_history")}
          </span>
          <ChevronRight className="h-4 w-4" />
          <span className="text-gray-800 font-medium">
            {t("payment_details")}
          </span>
        </div>
      </div>

      {/* Back button and payment ID */}
      <div className="bg-white shadow-none border-x border-b border-gray-200 p-4 mb-0 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <button
          onClick={() => router.push("/client/payment-history")}
          className="flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-200 mb-2 sm:mb-0"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          <span>{t("back")}</span>
        </button>

        <div className="flex items-center">
          <Typography className="text-sm text-gray-600 mr-2">
            {t("payment_id")}:
          </Typography>
          <Typography className="text-sm font-medium text-gray-800">
            {payment.paymentId}
          </Typography>
        </div>
      </div>

      {/* Services Section */}
      <Card className="mb-0 bg-white rounded-none shadow-none border-x border-gray-200">
        <CardBody>
          <Typography variant="h6" className="mb-4 font-semibold text-gray-800 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            {t("services")}
          </Typography>

          {/* Table for larger screens */}
          <div className="overflow-x-auto hidden sm:block">
            <table className="w-full text-left">
              <thead>
                <tr className="bg-gray-50 border-b border-gray-200">
                  <th className="p-4 font-semibold text-gray-800">{t("service")}</th>
                  <th className="p-4 font-semibold text-gray-800">{t("invoice.quantity")}</th>
                  <th className="p-4 font-semibold text-gray-800">{t("period")}</th>
                  <th className="p-4 font-semibold text-gray-800">{t("amount")}</th>
                  <th className="p-4 font-semibold text-gray-800">{t("discount")}</th>
                  <th className="p-4 font-semibold text-gray-800">{t("status")}</th>
                </tr>
              </thead>
              <tbody>
                {payment.services.map((service, index) => (
                  <tr key={index} className={`border-b border-gray-200 ${index === payment.services.length - 1 ? 'border-b-0' : ''}`}>
                    <td className="p-4 text-gray-900 font-medium">
                      {getLocalizedContent(service, 'name', params.locale || 'en')}
                    </td>
                    <td className="p-4 text-gray-700">{service.quantity || 1}</td>
                    <td className="p-4 text-gray-700">
                      {service.period} {service.period > 1 ? t("months") : t("month")}
                    </td>
                    <td className="p-4 text-gray-700">
                      {(service.price).toFixed(2)} {payment.currency}
                    </td>
                    <td className="p-4 text-gray-700">
                      {service.discount ? `${((service.discount / service.price) * 100).toFixed(2)}%` : '-'}
                    </td>
                    <td className="p-4">
                      <Chip
                        size="sm"
                        variant="ghost"
                        color={getStatusColor(service.status)}
                        value={service.status}
                        className="capitalize"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Card layout for smaller screens */}
          <div className="block sm:hidden space-y-4">
            {payment.services.map((service, index) => (
              <Card key={index} className="bg-white rounded-xl shadow-sm border border-gray-200">
                <CardBody className="p-4">
                  <div className="flex justify-between items-start mb-3">
                  <Typography variant="h6" className="font-semibold text-gray-800">
                    {getLocalizedContent(service, 'name', params.locale || 'en')}
                  </Typography>
                    <Chip
                      size="sm"
                      variant="ghost"
                      color={getStatusColor(service.status)}
                      value={service.status}
                      className="capitalize"
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Typography className="text-sm text-gray-600">
                        {t("quantity")}
                      </Typography>
                      <Typography className="text-sm text-gray-800">
                        {service.quantity || 1}
                      </Typography>
                    </div>
                    <div className="flex justify-between">
                      <Typography className="text-sm text-gray-600">
                        {t("period")}
                      </Typography>
                      <Typography className="text-sm text-gray-800">
                        {formatDate(payment.paymentDate)} to {calculateEndDate(payment.paymentDate, service.period)}
                  </Typography>
                    </div>
                    <div className="flex justify-between">
                      <Typography className="text-sm text-gray-600">
                        {t("amount")}
                  </Typography>
                      <Typography className="text-sm text-gray-800">
                        {service.price} {payment.currency}
                  </Typography>
                    </div>
                    <div className="flex justify-between">
                      <Typography className="text-sm text-gray-600">
                        {t("discount")}
                  </Typography>
                      <Typography className="text-sm text-gray-800">
                        {service.discount ? `${((service.discount / service.price) * 100).toFixed(2)}%` : '-'}
                  </Typography>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Billing Information Section */}
      <Card className="mb-0 bg-white rounded-none shadow-none border-x border-gray-200">
        <CardBody className="border-t border-gray-200">
          <Typography variant="h6" className="mb-4 font-semibold text-gray-800 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            {t("billing_information")}
          </Typography>

          <div className="space-y-4">
            {/* Billing info in horizontal layout */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
              <div className="p-3 bg-gray-50 rounded-md flex">
                <Typography className="w-24 text-sm font-medium text-gray-700">{t("full_name")}:</Typography>
                <Typography className="font-medium">{payment.billingInfo.BillToName}</Typography>
              </div>
              <div className="p-3 bg-gray-50 rounded-md flex">
                <Typography className="w-24 text-sm font-medium text-gray-700">{t("email")}:</Typography>
                <Typography className="text-sm text-gray-600">{payment.billingInfo.email}</Typography>
              </div>
              <div className="p-3 bg-gray-50 rounded-md flex">
                <Typography className="w-24 text-sm font-medium text-gray-700">{t("phone")}:</Typography>
                <Typography className="text-sm text-gray-600">{payment.billingInfo.phone}</Typography>
              </div>
              <div className="p-3 bg-gray-50 rounded-md flex">
                <Typography className="w-24 text-sm font-medium text-gray-700">{t("address")}:</Typography>
                <Typography className="text-sm text-gray-600">{payment.billingInfo.address}</Typography>
              </div>
              <div className="p-3 bg-gray-50 rounded-md flex">
                <Typography className="w-24 text-sm font-medium text-gray-700">{t("country")}:</Typography>
                <Typography className="text-sm text-gray-600">{payment.billingInfo.country}</Typography>
              </div>
            </div>

            {/* Company Information (conditionally rendered) */}
            {payment.billingInfo.isCompany && (
              <div className="mt-4 pt-4 border-t border-gray-200 p-4">
                <Typography className="font-semibold text-gray-800 mb-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  {t("company_info")}
                </Typography>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  {payment.billingInfo.companyICE && (
                    <div className="p-3 bg-gray-50 rounded-md flex">
                      <Typography className="w-24 text-sm font-medium text-gray-700">{t("ice")}:</Typography>
                      <Typography className="text-sm text-gray-700">{payment.billingInfo.companyICE}</Typography>
                    </div>
                  )}
                  {payment.billingInfo.companyEmail && (
                    <div className="p-3 bg-gray-50 rounded-md flex">
                      <Typography className="w-24 text-sm font-medium text-gray-700">{t("email")}:</Typography>
                      <Typography className="text-sm text-gray-700">{payment.billingInfo.companyEmail}</Typography>
                    </div>
                  )}
                  {payment.billingInfo.companyPhone && (
                    <div className="p-3 bg-gray-50 rounded-md flex">
                      <Typography className="w-24 text-sm font-medium text-gray-700">{t("phone")}:</Typography>
                      <Typography className="text-sm text-gray-700">{payment.billingInfo.companyPhone}</Typography>
                    </div>
                  )}
                  {payment.billingInfo.companyAddress && (
                    <div className="p-3 bg-gray-50 rounded-md flex">
                      <Typography className="w-24 text-sm font-medium text-gray-700">{t("address")}:</Typography>
                      <Typography className="text-sm text-gray-700">{payment.billingInfo.companyAddress}</Typography>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Payment Summary Section */}
      <Card className="mb-0 bg-white rounded-none shadow-none border-x border-gray-200">
        <CardBody className="border-t border-gray-200">
          <Typography variant="h6" className="mb-4 font-semibold text-gray-800 flex items-center">
            <Receipt className="h-5 w-5 mr-2 text-blue-600" />
            {t("payment_summary")}
          </Typography>
          <div className="space-y-3">
            <div className="flex justify-between py-2">
              <Typography className="text-gray-700">{t("subtotal")}</Typography>
              <Typography className="text-gray-900">
                {(payment.subTotal).toFixed(2)} {payment.currency}
              </Typography>
            </div>
            <div className="flex justify-between py-2">
              <Typography className="text-gray-700">{t("tax")}</Typography>
              <Typography className="text-gray-900">
                {(payment.taxAmount).toFixed(2)} {payment.currency}
              </Typography>
            </div>
            <div className="flex justify-between border-t border-gray-200 pt-3 mt-3">
              <Typography className="font-semibold text-gray-800">{t("total")}</Typography>
              <Typography className="font-semibold text-blue-600 text-lg">
                {(payment.totalPrice).toFixed(2)} {payment.currency}
              </Typography>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Action Buttons */}
      <div className="bg-white rounded-b-xl shadow-sm border border-gray-200 p-6 flex justify-center">
          <PDFDownloadLink
            document={<MyDocument payment={payment} t={t} />}
            fileName={`invoice-${payment.paymentId}.pdf`}
          className="w-full sm:w-auto"
          >
            {({ blob, url, loading, error }) => (
              <Button
                color="blue"
              className="flex items-center justify-center gap-2 w-full sm:w-auto px-6"
                disabled={loading}
              >
              <Download size={18} />
                {loading ? t('generating_invoice') : t('download_invoice')}
              </Button>
            )}
          </PDFDownloadLink>
        </div>
    </div>
  );
}