'use client';
import React, { useState, useEffect } from "react";
import {
  CircleArrowRight,
} from "lucide-react";
import {
  Typo<PERSON>,
  <PERSON><PERSON>
} from "@material-tailwind/react";
import Sidebar from "./sidebar";
import ticketService from '../../../services/ticketService';
import { useRouter } from "next/navigation"; // Add router for redirection
import { useTranslations } from "next-intl";
import { AccountState } from "@/app/config/AccountState";

const SupportLayout = ({children}) => {
  const t = useTranslations('client');
  const router = useRouter(); // Initialize router
  const [showForm, setShowForm] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);

  // Check local storage for user state
  const [userState, setUserState] = useState(null);

  useEffect(() => {
    // Get user from local storage
    const storedUser = localStorage.getItem('user');
    setUserState(storedUser ? JSON.parse(storedUser) : null);

    const fetchTickets = async () => {
      try {
        const response = await ticketService.getUserTickets();
        setTickets(response.data.userTickets);
      } catch (error) {
        console.error("Failed to fetch tickets:", error);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch tickets if user is not a guest
    if (storedUser && JSON.parse(storedUser).state !== AccountState.GUEST) {
      fetchTickets();
    }
  }, []);

  // Get the first 3 tickets
  const recentTickets = tickets.slice(0, 3);

  // Handle login redirect
  const handleLoginRedirect = () => {
    router.push('/auth/login');
  };

  return (
    <>
      {userState === null || userState?.state === AccountState.GUEST ? (
        <div className="md:flex min-h-[91vh] md:min-h-[87vh] max-w-[1400px] md:max-h-[87vh] flex flex-col items-center justify-center gap-4">
          <Typography variant="h5">
            {t('notSignedInMessage')}
          </Typography>
          <Button
            onClick={handleLoginRedirect}
            className="bg-blue-500 hover:bg-blue-600"
          >
            {t('loginButton')}
          </Button>
        </div>
      ) : (
        <div className="md:flex min-h-[91vh] md:min-h-[87vh] max-w-[1400px] md:max-h-[87vh]">
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="h-4 p-2 rounded-md lg:hidden"
            aria-label="Toggle Sidebar"
          >
            <CircleArrowRight className="h-6 w-6 text-gray-600" />
          </button>
          <Sidebar
            isSidebarOpen={isSidebarOpen}
            setIsSidebarOpen={setIsSidebarOpen}
            recentTickets={recentTickets}
            setShowForm={setShowForm}
            loading={loading}
          />
          <div className="flex-1 transition-all duration-300 overflow-y-auto">
            {children}
          </div>
        </div>
      )}
    </>
  );
};

export default SupportLayout;