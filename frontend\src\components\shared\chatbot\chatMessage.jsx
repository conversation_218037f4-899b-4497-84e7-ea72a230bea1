import React from 'react';
import Image from 'next/image';

const ChatMessage = ({ message }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex items-start mb-4 ${isUser ? 'justify-end' : ''}`}>
      {!isUser && (
        <div className="flex-shrink-0 mr-3">
          <Image
            src="/images/home/<USER>"
            alt="Assistant"
            width={30}
            height={30}
            className="rounded-full"
          />
        </div>
      )}
      <div
        className={`${
          isUser
            ? 'bg-indigo-600 text-white rounded-bl-3xl rounded-tl-3xl rounded-tr-xl'
            : 'bg-white text-gray-800 rounded-br-3xl rounded-tr-3xl rounded-tl-xl shadow-sm border border-gray-200'
        } px-4 py-2 max-w-[85%]`}
      >
        {message.content}
      </div>
      {isUser && (
        <div className="flex-shrink-0 ml-3">
          <div className="bg-gray-300 h-8 w-8 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-gray-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatMessage;
