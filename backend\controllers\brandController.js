const Brand = require('../models/Brand');

// Create a new brand
exports.createBrand = async (req, res) => {
    try {
        const brand = new Brand(req.body);
        await brand.save();
        res.status(201).json(brand);
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};

// Get all brands
exports.getAllBrands = async (req, res) => {
    try {
        const brands = await Brand.find().populate('category');
        res.status(200).json(brands);
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};

// Get a single brand
exports.getBrand = async (req, res) => {
    try {
        const brand = await Brand.findById(req.params.id).populate('category');
        if (!brand) throw new Error('Brand not found');
        res.status(200).json(brand);
    } catch (err) {
        res.status(404).json({ error: err.message });
    }
};

// Update a brand
exports.updateBrand = async (req, res) => {
    try {
        const brand = await Brand.findByIdAndUpdate(req.params.id, req.body, { new: true });
        res.status(200).json(brand);
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};

// Delete a brand
exports.deleteBrand = async (req, res) => {
    try {
        await Brand.findByIdAndDelete(req.params.id);
        res.status(200).json({ message: 'Brand deleted successfully' });
    } catch (err) {
        res.status(400).json({ error: err.message });
    }
};
