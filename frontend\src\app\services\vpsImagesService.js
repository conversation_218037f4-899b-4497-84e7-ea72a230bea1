import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5002';

// Configuration axios avec intercepteurs
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// Intercepteur pour ajouter le token d'authentification
// TEMPORAIREMENT DÉSACTIVÉ POUR LES TESTS
apiClient.interceptors.request.use(
  (config) => {
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
// TEMPORAIREMENT DÉSACTIVÉ POUR LES TESTS
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // if (error.response?.status === 401) {
    //   // Token expiré ou invalide
    //   localStorage.removeItem('token');
    //   window.location.href = '/admin/login';
    // }
    return Promise.reject(error);
  }
);

/**
 * Service pour les Images OS (Admin)
 */
export const osImagesService = {
  // Récupérer toutes les images OS
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/admin/os-images', { params });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des images OS');
    }
  },

  // Récupérer une image OS par ID
  getById: async (id) => {
    try {
      const response = await apiClient.get(`/api/admin/os-images/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'image OS');
    }
  },

  // Créer une nouvelle image OS
  create: async (formData) => {
    try {
      const response = await apiClient.post('/api/admin/os-images', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'image OS');
    }
  },

  // Modifier une image OS
  update: async (id, formData) => {
    try {
      const response = await apiClient.put(`/api/admin/os-images/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la modification de l\'image OS');
    }
  },

  // Supprimer une image OS
  delete: async (id) => {
    try {
      const response = await apiClient.delete(`/api/admin/os-images/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'image OS');
    }
  },

  // Changer le statut d'une image OS
  updateStatus: async (id, status) => {
    try {
      const response = await apiClient.patch(`/api/admin/os-images/${id}/status`, { status });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors du changement de statut');
    }
  },

  // Récupérer les statistiques
  getStats: async () => {
    try {
      const response = await apiClient.get('/api/admin/os-images/stats/overview');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des statistiques');
    }
  }
};

/**
 * Service pour les Applications (Admin)
 */
export const appImagesService = {
  // Récupérer toutes les applications
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/admin/app-images', { params });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des applications');
    }
  },

  // Récupérer une application par ID
  getById: async (id) => {
    try {
      const response = await apiClient.get(`/api/admin/app-images/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'application');
    }
  },

  // Créer une nouvelle application
  create: async (formData) => {
    try {
      const response = await apiClient.post('/api/admin/app-images', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'application');
    }
  },

  // Modifier une application
  update: async (id, formData) => {
    try {
      const response = await apiClient.put(`/api/admin/app-images/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la modification de l\'application');
    }
  },

  // Supprimer une application
  delete: async (id) => {
    try {
      const response = await apiClient.delete(`/api/admin/app-images/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'application');
    }
  },

  // Changer le statut d'une application
  updateStatus: async (id, status) => {
    try {
      const response = await apiClient.patch(`/api/admin/app-images/${id}/status`, { status });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors du changement de statut');
    }
  },

  // Récupérer les statistiques
  getStats: async () => {
    try {
      const response = await apiClient.get('/api/admin/app-images/stats/overview');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des statistiques');
    }
  }
};

/**
 * Service pour les Images VPS (Public - Page de configuration)
 */
export const vpsImagesPublicService = {
  // Récupérer toutes les images organisées par catégorie
  getAll: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/vps-images', { params });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des images VPS');
    }
  },

  // Récupérer les images populaires
  getPopular: async () => {
    try {
      const response = await apiClient.get('/api/vps-images/popular');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des images populaires');
    }
  },

  // Récupérer les images OS
  getOS: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/vps/images/os', { params });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des images OS');
    }
  },

  // Récupérer les applications
  getApps: async (params = {}) => {
    try {
      const response = await apiClient.get('/api/vps/images/apps', { params });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des applications');
    }
  },

  // Récupérer les applications blockchain
  getBlockchain: async () => {
    try {
      const response = await apiClient.get('/api/vps/images/blockchain');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des applications blockchain');
    }
  },

  // Récupérer une image spécifique
  getById: async (id) => {
    try {
      const response = await apiClient.get(`/api/vps/images/${id}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'image');
    }
  }
};

/**
 * Utilitaires pour les fichiers
 */
export const fileUtils = {
  // Valider un fichier image
  validateImageFile: (file) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/webp'];
    const maxSize = 2 * 1024 * 1024; // 2MB

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Type de fichier non supporté. Utilisez JPG, PNG, SVG ou WebP.');
    }

    if (file.size > maxSize) {
      throw new Error('Le fichier est trop volumineux. Taille maximale : 2MB.');
    }

    return true;
  },

  // Créer un FormData pour l'upload
  createFormData: (data, iconFile = null) => {
    const formData = new FormData();

    // Ajouter tous les champs de données
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        if (Array.isArray(data[key]) || typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
    });

    // Ajouter le fichier icône si présent
    if (iconFile) {
      formData.append('icon', iconFile);
    }

    return formData;
  },

  // Obtenir l'URL complète d'une icône
  getIconUrl: (iconPath) => {
    if (!iconPath) return null;
    if (iconPath.startsWith('http')) return iconPath;
    return `${API_BASE_URL}${iconPath}`;
  }
};

export default {
  osImagesService,
  appImagesService,
  vpsImagesPublicService,
  fileUtils
};
