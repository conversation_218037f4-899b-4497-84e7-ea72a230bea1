import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import Link from "next/link";
import { BsArrowUpRight } from "react-icons/bs";
import { motion } from "framer-motion";
import { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import service1img from "/public/images/home/<USER>";
import service2img from "/public/images/home/<USER>";
import service3img from "/public/images/home/<USER>";

const PrimaryServiceCard = ({
  imageSrc,
  title,
  description,
  price,
  url,
  t,
  isPopular,
  className = "",
}) => (
  <motion.div
    // Only apply hover animation to non-popular cards
    whileHover={isPopular ? {} : { y: -8 }}
    transition={{ duration: 0.3 }}
    className={`relative border-2 ${
      isPopular ? "border-[#606af5]" : "border-gray-200"
    } 
    shadow-lg hover:shadow-xl rounded-xl p-4 bg-white w-[350px] flex flex-col items-center text-center ${className}`}
  >
    {isPopular && (
      <div className="absolute uppercase -top-4 left-1/2 transform -translate-x-1/2 bg-[#606af5] text-white px-4 py-1 rounded-full text-sm font-medium z-10">
        {t("most_popular")}
      </div>
    )}
    <div className="relative w-full h-48 mb-4 overflow-hidden rounded-lg">
      <Image
        src={imageSrc}
        alt={t(title)}
        fill
        className="object-cover transition-transform duration-300 hover:scale-110"
      />
    </div>

    {/* Rest of the component remains the same */}
    <Typography variant="h3" className="text-xl font-bold mt-2 text-gray-800">
      {t(title)}
    </Typography>
    <p className="text-gray-600 mt-4 mb-6 mx-2 flex-grow text-sm leading-relaxed">
      {t(description).substring(0, 200)}...
    </p>

    <div className="w-full px-4 py-3 bg-gray-50 rounded-lg mb-4">
      <p className="font-medium text-sm text-gray-600">
        {t("a_partir_de")}
        <span className="block text-primary mt-1">
          <span className="text-3xl font-bold">{price}</span>
          <span className="ml-1 text-lg">DH HT/m</span>
        </span>
      </p>
    </div>

    <Link
      href={url}
      className="group w-full flex items-center justify-center bg-[#606af5] hover:bg-blue-800 text-white rounded-lg text-sm px-6 py-3 transition-all duration-300"
    >
      {t("voir_les_offres")}
      <BsArrowUpRight className="ml-2 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
    </Link>
  </motion.div>
);

const PrimaryServicesData = [
  {
    imageSrc: service1img,
    title: "hosting.title",
    description: "hosting.description",
    price: 18,
    url: "/hosting",
  },
  {
    imageSrc: service2img,
    title: "web_dev.title",
    description: "web_dev.description",
    price: 1499,
    url: "/web-development",
    isPopular: true,
  },
  {
    imageSrc: service3img,
    title: "cloud_maroc.title",
    description: "cloud_maroc.description",
    price: 899,
    url: "/cloud-maroc",
  },
];

// Mobile Carousel Component
const MobileCarousel = ({ services, t }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % services.length);
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev - 1 + services.length) % services.length);
  };

  const handleTouchStart = (e) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 100) {
      // Swipe left
      handleNext();
    }

    if (touchStart - touchEnd < -100) {
      // Swipe right
      handlePrev();
    }
  };

  return (
    <div className="relative w-full px-4 py-8">
      <div
        className="overflow-hidden"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {services.map((service, index) => (
            <div
              key={index}
              className="min-w-full flex justify-center items-center"
            >
              <PrimaryServiceCard
                imageSrc={service.imageSrc}
                title={service.title}
                description={service.description}
                price={service.price}
                url={service.url}
                t={t}
                isPopular={service.isPopular}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Carousel Controls */}
      <button
      name="prev"
        onClick={handlePrev}
        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md z-10"
      >
        <ChevronLeft className="w-5 h-5 text-gray-700" />
      </button>

      <button
      name="next"
        onClick={handleNext}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md z-10"
      >
        <ChevronRight className="w-5 h-5 text-gray-700" />
      </button>

      {/* Dots indicator */}
      <div className="flex justify-center mt-4 gap-2">
        {services.map((_, index) => (
          <button
          name="dot"
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-2 h-2 rounded-full ${
              currentIndex === index ? "bg-[#606af5]" : "bg-gray-300"
            }`}
          />
        ))}
      </div>
    </div>
  );
};

const Services = ({ t }) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => {
      window.removeEventListener("resize", checkIsMobile);
    };
  }, []);

  return (
    <section className="w-full py-16 bg-indigo-200/10">
      <div className="max-w-[1400px] mx-auto px-4">
        <div className="text-center mb-16">
          <Typography
            variant="h2"
            className="text-4xl font-normal font-inter text-gray-900 mb-4"
          >
            {t("main_services_title")}
          </Typography>
          <Typography
            variant="paragraph"
            className="font-inter text-lg text-gray-600 max-w-2xl mx-auto"
          >
            {t("boost_online_presence")}
          </Typography>
        </div>
        {/* Mobile Carousel View */}
        <div className="md:hidden">
          <MobileCarousel services={PrimaryServicesData} t={t} />
        </div>
        {/* Desktop View with Elevated Popular Card */}
        <div className="hidden md:flex justify-center items-center gap-8 mt-8 relative">
          {PrimaryServicesData.map((service, index) => (
            <PrimaryServiceCard
              key={service.title}
              imageSrc={service.imageSrc}
              title={service.title}
              description={service.description}
              price={service.price}
              url={service.url}
              t={t}
              isPopular={service.isPopular}
              className={
                service.isPopular ? "transform -translate-y-8 scale-105" : ""
              }
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
