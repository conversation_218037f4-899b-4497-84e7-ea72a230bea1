/**
 * Test script for VPS Applications endpoint
 * Tests the /api/vps/applications endpoint that was just fixed
 */

const axios = require('axios');

async function testApplicationsEndpoint() {
  console.log('🧪 Testing VPS Applications Endpoint');
  console.log('====================================');
  
  try {
    console.log('\n1. Testing /api/vps/applications endpoint...');
    
    const response = await axios.get('http://localhost:5002/api/vps/applications', {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Endpoint is accessible');
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log(`📦 Response structure:`, {
      success: response.data.success,
      message: response.data.message,
      dataType: Array.isArray(response.data.data) ? 'array' : typeof response.data.data,
      dataLength: response.data.data?.length || 0
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      console.log('✅ Response has correct structure');
      
      if (response.data.data.length > 0) {
        console.log(`✅ Found ${response.data.data.length} applications`);
        
        // Show first few applications
        const firstApp = response.data.data[0];
        console.log('\n📋 Sample application:');
        console.log(`   Name: ${firstApp.name}`);
        console.log(`   ID: ${firstApp.applicationId}`);
        console.log(`   Description: ${firstApp.description?.substring(0, 100)}...`);
        
        if (response.data.data.length > 1) {
          console.log(`\n📋 Other applications available: ${response.data.data.slice(1, 6).map(app => app.name).join(', ')}${response.data.data.length > 6 ? '...' : ''}`);
        }
      } else {
        console.log('⚠️ No applications found in response');
      }
    } else {
      console.log('❌ Response structure is incorrect');
    }
    
    console.log('\n🎉 Applications endpoint test completed successfully!');
    console.log('\n✅ Fix Summary:');
    console.log('- Added missing getAvailableApplications import to vpsRouter.js');
    console.log('- Added missing /api/vps/applications route to vpsRouter.js');
    console.log('- Added missing getAvailableApplications method to vpsService.js');
    console.log('- Added missing getAvailableImages method to vpsService.js');
    console.log('- Endpoint now returns applications from Contabo API');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the backend server is running: npm run dev');
    } else if (error.response) {
      console.log(`📊 HTTP Status: ${error.response.status}`);
      console.log(`📋 Error Response:`, error.response.data);
    }
  }
}

// Run the test
testApplicationsEndpoint();
