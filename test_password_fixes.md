# VPS Password Reset and Reinstall Fixes

## Issues Fixed

### 1. Password Reset (`resetVPSPassword`)
**Problem**: The password reset was not working because Contabo API requires `rootPassword` to be a secret ID (number), not plain text.

**Solution**: 
- Modified `ContaboProvider.resetPassword()` to properly handle password secrets
- When a plain text password is provided, it creates a password secret using Contabo's Secrets API
- The secret ID is then used in the password reset request
- Removed the old, non-functional `resetPassword` method

### 2. VPS Reinstall (`reinstallVPS`)
**Problem**: The reinstall functionality wasn't properly handling password setting during reinstallation.

**Solution**:
- Updated `ContaboProvider.reinstallVPS()` to handle password secrets correctly
- Added proper password secret creation for plain text passwords
- Added support for SSH keys and userData during reinstall
- Added database updates to track password changes
- Added standalone `reinstallVPS` method to VPSService

## Key Changes Made

### ContaboProvider.js
1. **Enhanced `resetPassword` method**:
   - Detects if password is already a secret ID or plain text
   - Creates password secret for plain text passwords
   - Uses secret ID in API calls
   - Returns `passwordSecretId` in response

2. **Enhanced `reinstallVPS` method**:
   - Added password secret handling
   - Improved SSH key management
   - Better error handling and logging
   - Returns `passwordSecretId` when password is set

3. **Removed old `resetPassword` method** that didn't handle secrets properly

### VPSService.js
1. **Enhanced `resetVPSPassword` method**:
   - Better database updates
   - Stores password secret ID

2. **Added `reinstallVPS` method**:
   - Handles reinstall requests
   - Updates database with new configuration
   - Tracks password and SSH key changes

## How It Works Now

### Password Reset Flow:
1. User provides plain text password
2. System creates a password secret in Contabo
3. Uses secret ID in password reset API call
4. Updates database with new password info
5. Password is now properly set and functional

### Reinstall Flow:
1. User provides image ID and optional password
2. If password provided, system creates password secret
3. Uses secret ID in reinstall API call
4. Updates database with new OS and password info
5. VPS is reinstalled with correct password

## Testing

To test these fixes:

1. **Password Reset**:
   ```bash
   curl -X POST /api/vps/instances/{instanceId}/reset-password \
     -H "Content-Type: application/json" \
     -d '{"rootPassword": "YourNewPassword123"}'
   ```

2. **Reinstall**:
   ```bash
   curl -X POST /api/vps/instances/{instanceId}/reinstall \
     -H "Content-Type: application/json" \
     -d '{"imageId": "ubuntu-22.04", "rootPassword": "YourNewPassword123"}'
   ```

Both operations should now properly set the password and allow successful login to the VPS.
