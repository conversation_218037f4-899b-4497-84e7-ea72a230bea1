/**
 * <PERSON><PERSON><PERSON> to populate existing VPS packages with storage types
 * This script updates existing packages to include the new storageTypes field
 */

const mongoose = require('mongoose');
const Package = require('../models/Package');
const vpsStorageService = require('../services/vpsStorageService');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

async function populateStorageTypes() {
  try {
    console.log('🚀 Starting storage types population...');

    // Find all VPS packages (packages with vpsConfig)
    const vpsPackages = await Package.find({
      'vpsConfig.provider': { $exists: true }
    });

    console.log(`📦 Found ${vpsPackages.length} VPS packages to update`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const pkg of vpsPackages) {
      console.log(`\n🔄 Processing package: ${pkg.name}`);
      
      // Check if package already has storage types
      if (pkg.vpsConfig.storageTypes && pkg.vpsConfig.storageTypes.length > 0) {
        console.log(`⏭️ Package ${pkg.name} already has storage types, skipping...`);
        skippedCount++;
        continue;
      }

      // Update package with storage types
      try {
        await vpsStorageService.updatePackageStorageTypes(pkg);
        console.log(`✅ Updated package: ${pkg.name}`);
        
        // Log the storage types that were added
        if (pkg.vpsConfig.storageTypes && pkg.vpsConfig.storageTypes.length > 0) {
          console.log(`   Storage types added:`);
          pkg.vpsConfig.storageTypes.forEach(storage => {
            console.log(`   - ${storage.type}: ${storage.productId} (${storage.diskSize})${storage.isDefault ? ' [DEFAULT]' : ''}`);
          });
        } else {
          console.log(`   ⚠️ No storage types found for package: ${pkg.name}`);
        }
        
        updatedCount++;
      } catch (error) {
        console.error(`❌ Error updating package ${pkg.name}:`, error.message);
      }
    }

    console.log('\n📊 Summary:');
    console.log(`✅ Updated packages: ${updatedCount}`);
    console.log(`⏭️ Skipped packages: ${skippedCount}`);
    console.log(`📦 Total packages: ${vpsPackages.length}`);

    // Verify the updates
    console.log('\n🔍 Verification:');
    const updatedPackages = await Package.find({
      'vpsConfig.storageTypes': { $exists: true, $ne: [] }
    });
    
    console.log(`✅ Packages with storage types: ${updatedPackages.length}`);
    
    // Show sample of updated packages
    if (updatedPackages.length > 0) {
      console.log('\n📋 Sample updated packages:');
      for (const pkg of updatedPackages.slice(0, 3)) {
        console.log(`\n📦 ${pkg.name}:`);
        console.log(`   Provider Product ID: ${pkg.vpsConfig.providerProductId}`);
        console.log(`   Storage Types: ${pkg.vpsConfig.storageTypes.length}`);
        pkg.vpsConfig.storageTypes.forEach(storage => {
          console.log(`   - ${storage.type}: ${storage.productId} (${storage.diskSize})${storage.isDefault ? ' [DEFAULT]' : ''}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Error in populateStorageTypes:', error);
  }
}

async function main() {
  try {
    await connectDB();
    await populateStorageTypes();
    console.log('\n🎉 Storage types population completed!');
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📴 Database connection closed');
    process.exit(0);
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️ Script interrupted by user');
  await mongoose.disconnect();
  process.exit(0);
});

process.on('unhandledRejection', async (error) => {
  console.error('❌ Unhandled rejection:', error);
  await mongoose.disconnect();
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { populateStorageTypes };
