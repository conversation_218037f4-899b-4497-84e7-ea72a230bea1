import React from 'react';
import { Typography } from '@material-tailwind/react';

const CustomSwiper2 = ({ data }) => {
    return (
        <div className="w-full relative overflow-x-auto">
            {/* Horizontal container */}
            <div className="flex space-x-4 w-max px-4">
                {data.map((plan, index) => (
                    <div
                        key={index}
                        className="flex-shrink-0 w-[300px] border border-gray-300 rounded-lg bg-white shadow-md p-6"
                    >
                        <Typography
                            variant="h5"
                            className="text-blue-600 font-bold text-center mb-4"
                        >
                            {plan.name}
                        </Typography>
                        <ul className="space-y-3">
                            {plan.features.map((feature, idx) => (
                                <li
                                    key={idx}
                                    className="flex items-center gap-2 text-gray-700"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="currentColor"
                                        className="w-6 h-6 text-blue-600"
                                    >
                                        <path
                                            fillRule="evenodd"
                                            d="M9 16.2L4.8 12l-1.4 1.4 6 6 12-12-1.4-1.4L9 16.2z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    {feature}
                                </li>
                            ))}
                        </ul>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default CustomSwiper2;
