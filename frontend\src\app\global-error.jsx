'use client'

export default function GlobalError({
  error,
  reset,
}) {
  console.error('Global error caught:', error);

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong!</h2>
            <p className="text-gray-600 mb-6">
              We{"'"}re sorry, but there was an unexpected error. Our team has been notified.
            </p>
            <div className="bg-gray-100 p-4 rounded mb-6 overflow-auto max-h-32">
              <code className="text-sm text-gray-800">
                {error.message || 'Unknown error'}
              </code>
            </div>
            <button
              onClick={() => reset()}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-150"
            >
              Try again
            </button>
          </div>
        </div>
      </body>
    </html>
  )
}