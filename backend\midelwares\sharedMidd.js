const { isProd } = require("../constants/constant");
const { getCookieConfig } = require("../helpers/helpers");

exports.asyncBackFrontEndLang = (req, res, next) => {
  try {
    const { BACKEND_LANG } = req.cookies;
    console.log("req.cookies: ", req.cookies);

    if (["fr", "en"].includes(BACKEND_LANG)) {
      req.i18n.changeLanguage(BACKEND_LANG);
      console.log("lang changed to: ", BACKEND_LANG);
    }
    next();
  } catch (error) {
    console.error("Token verification failed:", error);
    return res
      .status(403)
      .send("Invalid or expired token, please login again.");
  }
};

// Function to clear the 'guest_token' cookie
exports.clearGuestToken = (req, res) => {
  try {
    if (req.cookies.guest_token) {
      res.clearCookie("guest_token", {
        ...getCookieConfig(isProd),
      });
      console.log("guest_token cleared...");
      console.log("guest_token...after: ..", req.cookies);
    } else {
      console.log("No guest_token found.");
    }
  } catch (error) {
    console.error("guest token removing failed:", error);
  }
};

exports.captchaVerification = async (req, res, next) => {
  try {
    const recaptchaToken = req.body["g-recaptcha-response"];

    if (!recaptchaToken) {
      return res.status(400).json({
        message: "reCAPTCHA verification failed",
        errors: [
          {
            key: "g-recaptcha-response",
            msg: req.t("recaptcha_required"),
          },
        ],
      });
    }

    const recaptchaResponse = await fetch(
      "https://www.google.com/recaptcha/api/siteverify",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          secret: process.env.RECAPTCHA_SECRET_KEY,
          response: recaptchaToken,
        }),
      }
    );

    const recaptchaResult = await recaptchaResponse.json();

    if (!recaptchaResult.success) {
      return res.status(400).json({
        message: "reCAPTCHA verification failed",
        errors: [
          {
            key: "g-recaptcha-response",
            msg: req.t("recaptcha_invalid"),
          },
        ],
      });
    }

    return next();
  } catch (error) {
    console.error("reCAPTCHA verification error:", error);
    return res.status(500).json({
      message: "Internal server error",
      errors: [
        {
          key: "g-recaptcha-response",
          msg: req.t("recaptcha_error"),
        },
      ],
    });
  }
};
