'use client'
import { Typography } from '@material-tailwind/react'
import React from 'react'
import { HEADsvg, LOCKsvg, MALEPERSONsvg, MOSQUEsvg, SSLCERTIFICATIONsvg } from '../../icons/svgIcons'
import { ArrowRightIcon, PhoneIcon } from '@heroicons/react/24/solid'
import Link from 'next/link'
import Image from 'next/image'
import sslShield from "/public/images/services/ssl-shield.png";
import sslSideimg from "/public/images/services/ssl-sideimg.png";


const benefits = [
    {
        title: "benefits.0.title",
        description: "benefits.0.description"
    },
    {
        title: "benefits.1.title",
        description: "benefits.2.description"
    },
    {
        title: "benefits.2.title",
        description: "benefits.2.description"
    }
]

const advantages = [
    {
        icon: <HEADsvg color="#497ef7" />,
        title: "advantages.0.title",
        description: "advantages.0.description"
    },
    {
        icon: <MALEPERSONsvg />,
        title: "advantages.1.title",
        description: "advantages.1.description"
    },
    {
        icon: <MOSQUEsvg />,
        title: "advantages.2.title",
        description: "advantages.2.description"
    }
]

const solutions = [
    {
        icon: <LOCKsvg />,
        title: "solutions.0.title",
        description:
            "solutions.0.description",
    },
    {
        icon: <SSLCERTIFICATIONsvg />,
        title: "solutions.1.title",
        description:
            "solutions.1.description",
    },
    {
        icon: <MALEPERSONsvg />,
        title: "solutions.2.title",
        description:
            "solutions.2.description",
    }
]

function SSLMain({ t }) {
    return (
        <div className="w-full">
            <div className='max-w-[1400px] mx-auto flex flex-col gap-y-0 p-0'>
                <div className="md:p-16 p-4 flex my-10 md:my-0 flex-col md:flex-row items-center justify-between md:gap-8 gap-4">
                    <div className="bg-white">
                        <Typography
                            variant='h2'
                            className="md:text-3xl text-2xl font-semibold normal-case md:mb-6 mb-4 text-black max-w-lg">
                            {t('why_ssl_essential_title')}
                        </Typography>
                        <p className='max-w-md normal-case'>
                            {t('why_ssl_essential_subtitle')}
                        </p>
                        <div className="md:mt-10 mt-4 flex flex-col border-l-2 border-gray-700 px-4 py-2 gap-y-6 max-w-lg">
                            {benefits.map((benefit, index) => (
                                <div key={index} className="flex flex-col gap-y-2">
                                    <Typography
                                        variant="h5"
                                        className="text-secondary font-inter font-medium md:text-xl text-base"
                                    >
                                        {t(benefit.title)}
                                    </Typography>
                                    <p className="text-gray-700">{t(benefit.description)}</p>
                                </div>
                            ))}
                        </div>
                        <div className="flex md:flex-row flex-col items-center gap-4 mt-8">
                            <Link
                                href="#contact-nous"
                                className='px-6 py-3 md:py-2 flex justify-between md:justify-normal md:w-fit w-full  gap-x-4 text-black shadow-none uppercase border-2 border-black hover:border-secondary rounded-3xl bg-transparent hover:bg-secondary hover:text-white font-inter text-sm font-medium' >
                                {t('request_quote')}
                                <ArrowRightIcon className='w-4 my-auto' />
                            </Link>
                            <Link
                                href="tel:+212662841605"
                                className='px-6 md:py-2 py-3 flex md:flex-row flex-row-reverse justify-between md:justify-normal md:w-fit w-full gap-x-3 text-secondary shadow-none uppercase border-2 border-gray-500 rounded-3xl hover:bg-primary hover:text-white font-inter text-sm font-medium'>
                                <PhoneIcon className='text-secondary md:w-4 w-5 my-auto' />
                                <p className='md:m-auto'>+212 662 841 605</p>
                            </Link>
                        </div>
                    </div>
                    <div className="bg-[#F2F4FB] rounded-xl border shadow-inner max-w-lg md:min-h-[500px] md:h-full h-[350px] flex items-center justify-center w-full">
                        {/* <img
                            loading="lazy"
                            src="/images/services/ssl-shield.png"
                            alt="Data center"
                            className="md:w-[250px] w-[200px] h-auto rounded-xl object-cover m-auto"
                        /> */}
                        <Image
                            src={sslShield}
                            width={300}
                            height={300}
                            sizes="(max-width: 768px) 100vw, 50vw"
                            alt="ssl-shield"
                            className="md:w-[250px] w-[200px] h-auto rounded-xl object-cover m-auto"
                        />
                    </div>
                </div>


                <div className="bg-[#F2F4FB] p-10">
                    <Typography
                        variant='h2'
                        className="text-2xl font-semibold text-center mb-4">
                        {t('ssl_solutions_title')}
                    </Typography>

                    <div className="flex flex-col gap-y-4 md:flex-row gap-x-6 max-w-6xl m-auto p-4 justify-between">
                        {solutions.map((item, index) => (
                            <div
                                key={index}
                                className="md:w-1/3 w-full bg-white shadow-md rounded-lg p-6 border border-secondary text-center"
                            >
                                <div className="w-full flex items-center justify-center text-4xl text-blue-500 mb-4">
                                    <span className="border border-[#F2F4FB] rounded-md p-2 bg-[#F2F4FB]">
                                        {item.icon}
                                    </span>
                                </div>
                                <Typography
                                    variant='h3'
                                    className="font-medium text-lg mb-2 md:w-1/2 m-auto p-1">
                                    {t(item.title)}
                                </Typography>
                                <p className="text-gray-800 text-[15px]">{t(item.description)}</p>
                            </div>
                        ))}
                    </div>
                </div>



                <div className='bg-transparent items-center flex flex-col justify-center gap-y-2 p-0 overflow-hidden w-full'>
                    <div className="w-full flex flex-col md:flex-row justify-between mt-0 md:p-10 p-4">
                        <div className="bg-white md:w-1/2">
                            <Typography
                                variant='h2'
                                className="md:text-3xl text-2xl font-semibold mb-6 text-black md:max-w-lg">
                                {t('why_ztech_ssl')}
                            </Typography>

                            <div className="mt-10 flex flex-col md:px-4 py-2 gap-y-6 max-w-lg">
                                {advantages.map((item, index) => (
                                    <div
                                        key={index}
                                        className="flex flex-row md:gap-x-0 gap-x-2 justify-start items-center">
                                        <div
                                            className="md:border md:shadow-inner  rounded-md md:bg-[#F2F4FB] w-[90px] h-[90px] p-6 flex m-auto items-center text-4xl text-primary hover:border-secondary md:flex-shrink-0 ">
                                            {item.icon}
                                        </div>
                                        <div className="flex flex-col gap-y-2 w-3/4">
                                            <Typography
                                                variant='h5'
                                                className="text-secondary font-inter md:text-xl text-base font-medium">
                                                {t(item.title)}
                                            </Typography>
                                            <p className="text-gray-700">
                                                {t(item.description)}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="flex md:flex-row flex-col items-center gap-4 mt-8">
                                <Link
                                    href="#contact-nous"
                                    className='px-4 py-3 md:py-2 flex justify-between md:justify-normal md:w-fit w-full  gap-x-4 text-black shadow-none uppercase border-2 hover:border-secondary border-black rounded-3xl bg-transparent hover:bg-secondary hover:text-white font-inter text-sm font-medium' >
                                    {t('request_quote')}
                                    <ArrowRightIcon className='w-4 my-auto' />
                                </Link>
                                <Link
                                    href="tel:+212662841605"
                                    className='px-6 md:py-2 py-3 flex md:flex-row flex-row-reverse justify-between md:justify-normal md:w-fit w-full gap-x-2 text-secondary shadow-none uppercase border-2 border-gray-500 rounded-3xl hover:bg-primary hover:text-white font-inter text-sm font-medium'>
                                    <PhoneIcon className='text-secondary md:w-4 w-5 my-auto' />
                                    <p className='md:m-auto'>+212 662 841 605</p>
                                </Link>
                            </div>
                        </div>
                        <div className="md:w-1/2 hidden md:flex items-center justify-center md:px-10 pb-0 rounded-3xl md:mt-0 mt-4">
                            {/* <img
                                loading="lazy"
                                src="/images/services/ssl-sideimg.png"
                                alt="Data center"
                                className="h-full w-[90%] rounded-3xl object-cover m-auto"
                            /> */}
                            <Image
                                src={sslSideimg}
                                width={900}
                                height={300}
                                sizes="(max-width: 768px) 100vw, 50vw"
                                alt="ssl-shield"
                                className="h-full w-[90%] rounded-3xl object-cover m-auto"
                                priority
                            />
                        </div>
                    </div>
                </div>

            </div>
        </div>
    )
}

export default SSLMain;