import { useState, useEffect } from "react";

const useDeviceScale = () => {
    const [scale, setScale] = useState(4); // Default to 'Tablet'

    useEffect(() => {
        const updateScale = () => {
            const width = window.innerWidth;

            if (width < 360) setScale(1); // Small Mobile
            else if (width >= 360 && width < 768) setScale(2); // Mobile
            else if (width >= 768 && width < 1024) setScale(3); // Tablet
            else if (width >= 1024 && width < 1130) setScale(4); // Small Desktop
            else if (width >= 1130 && width < 1300) setScale(5); // Small Desktop
            else if (width >= 1300 && width < 1600) setScale(6); // Desktop
            else if (width >= 1600 && width < 1800) setScale(7); // Desktop
            else if (width >= 1800 && width < 1920) setScale(8); // Desktop
            else setScale(9); // Large Desktop
        };

        updateScale(); // Initial call
        window.addEventListener("resize", updateScale);

        return () => window.removeEventListener("resize", updateScale);
    }, []);

    return scale; // Returns values 1, 2, 3, 4, 5, or 6
};

export default useDeviceScale;
