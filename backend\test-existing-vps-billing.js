/**
 * Test script for VPS Billing Notifications using existing VPS instances
 * This script will find existing VPS instances and test the billing notification system
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const VPSInstance = require('./models/VPSInstance');
const User = require('./models/User');
const NotificationSetting = require('./models/NotificationSetting');

// Import the VPS billing service
const vpsBillingService = require('./services/vpsBillingNotificationService');

async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

async function findExistingVPSInstances() {
  try {
    console.log('🔍 Looking for existing VPS instances...');
    
    const vpsInstances = await VPSInstance.find()
      .populate('user', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(10);
    
    console.log(`📊 Found ${vpsInstances.length} VPS instances in database`);
    
    if (vpsInstances.length === 0) {
      console.log('❌ No VPS instances found in database');
      return [];
    }
    
    // Display information about each VPS instance
    vpsInstances.forEach((instance, index) => {
      const user = instance.user;
      const billingDate = instance.billing?.nextBillingDate;
      const price = instance.billing?.monthlyPrice;
      const currency = instance.billing?.currency || 'MAD';
      
      console.log(`\n📋 VPS Instance ${index + 1}:`);
      console.log(`   ID: ${instance.instanceId}`);
      console.log(`   Name: ${instance.displayName || instance.config?.name || 'N/A'}`);
      console.log(`   User: ${user?.firstName || 'N/A'} ${user?.lastName || ''} (${user?.email || 'N/A'})`);
      console.log(`   Status: ${instance.status}`);
      console.log(`   Price: ${price || 'N/A'} ${currency}`);
      console.log(`   Next Billing: ${billingDate ? billingDate.toLocaleDateString() : 'Not set'}`);
      
      if (billingDate) {
        const now = new Date();
        const daysUntilBilling = Math.ceil((billingDate - now) / (24 * 60 * 60 * 1000));
        console.log(`   Days until billing: ${daysUntilBilling}`);
      }
    });
    
    return vpsInstances;
  } catch (error) {
    console.error('❌ Error finding VPS instances:', error);
    throw error;
  }
}

async function updateVPSBillingDate(instanceId, daysFromNow) {
  try {
    const nextBillingDate = new Date();
    nextBillingDate.setDate(nextBillingDate.getDate() + daysFromNow);
    
    const updatedInstance = await VPSInstance.findOneAndUpdate(
      { instanceId: instanceId },
      { 
        'billing.nextBillingDate': nextBillingDate,
        'billing.monthlyPrice': 46, // Set a default price if not set
        'billing.currency': 'MAD'
      },
      { new: true }
    );
    
    if (updatedInstance) {
      console.log(`✅ Updated VPS ${instanceId} billing date to ${nextBillingDate.toLocaleDateString()} (${daysFromNow} days from now)`);
      return updatedInstance;
    } else {
      console.log(`❌ VPS instance ${instanceId} not found`);
      return null;
    }
  } catch (error) {
    console.error(`❌ Error updating VPS billing date:`, error);
    throw error;
  }
}

async function setupBillingNotificationSettings() {
  try {
    // Create or update VPS billing notification settings
    const settings = await NotificationSetting.findOneAndUpdate(
      { type: 'vps_billing' },
      {
        type: 'vps_billing',
        enabled: true,
        daysBefore: [7, 3, 1],
        methods: {
          email: true,
          inApp: true
        },
        schedule: {
          frequency: 'daily'
        }
      },
      { upsert: true, new: true }
    );
    
    console.log('\n✅ VPS billing notification settings configured');
    console.log(`   - Enabled: ${settings.enabled}`);
    console.log(`   - Days before: ${settings.daysBefore.join(', ')}`);
    console.log(`   - Email: ${settings.methods.email}, In-App: ${settings.methods.inApp}`);
    
    return settings;
  } catch (error) {
    console.error('❌ Error setting up billing notification settings:', error);
    throw error;
  }
}

async function testWithExistingVPS() {
  try {
    console.log('\n🧪 Testing VPS Billing Notification System with Existing Data');
    console.log('=============================================================');
    
    // Connect to database
    await connectToDatabase();
    
    // Find existing VPS instances
    const vpsInstances = await findExistingVPSInstances();
    
    if (vpsInstances.length === 0) {
      console.log('\n❌ No VPS instances found. Please create a VPS instance first.');
      return;
    }
    
    // Use the first VPS instance for testing
    const testInstance = vpsInstances[0];
    console.log(`\n🎯 Using VPS instance: ${testInstance.instanceId} for testing`);
    
    // Update the billing date to tomorrow for testing
    console.log('\n📅 Setting up test billing dates...');
    await updateVPSBillingDate(testInstance.instanceId, 1); // Due tomorrow
    
    // Setup billing notification settings
    await setupBillingNotificationSettings();
    
    console.log('\n🔔 Triggering manual billing notification check...');
    
    // Trigger the billing notification check
    await vpsBillingService.triggerManualCheck();
    
    console.log('\n✅ Test completed successfully!');
    console.log('\n📧 Check the following:');
    console.log('   1. Email notifications sent to the VPS owner');
    console.log('   2. In-app notifications created in the database');
    console.log('   3. Console logs above for any errors');
    
    // Show what notifications should have been sent
    const user = testInstance.user;
    if (user && user.email) {
      console.log(`\n💌 Expected email notification sent to: ${user.email}`);
      console.log(`📱 Expected in-app notification created for user: ${user._id}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

async function testMultipleDays() {
  try {
    console.log('\n🧪 Testing Multiple Billing Date Scenarios');
    console.log('==========================================');
    
    await connectToDatabase();
    
    const vpsInstances = await findExistingVPSInstances();
    
    if (vpsInstances.length === 0) {
      console.log('\n❌ No VPS instances found.');
      return;
    }
    
    const testInstance = vpsInstances[0];
    console.log(`\n🎯 Using VPS instance: ${testInstance.instanceId} for multiple day testing`);
    
    // Setup billing notification settings
    await setupBillingNotificationSettings();
    
    // Test different billing dates
    const testDays = [1, 3, 7];
    
    for (const days of testDays) {
      console.log(`\n📅 Testing billing notification for ${days} day(s) before billing...`);
      
      // Update billing date
      await updateVPSBillingDate(testInstance.instanceId, days);
      
      // Trigger notification check
      console.log(`🔔 Triggering notification check for ${days} day(s) scenario...`);
      await vpsBillingService.triggerManualCheck();
      
      // Wait a moment between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✅ Multiple day testing completed!');
    
  } catch (error) {
    console.error('❌ Multiple day test failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

// Check command line arguments
const args = process.argv.slice(2);

if (args.includes('--multiple')) {
  testMultipleDays();
} else {
  testWithExistingVPS();
}
