const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const brandSchema = new Schema(
  {
    name: { type: String, required: true },
    name_fr: { type: String },
    description: { type: String },
    description_fr: { type: String },
    category: { type: Schema.Types.ObjectId, ref: "Category", required: true },
    logo: { type: String },
    packages: [{ type: Schema.Types.ObjectId, ref: "Package" }],
  },
  { timestamps: true }
);

const Brand = mongoose.model("Brand", brandSchema);
module.exports = Brand;
