"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import AiIntro from "@/components/ai-services/aiIntro";
import AiMain from "@/components/ai-services/aiMain";
import ContactForm2 from "@/components/shared/contactForm2";
import DynamicMetadataClient from "@/components/DynamicMetadataClient";

const animations = {
  fadeInLeft: {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
  fadeInRight: {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  },
  fadeInUp: {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.95 
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.8, 
        ease: [0.4, 0, 0.2, 1],
        delay: 0.4 
      },
    },
  },
};

function AiPage() {
  const t = useTranslations("ai");

  const [data, setData] = useState({
    page: "AI page",
    offerName: "No offer selected",
    id: 0,
    url: "/ai",
  });

  return (
    <div className="flex flex-col gap-y-5 h-full w-full">
      <DynamicMetadataClient
  title={`ZtechEngineering | ${t('title')}`}
  desc={`${t('desc')}`}
/> 
      <AiIntro t={t} animations={animations} />
      <AiMain t={t} animations={animations} />
      <ContactForm2 data={data} setData={setData} />
    </div>
  );
}

export default AiPage;
