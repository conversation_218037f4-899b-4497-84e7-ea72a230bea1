/**
 * Debug script to test exactly what the frontend receives
 * This simulates the exact API call the frontend makes
 */

const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3001';
const BACKEND_URL = 'http://localhost:5002';

async function debugRegionsFrontend() {
  console.log('🔍 Debugging regions from frontend perspective...\n');

  try {
    // Test 1: Direct backend call (what we know works)
    console.log('1. Testing direct backend call...');
    try {
      const response = await axios.get(`${BACKEND_URL}/api/regions`);
      console.log('✅ Direct backend response:', {
        status: response.status,
        success: response.data.success,
        regionsCount: response.data.data?.length || 0
      });
      
      if (response.data.data && response.data.data.length > 0) {
        console.log('Sample region from backend:');
        const region = response.data.data[0];
        console.log(`   - ${region.name} (${region.regionId})`);
        console.log(`   - Country: ${region.country}`);
        console.log(`   - Status: ${region.status}`);
        console.log(`   - Pricing: ${region.pricing?.length || 0} entries`);
      }
    } catch (error) {
      console.error('❌ Direct backend failed:', error.message);
    }

    // Test 2: Frontend proxy call (what the frontend actually uses)
    console.log('\n2. Testing frontend proxy call...');
    try {
      const response = await axios.get(`${FRONTEND_URL}/api/regions`);
      console.log('✅ Frontend proxy response:', {
        status: response.status,
        success: response.data.success,
        regionsCount: response.data.data?.length || 0
      });
      
      if (response.data.data && response.data.data.length > 0) {
        console.log('Sample region from frontend proxy:');
        const region = response.data.data[0];
        console.log(`   - ${region.name} (${region.regionId})`);
        console.log(`   - Country: ${region.country}`);
        console.log(`   - Status: ${region.status}`);
        console.log(`   - Pricing: ${region.pricing?.length || 0} entries`);
      } else {
        console.log('⚠️ No regions returned from frontend proxy - this is the issue!');
      }
    } catch (error) {
      console.error('❌ Frontend proxy failed:', error.response?.data || error.message);
    }

    // Test 3: Check if frontend is using the correct service
    console.log('\n3. Testing regionsService.getActiveRegions() equivalent...');
    try {
      // This simulates what regionsService.getActiveRegions() does
      const response = await axios.get(`${BACKEND_URL}/api/regions`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ regionsService equivalent response:', {
        status: response.status,
        success: response.data.success,
        regionsCount: response.data.data?.length || 0
      });
      
      // Check the exact structure the frontend expects
      if (response.data && Array.isArray(response.data.data)) {
        const regions = response.data.data
          .filter(region => region.status === 'active' || !region.status) // Frontend filtering
          .sort((a, b) => {
            if (a.displayOrder !== b.displayOrder) {
              return (a.displayOrder || 0) - (b.displayOrder || 0);
            }
            return a.name.localeCompare(b.name);
          });
        
        console.log(`After frontend filtering: ${regions.length} regions`);
        
        if (regions.length > 0) {
          console.log('Filtered regions:');
          regions.forEach(region => {
            console.log(`   - ${region.name} (${region.regionId})`);
            console.log(`     Status: ${region.status || 'undefined'}`);
            console.log(`     Country: ${region.country}`);
            console.log(`     Pricing: ${region.pricing?.length || 0} entries`);
          });
        }
      }
    } catch (error) {
      console.error('❌ regionsService equivalent failed:', error.message);
    }

    console.log('\n📋 Diagnosis:');
    console.log('- If direct backend works but frontend proxy fails, there\'s a proxy issue');
    console.log('- If both work but frontend filtering removes regions, there\'s a filtering issue');
    console.log('- If regions have status: undefined, that\'s the root cause');

  } catch (error) {
    console.error('❌ Debug suite failed:', error.message);
  }
}

// Run the debug
if (require.main === module) {
  debugRegionsFrontend();
}

module.exports = { debugRegionsFrontend };
