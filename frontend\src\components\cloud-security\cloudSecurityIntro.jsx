'use client'
import React from 'react'
import { Typography } from '@material-tailwind/react'
import { motion } from "framer-motion";
import CloudSecurity from 'src/assets/cloud-security.json'
import { ShieldCheck, Lock, Server, LockIcon, Quote } from "lucide-react";
import Section from '../home/<USER>';
import <PERSON><PERSON> from 'lottie-react';


function CloudSecurityIntro({ t, animations }) {
    const features = [
        {
            icon: <ShieldCheck className="w-6 h-6" />,
            title: t('feature1.title'),
            description: t('feature1.description')
        },
        {
            icon: <Lock className="w-6 h-6" />,
            title: t('feature2.title'),
            description: t('feature2.description')
        },
        {
            icon: <Server className="w-6 h-6" />,
            title: t('feature3.title'),
            description: t('feature3.description')
        }
    ];

    return (
        <Section className="relative overflow-hidden">
            <div>
                <div className="relative z-10 flex flex-col lg:flex-row justify-between px-4 sm:px-6 lg:px-8">
                    {/* Text Content */}
                    <motion.div 
                    variants={animations.fadeInLeft}
                    initial="hidden"
                    animate="visible"
                    className="space-y-8"
                    >
                        <div className="space-y-4">
                            <Typography
                                variant="small"
                                color="blue"
                                className="inline-flex items-center gap-2 text-indigo-500 font-inter py-2 px-4 rounded-full font-light tracking-widest uppercase border border-indigo-200 shadow-sm hover:shadow-md transition-shadow duration-300"
                            >
                                <LockIcon className='w-5 h-5' />
                                {t('secureCloudTitle')}
                            </Typography>
                            <Typography
                                variant="h1"
                                className="text-2xl sm:text-4xl lg:text-5xl font-bold leading-tight tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500"
                            >
                                {t('secureCloudSubtitle')}
                            </Typography>
                            <Typography
                                variant="paragraph"
                                className="text-base font-inter text-gray-600"
                            >
                                {t('secureCloudDescription')}
                            </Typography>
                        </div>

                        {/* Features Grid */}
                        <div className="grid md:grid-cols-3 gap-6">
                            {features.map((feature, index) => (
                                <div key={index} className="p-4 rounded-xl bg-white shadow-md hover:shadow-lg transition-shadow">
                                    <div className="text-blue-500 mb-3">{feature.icon}</div>
                                    <Typography variant="h6" className="mb-2">
                                        {feature.title}
                                    </Typography>
                                    <Typography variant="small" className="text-gray-600">
                                        {feature.description}
                                    </Typography>
                                </div>
                            ))}
                        </div>
                    </motion.div>

                    {/* Image Section */}
                    <motion.div 
                    variants={animations.fadeInRight}
                    initial="hidden"
                    animate="visible" className="relative w-full md:w-1/2 mx-auto">
                        <div className="absolute inset-0 rounded-full blur-3xl bg-indigo-200 opacity-20" />
                        <Lottie 
                            animationData={CloudSecurity}
                            loop
                            autoplay
                            className="w-full h-auto"
                        />
                        
                    </motion.div>
                </div>

                <motion.div 
                    variants={animations.fadeInUp}
                    initial="hidden"
                    animate="visible"
                >
                    <Typography
                        variant="small"
                        className="text-gray-800 max-w-6xl mx-auto text-center text-base font-inter leading-relaxed px-4"
                    >
                        {t('secureCloudBody')}
                    </Typography>
                </motion.div>
            </div>
        </Section>
    )
}

export default CloudSecurityIntro