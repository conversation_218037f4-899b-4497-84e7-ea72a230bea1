import { useTranslations } from "next-intl";
import React from "react";

const TermsAndConditions = () => {
  const t = useTranslations("terms_of_service");
  return (
    <div className="max-w-4xl mx-auto p-6 text-gray-800 leading-relaxed">
      <header className="mb-8">
        <h1 className="text-4xl font-bold text-center">{t("title")}</h1>
        <p className="text-sm text-center text-gray-600 mt-2">
          {t("last_updated")}
        </p>
      </header>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("introduction_title")}
        </h2>
        <p className="mb-4">{t("introduction_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("account_security_title")}
        </h2>
        <p className="mb-4">{t("account_security_content")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("account_security_item1")}</li>
          <li>{t("account_security_item2")}</li>
        </ul>
        <p className="mb-4">
          {t("account_security_notify")}{" "}
          <a href="mailto:<EMAIL>" className="text-blue-500">
            {t("contact_email")}
          </a>
          .
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("intellectual_property_title")}
        </h2>
        <p className="mb-4">{t("intellectual_property_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("cookies_title")}</h2>
        <p className="mb-4">{t("cookies_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("third_party_links_title")}
        </h2>
        <p className="mb-4">{t("third_party_links_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("acceptable_use_title")}
        </h2>
        <p className="mb-4">{t("acceptable_use_content")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("acceptable_use_item1")}</li>
          <li>{t("acceptable_use_item2")}</li>
          <li>{t("acceptable_use_item3")}</li>
          <li>{t("acceptable_use_item4")}</li>
          <li>{t("acceptable_use_item5")}</li>
        </ul>
        <p className="mb-4">{t("acceptable_use_action")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("service_specific_terms_title")}
        </h2>
        <h3 className="text-xl font-semibold mb-2">{t("web_hosting_title")}</h3>
        <p className="mb-4">{t("web_hosting_content")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("web_hosting_item1")}</li>
          <li>{t("web_hosting_item2")}</li>
          <li>{t("web_hosting_item3")}</li>
        </ul>
        <p className="mb-4">{t("web_hosting_details")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("web_hosting_detail_item1")}</li>
          <li>{t("web_hosting_detail_item2")}</li>
          <li>{t("web_hosting_detail_item3")}</li>
          <li>{t("web_hosting_detail_item4")}</li>
          <li>{t("web_hosting_detail_item5")}</li>
          <li>{t("web_hosting_detail_item6")}</li>
          <li>{t("web_hosting_detail_item7")}</li>
          <li>{t("web_hosting_detail_item8")}</li>
        </ul>

        <h3 className="text-xl font-semibold mb-2">
          {t("ssl_certificates_title")}
        </h3>
        <p className="mb-4">{t("ssl_certificates_content")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("ssl_certificates_item1")}</li>
          <li>{t("ssl_certificates_item2")}</li>
          <li>{t("ssl_certificates_item3")}</li>
        </ul>
        <p className="mb-4">{t("ssl_certificates_details")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("ssl_certificates_detail_item1")}</li>
          <li>{t("ssl_certificates_detail_item2")}</li>
          <li>{t("ssl_certificates_detail_item3")}</li>
          <li>{t("ssl_certificates_detail_item4")}</li>
          <li>{t("ssl_certificates_detail_item5")}</li>
          <li>{t("ssl_certificates_detail_item6")}</li>
        </ul>

        <h3 className="text-xl font-semibold mb-2">
          {t("server_sales_title")}
        </h3>
        <p className="mb-4">{t("server_sales_content")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("server_sales_item1")}</li>
          <li>{t("server_sales_item2")}</li>
          <li>{t("server_sales_item3")}</li>
        </ul>
        <p className="mb-4">{t("server_sales_details")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("server_sales_detail_item1")}</li>
          <li>{t("server_sales_detail_item2")}</li>
          <li>{t("server_sales_detail_item3")}</li>
          <li>{t("server_sales_detail_item4")}</li>
          <li>{t("server_sales_detail_item5")}</li>
          <li>{t("server_sales_detail_item6")}</li>
          <li>{t("server_sales_detail_item7")}</li>
        </ul>

        <h3 className="text-xl font-semibold mb-2">
          {t("website_creation_title")}
        </h3>
        <p className="mb-4">{t("website_creation_content")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("website_creation_item1")}</li>
          <li>{t("website_creation_item2")}</li>
          <li>{t("website_creation_item3")}</li>
          <li>{t("website_creation_item4")}</li>
        </ul>
        <p className="mb-4">{t("website_creation_details")}</p>
        <ul className="list-disc list-inside mb-4">
          <li>{t("website_creation_detail_item1")}</li>
          <li>{t("website_creation_detail_item2")}</li>
          <li>{t("website_creation_detail_item3")}</li>
          <li>{t("website_creation_detail_item4")}</li>
          <li>{t("website_creation_detail_item5")}</li>
          <li>{t("website_creation_detail_item6")}</li>
          <li>{t("website_creation_detail_item7")}</li>
          <li>{t("website_creation_detail_item8")}</li>
          <li>{t("website_creation_detail_item9")}</li>
          <li>{t("website_creation_detail_item10")}</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("payment_terms_title")}
        </h2>
        <p className="mb-4">{t("payment_terms_content")}</p>
        <p className="mb-4">{t("order_placement")}</p>
        <p className="mb-4">{t("payment_requirement")}</p>
        <p className="mb-4">{t("price_change")}</p>
        <img
          className="w-[600px] h-fit rounded-lg"
          src="/images/payzone/Moyen-de-sécurité-ABB.png"
          alt="term of services"
        />
      </section>
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("refund_and_cancellation_policy_title")}
        </h2>
        <p className="mb-4">{t("hosting_refund")}</p>
        <p className="mb-4">{t("ssl_refund")}</p>
        <p className="mb-4">{t("web_mobile_refund")}</p>
        <p className="mb-4">{t("cloud_services_refund")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("data_protection_title")}
        </h2>
        <p className="mb-4">{t("data_protection_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("limitation_liability_title")}
        </h2>
        <p className="mb-4">{t("limitation_liability_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("termination_title")}
        </h2>
        <p className="mb-4">{t("termination_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("governing_law_title")}
        </h2>
        <p className="mb-4">{t("governing_law_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("modifications_title")}
        </h2>
        <p className="mb-4">{t("modifications_content")}</p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("general_provisions_title")}
        </h2>
        <ul className="list-disc list-inside mb-4">
          <li>{t("general_provisions_item1")}</li>
          <li>{t("general_provisions_item2")}</li>
          <li>{t("general_provisions_item3")}</li>
          <li>{t("general_provisions_item4")}</li>
          <li>{t("general_provisions_item5")}</li>
          <li>{t("general_provisions_item6")}</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">
          {t("contact_information_title")}
        </h2>
        <p className="mb-4">
          {t("contact_information_content")}{" "}
          <a href="mailto:<EMAIL>" className="text-blue-500">
            {t("contact_email")}
          </a>{" "}
          {t("call_us")}
        </p>
      </section>
    </div>
  );
};

export default TermsAndConditions;
