import apiService from '../lib/apiService';

const categoryService = {
    createCategory: (data) => apiService.post('/category/create-category', data),
    getCategories: () => apiService.get('/category/get-categories'),
    getCategory: (name) => apiService.get(`/category/get-category/${name}`),
    updateCategory: (id, data) => apiService.put(`/category/update-category/${id}`, data),
    deleteCategory: (id) => apiService.delete(`/category/delete-category/${id}`),
};

export default categoryService;
