import React, { useEffect, useState } from "react";
import { ONESTARsvg, ORANGESTARSsvg, PURBLESTARSsvg } from "../../icons/svgIcons";
import { useRouter } from "next/navigation";
import categoryService from "../../app/services/categoryService";
import { useAuth } from "../../app/context/AuthContext";
import cartService from "../../app/services/cartService";

const svg1 = <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.5" width="24" height="24" rx="12" fill="#AEAEAF" />
    <path d="M14.72 9.28998L10.43 13.59L8.78 11.94C8.69036 11.8353 8.58004 11.7503 8.45597 11.6903C8.33191 11.6303 8.19678 11.5965 8.05906 11.5912C7.92134 11.5859 7.78401 11.6091 7.65568 11.6594C7.52736 11.7096 7.41081 11.7859 7.31335 11.8833C7.2159 11.9808 7.13964 12.0974 7.08937 12.2257C7.03909 12.354 7.01589 12.4913 7.02121 12.6291C7.02653 12.7668 7.06026 12.9019 7.12028 13.026C7.1803 13.15 7.26532 13.2604 7.37 13.35L9.72 15.71C9.81344 15.8027 9.92426 15.876 10.0461 15.9258C10.1679 15.9755 10.2984 16.0008 10.43 16C10.6923 15.9989 10.9437 15.8947 11.13 15.71L16.13 10.71C16.2237 10.617 16.2981 10.5064 16.3489 10.3846C16.3997 10.2627 16.4258 10.132 16.4258 9.99998C16.4258 9.86797 16.3997 9.73727 16.3489 9.61541C16.2981 9.49355 16.2237 9.38294 16.13 9.28998C15.9426 9.10373 15.6892 8.9992 15.425 8.9992C15.1608 8.9992 14.9074 9.10373 14.72 9.28998ZM12 2.49998C10.0222 2.49998 8.08879 3.08647 6.4443 4.18528C4.79981 5.2841 3.51809 6.84588 2.76121 8.67315C2.00433 10.5004 1.8063 12.5111 2.19215 14.4509C2.578 16.3907 3.53041 18.1725 4.92894 19.5711C6.32746 20.9696 8.10929 21.922 10.0491 22.3079C11.9889 22.6937 13.9996 22.4957 15.8268 21.7388C17.6541 20.9819 19.2159 19.7002 20.3147 18.0557C21.4135 16.4112 22 14.4778 22 12.5C22 11.1868 21.7413 9.8864 21.2388 8.67315C20.7363 7.45989 19.9997 6.3575 19.0711 5.42891C18.1425 4.50033 17.0401 3.76373 15.8268 3.26118C14.6136 2.75864 13.3132 2.49998 12 2.49998ZM12 20.5C10.4178 20.5 8.87104 20.0308 7.55544 19.1518C6.23985 18.2727 5.21447 17.0233 4.60897 15.5615C4.00347 14.0997 3.84504 12.4911 4.15372 10.9393C4.4624 9.38741 5.22433 7.96196 6.34315 6.84313C7.46197 5.72431 8.88743 4.96238 10.4393 4.6537C11.9911 4.34502 13.5997 4.50344 15.0615 5.10894C16.5233 5.71445 17.7727 6.73982 18.6518 8.05542C19.5308 9.37101 20 10.9177 20 12.5C20 14.6217 19.1572 16.6566 17.6569 18.1569C16.1566 19.6571 14.1217 20.5 12 20.5Z" fill="white" />
</svg>

const svg2 = <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.5" width="24" height="24" rx="12" fill="#F7AF2A" />
    <path d="M14.72 9.28998L10.43 13.59L8.78 11.94C8.69036 11.8353 8.58004 11.7503 8.45597 11.6903C8.33191 11.6303 8.19678 11.5965 8.05906 11.5912C7.92134 11.5859 7.78401 11.6091 7.65568 11.6594C7.52736 11.7096 7.41081 11.7859 7.31335 11.8833C7.2159 11.9808 7.13964 12.0974 7.08937 12.2257C7.03909 12.354 7.01589 12.4913 7.02121 12.6291C7.02653 12.7668 7.06026 12.9019 7.12028 13.026C7.1803 13.15 7.26532 13.2604 7.37 13.35L9.72 15.71C9.81344 15.8027 9.92426 15.876 10.0461 15.9258C10.1679 15.9755 10.2984 16.0008 10.43 16C10.6923 15.9989 10.9437 15.8947 11.13 15.71L16.13 10.71C16.2237 10.617 16.2981 10.5064 16.3489 10.3846C16.3997 10.2627 16.4258 10.132 16.4258 9.99998C16.4258 9.86797 16.3997 9.73727 16.3489 9.61541C16.2981 9.49355 16.2237 9.38294 16.13 9.28998C15.9426 9.10373 15.6892 8.9992 15.425 8.9992C15.1608 8.9992 14.9074 9.10373 14.72 9.28998ZM12 2.49998C10.0222 2.49998 8.08879 3.08647 6.4443 4.18528C4.79981 5.2841 3.51809 6.84588 2.76121 8.67315C2.00433 10.5004 1.8063 12.5111 2.19215 14.4509C2.578 16.3907 3.53041 18.1725 4.92894 19.5711C6.32746 20.9696 8.10929 21.922 10.0491 22.3079C11.9889 22.6937 13.9996 22.4957 15.8268 21.7388C17.6541 20.9819 19.2159 19.7002 20.3147 18.0557C21.4135 16.4112 22 14.4778 22 12.5C22 11.1868 21.7413 9.8864 21.2388 8.67315C20.7363 7.45989 19.9997 6.3575 19.0711 5.42891C18.1425 4.50033 17.0401 3.76373 15.8268 3.26118C14.6136 2.75864 13.3132 2.49998 12 2.49998ZM12 20.5C10.4178 20.5 8.87104 20.0308 7.55544 19.1518C6.23985 18.2727 5.21447 17.0233 4.60897 15.5615C4.00347 14.0997 3.84504 12.4911 4.15372 10.9393C4.4624 9.38741 5.22433 7.96196 6.34315 6.84313C7.46197 5.72431 8.88743 4.96238 10.4393 4.6537C11.9911 4.34502 13.5997 4.50344 15.0615 5.10894C16.5233 5.71445 17.7727 6.73982 18.6518 8.05542C19.5308 9.37101 20 10.9177 20 12.5C20 14.6217 19.1572 16.6566 17.6569 18.1569C16.1566 19.6571 14.1217 20.5 12 20.5Z" fill="white" />
</svg>

const svg3 = <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect y="0.5" width="24" height="24" rx="12" fill="#497EF7" />
    <path d="M14.72 9.28998L10.43 13.59L8.78 11.94C8.69036 11.8353 8.58004 11.7503 8.45597 11.6903C8.33191 11.6303 8.19678 11.5965 8.05906 11.5912C7.92134 11.5859 7.78401 11.6091 7.65568 11.6594C7.52736 11.7096 7.41081 11.7859 7.31335 11.8833C7.2159 11.9808 7.13964 12.0974 7.08937 12.2257C7.03909 12.354 7.01589 12.4913 7.02121 12.6291C7.02653 12.7668 7.06026 12.9019 7.12028 13.026C7.1803 13.15 7.26532 13.2604 7.37 13.35L9.72 15.71C9.81344 15.8027 9.92426 15.876 10.0461 15.9258C10.1679 15.9755 10.2984 16.0008 10.43 16C10.6923 15.9989 10.9437 15.8947 11.13 15.71L16.13 10.71C16.2237 10.617 16.2981 10.5064 16.3489 10.3846C16.3997 10.2627 16.4258 10.132 16.4258 9.99998C16.4258 9.86797 16.3997 9.73727 16.3489 9.61541C16.2981 9.49355 16.2237 9.38294 16.13 9.28998C15.9426 9.10373 15.6892 8.9992 15.425 8.9992C15.1608 8.9992 14.9074 9.10373 14.72 9.28998ZM12 2.49998C10.0222 2.49998 8.08879 3.08647 6.4443 4.18528C4.79981 5.2841 3.51809 6.84588 2.76121 8.67315C2.00433 10.5004 1.8063 12.5111 2.19215 14.4509C2.578 16.3907 3.53041 18.1725 4.92894 19.5711C6.32746 20.9696 8.10929 21.922 10.0491 22.3079C11.9889 22.6937 13.9996 22.4957 15.8268 21.7388C17.6541 20.9819 19.2159 19.7002 20.3147 18.0557C21.4135 16.4112 22 14.4778 22 12.5C22 11.1868 21.7413 9.8864 21.2388 8.67315C20.7363 7.45989 19.9997 6.3575 19.0711 5.42891C18.1425 4.50033 17.0401 3.76373 15.8268 3.26118C14.6136 2.75864 13.3132 2.49998 12 2.49998ZM12 20.5C10.4178 20.5 8.87104 20.0308 7.55544 19.1518C6.23985 18.2727 5.21447 17.0233 4.60897 15.5615C4.00347 14.0997 3.84504 12.4911 4.15372 10.9393C4.4624 9.38741 5.22433 7.96196 6.34315 6.84313C7.46197 5.72431 8.88743 4.96238 10.4393 4.6537C11.9911 4.34502 13.5997 4.50344 15.0615 5.10894C16.5233 5.71445 17.7727 6.73982 18.6518 8.05542C19.5308 9.37101 20 10.9177 20 12.5C20 14.6217 19.1572 16.6566 17.6569 18.1569C16.1566 19.6571 14.1217 20.5 12 20.5Z" fill="white" />
</svg>

const HostingPricingTable = ({ setData, t }) => {
    const router = useRouter();
    const [hostingPacks, setHostingPacks] = useState();
    const { setCartCount, cartCount } = useAuth()

    useEffect(() => {
        // Fetching data from JSON file
        const getHostingData = async () => {
            try {
                const response = await categoryService.getCategory('Promotions');
                const responseTest = await categoryService.getCategory('Hosting');
                console.log('response.data ..: ', response.data.brands[0].packages);
                console.log('responseTest.data ..: ', responseTest.data.brands[0].packages[0]._id);
                setHostingPacks(responseTest.data.brands[0].packages);
                console.log("id 1 : ", hostingPacks[0]._id);

            } catch (error) {
                console.error('error fetching promotions: ', error);
            }
        };
        getHostingData();
    }, []);

    const handleAddPackageToCart = async (id) => {
        try {
            const res = await cartService.addItemToCart({ packageId: id, quantity: 1 });
            console.log('addPackageToCart :', cartCount);
            setCartCount(res.data.cart.cartCount);
        } catch (error) {
            console.error('error adding package to cart', error);
        }
    }

    const serverData = [
        { label: t('disk_space'), values: ["400 Go SSD", "800 Go SSD", "1200 Go SSD"] },
        { label: t('memory'), values: ["4 Gb RAM", "16 Gb RAM", "24 Gb RAM"] },
        { label: t('cpu_processor'), values: ["6 " + t('cpu_cores'), "6 " + t('cpu_cores'), "8 " + t('cpu_cores')] },
        { label: t('port'), values: ["100 Mbit/s", "100 Mbit/s", "500 Mbit/s"] },
        { label: t('fiber_optic_online'), values: [t('free'), t('free'), t('free')] },
        { label: t('remote_restart'), values: [t('included'), t('included'), t('included')] },
        { label: t('ssh_access'), values: [t('included'), t('included'), t('included')] },
        { label: t('vnc_access'), values: [svg1, svg2, svg3] },
        { label: t('os_reinstallation'), values: [svg1, svg2, svg3] },
        { label: t('minimum_contract'), values: ["1 " + t('month'), "1 " + t('month'), "1 " + t('month')] },
        { label: t('ipv4'), values: ["1", "1", "1"] },
        { label: t('managed_service'), values: [t('optional'), t('optional'), t('optional')] },
    ]

    return (
        <div className="max-w-[1400px] p-0 bg-white w-full mx-auto my-10">
            <div className="p-2 md:py-4 w-full flex flex-col md:flex-row justify-between gap-4 mb-0 md:items-end items-center">
                <div className="flex flex-col">
                    <h2 className="text-3xl font-medium text-gray-800 mb-1 font-outfit text-center md:text-left">
                        {t('pricing')}
                    </h2>
                    <p className="text-gray-500 mb-0 text-lg font-inter max-w-[230px]">
                        {t('plans_title')}
                    </p>
                </div>
                <div className="flex gap-x-2 justify-between text-center md:w-3/4 w-full h-fit text-[#212121] md:text-xl text-base">
                    <div className="flex items-center md:w-[300px] w-1/3 md:py-1 py-2 bg-[#f5f5f5] border border-[#AEAEAF] md:rounded-full rounded-md">
                        <div className="flex flex-col md:justify-evenly md:px-4 md:gap-x-2 md:flex-row">
                            <span className="mx-auto mt-auto md:my-auto">
                                <ONESTARsvg />
                            </span>
                            <span className="md:w-full text-base md:px-0 px-2 mt-2 md:my-auto">
                                Cloud / VPS Perso SSD
                            </span>
                            <div className="md:hidden flex flex-col mt-2">
                                <span className="font-inter text-2xl font-extrabold">
                                    429
                                </span>
                                <span className="text-sm">
                                    DH HT/{t('month')}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center md:w-[300px] w-1/3 md:py-1 py-2 bg-[#F7AF2A1C] border border-[#F7AF2A] md:rounded-full rounded-md">
                        <div className="flex flex-col md:justify-evenly md:px-4 md:gap-x-2 md:flex-row">
                            <span className="mx-auto md:my-auto">
                                <ORANGESTARSsvg />
                            </span>
                            <span className="md:w-full text-base mt-2 md:my-auto">
                                Cloud / VPS PRO SSD
                            </span>
                            <div className="md:hidden flex flex-col mt-2">
                                <span className="font-inter text-2xl font-extrabold">
                                    519
                                </span>
                                <span className="text-sm">
                                    DH HT/{t('month')}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center md:w-[300px] w-1/3 md:py-1 py-2 bg-[#ebf1fe] border border-secondary md:rounded-full rounded-md">
                        <div className="flex flex-col md:justify-evenly md:px-4 px-0 md:gap-x-2 md:flex-row">
                            <span className="mx-auto md:my-auto">
                                <PURBLESTARSsvg />
                            </span>
                            <span className="md:w-full text-base mt-2 md:my-auto">
                                Cloud / VPS business SSD
                            </span>
                            <div className="md:hidden flex flex-col mt-2">
                                <span className="font-inter text-2xl font-extrabold">
                                    649
                                </span>
                                <span className="text-sm">
                                    DH HT/{t('month')}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <table className="p-8 hidden md:inline-table w-full text-base font-inter text-left text-gray-500 border">
                <tbody>
                    {serverData.map((row, idx) => (
                        <tr key={idx} className="border-t border-b border-gray-200">
                            <td className="p-4 font-semibold text-[#333333] w-1/4">{row.label}</td>
                            {row.values.map((value, i) => (
                                <td key={i} className="p-4 text-center m-auto  justify-items-center">{value}</td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>

            <div className="p-8 hidden md:flex justify-between items-center mt-8 w-3/4 ml-auto px-16">
                <div className="text-center">
                    <p className="text-sm font-normal font-inter text-gray-800">
                        <span className="font-extrabold text-xl">429</span> DH HT/{t('month')}
                    </p>
                    <button
                        // onClick={() => handleOfferClick("Cloud / VPS Perso SSD", 4)}
                        onClick={() => {
                            handleAddPackageToCart(hostingPacks[0]._id)
                            router.push('/client/cart');
                        }}
                        className="text-lg mt-2 px-10 py-2 bg-white border text-black rounded-lg">
                        {t('choose_plan')}
                    </button>
                </div>
                <div className="text-center">
                    <p className="text-sm font-normal font-inter text-gray-800">
                        <span className="font-extrabold text-xl">519</span> DH HT/{t('month')}
                    </p>
                    <button
                        // onClick={() => handleOfferClick("Cloud / VPS PRO SSD", 5)}
                        onClick={() => {
                            handleAddPackageToCart(hostingPacks[1]._id)
                            router.push('/client/cart');
                        }}
                        className="text-lg mt-2 px-10 py-2 bg-[#F7AF2A] text-white rounded-lg">
                        {t('choose_plan')}
                    </button>
                </div>
                <div className="text-center">
                    <p className="text-sm font-normal font-inter text-gray-800">
                        <span className="font-extrabold text-xl">649</span> DH HT/{t('month')}
                    </p>
                    <button
                        // onClick={() => handleOfferClick("Cloud / VPS business SSD", 6)}
                        onClick={() => {
                            handleAddPackageToCart(hostingPacks[2]._id)
                            router.push('/client/cart');
                        }}
                        className="text-lg mt-2 px-10 py-2 bg-[#497EF7] text-white rounded-lg">
                        {t('choose_plan')}
                    </button>
                </div>
            </div>


            {/* this is the table version for mobile size */}

            <div className="p-4 md:hidden flex flex-col gap-y-8 w-full text-base font-inter text-left text-gray-500">
                {serverData.map((row, idx) => (
                    <div key={idx} className="flex flex-col gap-y-1">
                        <div className="p-0 font-semibold text-gray-700 w-full text-sm">{row.label}</div>
                        <div className="flex border-y">
                            {row.values.map((value, i) => (
                                <div key={i} className={`py-1 text-center m-auto justify-items-center ${i % 2 != 0 ? "bg-[#FFFCF6]" : ""} w-1/3`}>{value}</div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>

            <div className="px-2 flex md:hidden gap-x-2 justify-between text-center md:w-3/4 w-full h-fit text-[#212121] md:text-xl text-base">
                <div className="flex items-center md:w-[300px] w-1/3 md:py-1 py-2 bg-[#f5f5f5] border border-[#AEAEAF] md:rounded-full rounded-md">
                    <div className="flex flex-col md:justify-evenly md:px-4 md:gap-x-2 md:flex-row">
                        <span className="mx-auto mt-auto md:my-auto">
                            <ONESTARsvg />
                        </span>
                        <span className="md:w-full text-base md:px-0 px-2 mt-2 md:my-auto">
                            Cloud / VPS Perso SSD
                        </span>
                        <div className="md:hidden flex flex-col mt-2">
                            <span className="font-inter text-2xl font-extrabold">
                                429
                            </span>
                            <span className="text-sm">
                                DH HT/{t('month')}
                            </span>
                        </div>
                        <button
                            // onClick={() => handleOfferClick("Cloud / VPS Perso SSD", 4)}
                            onClick={() => {
                                handleAddPackageToCart(hostingPacks[0]._id)
                                router.push('/client/cart');
                            }}
                            className="text-lg mt-2 px-10 py-2 bg-white border text-black rounded-lg">
                            {t('choose_plan')}
                        </button>
                    </div>
                </div>
                <div className="flex items-center md:w-[300px] w-1/3 md:py-1 py-2 bg-[#F7AF2A1C] border border-[#F7AF2A] md:rounded-full rounded-md">
                    <div className="flex flex-col md:justify-evenly md:px-4 md:gap-x-2 md:flex-row">
                        <span className="mx-auto md:my-auto">
                            <ORANGESTARSsvg />
                        </span>
                        <span className="md:w-full text-base mt-2 md:my-auto">
                            Cloud / VPS PRO SSD
                        </span>
                        <div className="md:hidden flex flex-col mt-2">
                            <span className="font-inter text-2xl font-extrabold">
                                519
                            </span>
                            <span className="text-sm">
                                DH HT/{t('month')}
                            </span>
                        </div>
                        <button
                            // onClick={() => handleOfferClick("Cloud / VPS PRO SSD", 5)}
                            onClick={() => {
                                handleAddPackageToCart(hostingPacks[1]._id)
                                router.push('/client/cart');
                            }}
                            className="text-lg mt-2 px-10 py-2 bg-[#F7AF2A] text-white rounded-lg">
                            {t('choose_plan')}
                        </button>
                    </div>
                </div>
                <div className="flex items-center md:w-[300px] w-1/3 md:py-1 py-2 bg-[#ebf1fe] border border-secondary md:rounded-full rounded-md">
                    <div className="flex flex-col md:justify-evenly md:px-4 px-0 md:gap-x-2 md:flex-row">
                        <span className="mx-auto md:my-auto">
                            <PURBLESTARSsvg />
                        </span>
                        <span className="md:w-full text-base mt-2 md:my-auto">
                            Cloud / VPS business SSD
                        </span>
                        <div className="md:hidden flex flex-col mt-2">
                            <span className="font-inter text-2xl font-extrabold">
                                649
                            </span>
                            <span className="text-sm">
                                DH HT/{t('month')}
                            </span>
                        </div>
                        <button
                            // onClick={() => handleOfferClick("Cloud / VPS business SSD", 6)}
                            onClick={() => {
                                handleAddPackageToCart(hostingPacks[2]._id)
                                router.push('/client/cart');
                            }}

                            className="text-lg mt-2 px-10 py-2 bg-[#497EF7] text-white rounded-lg">
                            {t('choose_plan')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default HostingPricingTable;
