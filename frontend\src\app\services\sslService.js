import apiService from '../lib/apiService';

const sslService = {
    // Generate a new CSR with provided information
    generateCSR: (data) => apiService.post('/ssl/generate-csr', data, { withCredentials: true }),

    // Submit CSR for a specific certificate in a suborder
    submitCSR: (subOrderId, data) => apiService.post(`/ssl/submit-csr/${subOrderId}`, data, { withCredentials: true }),

    // Verify if CSR domain matches the provided domain
    verifyCSR: (data) => apiService.post('/ssl/verify-csr', data, { withCredentials: true }),

    // Get validation emails for a domain
    getValidationEmails: (domain) => apiService.get(`/ssl/validation-emails?domain=${encodeURIComponent(domain)}`, { withCredentials: true }),

    // Update SSL certificate status
    updateCertificateStatus: (subOrderId, certificateIndex, data) => apiService.put(`/ssl/ssl-certificate/${subOrderId}/${certificateIndex}`, data, { withCredentials: true }),
};

export default sslService;