/* Modern Toggle Switch Styles */
.toggle-checkbox {
  left: 0;
  z-index: 5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-checkbox:checked {
  left: calc(100% - 24px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.toggle-label {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.toggle-label::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  border-radius: 9999px;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.toggle-checkbox:focus + .toggle-label::before {
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.toggle-checkbox:focus {
  outline: none;
}

.toggle-label:hover {
  opacity: 0.9;
  transform: scale(1.02);
}

/* Smooth animations for disabled states */
.toggle-checkbox:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-checkbox:disabled + .toggle-label {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Custom scrollbar for textarea */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced focus states for better accessibility */
input:focus,
textarea:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth transitions for all interactive elements */
button,
input,
textarea {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading state animations */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.loading-pulse {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
