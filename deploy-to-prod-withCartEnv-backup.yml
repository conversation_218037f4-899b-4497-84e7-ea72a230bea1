name: Deploy ztechEngineering to PROD WITH CART Environment

on:
  push:
    branches: [prodwithcart]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"
          cache-dependency-path: "**/package-lock.json"

      - name: Install frontend modules
        run: |
          cd frontend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Build the frontend
        run: |
          cd frontend
          npm run build
          sync # Ensure file writes are complete
          ls -la .next # Debug: Verify build output

      - name: Install backend modules
        run: |
          cd backend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Create frontend deployment package
        run: |
          cd frontend
          tar -czf ../frontend.tar.gz .next package.json package-lock.json .env.local
          cd ..
          ls -lh frontend.tar.gz # Debug: Verify package size

      - name: Verify frontend deployment package
        run: |
          if [ ! -f frontend.tar.gz ]; then
            echo "ERROR: frontend.tar.gz was not created!"
            exit 1
          fi

      - name: Debug backend files before packaging
        run: |
          cd backend
          ls -la # Debug: List files before creating tar
          cd ..

      - name: Create backend deployment package
        run: |
          cd backend
          tar -czf ../backend.tar.gz --exclude='node_modules' .
          cd ..
          ls -lh backend.tar.gz # Debug: Verify package size

      - name: Verify backend deployment package
        run: |
          if [ ! -f backend.tar.gz ]; then
            echo "ERROR: backend.tar.gz was not created!"
            exit 1
          fi

      - name: Install lftp
        run: sudo apt-get update && sudo apt-get install -y lftp

      - name: Transfer frontend deployment package to server via FTP
        env:
          FTP_HOST: ${{ secrets.SSH_HOST }}
          FTP_USER: ${{ secrets.SSH_USER }}
          FTP_PASS: ${{ secrets.ROOT_PASS }}
        run: |
          lftp -u $FTP_USER,$FTP_PASS $FTP_HOST <<EOF
          set ssl:verify-certificate no
          pwd
          ls -la
          put frontend.tar.gz
          bye
          EOF

      - name: Transfer backend deployment package to server via FTP
        env:
          FTP_HOST: ${{ secrets.SSH_HOST }}
          FTP_USER: ${{ secrets.SSH_USER }}
          FTP_PASS: ${{ secrets.ROOT_PASS }}
        run: |
          lftp -u $FTP_USER,$FTP_PASS $FTP_HOST <<EOF
          set ssl:verify-certificate no
          put backend.tar.gz
          bye
          EOF

  deploy:
    needs: build
    runs-on: ["self-hosted", "ztech-nextjs"]

    steps:
      - name: Debug deploy environment
        run: |
          pwd
          ls -la

      - name: Change ownership to eziane before cleanup
        run: |
          sudo chown -R eziane:ztechengineering .

      - name: Clean workspace except deployment packages
        run: |
          find . -mindepth 1 ! -name 'frontend.tar.gz' ! -name 'backend.tar.gz' -exec rm -rf {} +

      - name: Verify frontend deployment package presence
        run: |
          if [ ! -f frontend.tar.gz ]; then
            echo "ERROR: frontend.tar.gz not found in $(pwd)!"
            exit 1
          fi
          ls -lh frontend.tar.gz

      - name: Extract frontend deployment package
        run: |
          tar -xzf frontend.tar.gz -C .
          rm frontend.tar.gz
          mv frontend frontend_temp
          rm -rf frontend
          mv frontend_temp/frontend frontend
          rm -rf frontend_temp
          ls -la frontend

      - name: Verify backend deployment package presence
        run: |
          if [ ! -f backend.tar.gz ]; then
            echo "ERROR: backend.tar.gz not found in $(pwd)!"
            exit 1
          fi
          ls -lh backend.tar.gz

      - name: Extract backend deployment package
        run: |
          tar -xzf backend.tar.gz -C .
          rm backend.tar.gz
          mv backend backend_temp
          rm -rf backend
          mv backend_temp/backend backend
          rm -rf backend_temp
          ls -la backend

      - name: Write .env for backend
        run: |
          cd backend
          echo "${{ secrets.PROD_ENV_FILE_CONTENT_08_05_2025}}" > .env
          chmod 600 .env # Secure permissions
          ls -la .env # Debug: Verify .env file

      - name: Install backend dependencies
        run: |
          cd backend
          npm ci
          npm list # Debug: Show installed dependencies

      - name: Start frontend with pm2 (port 3001)
        run: |
          cd frontend
          pm2 delete ztech-frontend || true
          pm2 start npm --name ztech-frontend -- start -- --port=3001
          pm2 list # Debug: Show pm2 processes

      - name: Start backend with pm2 (port 5002)
        run: |
          cd backend
          pm2 delete ztech-backend || true
          pm2 start npm --name ztech-backend -- start -- --port=5002
          pm2 save
          pm2 list # Debug: Show pm2 processes
