const Cart = require("../models/Cart");
const Package = require("../models/Package"); // Assuming this is the model for the package

exports.mergeGuestCartToUserCart = async (guestUserId, userId) => {
  try {
    const guestCart = await Cart.findOne({ user: guestUserId });
    const userCart = await Cart.findOne({ user: userId });

    if (!guestCart) {
      console.log("No guest cart found");
      return;
    }

    if (!userCart) {
      console.log("No user cart found, assigning guest cart");
      guestCart.user = userId;
      await guestCart.save();
    } else {
      console.log("Merging guest cart into user cart");
      for (const guestItem of guestCart.items) {
        // If the guestItem.package is just an ID, fetch the corresponding Package
        const package = await Package.findById(guestItem.package);
        if (!package) {
          console.log("Package not found:", guestItem.package);
          continue; // Skip this item if the package is not found
        }

        await userCart.addItem(
          package, // Use the actual package object
          guestItem.quantity,
          guestItem.period
        );
      }
      await Cart.findByIdAndDelete(guestCart._id);
    }

    console.log("Guest cart merged successfully");
  } catch (error) {
    console.error("Error merging guest cart:", error);
  }
};
