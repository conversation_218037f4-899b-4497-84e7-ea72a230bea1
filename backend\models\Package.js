const mongoose = require('mongoose');
const ProductStatus = require('../constants/enums/poduct-status');
const Schema = mongoose.Schema;

const packageSchema = new Schema({
  reference: { type: String, unique: true, required: true },
  name: { type: String, required: true, maxlength: 255 },
  name_fr: { type: String, maxlength: 255 },
  quantity: { type: Number, default: 1 },
  description: { type: String, required: true, maxlength: 100 },
  description_fr: { type: String, maxlength: 100 },
  price: { type: Number, required: true },
  regularPrice: { type: Number },
  discounts: [{
    period: {
      type: Number,
      // enum: [1, 12, 24, 36],
      required: true
    },
    percentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    }
  }],
  image: { type: String },
  category: { type: Schema.Types.ObjectId, ref: 'Category', required: true },
  brand: { type: Schema.Types.ObjectId, ref: 'Brand', required: true },
  specifications: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Specification' }],

  // VPS-specific fields for provider integration
  vpsConfig: {
    provider: {
      type: String,
      enum: ['contabo', 'digitalocean', 'vultr', 'linode'],
      default: null // Only set for VPS packages
    },
    providerProductId: {
      type: String,
      default: null // Contabo product ID like "V91", "V92", etc. (default/primary option)
    },
    providerPlanName: {
      type: String,
      default: null // Provider's plan name for reference
    },
    // Storage type variants for the same VPS plan
    storageTypes: [{
      productId: {
        type: String,
        required: true // Contabo product ID for this storage variant
      },
      storageDescription: {
        type: String,
        required: true // e.g., "75 GB NVMe", "150 GB SSD", "300 GB Storage"
      },
      isDefault: {
        type: Boolean,
        default: false // Mark which storage type is the default
      },
      additionalPrice: {
        type: Number,
        default: 0 // Additional cost for this storage type (in MAD)
      }
    }]
  },

  status: {
    type: String,
    required: true,
    enum: [
      ProductStatus.PUBLISHED,
      ProductStatus.DRAFT,
    ],
    default: ProductStatus.DRAFT,
  },
  sslType: {
    type: String,
    enum: ['DV', 'OV', 'EV', 'Wildcard', 'Multi-Domain'],
    // default: "TYPE",
  }
}, { timestamps: true });

const Package = mongoose.model('Package', packageSchema);
module.exports = Package;
