'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import jobService from '@/app/services/jobService';
import { toast } from 'react-toastify';
import { ChevronLeft, CheckCircle, X } from 'lucide-react';
import { useAuth } from '@/app/context/AuthContext';

export default function JobDetailsPage({ params }) {
  const t = useTranslations('jobs');
  const router = useRouter();
  const { id, locale } = params;
  const { user, isAuthenticated } = useAuth();

  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userApplication, setUserApplication] = useState(null);
  const [applicationLoading, setApplicationLoading] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    coverLetter: null,
    resume: null
  });
  const [submitting, setSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState({
    coverLetter: false,
    resume: false
  });

  // Fetch user's application for this job
  const fetchUserApplication = async () => {
    if (!isAuthenticated || !user || !id) return;

    try {
      setApplicationLoading(true);
      const response = await jobService.getUserApplication(id);
      setUserApplication(response.data);
    } catch (error) {
      // No application found is not an error
      if (error.response?.status !== 404) {
        console.error('Error fetching user application:', error);
      }
    } finally {
      setApplicationLoading(false);
    }
  };

  useEffect(() => {
    const fetchJob = async () => {
      try {
        setLoading(true);
        const response = await jobService.getJobById(id);
        setJob(response.data);
      } catch (error) {
        console.error('Error fetching job details:', error);
        toast.error(t('error_loading_job'));
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchJob();
    }
  }, [id, t]);

  // Fetch user application when authenticated
  useEffect(() => {
    if (user && user.authenticated !== false && id) {
      fetchUserApplication();
    }
  }, [user, id]);

  // Pre-fill form with user data if authenticated
  useEffect(() => {
    console.log('Authentication status:', {
      hasUser: !!user,
      userAuthenticated: user?.authenticated,
      userState: user?.state,
      userEmail: user?.email,
      isGuestUser: user?.authenticated === false
    });
    if (user && user.authenticated !== false) {
      setFormData(prevData => ({
        ...prevData,
        fullName: user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.firstName || '',
        email: user.email || '',
        phone: user.phone || ''
      }));
    }
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle drag events
  const handleDrag = (e, fieldName, active) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(prev => ({ ...prev, [fieldName]: active }));
    } else if (e.type === 'dragleave') {
      setDragActive(prev => ({ ...prev, [fieldName]: false }));
    }
  };

  // Handle dropped files
  const handleDrop = (e, fieldName) => {
    e.preventDefault();
    e.stopPropagation();

    setDragActive(prev => ({ ...prev, [fieldName]: false }));

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      // Check file type
      const file = e.dataTransfer.files[0];
      const validFileTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      // Check file extension as a fallback
      const fileName = file.name.toLowerCase();
      const validExtensions = ['.pdf', '.doc', '.docx'];
      const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext));

      if (validFileTypes.includes(file.type) || hasValidExtension) {
        // Check file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          toast.error(
            fieldName === 'resume'
              ? (t('resume_too_large') || 'Resume file is too large (max 5MB)')
              : (t('cover_letter_too_large') || 'Cover letter file is too large (max 5MB)')
          );
          return;
        }

        setFormData({
          ...formData,
          [fieldName]: file
        });

        // Show success message
        if (fieldName === 'resume') {
          toast.success(t('resume_uploaded') || 'Resume uploaded successfully');
        } else if (fieldName === 'coverLetter') {
          toast.success(t('cover_letter_uploaded') || 'Cover letter uploaded successfully');
        }
      } else {
        // Show error for invalid file type
        toast.error(
          fieldName === 'resume'
            ? (t('invalid_resume_format') || 'Invalid resume format. Please use PDF, DOC, or DOCX')
            : (t('invalid_cover_letter_format') || 'Invalid cover letter format. Please use PDF, DOC, or DOCX')
        );
      }
    }
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      const file = files[0];

      // Check file type
      const validFileTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      // Check file extension as a fallback
      const fileName = file.name.toLowerCase();
      const validExtensions = ['.pdf', '.doc', '.docx'];
      const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext));

      if (validFileTypes.includes(file.type) || hasValidExtension) {
        // Check file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          toast.error(
            name === 'resume'
              ? (t('resume_too_large') || 'Resume file is too large (max 5MB)')
              : (t('cover_letter_too_large') || 'Cover letter file is too large (max 5MB)')
          );
          return;
        }

        setFormData({
          ...formData,
          [name]: file
        });

        // Show success message
        if (name === 'resume') {
          toast.success(t('resume_uploaded') || 'Resume uploaded successfully');
        } else if (name === 'coverLetter') {
          toast.success(t('cover_letter_uploaded') || 'Cover letter uploaded successfully');
        }
      } else {
        // Show error for invalid file type
        toast.error(
          name === 'resume'
            ? (t('invalid_resume_format') || 'Invalid resume format. Please use PDF, DOC, or DOCX')
            : (t('invalid_cover_letter_format') || 'Invalid cover letter format. Please use PDF, DOC, or DOCX')
        );

        // Reset the file input
        e.target.value = '';
      }
    }
  };

  // Validate form data
  const validateForm = () => {
    // Check required fields
    if (!formData.fullName || !formData.fullName.trim()) {
      toast.error(t('name_required') || 'Full name is required');
      return false;
    }

    if (!formData.email || !formData.email.trim()) {
      toast.error(t('email_required') || 'Email is required');
      return false;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error(t('invalid_email_format') || 'Invalid email format');
      return false;
    }

    // Validate phone (required in this form)
    if (!formData.phone || !formData.phone.trim()) {
      toast.error(t('phone_required') || 'Phone number is required');
      return false;
    }

    // Validate phone format
    const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
    if (!phoneRegex.test(formData.phone)) {
      toast.error(t('invalid_phone_format') || 'Invalid phone format');
      return false;
    }

    // Check resume
    if (!formData.resume) {
      toast.error(t('resume_required') || 'Resume is required');
      return false;
    }

    // Validate file types
    const validFileTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!validFileTypes.includes(formData.resume.type)) {
      toast.error(t('invalid_resume_format') || 'Invalid resume format. Please use PDF, DOC, or DOCX');
      return false;
    }

    // Validate cover letter if provided
    if (formData.coverLetter && !validFileTypes.includes(formData.coverLetter.type)) {
      toast.error(t('invalid_cover_letter_format') || 'Invalid cover letter format. Please use PDF, DOC, or DOCX');
      return false;
    }

    // Validate file sizes (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (formData.resume.size > maxSize) {
      toast.error(t('resume_too_large') || 'Resume file is too large (max 5MB)');
      return false;
    }

    if (formData.coverLetter && formData.coverLetter.size > maxSize) {
      toast.error(t('cover_letter_too_large') || 'Cover letter file is too large (max 5MB)');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check if user is authenticated (guest users have authenticated: false, real users don't have this property)
    if (!user || user.authenticated === false) {
      setShowLoginPrompt(true);
      return;
    }

    // Validate form data
    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);

      // Create FormData object for file upload
      const applicationData = new FormData();
      applicationData.append('fullName', formData.fullName.trim());
      applicationData.append('email', formData.email.trim());
      applicationData.append('phone', formData.phone.trim());

      if (formData.coverLetter) {
        applicationData.append('coverLetter', formData.coverLetter);
      }

      applicationData.append('resume', formData.resume);
      applicationData.append('jobId', id);

      // Add application date
      applicationData.append('appliedAt', new Date().toISOString());

      await jobService.applyForJob(id, applicationData);

      toast.success(t('application_submitted'));

      // Refresh user application status
      await fetchUserApplication();

      // Reset form
      setFormData({
        fullName: '',
        email: '',
        phone: '',
        coverLetter: null,
        resume: null
      });

    } catch (error) {
      console.error('Error submitting application:', error);
      toast.error(t('error_submitting_application'));
    } finally {
      setSubmitting(false);
    }
  };

  const goBack = () => {
    router.push(`/${locale}/jobs`);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('job_not_found')}</h1>
          <button
            onClick={goBack}
            className="flex items-center text-blue-600 hover:text-blue-800"
          >
            <ChevronLeft size={20} />
            <span>{t('back_to_jobs')}</span>
          </button>
        </div>
      </div>
    );
  }

  // Get localized content with fallback to English
  const jobTitle = typeof job.title === 'object' ? (job.title[locale] || job.title.en || '') : job.title || '';
  const jobDescription = typeof job.description === 'object' ? (job.description[locale] || job.description.en || '') : job.description || '';

  // Get localized requirements with fallback to English
  const localizedRequirements = job.requirements ? job.requirements.map(req => {
    if (typeof req === 'object') {
      return req[locale] || req.en || '';
    }
    return req || '';
  }) : [];

  // Get localized responsibilities with fallback to English
  const localizedResponsibilities = job.responsibilities ? job.responsibilities.map(resp => {
    if (typeof resp === 'object') {
      return resp[locale] || resp.en || '';
    }
    return resp || '';
  }) : [];

  const localizedBenefits = job.benefits ? job.benefits.map(benefit => {
    if (typeof benefit === 'object') {
      return benefit[locale] || benefit.en || '';
    }
    return benefit || '';
  }) : [];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back button */}
      <button
        onClick={goBack}
        className="flex items-center text-blue-600 hover:text-blue-800 mb-6"
      >
        <ChevronLeft size={20} />
        <span>{t('back_to_jobs')}</span>
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Job Details */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{jobTitle}</h1>

            <div className="flex flex-wrap gap-3 mb-6">
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">{job.department}</span>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">{job.location}</span>
              <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">{job.type}</span>
              {job.experience && (
                <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">{job.experience}</span>
              )}
            </div>

            {job.salary_range && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h2 className="text-lg font-semibold text-gray-800 mb-2">{t('salary_range')}</h2>
                <p className="text-gray-700">
                  {job.salary_range.min} - {job.salary_range.max} {job.salary_range.currency}
                </p>
              </div>
            )}

            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-3">{t('job_description')}</h2>
              <p className="text-gray-700 whitespace-pre-line">{jobDescription}</p>
            </div>

            {localizedRequirements.length > 0 && (
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-3">{t('requirements')}</h2>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  {localizedRequirements.map((req, index) => (
                    <li key={index}>{req}</li>
                  ))}
                </ul>
              </div>
            )}

            {localizedResponsibilities.length > 0 && (
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-3">{t('responsibilities')}</h2>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  {localizedResponsibilities.map((resp, index) => (
                    <li key={index}>{resp}</li>
                  ))}
                </ul>
              </div>
            )}

            {localizedBenefits.length > 0 && (
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-3">{t('benefits')}</h2>
                <ul className="list-disc list-inside text-gray-700 space-y-2">
                  {localizedBenefits.map((benefit, index) => (
                    <li key={index}>{benefit}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Application Form */}
        <div className="lg:col-span-1">
          {/* Show application status if user has already applied */}
          {user && user.authenticated !== false && userApplication ? (
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8 mb-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">{t('application_status')}</h2>

              <div className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-green-800 font-medium">{t('application_submitted')}</span>
                  </div>
                  <p className="text-green-700 text-sm mt-1">
                    {t('applied_on')}: {new Date(userApplication.appliedAt).toLocaleDateString()}
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-700">{t('current_status')}:</span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      userApplication.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      userApplication.status === 'reviewed' ? 'bg-blue-100 text-blue-800' :
                      userApplication.status === 'interviewed' ? 'bg-purple-100 text-purple-800' :
                      userApplication.status === 'accepted' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {t(userApplication.status)}
                    </span>
                  </div>

                  {userApplication.interviews && userApplication.interviews.length > 0 && (
                    <div className="border-t pt-3">
                      <h4 className="font-medium text-gray-800 mb-2">{t('interviews')}</h4>
                      {userApplication.interviews.map((interview, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg mb-2">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="text-sm font-medium text-gray-800">
                                {new Date(interview.scheduledAt).toLocaleDateString()} at {new Date(interview.scheduledAt).toLocaleTimeString()}
                              </p>
                              <p className="text-xs text-gray-600">
                                Duration: {interview.duration} minutes
                              </p>
                              {interview.meetLink && (
                                <a
                                  href={interview.meetLink}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                                >
                                  {t('join_meeting')}
                                </a>
                              )}
                            </div>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              interview.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                              interview.status === 'completed' ? 'bg-green-100 text-green-800' :
                              interview.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {t(interview.status)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : user && user.authenticated !== false ? (
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">{t('apply_now')}</h2>

            {/* Application Rules */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-md font-semibold text-gray-800 mb-2">{t('application_rules')}</h3>
              <ul className="text-sm text-gray-700 space-y-2 list-disc list-inside">
                <li>{t('rule_complete_profile')}</li>
                <li>{t('rule_accurate_info')}</li>
                <li>{t('rule_file_format')}</li>
                <li>{t('rule_response_time')}</li>
                <li>{t('rule_one_application')}</li>
              </ul>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="fullName" className="block text-gray-700 font-medium mb-2">
                  {t('full_name')} *
                </label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                  {t('email')} *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                  {t('phone')} *
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="coverLetter" className="block text-gray-700 font-medium mb-2">
                  {t('cover_letter')}
                </label>
                <div
                  className={`mt-2 flex justify-center rounded-lg border border-dashed ${dragActive.coverLetter ? 'border-blue-500 bg-blue-50' : 'border-gray-900/25'} px-6 py-10`}
                  onDragEnter={(e) => handleDrag(e, 'coverLetter', true)}
                  onDragOver={(e) => handleDrag(e, 'coverLetter', true)}
                  onDragLeave={(e) => handleDrag(e, 'coverLetter', false)}
                  onDrop={(e) => handleDrop(e, 'coverLetter')}
                >
                  <div className="text-center">
                    <svg className="mx-auto h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12l-3-3m0 0l-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                    </svg>
                    <div className="mt-4 flex text-sm leading-6 text-gray-600">
                      <label htmlFor="coverLetter" className="relative cursor-pointer rounded-md bg-white font-semibold text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500">
                        <span>{t('upload_cover_letter')}</span>
                        <input
                          id="coverLetter"
                          name="coverLetter"
                          type="file"
                          className="sr-only"
                          onChange={handleFileChange}
                          accept=".pdf,.doc,.docx"
                        />
                      </label>
                      <p className="pl-1">{t('or_drag_and_drop')}</p>
                    </div>
                    <p className="text-xs leading-5 text-gray-600">{t('accepted_file_types')}: PDF, DOC, DOCX</p>
                    {formData.coverLetter && (
                      <p className="mt-2 text-sm text-green-600">
                        {formData.coverLetter.name}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="resume" className="block text-gray-700 font-medium mb-2">
                  {t('resume')} *
                </label>
                <div
                  className={`mt-2 flex justify-center rounded-lg border border-dashed ${dragActive.resume ? 'border-blue-500 bg-blue-50' : 'border-gray-900/25'} px-6 py-10`}
                  onDragEnter={(e) => handleDrag(e, 'resume', true)}
                  onDragOver={(e) => handleDrag(e, 'resume', true)}
                  onDragLeave={(e) => handleDrag(e, 'resume', false)}
                  onDrop={(e) => handleDrop(e, 'resume')}
                >
                  <div className="text-center">
                    <svg className="mx-auto h-12 w-12 text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12l-3-3m0 0l-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                    </svg>
                    <div className="mt-4 flex text-sm leading-6 text-gray-600">
                      <label htmlFor="resume" className="relative cursor-pointer rounded-md bg-white font-semibold text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500">
                        <span>{t('upload_resume')}</span>
                        <input
                          id="resume"
                          name="resume"
                          type="file"
                          className="sr-only"
                          onChange={handleFileChange}
                          accept=".pdf,.doc,.docx"
                        />
                      </label>
                      <p className="pl-1">{t('or_drag_and_drop')}</p>
                    </div>
                    <p className="text-xs leading-5 text-gray-600">{t('accepted_file_types')}: PDF, DOC, DOCX</p>
                    {formData.resume && (
                      <p className="mt-2 text-sm text-green-600">
                        {formData.resume.name}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={submitting}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 disabled:bg-blue-400"
              >
                {submitting ? t('submitting') : t('submit_application')}
              </button>
            </form>
          </div>
          ) : (
            /* Simple professional authentication required message */
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8 border border-gray-200">
              <div className="text-center">
                {/* Simple Lock Icon */}
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                  <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>

                {/* Main Message */}
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {t('login_required')}
                </h3>
                <p className="text-gray-600 mb-6">
                  {t('login_required_message')}
                </p>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={() => router.push(`/${locale}/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                  >
                    {t('sign_in')}
                  </button>

                  <button
                    onClick={() => router.push(`/${locale}/auth/register?redirect=${encodeURIComponent(window.location.pathname)}`)}
                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                  >
                    {t('create_account')}
                  </button>
                </div>

                {/* Security Note */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    {t('secure_login_process')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Login Prompt Modal */}
      {showLoginPrompt && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">{t('login_required')}</h3>
                <button
                  onClick={() => setShowLoginPrompt(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="mb-4">
                <p className="text-gray-700 mb-4">
                  {t('login_required_message')}
                </p>

                <div className="space-y-3">
                  <button
                    onClick={() => router.push(`/${locale}/auth/login?redirect=${encodeURIComponent(window.location.pathname)}`)}
                    className="w-full px-4 py-2 text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {t('login')}
                  </button>

                  <button
                    onClick={() => router.push(`/${locale}/auth/register?redirect=${encodeURIComponent(window.location.pathname)}`)}
                    className="w-full px-4 py-2 text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {t('create_account')}
                  </button>

                  <button
                    onClick={() => setShowLoginPrompt(false)}
                    className="w-full px-4 py-2 text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    {t('cancel')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
