"use client";
import React, { useState, useEffect, useCallback } from "react";
import { adminService } from "../../services/adminService";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  <PERSON>ton,
  Spinner,
  Dialog,
  DialogHeader,
  DialogBody,
  DialogFooter,
  Checkbox,
} from "@material-tailwind/react";
import {
  MessageSquare,
  Clock,
  Trash2,
  Alert<PERSON>riangle,
  Settings,
} from "lucide-react";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import ChatbotContextModal from "../../../components/admin/ChatbotContextModal";

export default function ChatConversations() {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedConversations, setSelectedConversations] = useState([]);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleteType, setDeleteType] = useState(""); // 'all' or 'selected'
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showContextModal, setShowContextModal] = useState(false);
  const router = useRouter();

  const fetchConversations = useCallback(async () => {
    try {
      setLoading(true);

      // Simple parameters with just pagination
      const params = {
        page: currentPage,
        limit: 10,
      };

      const response = await adminService.getAllChatConversations(params);

      setConversations(response.data.data || []);
      setTotalPages(response.data.pagination?.totalPages || 1);
      // Clear selections when fetching new data
      setSelectedConversations([]);
    } catch (error) {
      toast.error("Failed to fetch chat conversations");
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [currentPage]);

  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  const handleViewConversation = (id) => {
    router.push(`/admin/chats/${id}`);
  };

  const toggleConversationSelection = (id, event) => {
    event.stopPropagation();

    if (selectedConversations.includes(id)) {
      setSelectedConversations((prev) =>
        prev.filter((convId) => convId !== id)
      );
    } else {
      setSelectedConversations((prev) => [...prev, id]);
    }
  };

  const toggleSelectAll = () => {
    if (selectedConversations.length === conversations.length) {
      setSelectedConversations([]);
    } else {
      setSelectedConversations(conversations.map((conv) => conv._id));
    }
  };

  const openDeleteConfirmation = (type) => {
    if (type === "selected" && selectedConversations.length === 0) {
      toast.warning("Please select at least one conversation to delete");
      return;
    }

    setDeleteType(type);
    setShowDeleteConfirmation(true);
  };

  const closeDeleteConfirmation = () => {
    setShowDeleteConfirmation(false);
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);

      if (deleteType === "all") {
        await adminService.deleteAllChatConversations();
        toast.success("All conversations deleted successfully");
      } else if (deleteType === "selected") {
        await adminService.deleteMultipleChatConversations(
          selectedConversations
        );
        toast.success(
          `${selectedConversations.length} conversations deleted successfully`
        );
      }

      // Refresh the conversation list
      fetchConversations();
    } catch (error) {
      toast.error("Failed to delete conversations");
      console.error(error);
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirmation(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Typography variant="h4" color="blue-gray" className="font-bold">
          Chat Conversations
        </Typography>

        <Button
          variant="text"
          color="blue-gray"
          className="flex items-center gap-2 p-2"
          onClick={() => setShowContextModal(true)}
        >
          <Settings className="h-5 w-5" />
          <span className="hidden sm:inline">Settings</span>
        </Button>
      </div>

      {/* Chatbot Context Modal */}
      <ChatbotContextModal
        open={showContextModal}
        onClose={() => setShowContextModal(false)}
      />

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Spinner className="h-12 w-12" color="blue" />
        </div>
      ) : (
        <Card className="bg-white shadow-lg rounded-lg">
          <CardBody className="p-0">
            {conversations.length > 0 ? (
              <>
                <div className="p-4 border-b border-gray-100 flex items-center justify-between">
                  <div className="flex items-center">
                    <Checkbox
                      id="select-all"
                      checked={
                        selectedConversations.length === conversations.length &&
                        conversations.length > 0
                      }
                      onChange={toggleSelectAll}
                      label={`Select All (${selectedConversations.length}/${conversations.length})`}
                    />
                  </div>
                  <Button
                    variant="outlined"
                    color="red"
                    size="sm"
                    className="flex items-center gap-2"
                    onClick={() =>
                      openDeleteConfirmation(
                        selectedConversations.length > 0 ? "selected" : "all"
                      )
                    }
                  >
                    <Trash2 className="h-4 w-4" />
                    {selectedConversations.length > 0
                      ? `Delete selected (${selectedConversations.length})`
                      : "Delete all"}
                  </Button>
                </div>
                <div className="divide-y divide-gray-100">
                  {conversations.map((conversation) => (
                    <div
                      key={conversation._id}
                      className="p-4 hover:bg-gray-50 transition-colors duration-200 ease-in-out cursor-pointer"
                      onClick={() => handleViewConversation(conversation._id)}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-4">
                          <Checkbox
                            id={`select-${conversation._id}`}
                            checked={selectedConversations.includes(
                              conversation._id
                            )}
                            onChange={(e) =>
                              toggleConversationSelection(conversation._id, e)
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <MessageSquare className="h-5 w-5 text-blue-500" />
                            </div>
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <Typography
                                variant="h6"
                                className="text-sm font-semibold text-gray-900"
                              >
                                Session:{" "}
                                {conversation.sessionId.substring(0, 12)}...
                              </Typography>
                              {conversation.userInfo &&
                              conversation.userInfo.userId ? (
                                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                  {conversation.userInfo.userId.firstName}{" "}
                                  {conversation.userInfo.userId.lastName || ""}
                                </span>
                              ) : (
                                <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                  Guest
                                </span>
                              )}
                            </div>
                            <Typography className="text-sm text-gray-600 line-clamp-1 mt-1">
                              {conversation.messages[
                                conversation.messages.length - 1
                              ]
                                ? // Strip HTML tags and truncate to first 8 words
                                  conversation.messages[
                                    conversation.messages.length - 1
                                  ].content
                                    .replace(/<[^>]*>/g, "")
                                    .split(" ")
                                    .slice(0, 8)
                                    .join(" ") +
                                  (conversation.messages[
                                    conversation.messages.length - 1
                                  ].content.split(" ").length > 8
                                    ? "..."
                                    : "")
                                : "No messages"}
                            </Typography>
                            <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>
                                  {new Date(
                                    conversation.lastActivity
                                  ).toLocaleString()}
                                </span>
                              </div>
                              <div className="flex items-center gap-1">
                                <MessageSquare className="h-3 w-3" />
                                <span>
                                  {conversation.messages.length} messages
                                </span>
                              </div>
                              {conversation.userInfo &&
                                conversation.userInfo.userId && (
                                  <div className="flex items-center gap-1 text-blue-500">
                                    <span>
                                      ID:{" "}
                                      {conversation.userInfo.userId
                                        .identifiant ||
                                        conversation.userInfo.userId._id.substring(
                                          0,
                                          8
                                        )}
                                    </span>
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="outlined"
                          color="blue"
                          size="sm"
                          className="text-xs py-2"
                        >
                          VIEW DETAILS
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <MessageSquare className="h-16 w-16 text-gray-300 mb-4" />
                <Typography variant="h5" color="gray" className="font-normal">
                  No chat conversations found
                </Typography>
                <Typography color="gray" className="mt-2 text-center max-w-md">
                  There are no chat conversations in the system yet. They will
                  appear here once users start chatting with the assistant.
                </Typography>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outlined"
              color="blue"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            >
              Previous
            </Button>
            <div className="flex items-center px-4 font-medium">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outlined"
              color="blue"
              disabled={currentPage === totalPages}
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteConfirmation}
        handler={closeDeleteConfirmation}
        size="xs"
      >
        <DialogHeader className="flex items-center justify-center text-center border-b pb-4">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-red-50 rounded-full flex items-center justify-center mb-2">
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
            <Typography variant="h5">Confirm Deletion</Typography>
          </div>
        </DialogHeader>
        <DialogBody className="text-center py-6">
          {deleteType === "all" ? (
            <Typography color="gray" className="font-normal">
              Are you sure you want to delete all chat conversations? This
              action cannot be undone.
            </Typography>
          ) : (
            <Typography color="gray" className="font-normal">
              Are you sure you want to delete {selectedConversations.length}{" "}
              selected conversation(s)? This action cannot be undone.
            </Typography>
          )}
        </DialogBody>
        <DialogFooter className="flex justify-center gap-3 border-t pt-4">
          <Button
            variant="outlined"
            color="gray"
            onClick={closeDeleteConfirmation}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            variant="filled"
            color="red"
            onClick={handleDelete}
            disabled={deleteLoading}
            className="flex items-center justify-center gap-2 flex-1"
          >
            {deleteLoading ? (
              <>
                <Spinner className="h-4 w-4" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete
              </>
            )}
          </Button>
        </DialogFooter>
      </Dialog>
    </div>
  );
}
