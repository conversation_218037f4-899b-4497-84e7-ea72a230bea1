import React from 'react';

export const VCPUsvg = () => (
    <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path opacity="0.5" d="M12.1392 16.627C12.1392 14.2756 12.1392 13.0999 12.8697 12.3694C13.6001 11.6389 14.7759 11.6389 17.1273 11.6389H23.7781C26.1294 11.6389 27.3051 11.6389 28.0357 12.3694C28.7662 13.0999 28.7662 14.2756 28.7662 16.627V23.2778C28.7662 25.6292 28.7662 26.8048 28.0357 27.5354C27.3051 28.2659 26.1294 28.2659 23.7781 28.2659H17.1273C14.7759 28.2659 13.6001 28.2659 12.8697 27.5354C12.1392 26.8048 12.1392 25.6292 12.1392 23.2778V16.627Z" stroke="#5956E9" strokeWidth="2.49405" />
        <path d="M7.15088 19.9524C7.15088 13.6819 7.15088 10.5467 9.09885 8.59874C11.0468 6.65077 14.182 6.65077 20.4525 6.65077C26.7228 6.65077 29.8582 6.65077 31.8061 8.59874C33.7541 10.5467 33.7541 13.6819 33.7541 19.9524C33.7541 26.2227 33.7541 29.3581 31.8061 31.3059C29.8582 33.254 26.7228 33.254 20.4525 33.254C14.182 33.254 11.0468 33.254 9.09885 31.3059C7.15088 29.3581 7.15088 26.2227 7.15088 19.9524Z" stroke="#5956E9" strokeWidth="2.49405" />
        <path opacity="0.5" d="M7.15059 19.9524H3.8252" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M37.0793 19.9524H33.7539" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M7.15059 14.9643H3.8252" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M37.0793 14.9643H33.7539" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M7.15059 24.9405H3.8252" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M37.0793 24.9405H33.7539" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M20.4524 33.254V36.5794" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M20.4524 3.32542V6.65082" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M15.4644 33.254V36.5794" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M15.4644 3.32542V6.65082" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M25.4404 33.254V36.5794" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
        <path opacity="0.5" d="M25.4404 3.32542V6.65082" stroke="#5956E9" strokeWidth="2.49405" strokeLinecap="round" />
    </svg>
);
export const RAMsvg = () => (
    <svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5.89575 12.2292V28.8958C5.89575 33.8958 8.34575 37.2292 14.2291 37.2292H25.8958C31.6791 37.2292 34.2291 33.8958 34.2291 28.8958V26.9458C34.2291 26.0625 33.8791 25.2125 33.2457 24.5958L31.8624 23.2125C31.2291 22.5792 30.8791 21.7458 30.8791 20.8625V17.2292C30.8791 16.3125 31.6291 15.5625 32.5457 15.5625C33.4624 15.5625 34.2124 14.8125 34.2124 13.8958V12.2292C34.2124 7.22916 31.6624 3.89583 25.8791 3.89583H14.2124C8.34575 3.89583 5.89575 7.22916 5.89575 12.2292Z" stroke="#5956E9" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
        <path opacity="0.4" d="M13.3799 37.1792L13.3633 32.2292C13.3633 30.3792 14.8466 28.8958 16.6966 28.8958H23.38C25.2133 28.8958 26.6967 30.3792 26.7133 32.2125L26.7633 37.1958" stroke="#5956E9" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
        <path opacity="0.4" d="M14.9634 8.81259L15.0467 3.99593" stroke="#5956E9" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
        <path opacity="0.4" d="M19.9631 8.81259L20.0465 3.99593" stroke="#5956E9" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
        <path opacity="0.4" d="M24.9631 8.76257L25.0465 3.96257" stroke="#5956E9" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

);

export const SSDDISKsvg = () => (
    <svg width="41" height="42" viewBox="0 0 41 42" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M36.3836 26.8571C35.3748 24.9562 33.4523 23.672 31.2465 23.672H9.37485C7.16906 23.672 5.24654 24.9562 4.23777 26.8571M36.3836 26.8571C36.8621 27.7587 37.135 28.7991 37.135 29.907C37.135 33.3504 34.4986 36.1419 31.2465 36.1419H20.3107M36.3836 26.8571L34.2357 18.139M4.23777 26.8571L8.53363 9.4209C9.37485 6.74879 10.8813 5.85809 12.7397 5.85809H27.8816C29.74 5.85809 31.2465 6.74879 32.0877 9.4209L32.6247 11.6004M4.23777 26.8571C3.75924 27.7587 3.48633 28.7991 3.48633 29.907C3.48633 33.3504 6.1227 36.1419 9.37485 36.1419H13.5809" stroke="#5956E9" strokeWidth="2.52365" strokeLinecap="round" />
        <path d="M30.4053 29.4122V31.0946" stroke="#5956E9" strokeWidth="2.52365" strokeLinecap="round" />
        <path d="M26.1992 29.4122V31.0946" stroke="#5956E9" strokeWidth="2.52365" strokeLinecap="round" />
        <path d="M21.9932 29.4122V31.0946" stroke="#5956E9" strokeWidth="2.52365" strokeLinecap="round" />
        <path d="M17.7871 29.4122V31.0946" stroke="#5956E9" strokeWidth="2.52365" strokeLinecap="round" />
    </svg>
);

export const BACKUPsvg = () => (
    <svg width="150" height="95" viewBox="0 0 173 123" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.4375" width="172.293" height="122" rx="10.407" fill="#F2F4FB" fillOpacity="0.14" />
        <path d="M26.5955 22.8655C24.8925 22.8655 23.512 24.246 23.512 25.949C23.512 27.652 24.8925 29.0326 26.5955 29.0326C28.2985 29.0326 29.6791 27.652 29.6791 25.949C29.6791 24.246 28.2985 22.8655 26.5955 22.8655ZM26.5955 69.3114H26.0174V69.8896H26.5955V69.3114ZM26.0174 25.949L26.0174 69.3114H27.1737L27.1737 25.949H26.0174ZM26.5955 69.8896H53.7693V68.7333H26.5955V69.8896Z" fill="white" />
        <path d="M143.385 108.241C145.088 108.241 146.469 106.861 146.469 105.158C146.469 103.455 145.088 102.074 143.385 102.074C141.682 102.074 140.301 103.455 140.301 105.158C140.301 106.861 141.682 108.241 143.385 108.241ZM143.385 75.0931H143.963V74.5149H143.385V75.0931ZM143.963 105.158V75.0931H142.807V105.158H143.963ZM143.385 74.5149H115.633V75.6712H143.385V74.5149Z" fill="white" />
        <path d="M86.9024 87.342C57.4027 85.9663 59.0713 61.931 63.593 50.0852C59.237 53.295 57.4473 61.6762 57.097 65.4656C42.4809 65.4656 43.2451 87.342 55.7596 87.342H86.9024Z" fill="white" />
        <path d="M114.724 51.4646C109.45 38.0139 97.4963 35.5747 92.7517 35.9887C74.6392 72.0609 101.891 84.1677 117.494 85.6644C135.759 73.7422 124.372 52.6109 114.724 51.4646Z" fill="white" />
        <path d="M109.645 87.724C68.9495 84.0938 78.8847 44.5444 89.8706 33.845C79.3241 35.832 73.5031 46.3913 71.9109 51.4225C66.7905 52.6453 65.383 53.7153 65.3193 54.0974C58.4412 83.5206 92.0041 88.7748 109.645 87.724Z" fill="white" />
        <path d="M106.055 51.895C103.38 43.412 96.0242 39.0622 92.6807 37.9476C92.7125 37.8203 92.8526 37.3936 93.1583 36.7058C103.858 34.948 111.5 46.0995 113.984 51.895C120.327 53.1942 124.651 60.5246 126.021 64.0274C123.823 67.753 119.219 73.1791 118.378 65.0782C117.327 54.952 105.93 51.5129 106.055 51.895Z" fill="url(#paint0_linear_527_1842)" />
        <path d="M74.4349 72.2478C65.264 50.2377 80.103 37.9843 88.6689 34.6089C76.976 48.9002 79.3388 64.3825 81.9818 70.3372C83.1281 74.4131 83.2236 80.5016 74.4349 72.2478Z" fill="white" />
        <path d="M53.0983 66.5162C53.939 65.9048 56.1871 65.8793 57.2061 65.943C56.9869 61.5586 60.069 55.311 61.8428 52.3777C58.5976 66.0576 62.4944 75.5278 64.8485 78.553C66.2178 81.196 67.6953 86.0616 62.6513 84.3803C56.3463 82.2786 52.0475 67.2804 53.0983 66.5162Z" fill="white" />
        <g clip-path="url(#clip0_527_1842)">
            <path fillRule="evenodd" clipRule="evenodd" d="M135.58 25.5154C135.58 25.1158 135.904 24.7927 136.302 24.7927C136.701 24.7927 137.025 25.1158 137.025 25.5154V28.4062C137.025 28.8059 136.701 29.1289 136.302 29.1289C135.904 29.1289 135.58 28.8059 135.58 28.4062V25.5154ZM129.075 30.5744H139.193C139.592 30.5744 139.916 30.2513 139.916 29.8516V21.9019H128.353V29.8516C128.353 30.2513 128.676 30.5744 129.075 30.5744ZM142.807 21.9019H141.361V30.5744C141.361 31.3722 140.714 32.0198 139.916 32.0198H128.353C127.555 32.0198 126.907 31.3722 126.907 30.5744V21.9019H125.462C123.865 21.9019 122.571 23.1962 122.571 24.7927V42.1377C122.571 43.7341 123.865 45.0285 125.462 45.0285H142.807C144.403 45.0285 145.698 43.7341 145.698 42.1377V24.7927C145.698 23.1962 144.403 21.9019 142.807 21.9019Z" fill="white" fillOpacity="0.91" />
        </g>
        <defs>
            <linearGradient id="paint0_linear_527_1842" x1="109.351" y1="36.52" x2="109.351" y2="69.289" gradientUnits="userSpaceOnUse">
                <stop stopColor="white" />
                <stop offset="1" stopColor="#C4C4C4" stopOpacity="0" />
            </linearGradient>
            <clipPath id="clip0_527_1842">
                <rect width="23.1266" height="23.1266" fill="white" transform="translate(122.571 21.9019)" />
            </clipPath>
        </defs>
    </svg>
);

export const BACKUPsvg2 = () => (
    <svg width="120" height="85" viewBox="0 0 175 120" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.4375" width="172.293" height="122" rx="10.407" fill="#F2F4FB" fillOpacity="0.14" />
        <path d="M26.5955 22.8655C24.8925 22.8655 23.512 24.246 23.512 25.949C23.512 27.652 24.8925 29.0326 26.5955 29.0326C28.2985 29.0326 29.6791 27.652 29.6791 25.949C29.6791 24.246 28.2985 22.8655 26.5955 22.8655ZM26.5955 69.3114H26.0174V69.8896H26.5955V69.3114ZM26.0174 25.949L26.0174 69.3114H27.1737L27.1737 25.949H26.0174ZM26.5955 69.8896H53.7693V68.7333H26.5955V69.8896Z" fill="white" />
        <path d="M143.385 108.241C145.088 108.241 146.469 106.861 146.469 105.158C146.469 103.455 145.088 102.074 143.385 102.074C141.682 102.074 140.301 103.455 140.301 105.158C140.301 106.861 141.682 108.241 143.385 108.241ZM143.385 75.0931H143.963V74.5149H143.385V75.0931ZM143.963 105.158V75.0931H142.807V105.158H143.963ZM143.385 74.5149H115.633V75.6712H143.385V74.5149Z" fill="white" />
        <path d="M86.9024 87.342C57.4027 85.9663 59.0713 61.931 63.593 50.0852C59.237 53.295 57.4473 61.6762 57.097 65.4656C42.4809 65.4656 43.2451 87.342 55.7596 87.342H86.9024Z" fill="white" />
        <path d="M114.724 51.4646C109.45 38.0139 97.4963 35.5747 92.7517 35.9887C74.6392 72.0609 101.891 84.1677 117.494 85.6644C135.759 73.7422 124.372 52.6109 114.724 51.4646Z" fill="white" />
        <path d="M109.645 87.724C68.9495 84.0938 78.8847 44.5444 89.8706 33.845C79.3241 35.832 73.5031 46.3913 71.9109 51.4225C66.7905 52.6453 65.383 53.7153 65.3193 54.0974C58.4412 83.5206 92.0041 88.7748 109.645 87.724Z" fill="white" />
        <path d="M106.055 51.895C103.38 43.412 96.0242 39.0622 92.6807 37.9476C92.7125 37.8203 92.8526 37.3936 93.1583 36.7058C103.858 34.948 111.5 46.0995 113.984 51.895C120.327 53.1942 124.651 60.5246 126.021 64.0274C123.823 67.753 119.219 73.1791 118.378 65.0782C117.327 54.952 105.93 51.5129 106.055 51.895Z" fill="url(#paint0_linear_527_1842)" />
        <path d="M74.4349 72.2478C65.264 50.2377 80.103 37.9843 88.6689 34.6089C76.976 48.9002 79.3388 64.3825 81.9818 70.3372C83.1281 74.4131 83.2236 80.5016 74.4349 72.2478Z" fill="white" />
        <path d="M53.0983 66.5162C53.939 65.9048 56.1871 65.8793 57.2061 65.943C56.9869 61.5586 60.069 55.311 61.8428 52.3777C58.5976 66.0576 62.4944 75.5278 64.8485 78.553C66.2178 81.196 67.6953 86.0616 62.6513 84.3803C56.3463 82.2786 52.0475 67.2804 53.0983 66.5162Z" fill="white" />
        <g clip-path="url(#clip0_527_1842)">
            <path fillRule="evenodd" clipRule="evenodd" d="M135.58 25.5154C135.58 25.1158 135.904 24.7927 136.302 24.7927C136.701 24.7927 137.025 25.1158 137.025 25.5154V28.4062C137.025 28.8059 136.701 29.1289 136.302 29.1289C135.904 29.1289 135.58 28.8059 135.58 28.4062V25.5154ZM129.075 30.5744H139.193C139.592 30.5744 139.916 30.2513 139.916 29.8516V21.9019H128.353V29.8516C128.353 30.2513 128.676 30.5744 129.075 30.5744ZM142.807 21.9019H141.361V30.5744C141.361 31.3722 140.714 32.0198 139.916 32.0198H128.353C127.555 32.0198 126.907 31.3722 126.907 30.5744V21.9019H125.462C123.865 21.9019 122.571 23.1962 122.571 24.7927V42.1377C122.571 43.7341 123.865 45.0285 125.462 45.0285H142.807C144.403 45.0285 145.698 43.7341 145.698 42.1377V24.7927C145.698 23.1962 144.403 21.9019 142.807 21.9019Z" fill="white" fillOpacity="0.91" />
        </g>
        <defs>
            <linearGradient id="paint0_linear_527_1842" x1="109.351" y1="36.52" x2="109.351" y2="69.289" gradientUnits="userSpaceOnUse">
                <stop stopColor="white" />
                <stop offset="1" stopColor="#C4C4C4" stopOpacity="0" />
            </linearGradient>
            <clipPath id="clip0_527_1842">
                <rect width="23.1266" height="23.1266" fill="white" transform="translate(122.571 21.9019)" />
            </clipPath>
        </defs>
    </svg>
);

export const DASHBOARDsvg = () => (
    <svg width="120" height="85" viewBox="0 0 172 122" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1.20386 118.903V3.53475C1.20441 3.03277 1.40406 2.55151 1.75901 2.19656C2.11396 1.84161 2.59522 1.64195 3.0972 1.6414H168.578C169.08 1.64195 169.561 1.84161 169.916 2.19656C170.271 2.55151 170.471 3.03277 170.471 3.53475V118.903C170.471 119.405 170.271 119.886 169.916 120.241C169.561 120.596 169.08 120.796 168.578 120.796H3.0972C2.59522 120.795 2.11396 120.596 1.75901 120.241C1.40406 119.886 1.20441 119.405 1.20386 118.903Z" fill="white" />
        <path d="M3.09722 122C2.27661 121.998 1.49016 121.671 0.909776 121.091C0.329397 120.511 0.00230461 119.725 0 118.904V3.53475C0.00195284 2.71415 0.328702 1.9277 0.908825 1.34731C1.48895 0.766928 2.27526 0.439822 3.09586 0.4375H168.578C169.399 0.439471 170.185 0.766243 170.766 1.34638C171.346 1.92652 171.673 2.71283 171.675 3.53343V118.903C171.673 119.723 171.347 120.51 170.767 121.09C170.186 121.671 169.4 121.998 168.579 122H3.09722ZM2.40774 118.901C2.40824 119.084 2.48118 119.26 2.61063 119.389C2.74008 119.519 2.91551 119.592 3.09858 119.592H168.578C168.761 119.591 168.936 119.518 169.065 119.389C169.194 119.26 169.267 119.084 169.268 118.901V3.53475C169.267 3.35189 169.194 3.17676 169.064 3.04758C168.935 2.9184 168.76 2.84566 168.577 2.84524H3.09722C2.91436 2.84602 2.73924 2.9191 2.61007 3.04853C2.4809 3.17796 2.40816 3.35322 2.40774 3.53608V118.901Z" fill="#E4E4E4" />
        <path d="M141.398 28.5888C141.792 28.5953 142.168 28.7563 142.444 29.0371C142.72 29.3179 142.875 29.696 142.875 30.0899C142.875 30.4837 142.72 30.8618 142.444 31.1426C142.168 31.4234 141.792 31.5844 141.398 31.5909H103.762C103.565 31.5944 103.369 31.5589 103.185 31.4865C103.002 31.4142 102.834 31.3064 102.692 31.1693C102.551 31.0321 102.437 30.8684 102.359 30.6874C102.28 30.5065 102.238 30.3118 102.235 30.1145C102.232 29.9173 102.267 29.7213 102.34 29.5379C102.412 29.3544 102.52 29.187 102.658 29.0453C102.795 28.9036 102.959 28.7904 103.14 28.712C103.321 28.6337 103.516 28.5918 103.713 28.5888C103.729 28.5885 103.746 28.5885 103.762 28.5888H141.398Z" fill="#E4E4E4" />
        <path d="M141.398 39.1995C141.792 39.206 142.168 39.3669 142.444 39.6477C142.72 39.9285 142.875 40.3067 142.875 40.7006C142.875 41.0944 142.72 41.4726 142.444 41.7534C142.168 42.0342 141.792 42.1951 141.398 42.2016H103.762C103.565 42.2048 103.369 42.1692 103.186 42.0968C103.003 42.0243 102.835 41.9165 102.694 41.7794C102.552 41.6423 102.439 41.4786 102.36 41.2977C102.282 41.1169 102.24 40.9223 102.237 40.7252C102.233 40.5281 102.269 40.3323 102.341 40.1489C102.414 39.9655 102.522 39.7982 102.659 39.6566C102.796 39.5149 102.96 39.4016 103.14 39.3232C103.321 39.2448 103.516 39.2027 103.713 39.1995C103.729 39.1992 103.746 39.1992 103.762 39.1995H141.398Z" fill="#E4E4E4" />
        <path d="M141.398 49.8102C141.792 49.8167 142.168 49.9777 142.444 50.2585C142.72 50.5393 142.875 50.9174 142.875 51.3113C142.875 51.7052 142.72 52.0833 142.444 52.3641C142.168 52.6448 141.792 52.8058 141.398 52.8123H103.762C103.565 52.8158 103.369 52.7803 103.185 52.7079C103.002 52.6356 102.834 52.5278 102.692 52.3907C102.551 52.2536 102.437 52.0898 102.359 51.9089C102.28 51.7279 102.238 51.5332 102.235 51.3359C102.232 51.1387 102.267 50.9427 102.34 50.7593C102.412 50.5758 102.52 50.4084 102.658 50.2668C102.795 50.1251 102.959 50.0118 103.14 49.9335C103.321 49.8551 103.516 49.8133 103.713 49.8102C103.729 49.8099 103.746 49.8099 103.762 49.8102H141.398Z" fill="#E4E4E4" />
        <path d="M117.417 66.2133C117.417 65.1968 117.718 64.2032 118.283 63.358C118.847 62.5128 119.65 61.8541 120.589 61.4651C121.528 61.0761 122.562 60.9743 123.559 61.1726C124.556 61.3709 125.471 61.8604 126.19 62.5792C126.909 63.2979 127.398 64.2137 127.597 65.2107C127.795 66.2076 127.693 67.241 127.304 68.1801C126.915 69.1192 126.256 69.9219 125.411 70.4866C124.566 71.0514 123.572 71.3528 122.556 71.3528C121.881 71.3528 121.213 71.2199 120.589 70.9616C119.966 70.7033 119.399 70.3247 118.922 69.8475C118.445 69.3702 118.066 68.8037 117.808 68.1801C117.549 67.5566 117.417 66.8883 117.417 66.2133Z" fill="#6C63FF" />
        <path d="M120.527 65.7064H122.048V64.1854C122.049 64.0512 122.102 63.9226 122.197 63.8277C122.292 63.7328 122.42 63.6793 122.554 63.6791C122.689 63.6789 122.818 63.7319 122.913 63.8264C123.008 63.921 123.062 64.0495 123.062 64.1837V65.7064H124.583C124.718 65.7064 124.847 65.7598 124.942 65.8548C125.037 65.9499 125.09 66.0789 125.09 66.2133C125.09 66.3478 125.037 66.4767 124.942 66.5718C124.847 66.6669 124.718 66.7203 124.583 66.7203H123.062V68.2412C123.062 68.3754 123.008 68.5039 122.913 68.5986C122.818 68.6933 122.69 68.7465 122.555 68.7465C122.421 68.7465 122.293 68.6933 122.197 68.5986C122.102 68.5039 122.049 68.3754 122.048 68.2412V66.7203H120.527C120.461 66.7204 120.395 66.7075 120.333 66.6821C120.272 66.6567 120.216 66.6195 120.169 66.5725C120.122 66.5255 120.084 66.4697 120.058 66.4082C120.033 66.3468 120.02 66.2809 120.02 66.2143C120.019 66.1477 120.032 66.0818 120.058 66.0202C120.083 65.9587 120.12 65.9027 120.167 65.8555C120.214 65.8084 120.27 65.7709 120.332 65.7453C120.393 65.7197 120.459 65.7065 120.526 65.7064H120.527Z" fill="white" />
        <path d="M69.2596 42.2394C69.2626 44.6401 68.8345 47.0219 67.9957 49.2712C67.6148 50.3015 67.1475 51.2978 66.5989 52.2494C64.835 55.3272 62.2898 57.8847 59.2206 59.6633C56.1513 61.4419 52.6669 62.3785 49.1195 62.3785C45.5722 62.3785 42.0877 61.4419 39.0185 59.6633C35.9492 57.8847 33.404 55.3272 31.6402 52.2494C31.4616 51.9399 31.2923 51.6258 31.1322 51.307C29.7632 48.5912 29.0269 45.6007 28.9786 42.5598C28.9303 39.5189 29.5713 36.5065 30.8535 33.7487C32.1356 30.9909 34.0257 28.5592 36.3818 26.6361C38.7379 24.7131 41.4989 23.3485 44.4577 22.6448C47.4164 21.9412 50.4961 21.9167 53.4657 22.5732C56.4353 23.2297 59.2177 24.5502 61.6041 26.4355C63.9905 28.3209 65.919 30.7222 67.2449 33.4593C68.5707 36.1963 69.2595 39.1981 69.2596 42.2394Z" fill="#E4E4E4" />
        <path d="M50.1668 45.6395C52.2933 45.6395 54.0172 43.9156 54.0172 41.7891C54.0172 39.6626 52.2933 37.9387 50.1668 37.9387C48.0403 37.9387 46.3164 39.6626 46.3164 41.7891C46.3164 43.9156 48.0403 45.6395 50.1668 45.6395Z" fill="#6C63FF" />
        <path d="M31.1323 51.307C32.8288 49.4736 35.0967 46.5629 37.2237 48.6661C38.2911 49.6581 39.9618 52.1165 41.561 50.5093L54.0492 38.0211C54.2843 37.789 54.5635 37.6062 54.8702 37.4835C55.177 37.3608 55.5051 37.3006 55.8354 37.3066C56.1658 37.3125 56.4916 37.3844 56.7937 37.518C57.0959 37.6516 57.3683 37.8442 57.5949 38.0846L67.9958 49.2712C67.6149 50.3015 67.1476 51.2978 66.599 52.2495H31.6403L31.1323 51.307Z" fill="white" />
        <path d="M66.6084 98.4133C66.4818 98.4131 66.3564 98.4378 66.2394 98.4861C66.1224 98.5344 66.016 98.6053 65.9264 98.6947C65.8368 98.7842 65.7658 98.8904 65.7173 99.0073C65.6688 99.1243 65.6438 99.2496 65.6438 99.3762C65.6438 99.5028 65.6688 99.6282 65.7173 99.7451C65.7658 99.8621 65.8368 99.9683 65.9264 100.058C66.016 100.147 66.1224 100.218 66.2394 100.266C66.3564 100.315 66.4818 100.339 66.6084 100.339H76.7131C76.8397 100.339 76.9651 100.315 77.0821 100.266C77.1991 100.218 77.3055 100.147 77.3951 100.058C77.4847 99.9683 77.5557 99.8621 77.6042 99.7451C77.6527 99.6282 77.6777 99.5028 77.6777 99.3762C77.6777 99.2496 77.6527 99.1243 77.6042 99.0073C77.5557 98.8904 77.4847 98.7842 77.3951 98.6947C77.3055 98.6053 77.1991 98.5344 77.0821 98.4861C76.9651 98.4378 76.8397 98.4131 76.7131 98.4133H66.6084Z" fill="#6C63FF" />
        <path d="M21.0843 84.2698C20.8289 84.2698 20.584 84.3713 20.4034 84.5519C20.2228 84.7325 20.1213 84.9774 20.1213 85.2328C20.1213 85.4882 20.2228 85.7331 20.4034 85.9137C20.584 86.0943 20.8289 86.1957 21.0843 86.1957H77.1552C77.4106 86.1957 77.6555 86.0943 77.8361 85.9137C78.0167 85.7331 78.1182 85.4882 78.1182 85.2328C78.1182 84.9774 78.0167 84.7325 77.8361 84.5519C77.6555 84.3713 77.4106 84.2698 77.1552 84.2698H21.0843Z" fill="#E5E5E5" />
        <path d="M21.0843 91.3416C20.8289 91.3416 20.584 91.443 20.4034 91.6236C20.2228 91.8042 20.1213 92.0491 20.1213 92.3045C20.1213 92.5599 20.2228 92.8048 20.4034 92.9854C20.584 93.166 20.8289 93.2674 21.0843 93.2674H77.1552C77.4106 93.2674 77.6555 93.166 77.8361 92.9854C78.0167 92.8048 78.1182 92.5599 78.1182 92.3045C78.1182 92.0491 78.0167 91.8042 77.8361 91.6236C77.6555 91.443 77.4106 91.3416 77.1552 91.3416H21.0843Z" fill="#E5E5E5" />
        <path d="M140.044 98.4133C139.918 98.4131 139.792 98.4378 139.675 98.4861C139.558 98.5344 139.452 98.6053 139.362 98.6947C139.273 98.7842 139.202 98.8904 139.153 99.0073C139.105 99.1243 139.08 99.2496 139.08 99.3762C139.08 99.5028 139.105 99.6282 139.153 99.7451C139.202 99.8621 139.273 99.9683 139.362 100.058C139.452 100.147 139.558 100.218 139.675 100.266C139.792 100.315 139.918 100.339 140.044 100.339H150.149C150.275 100.339 150.401 100.315 150.518 100.266C150.635 100.218 150.741 100.147 150.831 100.058C150.92 99.9683 150.992 99.8621 151.04 99.7451C151.089 99.6282 151.113 99.5028 151.113 99.3762C151.113 99.2496 151.089 99.1243 151.04 99.0073C150.992 98.8904 150.92 98.7842 150.831 98.6947C150.741 98.6053 150.635 98.5344 150.518 98.4861C150.401 98.4378 150.275 98.4131 150.149 98.4133H140.044Z" fill="#6C63FF" />
        <path d="M94.5201 84.2698C94.2647 84.2698 94.0198 84.3713 93.8392 84.5519C93.6586 84.7325 93.5571 84.9774 93.5571 85.2328C93.5571 85.4882 93.6586 85.7331 93.8392 85.9137C94.0198 86.0943 94.2647 86.1957 94.5201 86.1957H150.591C150.846 86.1957 151.091 86.0943 151.272 85.9137C151.452 85.7331 151.554 85.4882 151.554 85.2328C151.554 84.9774 151.452 84.7325 151.272 84.5519C151.091 84.3713 150.846 84.2698 150.591 84.2698H94.5201Z" fill="#E5E5E5" />
        <path d="M94.5201 91.3416C94.2647 91.3416 94.0198 91.443 93.8392 91.6236C93.6586 91.8042 93.5571 92.0491 93.5571 92.3045C93.5571 92.5599 93.6586 92.8048 93.8392 92.9854C94.0198 93.166 94.2647 93.2674 94.5201 93.2674H150.591C150.846 93.2674 151.091 93.166 151.272 92.9854C151.452 92.8048 151.554 92.5599 151.554 92.3045C151.554 92.0491 151.452 91.8042 151.272 91.6236C151.091 91.443 150.846 91.3416 150.591 91.3416H94.5201Z" fill="#E5E5E5" />
    </svg>

);

export const BLUECHECKsvg = () => (
    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.4375" width="21.5625" height="21.5625" rx="10.7812" fill="#497EF7" />
        <path d="M13.225 8.33475L9.3707 12.198L7.88828 10.7156C7.80775 10.6215 7.70863 10.5452 7.59716 10.4913C7.4857 10.4374 7.3643 10.407 7.24056 10.4022C7.11683 10.3975 6.99345 10.4183 6.87815 10.4635C6.76286 10.5086 6.65815 10.5772 6.57059 10.6647C6.48304 10.7523 6.41452 10.857 6.36936 10.9723C6.32418 11.0876 6.30334 11.2109 6.30812 11.3347C6.3129 11.4584 6.3432 11.5798 6.39713 11.6913C6.45105 11.8027 6.52744 11.9019 6.62149 11.9824L8.73281 14.1027C8.81676 14.186 8.91633 14.2519 9.02579 14.2966C9.13522 14.3413 9.25247 14.364 9.3707 14.3633C9.60636 14.3623 9.83223 14.2687 9.99961 14.1027L14.4918 9.61053C14.576 9.52698 14.6428 9.42761 14.6885 9.31816C14.7341 9.20868 14.7576 9.09124 14.7576 8.97264C14.7576 8.85404 14.7341 8.7366 14.6885 8.62712C14.6428 8.51764 14.576 8.41827 14.4918 8.33475C14.3234 8.16742 14.0958 8.07349 13.8584 8.07349C13.621 8.07349 13.3934 8.16742 13.225 8.33475ZM10.7813 2.23436C9.00432 2.23436 7.26727 2.76128 5.7898 3.7485C4.31233 4.73572 3.16078 6.13888 2.48078 7.78057C1.80077 9.42222 1.62285 11.2287 1.96951 12.9715C2.31617 14.7143 3.17185 16.3151 4.42834 17.5717C5.68483 18.8281 7.28569 19.6838 9.02849 20.0305C10.7713 20.3771 12.5778 20.1992 14.2194 19.5192C15.8611 18.8392 17.2643 17.6877 18.2515 16.2102C19.2387 14.7327 19.7656 12.9957 19.7656 11.2187C19.7656 10.0389 19.5332 8.8706 19.0817 7.78057C18.6303 6.69053 17.9685 5.7001 17.1342 4.86582C16.2999 4.03155 15.3095 3.36976 14.2194 2.91825C13.1294 2.46675 11.9611 2.23436 10.7813 2.23436ZM10.7813 18.4062C9.35974 18.4062 7.97008 17.9847 6.78809 17.195C5.60612 16.4051 4.68488 15.2826 4.14087 13.9693C3.59687 12.656 3.45453 11.2107 3.73186 9.81654C4.00919 8.42228 4.69374 7.1416 5.69892 6.13641C6.70411 5.13122 7.9848 4.44667 9.37906 4.16934C10.7733 3.89201 12.2185 4.03434 13.5318 4.57835C14.8452 5.12236 15.9677 6.04359 16.7575 7.22558C17.5472 8.40755 17.9688 9.79714 17.9688 11.2187C17.9688 13.1249 17.2115 14.9532 15.8636 16.3011C14.5157 17.6489 12.6875 18.4062 10.7813 18.4062Z" fill="white" />
    </svg>
);

export const TERMINALsvg = () => (
    <svg width="80" height="50" viewBox="0 0 74 61" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="8" width="62" height="53" rx="7" fill="#F2F4FB" />
        <path d="M16.5625 45.4731V23.1294H45.7812V45.4731H16.5625ZM19.098 30.7345L24.2969 28.7146V27.5541L19.098 25.4916V26.7814L22.794 28.1138L19.098 29.4888V30.7345ZM24.7699 32.2388H29.8835V30.949H24.7699V32.2388Z" fill="#615EF8" />
        <rect x="50" y="-0.0205078" width="23.0625" height="23.0625" rx="11.5312" fill="#497EF7" />
        <path d="M64.145 8.42613L60.0226 12.5582L58.437 10.9726C58.3509 10.872 58.2449 10.7903 58.1257 10.7327C58.0064 10.675 57.8766 10.6425 57.7443 10.6374C57.6119 10.6323 57.4799 10.6546 57.3566 10.703C57.2333 10.7512 57.1213 10.8245 57.0277 10.9181C56.934 11.0118 56.8607 11.1239 56.8124 11.2472C56.7641 11.3704 56.7418 11.5024 56.7469 11.6348C56.7521 11.7671 56.7845 11.8969 56.8421 12.0162C56.8998 12.1354 56.9815 12.2414 57.0821 12.3275L59.3403 14.5954C59.4301 14.6844 59.5366 14.7549 59.6537 14.8027C59.7707 14.8505 59.8961 14.8748 60.0226 14.874C60.2746 14.873 60.5162 14.7728 60.6952 14.5954L65.4999 9.79066C65.59 9.7013 65.6615 9.59502 65.7103 9.47796C65.7591 9.36086 65.7842 9.23525 65.7842 9.1084C65.7842 8.98155 65.7591 8.85594 65.7103 8.73884C65.6615 8.62174 65.59 8.51546 65.4999 8.42613C65.3198 8.24716 65.0763 8.1467 64.8225 8.1467C64.5686 8.1467 64.3251 8.24716 64.145 8.42613ZM61.5313 1.90137C59.6307 1.90137 57.7728 2.46495 56.1926 3.52084C54.6123 4.57673 53.3807 6.07751 52.6534 7.8334C51.926 9.58925 51.7357 11.5214 52.1065 13.3854C52.4773 15.2495 53.3925 16.9617 54.7364 18.3056C56.0803 19.6495 57.7925 20.5647 59.6566 20.9355C61.5206 21.3063 63.4527 21.116 65.2086 20.3887C66.9645 19.6613 68.4653 18.4297 69.5212 16.8494C70.577 15.2692 71.1406 13.4113 71.1406 11.5107C71.1406 10.2488 70.892 8.99926 70.4092 7.8334C69.9263 6.66753 69.2185 5.6082 68.3261 4.71589C67.4338 3.82358 66.3745 3.11575 65.2086 2.63283C64.0428 2.14992 62.7932 1.90137 61.5313 1.90137ZM61.5313 19.1982C60.0109 19.1982 58.5245 18.7474 57.2603 17.9027C55.9961 17.0579 55.0108 15.8574 54.4289 14.4527C53.8471 13.048 53.6948 11.5022 53.9915 10.011C54.2881 8.51976 55.0203 7.14998 56.0954 6.07486C57.1705 4.99975 58.5403 4.26758 60.0315 3.97096C61.5227 3.67434 63.0685 3.82657 64.4732 4.40842C65.8779 4.99027 67.0785 5.97559 67.9232 7.2398C68.7679 8.504 69.2188 9.99025 69.2188 11.5107C69.2188 13.5496 68.4089 15.505 66.9672 16.9467C65.5255 18.3883 63.5701 19.1982 61.5313 19.1982Z" fill="white" />
    </svg>
);

export const SHIELDCHECKsvg = ({ width }) => (
    <svg width={width || 41} height={width || 40} viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_715_2026)">
            <g clip-path="url(#clip1_715_2026)">
                <path fillRule="evenodd" clipRule="evenodd" d="M21 40L11.8752 33.4822C6.61933 29.728 3.5 23.6667 3.5 17.2076V7.49999L21 -7.62939e-06L38.5 7.49999V17.2076C38.5 23.6667 35.3808 29.728 30.1248 33.4822L21 40ZM31.5177 14.2678L27.9823 10.7322L18.5 20.2145L14.0178 15.7322L10.4822 19.2678L18.5 27.2855L31.5177 14.2678Z" fill="#5956E9" />
            </g>
        </g>
        <defs>
            <clipPath id="clip0_715_2026">
                <rect width="40" height="40" fill="white" transform="translate(0.5)" />
            </clipPath>
            <clipPath id="clip1_715_2026">
                <rect width="40" height="40" fill="white" transform="translate(1)" />
            </clipPath>
        </defs>
    </svg>
);

export const ENCRYPTIONsvg = () => (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_715_2038)">
            <path d="M38.8055 33.0156L35.3823 29.5924C36.0822 28.0176 36.4158 26.291 36.3429 24.5188C36.218 21.4869 34.9201 18.6852 32.6879 16.6295C31.869 15.8753 30.96 15.2553 29.9879 14.7779C29.8678 6.61222 23.1892 0.00601959 14.9954 0.00601959C6.7269 0.00601959 0 6.733 0 15.0015C0 23.201 6.61542 29.8833 14.7892 29.9942C15.4011 31.24 16.2452 32.3742 17.2968 33.3426C19.4112 35.2898 22.1237 36.3466 24.981 36.3466C25.14 36.3466 25.2998 36.3433 25.4596 36.3367C26.9111 36.277 28.3095 35.9476 29.5988 35.3764L33.0218 38.7995C33.7922 39.5698 34.8192 39.994 35.9137 39.994C37.0082 39.994 38.0351 39.5698 38.8054 38.7994C39.5757 38.0291 39.9999 37.0021 39.9999 35.9076C40 34.8131 39.5759 33.7859 38.8055 33.0156ZM26.8067 13.7684C26.0607 13.6493 25.2972 13.6037 24.525 13.6355C23.9045 13.6611 23.294 13.7365 22.6972 13.8583C22.6626 12.9221 22.5855 12.0032 22.4681 11.1108C23.0292 11.2983 23.562 11.5068 24.0582 11.7384C25.3356 12.3345 26.2848 13.0452 26.8067 13.7684ZM26.0571 9.6966C25.7879 9.54848 25.506 9.40535 25.2112 9.2677C24.2108 8.80082 23.0934 8.41129 21.8934 8.10324C21.5853 6.90323 21.1959 5.78589 20.729 4.78541C20.5914 4.49072 20.4483 4.20869 20.3001 3.93947C22.8102 5.14831 24.8485 7.18659 26.0571 9.6966ZM11.7323 5.93846C12.6692 3.93096 13.889 2.73244 14.9954 2.73244C16.1018 2.73244 17.3216 3.93096 18.2584 5.93846C18.4899 6.43456 18.6986 6.96753 18.8861 7.52863C17.6325 7.3637 16.3272 7.27652 14.9954 7.27652C13.6636 7.27652 12.3583 7.3637 11.1047 7.52863C11.2922 6.96753 11.5008 6.43464 11.7323 5.93846ZM2.72642 15.0015C2.72642 13.8951 3.92494 12.6752 5.93244 11.7385C6.42854 11.5069 6.96151 11.2983 7.52261 11.1108C7.35768 12.3643 7.2705 13.6697 7.2705 15.0015C7.2705 16.3332 7.35768 17.6387 7.52261 18.8922C6.96151 18.7047 6.42862 18.4961 5.93244 18.2645C3.92494 17.3277 2.72642 16.1078 2.72642 15.0015ZM3.93361 20.3063C4.20283 20.4544 4.4847 20.5975 4.77955 20.7351C5.77994 21.202 6.89729 21.5915 8.09722 21.8995C8.40527 23.0995 8.7948 24.2169 9.26168 25.2174C9.39925 25.5121 9.54238 25.7941 9.69058 26.0633C7.1805 24.8546 5.14221 22.8164 3.93361 20.3063ZM9.26168 4.78565C8.7948 5.78604 8.40542 6.90347 8.09722 8.10347C6.89721 8.4116 5.77987 8.80105 4.77955 9.26793C4.4847 9.40551 4.20291 9.54864 3.93361 9.69684C5.14221 7.18659 7.1805 5.14831 9.69058 3.93979C9.54246 4.20893 9.39918 4.49088 9.26168 4.78565ZM13.6418 25.4534C13.6608 25.917 13.7078 26.3751 13.7808 26.826C13.0513 26.3071 12.3336 25.3527 11.7323 24.0643C11.5008 23.5682 11.2922 23.0352 11.1047 22.4742C11.9996 22.592 12.9209 22.669 13.8596 22.7035C13.6781 23.5971 13.6033 24.5189 13.6418 25.4534ZM14.7808 19.9975C13.2514 19.986 11.7704 19.853 10.3883 19.6084C10.1324 18.1617 9.99699 16.607 9.99699 15.0014C9.99699 13.3958 10.1324 11.8411 10.3885 10.3944C11.8352 10.1384 13.3899 10.0029 14.9954 10.0029C16.6009 10.0029 18.1557 10.1383 19.6024 10.3944C19.8466 11.7743 19.9796 13.2526 19.9914 14.7794C18.7429 15.3916 17.6062 16.2368 16.6359 17.2905C15.8798 18.1115 15.2586 19.0228 14.7808 19.9975ZM25.3477 33.6125C23.043 33.7079 20.8402 32.8993 19.1437 31.337C17.4473 29.7748 16.4608 27.6455 16.3661 25.3412C16.2712 23.037 17.0793 20.8338 18.6416 19.1373C20.2038 17.4409 22.3331 16.4544 24.6374 16.3597C24.7589 16.3546 24.8801 16.3521 25.0012 16.3521C27.1725 16.3521 29.2343 17.1554 30.8412 18.6352C32.5376 20.1973 33.5241 22.3267 33.6189 24.6309C33.7137 26.9351 32.9057 29.1384 31.3434 30.8348C29.7812 32.5311 27.6518 33.5176 25.3477 33.6125ZM36.8776 36.8713C36.3461 37.4029 35.4812 37.4029 34.9497 36.8714L32.0045 33.9262C32.4804 33.5512 32.9304 33.1362 33.3489 32.6817C33.553 32.4601 33.7469 32.2315 33.9313 31.9972L36.8776 34.9435C37.133 35.1989 37.2735 35.5412 37.2735 35.9074C37.2735 36.2737 37.133 36.616 36.8776 36.8713Z" fill="#5956E9" />
            <path d="M29.9908 24.9861C29.9908 24.5638 29.9377 24.1541 29.8387 23.7621L31.1833 22.9858L29.8201 20.6247L28.4722 21.4029C27.8864 20.8339 27.1624 20.4073 26.3555 20.1782V18.6245H23.6291V20.1782C22.8224 20.4073 22.0982 20.8338 21.5124 21.4029L20.1645 20.6247L18.8013 22.9858L20.1459 23.7621C20.0469 24.154 19.9938 24.5638 19.9938 24.9861C19.9938 25.4084 20.0468 25.8182 20.1459 26.2101L18.8013 26.9864L20.1645 29.3475L21.5124 28.5693C22.0982 29.1384 22.8223 29.565 23.6291 29.794V31.3478H26.3555V29.794C27.1624 29.5649 27.8864 29.1383 28.4722 28.5693L29.8201 29.3475L31.1833 26.9864L29.8387 26.2101C29.9376 25.8181 29.9908 25.4083 29.9908 24.9861ZM24.9923 27.2582C23.7395 27.2582 22.7203 26.2389 22.7203 24.9861C22.7203 23.7333 23.7395 22.7141 24.9923 22.7141C26.2451 22.7141 27.2643 23.7333 27.2643 24.9861C27.2643 26.2389 26.2452 27.2582 24.9923 27.2582Z" fill="#5956E9" />
        </g>
        <defs>
            <clipPath id="clip0_715_2038">
                <rect width="40" height="40" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

export const AUDITsvg = () => (
    <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_711_2010)">
            <path d="M7.28322 0.00323103C6.20524 0.112293 5.37939 1.03044 5.37939 2.13923V37.0582C5.37939 38.2409 6.32107 39.1942 7.50378 39.1942H33.4376C34.6203 39.1942 35.562 38.2409 35.562 37.0582V10.8225H27.7493C26.1497 10.8225 24.7543 9.65568 24.7543 8.1061V0.00323103H7.50378C7.42986 0.00323103 7.35508 -0.00403879 7.28322 0.00323103ZM26.2402 0.00323103V8.1061C26.2402 8.74286 26.8464 9.33662 27.7493 9.33662H35.562L26.2402 0.00323103ZM7.73596 15.7678H11.2302V17.4511H7.73596V15.7678ZM12.124 15.7678H15.6299V17.4511H12.124V15.7678ZM16.5237 15.7678H20.018V17.4511H16.5237V15.7678ZM25.3115 15.7678H28.8173V17.4511H25.3115V15.7678ZM29.7112 15.7678H33.2054V17.4511H29.7112V15.7678ZM12.124 18.6816H15.6299V20.3649H12.124V18.6816ZM16.5237 18.6816H20.018V20.3649H16.5237V18.6816ZM20.9234 18.6816H24.4176V20.3649H20.9234V18.6816ZM7.73596 21.5954H11.2302V23.267H7.73596V21.5954ZM16.5237 21.5954H20.018V23.267H16.5237V21.5954ZM25.3115 21.5954H28.8173V23.267H25.3115V21.5954ZM29.7112 21.5954H33.2054V23.267H29.7112V21.5954ZM12.124 24.4976H15.6299V26.1808H12.124V24.4976ZM20.9234 24.4976H24.4176V26.1808H20.9234V24.4976ZM25.3115 24.4976H28.8173V26.1808H25.3115V24.4976ZM7.73596 27.4113H11.2302V29.0946H7.73596V27.4113ZM12.124 27.4113H15.6299V29.0946H12.124V27.4113ZM16.5237 27.4113H20.018V29.0946H16.5237V27.4113ZM20.9234 27.4113H24.4176V29.0946H20.9234V27.4113ZM25.3115 27.4113H28.8173V29.0946H25.3115V27.4113ZM29.7112 27.4113H33.2054V29.0946H29.7112V27.4113ZM12.124 30.3251H15.6299V31.9968H12.124V30.3251ZM20.9234 30.3251H24.4176V31.9968H20.9234V30.3251ZM29.7112 30.3251H33.2054V31.9968H29.7112V30.3251ZM7.73596 33.2273H11.2302V34.9106H7.73596V33.2273ZM12.124 33.2273H15.6299V34.9106H12.124V33.2273ZM16.5237 33.2273H20.018V34.9106H16.5237V33.2273ZM25.3115 33.2273H28.8173V34.9106H25.3115V33.2273ZM29.7112 33.2273H33.2054V34.9106H29.7112V33.2273Z" fill="#5956E9" />
        </g>
        <defs>
            <clipPath id="clip0_711_2010">
                <rect width="40" height="40" fill="white" transform="translate(0.5)" />
            </clipPath>
        </defs>
    </svg>
);

export const MALEPERSONsvg = () => (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_711_1980)">
            <path d="M12.3001 17.8205C14.1319 21.8629 18.0535 23.6225 20 23.6225C21.2799 23.6225 23.4137 22.8611 25.2459 21.191C24.5759 21.2377 23.8599 21.2614 23.0948 21.2614C22.9153 21.2614 22.7269 21.2598 22.5256 21.2567C22.1399 21.5537 21.6621 21.7212 21.1663 21.7212H19.5361C18.3075 21.7212 17.3081 20.7217 17.3081 19.4932C17.3081 18.2646 18.3075 17.2651 19.5361 17.2651H21.1663C21.7553 17.2651 22.3061 17.4947 22.7146 17.8906C22.8504 17.8921 22.9842 17.8928 23.1155 17.8928C25.22 17.8928 26.6069 17.6998 27.5142 17.467C27.3965 17.1806 27.3324 16.8672 27.3324 16.5401V10.8911C27.3324 10.3034 27.5505 9.74011 27.9276 9.30494C27.7264 7.73826 27.3611 6.5422 26.8159 5.65991C25.6926 3.84174 23.5267 2.99499 20 2.99499C17.0744 2.99499 15.0541 3.60231 13.8238 4.85163C12.8701 5.82011 12.3174 7.20202 12.0591 9.28907C12.4447 9.72587 12.6677 10.2949 12.6677 10.8911V16.5401C12.6678 17.0102 12.5327 17.4488 12.3001 17.8205Z" fill="#5956E9" />
            <path d="M8.29177 17.8738H10.2474C10.9836 17.8738 11.581 17.2767 11.581 16.5401V10.8911C11.581 10.399 11.3137 9.97019 10.9172 9.73913C11.3539 5.11345 13.1389 1.90805 20.0001 1.90805C23.9402 1.90805 26.3999 2.91881 27.7406 5.08867C28.5207 6.35136 28.896 7.98226 29.0713 9.74609C28.6811 9.97878 28.4192 10.4037 28.4192 10.891V16.54C28.4192 17.175 28.8635 17.7053 29.4579 17.8398C28.5958 18.4036 26.811 18.9795 23.1154 18.9795C22.8161 18.9795 22.5041 18.9757 22.1791 18.9679C21.9889 18.6022 21.6072 18.3519 21.1664 18.3519H19.5363C18.9061 18.3519 18.3951 18.8628 18.3951 19.493C18.3951 20.1233 18.9061 20.6342 19.5363 20.6342H21.1664C21.5473 20.6342 21.8837 20.447 22.0909 20.1604C22.4341 20.1691 22.7702 20.1745 23.0949 20.1745C27.2605 20.1744 29.9086 19.4634 30.9811 18.0527C31.0267 17.9927 31.0684 17.9329 31.1063 17.8738H31.7087C32.4451 17.8738 33.0423 17.2767 33.0423 16.5401V10.8911C33.0423 10.1546 32.4452 9.55752 31.7087 9.55752H30.9732C30.7662 7.42646 30.3075 5.61231 29.3639 4.08552C27.6654 1.33637 24.6022 0 20 0C15.3975 0 12.3347 1.33637 10.6361 4.08563C9.6926 5.61242 9.23406 7.42635 9.02702 9.55763H8.29166C7.55512 9.55763 6.95801 10.1546 6.95801 10.8912V16.5402C6.95801 17.2767 7.55522 17.8738 8.29177 17.8738Z" fill="#5956E9" />
            <path d="M36.1228 34.4852C35.6794 31.7282 34.7666 28.1769 32.9358 26.915C31.6875 26.0542 27.3293 23.7256 25.4683 22.7314L25.4291 22.7105C25.2161 22.5967 24.9563 22.6195 24.7664 22.7686C23.7901 23.5349 22.7213 24.0511 21.5898 24.3029C21.3898 24.3474 21.2256 24.4897 21.1529 24.6812L19.9999 27.7201L18.8468 24.6812C18.7741 24.4896 18.6099 24.3474 18.4099 24.3029C17.2784 24.0511 16.2094 23.5349 15.233 22.7686C15.0432 22.6194 14.7833 22.5966 14.5704 22.7105C12.7301 23.6937 8.31551 26.0711 7.06912 26.9114C4.96077 28.3317 4.04 33.4698 3.87687 34.4853C3.86068 34.586 3.87002 34.6891 3.90404 34.7852C3.97947 34.9982 5.91338 40.0001 19.9997 40.0001C34.0859 40.0001 36.02 34.9982 36.0956 34.7853C36.1297 34.689 36.139 34.5858 36.1228 34.4852ZM29.564 31.3192H23.9848V30.1443H29.564V31.3192Z" fill="#5956E9" />
        </g>
        <defs>
            <clipPath id="clip0_711_1980">
                <rect width="40" height="40" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

export const LIGHTNINGsvg = () => (
    <svg width="35" height="35" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_720_3360)">
            <path d="M21.8095 6.4976L19.9999 15.2528C19.9905 15.3256 19.9979 15.3997 20.0216 15.4692C20.0453 15.5387 20.0846 15.6018 20.1366 15.6538C20.1885 15.7057 20.2516 15.745 20.3212 15.7687C20.3907 15.7924 20.4647 15.7998 20.5375 15.7904H29.9071C30.0622 15.8042 30.2116 15.8555 30.3424 15.9399C30.4733 16.0242 30.5816 16.1392 30.6582 16.2747C30.7347 16.4103 30.7771 16.5625 30.7817 16.7181C30.7864 16.8737 30.7531 17.0281 30.6847 17.168L20.4799 33.92C20.3891 34.0993 20.2401 34.2425 20.0573 34.3262C19.8745 34.4098 19.6687 34.429 19.4736 34.3805C19.2785 34.332 19.1056 34.2187 18.9832 34.0592C18.8609 33.8997 18.7962 33.7034 18.7999 33.5024L20.5999 23.192C20.5999 22.832 20.3023 22.952 19.9423 22.952H10.1215C9.46394 22.952 8.98394 21.992 9.34394 21.392L20.1343 6.08C20.2292 5.90725 20.3787 5.77076 20.5593 5.69182C20.7399 5.61289 20.9415 5.59593 21.1328 5.64361C21.324 5.69128 21.4941 5.80091 21.6165 5.95539C21.739 6.10987 21.8068 6.30052 21.8095 6.4976Z" fill="#5956E9" />
        </g>
        <defs>
            <clipPath id="clip0_720_3360">
                <rect width="40" height="40" fill="white" transform="translate(0.25)" />
            </clipPath>
        </defs>
    </svg>
);

export const SHIELDsvg = () => (
    <svg width="38" height="38" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path opacity="0.4" d="M30.5505 9.45L10.9838 29.0167C10.2505 29.75 9.01712 29.65 8.41712 28.7833C6.35046 25.7667 5.13379 22.2 5.13379 18.5333V11.2167C5.13379 9.84999 6.16712 8.29999 7.43379 7.78333L16.7172 3.98333C18.8172 3.11666 21.1505 3.11666 23.2505 3.98333L29.9838 6.73331C31.1005 7.18331 31.3838 8.61666 30.5505 9.45Z" fill="#5956E9" />
        <path d="M32.1163 11.7335C33.1997 10.8169 34.8497 11.6002 34.8497 13.0168V18.5335C34.8497 26.6835 28.933 34.3168 20.8497 36.5502C20.2997 36.7002 19.6997 36.7002 19.133 36.5502C16.7663 35.8835 14.5663 34.7668 12.683 33.3002C11.883 32.6835 11.7997 31.5168 12.4997 30.8002C16.133 27.0835 26.7663 16.2502 32.1163 11.7335Z" fill="#5956E9" />
    </svg>
);

export const MOSQUEsvg = () => (
    <svg width="45" height="45" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M65.875 61.625H2.125" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M38.8407 53.1271H62.6875V61.625H5.3125V53.1271H29.1593" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M8.5 53.1271V49.9375H13.6096" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M54.3906 49.9375H59.5002V53.1271" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M29.1591 49.9375H13.6094L16.1668 45.6875H29.1591" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M38.25 45.6875H51.833L54.3904 49.9375H38.25" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M56.3125 58.4471H53.125V61.625H56.3125V58.4471Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M36.125 58.4471H32.9375V61.625H36.125V58.4471Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M18.0625 58.4471H14.875V61.625H18.0625V58.4471Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M15.3818 58.4471H54.5976" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M38.25 24.4375H29.75V58.4471H38.25V24.4375Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M34 37.1875V58.4375" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M38.25 31.8506H29.75V37.1695H38.25V31.8506Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M38.25 24.4375H29.75V28.6875H38.25V24.4375Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M37.1875 19.125H30.8125V24.4375H37.1875V19.125Z" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M34 19.125V14.875" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M34 28.6875V31.875" stroke="#497EF7" strokeWidth="2.125" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
);

export const DIVISIONsvg = () => (
    <svg width="38" height="38" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M50.2341 25.3608L46.6874 21.8141C46.0808 21.2074 45.5908 20.0174 45.5908 19.1774V14.1374C45.5908 12.0841 43.9108 10.4041 41.8574 10.4041H36.8408C36.0008 10.4041 34.8108 9.91411 34.2041 9.30745L30.6574 5.76078C29.2108 4.31411 26.8308 4.31411 25.3841 5.76078L21.7908 9.30745C21.2074 9.91411 20.0174 10.4041 19.1541 10.4041H14.1374C12.0841 10.4041 10.4041 12.0841 10.4041 14.1374V19.1541C10.4041 19.9941 9.91411 21.1841 9.30745 21.7908L5.76078 25.3374C4.31411 26.7841 4.31411 29.1641 5.76078 30.6108L9.30745 34.1574C9.91411 34.7641 10.4041 35.9541 10.4041 36.7941V41.8108C10.4041 43.8641 12.0841 45.5441 14.1374 45.5441H19.1541C19.9941 45.5441 21.1841 46.0341 21.7908 46.6408L25.3374 50.1874C26.7841 51.6341 29.1641 51.6341 30.6108 50.1874L34.1574 46.6408C34.7641 46.0341 35.9541 45.5441 36.7941 45.5441H41.8108C43.8641 45.5441 45.5441 43.8641 45.5441 41.8108V36.7941C45.5441 35.9541 46.0341 34.7641 46.6408 34.1574L50.1874 30.6108C51.7041 29.1874 51.7041 26.8074 50.2341 25.3608ZM18.6641 20.9974C18.6641 19.7141 19.7141 18.6641 20.9974 18.6641C22.2808 18.6641 23.3308 19.7141 23.3308 20.9974C23.3308 22.2808 22.3041 23.3308 20.9974 23.3308C19.7141 23.3308 18.6641 22.2808 18.6641 20.9974ZM22.2341 36.2341C21.8841 36.5841 21.4408 36.7474 20.9974 36.7474C20.5541 36.7474 20.1108 36.5841 19.7608 36.2341C19.0841 35.5574 19.0841 34.4374 19.7608 33.7608L33.7608 19.7608C34.4374 19.0841 35.5574 19.0841 36.2341 19.7608C36.9108 20.4374 36.9108 21.5574 36.2341 22.2341L22.2341 36.2341ZM34.9974 37.3308C33.6908 37.3308 32.6408 36.2808 32.6408 34.9974C32.6408 33.7141 33.6908 32.6641 34.9741 32.6641C36.2574 32.6641 37.3074 33.7141 37.3074 34.9974C37.3074 36.2808 36.2808 37.3308 34.9974 37.3308Z" fill="#497EF7" />
    </svg>
);

export const LOCKsvg = () => (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20.0003 5.30414C11.8851 5.30414 5.30443 11.8848 5.30443 20C5.30443 28.1152 11.8851 34.6959 20.0003 34.6959C28.1155 34.6959 34.6961 28.1152 34.6961 20C34.6961 11.8848 28.1155 5.30414 20.0003 5.30414ZM13.2676 14.4908C13.2676 10.7788 16.2883 7.75805 20.0003 7.75805C23.7123 7.75805 26.733 10.7788 26.733 14.4908V16.3295C26.733 16.6751 26.4634 16.9447 26.1178 16.9447H24.8943C24.5487 16.9447 24.2791 16.675 24.2791 16.3295V14.4908C24.2791 8.81565 15.7077 8.81565 15.7077 14.4908V16.3295C15.7077 16.6751 15.4381 16.9447 15.0924 16.9447H13.8689C13.5233 16.9447 13.2537 16.675 13.2537 16.3295V14.4908H13.2676ZM29.7952 28.5714C29.7952 29.2419 29.2422 29.7949 28.5717 29.7949H11.4289C10.7583 29.7949 10.2053 29.2419 10.2053 28.5714V18.7765C10.2053 18.106 10.7583 17.553 11.4289 17.553H28.5717C29.2422 17.553 29.7952 18.106 29.7952 18.7765V28.5714ZM12.6524 19.0807V28.2673C12.6524 28.4401 12.521 28.5714 12.3482 28.5714H11.733C11.5602 28.5714 11.4289 28.4401 11.4289 28.2673V19.0807C11.4289 18.9078 11.5602 18.7765 11.733 18.7765H12.3482C12.521 18.7765 12.6524 18.9079 12.6524 19.0807ZM22.4473 22.447C22.4473 23.3456 21.9496 24.1336 21.2238 24.5484V26.7327C21.2238 27.0783 20.9542 27.3479 20.6086 27.3479H19.3851C19.0394 27.3479 18.7699 27.0782 18.7699 26.7327V24.5484C18.0441 24.1267 17.5464 23.3456 17.5464 22.447C17.5464 21.0853 18.6385 20 19.9934 20C21.3482 20 22.4473 21.0922 22.4473 22.447ZM20.0003 2.85714C10.5302 2.85714 2.85742 10.5299 2.85742 20C2.85742 29.4701 10.5302 37.1429 20.0003 37.1429C29.4703 37.1429 37.1431 29.4701 37.1431 20C37.1431 10.5299 29.4703 2.85714 20.0003 2.85714ZM20.0003 35.9193C11.2215 35.9193 4.08092 28.7788 4.08092 20C4.08092 11.2212 11.2215 4.08065 20.0003 4.08065C28.7791 4.08065 35.9196 11.2212 35.9196 20C35.9196 28.7788 28.7791 35.9193 20.0003 35.9193Z" fill="#5956E9" />
    </svg>
);

export const SSLCERTIFICATIONsvg = () => (
    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_720_3202)">
            <path d="M34.5 18C34.5063 16.6825 34.2435 15.3777 33.7276 14.1654C33.2117 12.9531 32.4537 11.8589 31.5 10.95V5.25C31.5 4.85218 31.342 4.47064 31.0607 4.18934C30.7794 3.90804 30.3978 3.75 30 3.75H3C2.60218 3.75 2.22064 3.90804 1.93934 4.18934C1.65804 4.47064 1.5 4.85218 1.5 5.25V30.75C1.5 31.1478 1.65804 31.5294 1.93934 31.8107C2.22064 32.092 2.60218 32.25 3 32.25H19.5V33.45C19.5 34.95 21 35.775 21.975 34.8L24.75 32.25L27.525 34.8C28.5 35.775 30 34.95 30 33.45V26.175C31.3785 25.3023 32.514 24.0952 33.3007 22.6659C34.0875 21.2366 34.5001 19.6315 34.5 18ZM24.75 11.25C26.085 11.25 27.3901 11.6459 28.5001 12.3876C29.6101 13.1293 30.4753 14.1835 30.9862 15.4169C31.4971 16.6503 31.6307 18.0075 31.3703 19.3169C31.1098 20.6262 30.467 21.829 29.523 22.773C28.579 23.717 27.3762 24.3599 26.0669 24.6203C24.7575 24.8808 23.4003 24.7471 22.1669 24.2362C20.9335 23.7253 19.8793 22.8601 19.1376 21.7501C18.3959 20.6401 18 19.335 18 18C18 16.2098 18.7112 14.4929 19.977 13.227C21.2429 11.9612 22.9598 11.25 24.75 11.25ZM9 10.5H18.525L17.25 11.775C16.8069 12.3026 16.4291 12.8818 16.125 13.5H9C8.60217 13.5 8.22064 13.342 7.93934 13.0607C7.65803 12.7794 7.5 12.3978 7.5 12C7.5 11.6022 7.65803 11.2206 7.93934 10.9393C8.22064 10.658 8.60217 10.5 9 10.5ZM9 16.5H15.15C15.0225 16.9892 14.9719 17.4952 15 18C14.9754 18.5047 15.026 19.0102 15.15 19.5H9C8.60217 19.5 8.22064 19.342 7.93934 19.0607C7.65803 18.7794 7.5 18.3978 7.5 18C7.5 17.6022 7.65803 17.2206 7.93934 16.9393C8.22064 16.658 8.60217 16.5 9 16.5ZM9 25.5C8.60217 25.5 8.22064 25.342 7.93934 25.0607C7.65803 24.7794 7.5 24.3978 7.5 24C7.5 23.6022 7.65803 23.2206 7.93934 22.9393C8.22064 22.658 8.60217 22.5 9 22.5H16.125C16.4291 23.1182 16.8069 23.6974 17.25 24.225C17.6368 24.6865 18.0635 25.1132 18.525 25.5H9Z" fill="#5956E9" />
            <path d="M24.75 21.75C26.8211 21.75 28.5 20.0711 28.5 18C28.5 15.9289 26.8211 14.25 24.75 14.25C22.6789 14.25 21 15.9289 21 18C21 20.0711 22.6789 21.75 24.75 21.75Z" fill="#5956E9" />
        </g>
        <defs>
            <clipPath id="clip0_720_3202">
                <rect width="36" height="36" fill="white" />
            </clipPath>
        </defs>
    </svg>
);

export const HEADsvg = ({ color }) => (
    <svg width="38" height="38" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22.9619 3.58099C23.034 3.63324 23.0644 3.7263 23.0369 3.81097L22.507 5.44154C22.4452 5.63129 22.6625 5.7891 22.8238 5.67172L24.2108 4.6642C24.2829 4.61175 24.3806 4.61175 24.4527 4.6642L25.8397 5.67181C26.0011 5.7891 26.2184 5.63138 26.1567 5.44164L25.6269 3.81107C25.5992 3.7264 25.6295 3.63334 25.7017 3.58109L27.0887 2.5599C27.25 2.44261 27.1673 2.17406 26.9678 2.17406H25.2532C25.1642 2.17406 25.085 2.13002 25.0575 2.04515L24.5276 0.421324C24.466 0.231773 24.1977 0.234996 24.136 0.424546L23.6061 2.04349C23.5785 2.12816 23.4995 2.17406 23.4102 2.17406H21.6957C21.4964 2.17406 21.4134 2.44261 21.5748 2.5599L22.9619 3.58099Z" fill={color || "white"} />
        <path d="M40.0049 9.34017C39.9419 9.27708 39.9266 9.1805 39.9671 9.1012L40.7455 7.57337C40.8359 7.39583 40.6461 7.20599 40.4683 7.29642L38.9407 8.07484C38.8613 8.11546 38.7648 8.10003 38.7017 8.03705L37.4894 6.82474C37.3485 6.68363 37.1095 6.8055 37.1406 7.00247L37.4088 8.69583C37.4227 8.78392 37.3782 8.87113 37.2989 8.91156L35.7713 9.68997C35.5935 9.7804 35.6354 10.0455 35.8325 10.0766L37.5259 10.345C37.6141 10.3589 37.6832 10.428 37.6971 10.516L37.9651 12.2094C37.9964 12.4065 38.2615 12.4484 38.3519 12.2706L39.1304 10.743C39.1708 10.6635 39.258 10.6192 39.3461 10.6331L41.0394 10.9013C41.2365 10.9326 41.3584 10.6934 41.2172 10.5525L40.0049 9.34017Z" fill={color || "white"} />
        <path d="M47.6303 23.7022L46.0332 23.1723C45.9481 23.1447 45.9241 23.0657 45.9241 22.9764V21.2619C45.9241 21.0625 45.6354 20.9795 45.5183 21.141L44.4942 22.528C44.4416 22.6 44.3401 22.6304 44.2555 22.603L42.6208 22.0729C42.4312 22.0113 42.2714 22.2284 42.3885 22.3897L43.3954 23.7769C43.4477 23.849 43.4474 23.9466 43.3949 24.0188L42.3869 25.406C42.2698 25.5673 42.4271 25.7844 42.6169 25.7228L44.2474 25.1929C44.3322 25.1655 44.4249 25.1956 44.4775 25.2677L45.5185 26.6547C45.6355 26.8161 45.9241 26.7333 45.9241 26.5338V24.8193C45.9241 24.73 45.9481 24.651 46.0332 24.6236L47.6472 24.0935C47.8367 24.0322 47.82 23.7638 47.6303 23.7022Z" fill={color || "white"} />
        <path d="M12.6432 9.62315C12.8402 9.5919 12.8821 9.32696 12.7046 9.23634L11.1769 8.45792C11.0973 8.41749 11.0528 8.33028 11.0671 8.2422L11.3351 6.54884C11.3663 6.35177 11.1271 6.23009 10.9862 6.3711L9.77412 7.58341C9.71103 7.64649 9.61425 7.66183 9.53496 7.6212L8.00712 6.84278C7.82959 6.75235 7.63974 6.9422 7.73037 7.11974L8.50859 8.64757C8.54921 8.72696 8.53398 8.82345 8.4708 8.88653L7.25849 10.0988C7.11757 10.2398 7.23925 10.479 7.43623 10.4477L9.12959 10.1795C9.21787 10.1655 9.30468 10.2099 9.34531 10.2894L10.1237 11.817C10.2144 11.9947 10.4793 11.9528 10.5103 11.7558L10.7787 10.0624C10.7927 9.97433 10.8617 9.90528 10.9498 9.89132L12.6432 9.62315Z" fill={color || "white"} />
        <path d="M5.63563 21.7481C5.75272 21.587 5.59501 21.3697 5.40546 21.4315L3.77489 21.9612C3.69003 21.9888 3.59716 21.9585 3.54491 21.8864L2.54159 20.4994C2.4245 20.338 2.17401 20.4208 2.17401 20.6203V22.3348C2.17401 22.4241 2.1121 22.5031 2.02704 22.5305L0.39413 23.0606C0.20458 23.122 0.203017 23.3904 0.392861 23.4522L2.02743 23.9818C2.11229 24.0094 2.17392 24.0884 2.17392 24.1777V25.8922C2.17392 26.0916 2.4244 26.1746 2.54149 26.0131L3.54716 24.6261C3.5996 24.5541 3.69101 24.5237 3.77587 24.5513L5.40585 25.0812C5.5956 25.1428 5.75282 24.9257 5.63573 24.7644L4.62792 23.3774C4.57548 23.3051 4.57558 23.2075 4.62802 23.1353L5.63563 21.7481Z" fill={color || "white"} />
        <path d="M35.1769 24.0948C34.9907 20.0591 32.661 16.5831 29.2982 14.782C29.0331 14.6375 28.7598 14.5049 28.4808 14.3842C27.1191 13.7923 25.6165 13.4639 24.037 13.4639C18.1044 13.4639 13.2604 18.0978 12.9131 23.9449L12.9161 23.9443V23.9642C12.9161 24.1799 12.9059 25.1456 12.9059 25.1456L12.7397 25.5138H13.8502C14.2675 24.1466 15.5487 23.2122 17.0602 23.2122C18.9103 23.2122 20.4156 24.7577 20.4156 26.6078C20.4156 28.4577 18.9103 29.9332 17.0602 29.9332C15.2893 29.9332 13.8348 28.5411 13.714 26.7833H12.389C12.3607 26.7833 12.334 26.8285 12.3067 26.8247L10.9129 30.7178C10.7172 31.2606 11.1192 31.8614 11.6961 31.8614H12.8964L12.8874 38.0614C12.8874 39.2855 13.8802 40.2599 15.1043 40.2599H19.0685V46.0893C19.0685 46.9976 19.9807 47.6817 20.889 47.6817H31.0876C31.9959 47.6817 32.7404 46.9976 32.7404 46.0893V34.3786C32.7404 32.7812 33.1619 31.2216 33.9083 29.8088C34.688 28.3331 35.1444 26.6616 35.187 24.8865C35.189 24.7975 35.1898 24.7064 35.1898 24.6153C35.1897 24.4412 35.1847 24.267 35.1769 24.0948ZM24.2165 23.2676H23.2677V24.2239C23.2677 24.5577 22.9665 24.8282 22.6329 24.8282C22.2993 24.8282 21.9981 24.5577 21.9981 24.2239V23.2676H21.1386C20.805 23.2676 20.5343 22.9664 20.5343 22.6328C20.5343 22.2992 20.8049 21.998 21.1386 21.998H21.9981V21.146C21.9981 20.8124 22.2993 20.5417 22.6329 20.5417C22.9665 20.5417 23.2677 20.8123 23.2677 21.146V21.998H24.2165C24.5501 21.998 24.8208 22.2992 24.8208 22.6328C24.8208 22.9664 24.5501 23.2676 24.2165 23.2676Z" fill={color || "white"} />
        <path d="M18.5808 28.1641C18.9654 27.7752 19.2072 27.2413 19.2072 26.6481C19.2072 25.4637 18.2448 24.5015 17.0603 24.5015C16.4736 24.5015 15.9397 24.7366 15.5508 25.1212C15.1578 25.5122 14.9138 26.0527 14.9138 26.6482C14.9138 27.8325 15.8761 28.7949 17.0603 28.7949C17.6537 28.7948 18.1918 28.5531 18.5808 28.1641Z" fill={color || "white"} />
    </svg>
);

export const COMPUTERsvg = () => (
    <svg width="36" height="36" viewBox="0 0 42 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M25.1668 29.4165H16.8335V37.7498H25.1668V29.4165Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M35.5833 16.5833L26.5417 7.5625L24.4375 9.66667L35.5833 20.7708V16.5833Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M28.6458 13.8542L24.4375 9.66667L26.5417 7.5625L28.6458 9.66667V13.8542Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M18.7917 19.5L16.6875 17.3958L26.5417 7.5625L28.6458 9.66667L18.7917 19.5Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M18.7918 19.4997L12.8335 13.5622L14.9585 11.458L20.896 17.3955L18.7918 19.4997Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M17.0002 17.708L12.8335 13.5622L14.9585 11.458L17.0002 13.4997V17.708Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M7.33341 23.1455L17.0001 13.4997L14.8959 11.3955L6.41675 19.8538V23.1455H7.33341Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M41.8334 2.43734C41.7399 1.91914 41.4902 1.4419 41.1179 1.06956C40.7455 0.697213 40.2683 0.447546 39.7501 0.354004H2.25008C1.73188 0.447546 1.25464 0.697213 0.882301 1.06956C0.509957 1.4419 0.26029 1.91914 0.166748 2.43734V29.4165C0.245532 29.9409 0.490392 30.4262 0.865367 30.8012C1.24034 31.1762 1.72567 31.4211 2.25008 31.4998H39.7501C40.2745 31.4211 40.7598 31.1762 41.1348 30.8012C41.5098 30.4262 41.7546 29.9409 41.8334 29.4165V2.43734ZM37.6667 27.3332H4.33341V4.4165H37.6667V27.3332Z" fill="white" />
        <path fillRule="evenodd" clipRule="evenodd" d="M12.6667 39.8333C12.6667 38.3958 13.3126 37.75 14.7501 37.75H27.2501C28.6876 37.75 29.3334 38.3958 29.3334 39.8333H12.6667Z" fill="white" />
    </svg>
);

export const SUPPORTsvg = ({ size, color }) => (
    <svg width={size || '36'} height={size || '36'} viewBox="0 0 42 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.3748 22.2756C13.6645 27.3285 18.5665 29.528 20.9997 29.528C22.5995 29.528 25.2667 28.5763 27.557 26.4887C26.7196 26.5471 25.8246 26.5767 24.8681 26.5767C24.6437 26.5767 24.4083 26.5747 24.1567 26.5707C23.6745 26.942 23.0773 27.1514 22.4575 27.1514H20.4197C18.884 27.1514 17.6347 25.9021 17.6347 24.3664C17.6347 22.8307 18.884 21.5813 20.4197 21.5813H22.4575C23.1937 21.5813 23.8822 21.8683 24.3929 22.3632C24.5626 22.3651 24.7298 22.3659 24.894 22.3659C27.5246 22.3659 29.2583 22.1246 30.3924 21.8336C30.2453 21.4756 30.1651 21.084 30.1651 20.6751V13.6137C30.1651 12.8792 30.4378 12.1751 30.9092 11.6311C30.6576 9.67275 30.201 8.17767 29.5195 7.0748C28.1153 4.80209 25.408 3.74365 20.9997 3.74365C17.3426 3.74365 14.8172 4.50281 13.2794 6.06445C12.0872 7.27505 11.3964 9.00244 11.0735 11.6113C11.5555 12.1573 11.8343 12.8686 11.8343 13.6137V20.6751C11.8344 21.2626 11.6655 21.8109 11.3748 22.2756Z" fill="white" />
        <path d="M6.36447 22.3422H8.80904C9.72931 22.3422 10.476 21.5958 10.476 20.6751V13.6138C10.476 12.9987 10.1419 12.4627 9.64631 12.1739C10.1922 6.39182 12.4234 2.38506 20.9999 2.38506C25.9251 2.38506 28.9996 3.64851 30.6755 6.36084C31.6507 7.9392 32.1198 9.97783 32.3389 12.1826C31.8512 12.4735 31.5238 13.0047 31.5238 13.6137V20.675C31.5238 21.4688 32.0791 22.1316 32.8221 22.2997C31.7445 23.0045 29.5135 23.7244 24.8941 23.7244C24.5199 23.7244 24.1299 23.7196 23.7237 23.7099C23.4859 23.2527 23.0088 22.9398 22.4578 22.9398H20.4201C19.6324 22.9398 18.9936 23.5785 18.9936 24.3663C18.9936 25.1541 19.6324 25.7928 20.4201 25.7928H22.4578C22.9338 25.7928 23.3544 25.5587 23.6134 25.2005C24.0424 25.2113 24.4625 25.2181 24.8684 25.2181C30.0754 25.218 33.3855 24.3292 34.7261 22.5658C34.7832 22.4908 34.8352 22.4161 34.8826 22.3422H35.6357C36.5561 22.3422 37.3026 21.5958 37.3026 20.6751V13.6138C37.3026 12.6933 36.5562 11.9469 35.6357 11.9469H34.7162C34.4575 9.28307 33.8841 7.01539 32.7046 5.1069C30.5815 1.67047 26.7526 0 20.9998 0C15.2466 0 11.4181 1.67047 9.29485 5.10704C8.1155 7.01552 7.54233 9.28293 7.28353 11.947H6.36434C5.44365 11.947 4.69727 12.6933 4.69727 13.614V20.6753C4.69727 21.5958 5.44379 22.3422 6.36447 22.3422Z" fill="white" />
        <path d="M41.1536 43.1066C40.5993 39.6604 39.4582 35.2212 37.1698 33.6439C35.6093 32.5678 30.1616 29.6571 27.8353 28.4143L27.7864 28.3883C27.5202 28.246 27.1953 28.2746 26.958 28.4608C25.7376 29.4187 24.4016 30.064 22.9872 30.3788C22.7373 30.4344 22.532 30.6122 22.4411 30.8516L20.9998 34.6502L19.5585 30.8516C19.4677 30.6121 19.2624 30.4344 19.0124 30.3788C17.598 30.064 16.2618 29.4187 15.0412 28.4608C14.804 28.2744 14.4791 28.2459 14.2129 28.3883C11.9127 29.6172 6.39438 32.589 4.8364 33.6393C2.20096 35.4148 1.05 41.8373 0.846087 43.1067C0.825844 43.2327 0.837528 43.3615 0.88005 43.4816C0.974333 43.7478 3.39173 50.0003 20.9997 50.0003C38.6074 50.0003 41.025 43.7478 41.1194 43.4817C41.1621 43.3613 41.1738 43.2324 41.1536 43.1066ZM32.955 39.1492H25.9811V37.6804H32.955V39.1492Z" fill="white" />
    </svg>
);

export const ONESTARsvg = () => (
    <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.2827 2.45332C10.5131 1.98638 10.6284 1.75291 10.7848 1.67831C10.9209 1.61341 11.0791 1.61341 11.2152 1.67831C11.3717 1.75291 11.4869 1.98638 11.7174 2.45332L13.9041 6.88328C13.9721 7.02113 14.0061 7.09006 14.0558 7.14358C14.0999 7.19096 14.1527 7.22935 14.2113 7.25662C14.2776 7.28742 14.3536 7.29854 14.5057 7.32077L19.397 8.03571C19.9121 8.11099 20.1696 8.14863 20.2888 8.27444C20.3925 8.38389 20.4412 8.5343 20.4215 8.68377C20.3988 8.85558 20.2124 9.0372 19.8395 9.4004L16.3014 12.8464C16.1912 12.9538 16.136 13.0076 16.1004 13.0715C16.0689 13.128 16.0487 13.1902 16.0409 13.2545C16.0321 13.3271 16.0451 13.403 16.0711 13.5547L16.906 18.4221C16.994 18.9355 17.038 19.1922 16.9553 19.3445C16.8833 19.477 16.7554 19.57 16.6071 19.5975C16.4366 19.6291 16.2061 19.5078 15.7451 19.2654L11.3724 16.9658C11.2361 16.8942 11.168 16.8584 11.0962 16.8443C11.0327 16.8318 10.9673 16.8318 10.9038 16.8443C10.832 16.8584 10.7639 16.8942 10.6277 16.9658L6.25492 19.2654C5.79392 19.5078 5.56341 19.6291 5.39297 19.5975C5.24468 19.57 5.11672 19.477 5.04474 19.3445C4.962 19.1922 5.00603 18.9355 5.09407 18.4221L5.92889 13.5547C5.95491 13.403 5.96793 13.3271 5.95912 13.2545C5.95132 13.1902 5.93111 13.128 5.89961 13.0715C5.86402 13.0076 5.80888 12.9538 5.69859 12.8464L2.16056 9.4004C1.78766 9.0372 1.60121 8.85558 1.57853 8.68377C1.55879 8.5343 1.60755 8.38389 1.71125 8.27444C1.83044 8.14863 2.08797 8.11099 2.60304 8.03571L7.49431 7.32077C7.64642 7.29854 7.72248 7.28742 7.78872 7.25662C7.84736 7.22935 7.90016 7.19096 7.94419 7.14358C7.99391 7.09006 8.02793 7.02113 8.09597 6.88328L10.2827 2.45332Z" fill="#AEAEAF" stroke="#AEAEAF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
)

export const ORANGESTARSsvg = () => (
    <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 21V16V21ZM4 6V1V6ZM1.5 3.5H6.5H1.5ZM1.5 18.5H6.5H1.5ZM12.5 2L10.7658 6.50886C10.4838 7.24209 10.3428 7.60871 10.1235 7.91709C9.9292 8.1904 9.6904 8.42919 9.41709 8.62353C9.10871 8.8428 8.74209 8.98381 8.00886 9.2658L3.5 11L8.00886 12.7342C8.74209 13.0162 9.10871 13.1572 9.41709 13.3765C9.6904 13.5708 9.9292 13.8096 10.1235 14.0829C10.3428 14.3913 10.4838 14.7579 10.7658 15.4911L12.5 20L14.2342 15.4911C14.5162 14.7579 14.6572 14.3913 14.8765 14.0829C15.0708 13.8096 15.3096 13.5708 15.5829 13.3765C15.8913 13.1572 16.2579 13.0162 16.9911 12.7342L21.5 11L16.9911 9.2658C16.2579 8.98381 15.8913 8.8428 15.5829 8.62353C15.3096 8.42919 15.0708 8.1904 14.8765 7.91709C14.6572 7.60871 14.5162 7.24209 14.2342 6.50886L12.5 2Z" fill="#F7AF2A" />
        <path d="M4 21V16M4 6V1M1.5 3.5H6.5M1.5 18.5H6.5M12.5 2L10.7658 6.50886C10.4838 7.24209 10.3428 7.60871 10.1235 7.91709C9.9292 8.1904 9.6904 8.42919 9.41709 8.62353C9.10871 8.8428 8.74209 8.98381 8.00886 9.2658L3.5 11L8.00886 12.7342C8.74209 13.0162 9.10871 13.1572 9.41709 13.3765C9.6904 13.5708 9.9292 13.8096 10.1235 14.0829C10.3428 14.3913 10.4838 14.7579 10.7658 15.4911L12.5 20L14.2342 15.4911C14.5162 14.7579 14.6572 14.3913 14.8765 14.0829C15.0708 13.8096 15.3096 13.5708 15.5829 13.3765C15.8913 13.1572 16.2579 13.0162 16.9911 12.7342L21.5 11L16.9911 9.2658C16.2579 8.98381 15.8913 8.8428 15.5829 8.62353C15.3096 8.42919 15.0708 8.1904 14.8765 7.91709C14.6572 7.60871 14.5162 7.24209 14.2342 6.50886L12.5 2Z" stroke="#F7AF2A" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
)

export const PURBLESTARSsvg = () => (
    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.5 7V2V7ZM4.5 20V15V20ZM15 4.5H20H15ZM2 17.5H7H2ZM5.5 1L4.71554 2.56892C4.45005 3.09989 4.31731 3.36538 4.13997 3.59545C3.98261 3.79959 3.79959 3.98261 3.59545 4.13997C3.36538 4.31731 3.0999 4.45005 2.56892 4.71554L1 5.5L2.56892 6.28446C3.0999 6.54995 3.36538 6.68269 3.59545 6.86003C3.79959 7.01739 3.98261 7.20041 4.13997 7.40455C4.31731 7.63462 4.45005 7.9001 4.71554 8.43108L5.5 10L6.28446 8.43108C6.54995 7.9001 6.68269 7.63462 6.86003 7.40455C7.01739 7.20041 7.20041 7.01739 7.40455 6.86003C7.63462 6.68269 7.9001 6.54995 8.43108 6.28446L10 5.5L8.43108 4.71554C7.9001 4.45005 7.63462 4.31731 7.40455 4.13997C7.20041 3.98261 7.01739 3.79959 6.86003 3.59545C6.68269 3.36538 6.54995 3.0999 6.28446 2.56892L5.5 1ZM16 11L15.0489 12.9022C14.7834 13.4332 14.6506 13.6987 14.4733 13.9288C14.3159 14.1329 14.1329 14.3159 13.9288 14.4733C13.6987 14.6506 13.4332 14.7834 12.9023 15.0489L11 16L12.9023 16.9511C13.4332 17.2166 13.6987 17.3494 13.9288 17.5267C14.1329 17.6841 14.3159 17.8671 14.4733 18.0712C14.6506 18.3013 14.7834 18.5668 15.0489 19.0977L16 21L16.9511 19.0978C17.2166 18.5668 17.3494 18.3013 17.5267 18.0712C17.6841 17.8671 17.8671 17.6841 18.0712 17.5267C18.3013 17.3494 18.5668 17.2166 19.0977 16.9511L21 16L19.0977 15.0489C18.5668 14.7834 18.3013 14.6506 18.0712 14.4733C17.8671 14.3159 17.6841 14.1329 17.5267 13.9288C17.3494 13.6987 17.2166 13.4332 16.9511 12.9023L16 11Z" fill="#497EF7" />
        <path d="M17.5 7V2M4.5 20V15M15 4.5H20M2 17.5H7M5.5 1L4.71554 2.56892C4.45005 3.09989 4.31731 3.36538 4.13997 3.59545C3.98261 3.79959 3.79959 3.98261 3.59545 4.13997C3.36538 4.31731 3.0999 4.45005 2.56892 4.71554L1 5.5L2.56892 6.28446C3.0999 6.54995 3.36538 6.68269 3.59545 6.86003C3.79959 7.01739 3.98261 7.20041 4.13997 7.40455C4.31731 7.63462 4.45005 7.9001 4.71554 8.43108L5.5 10L6.28446 8.43108C6.54995 7.9001 6.68269 7.63462 6.86003 7.40455C7.01739 7.20041 7.20041 7.01739 7.40455 6.86003C7.63462 6.68269 7.9001 6.54995 8.43108 6.28446L10 5.5L8.43108 4.71554C7.9001 4.45005 7.63462 4.31731 7.40455 4.13997C7.20041 3.98261 7.01739 3.79959 6.86003 3.59545C6.68269 3.36538 6.54995 3.0999 6.28446 2.56892L5.5 1ZM16 11L15.0489 12.9022C14.7834 13.4332 14.6506 13.6987 14.4733 13.9288C14.3159 14.1329 14.1329 14.3159 13.9288 14.4733C13.6987 14.6506 13.4332 14.7834 12.9023 15.0489L11 16L12.9023 16.9511C13.4332 17.2166 13.6987 17.3494 13.9288 17.5267C14.1329 17.6841 14.3159 17.8671 14.4733 18.0712C14.6506 18.3013 14.7834 18.5668 15.0489 19.0977L16 21L16.9511 19.0978C17.2166 18.5668 17.3494 18.3013 17.5267 18.0712C17.6841 17.8671 17.8671 17.6841 18.0712 17.5267C18.3013 17.3494 18.5668 17.2166 19.0977 16.9511L21 16L19.0977 15.0489C18.5668 14.7834 18.3013 14.6506 18.0712 14.4733C17.8671 14.3159 17.6841 14.1329 17.5267 13.9288C17.3494 13.6987 17.2166 13.4332 16.9511 12.9023L16 11Z" stroke="#497EF7" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
)

export const HTTPsvg = () => (
    <svg width="74" height="50" viewBox="0 0 74 60" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="8" width="62" height="51" rx="7" fill="#F2F4FB" />
        <g clip-path="url(#clip0_568_1185)">
            <path d="M31 51C40.3888 51 48 43.3888 48 34C48 24.6112 40.3888 17 31 17C21.6112 17 14 24.6112 14 34C14 43.3888 21.6112 51 31 51Z" fill="#615EF8" />
            <path d="M17.8525 30.4336H19.059V33.2545H19.0787C19.2242 33.0309 19.4192 32.846 19.6721 32.71C19.9053 32.5735 20.1874 32.4959 20.4891 32.4959C21.2966 32.4959 22.1424 33.0314 22.1424 34.5487V37.3404H20.946V34.6847C20.946 33.994 20.6931 33.4782 20.0312 33.4782C19.5642 33.4782 19.2338 33.7895 19.1074 34.1497C19.0686 34.2474 19.059 34.3733 19.059 34.5099V37.3404H17.8525V30.4336Z" fill="white" />
            <path d="M24.7417 31.2505V32.6031H25.8802V33.4982H24.7417V35.5897C24.7417 36.1635 24.8974 36.4647 25.3543 36.4647C25.5588 36.4647 25.7144 36.4355 25.8212 36.4068L25.8409 37.3211C25.6656 37.3896 25.3543 37.4385 24.9749 37.4385C24.5377 37.4385 24.1674 37.2924 23.9443 37.0496C23.6909 36.7766 23.5746 36.3494 23.5746 35.7268V33.4993H22.894V32.6025H23.5746V31.5321L24.7417 31.2505Z" fill="white" />
            <path d="M28.1573 31.2505V32.6031H29.2957V33.4982H28.1573V35.5897C28.1573 36.1635 28.3129 36.4647 28.7698 36.4647C28.9743 36.4647 29.13 36.4355 29.2368 36.4068L29.2564 37.3211C29.0811 37.3896 28.7698 37.4385 28.3905 37.4385C27.9533 37.4385 27.583 37.2924 27.3599 37.0496C27.1064 36.7766 26.9901 36.3494 26.9901 35.7268V33.4993H26.3096V32.6025H26.9901V31.5321L28.1573 31.2505Z" fill="white" />
            <path d="M30.2019 34.1781C30.2019 33.5555 30.1823 33.0497 30.1626 32.6024H31.2139L31.2718 33.3318H31.2915C31.6411 32.7969 32.1957 32.4951 32.9161 32.4951C34.0057 32.4951 34.9098 33.4291 34.9098 34.9075C34.9098 36.6197 33.8303 37.4464 32.7508 37.4464C32.1574 37.4464 31.6613 37.1935 31.4179 36.8147H31.3983V39.2654H30.2019V34.1781ZM31.3983 35.3554C31.3983 35.4722 31.4078 35.579 31.4371 35.6858C31.5539 36.1724 31.9816 36.5225 32.4879 36.5225C33.2465 36.5225 33.6943 35.8903 33.6943 34.9564C33.6943 34.1202 33.2853 33.4391 32.5171 33.4391C32.0209 33.4391 31.5635 33.7993 31.4472 34.3247C31.4174 34.422 31.3983 34.5287 31.3983 34.626V35.3554Z" fill="white" />
            <path d="M35.748 33.4103C35.748 32.9624 36.0594 32.6421 36.4875 32.6421C36.9253 32.6421 37.217 32.963 37.227 33.4103C37.227 33.848 36.9354 34.1694 36.4875 34.1694C36.0498 34.1694 35.748 33.848 35.748 33.4103ZM35.748 36.6886C35.748 36.2408 36.0594 35.9199 36.4875 35.9199C36.9253 35.9199 37.217 36.2312 37.227 36.6886C37.227 37.1168 36.9354 37.4472 36.4875 37.4472C36.0498 37.4472 35.748 37.1168 35.748 36.6886Z" fill="white" />
            <path d="M37.6274 37.7291L40.0011 30.6768H40.8378L38.4636 37.7291H37.6274Z" fill="white" />
            <path d="M40.9062 37.7291L43.2799 30.6768H44.1161L41.7435 37.7291H40.9062Z" fill="white" />
        </g>
        <rect x="50" y="-0.0205078" width="23.0625" height="23.0625" rx="11.5312" fill="#497EF7" />
        <path d="M64.145 8.42613L60.0226 12.5582L58.437 10.9726C58.3509 10.872 58.2449 10.7903 58.1257 10.7327C58.0064 10.675 57.8766 10.6425 57.7443 10.6374C57.6119 10.6323 57.4799 10.6546 57.3566 10.703C57.2333 10.7512 57.1213 10.8245 57.0277 10.9181C56.934 11.0118 56.8607 11.1239 56.8124 11.2472C56.7641 11.3704 56.7418 11.5024 56.7469 11.6348C56.7521 11.7671 56.7845 11.8969 56.8421 12.0162C56.8998 12.1354 56.9815 12.2414 57.0821 12.3275L59.3403 14.5954C59.4301 14.6844 59.5366 14.7549 59.6537 14.8027C59.7707 14.8505 59.8961 14.8748 60.0226 14.874C60.2746 14.873 60.5162 14.7728 60.6952 14.5954L65.4999 9.79066C65.59 9.7013 65.6615 9.59502 65.7103 9.47796C65.7591 9.36086 65.7842 9.23525 65.7842 9.1084C65.7842 8.98155 65.7591 8.85594 65.7103 8.73884C65.6615 8.62174 65.59 8.51546 65.4999 8.42613C65.3198 8.24716 65.0763 8.1467 64.8225 8.1467C64.5686 8.1467 64.3251 8.24716 64.145 8.42613ZM61.5313 1.90137C59.6307 1.90137 57.7728 2.46495 56.1926 3.52084C54.6123 4.57673 53.3807 6.07751 52.6534 7.8334C51.926 9.58925 51.7357 11.5214 52.1065 13.3854C52.4773 15.2495 53.3925 16.9617 54.7364 18.3056C56.0803 19.6495 57.7925 20.5647 59.6566 20.9355C61.5206 21.3063 63.4527 21.116 65.2086 20.3887C66.9645 19.6613 68.4653 18.4297 69.5212 16.8494C70.577 15.2692 71.1406 13.4113 71.1406 11.5107C71.1406 10.2488 70.892 8.99926 70.4092 7.8334C69.9263 6.66753 69.2185 5.6082 68.3261 4.71589C67.4338 3.82358 66.3745 3.11575 65.2086 2.63283C64.0428 2.14992 62.7932 1.90137 61.5313 1.90137ZM61.5313 19.1982C60.0109 19.1982 58.5245 18.7474 57.2603 17.9027C55.9961 17.0579 55.0108 15.8574 54.4289 14.4527C53.8471 13.048 53.6948 11.5022 53.9915 10.011C54.2881 8.51976 55.0203 7.14998 56.0954 6.07486C57.1705 4.99975 58.5403 4.26758 60.0315 3.97096C61.5227 3.67434 63.0685 3.82657 64.4732 4.40842C65.8779 4.99027 67.0785 5.97559 67.9232 7.2398C68.7679 8.504 69.2188 9.99025 69.2188 11.5107C69.2188 13.5496 68.4089 15.505 66.9672 16.9467C65.5255 18.3883 63.5701 19.1982 61.5313 19.1982Z" fill="white" />
        <defs>
            <clipPath id="clip0_568_1185">
                <rect width="34" height="34" fill="white" transform="translate(14 17)" />
            </clipPath>
        </defs>
    </svg>
)

export const EARTHsvg = () => (
    <svg width="26" height="26" viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_1202_5053)">
            <g clip-path="url(#clip1_1202_5053)">
                <path d="M29.9711 25.4829L27.3457 22.8574C27.8824 21.6497 28.1383 20.3255 28.0824 18.9662C27.9866 16.6409 26.9911 14.4921 25.2792 12.9155C24.6511 12.337 23.954 11.8615 23.2084 11.4954C23.1163 5.23268 17.994 0.166016 11.7098 0.166016C5.36822 0.166016 0.208984 5.32531 0.208984 11.6669C0.208984 17.9556 5.28271 23.0806 11.5516 23.1656C12.0209 24.1211 12.6683 24.991 13.4748 25.7337C15.0965 27.2271 17.1769 28.0376 19.3683 28.0376C19.4903 28.0376 19.6128 28.0351 19.7354 28.03C20.8486 27.9843 21.9211 27.7316 22.9099 27.2935L25.5353 29.9189C26.1261 30.5097 26.9138 30.835 27.7532 30.835C28.5926 30.835 29.3803 30.5097 29.971 29.9188C30.5618 29.328 30.8871 28.5404 30.8871 27.701C30.8872 26.8615 30.5619 26.0737 29.9711 25.4829ZM20.7686 10.7212C20.1964 10.6298 19.6108 10.5948 19.0186 10.6192C18.5427 10.6388 18.0744 10.6967 17.6167 10.7901C17.5902 10.0721 17.531 9.3673 17.441 8.68285C17.8713 8.82666 18.28 8.98664 18.6606 9.16424C19.6403 9.62142 20.3682 10.1665 20.7686 10.7212ZM20.1936 7.59826C19.9872 7.48466 19.771 7.37489 19.5448 7.26931C18.7776 6.91124 17.9206 6.61248 17.0002 6.37622C16.764 5.45587 16.4653 4.59892 16.1072 3.8316C16.0017 3.60559 15.8919 3.38928 15.7782 3.1828C17.7034 4.10992 19.2667 5.6732 20.1936 7.59826ZM9.20715 4.71594C9.92569 3.17627 10.8613 2.25706 11.7098 2.25706C12.5583 2.25706 13.4938 3.17627 14.2124 4.71594C14.3899 5.09642 14.55 5.50519 14.6938 5.93552C13.7323 5.80903 12.7312 5.74216 11.7098 5.74216C10.6883 5.74216 9.68721 5.80903 8.72576 5.93552C8.86956 5.50519 9.02961 5.09648 9.20715 4.71594ZM2.30002 11.6669C2.30002 10.8183 3.21924 9.88272 4.75891 9.1643C5.13939 8.9867 5.54815 8.82666 5.97849 8.68291C5.852 9.64431 5.78513 10.6455 5.78513 11.6669C5.78513 12.6882 5.852 13.6895 5.97849 14.6509C5.54815 14.5071 5.13945 14.3471 4.75891 14.1695C3.21924 13.451 2.30002 12.5154 2.30002 11.6669ZM3.22589 15.7354C3.43237 15.849 3.64855 15.9588 3.87469 16.0643C4.64194 16.4224 5.4989 16.7211 6.41919 16.9574C6.65545 17.8777 6.95421 18.7347 7.31228 19.502C7.4178 19.7281 7.52757 19.9443 7.64123 20.1508C5.71611 19.2237 4.15283 17.6606 3.22589 15.7354ZM7.31228 3.83178C6.95421 4.59904 6.65557 5.45605 6.41919 6.3764C5.49884 6.61272 4.64188 6.91142 3.87469 7.26949C3.64855 7.37501 3.43243 7.48478 3.22589 7.59844C4.15283 5.6732 5.71611 4.10992 7.64123 3.18304C7.52763 3.38946 7.41774 3.60571 7.31228 3.83178ZM10.6716 19.683C10.6862 20.0386 10.7222 20.3899 10.7783 20.7358C10.2188 20.3378 9.66828 19.6058 9.20715 18.6177C9.02961 18.2372 8.86956 17.8284 8.72576 17.3981C9.41213 17.4884 10.1187 17.5475 10.8387 17.5739C10.6995 18.2593 10.6421 18.9663 10.6716 19.683ZM11.5452 15.4986C10.3722 15.4898 9.23633 15.3877 8.17637 15.2002C7.98007 14.0906 7.87623 12.8982 7.87623 11.6668C7.87623 10.4354 7.98007 9.24303 8.17649 8.13346C9.28606 7.9371 10.4784 7.8332 11.7098 7.8332C12.9412 7.8332 14.1336 7.93704 15.2432 8.13346C15.4305 9.19174 15.5325 10.3256 15.5415 11.4966C14.5839 11.9661 13.7122 12.6143 12.968 13.4224C12.3881 14.0521 11.9116 14.7511 11.5452 15.4986ZM19.6495 25.9407C17.8819 26.0138 16.1925 25.3937 14.8914 24.1955C13.5903 22.9974 12.8337 21.3643 12.761 19.597C12.6883 17.8297 13.308 16.14 14.5062 14.8389C15.7044 13.5378 17.3375 12.7812 19.1047 12.7085C19.198 12.7046 19.2909 12.7027 19.3838 12.7027C21.0491 12.7027 22.6304 13.3188 23.8628 14.4537C25.1639 15.6519 25.9205 17.285 25.9932 19.0522C26.0659 20.8194 25.4462 22.5093 24.248 23.8103C23.0498 25.1113 21.4167 25.8679 19.6495 25.9407ZM28.4925 28.4401C28.0848 28.8477 27.4215 28.8477 27.0139 28.4401L24.755 26.1812C25.12 25.8936 25.4651 25.5754 25.7861 25.2268C25.9427 25.0568 26.0914 24.8815 26.2328 24.7018L28.4924 26.9615C28.6883 27.1574 28.7961 27.4199 28.7961 27.7008C28.7961 27.9817 28.6883 28.2442 28.4925 28.4401Z" fill="#5956E9" />
                <path d="M23.2108 19.3244C23.2108 19.0006 23.1701 18.6863 23.0941 18.3857L24.1254 17.7903L23.0799 15.9794L22.0461 16.5763C21.5968 16.1399 21.0415 15.8127 20.4227 15.637V14.4453H18.3316V15.637C17.7129 15.8127 17.1575 16.1398 16.7082 16.5763L15.6744 15.9794L14.6289 17.7903L15.6602 18.3857C15.5842 18.6862 15.5435 19.0006 15.5435 19.3244C15.5435 19.6483 15.5842 19.9626 15.6602 20.2632L14.6289 20.8586L15.6744 22.6694L16.7082 22.0726C17.1575 22.509 17.7129 22.8362 18.3316 23.0119V24.2035H20.4227V23.0119C21.0415 22.8361 21.5968 22.509 22.0461 22.0726L23.0799 22.6694L24.1254 20.8586L23.0941 20.2632C23.17 19.9625 23.2108 19.6482 23.2108 19.3244ZM19.3772 21.067C18.4163 21.067 17.6346 20.2853 17.6346 19.3244C17.6346 18.3636 18.4163 17.5819 19.3772 17.5819C20.338 17.5819 21.1197 18.3636 21.1197 19.3244C21.1197 20.2852 20.3381 21.067 19.3772 21.067Z" fill="#5956E9" />
            </g>
        </g>
        <defs>
            <clipPath id="clip0_1202_5053">
                <rect width="30.6782" height="30.6782" fill="white" transform="translate(0.208984 0.161133)" />
            </clipPath>
            <clipPath id="clip1_1202_5053">
                <rect width="30.6782" height="30.6782" fill="white" transform="translate(0.208984 0.161133)" />
            </clipPath>
        </defs>
    </svg>
)


export const ICON6svg = ({ width, height }) => (
    <svg width={width || 40} height={height || 40} viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_1002_5754)">
            <path d="M37.8486 0.285156C36.8607 0.285156 36.0599 1.086 36.0599 2.07389V3.86263H34.2712C33.2832 3.86263 32.4824 4.66348 32.4824 5.65137C32.4824 6.63925 33.2832 7.4401 34.2712 7.4401H36.0599V9.22884C36.0599 10.2167 36.8607 11.0176 37.8486 11.0176C38.8366 11.0176 39.6374 10.2167 39.6374 9.22884V7.4401H41.4261C42.414 7.4401 43.2148 6.63925 43.2148 5.65137C43.2148 4.66348 42.414 3.86263 41.4261 3.86263H39.6374V2.07389C39.6374 1.086 38.8366 0.285156 37.8486 0.285156Z" fill="url(#paint0_linear_1002_5754)" />
            <path d="M40.4729 15.6984C40.169 14.7584 39.104 14.3405 38.1953 14.7284C37.2868 15.1163 36.8697 16.1679 37.1542 17.1139C38.0511 20.0935 38.0679 23.2831 37.182 26.2927C36.127 29.8766 33.8572 32.9817 30.7625 35.0747C27.6679 37.1679 23.9412 38.1184 20.2219 37.7635C16.5028 37.4084 13.0231 35.7701 10.3802 33.1294C7.73735 30.4887 6.09603 27.0103 5.73799 23.2916C5.37994 19.5726 6.32751 15.8451 8.41802 12.7486C10.5086 9.6522 13.6117 7.37997 17.1948 6.32195C20.2036 5.43352 23.3933 5.44776 26.3735 6.34208C27.3198 6.626 28.371 6.2079 28.7583 5.29903C29.1453 4.39017 28.7266 3.32548 27.7864 3.02241C24.0328 1.8125 19.987 1.75457 16.1782 2.87921C11.7955 4.17334 7.99994 6.95261 5.44292 10.74C2.88589 14.5274 1.72687 19.0868 2.16481 23.6355C2.60277 28.1843 4.61034 32.4388 7.843 35.6687C11.0757 38.8986 15.3318 40.9027 19.8809 41.3369C24.4301 41.7712 28.9883 40.6083 32.7736 38.0483C36.5589 35.4881 39.3351 31.6902 40.6256 27.3064C41.747 23.4967 41.6858 19.451 40.4729 15.6984Z" fill="url(#paint1_linear_1002_5754)" />
            <path fillRule="evenodd" clipRule="evenodd" d="M16.3828 16.3836C16.3828 14.4078 17.9845 12.8062 19.9603 12.8062H25.3265C26.3144 12.8062 27.1152 13.607 27.1152 14.5949C27.1152 15.5828 26.3144 16.3836 25.3265 16.3836H20.318C20.1204 16.3836 19.9603 16.5438 19.9603 16.7414V19.6034C19.9603 19.801 20.1204 19.9611 20.318 19.9611H23.5378C25.5136 19.9611 27.1152 21.5627 27.1152 23.5386V27.116C27.1152 29.0919 25.5136 30.6935 23.5378 30.6935H19.9603C17.9845 30.6935 16.3828 29.0919 16.3828 27.116V16.3836ZM19.9603 25.3273C19.9603 26.3152 20.7611 27.116 21.749 27.116C22.7369 27.116 23.5378 26.3152 23.5378 25.3273C23.5378 24.3394 22.7369 23.5386 21.749 23.5386C20.7611 23.5386 19.9603 24.3394 19.9603 25.3273Z" fill="url(#paint2_linear_1002_5754)" />
        </g>
        <defs>
            <linearGradient id="paint0_linear_1002_5754" x1="37.8486" y1="0.285156" x2="37.8486" y2="11.0176" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint1_linear_1002_5754" x1="21.7503" y1="2.07373" x2="21.7503" y2="41.426" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint2_linear_1002_5754" x1="21.749" y1="12.8062" x2="21.749" y2="30.6935" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <clipPath id="clip0_1002_5754">
                <rect width="42.9297" height="42.9297" fill="white" transform="translate(0.285156 0.285156)" />
            </clipPath>
        </defs>
    </svg>
)

export const MAPLOCATIONsvg = ({ width, height }) => (
    <svg width={width || 40} height={height || 40} viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22.4158 2.96826C15.708 2.96826 10.3418 8.33447 10.3418 14.9081C10.3418 24.8356 21.0742 33.2874 21.6108 33.5557C21.8792 33.6898 22.1475 33.824 22.4158 33.824C22.6841 33.824 22.9524 33.6898 23.2207 33.5557C23.7573 33.1532 34.4897 24.8356 34.4897 14.9081C34.4897 8.33447 29.1235 2.96826 22.4158 2.96826ZM22.4158 19.0669C20.1351 19.0669 18.3911 17.3229 18.3911 15.0422C18.3911 12.7616 20.1351 11.0176 22.4158 11.0176C24.6964 11.0176 26.4404 12.7616 26.4404 15.0422C26.4404 17.3229 24.6964 19.0669 22.4158 19.0669Z" fill="url(#paint0_linear_1002_5811)" />
        <path d="M41.0634 38.6536L35.6971 27.9211C35.4288 27.3845 35.0264 27.1162 34.4897 27.1162H32.8799C29.6602 31.9458 25.6355 35.1655 24.8306 35.7021C24.1598 36.2388 23.3549 36.5071 22.4158 36.5071C21.4767 36.5071 20.6718 36.2388 20.001 35.7021C19.3302 35.1655 15.1714 31.9458 11.9517 27.1162H10.3418C9.80518 27.1162 9.40271 27.3845 9.1344 27.9211L3.76819 38.6536C3.49988 39.056 3.63404 39.5927 3.76819 39.9951C3.90235 40.3976 4.57312 40.5317 4.97559 40.5317H39.856C40.2584 40.5317 40.795 40.2634 41.0634 39.861C41.3317 39.4585 41.1975 39.056 41.0634 38.6536Z" fill="url(#paint1_linear_1002_5811)" />
        <defs>
            <linearGradient id="paint0_linear_1002_5811" x1="22.4158" y1="2.96826" x2="22.4158" y2="33.824" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint1_linear_1002_5811" x1="22.4158" y1="27.1162" x2="22.4158" y2="40.5317" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
        </defs>
    </svg>
)


export const SUPPORT2svg = ({ width, height, color }) => (
    <svg width={width || 40} height={height || 40} viewBox="0 0 40 44" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M33.0254 29.6719C29.2856 29.6719 26.2539 32.7035 26.2539 36.4433C26.2539 40.1834 29.2856 43.2151 33.0254 43.2151C36.7653 43.2151 39.797 40.1834 39.797 36.4433C39.7968 32.7035 36.7651 29.6719 33.0254 29.6719ZM37.4246 39.7226L36.3654 40.7817C36.2785 40.8689 36.1366 40.8689 36.0496 40.7817L33.9633 38.6953L33.8842 38.7743C33.797 38.8615 33.6555 38.8615 33.5684 38.7743L33.0624 38.2684C32.9755 38.1813 32.9755 38.0396 33.0624 37.9525L33.5811 37.4338L33.1194 36.9721L31.9581 38.1337C32.1739 38.7486 32.0365 39.461 31.5457 39.9517C31.2133 40.284 30.7719 40.4658 30.302 40.4639C30.2617 40.4636 30.2236 40.4473 30.1966 40.4197C30.1829 40.4057 30.1716 40.3894 30.164 40.3709C30.1408 40.3149 30.1537 40.2503 30.1966 40.2077L30.6842 39.7198L30.393 38.6318L29.3052 38.3404L28.8171 38.8283C28.7743 38.8712 28.7097 38.8841 28.6541 38.8608C28.5981 38.8377 28.5614 38.7831 28.5612 38.7229C28.5591 38.253 28.7411 37.8112 29.073 37.4792C29.564 36.9886 30.2763 36.851 30.8913 37.0669L32.0526 35.9054L30.5197 34.3727L29.434 33.6739L28.9497 32.8192L29.462 32.3072L30.3164 32.7915L31.0154 33.8771L32.5481 35.4098L33.5875 34.3705C33.3717 33.7555 33.5091 33.0431 34 32.5522C34.3324 32.2201 34.7737 32.0383 35.2436 32.0402C35.3043 32.0406 35.3586 32.077 35.3817 32.1331C35.4048 32.1894 35.3919 32.2538 35.349 32.2964L34.8612 32.7842L35.1527 33.8723L36.2406 34.1637L36.7287 33.6759C36.7713 33.6329 36.8359 33.62 36.8917 33.6432C36.9476 33.6664 36.9841 33.7208 36.9845 33.7811C36.9867 34.2511 36.8046 34.6928 36.4728 35.0248C35.9818 35.5155 35.2694 35.6532 34.6544 35.4374L33.615 36.4768L34.0764 36.9385L34.595 36.4199C34.6823 36.3326 34.8239 36.3326 34.9112 36.4199L35.4169 36.9256C35.5042 37.0129 35.5042 37.1545 35.4169 37.2417L35.338 37.3205L37.4243 39.4068C37.512 39.4938 37.512 39.6353 37.4246 39.7226Z" fill="url(#paint0_linear_1002_5771)" />
        <path d="M29.4727 28.7869L25.309 27.1267L23.2888 25.425C22.7554 24.974 21.9675 25.0074 21.4728 25.4992L18.6643 28.2915L15.848 25.498C15.3528 25.007 14.566 24.9752 14.0329 25.4247L12.013 27.1264L3.38683 30.5664C0.609191 31.6725 0.611426 41.5092 0.373047 42.4121H26.9121C25.3459 40.9024 24.3686 38.7854 24.3686 36.4429C24.3684 32.9994 26.4782 30.0398 29.4727 28.7869Z" fill="url(#paint1_linear_1002_5771)" />
        <path d="M8.78501 16.5601L10.405 16.556C11.4057 21.7842 14.9567 24.7589 18.6629 24.7589C22.3692 24.7589 24.5224 22.997 25.9503 19.7501C24.8602 20.6053 23.2755 21.3875 21.0593 21.5491C20.8557 21.8524 20.5096 22.0521 20.1167 22.0521H19.0814C18.4547 22.0521 17.9469 21.5442 17.9469 20.9175C17.9469 20.2907 18.4547 19.7828 19.0814 19.7828H20.1167C20.4838 19.7828 20.809 19.9578 21.0164 20.2281C24.5797 19.9421 26.2554 17.781 26.9236 16.5609L28.5408 16.5603C29.5311 16.5603 30.3338 15.7572 30.3338 14.7672V10.7032C30.3338 10.0686 30.0032 9.51204 29.5055 9.19338C28.9065 4.19047 24.2727 0.285156 18.6629 0.285156C13.0531 0.285156 8.41941 4.19047 7.82052 9.19318C7.32283 9.51193 6.99219 10.0684 6.99219 10.703V14.767C6.99198 15.7569 7.79461 16.5601 8.78501 16.5601ZM18.6628 2.31727C23.0781 2.31727 26.7502 5.22903 27.4272 9.02776C27.2156 9.10813 27.0237 9.22681 26.8591 9.37618C25.8018 6.03776 22.7469 4.05329 18.6628 4.05329C14.5788 4.05329 11.5221 6.03664 10.4633 9.37334C10.2996 9.22539 10.1087 9.10752 9.89846 9.02776C10.5755 5.22903 14.2475 2.31727 18.6628 2.31727Z" fill="url(#paint2_linear_1002_5771)" />
        <defs>
            <linearGradient id="paint0_linear_1002_5771" x1="33.0255" y1="29.6719" x2="33.0255" y2="43.2151" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint1_linear_1002_5771" x1="14.9229" y1="25.1074" x2="14.9229" y2="42.4121" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint2_linear_1002_5771" x1="18.663" y1="0.285156" x2="18.663" y2="24.7589" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
        </defs>
    </svg>
)

export const H24svg = ({ width }) => (
    <svg width={width || 40} height={width || 40} viewBox="0 0 44 43" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M25.6352 0.0219727C21.1396 0.0219727 17.0336 1.71904 13.9219 4.50515C14.6121 5.60264 15.0042 6.87951 15.041 8.19754L15.0412 8.20138L15.0413 8.20522C15.057 8.79912 15.0925 9.39317 15.1433 9.9863C17.5035 6.74313 21.3262 4.6294 25.6352 4.6294C32.7876 4.6294 38.6065 10.4483 38.6065 17.6007C38.6065 24.6777 32.9089 30.4453 25.8602 30.5663C26.5038 31.1041 27.1684 31.6168 27.853 32.0981L27.8561 32.1004L27.8593 32.1026C28.7916 32.7602 29.5509 33.6224 30.0837 34.6085C37.6295 32.6333 43.2137 25.7572 43.2137 17.6007C43.2139 7.90773 35.3283 0.0219727 25.6352 0.0219727Z" fill="url(#paint0_linear_1002_5761)" />
        <path d="M25.2047 35.8678C23.1965 34.2647 21.9435 33.1972 20.1505 31.5362C19.6229 31.0475 18.9782 30.7431 18.3161 30.7431C17.8095 30.7431 17.3091 30.9045 16.8972 31.2426C16.4721 31.6004 16.047 31.9582 15.6217 32.3159C12.1663 28.2095 9.6004 23.4244 8.09209 18.2739C8.62532 18.1178 9.1587 17.9617 9.69194 17.8056C10.8671 17.4458 11.42 16.1912 11.2476 14.9335C10.8724 12.1961 10.6686 10.8361 10.4363 8.32676C10.3173 7.04052 9.46126 5.92322 8.2343 5.85641C8.04294 5.83783 7.85173 5.82861 7.66206 5.82861C3.09917 5.82861 -0.873659 11.1173 0.595957 17.5874C2.42464 25.6 6.3835 32.9828 12.0456 38.9399C14.3649 41.3751 17.2063 42.4778 19.7725 42.4778C22.4797 42.4778 24.8808 41.2516 26.0437 39.069C26.6671 38.0101 26.2143 36.6738 25.2047 35.8678Z" fill="url(#paint1_linear_1002_5761)" />
        <path d="M20.3493 17.9261C19.1342 19.0535 18.3391 19.94 17.9192 20.6357C17.6228 21.1264 17.4065 21.6452 17.2762 22.1777C17.2149 22.4283 17.2712 22.6885 17.4308 22.8917C17.5902 23.0945 17.8295 23.211 18.0875 23.211H23.8864C24.5969 23.211 25.175 22.6329 25.175 21.9224C25.175 21.2119 24.5969 20.6339 23.8864 20.6339H21.2677C21.2891 20.6087 21.311 20.5835 21.3335 20.5582C21.5045 20.3656 21.9235 19.9632 22.5785 19.3625C23.2655 18.7321 23.7293 18.26 23.9964 17.9192C24.3989 17.4068 24.6984 16.9066 24.8862 16.4327C25.0778 15.9497 25.175 15.4347 25.175 14.9019C25.175 13.946 24.8288 13.1347 24.1461 12.4899C23.4679 11.8504 22.5343 11.5264 21.3708 11.5264C20.3135 11.5264 19.4155 11.8031 18.702 12.3489C18.2715 12.6785 17.9401 13.1351 17.7168 13.7064C17.566 14.0924 17.605 14.5339 17.8212 14.8873C18.0375 15.2409 18.4128 15.4766 18.8252 15.5178C18.8711 15.5224 18.9169 15.5247 18.962 15.5247C19.5424 15.5247 20.06 15.1556 20.2501 14.6064C20.3097 14.4343 20.3885 14.2974 20.4846 14.1994C20.687 13.9932 20.9596 13.8927 21.3182 13.8927C21.6827 13.8927 21.955 13.9877 22.1503 14.1829C22.3452 14.3778 22.44 14.662 22.44 15.0521C22.44 15.4144 22.3105 15.7922 22.0553 16.1754C21.9184 16.376 21.5095 16.8452 20.3493 17.9261Z" fill="url(#paint2_linear_1002_5761)" />
        <path d="M31.9586 11.5264H30.99C30.714 11.5264 30.4563 11.6624 30.3005 11.8904L25.8209 18.4479C25.6902 18.6393 25.6211 18.8631 25.6211 19.0948V20.1575C25.6211 20.6177 25.9957 20.9923 26.456 20.9923H30.1335V21.8811C30.1335 22.6144 30.7301 23.2111 31.4635 23.2111C32.1968 23.2111 32.7935 22.6144 32.7935 21.8811V20.9923H32.9263C33.6078 20.9923 34.1622 20.4379 34.1622 19.7565C34.1622 19.075 33.6078 18.5206 32.9263 18.5206H32.7935V12.3612C32.7935 11.901 32.4189 11.5264 31.9586 11.5264ZM30.1336 18.5206H28.4829L30.1336 16.0667V18.5206Z" fill="url(#paint3_linear_1002_5761)" />
        <defs>
            <linearGradient id="paint0_linear_1002_5761" x1="28.5678" y1="0.0219727" x2="28.5678" y2="34.6085" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint1_linear_1002_5761" x1="13.3069" y1="5.82861" x2="13.3069" y2="42.4778" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint2_linear_1002_5761" x1="21.2135" y1="11.5264" x2="21.2135" y2="23.211" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint3_linear_1002_5761" x1="29.8916" y1="11.5264" x2="29.8916" y2="23.2111" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
        </defs>
    </svg>
)

export const PERSONWITHSTARSsvg = () => (
    <svg width="40" height="40" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_1002_5777)">
            <path d="M30.9582 33.619L25.0561 32.7614L22.4166 27.4131L19.7771 32.7614L13.875 33.619L18.1457 37.782L17.1376 43.6603L22.4166 40.885L27.6956 43.6603L26.6874 37.782L30.9582 33.619Z" fill="url(#paint0_linear_1002_5777)" />
            <path d="M43.8817 29.8254L38.7719 29.0828L36.4869 24.4526L34.2016 29.0828L29.0918 29.8254L32.7893 33.4296L31.9164 38.5187L36.4869 36.116L41.0572 38.5187L40.1843 33.4296L43.8817 29.8254Z" fill="url(#paint1_linear_1002_5777)" />
            <path d="M15.7411 29.8254L10.6313 29.0828L8.34606 24.4526L6.06098 29.0828L0.951172 29.8254L4.64866 33.4296L3.77581 38.5187L8.34606 36.116L12.9165 38.5187L12.0436 33.4296L15.7411 29.8254Z" fill="url(#paint2_linear_1002_5777)" />
            <path d="M26.8189 16.067C27.5355 15.1324 27.9628 13.9649 27.9628 12.6988C27.9628 9.64042 25.4747 7.15234 22.4164 7.15234C19.358 7.15234 16.8699 9.64042 16.8699 12.6988C16.8699 13.9649 17.2972 15.1324 18.0139 16.067C15.7945 16.8924 14.209 19.032 14.209 21.5354V26.4978H30.6238V21.5354C30.6238 19.032 29.0383 16.8925 26.8189 16.067Z" fill="url(#paint3_linear_1002_5777)" />
            <path d="M23.6869 0.839844H21.1445V5.03379H23.6869V0.839844Z" fill="url(#paint4_linear_1002_5777)" />
            <path d="M14.9285 3.41472L13.1309 5.2124L16.0964 8.17794L17.8941 6.38026L14.9285 3.41472Z" fill="url(#paint5_linear_1002_5777)" />
            <path d="M14.7506 11.4277H10.5566V13.9701H14.7506V11.4277Z" fill="url(#paint6_linear_1002_5777)" />
            <path d="M34.276 11.4277H30.082V13.9701H34.276V11.4277Z" fill="url(#paint7_linear_1002_5777)" />
            <path d="M29.9011 3.41483L26.9355 6.38037L28.7332 8.17805L31.6988 5.21251L29.9011 3.41483Z" fill="url(#paint8_linear_1002_5777)" />
        </g>
        <defs>
            <linearGradient id="paint0_linear_1002_5777" x1="22.4166" y1="27.4131" x2="22.4166" y2="43.6603" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint1_linear_1002_5777" x1="36.4868" y1="24.4526" x2="36.4868" y2="38.5187" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint2_linear_1002_5777" x1="8.34615" y1="24.4526" x2="8.34615" y2="38.5187" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint3_linear_1002_5777" x1="22.4164" y1="7.15234" x2="22.4164" y2="26.4978" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint4_linear_1002_5777" x1="22.4157" y1="0.839844" x2="22.4157" y2="5.03379" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint5_linear_1002_5777" x1="14.0297" y1="4.31356" x2="16.9952" y2="7.2791" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint6_linear_1002_5777" x1="12.6536" y1="11.4277" x2="12.6536" y2="13.9701" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint7_linear_1002_5777" x1="32.179" y1="11.4277" x2="32.179" y2="13.9701" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <linearGradient id="paint8_linear_1002_5777" x1="28.4183" y1="4.8976" x2="30.216" y2="6.69528" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
            <clipPath id="clip0_1002_5777">
                <rect width="42.9297" height="42.9297" fill="white" transform="translate(0.951172 0.785156)" />
            </clipPath>
        </defs>
    </svg>
)

export const DOLLARTICKETsvg = () => (
    <svg width="40" height="40" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M39.9716 22.4309C39.9711 25.9688 38.9216 29.427 36.9558 32.3684C34.9899 35.3098 32.196 37.6022 28.9274 38.9558C25.6587 40.3093 22.0621 40.6633 18.5923 39.9728C15.1225 39.2823 11.9353 37.5785 9.43383 35.0767C6.93236 32.5749 5.2289 29.3875 4.53887 25.9176C3.84884 22.4477 4.20321 18.8511 5.55719 15.5826C6.91116 12.3141 9.20393 9.52054 12.1456 7.55506C15.0872 5.58959 18.5456 4.54053 22.0834 4.54053C26.8273 4.54053 31.3768 6.42485 34.7315 9.77904C38.0861 13.1332 39.971 17.6826 39.9716 22.4264V22.4309ZM33.3514 24.0509L23.1702 13.8743C22.3277 13.1177 21.249 12.6762 20.1177 12.6249L14.164 12.6836C13.687 12.6917 13.2317 12.8847 12.8941 13.2219C12.5565 13.5591 12.3629 14.0141 12.3542 14.4912L12.2842 20.4652C12.3325 21.5965 12.7717 22.6761 13.5269 23.52L23.7057 33.6966C24.0419 34.031 24.4968 34.2188 24.971 34.2188C25.4452 34.2188 25.9002 34.031 26.2363 33.6966L33.3491 26.5793C33.6829 26.2432 33.8702 25.7888 33.8702 25.3151C33.8702 24.8415 33.6829 24.387 33.3491 24.0509H33.3514ZM15.8112 14.9069C15.5636 14.9069 15.3216 14.9803 15.1158 15.1179C14.9099 15.2554 14.7495 15.4509 14.6547 15.6796C14.56 15.9084 14.5352 16.16 14.5835 16.4029C14.6318 16.6457 14.751 16.8687 14.9261 17.0438C15.1011 17.2188 15.3242 17.338 15.567 17.3863C15.8098 17.4346 16.0615 17.4099 16.2902 17.3151C16.5189 17.2204 16.7144 17.0599 16.852 16.8541C16.9895 16.6482 17.0629 16.4062 17.0629 16.1587C17.0629 15.8267 16.931 15.5083 16.6963 15.2735C16.4616 15.0388 16.1432 14.9069 15.8112 14.9069ZM27.7999 28.1406C27.6321 28.3083 27.4045 28.4026 27.1672 28.4026C26.9299 28.4026 26.7024 28.3083 26.5346 28.1406L25.9787 27.5847C25.7453 27.7387 25.497 27.8689 25.2376 27.9734C24.9051 28.1128 24.5476 28.1828 24.187 28.179C24.1102 28.179 24.0334 28.179 23.9543 28.1722C23.7176 28.1551 23.4973 28.0447 23.3421 27.8653C23.1868 27.6858 23.1091 27.452 23.1262 27.2153C23.1433 26.9786 23.2537 26.7584 23.4331 26.6031C23.6126 26.4478 23.8464 26.3701 24.0831 26.3872C24.2353 26.4043 24.3894 26.3849 24.5327 26.3307C24.6891 26.2689 24.8389 26.1917 24.9801 26.1003C25.1235 26.0028 25.2602 25.8956 25.389 25.7794C25.6834 25.4873 25.8921 25.12 25.9923 24.7175C26.0872 24.2678 25.9358 24.1165 25.8635 24.0442C25.7502 23.9336 25.6012 23.8671 25.4433 23.8566C25.2749 23.8461 25.106 23.8644 24.9439 23.9109C24.7799 23.9633 24.6234 24.037 24.4785 24.13C24.3445 24.2175 24.2203 24.319 24.1079 24.4328C23.9216 24.6211 23.7198 24.7934 23.5046 24.9479C23.2127 25.1569 22.8914 25.3214 22.5511 25.436C22.1542 25.5682 21.7337 25.6144 21.3175 25.5716C20.7482 25.5184 20.2145 25.2711 19.8059 24.8711C19.5045 24.558 19.2889 24.1725 19.1801 23.7518C19.0712 23.331 19.0728 22.8893 19.1846 22.4693C19.2833 22.0508 19.4522 21.6521 19.6839 21.2899L19.1416 20.7476C19.0525 20.6661 18.9809 20.5674 18.9309 20.4574C18.881 20.3474 18.8539 20.2285 18.8511 20.1077C18.8484 19.987 18.8701 19.867 18.915 19.7549C18.9599 19.6427 19.0271 19.5409 19.1124 19.4554C19.1977 19.3699 19.2995 19.3026 19.4115 19.2575C19.5235 19.2124 19.6435 19.1904 19.7643 19.193C19.885 19.1955 20.004 19.2224 20.1141 19.2721C20.2241 19.3219 20.323 19.3934 20.4047 19.4823L20.9673 20.0427C21.2126 19.8917 21.4721 19.7653 21.7423 19.6653C22.0925 19.5327 22.4636 19.4638 22.8381 19.462C23.0754 19.4617 23.3031 19.5557 23.4711 19.7232C23.6391 19.8908 23.7337 20.1183 23.734 20.3556C23.7343 20.5929 23.6403 20.8206 23.4727 20.9886C23.3051 21.1566 23.0777 21.2512 22.8404 21.2515C22.6812 21.2531 22.5236 21.2829 22.3749 21.3396C22.211 21.3988 22.0536 21.4745 21.9049 21.5655L21.8598 21.5926C21.742 21.6633 21.6302 21.7434 21.5254 21.8322C21.2385 22.1174 21.0327 22.4738 20.9289 22.8647C20.8855 22.9899 20.8758 23.1243 20.9007 23.2543C20.9257 23.3844 20.9845 23.5057 21.0712 23.6058C21.1904 23.7126 21.3411 23.7776 21.5005 23.7911C21.6643 23.8075 21.8297 23.7898 21.9863 23.7391C22.1561 23.6801 22.3166 23.5972 22.463 23.4929C22.5984 23.3952 22.7254 23.2863 22.8426 23.1675C23.0457 22.9635 23.2699 22.7817 23.5114 22.6252C23.7921 22.4454 24.0952 22.3033 24.4129 22.2027C24.7862 22.091 25.1764 22.0467 25.5653 22.0717C26.1551 22.1113 26.7105 22.3634 27.1288 22.7811C27.424 23.0787 27.6358 23.4488 27.7429 23.8541C27.85 24.2594 27.8486 24.6857 27.7389 25.0903C27.6424 25.523 27.472 25.9358 27.235 26.3104L27.7999 26.8775C27.8832 26.9605 27.9494 27.059 27.9945 27.1676C28.0396 27.2762 28.0628 27.3926 28.0628 27.5102C28.0628 27.6277 28.0396 27.7442 27.9945 27.8527C27.9494 27.9613 27.8832 28.0576 27.7999 28.1406Z" fill="url(#paint0_linear_1002_5807)" />
        <defs>
            <linearGradient id="paint0_linear_1002_5807" x1="22.0834" y1="4.54053" x2="22.0834" y2="40.3168" gradientUnits="userSpaceOnUse">
                <stop stopColor="#497EF7" />
                <stop offset="1" stopColor="#5956E9" />
            </linearGradient>
        </defs>
    </svg>
)

export const ZTECHLOGOsvg = ({ size }) => (
    <svg width={size || 40} height={size || 25} viewBox="0 0 44 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22.3452 28.6651C6.54284 27.9282 7.43668 15.053 9.85888 8.70752C7.52543 10.4269 6.56675 14.9165 6.37909 16.9464C-1.45043 16.9464 -1.04106 28.6651 5.66268 28.6651H22.3452Z" fill="#497EF7" />
        <path d="M37.2481 9.44606C34.4233 2.24086 28.0198 0.934228 25.4782 1.15598C15.7758 20.479 30.3738 26.9644 38.7321 27.7661C48.5164 21.3797 42.4166 10.0601 37.2481 9.44606Z" fill="#497EF7" />
        <path d="M34.5278 28.8695C12.728 26.925 18.05 5.73922 23.935 0.0078125C18.2854 1.07222 15.1673 6.72857 14.3144 9.4237C11.5715 10.0787 10.8175 10.6519 10.7834 10.8565C7.09893 26.6179 25.0778 29.4324 34.5278 28.8695Z" fill="#497EF7" />
        <path d="M32.6057 9.67696C31.1728 5.13277 27.2325 2.80268 25.4414 2.20566C25.4585 2.13743 25.5335 1.90886 25.6973 1.54041C31.4287 0.598821 35.5225 6.57245 36.853 9.67696C40.251 10.3729 42.5674 14.2996 43.3009 16.176C42.1239 18.1717 39.6573 21.0784 39.207 16.7389C38.6441 11.3145 32.5389 9.47227 32.6057 9.67696Z" fill="url(#paint0_linear_936_3613)" />
        <path d="M15.6668 20.5793C10.7542 8.78894 18.7031 2.22511 23.2917 0.416992C17.028 8.07252 18.2937 16.366 19.7095 19.5558C20.3236 21.7392 20.3748 25.0006 15.6668 20.5793Z" fill="#497EF7" />
        <path d="M4.23705 17.5088C4.68738 17.1813 5.89165 17.1677 6.4375 17.2018C6.32007 14.8531 7.97108 11.5065 8.92128 9.93516C7.18289 17.2632 9.27034 22.3362 10.5314 23.9566C11.2648 25.3724 12.0563 27.9789 9.35438 27.0782C5.97694 25.9524 3.67415 17.9182 4.23705 17.5088Z" fill="#497EF7" />
        <defs>
            <linearGradient id="paint0_linear_936_3613" x1="34.3711" y1="1.44092" x2="34.3711" y2="18.9945" gradientUnits="userSpaceOnUse">
                <stop stopColor="white" />
                <stop offset="1" stopColor="#C4C4C4" stop-opacity="0" />
            </linearGradient>
        </defs>
    </svg>
)

export const SERVERSsvg = ({ width }) => (
    <svg width={width || 40} height={width || 40} viewBox="0 0 40 40" fill="#5956e9" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_746_2148)">
            <path d="M6.85899 1.64648H33.2005C33.6371 1.64648 34.0559 1.81994 34.3646 2.12869C34.6734 2.43744 34.8468 2.85619 34.8468 3.29283V9.87819C34.8468 10.3148 34.6734 10.7336 34.3646 11.0423C34.0559 11.3511 33.6371 11.5245 33.2005 11.5245H6.85899C6.42235 11.5245 6.0036 11.3511 5.69485 11.0423C5.3861 10.7336 5.21265 10.3148 5.21265 9.87819V3.29283C5.21265 2.85619 5.3861 2.43744 5.69485 2.12869C6.0036 1.81994 6.42235 1.64648 6.85899 1.64648ZM6.85899 14.8172H33.2005C33.6371 14.8172 34.0559 14.9907 34.3646 15.2994C34.6734 15.6082 34.8468 16.0269 34.8468 16.4636V23.0489C34.8468 23.4856 34.6734 23.9043 34.3646 24.2131C34.0559 24.5218 33.6371 24.6953 33.2005 24.6953H6.85899C6.42235 24.6953 6.0036 24.5218 5.69485 24.2131C5.3861 23.9043 5.21265 23.4856 5.21265 23.0489V16.4636C5.21265 16.0269 5.3861 15.6082 5.69485 15.2994C6.0036 14.9907 6.42235 14.8172 6.85899 14.8172ZM6.85899 27.988H33.2005C33.6371 27.988 34.0559 28.1614 34.3646 28.4702C34.6734 28.7789 34.8468 29.1977 34.8468 29.6343V36.2197C34.8468 36.6563 34.6734 37.0751 34.3646 37.3838C34.0559 37.6926 33.6371 37.866 33.2005 37.866H6.85899C6.42235 37.866 6.0036 37.6926 5.69485 37.3838C5.3861 37.0751 5.21265 36.6563 5.21265 36.2197V29.6343C5.21265 29.1977 5.3861 28.7789 5.69485 28.4702C6.0036 28.1614 6.42235 27.988 6.85899 27.988ZM15.0907 8.23185H16.737V4.93917H15.0907V8.23185ZM15.0907 21.4026H16.737V18.1099H15.0907V21.4026ZM15.0907 34.5733H16.737V31.2806H15.0907V34.5733ZM8.50533 4.93917V8.23185H11.798V4.93917H8.50533ZM8.50533 18.1099V21.4026H11.798V18.1099H8.50533ZM8.50533 31.2806V34.5733H11.798V31.2806H8.50533Z" fill="black" />
            <path d="M6.85899 1.64648H33.2005C33.6371 1.64648 34.0559 1.81994 34.3646 2.12869C34.6734 2.43744 34.8468 2.85619 34.8468 3.29283V9.87819C34.8468 10.3148 34.6734 10.7336 34.3646 11.0423C34.0559 11.3511 33.6371 11.5245 33.2005 11.5245H6.85899C6.42235 11.5245 6.0036 11.3511 5.69485 11.0423C5.3861 10.7336 5.21265 10.3148 5.21265 9.87819V3.29283C5.21265 2.85619 5.3861 2.43744 5.69485 2.12869C6.0036 1.81994 6.42235 1.64648 6.85899 1.64648ZM6.85899 14.8172H33.2005C33.6371 14.8172 34.0559 14.9907 34.3646 15.2994C34.6734 15.6082 34.8468 16.0269 34.8468 16.4636V23.0489C34.8468 23.4856 34.6734 23.9043 34.3646 24.2131C34.0559 24.5218 33.6371 24.6953 33.2005 24.6953H6.85899C6.42235 24.6953 6.0036 24.5218 5.69485 24.2131C5.3861 23.9043 5.21265 23.4856 5.21265 23.0489V16.4636C5.21265 16.0269 5.3861 15.6082 5.69485 15.2994C6.0036 14.9907 6.42235 14.8172 6.85899 14.8172ZM6.85899 27.988H33.2005C33.6371 27.988 34.0559 28.1614 34.3646 28.4702C34.6734 28.7789 34.8468 29.1977 34.8468 29.6343V36.2197C34.8468 36.6563 34.6734 37.0751 34.3646 37.3838C34.0559 37.6926 33.6371 37.866 33.2005 37.866H6.85899C6.42235 37.866 6.0036 37.6926 5.69485 37.3838C5.3861 37.0751 5.21265 36.6563 5.21265 36.2197V29.6343C5.21265 29.1977 5.3861 28.7789 5.69485 28.4702C6.0036 28.1614 6.42235 27.988 6.85899 27.988ZM15.0907 8.23185H16.737V4.93917H15.0907V8.23185ZM15.0907 21.4026H16.737V18.1099H15.0907V21.4026ZM15.0907 34.5733H16.737V31.2806H15.0907V34.5733ZM8.50533 4.93917V8.23185H11.798V4.93917H8.50533ZM8.50533 18.1099V21.4026H11.798V18.1099H8.50533ZM8.50533 31.2806V34.5733H11.798V31.2806H8.50533Z" fill="#5956e9" />
        </g>
        <defs>
            <clipPath id="clip0_746_2148">
                <rect width="39.5122" height="39.5122" fill="white" transform="translate(0.273438)" />
            </clipPath>
        </defs>
    </svg>
)

export const INFOGERANCEsvg = ({ width }) => (
    <svg width={width || 31} height={width || 31} viewBox="0 0 31 30" fill="#5956e9" xmlns="http://www.w3.org/2000/svg">
        <path d="M17 19C19.7614 19 22 16.7614 22 14C22 11.2386 19.7614 9 17 9C14.2386 9 12 11.2386 12 14C12 16.7614 14.2386 19 17 19Z" fill="#5956e9" />
        <path d="M28.5093 5.64049C26.4643 2.7448 23.4167 0.731521 19.9711 0L19.5044 2.27017C22.0613 2.805 24.3687 4.1861 26.0613 6.1949C27.7539 8.2037 28.7352 10.7255 28.8497 13.3611C28.9643 15.9967 28.2056 18.5957 26.6937 20.7466C25.1818 22.8976 23.0032 24.4777 20.5025 25.2369C18.0018 25.9961 15.3219 25.891 12.887 24.9382C10.4521 23.9855 8.40117 22.2395 7.05885 19.9767C5.71654 17.7138 5.1595 15.0632 5.47588 12.4445C5.79225 9.82579 6.96398 7.38846 8.80563 5.51825L7.07723 3.87674C5.26166 5.71976 3.98405 8.03277 3.38479 10.5616C2.78554 13.0904 2.88782 15.7373 3.68041 18.2112C4.473 20.6851 5.92523 22.8904 7.8775 24.5848C9.82978 26.2792 12.2066 27.3971 14.7467 27.8156C17.2868 28.2341 19.892 27.9371 22.2759 26.9572C24.6598 25.9772 26.7303 24.3523 28.2597 22.261C29.7892 20.1696 30.7185 17.6928 30.9454 15.1026C31.1724 12.5125 30.6883 9.90918 29.5464 7.57886C29.226 7.3701 28.9629 7.08299 28.7816 6.74414C28.6004 6.40528 28.5067 6.02567 28.5093 5.64049Z" fill="#5956e9" />
        <path d="M22 29.9387C21.5127 30.0347 21.0109 30.0178 20.5306 29.8893C20.0502 29.7608 19.6031 29.5239 19.2211 29.1955C18.8391 28.8671 18.5316 28.4553 18.3205 27.9893C18.1094 27.5234 18 27.0149 18 26.5C18 25.9851 18.1094 25.4766 18.3205 25.0107C18.5316 24.5447 18.8391 24.1329 19.2211 23.8045C19.6031 23.4761 20.0502 23.2392 20.5306 23.1107C21.0109 22.9822 21.5127 22.9653 22 23.0612" fill="#5956e9" />
        <path d="M21 29.9387C21.4873 30.0347 21.9891 30.0178 22.4694 29.8893C22.9498 29.7608 23.3969 29.5239 23.7789 29.1955C24.1609 28.8671 24.4684 28.4553 24.6795 27.9893C24.8906 27.5234 25 27.0149 25 26.5C25 25.9851 24.8906 25.4766 24.6795 25.0107C24.4684 24.5447 24.1609 24.1329 23.7789 23.8045C23.3969 23.4761 22.9498 23.2392 22.4694 23.1107C21.9891 22.9822 21.4873 22.9653 21 23.0612" fill="#5956e9" />
        <path d="M4 15.9388C3.51265 16.0347 3.01092 16.0178 2.53058 15.8893C2.05024 15.7608 1.60312 15.5239 1.2211 15.1955C0.839087 14.8671 0.53158 14.4553 0.320508 13.9893C0.109437 13.5234 0 13.0149 0 12.5C0 11.9851 0.109437 11.4766 0.320508 11.0107C0.53158 10.5447 0.839087 10.1329 1.2211 9.80447C1.60312 9.47606 2.05024 9.23917 2.53058 9.1107C3.01092 8.98224 3.51265 8.96535 4 9.06125" fill="#5956e9" />
        <path d="M3 15.9388C3.48735 16.0347 3.98908 16.0178 4.46942 15.8893C4.94976 15.7608 5.39688 15.5239 5.7789 15.1955C6.16091 14.8671 6.46842 14.4553 6.67949 13.9893C6.89056 13.5234 7 13.0149 7 12.5C7 11.9851 6.89056 11.4766 6.67949 11.0107C6.46842 10.5447 6.16091 10.1329 5.7789 9.80447C5.39688 9.47606 4.94976 9.23917 4.46942 9.1107C3.98908 8.98224 3.48735 8.96535 3 9.06125" fill="#5956e9" />
    </svg>
)

export const CLOUDSERVERsvg = () => (
    <svg width="37" height="34" viewBox="0 0 37 34" fill="#5956e9" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.13423 7.24325V7.2102H2.10117C1.60223 7.2102 1.20387 7.0444 0.837223 6.71109L0.8371 6.71098C0.504907 6.412 0.306491 6.0145 0.306491 5.5509L0.306491 1.7262C0.306491 1.26145 0.471941 0.864235 0.837222 0.532162L0.837247 0.532189L0.83836 0.531076C1.17001 0.199431 1.60131 0.0330537 2.10117 0.0330537L25.3202 0.0330537C25.8191 0.0330537 26.2175 0.198849 26.5841 0.532162C26.9494 0.864235 27.1149 1.26145 27.1149 1.7262V5.58475C27.1149 6.01527 26.9497 6.41261 26.5841 6.74494L26.5841 6.74491L26.583 6.74602C26.2514 7.07767 25.8201 7.24404 25.3202 7.24404H2.13423V7.24325ZM2.13423 16.6527V16.6197H2.10117C1.60223 16.6197 1.20387 16.4539 0.837223 16.1205L0.8371 16.1204C0.505487 15.822 0.306491 15.3909 0.306491 14.9265L0.306491 11.1018C0.306491 10.6713 0.471664 10.2739 0.837222 9.94162L0.837247 9.94165L0.83836 9.94053C1.17001 9.60889 1.60131 9.44251 2.10117 9.44251H25.3202C25.8191 9.44251 26.2175 9.6083 26.5841 9.94162C26.95 10.2742 27.1149 10.6378 27.1149 11.1018V14.9604C27.1149 15.4251 26.9494 15.8223 26.5841 16.1544L26.5841 16.1544L26.583 16.1555C26.2514 16.4871 25.8201 16.6535 25.3202 16.6535H2.13423V16.6527ZM1.96892 18.8858L15.9646 18.9194C15.3575 19.3225 14.849 20.1464 14.4635 21.2961C12.9081 21.7049 11.4878 22.4843 10.3372 23.5673L10.3372 23.5673C9.14585 24.6905 8.53324 26.0866 8.46522 27.787C8.39774 29.5078 8.9663 31.3596 10.1336 33.4077H1.96579C1.56713 33.4077 1.23725 33.2834 0.9776 33.032C0.717824 32.7805 0.525718 32.3992 0.408032 31.8801L0.408032 20.4436C0.408032 20.0282 0.540668 19.69 0.800284 19.4304C1.0601 19.1706 1.44981 18.9867 1.96892 18.8858ZM33.2412 24.2675L33.2407 24.2885L33.2594 24.2978C35.5537 25.4449 36.6936 27.0262 36.6936 28.973C36.6936 29.9646 36.408 30.8203 35.8334 31.5417C35.2594 32.2624 34.3953 32.851 33.2341 33.3062H14.1925C12.9812 32.6826 12.0829 31.934 11.4872 31.0613C10.8904 30.1872 10.596 29.1869 10.596 28.0591C10.596 26.9287 10.7901 26.021 11.1756 25.3337C11.5607 24.6473 12.1378 24.1787 12.9086 23.9274C13.686 23.6739 14.3609 23.5052 14.9295 23.4252C15.4986 23.3451 15.9587 23.3541 16.3078 23.4539L16.3478 23.4653L16.3499 23.4237C16.4005 22.4121 16.628 21.6061 17.0215 21.0075C17.414 20.4104 17.973 20.0176 18.6919 19.8331C20.4801 19.6648 21.7542 19.9697 22.5901 20.7387L22.6122 20.7591L22.6346 20.7389C25.0659 18.5439 27.4561 17.8742 29.7759 18.782L29.7762 18.7821C30.9724 19.237 31.8546 19.9183 32.4297 20.8293C33.005 21.7405 33.2749 22.8844 33.2412 24.2675ZM4.16584 1.69314C3.64962 1.69314 3.23497 1.86568 2.89013 2.21053C2.54505 2.5556 2.37274 3.0042 2.37274 3.48624C2.37274 3.96896 2.54553 4.3835 2.89013 4.7281C3.2352 5.07317 3.6838 5.24549 4.16584 5.24549C4.64856 5.24549 5.0631 5.0727 5.4077 4.7281C5.7523 4.3835 5.92509 3.96896 5.92509 3.48624C5.92509 2.97001 5.75254 2.55537 5.4077 2.21053C5.0973 1.90012 4.64904 1.69314 4.16584 1.69314ZM4.16584 11.2041C3.64962 11.2041 3.23497 11.3767 2.89013 11.7215C2.54505 12.0666 2.37274 12.5152 2.37274 12.9972C2.37274 13.48 2.54553 13.8945 2.89013 14.2391C3.2352 14.5842 3.6838 14.7565 4.16584 14.7565C4.64856 14.7565 5.0631 14.5837 5.4077 14.2391C5.75277 13.894 5.92509 13.4454 5.92509 12.9634C5.92509 12.4807 5.7523 12.0661 5.4077 11.7215C5.0973 11.4111 4.64904 11.2041 4.16584 11.2041Z" fill="#939393" />
        <path d="M2.13423 7.24325V7.2102H2.10117C1.60223 7.2102 1.20387 7.0444 0.837223 6.71109L0.8371 6.71098C0.504907 6.412 0.306491 6.0145 0.306491 5.5509L0.306491 1.7262C0.306491 1.26145 0.471941 0.864235 0.837222 0.532162L0.837247 0.532189L0.83836 0.531076C1.17001 0.199431 1.60131 0.0330537 2.10117 0.0330537L25.3202 0.0330537C25.8191 0.0330537 26.2175 0.198849 26.5841 0.532162C26.9494 0.864235 27.1149 1.26145 27.1149 1.7262V5.58475C27.1149 6.01527 26.9497 6.41261 26.5841 6.74494L26.5841 6.74491L26.583 6.74602C26.2514 7.07767 25.8201 7.24404 25.3202 7.24404H2.13423V7.24325ZM2.13423 16.6527V16.6197H2.10117C1.60223 16.6197 1.20387 16.4539 0.837223 16.1205L0.8371 16.1204C0.505487 15.822 0.306491 15.3909 0.306491 14.9265L0.306491 11.1018C0.306491 10.6713 0.471664 10.2739 0.837222 9.94162L0.837247 9.94165L0.83836 9.94053C1.17001 9.60889 1.60131 9.44251 2.10117 9.44251H25.3202C25.8191 9.44251 26.2175 9.6083 26.5841 9.94162C26.95 10.2742 27.1149 10.6378 27.1149 11.1018V14.9604C27.1149 15.4251 26.9494 15.8223 26.5841 16.1544L26.5841 16.1544L26.583 16.1555C26.2514 16.4871 25.8201 16.6535 25.3202 16.6535H2.13423V16.6527ZM1.96892 18.8858L15.9646 18.9194C15.3575 19.3225 14.849 20.1464 14.4635 21.2961C12.9081 21.7049 11.4878 22.4843 10.3372 23.5673L10.3372 23.5673C9.14585 24.6905 8.53324 26.0866 8.46522 27.787C8.39774 29.5078 8.9663 31.3596 10.1336 33.4077H1.96579C1.56713 33.4077 1.23725 33.2834 0.9776 33.032C0.717824 32.7805 0.525718 32.3992 0.408032 31.8801L0.408032 20.4436C0.408032 20.0282 0.540668 19.69 0.800284 19.4304C1.0601 19.1706 1.44981 18.9867 1.96892 18.8858ZM33.2412 24.2675L33.2407 24.2885L33.2594 24.2978C35.5537 25.4449 36.6936 27.0262 36.6936 28.973C36.6936 29.9646 36.408 30.8203 35.8334 31.5417C35.2594 32.2624 34.3953 32.851 33.2341 33.3062H14.1925C12.9812 32.6826 12.0829 31.934 11.4872 31.0613C10.8904 30.1872 10.596 29.1869 10.596 28.0591C10.596 26.9287 10.7901 26.021 11.1756 25.3337C11.5607 24.6473 12.1378 24.1787 12.9086 23.9274C13.686 23.6739 14.3609 23.5052 14.9295 23.4252C15.4986 23.3451 15.9587 23.3541 16.3078 23.4539L16.3478 23.4653L16.3499 23.4237C16.4005 22.4121 16.628 21.6061 17.0215 21.0075C17.414 20.4104 17.973 20.0176 18.6919 19.8331C20.4801 19.6648 21.7542 19.9697 22.5901 20.7387L22.6122 20.7591L22.6346 20.7389C25.0659 18.5439 27.4561 17.8742 29.7759 18.782L29.7762 18.7821C30.9724 19.237 31.8546 19.9183 32.4297 20.8293C33.005 21.7405 33.2749 22.8844 33.2412 24.2675ZM4.16584 1.69314C3.64962 1.69314 3.23497 1.86568 2.89013 2.21053C2.54505 2.5556 2.37274 3.0042 2.37274 3.48624C2.37274 3.96896 2.54553 4.3835 2.89013 4.7281C3.2352 5.07317 3.6838 5.24549 4.16584 5.24549C4.64856 5.24549 5.0631 5.0727 5.4077 4.7281C5.7523 4.3835 5.92509 3.96896 5.92509 3.48624C5.92509 2.97001 5.75254 2.55537 5.4077 2.21053C5.0973 1.90012 4.64904 1.69314 4.16584 1.69314ZM4.16584 11.2041C3.64962 11.2041 3.23497 11.3767 2.89013 11.7215C2.54505 12.0666 2.37274 12.5152 2.37274 12.9972C2.37274 13.48 2.54553 13.8945 2.89013 14.2391C3.2352 14.5842 3.6838 14.7565 4.16584 14.7565C4.64856 14.7565 5.0631 14.5837 5.4077 14.2391C5.75277 13.894 5.92509 13.4454 5.92509 12.9634C5.92509 12.4807 5.7523 12.0661 5.4077 11.7215C5.0973 11.4111 4.64904 11.2041 4.16584 11.2041Z" stroke="url(#paint0_linear_746_2128)" strokeWidth="0.0661073" />
        <path d="M2.13423 7.24325V7.2102H2.10117C1.60223 7.2102 1.20387 7.0444 0.837223 6.71109L0.8371 6.71098C0.504907 6.412 0.306491 6.0145 0.306491 5.5509L0.306491 1.7262C0.306491 1.26145 0.471941 0.864235 0.837222 0.532162L0.837247 0.532189L0.83836 0.531076C1.17001 0.199431 1.60131 0.0330537 2.10117 0.0330537L25.3202 0.0330537C25.8191 0.0330537 26.2175 0.198849 26.5841 0.532162C26.9494 0.864235 27.1149 1.26145 27.1149 1.7262V5.58475C27.1149 6.01527 26.9497 6.41261 26.5841 6.74494L26.5841 6.74491L26.583 6.74602C26.2514 7.07767 25.8201 7.24404 25.3202 7.24404H2.13423V7.24325ZM2.13423 16.6527V16.6197H2.10117C1.60223 16.6197 1.20387 16.4539 0.837223 16.1205L0.8371 16.1204C0.505487 15.822 0.306491 15.3909 0.306491 14.9265L0.306491 11.1018C0.306491 10.6713 0.471664 10.2739 0.837222 9.94162L0.837247 9.94165L0.83836 9.94053C1.17001 9.60889 1.60131 9.44251 2.10117 9.44251H25.3202C25.8191 9.44251 26.2175 9.6083 26.5841 9.94162C26.95 10.2742 27.1149 10.6378 27.1149 11.1018V14.9604C27.1149 15.4251 26.9494 15.8223 26.5841 16.1544L26.5841 16.1544L26.583 16.1555C26.2514 16.4871 25.8201 16.6535 25.3202 16.6535H2.13423V16.6527ZM1.96892 18.8858L15.9646 18.9194C15.3575 19.3225 14.849 20.1464 14.4635 21.2961C12.9081 21.7049 11.4878 22.4843 10.3372 23.5673L10.3372 23.5673C9.14585 24.6905 8.53324 26.0866 8.46522 27.787C8.39774 29.5078 8.9663 31.3596 10.1336 33.4077H1.96579C1.56713 33.4077 1.23725 33.2834 0.9776 33.032C0.717824 32.7805 0.525718 32.3992 0.408032 31.8801L0.408032 20.4436C0.408032 20.0282 0.540668 19.69 0.800284 19.4304C1.0601 19.1706 1.44981 18.9867 1.96892 18.8858ZM33.2412 24.2675L33.2407 24.2885L33.2594 24.2978C35.5537 25.4449 36.6936 27.0262 36.6936 28.973C36.6936 29.9646 36.408 30.8203 35.8334 31.5417C35.2594 32.2624 34.3953 32.851 33.2341 33.3062H14.1925C12.9812 32.6826 12.0829 31.934 11.4872 31.0613C10.8904 30.1872 10.596 29.1869 10.596 28.0591C10.596 26.9287 10.7901 26.021 11.1756 25.3337C11.5607 24.6473 12.1378 24.1787 12.9086 23.9274C13.686 23.6739 14.3609 23.5052 14.9295 23.4252C15.4986 23.3451 15.9587 23.3541 16.3078 23.4539L16.3478 23.4653L16.3499 23.4237C16.4005 22.4121 16.628 21.6061 17.0215 21.0075C17.414 20.4104 17.973 20.0176 18.6919 19.8331C20.4801 19.6648 21.7542 19.9697 22.5901 20.7387L22.6122 20.7591L22.6346 20.7389C25.0659 18.5439 27.4561 17.8742 29.7759 18.782L29.7762 18.7821C30.9724 19.237 31.8546 19.9183 32.4297 20.8293C33.005 21.7405 33.2749 22.8844 33.2412 24.2675ZM4.16584 1.69314C3.64962 1.69314 3.23497 1.86568 2.89013 2.21053C2.54505 2.5556 2.37274 3.0042 2.37274 3.48624C2.37274 3.96896 2.54553 4.3835 2.89013 4.7281C3.2352 5.07317 3.6838 5.24549 4.16584 5.24549C4.64856 5.24549 5.0631 5.0727 5.4077 4.7281C5.7523 4.3835 5.92509 3.96896 5.92509 3.48624C5.92509 2.97001 5.75254 2.55537 5.4077 2.21053C5.0973 1.90012 4.64904 1.69314 4.16584 1.69314ZM4.16584 11.2041C3.64962 11.2041 3.23497 11.3767 2.89013 11.7215C2.54505 12.0666 2.37274 12.5152 2.37274 12.9972C2.37274 13.48 2.54553 13.8945 2.89013 14.2391C3.2352 14.5842 3.6838 14.7565 4.16584 14.7565C4.64856 14.7565 5.0631 14.5837 5.4077 14.2391C5.75277 13.894 5.92509 13.4454 5.92509 12.9634C5.92509 12.4807 5.7523 12.0661 5.4077 11.7215C5.0973 11.4111 4.64904 11.2041 4.16584 11.2041Z" stroke="black" stroke-opacity="0.2" strokeWidth="0.0661073" />
        <defs>
            <linearGradient id="paint0_linear_746_2128" x1="5.48104" y1="0.0146918" x2="36.3728" y2="34.6379" gradientUnits="userSpaceOnUse">
                <stop stopColor="#3D71E9" />
                <stop offset="1" stopColor="#59A7F7" />
            </linearGradient>
        </defs>
    </svg>
)