import apiService from '../lib/apiService';

const authService = {
    register: (data) => apiService.post('/auth/register', data, { withCredentials: true }),

    cartRegister: (data) => apiService.post('/auth/cartRegister', data, { withCredentials: true }),

    login: (data) => apiService.post('/auth/login', data, { withCredentials: true }),

    checkAuth: () => apiService.get(`/auth/checkAuth`, { withCredentials: true }),

    logout: () => apiService.post('/auth/logout', { withCredentials: true }),

    refreshToken: () => apiService.post('/auth/refresh-token', {}, { withCredentials: true }),

    forgotPassword: (data) => apiService.post('/auth/forgot-password', data, { withCredentials: true }),

    resetPassword: (data) => apiService.post('/auth/reset-password', data, { withCredentials: true }),
};

export default authService;
