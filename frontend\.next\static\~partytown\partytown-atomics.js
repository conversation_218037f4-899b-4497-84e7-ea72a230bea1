/* Partytown 0.10.3-dev1734732935152 - MIT builder.io */
(e=>{const t=()=>{},r=e=>e.length,n=e=>{var t,r,n;try{const r=null===(t=null==e?void 0:e.constructor)||void 0===t?void 0:t.name;if(r)return r}catch(e){}try{const t=null===(n=null===(r=null==e?void 0:e.__zone_symbol__originalInstance)||void 0===r?void 0:r.constructor)||void 0===n?void 0:n.name;if(t)return t}catch(e){}return""},i=(e,t)=>e.startsWith(t),s=e=>!(i(e,"webkit")||i(e,"toJSON")||i(e,"constructor")||i(e,"toString")||i(e,"_")),o=e=>11===e.nodeType&&e.host?"#s":e.nodeName,a=()=>Math.round(Math.random()*Number.MAX_SAFE_INTEGER).toString(36),c={Anchor:"a",DList:"dl",Image:"img",OList:"ol",Paragraph:"p",Quote:"q",TableCaption:"caption",TableCell:"td",TableCol:"colgroup",TableRow:"tr",TableSection:"tbody",UList:"ul"},l={Graphics:"g",SVG:"svg"},u={preserveBehavior:!1},p=Object.freeze((e=>{const t=new Set;let r=[];do{Object.getOwnPropertyNames(r).forEach((e=>{"function"==typeof r[e]&&t.add(e)}))}while((r=Object.getPrototypeOf(r))!==Object.prototype);return Array.from(t)})()),d=Symbol(),h=Symbol(),g=new Map,m=new Map,f={},y=new WeakMap,w=(e,t)=>{if(e)return(t=y.get(e))||(t=e[d])||v(e,t=a()),t},$=(e,t,r,n,i)=>{if((r=f[e])&&r.U){if(e===t)return r.U;if(n=r.U.document,"d"===(i=t.split(".").pop()))return n;if("e"===i)return n.documentElement;if("h"===i)return n.head;if("b"===i)return n.body}return g.get(t)},v=(e,t,r)=>{e&&(g.set(t,e),e[d]=t,e[h]=r=Date.now(),r>b+5e3&&(g.forEach(((e,t)=>{e[h]<b&&e.nodeType&&!e.isConnected&&g.delete(t)})),b=r))};let b=0;const S=e.parent,E=document.implementation.createHTMLDocument(),T=S.partytown||{},I=(T.lib||"/~partytown/")+"",R=(e,t,r)=>{const n=((e,t)=>{return r=e,n={value:t},Object.defineProperty(r,"name",{...n,configurable:!0});var r,n})(class extends f[e].U.HTMLElement{},r[0]);return"connectedCallback,disconnectedCallback,attributeChangedCallback,adoptedCallback".split(",").map((r=>n.prototype[r]=function(...n){t.postMessage([15,e,w(this),r,n])})),n.observedAttributes=r[1],n},M=(e,t,r,s,a,c)=>void 0!==t&&(s=typeof t)?"string"===s||"number"===s||"boolean"===s||null==t?[0,t]:"function"===s?[6]:(r=r||new Set)&&Array.isArray(t)?r.has(t)?[1,[]]:r.add(t)&&[1,t.map((t=>M(e,t,r)))]:"object"===s?A(t)?[14,{name:t.name,message:t.message,stack:t.stack}]:""===(a=n(t))?[2,{}]:"Window"===a?[3,[e,e]]:"HTMLCollection"===a||"NodeList"===a?[7,Array.from(t).map((t=>M(e,t,r)[1]))]:a.endsWith("Event")?[5,x(e,t,r)]:"CSSRuleList"===a?[12,Array.from(t).map(N)]:i(a,"CSS")&&a.endsWith("Rule")?[11,N(t)]:"CSSStyleDeclaration"===a?[13,x(e,t,r)]:"Attr"===a?[10,[t.name,t.value]]:t.nodeType?[3,[e,w(t),o(t),c]]:[2,x(e,t,r,!0,!0)]:void 0:t,x=(e,t,r,i,o,a,c,l)=>{if(a={},!r.has(t))for(c in r.add(t),t)s(c)&&(l="path"===c&&n(t).endsWith("Event")?t.composedPath():t[c],(i||"function"!=typeof l)&&(o||""!==l)&&(a[c]=M(e,l,r)));return a},N=e=>{let t,r={};for(t in e)U.includes(t)&&(r[t]=String(e[t]));return r};let L=null;const A=t=>{var r;return L=(null===(r=e.top)||void 0===r?void 0:r.Error)||L,t instanceof L},C=(t,r,n,i)=>r?(n=r[0],i=r[1],0===n?i:4===n?O(t,i):1===n?i.map((e=>C(t,e))):3===n?$(i[0],i[1]):5===n?j(P(t,i)):2===n?P(t,i):8===n?i:9===n?new e[r[2]](i):void 0):void 0,O=(e,{V:t,w:r,L:n},i)=>((i=m.get(n))||(i=function(...i){e.postMessage([9,{V:t,w:r,L:n,R:M(t,this),b:M(t,i)}])},m.set(n,i)),i),j=e=>new("detail"in e?CustomEvent:Event)(e.type,e),P=(e,t,r,n)=>{for(n in r={},t)r[n]=C(e,t[n]);return r},U="cssText,selectorText,href,media,namespaceURI,prefix,name,conditionText".split(","),H=async(e,t)=>{let n,i,s,o,a,c,l={F:t.F},u=r(t.Q),p=0;for(;p<u;p++)try{c=p===u-1,n=t.Q[p],i=n.V,s=n.a,!f[i]&&i.startsWith("f_")&&await new Promise((e=>{let t=0,r=()=>{f[i]||t++>1e3?e():requestAnimationFrame(r)};r()})),1===s[0]&&s[1]in f[i].U?v(new f[i].U[s[1]](...C(e,s[2])),n.w):(o=$(i,n.w),o?(a=_(e,i,o,s,c,n.r),n.d&&("string"==typeof n.d?v(a,n.d):f[n.d.V]={V:n.d.V,U:{document:a}}),"object"==typeof(d=a)&&d&&d.then&&(a=await a,c&&(l.z=!0)),c&&(l.M=M(i,a,void 0,void 0,void 0,n.w))):l.p=n.w+" not found")}catch(e){c?l.p=String(e.stack||e):console.error(e)}var d;return l},_=(e,t,i,s,o,a)=>{let c,l,u,p,d,h=0,g=r(s);for(;h<g;h++){l=s[h],c=s[h+1],u=s[h-1];try{if(!Array.isArray(c))if("string"==typeof l||"number"==typeof l){if(h+1===g&&a)return d={},a.map((e=>d[e]=i[e])),d;i=i[l]}else{if(0===c)return void(i[u]=C(e,l));if("function"==typeof i[u]&&(p=C(e,l),"define"===u&&"CustomElementRegistry"===n(i)&&(p[1]=R(t,e,p[1])),"insertRule"===u&&p[1]>r(i.cssRules)&&(p[1]=r(i.cssRules)),i=i[u].apply(i,p),"play"===u))return Promise.resolve()}}catch(e){if(o)throw e;console.debug(e)}}return i},k=(e,t)=>{let n,i,s,o=t.V,a=t.U,c=a.document,l='script[type="text/partytown"]:not([data-ptid]):not([data-pterror])',d=l+":not([async]):not([defer])";c&&c.body?(n=c.querySelector(d),n||(n=c.querySelector(l)),n?(n.dataset.ptid=i=w(n,o),s={V:o,w:i},n.src?(s.S=n.src,s.H=n.dataset.ptsrc||n.src):s.j=n.innerHTML,e.postMessage([7,s])):(t.x||(t.x=1,((e,t,n)=>{let i,s,o=n._ptf,a=(n.partytown||{}).forward||[],c=(r,n)=>e.postMessage([10,{V:t,q:r,b:M(t,Array.from(n))}]);if(n._ptf=void 0,a.map((e=>{const[t,{preserveBehavior:i}]=(e=>{if("string"==typeof e)return[e,u];const[t,r=u]=e;return[t,{...u,...r}]})(e);s=n,t.split(".").map(((e,t,o)=>{var a;s=s[o[t]]=t+1<r(o)?s[o[t]]||(a=o[t+1],p.includes(a)?[]:{}):(()=>{let e=null;if(i){const{methodOrProperty:t,thisObject:r}=((e,t)=>{let r=e;for(let e=0;e<t.length-1;e+=1)r=r[t[e]];return{thisObject:r,methodOrProperty:t.length>0?r[t[t.length-1]]:void 0}})(n,o);"function"==typeof t&&(e=(...e)=>t.apply(r,...e))}return(...t)=>{let r;return e&&(r=e(t)),c(o,t),r}})()}))})),o)for(i=0;i<r(o);i+=2)c(o[i],o[i+1])})(e,o,a),c.dispatchEvent(new CustomEvent("pt0"))),e.postMessage([8,o]))):requestAnimationFrame((()=>k(e,t)))},B=(e,t,r)=>{if(!y.has(r)){y.set(r,t);const n=r.document,i=r.history,s=y.get(r.parent);let o=!1;const a=[],c=e=>{o?e():a.push(e)},l=()=>{e.postMessage([5,{V:t,J:s,S:n.baseURI,T:n.visibilityState}]),setTimeout((()=>{o=!0,a.forEach((e=>{e()}))}))},u=i.pushState.bind(i),p=i.replaceState.bind(i),d=(r,i,s,o)=>()=>{e.postMessage([13,{V:t,type:r,state:i,url:n.baseURI,newUrl:s,oldUrl:o}])};i.pushState=(e,t,r)=>{u(e,t,r),c(d(0,e,null==r?void 0:r.toString()))},i.replaceState=(e,t,r)=>{p(e,t,r),c(d(1,e,null==r?void 0:r.toString()))},r.addEventListener("popstate",(e=>{c(d(2,e.state))})),r.addEventListener("hashchange",(e=>{c(d(3,{},e.newURL,e.oldURL))})),r.addEventListener("ptupdate",(()=>{k(e,f[t])})),n.addEventListener("visibilitychange",(()=>e.postMessage([14,t,n.visibilityState]))),f[t]={V:t,U:r},"complete"===n.readyState?l():r.addEventListener("load",l)}},V=()=>{const e=Object.getOwnPropertyNames(S).map((e=>((e,t,r,n)=>{if(r=t.match(/^(HTML|SVG)(.+)Element$/))return n=r[2],"S"==t[0]?e.createElementNS("http://www.w3.org/2000/svg",l[n]||n.slice(0,2).toLowerCase()+n.slice(2)):e.createElement(c[n]||n)})(E,e))).filter((e=>e)).map((e=>[e]));return D(e,[])},D=(e,t)=>{const r=new Set(["Object"]);return e.filter((e=>e[0])).map((e=>{const t=e[0],r=e[1],i=n(t);return[i,S[i].prototype,t,r]})).map((([e,n,i,s])=>F(r,t,e,n,i,s))),t},W=(e,t,r)=>{let n=[],i=[e,"Object",n];for(r in t)J(n,t,r);return i},F=(e,t,r,i,s,a)=>{if(!e.has(r)){e.add(r);const c=Object.getPrototypeOf(i),l=n(c),u=[],p=Object.getOwnPropertyDescriptors(i);F(e,t,l,c,s,a);for(const e in p)J(u,s,e);t.push([r,l,u,a,o(s)])}},J=(e,t,r,i,o,a)=>{try{s(r)&&isNaN(r[0])&&"all"!==r&&("function"==(o=typeof(i=t[r]))?(String(i).includes("[native")||Object.getPrototypeOf(t)[r])&&e.push([r,5]):"object"===o&&null!=i?"Object"!==(a=n(i))&&"Function"!==a&&self[a]&&e.push([r,i.nodeType||a]):"symbol"!==o&&(r.toUpperCase()===r?e.push([r,6,i]):e.push([r,6])))}catch(e){console.warn(e)}},q=(e,r)=>void 0!==e[r]?new e[r](t):0;let G;(async e=>{const t=new SharedArrayBuffer(1073741824),r=new Int32Array(t);return(n,i)=>{const s=i[0],o=i[1];if(0===s){const e=(()=>{const e=E.createElement("i"),t=E.createTextNode(""),r=E.createComment(""),n=E.createDocumentFragment(),i=E.createElement("p").attachShadow({mode:"open"}),s=q(S,"IntersectionObserver"),o=q(S,"MutationObserver"),a=q(S,"ResizeObserver"),c=S.performance,l=S.screen,u=[[S.history],[c],[c.navigation],[c.timing],[l],[l.orientation],[S.visualViewport],[s,12],[o,12],[a,12],[t],[r],[n],[i],[e],[e.attributes],[e.classList],[e.dataset],[e.style],[E],[E.doctype]],p=[W("Window",S),W("Node",t)],d=function(e){return JSON.stringify(e,((e,t)=>("function"==typeof t&&(t=String(t)).startsWith(e+"(")&&(t="function "+t),"loadScriptsOnMainThread"===e&&(t=t.map((e=>Array.isArray(e)?e:["string"==typeof e?"string":"regexp","string"==typeof e?e:e.source]))),t)))}(T),h={i:d,v:D(u,p),C:new URL(I,S.location)+"",I:origin,$tabId$:S._pttab};return((e,t,r)=>{void 0!==t[r]&&e.push([r,"Object",Object.keys(t[r].prototype).map((e=>[e,6])),12])})(h.v,S,"IntersectionObserverEntry"),h})();e.P=t,n.postMessage([1,e])}else 2===i[0]?n.postMessage([3,V()]):11===s?e(o,(e=>{const t=JSON.stringify(e),n=t.length;for(let e=0;e<n;e++)r[e+1]=t.charCodeAt(e);r[0]=n,Atomics.notify(r,0)})):((e,t,r)=>{4===t[0]?B(e,a(),S):(r=f[t[1]])&&(7===t[0]?requestAnimationFrame((()=>k(e,r))):6===t[0]&&((e,t,r,n,i)=>{(i=t.U.document.querySelector(`[data-ptid="${r}"]`))&&(n?i.dataset.pterror=n:i.type+="-x",delete i.dataset.ptid),k(e,t)})(e,r,t[2],t[3]))})(n,i)}})(((e,t)=>H(G,e).then(t))).then((e=>{e&&(G=new Worker(URL.createObjectURL(new Blob(['/* Partytown 0.10.3-dev1734732935152 - MIT builder.io */\n(e=>{const t=Symbol(),r=Symbol(),n=Symbol(),i=Symbol(),s=Symbol(),o=Symbol(),a=Symbol(),c=Symbol(),l=new Map,$={},d=new WeakMap,u=[],h={},p={},g=new Map,m=new Map,f="about:blank",w=e=>e.split(","),y=e=>{if(e=h.C+e,new URL(e).origin!=location.origin)throw"Invalid "+e;return e},v=w("clientWidth,clientHeight,clientTop,clientLeft,innerWidth,innerHeight,offsetWidth,offsetHeight,offsetTop,offsetLeft,outerWidth,outerHeight,pageXOffset,pageYOffset,scrollWidth,scrollHeight,scrollTop,scrollLeft"),S=w("childElementCount,children,firstElementChild,lastElementChild,nextElementSibling,previousElementSibling"),b=w("insertBefore,remove,removeChild,replaceChild"),I=w("className,width,height,hidden,innerHTML,innerText,textContent,text"),M=w("setAttribute,setAttributeNS,setProperty"),T=w("addEventListener,dispatchEvent,removeEventListener"),E=T.concat(M,w("add,observe,remove,unobserve")),N=/^[A-Z_]([A-Z0-9-]*[A-Z0-9])?$/,x=()=>{},L=e=>e.length,C=e=>{var t,r,n;try{const r=null===(t=null==e?void 0:e.constructor)||void 0===t?void 0:t.name;if(r)return r}catch(e){}try{const t=null===(n=null===(r=null==e?void 0:e.__zone_symbol__originalInstance)||void 0===r?void 0:r.constructor)||void 0===n?void 0:n.name;if(t)return t}catch(e){}return""},R=[],A=()=>Math.round(Math.random()*Number.MAX_SAFE_INTEGER).toString(36),P="text/partytown",W=(e,t,r)=>Object.defineProperty(e,t,{...r,configurable:!0}),H=(e,t)=>W(e,"name",{value:t}),O=(e,t,r)=>W(e.prototype,t,r),j=(e,t)=>Object.defineProperties(e.prototype,t),k=(e,t,r)=>O(e,t,{value:r,writable:!0});function D(e,t){var r,n;return null!==(n=null===(r=e.loadScriptsOnMainThread)||void 0===r?void 0:r.map((([e,t])=>new RegExp("string"===e?t.replace(/[.*+?^${}()|[\\]\\\\]/g,"\\\\$&"):t))).some((e=>e.test(t))))&&void 0!==n&&n}Object.freeze((e=>{const t=new Set;let r=[];do{Object.getOwnPropertyNames(r).forEach((e=>{"function"==typeof r[e]&&t.add(e)}))}while((r=Object.getPrototypeOf(r))!==Object.prototype);return Array.from(t)})());const U=(e,t)=>t in e[o],_=(e,t)=>e[o][t],B=(e,t,r)=>e[o][t]=r,F=(e,t,r,n,i,s)=>{if(!(i=l.get(t))&&r&&p[e]){const o=l.get(s||"");i=p[e].k(r,t,n,o),l.set(t,i)}return i},z=(e,t)=>k(e,"nodeType",t),q=(e,t)=>t.map((t=>O(e,t,{get(){let e=V(this,t),r=m.get(e);return r||(r=$e(this,[t]),m.set(e,r)),r}}))),V=(e,n,i)=>[e[t],e[r],n,...(i||R).map((e=>String(e&&e[t]?e[r]:e)))].join("."),X=(e,t)=>w(t).map((t=>O(e,t,{get(){return U(this,t)||B(this,t,$e(this,[t])),_(this,t)},set(e){_(this,t)!==e&&de(this,[t],e),B(this,t,e)}}))),Z=e=>v.map((t=>O(e,t,{get(){const e=g.get(V(this,t));if("number"==typeof e)return e;const r=$e(this,[t],v);return r&&"object"==typeof r?(Object.entries(r).map((([e,t])=>g.set(V(this,e),t))),r[t]):r}}))),G=(e,t)=>t.map((t=>{e.prototype[t]=function(...e){let r=V(this,t,e),n=g.get(r);return n||(n=ue(this,[t],e),g.set(r,n)),n}})),J=(e,n,i,s,o)=>{return void 0!==i&&(o=typeof i)?"string"===o||"boolean"===o||"number"===o||null==i?[0,i]:"function"===o?[4,{V:e,w:n,L:(a=i,(c=d.get(a))||(d.set(a,c=A()),$[c]=a),c)}]:(s=s||new Set)&&Array.isArray(i)?s.has(i)?[1,[]]:s.add(i)&&[1,i.map((t=>J(e,n,t,s)))]:"object"===o?i[r]?[3,[i[t],i[r]]]:i instanceof Event?[5,K(e,n,i,!1,s)]:Y&&i instanceof TrustedHTML?[0,i.toString()]:i instanceof ArrayBuffer?[8,i]:ArrayBuffer.isView(i)?[9,i.buffer,C(i)]:[2,K(e,n,i,!0,s)]:void 0:i;var a,c},Y="undefined"!=typeof TrustedHTML,K=(e,t,r,n,i,s,o,a)=>{if(s={},!i.has(r))for(o in i.add(r),r)a=r[o],(n||"function"!=typeof a)&&(s[o]=J(e,t,a,i));return s},Q=(e,n)=>e?J(e[t],e[r],n):[0,n],ee=(e,t,r,n,i,s,o,a)=>{if(n){if(i=n[0],s=n[1],0===i||11===i||12===i)return s;if(4===i)return re(r,s);if(6===i)return e&&r.length>0?(...t)=>ue(p[e].U,r,t,1):x;if(3===i)return te(s);if(7===i)return new ie(s.map(te));if(10===i)return new se(s);if(1===i)return s.map((n=>ee(e,t,r,n)));if(14===i)return new ne(s);for(a in o={},s)o[a]=ee(e,t,[...r,a],s[a]);if(13===i)return new p[e].U.CSSStyleDeclaration(e,t,r,o);if(5===i){if("message"===o.type&&o.origin){let e,t=JSON.stringify(o.data),r=u.find((e=>e.m===t));r&&(e=p[r.V],e&&(o.source=e.U,o.origin=e.E.origin))}return new Proxy(new Event(o.type,o),{get:(e,t)=>t in o?o[t]:"function"==typeof e[String(t)]?x:e[String(t)]})}if(2===i)return o}},te=([e,t,r,n])=>t===e&&p[e]?p[e].U:F(e,t,r,void 0,void 0,n),re=(e,{V:t,w:r,G:n,L:i})=>($[i]||d.set($[i]=function(...i){const s=F(t,r,n);return ue(s,e,i)},i),$[i]);class ne extends Error{constructor(e){super(e.message),this.name=e.name,this.message=e.message,this.stack=e.stack}}class ie{constructor(e){(this._=e).map(((e,t)=>this[t]=e))}entries(){return this._.entries()}forEach(e,t){this._.map(e,t)}item(e){return this[e]}keys(){return this._.keys()}get length(){return L(this._)}values(){return this._.values()}[Symbol.iterator](){return this._[Symbol.iterator]()}}const se=class{constructor(e){this.name=e[0],this.value=e[1]}get nodeName(){return this.name}get nodeType(){return 2}},oe=(e,t,r)=>console.warn(`Partytown unable to ${e} cross-origin ${t}: `+r.E),ae=[],ce=(e,n,i,o,a,c)=>{if(e[s]){if(ae.push({V:e[t],w:e[r],a:[...e[s],...n],d:o,r:a}),3===i)h.K([12,{F:A(),Q:[...ae]}],c?[c instanceof ArrayBuffer?c:c.buffer]:void 0),ae.length=0;else if(1===i)return le(!0);h.c=setTimeout(le,20)}},le=e=>{if(clearTimeout(h.c),L(ae)){const t=ae[L(ae)-1],r={F:`${A()}.${h.$tabId$}`,Q:[...ae]};if(ae.length=0,e){const e=((e,t)=>{const r=e.P,n=new Int32Array(r);Atomics.store(n,0,0),e.K([11,t]),Atomics.wait(n,0,0);let i=Atomics.load(n,0),s="",o=0;for(;o<i;o++)s+=String.fromCharCode(n[o+1]);return JSON.parse(s)})(h,r),n=e.z,i=ee(t.V,t.w,t.a,e.M);if(e.p){if(n)return Promise.reject(e.p);throw new Error(e.p)}return n?Promise.resolve(i):i}h.K([12,r])}},$e=(e,t,r,n)=>h.i.get&&(n=h.i.get(pe(e,t)))!==a?n:n=ce(e,t,1,void 0,r),de=(e,t,r,n)=>{if(h.i.set){if((n=h.i.set({value:r,prevent:c,...pe(e,t)}))===c)return;n!==a&&(r=n)}I.some((e=>t.includes(e)))&&(g.clear(),t[t.length-1]),t=[...t,Q(e,r),0],ce(e,t,2)},ue=(e,t,r,n,i,s,o,c)=>h.i.apply&&(o=h.i.apply({args:r,...pe(e,t)}))!==a?o:(c=t[L(t)-1],t=[...t,Q(e,r)],n=n||(E.includes(c)?2:1),"setAttribute"===c&&U(e,r[0])?B(e,r[0],r[1]):b.includes(c)?(g.clear(),m.clear()):M.includes(c)&&(n=2,g.clear()),o=ce(e,t,n,i,void 0,s)),he=(e,t,r)=>{ce(e,[1,t,Q(e,r)],1)},pe=(e,r)=>({name:r.join("."),continue:a,nodeName:e[n],constructor:C(e),instance:e,window:p[e[t]].U}),ge=(e,t,r,n)=>{let i={getItem(i){if(r)return ue(e,[t,"getItem"],[i],1);oe("get",t,n)},setItem(i,s){r?ue(e,[t,"setItem"],[i,s],1):oe("set",t,n)},removeItem(i){r?ue(e,[t,"removeItem"],[i],1):oe("remove",t,n)},key(i){if(r)return ue(e,[t,"key"],[i],1);oe("key",t,n)},clear(){r?ue(e,[t,"clear"],R,1):oe("clear",t,n)},get length(){if(r)return $e(e,[t,"length"]);oe("length",t,n)}};e[t]=new Proxy(i,{get:(e,t)=>Reflect.has(e,t)?Reflect.get(e,t):e.getItem(t),set:(e,t,r)=>(e.setItem(t,r),!0),has:(e,t)=>!!Reflect.has(e,t)||"string"==typeof t&&null!==e.getItem(t),deleteProperty:(e,t)=>(e.removeItem(t),!0)})},me=(e,t,r)=>{e[r]=H(class extends t{constructor(e,t,r,i){return super(e,t,r,i||{}),new Proxy(this,{get:(e,t)=>e[t]?e[t]:(e[t]||"string"!=typeof t||e[n][t]||(e[n][t]=$e(e,[t])),e[n][t]),set:(e,t,r)=>(e[n][t]=r,de(e,[t],r),g.clear(),!0)})}setProperty(...e){this[n][e[0]]=e[1],ue(this,["setProperty"],e,2),e[0],g.clear()}getPropertyValue(e){return this[e]}removeProperty(e){let t=this[n][e];return ue(this,["removeProperty"],[e],2),g.clear(),this[n][e]=void 0,t}},r)},fe=(e,t)=>{e[t]=H(class{constructor(e){this.ownerNode=e}get cssRules(){const e=this.ownerNode;return new Proxy({},{get(t,r){const n=String(r);return"item"===n?t=>ye(e,t):"length"===n?we(e).length:isNaN(n)?t[r]:ye(e,n)}})}insertRule(e,t){const r=we(this.ownerNode);return(t=void 0===t?0:t)>=0&&t<=r.length&&(ue(this.ownerNode,["sheet","insertRule"],[e,t],2),r.splice(t,0,0)),this.ownerNode,g.clear(),t}deleteRule(e){ue(this.ownerNode,["sheet","deleteRule"],[e],2),we(this.ownerNode).splice(e,1),this.ownerNode,g.clear()}get type(){return"text/css"}},t);const r={sheet:{get(){return new e[t](this)}}};j(e.HTMLStyleElement,r)},we=(e,t)=>((t=_(e,2))||(t=$e(e,["sheet","cssRules"]),B(e,2,t)),t),ye=(e,t,r)=>(0===(r=we(e))[t]&&(r[t]=$e(e,["sheet","cssRules",parseInt(t,10)])),r[t]),ve="0.10.3-dev1734732935152",Se=(e,t,r,n,i)=>{try{e.l=t,be(e,r)}catch(e){console.error(r,e),i=String(e.stack||e)}return e.l="",i},be=(e,t,r)=>{e.N=1;let n=((e,t)=>e.replace(/([a-zA-Z0-9_$\\.\\\'\\"\\`])?(\\.\\.\\.)?this(?![a-zA-Z0-9_$:])/g,((e,t,r)=>{const n=(t||"")+(r||"");return null!=t?n+"this":n+"(thi$(this)?window:this)"})))(t);t=`with(this){${n.replace(/\\/\\/# so/g,"//Xso")}\\n;function thi$(t){return t===this}};${(h.i.globalFns||[]).filter((e=>/[a-zA-Z_$][0-9a-zA-Z_$]*/.test(e))).map((e=>`(typeof ${e}==\'function\'&&(this.${e}=${e}))`)).join(";")};`+(r?"\\n//# sourceURL="+r:""),e.A||(t=t.replace(/.postMessage\\(/g,`.postMessage(\'${e.V}\',`)),new Function(t).call(e.U),e.N=0},Ie=(e,t,r)=>{(r=_(e,t))&&setTimeout((()=>r.map((e=>e({type:t})))))},Me=(e,t)=>{for(t=e.E;!t.host&&(t=(e=p[e.J]).E,e.V!==e.J););return t},Te=(e,t,r,n,i,s)=>(n=Me(e,n),i=new URL(t||"",n),r&&h.i.resolveUrl&&(s=h.i.resolveUrl(i,n,r))?s:i),Ee=(e,t,r)=>Te(e,t,r)+"",Ne=(e,t)=>{const r=Me(e),n=new URL(t||"",r);if(h.i.resolveSendBeaconRequestParameters){const e=h.i.resolveSendBeaconRequestParameters(n,r);if(e)return e}return{}},xe=()=>`<script src="${y("partytown.js?v="+ve)}"><\\/script>`,Le=e=>class{constructor(){this.s="",this.l=[],this.e=[],this.style={}}get src(){return this.s}set src(t){this.s=t,fetch(Ee(e,t,"image"),{mode:"no-cors",credentials:"include",keepalive:!0}).then((e=>{e.ok||0===e.status?this.l.map((e=>e({type:"load"}))):this.e.map((e=>e({type:"error"})))}),(()=>this.e.forEach((e=>e({type:"error"})))))}addEventListener(e,t){"load"===e&&this.l.push(t),"error"===e&&this.e.push(t)}removeEventListener(e,t){"load"===e&&(this.l=this.l.filter((e=>e!==t))),"error"===e&&(this.e=this.e.filter((e=>e!==t)))}get onload(){return this.l[0]}set onload(e){this.l=[e]}get onerror(){return this.e[0]}set onerror(e){this.e=[e]}},Ce={addEventListener:{value(...e){const t=e[0],r=_(this,t)||[];r.push(e[1]),B(this,t,r)}},async:{get:x,set:x},defer:{get:x,set:x},onload:{get(){let e=_(this,"load");return e&&e[0]||null},set(e){B(this,"load",e?[e]:null)}},onerror:{get(){let e=_(this,"error");return e&&e[0]||null},set(e){B(this,"error",e?[e]:null)}},getAttribute:{value(e){return"src"===e?this.src:ue(this,["getAttribute"],[e])}},setAttribute:{value(e,t){Re.includes(e)?this[e]=t:ue(this,["setAttribute"],[e,t])}}},Re=w("src,type"),Ae=(e,t)=>{const r={innerHTML:Pe,innerText:Pe,src:{get(){return _(this,4)||""},set(e){const r=Ee(t,e,null),n=h.i;e=Ee(t,e,"script"),B(this,4,e),de(this,["src"],e),r!==e&&de(this,["dataset","ptsrc"],r),this.type&&D(n,e)&&de(this,["type"],"text/javascript")}},text:Pe,textContent:Pe,type:{get(){return $e(this,["type"])},set(e){We(e)||(B(this,5,e),de(this,["type"],e))}},...Ce};j(e,r)},Pe={get(){const e=$e(this,["type"]);if(We(e)){const e=_(this,3);if(e)return e}return $e(this,["innerHTML"])||""},set(e){B(this,3,e)}},We=e=>!e||"text/javascript"===e,He=(e,i,s)=>{const o=h.i,a=H(class extends s{appendChild(e){return this.insertBefore(e,null)}get href(){}set href(e){}insertBefore(e,s){const a=e[t]=this[t],c=e[r],l=e[n],$="SCRIPT"===l,d="IFRAME"===l;if($){const t=_(e,3),r=_(e,5);if(t){if(We(r)){const r=e.id;if(r&&D(o,r))de(e,["type"],"text/javascript");else{const r=Se(i,c,t,0,""),n=r?"pterror":"ptid",s=r||c;de(e,["type"],P+"-x"),de(e,["dataset",n],s)}}de(e,["innerHTML"],t)}}if(ue(this,["insertBefore"],[e,s],2),d){const t=_(e,0);if(t&&t.startsWith("javascript:")){const e=t.split("javascript:")[1];Se(i,c,e,0,"")}((e,t)=>{let r,n,i=0,s=()=>{p[e]&&p[e].x&&!p[e].y?(r=_(t,1)?"error":"load",n=_(t,r),n&&n.map((e=>e({type:r})))):i++>2e3?(n=_(t,"error"),n&&n.map((e=>e({type:"error"})))):setTimeout(s,9)};s()})(c,e)}return $&&(le(!0),h.K([7,a])),e}get nodeName(){return"#s"===this[n]?"#document-fragment":this[n]}get nodeType(){return 3}get ownerDocument(){return i.n}},"Node");q(a,w("childNodes,firstChild,isConnected,lastChild,nextSibling,parentElement,parentNode,previousSibling")),e.Node=a},Oe=w("AUDIO,CANVAS,VIDEO"),je=w("Audio,MediaSource"),ke=(e,r,n)=>{j(e,{body:{get:()=>r.e},cookie:{get(){return r.A?$e(this,["cookie"]):(oe("get","cookie",r),"")},set(e){r.A&&de(this,["cookie"],e)}},createElement:{value(e){if(e=e.toUpperCase(),!N.test(e))throw e+" not valid";const r="IFRAME"===e,n=this[t],i=(r?"f_":"")+A();ue(this,["createElement"],[e],2,i);const s=F(n,i,e);if(r)Qe({V:i,J:n,S:f},!0).U.fetch=fetch,de(s,["srcdoc"],xe());else if("SCRIPT"===e){const e=_(s,5);We(e)&&de(s,["type"],P)}return s}},createElementNS:{value(e,r){const n=A(),i=F(this[t],n,r,e);return ue(this,["createElementNS"],[e,r],2,n),i}},createTextNode:{value(e){const r=this[t],n=A(),i=F(r,n,"#text");return ue(this,["createTextNode"],[e],2,n),i}},createEvent:{value:e=>new Event(e)},currentScript:{get(){return r.l?F(this[t],r.l,"SCRIPT"):null}},defaultView:{get:()=>n?null:r.U},documentElement:{get:()=>r.o},getElementsByTagName:{value(e){return"BODY"===(e=e.toUpperCase())?[r.e]:"HEAD"===e?[r.s]:ue(this,["getElementsByTagName"],[e])}},head:{get:()=>r.s},images:{get(){return $e(this,["images"])}},scripts:{get(){return $e(this,["scripts"])}},implementation:{get(){return{hasFeature:()=>!0,createHTMLDocument:e=>{const t=A();return ue(this,["implementation","createHTMLDocument"],[e],1,{V:t}),Qe({V:t,J:t,S:r.E+"",T:"hidden"},!0,!0).n}}}},location:{get:()=>r.E,set(e){r.E.href=e+""}},nodeType:{value:9},parentNode:{value:null},parentElement:{value:null},readyState:{value:"complete"},visibilityState:{get:()=>r.T||"visible"}}),X(e,"compatMode,referrer,forms")},De=(e,t)=>{j(e,{parentElement:{get(){return this.parentNode}},parentNode:{get:()=>t.o}})},Ue=(e,t)=>{j(e,{localName:{get(){return this[n].toLowerCase()}},namespaceURI:{get(){return this[i]||"http://www.w3.org/1999/xhtml"}},nodeType:{value:1},tagName:{get(){return this[n]}}}),q(e,S),X(e,"id"),Z(t),G(t,w("getClientRects,getBoundingClientRect"))},_e=(e,t)=>{const r={};w("hash,host,hostname,href,origin,pathname,port,protocol,search").map((e=>{r[e]={get(){let r,n=_(this,4);if("string"!=typeof n){if(r=$e(this,["href"]),""===r)return"protocol"===e?":":"";B(this,4,r),n=new URL(r)[e]}return Te(t,n,null)[e]},set(r){let n;if("href"===e)if((e=>{try{return new URL(e),!0}catch(e){return!1}})(r))n=new URL(r);else{const e=t.E.href;n=Te(t,e,null),n.href=new URL(r+"",n.href)}else n=Te(t,this.href,null),n[e]=r;B(this,4,n.href),de(this,["href"],n.href)}}})),j(e,r)},Be=(e,t)=>{const r={contentDocument:{get(){return Ve(this).n}},contentWindow:{get(){return Ve(this).U}},src:{get(){let e=_(this,0);return e&&e.startsWith("javascript:")?e:(e=Ve(this).E.href,e.startsWith("about:")?"":e)},set(e){if(e)if(e.startsWith("javascript:"))B(this,0,e);else if(!e.startsWith("about:")){let t,r=new XMLHttpRequest,n=Ve(this);n.E.href=e=Ee(n,e,"iframe"),n.y=1,B(this,1,void 0),r.open("GET",e,!1),r.send(),t=r.status,t>199&&t<300?(de(this,["srcdoc"],`<base href="${e}">`+r.responseText.replace(ze,((e,t)=>{const r=[];let n,i=!1;for(;n=qe.exec(t);){let[e]=n;e.startsWith("type=")&&(i=!0,e=e.replace(/(application|text)\\/javascript/,P)),r.push(e)}return i||r.push(\'type="\'+P+\'"\'),`<script ${r.join(" ")}>`}))+xe()),le(!0),h.K([7,n.V])):(B(this,1,t),n.y=0)}}},...Ce};j(e,r)},Fe="((?:\\\\w|-)+(?:=(?:(?:\\\\w|-)+|\'[^\']*\'|\\"[^\\"]*\\")?)?)",ze=new RegExp(`<script\\\\s*((${Fe}\\\\s*)*)>`,"mg"),qe=new RegExp(Fe,"mg"),Ve=e=>{const n=e[r];return p[n]||Qe({V:n,J:e[t],S:$e(e,["src"])||f},!0),p[n]},Xe=e=>{const t=(e,t)=>{const{a:r,b:n,c:i,d:s,e:o,f:a}=ue(e,[t],R);return new DOMMatrixReadOnly([r,n,i,s,o,a])},r={...e,getCTM:{value:function(){return t(this,"getCTM")}},getScreenCTM:{value:function(){return t(this,"getScreenCTM")}}};j(e,r)},Ze=(e,t)=>{e.NamedNodeMap=H(class extends t{constructor(e,t,r){return super(e,t,r),new Proxy(this,{get(e,t){const r=Je[t];return r?r.bind(e,[t]):$e(e,[t])},set(e,t,r){if(Je[t])throw new Error("Can\'t set read-only property: "+String(t));return de(e,[t],r),!0}})}},"NamedNodeMap")};function Ge(e,...t){return ue(this,e,t,1)}const Je={getNamedItem:Ge,getNamedItemNS:Ge,item:Ge,removeNamedItem:Ge,removeNamedItemNS:Ge,setNamedItem:Ge,setNamedItemNS:Ge},Ye=(a,c,l,$,d,g)=>{let m,v,b,I;const M=class{constructor(e,c,l,$,d){this[t]=e||a,this[r]=c||m||A(),this[s]=l||[],this[n]=$||v,this[i]=d||b,this[o]=I&&I[o]||{},m=v=b=void 0}},E=new(H(class extends URL{assign(){}reload(){}replace(){}},"Location"))(l),N=E.origin===h.I||E.origin===f,x=c===a,R={},P=()=>{let e,t,r=[];for(e in p)t=p[e],t.J!==a||t.B||r.push(t);return r},D=H(class extends M{constructor(){super(a,a),this.addEventListener=(...e)=>{"load"===e[0]?R.N&&setTimeout((()=>e[1]({type:"load"}))):ue(this,["addEventListener"],e,2)};let n,i,o=this,l=0,u=()=>{l||((h.u||(e.g=[$e,de,ue,he,j,A,t,r,s],h.t(y("partytown-media.js?v="+ve)),h.u=e.f,delete e.f),h.u)(M,J,R,o,je),l=1)},p={},f=(e,t,r,n)=>{Oe.includes(e)&&u();const i=p[e]?p[e]:e.includes("-")?p.UNKNOWN:p.I;return m=t,v=e,b=r,I=n,new i};var T,L;if(o.Window=D,o.name=name+a,He(o,R,M),(e=>{e.NodeList=H(ie,"NodeList")})(o),Ze(o,M),me(o,M,"CSSStyleDeclaration"),((e,t,r)=>{e[r]=H(class extends t{now(){return performance.now()}},r)})(o,M,"Performance"),((e,t)=>{const r="customElements",n=new Map;e[r]={define(i,s,o){n.set(i,s),t[i.toUpperCase()]=s;const a=[s.name,s.observedAttributes];ue(e,[r,"define"],[i,a,o])},get:t=>n.get(t)||ue(e,[r,"get"],[t]),whenDefined:t=>n.has(t)?Promise.resolve():ue(e,[r,"whenDefined"],[t]),upgrade:t=>ue(e,[r,"upgrade"],[t])}})(o,p),h.v.map((([e,t,n,i,c])=>{const l=Ke[e]?V:"EventTarget"===t?J:"Object"===t?M:o[t],$=o[e]=H(12===i?class extends M{constructor(...t){super(),he(this,e,t)}}:o[e]||class extends l{},e);c&&(p[c]=$),n.map((([e,t,n])=>{e in $.prototype||e in l.prototype||("string"==typeof t?O($,e,{get(){if(!U(this,e)){const n=this[r],i=[...this[s],e],c=o[t];c&&B(this,e,new c(a,n,i))}return _(this,e)},set(t){B(this,e,t)}}):5===t?k($,e,(function(...t){return ue(this,[e],t)})):t>0&&(void 0!==n?k($,e,n):O($,e,{get(){return $e(this,[e])},set(t){return de(this,[e],t)}})))}))})),w("atob,btoa,crypto,indexedDB,setTimeout,setInterval,clearTimeout,clearInterval").map((t=>{delete D.prototype[t],t in o||(n=e[t],null!=n&&(o[t]="function"!=typeof n||n.toString().startsWith("class")?n:n.bind(e)))})),Object.getOwnPropertyNames(e).map((t=>{t in o||(o[t]=e[t])})),je.map((e=>W(o,e,{get:()=>(u(),o[e])}))),"trustedTypes"in e&&(o.trustedTypes=e.trustedTypes),Ue(o.Element,o.HTMLElement),ke(o.Document,R,g),T=o.DocumentFragment,z(T,11),q(T,S),_e(o.HTMLAnchorElement,R),L=o.HTMLFormElement,j(L,{}),X(L,"elements"),Be(o.HTMLIFrameElement),Ae(o.HTMLScriptElement,R),Xe(o.SVGGraphicsElement),De(o.HTMLHeadElement,R),De(o.HTMLBodyElement,R),((e,t)=>{j(e,{parentElement:{value:null},parentNode:{get:()=>t.n}})})(o.HTMLHtmlElement,R),fe(o,"CSSStyleSheet"),z(o.Comment,8),z(o.DocumentType,10),Object.assign(R,{V:a,J:c,U:new Proxy(o,{get:(e,t)=>{var r;if("string"!=typeof t||isNaN(t))return(null===(r=h.i.mainWindowAccessors)||void 0===r?void 0:r.includes(t))?$e(this,[t]):e[t];{let e=P()[t];return e?e.U:void 0}},has:()=>!0}),n:f("#document",a+".d"),o:f("HTML",a+".e"),s:f("HEAD",a+".h"),e:f("BODY",a+".b"),E:E,T:$,A:N,B:x,k:f}),o.requestAnimationFrame=e=>setTimeout((()=>e(performance.now())),9),o.cancelAnimationFrame=e=>clearTimeout(e),o.requestIdleCallback=(e,t)=>(t=Date.now(),setTimeout((()=>e({didTimeout:!1,timeRemaining:()=>Math.max(0,50-(Date.now()-t))})),1)),o.cancelIdleCallback=e=>clearTimeout(e),ge(o,"localStorage",N,R),ge(o,"sessionStorage",N,R),N||(o.indexeddb=void 0),d)i={},o.history={pushState(e){i=e},replaceState(e){i=e},get state(){return i},length:0},o.indexeddb=void 0;else{const e=o.history.pushState.bind(o.history),t=o.history.replaceState.bind(o.history);o.history.pushState=(t,r,n)=>{!1!==R.$propagateHistoryChange$&&e(t,r,n)},o.history.replaceState=(e,r,n)=>{!1!==R.$propagateHistoryChange$&&t(e,r,n)}}o.Worker=void 0}get body(){return R.e}get document(){return R.n}get documentElement(){return R.o}fetch(e,t){return e="string"==typeof e||e instanceof URL?String(e):e.url,fetch(Ee(R,e,"fetch"),t)}get frames(){return R.U}get frameElement(){return x?null:F(c,a,"IFRAME")}get globalThis(){return R.U}get head(){return R.s}get length(){return P().length}get location(){return E}set location(e){E.href=e+""}get Image(){return Le(R)}get navigator(){return(e=>{const t={sendBeacon:(t,r)=>{try{return fetch(Ee(e,t,null),{method:"POST",body:r,mode:"no-cors",keepalive:!0,...Ne(e,t)}),!0}catch(e){return console.error(e),!1}}};for(let e in navigator)t[e]=navigator[e];return new Proxy(t,{set:(e,t,r)=>(navigator[t]=r,!0),get:(t,r)=>Object.prototype.hasOwnProperty.call(t,r)?t[r]:$e(e.U,["navigator",r])})})(R)}get origin(){return E.origin}set origin(e){}get parent(){for(let e in p)if(p[e].V===c)return p[e].U;return R.U}postMessage(...e){p[e[0]]&&(L(u)>50&&u.splice(0,5),u.push({V:e[0],m:JSON.stringify(e[1])}),e=e.slice(1)),ue(this,["postMessage"],e,3)}get self(){return R.U}get top(){for(let e in p)if(p[e].B)return p[e].U;return R.U}get window(){return R.U}get XMLHttpRequest(){const e=XMLHttpRequest,t=String(e),r=H(class extends e{open(...e){e[1]=Ee(R,e[1],"xhr"),super.open(...e)}set withCredentials(e){h.i.allowXhrCredentials&&(super.withCredentials=e)}toString(){return t}},C(e));return r.prototype.constructor.toString=()=>t,r}},"Window"),V=class extends M{constructor(e,t,r,n){return super(e,t,r,n),new Proxy(this,{get:(e,t)=>$e(e,[t]),set:(e,t,r)=>(de(e,[t],r),!0)})}},J=class extends M{};return T.map((e=>J.prototype[e]=function(...t){return ue(this,[e],t,2)})),X(D,"devicePixelRatio"),Z(D),G(D,["getComputedStyle"]),new D,R},Ke={DOMStringMap:1,NamedNodeMap:1},Qe=({V:e,J:t,S:r,T:n},i,s)=>(p[e]||(p[e]=Ye(e,t,r,n,i,s)),h.K([7,e]),p[e]),et=[],tt=t=>{const r=t.data,n=r[0],i=r[1];if(h.x)if(7===n)(async e=>{let t,r=e.V,n=e.w,i=F(r,n,"SCRIPT"),s=e.j,o=e.S,a=e.H,c="",l=p[r],$=["text/jscript","text/javascript","text/x-javascript","application/javascript","application/x-javascript","text/ecmascript","text/x-ecmascript","application/ecmascript"];if(o)try{if(o=Te(l,o,"script")+"",B(i,4,o),t=await fetch(o),t.ok){let e=t.headers.get("content-type");$.some((t=>{var r,n,i;return null===(i=null===(r=null==e?void 0:e.toLowerCase)||void 0===r?void 0:(n=r.call(e)).includes)||void 0===i?void 0:i.call(n,t)}))&&(s=await t.text(),l.l=n,be(l,s,a||o)),Ie(i,"load")}else c=t.statusText,Ie(i,"error")}catch(e){console.error(e),c=String(e.stack||e),Ie(i,"error")}else s&&(c=Se(l,n,s,0,c));l.l="",h.K([6,r,n,c])})(i);else if(9===n)(({V:e,w:t,L:r,R:n,b:i})=>{if($[r])try{$[r].apply(ee(e,t,[],n),ee(e,t,[],i))}catch(e){console.error(e)}})(i);else if(10===n)(({V:e,q:t,b:r})=>{try{let n=p[e].U,i=0,s=L(t);for(;i<s;i++)i+1<s?n=n[t[i]]:n[t[i]].apply(n,ee(null,e,[],r))}catch(e){console.error(e)}})(i);else if(5===n)Qe(i);else if(8===n)p[i].x=1,p[i].y=0;else if(14===n)p[i].T=r[2];else if(13===n){const e=i.V,t=p[e];t.E.href=i.url,function(e,t,r){const n=t.U.history;switch(r.type){case 0:t.$propagateHistoryChange$=!1;try{n.pushState(r.state,"",r.newUrl)}catch(e){}t.$propagateHistoryChange$=!0;break;case 1:t.$propagateHistoryChange$=!1;try{n.replaceState(r.state,"",r.newUrl)}catch(e){}t.$propagateHistoryChange$=!0}}(i.V,t,i)}else 15===n&&((e,t,r,n,i)=>{const s=F(t,r);s&&"function"==typeof s[n]&&s[n].apply(s,i)})(...r);else 1===n?((t=>{const r=h.i=JSON.parse(t.i),n=t.I;h.t=importScripts.bind(e),h.v=t.v,h.C=t.C,h.I=n,h.K=postMessage.bind(e),h.P=t.P,h.$tabId$=t.$tabId$,e.importScripts=void 0,delete e.postMessage,delete e.WorkerGlobalScope,w("resolveUrl,resolveSendBeaconRequestParameters,get,set,apply").map((e=>{r[e]&&(r[e]=new Function("return "+r[e])())}))})(i),h.K([2])):3===n?(h.v=[...h.v,...i],h.x=1,h.K([4]),[...et].map(tt),et.length=0):et.push(t)};e.onmessage=tt,postMessage([0])})(self);\n'],{type:"text/javascript"})),{name:"Partytown 🎉"}),G.onmessage=t=>{const r=t.data;12===r[0]?H(G,r[1]):e(G,r)},console.warn("@builder.io/partytown package has changed organization and now is\<EMAIL>/partytown https://www.npmjs.com/package/@qwik.dev/partytown\nWe recommend using the new package to stay up to date on feature releases and bug fixes."),S.addEventListener("pt1",(e=>B(G,w(e.detail.frameElement),e.detail))))}))})(window);
