import apiService from '../lib/apiService';

const ticketService = {
  // Get tickets for the logged-in user
  getUserTickets: () => apiService.get('/tickets/user', { withCredentials: true }),

  // Get a ticket by ID
  getTicketById: (ticketId) => apiService.get(`/tickets/${ticketId}`, { withCredentials: true }),

  // Create a new ticket
  createTicket: (data) => {
    // If data is FormData, do not set Content-Type header
    if (data instanceof FormData) {
      return apiService.post('/tickets', data, {
        withCredentials: true,
        headers: {
          // Let the browser set the Content-Type header automatically
        },
      });
    } else {
      // For non-FormData requests, use the default behavior
      return apiService.post('/tickets', data, { withCredentials: true });
    }
  },

  // Update a ticket
  updateTicket: (id, data) => apiService.put(`/tickets/${id}`, data, { withCredentials: true }),

  // Delete a ticket
  deleteTicket: (id) => apiService.delete(`/tickets/${id}`, { withCredentials: true }),
};

export default ticketService;