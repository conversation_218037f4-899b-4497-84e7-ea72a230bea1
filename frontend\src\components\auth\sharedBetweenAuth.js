import { Cookies  } from "react-cookie";

export const costomInputClasses=`w-full h-full bg-transparent text-blue-gray-700 font-sans font-normal outline outline-0 focus:outline-0 disabled:bg-blue-gray-50 disabled:border-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 border focus:border-2 border-t-transparent focus:border-t-transparent text-sm px-3 py-2.5 rounded-[7px] border-blue-gray-200 focus:border-primary-200`;
export const costomLabelClasses=`flex w-full h-full select-none pointer-events-none absolute left-0 font-normal truncate peer-placeholder-shown:text-blue-gray-500 leading-tight peer-focus:leading-tight peer-disabled:text-transparent peer-disabled:peer-placeholder-shown:text-blue-gray-500 transition-all -top-1.5 peer-placeholder-shown:text-sm text-[11px] peer-focus:text-[11px] before:content[' '] before:block before:box-border before:w-2.5 before:h-1.5 before:mt-[6.5px] before:mr-1 peer-placeholder-shown:before:border-transparent before:rounded-tl-md before:border-t peer-focus:before:border-t-2 before:border-l peer-focus:before:border-l-2 before:pointer-events-none before:transition-all peer-disabled:before:border-transparent after:content[' '] after:block after:flex-grow after:box-border after:w-2.5 after:h-1.5 after:mt-[6.5px] after:ml-1 peer-placeholder-shown:after:border-transparent after:rounded-tr-md after:border-t peer-focus:after:border-t-2 after:border-r peer-focus:after:border-r-2 after:pointer-events-none after:transition-all peer-disabled:after:border-transparent peer-placeholder-shown:leading-[4.1] text-blue-gray-400 peer-focus:text-primary-200 before:border-blue-gray-200 peer-focus:before:!border-primary-200 after:border-blue-gray-200 peer-focus:after:!border-primary-200`;
export const storeUserInLocal = (data) => {
    if(data.user){
        const tokenMaxAgeInSeconds = 3600 * 48; // 1 hour
          const co = new Cookies();

          co.set('token', data.token, { maxAge: tokenMaxAgeInSeconds, path: '/'  })
          co.set('user', JSON.stringify(data.user), { maxAge: tokenMaxAgeInSeconds, path: '/' })
          co.set('role', data.user.role, { maxAge: tokenMaxAgeInSeconds, path: '/' });
        //   co.remove('Language');
        //   co.set('Language', data.user.favoriteLang, { maxAge: tokenMaxAgeInSeconds, path: '/'+ data.user.favoriteLang });
    }

}