/**
 * API test script to test the site settings endpoints
 * Make sure your server is running before executing this
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5002'; // Adjust if your server runs on a different port

// Test data
const testSettings = {
  general: {
    siteName: 'API Test Site',
    siteDescription: 'Testing the API',
    contactEmail: '<EMAIL>',
    phone: '+1234567890',
    address: '123 API Street',
    socialLinks: {
      linkedin: 'https://linkedin.com/company/apitest',
      twitter: 'https://twitter.com/apitest',
      facebook: 'https://facebook.com/apitest'
    }
  },
  seo: {
    defaultTitle: 'API Test Site - Home',
    defaultDescription: 'This is an API test description',
    defaultKeywords: 'api, test, keywords',
    favicon: 'https://example.com/favicon.ico',
    googleAnalyticsId: 'G-APITEST123',
    googleSiteVerification: 'api-test-verification',
    bingVerification: 'api-test-bing',
    robotsTxt: 'User-agent: *\nAllow: /',
    sitemapEnabled: true
  }
};

async function testAPI() {
  try {
    console.log('🚀 Starting API tests...\n');

    // Test 1: Get current settings
    console.log('📝 Test 1: GET /admin/site-settings');
    try {
      const getResponse = await axios.get(`${BASE_URL}/admin/site-settings`, {
        withCredentials: true
      });
      console.log('✅ GET successful:', {
        status: getResponse.status,
        hasData: !!getResponse.data.data,
        siteName: getResponse.data.data?.general?.siteName || 'Not set'
      });
    } catch (error) {
      console.log('ℹ️ GET failed (expected if no settings exist yet):', error.response?.status, error.response?.data?.message);
    }

    // Test 2: Create/Update settings
    console.log('\n📝 Test 2: PUT /admin/site-settings');
    try {
      const putResponse = await axios.put(`${BASE_URL}/admin/site-settings`, testSettings, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ PUT successful:', {
        status: putResponse.status,
        message: putResponse.data.message,
        siteName: putResponse.data.data?.general?.siteName
      });
    } catch (error) {
      console.log('❌ PUT failed:', {
        status: error.response?.status,
        message: error.response?.data?.message,
        errors: error.response?.data?.errors
      });
      
      if (error.response?.data?.errors) {
        console.log('Validation errors:');
        error.response.data.errors.forEach(err => {
          console.log(`- ${err.field}: ${err.message}`);
        });
      }
    }

    // Test 3: Get settings after update
    console.log('\n📝 Test 3: GET /admin/site-settings (after update)');
    try {
      const getResponse2 = await axios.get(`${BASE_URL}/admin/site-settings`, {
        withCredentials: true
      });
      console.log('✅ GET after update successful:', {
        status: getResponse2.status,
        siteName: getResponse2.data.data?.general?.siteName,
        defaultTitle: getResponse2.data.data?.seo?.defaultTitle
      });
    } catch (error) {
      console.log('❌ GET after update failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 4: Update only general section
    console.log('\n📝 Test 4: PUT /admin/site-settings/general');
    try {
      const partialUpdate = {
        siteName: 'Updated via Section API',
        contactEmail: '<EMAIL>'
      };
      
      const sectionResponse = await axios.put(`${BASE_URL}/admin/site-settings/general`, partialUpdate, {
        withCredentials: true,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Section update successful:', {
        status: sectionResponse.status,
        message: sectionResponse.data.message
      });
    } catch (error) {
      console.log('❌ Section update failed:', {
        status: error.response?.status,
        message: error.response?.data?.message,
        errors: error.response?.data?.errors
      });
    }

    console.log('\n🎉 API tests completed!');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Helper function to test without authentication (if needed)
async function testWithoutAuth() {
  console.log('\n🔓 Testing without authentication...');
  try {
    const response = await axios.get(`${BASE_URL}/admin/site-settings`);
    console.log('⚠️ Request succeeded without auth (this might be a security issue)');
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.log('✅ Properly protected - authentication required');
    } else {
      console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
    }
  }
}

// Run tests
console.log('Make sure your server is running on', BASE_URL);
console.log('Note: These tests assume you have admin authentication set up\n');

testAPI().then(() => {
  return testWithoutAuth();
}).catch(console.error);
