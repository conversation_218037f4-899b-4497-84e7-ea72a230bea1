"use client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useAuth } from "@/app/context/AuthContext";

const VerificationPage = () => {
  const t = useTranslations("auth");
  const router = useRouter();
  const { checkAuth } = useAuth();
  const searchParams = useSearchParams();
  const [verificationStatus, setVerificationStatus] = useState("pending");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const verifyUser = async () => {
      const emailActivationStatus = searchParams.get("emailActivation");

      if (emailActivationStatus === "success") {
        try {
          await checkAuth();
          setVerificationStatus("success");
        } catch (error) {
          console.error("User verification failed:", error);
          setVerificationStatus("error");
        }
      } else {
        setVerificationStatus(emailActivationStatus || "error");
      }
      setLoading(false);
    };

    verifyUser();
  }, []);

  const getImageSrc = () => {
    switch (verificationStatus) {
      case "success":
        return "/images/verification-success.svg";
      case "failed":
        return "/images/verification-failed.svg";
      case "error":
        return "/images/verification-error.svg";
      case "pending":
        return "/images/verification-pending2.svg";
      default:
        router.push("/");
        return null;
    }
  };

  return (
    <main className="my-32 md:mb-52 md:mt-5 flex justify-center items-center">
      <div className="grid items-center justify-items-center">
        {loading ? (
          <div className="text-center">
            <p className="md:text-xl text-lg font-medium text-secondary font-outfit max-w-3xl">
              {t("email_verification_processing")}
            </p>
          </div>
        ) : (
          <>
            <Image
              className="h-auto"
              src={getImageSrc() || "/images/verification-pending2.svg"}
              width={450}
              height={600}
              alt="verification status img"
            />

            <div className="text-center">
              {verificationStatus === "success" && (
                <p className="md:text-xl text-lg font-medium text-secondary font-outfit max-w-3xl">
                  {t("email_verified_success")}
                </p>
              )}
              {verificationStatus === "failed" && (
                <p className="md:text-xl text-lg font-medium text-secondary font-outfit max-w-3xl">
                  {t("email_verification_failed")}
                </p>
              )}
              {verificationStatus === "error" && (
                <p className="md:text-xl text-lg font-medium text-secondary font-outfit max-w-3xl">
                  {t("email_verification_error")}
                </p>
              )}
              {verificationStatus === "pending" && (
                <>
                  {t("email_verification_text")
                    .split("\n")
                    .map((line, index) => (
                      <p
                        key={index}
                        className="md:text-xl text-lg font-medium text-secondary font-outfit max-w-3xl"
                      >
                        {line}
                      </p>
                    ))}
                </>
              )}
            </div>
          </>
        )}
      </div>
    </main>
  );
};

export default VerificationPage;
