import { Client } from '@notionhq/client';

const notion = new Client({ auth: process.env.NOTION_API_KEY });
const databaseId = process.env.NOTION_DATABASE_ID;


export const getPostContent = async (pageId) => {
    const blocks = [];
    let cursor = undefined;

    do {
        const response = await notion.blocks.children.list({
            block_id: pageId,
            start_cursor: cursor,
        });
        blocks.push(...response.results);
        cursor = response.next_cursor;
    } while (cursor);

    const processedBlocks = await Promise.all(
        blocks.map(async (block) => {
            const blockData = {
                id: block.id,
                type: block.type,
                hasChildren: block.has_children,
            };

            const blockContent = block[block.type];

            if (blockContent) {
                if (blockContent.rich_text) {
                    blockData.content = blockContent.rich_text
                        .map((t) => t.plain_text)
                        .join("") || null;
                }
                if (block.type === "image") {
                    blockData.content = blockContent.file?.url || blockContent.external?.url || null;
                }
                if (block.type === "column_list") {
                    const childColumns = await notion.blocks.children.list({
                        block_id: block.id,
                    });

                    blockData.columns = await Promise.all(
                        childColumns.results.map(async (column) => {
                            const columnData = {
                                id: column.id,
                                type: column.type,
                                hasChildren: column.has_children,
                            };

                            if (column.has_children) {
                                const columnChildren = await notion.blocks.children.list({
                                    block_id: column.id,
                                });

                                columnData.children = columnChildren.results.map((child) => ({
                                    id: child.id,
                                    type: child.type,
                                    content:
                                        child[child.type]?.file?.url ||
                                        child[child.type]?.external?.url ||
                                        (child[child.type]?.rich_text
                                            ?.map((t) => t.plain_text)
                                            .join("") || null),
                                }));
                            }

                            return columnData;
                        })
                    );
                }
            }
            return blockData;
        })
    );

    return processedBlocks;
};

// Function to retrieve all published posts
export const getPublishedPosts = async () => {
    const response = await notion.databases.query({
        database_id: databaseId,
        sorts: [
            {
                property: 'PublishedAt',
                direction: 'descending'
            }
        ],
        filter: {
            property: 'Status',
            status: {
                equals: "Published"
            }
        }
    });

    const posts = response.results.map((post) => {
        const { properties } = post;
        return {
            id: post.id,
            title: properties.Title?.rich_text?.[0]?.plain_text || 'Untitled',
            // slug: properties.Slug?.rich_text?.[0]?.plain_text || '',
            slug: properties.Slug?.formula?.string || '',
            description: properties.Description?.rich_text?.[0]?.plain_text || '',
            tags: properties.Tags?.multi_select || [],
            publishedAt: properties.PublishedAt?.date?.start || '',
            image: properties.Image?.files?.[0]?.name || null,
            status: properties.Status?.status?.name || 'Draft',
        };
    });

    return posts;
};

// Function to retrieve a single blog post by slug and its blocks
export const getSingleBlogPostBySlug = async (slug) => {
    const response = await notion.databases.query({
        database_id: databaseId,
        filter: {
            property: 'Slug',
            rich_text: {
                equals: slug,
            },
        },
    });

    if (!response.results.length) {
        throw new Error(`Post with slug "${slug}" not found.`);
    }

    const post = response.results[0];
    const { properties } = post;

    // Fetch content blocks for the post
    const contentBlocks = await getPostContent(post.id);

    return {
        id: post.id,
        title: properties.Title?.rich_text?.[0]?.plain_text || 'Untitled',
        slug: properties.Slug?.rich_text?.[0]?.plain_text || '',
        description: properties.Description?.rich_text?.[0]?.plain_text || '',
        tags: properties.Tags?.multi_select || [],
        publishedAt: properties.PublishedAt?.date?.start || '',
        image: properties.Image?.files?.[0]?.name || null,
        status: properties.Status?.status?.name || 'Draft',
        contentBlocks,
    };
};
