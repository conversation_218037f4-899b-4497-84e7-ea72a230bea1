{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/app/*": ["./src/app/*"], "@/app/[locale]/*": ["./src/app/[locale]/*"], "messages/*": ["messages/*"]}}, "include": ["src/**/*.js", "src/**/*.jsx", "messages/**/*.json", "src/app/[locale]/**/*.js", "src/app/[locale]/**/*.jsx"], "exclude": ["node_modules", ".next"]}