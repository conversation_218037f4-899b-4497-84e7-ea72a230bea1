const { google } = require('googleapis');
require('dotenv').config();

// Test Google Calendar configuration
async function testGoogleCalendar() {
  console.log('🔍 Testing Google Calendar Configuration...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log('GOOGLE_CALENDAR_CLIENT_ID:', process.env.GOOGLE_CALENDAR_CLIENT_ID ? '✅ Set' : '❌ Missing');
  console.log('GOOGLE_CALENDAR_CLIENT_SECRET:', process.env.GOOGLE_CALENDAR_CLIENT_SECRET ? '✅ Set' : '❌ Missing');
  console.log('GOOGLE_CALENDAR_REFRESH_TOKEN:', process.env.GOOGLE_CALENDAR_REFRESH_TOKEN ? '✅ Set' : '❌ Missing');
  console.log('GOOGLE_CALENDAR_REDIRECT_URI:', process.env.GOOGLE_CALENDAR_REDIRECT_URI ? '✅ Set' : '❌ Missing');
  console.log('');

  if (!process.env.GOOGLE_CALENDAR_CLIENT_ID || !process.env.GOOGLE_CALENDAR_CLIENT_SECRET || !process.env.GOOGLE_CALENDAR_REFRESH_TOKEN) {
    console.log('❌ Missing required Google Calendar environment variables');
    console.log('Please set up the following in your .env file:');
    console.log('- GOOGLE_CALENDAR_CLIENT_ID');
    console.log('- GOOGLE_CALENDAR_CLIENT_SECRET');
    console.log('- GOOGLE_CALENDAR_REFRESH_TOKEN');
    console.log('- GOOGLE_CALENDAR_REDIRECT_URI');
    return;
  }

  try {
    // Set up OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CALENDAR_CLIENT_ID,
      process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
      process.env.GOOGLE_CALENDAR_REDIRECT_URI || 'http://localhost:5002/auth/google/calendar/callback'
    );

    oauth2Client.setCredentials({
      refresh_token: process.env.GOOGLE_CALENDAR_REFRESH_TOKEN
    });

    console.log('🔐 OAuth2 client configured');

    // Test calendar access
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client });

    console.log('📅 Testing calendar access...');

    // Try to list calendars
    const calendarList = await calendar.calendarList.list();
    console.log('✅ Successfully connected to Google Calendar');
    console.log(`📋 Found ${calendarList.data.items.length} calendars`);

    // Find primary calendar
    const primaryCalendar = calendarList.data.items.find(cal => cal.primary);
    if (primaryCalendar) {
      console.log('✅ Primary calendar found:', primaryCalendar.summary);
    } else {
      console.log('⚠️ No primary calendar found');
    }

    // Test creating a simple event
    console.log('\n🧪 Testing event creation...');

    const testEvent = {
      summary: 'Test Event - Google Meet Integration',
      description: 'This is a test event to verify Google Meet link generation',
      start: {
        dateTime: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
        timeZone: 'UTC',
      },
      end: {
        dateTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
        timeZone: 'UTC',
      },
      conferenceData: {
        createRequest: {
          requestId: `test-${Date.now()}`,
          conferenceSolutionKey: {
            type: 'hangoutsMeet'
          }
        }
      }
    };

    const response = await calendar.events.insert({
      calendarId: 'primary',
      resource: testEvent,
      conferenceDataVersion: 1,
    });

    console.log('✅ Test event created successfully');
    console.log('📅 Event ID:', response.data.id);

    const meetLink = response.data.conferenceData?.entryPoints?.[0]?.uri ||
                     response.data.hangoutLink ||
                     null;

    if (meetLink) {
      console.log('✅ Google Meet link generated:', meetLink);
    } else {
      console.log('❌ No Google Meet link found in response');
      console.log('Conference data:', JSON.stringify(response.data.conferenceData, null, 2));
    }

    // Clean up - delete the test event
    console.log('\n🧹 Cleaning up test event...');
    await calendar.events.delete({
      calendarId: 'primary',
      eventId: response.data.id
    });
    console.log('✅ Test event deleted');

    console.log('\n🎉 Google Calendar integration test completed successfully!');

  } catch (error) {
    console.error('❌ Google Calendar test failed:', error.message);

    if (error.code === 401) {
      console.log('\n🔑 Authentication Error:');
      console.log('- Your refresh token may be expired');
      console.log('- Please regenerate your Google OAuth credentials');
      console.log('- Make sure the Google Calendar API is enabled in your Google Cloud Console');
    } else if (error.code === 403) {
      console.log('\n🚫 Permission Error:');
      console.log('- Make sure Google Calendar API is enabled');
      console.log('- Check that your OAuth app has the correct scopes');
      console.log('- Required scopes: https://www.googleapis.com/auth/calendar');
    }

    console.log('\nFull error details:', error);
  }
}

// Run the test
testGoogleCalendar();
