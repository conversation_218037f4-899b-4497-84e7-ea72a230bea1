import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const WhoTrustUs2 = ({ t }) => {
  const partners = [
    { src: "/images/home/<USER>/Figure.png" },
    { src: "/images/home/<USER>/Figure-1.png" },
    { src: "/images/home/<USER>/Figure-2.png" },
    { src: "/images/home/<USER>/Figure-3.png" },
    { src: "/images/home/<USER>/Figure-4.png" },
    { src: "/images/home/<USER>/Figure-5.png" },
    { src: "/images/home/<USER>/Figure-6.png" },
    { src: "/images/home/<USER>/Figure-7.png" },
    { src: "/images/home/<USER>/Figure-8.png" },
    { src: "/images/home/<USER>/Figure-9.png" },
    { src: "/images/galaxy_en.png" },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 25000, // Increased speed for smoother, slower scroll
    slidesToShow: 5,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 0,
    cssEase: "linear", // Ensures a linear, smooth scroll
    pauseOnHover: true,
    arrows: false,
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };

  return (
    <section className="w-[90%] mx-auto py-2 px-2 rounded-3xl">
      
      <div className="max-w-8xl mx-auto flex flex-col items-center">
      <Typography
            variant="h3"
            className="text-xl md:text-3xl xl:text-4xl font-inter font-bold"
          >
            {t("trusted_by_us")}
          </Typography>
        <div className="w-full mt-4 bg-white rounded-2xl overflow-hidden">
          <Slider {...settings}>
            {partners.map((partner, index) => (
              <div key={index} className="flex justify-center items-center ">
                <div
                  className="
                  flex items-center justify-center
                  transition-all duration-300  overflow-hidden"
                >
                  <Image
                    src={partner.src}
                    alt={`Partner ${index + 1}`}
                    width={140}
                    height={140}
                    className="object-contain"
                    quality={100}
                  />
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>
      <style jsx global>{`
        .slick-track {
          display: flex !important;
          align-items: center !important;
        }
        .slick-slide {
          height: auto !important;
          padding: 10px 0;
        }
        .slick-list {
          margin: 0 -12px;
          overflow: visible;
        }
        .slick-slide > div {
          display: flex;
          justify-content: center;
        }
        @media (max-width: 768px) {
          .slick-list {
            margin: 0 -6px;
            overflow: hidden;
          }
        }
      `}</style>
    </section>
  );
};
export default WhoTrustUs2;
