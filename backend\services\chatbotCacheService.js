const Package = require('../models/Package');
const fs = require('fs');
const path = require('path');

// Cache file path - store outside the watched directory to prevent nodemon restarts
// Using tmp directory which is typically not watched by nodemon
const CACHE_FILE_PATH = path.join(process.env.TEMP || '/tmp', 'ztech_package_cache.json');

// Cache expiration time (24 hours in milliseconds)
// const CACHE_EXPIRATION = 2000;
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

/**
 * Service for caching package data for the chatbot
 */
const chatbotCacheService = {
  /**
   * Get packages from cache or database
   * @returns {Promise<Array>} Array of formatted packages
   */
  getPackages: async () => {
    try {
      // Check if cache exists and is valid
      if (await isCacheValid()) {
        return readCacheFile();
      }

      // If cache is invalid, fetch from database and update cache
      return await updateCache();
    } catch (error) {
      console.error('Error getting packages:', error);
      // If there's an error, try to use cache anyway
      try {
        return readCacheFile();
      } catch (cacheError) {
        console.error('Could not read cache file:', cacheError);
        // If cache can't be read, return empty array
        return [];
      }
    }
  },

  /**
   * Force cache update
   * @returns {Promise<Array>} Array of formatted packages
   */
  updateCache: async () => {
    return await updateCache();
  }
};

/**
 * Check if cache file exists and is not expired
 * @returns {Promise<boolean>} True if cache is valid
 */
async function isCacheValid() {
  try {
    const stats = fs.statSync(CACHE_FILE_PATH);
    const cacheAge = Date.now() - stats.mtimeMs;
    const isValid = cacheAge < CACHE_EXPIRATION;
    return isValid;
  } catch (error) {
    // If file doesn't exist or can't be read
    return false;
  }
}

/**
 * Read and parse the cache file
 * @returns {Array} Array of packages from cache
 */
function readCacheFile() {
  try {
    const cacheData = fs.readFileSync(CACHE_FILE_PATH, 'utf8');
    const packages = JSON.parse(cacheData);
    return packages;
  } catch (error) {
    console.error('Error reading cache file:', error);
    throw error;
  }
}

/**
 * Fetch packages from database and update cache
 * @returns {Promise<Array>} Array of formatted packages
 */
async function updateCache() {
  try {
    // Fetch packages from database with relevant information
    const packages = await Package.find({})
      .populate({
        path: 'brand',
        populate: {
          path: 'category'
        }
      })
      .populate('specifications')
      .lean();

    // If no packages found, return an empty array
    if (!packages || packages.length === 0) {
      console.log('No packages found in database');

      // Create directory if it doesn't exist
      const dir = path.dirname(CACHE_FILE_PATH);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write empty array to cache file
      fs.writeFileSync(CACHE_FILE_PATH, JSON.stringify([], null, 2));
      return [];
    }

    // Format packages for the chatbot
    const formattedPackages = packages.map(pkg => ({
      id: pkg._id.toString(),
      name: pkg.name,
      name_fr: pkg.name_fr,
      description: pkg.description,
      description_fr: pkg.description_fr,
      price: pkg.price,
      regularPrice: pkg.regularPrice || pkg.price,
      discounts: pkg.discounts || [],
      brand: pkg.brand?.name || 'Unknown',
      brandFr: pkg.brand?.name_fr,
      category: pkg.brand?.category?.name || 'Unknown',
      categoryFr: pkg.brand?.category?.name_fr,
      specifications: pkg.specifications?.map(spec => ({
        // Specifications only have value and value_fr fields, no name field
        value: spec.value,
        value_fr: spec.value_fr
      })) || [],
      sslType: pkg.sslType
    }));

    // Create directory if it doesn't exist
    const dir = path.dirname(CACHE_FILE_PATH);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write to cache file
    fs.writeFileSync(CACHE_FILE_PATH, JSON.stringify(formattedPackages, null, 2));
    return formattedPackages;
  } catch (error) {
    console.error('Error updating cache:', error);

    // In case of error, return empty array but don't throw
    // This prevents the server from crashing
    return [];
  }
}

module.exports = chatbotCacheService;
