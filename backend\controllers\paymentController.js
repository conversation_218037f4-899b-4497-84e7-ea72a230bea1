const Payment = require("../models/Payment");
const mongoose = require("mongoose");

const generateInvoiceId = () => {
  const shortDate = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const randomNum = Math.floor(Math.random() * 100); // 2-digit random number
  return `INV-${shortDate}${randomNum}`;
};

const generatePaymentId = () => {
  const shortDate = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  const randomNum = Math.floor(Math.random() * 100); // 2-digit random number
  return `PAY-${shortDate}${randomNum}`;
};

exports.processPayment = async ({
  userId,
  orderId,
  paymentMethod,
  status,
  transactionId,
}) => {
  try {
    const payment = new Payment({
      user: userId,
      order: orderId,
      invoiceId: generateInvoiceId(), // Keep if needed
      paymentId: generatePaymentId(), // Keep if needed
      paymentMethod,
      status,
      transactionId,
      paymentDate: new Date(),
    });

    await payment.save();
    return payment;
  } catch (error) {
    console.error("Payment processing error:", error);
    throw new Error("Failed to process payment");
  }
};

// Get payment history
exports.getPaymentHistory = async (req, res) => {
  try {
    const userId = req.user?._id;
    const { type = "payment_history" } = req.query;

    let query = { user: userId, deleted: { $ne: true } };
    if (type === "payment_history") {
      query.status = { $in: ["refunded", "completed"] };
    } else if (type === "refund_history") {
      query.status = "refunded";
    }

    const payments = await Payment.find(query)
      .sort({ createdAt: -1 })
      .populate({
        path: "order",
        populate: {
          path: "subOrders",
          populate: {
            path: "package",
          },
        },
      })
      .lean();

    const paymentsWithServices = payments.map((payment) => ({
      ...payment,
      services: payment.order.subOrders.map((subOrder) => ({
        serviceId: subOrder.package._id,
        name: subOrder.package.name,
        name_fr: subOrder.package?.name_fr || "not translated",
        period: subOrder.period,
        quantity: subOrder.quantity,
        price: subOrder.price,
        discount: subOrder.discount,
        status: subOrder.status,
      })),
      totalPrice: payment.order.totalPrice,
      subTotal: payment.order.subTotal,
      taxAmount: payment.order.taxAmount,
      currency: payment.order.currency,
      billingInfo: payment.order.billingInfo,
    }));

    res.status(200).json({ success: true, payments: paymentsWithServices });
  } catch (error) {
    console.error("Error in getPaymentHistory:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving payment history",
      error: error.message,
    });
  }
};

// Get single payment details
exports.getPaymentDetails = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user?._id;

    const payment = await Payment.findOne({
      paymentId: paymentId,
      user: userId,
    })
      .populate({
        path: "order",
        populate: {
          path: "subOrders",
          populate: {
            path: "package",
          },
        },
      })
      .lean();

    if (!payment) {
      res
        .status(400)
        .json({ success: false, message: "Error retrieving payment details" });
    }

    const paymentWithServices = {
      ...payment,
      services: payment.order.subOrders.map((subOrder) => ({
        serviceId: subOrder.package._id,
        name: subOrder.package.name,
        name_fr: subOrder.package?.name_fr || "not translated",
        period: subOrder.period,
        quantity: subOrder.quantity,
        price: subOrder.price,
        discount: subOrder.discount,
        status: subOrder.status,
      })),
      totalPrice: payment.order.totalPrice,
      subTotal: payment.order.subTotal,
      taxAmount: payment.order.taxAmount,
      currency: payment.order.currency,
      billingInfo: payment.order.billingInfo,
    };

    res.status(200).json({ success: true, payment: paymentWithServices });
  } catch (error) {
    console.error("Error in getPaymentDetails:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving payment details",
    });
  }
};
