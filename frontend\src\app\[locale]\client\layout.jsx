'use client'
import { useIsMobile } from "../../hook/useIsMobile";
import Sidebar from "./sidebar";

export default function ClientLayout({ children }) {
    const isMobile = useIsMobile();

    return (
        <div className="w-full">
            <div className="flex min-h-[91vh] md:min-h-[87vh] max-w-[1400px] md:max-h-[87vh] mx-auto bg-white">
                {!isMobile && <Sidebar />}
                <main className="flex-1 p-0 bg-gray-50 md:max-h-[87vh] overflow-y-auto">
                    {children}
                </main>
            </div>
        </div>
    );
}
