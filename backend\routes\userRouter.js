const express = require('express');
const multer = require('multer');

const { getProfile, setImageOfProfile, setConfigAccount, changePassword, removeAccount, updateUserLanguage, getCurrentPaymentMethod, updatePaymentMethod, changeEmail, verifyOtp, resendOtpCode, getBillingInfo, updateBillingInfo, deleteBillingInfo } = require('../controllers/userControllers');
const { fetchUserMiddleware, requireSignIn, canHandleIt, authUserIsVerified } = require('../midelwares/authorization');
const { setConfigAccountValidator, changePasswordValidator, changeEmailValidator, billingInfoValidator } = require('../midelwares/requests/userRequest');
const { asyncBackFrontEndLang } = require('../midelwares/sharedMidd');
const { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } = require('../controllers/notificationController');

const userRouter = express.Router();
const upload = multer({
    // limits: { fileSize: 10 * 1024 * 1024 }, // Limit file size to 10MB, adjust as needed
    // fileFilter: (req, file, cb) => {
    //     // Check file type if needed
    //     if (file.mimetype.startsWith('image/')) {
    //         cb(null, true); // Accept the file
    //     } else {
    //         cb(new Error('Invalid file type'), false); // Reject the file
    //     }
    // }
});

userRouter.get('/profile', asyncBackFrontEndLang, fetchUserMiddleware, getProfile);
userRouter.post('/setConfigAccount', asyncBackFrontEndLang, canHandleIt, setConfigAccountValidator, setConfigAccount);
userRouter.post('/setNewPassword', asyncBackFrontEndLang, canHandleIt, changePasswordValidator, changePassword);
userRouter.post('/setNewEmail', asyncBackFrontEndLang, canHandleIt, changeEmailValidator, changeEmail);

userRouter.post('/verifyOtp', asyncBackFrontEndLang, canHandleIt, verifyOtp);
userRouter.post('/verifyEmail/resendOtp', asyncBackFrontEndLang, canHandleIt, resendOtpCode);
userRouter.post('/removeAccount', asyncBackFrontEndLang, requireSignIn, canHandleIt, removeAccount);


userRouter.post('/profileImage', upload.single('avatar'), asyncBackFrontEndLang, canHandleIt, setImageOfProfile);


// Get billing info for a user
userRouter.get('/getBillingInfo/:userId', asyncBackFrontEndLang, getBillingInfo);

// Update billing info for a user
userRouter.put('/editBillingInfo', asyncBackFrontEndLang, billingInfoValidator, updateBillingInfo);

// Delete billing info for a user
userRouter.delete('/deleteBillingInfo/:userId', asyncBackFrontEndLang, deleteBillingInfo);



userRouter.put('/favoriteLang', asyncBackFrontEndLang, requireSignIn, authUserIsVerified, updateUserLanguage);
userRouter.post('/updatePaymentMethod', asyncBackFrontEndLang, requireSignIn, authUserIsVerified, updatePaymentMethod);
userRouter.get('/currentPaymentMethod', asyncBackFrontEndLang, requireSignIn, authUserIsVerified, getCurrentPaymentMethod);

// Notification routes
userRouter.get('/notifications', asyncBackFrontEndLang, fetchUserMiddleware, getUserNotifications);
userRouter.put('/notifications/:notificationId/read', asyncBackFrontEndLang, fetchUserMiddleware, markNotificationAsRead);
userRouter.put('/notifications/read-all', asyncBackFrontEndLang, fetchUserMiddleware, markAllNotificationsAsRead);

module.exports = userRouter;