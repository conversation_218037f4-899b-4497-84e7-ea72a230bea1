const VPSPricing = require('../../models/VPSPricing');
const Package = require('../../models/Package');

// SOLUTION DÉFINITIVE - Contrôleur séparé pour les prix VPS
class VPSPricingController {
  
  // Sauvegarder/Mettre à jour un prix VPS
  async saveVPSPrice(req, res) {
    console.log('🎯 saveVPSPrice method called');
    console.log('📦 Full request body:', JSON.stringify(req.body, null, 2));
    console.log('👤 User info:', req.user ? { id: req.user._id, email: req.user.email } : 'No user');

    try {
      const { regionId, packageId, additionalPrice } = req.body;

      console.log(`💾 NOUVEAU SYSTÈME - Saving VPS price:`, {
        regionId,
        packageId,
        additionalPrice,
        type_regionId: typeof regionId,
        type_packageId: typeof packageId,
        type_additionalPrice: typeof additionalPrice
      });

      // Validation
      if (!regionId || !packageId || additionalPrice === undefined) {
        return res.status(400).json({
          success: false,
          message: 'regionId, packageId, and additionalPrice are required'
        });
      }

      // Vérifier que le package existe
      const vpsPackage = await Package.findById(packageId);
      if (!vpsPackage) {
        return res.status(404).json({
          success: false,
          message: 'Package not found'
        });
      }

      // Utiliser upsert pour créer ou mettre à jour
      const result = await VPSPricing.findOneAndUpdate(
        { regionId, packageId },
        {
          regionId,
          packageId,
          packageName: vpsPackage.name,
          additionalPrice: parseFloat(additionalPrice),
          updatedBy: req.user._id
        },
        {
          upsert: true, // Créer si n'existe pas
          new: true,    // Retourner le document mis à jour
          setDefaultsOnInsert: true
        }
      );

      console.log(`✅ Price saved successfully:`, {
        regionId: result.regionId,
        packageName: result.packageName,
        additionalPrice: result.additionalPrice
      });

      res.status(200).json({
        success: true,
        message: 'Prix sauvegardé avec succès',
        data: result
      });

    } catch (error) {
      console.error('❌ Error saving VPS price:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la sauvegarde du prix',
        error: error.message
      });
    }
  }

  // Récupérer tous les prix VPS
  async getAllVPSPrices(req, res) {
    try {
      const prices = await VPSPricing.find()
        .populate('packageId', 'name')
        .sort({ regionId: 1, packageName: 1 });

      // Grouper par région
      const pricesByRegion = {};
      prices.forEach(price => {
        if (!pricesByRegion[price.regionId]) {
          pricesByRegion[price.regionId] = [];
        }
        pricesByRegion[price.regionId].push({
          packageId: price.packageId._id,
          packageName: price.packageName,
          additionalPrice: price.additionalPrice
        });
      });

      res.status(200).json({
        success: true,
        data: pricesByRegion
      });

    } catch (error) {
      console.error('❌ Error getting VPS prices:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des prix',
        error: error.message
      });
    }
  }

  // Récupérer le prix pour une région et un package spécifique
  async getVPSPrice(req, res) {
    try {
      const { regionId, packageId } = req.params;

      const pricing = await VPSPricing.findOne({ regionId, packageId })
        .populate('packageId', 'name');

      if (!pricing) {
        return res.status(200).json({
          success: true,
          data: {
            regionId,
            packageId,
            additionalPrice: 0
          }
        });
      }

      res.status(200).json({
        success: true,
        data: {
          regionId: pricing.regionId,
          packageId: pricing.packageId._id,
          packageName: pricing.packageName,
          additionalPrice: pricing.additionalPrice
        }
      });

    } catch (error) {
      console.error('❌ Error getting VPS price:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du prix',
        error: error.message
      });
    }
  }
}

module.exports = new VPSPricingController();
