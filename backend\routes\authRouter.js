const express = require("express");
const passport = require("passport");

const {
  registerValidator,
  loginValidator,
  resetPasswordValidator,
  forgetPasswordValidator,
  cartRegisterValidator,
} = require("../midelwares/requests/authRequest");

const {
  registerController,
  loginController,
  logout,
  verifyEmailAccount,
  verifyEmailAccountToResetPassword,
  forgotPassword,
  resetPassword,
  checkAuth,
  refreshToken,
} = require("../controllers/authControllers");
const { checkUserOrRefreshToken } = require("../midelwares/authorization");
const {
  googleAuthCallback,
  facebookAuthCallback,
  githubAuthCallback,
} = require("../controllers/socialMediaControllers");
const { captchaVerification } = require("../midelwares/sharedMidd");

const authRouter = express.Router();

authRouter.post(
  "/register",
  registerValidator,
  captchaVerification,
  checkUserOrRefreshToken,
  registerController
);
authRouter.post(
  "/cartRegister",
  cartRegisterValidator,
  captchaVerification,
  checkUserOrRefreshToken,
  registerController
);
authRouter.post(
  "/login",
  loginValidator,
  checkUserOrRefreshToken,
  loginController
);
authRouter.post("/logout", logout);
authRouter.get("/checkAuth", checkAuth);
authRouter.get("/verify-email-account", verifyEmailAccount);
authRouter.post(
  "/forgot-password",
  forgetPasswordValidator,
  captchaVerification,
  forgotPassword
);
authRouter.get(
  "/verifyEmailAccountToResetPassword",
  verifyEmailAccountToResetPassword
);

authRouter.post("/reset-password", resetPasswordValidator, resetPassword);

authRouter.post("/refresh-token", refreshToken);

// Google OAuth Login Route
authRouter.get(
  "/google",
  passport.authenticate("google", { scope: ["profile", "email"] })
);

// Google OAuth Callback Route
authRouter.get(
  "/google/callback",
  passport.authenticate("google", {
    session: false,
    failureRedirect: "/auth/login",
    failWithError: true, // Use this instead of failureFlash
  }),
  googleAuthCallback
);

// Facebook OAuth Login Route
authRouter.get(
  "/facebook",
  passport.authenticate("facebook", { scope: ["email"] })
);

// Facebook OAuth Callback Route
authRouter.get(
  "/facebook/callback",
  passport.authenticate("facebook", {
    session: false,
    failureRedirect: "/auth/login",
    failWithError: true,
  }),
  facebookAuthCallback
);

authRouter.get(
  "/github",
  passport.authenticate("github", { scope: ["user:email"] })
);
authRouter.get(
  "/github/callback",
  passport.authenticate("github", {
    session: false,
    failureRedirect: "/auth/login",
    failWithError: true,
  }),
  githubAuthCallback
);

module.exports = authRouter;
