'use client'
import { <PERSON><PERSON><PERSON>, <PERSON>, CardBody, CardFooter } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { setServerMedia } from "../../../helpers/helpers";
import { useTranslations } from "next-intl";
import PaymentStatusModal from "../../../../components/order/paymentStatusModal";
import { useSearchParams } from "next/navigation";
import orderService from "../../../services/orderService";

export default function OrdersPage() {
    const t = useTranslations('client');
    const [subOrders, setSubOrders] = useState([]);
    const [loading, setLoading] = useState(true);

    const [paymentStatus, setPaymentStatus] = useState(null);
    const [orderId, setOrderId] = useState(null);
    const [openModal, setOpenModal] = useState(false);

    const searchParams = useSearchParams();
    const status = searchParams.get('status');
    const item = searchParams.get('item');
    const categoryName = 'Hosting'; // Hardcoded category name

    useEffect(() => {
        if (status && item) {
            setPaymentStatus(status);
            setOrderId(item);
            setOpenModal(true);
        }
    }, [status, item]);

    const closeModal = () => {
        setOpenModal(false);
    };

    useEffect(() => {
        const getSubOrders = async () => {
            try {
                const res = await orderService.getSubOrdersByCategory(categoryName);
                setSubOrders(res.data.subOrders);
            } catch (error) {
                console.error("Error getting suborders", error);
            } finally {
                setLoading(false);
            }
        };
        getSubOrders();
    }, [categoryName]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Typography variant="h6" className="text-gray-600">
                    {t('loading_orders')}
                </Typography>
            </div>
        );
    }

    if (!subOrders?.length) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <Typography variant="h4" className="text-gray-600">
                    {t('no_orders_found')}
                </Typography>
            </div>
        );
    }

    return (
        <div className="p-8 bg-gray-50 min-h-screen">
            <Typography variant="h1" className="text-3xl font-bold mb-6 text-gray-800">
                {t('your_orders')}
            </Typography>
            <div className="space-y-6">
                {subOrders.map(subOrder => {
                    const { id, package: pkg, quantity, price } = subOrder;
                    return (
                        <Card key={id} className="border border-gray-200 shadow-md">
                            <CardBody>
                                <div className="flex items-center space-x-4">
                                    <img
                                        src={setServerMedia(pkg.image)}
                                        alt={pkg.name}
                                        className="w-16 h-16 object-contain rounded-lg" />
                                    <div>
                                        <Typography variant="h6" className="text-md font-semibold text-gray-800">
                                            {t(pkg.name)}
                                        </Typography>
                                        <Typography className="text-sm text-gray-600">
                                            {t('reference')} {pkg.reference}
                                        </Typography>
                                        <Typography className="text-sm text-gray-600">
                                            {t('quantity')} {quantity}
                                        </Typography>
                                        <Typography className="text-sm text-gray-600">
                                            {t('price')}{price} {pkg.currency}
                                        </Typography>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    );
                })}
            </div>

            {/* Render the PaymentStatusModal if needed */}
            {openModal && (
                <PaymentStatusModal
                    status={paymentStatus}
                    orderId={orderId}
                    onClose={closeModal}
                    t={t}
                />
            )}
        </div>
    );
}
