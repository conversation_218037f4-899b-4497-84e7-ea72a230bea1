"use client";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { ChevronRight, ChevronDown } from "lucide-react";
import { Typography } from "@material-tailwind/react";

const FAQ2 = ({ t }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAutoCycling, setIsAutoCycling] = useState(true);
  const [isMobile, setIsMobile] = useState(false);


  // Check if we're on mobile or desktop
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      // Set initial auto-cycling state based on device type
      setIsAutoCycling(window.innerWidth >= 768);
    };
    
    // Check on initial load
    checkIfMobile();
    
    // Listen for resize events
    window.addEventListener('resize', checkIfMobile);
    
    return () => {
      window.removeEventListener('resize', checkIfMobile);
    };
  }, []);

  const faqItems = [
    {
      question: t("faq_1_question"),
      answer: t("faq_1_answer"),
    },
    {
      question: t("faq_2_question"),
      answer: t("faq_2_answer"),
    },
    {
      question: t("faq_3_question"),
      answer: t("faq_3_answer"),
    },
    {
      question: t("faq_4_question"),
      answer: t("faq_4_answer"),
    },
  ];

  const cycleToNextFaq = useCallback(() => {
    setActiveIndex((prevIndex) => {
      const nextIndex = prevIndex === null ? 0 : (prevIndex + 1) % faqItems.length;
      return nextIndex;
    });
  }, [faqItems.length]);

  useEffect(() => {
    let intervalId;

    if (isAutoCycling) {
      intervalId = setInterval(cycleToNextFaq, 10000); // Change every 10 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isAutoCycling, cycleToNextFaq]);

  const toggleAccordion = (index) => {
    // For both mobile and desktop: set active index
    setActiveIndex(index === activeIndex ? null : index);
    
    // But only enable auto-cycling on desktop
    if (!isMobile) {
      setIsAutoCycling(true);
    } else {
      setIsAutoCycling(false);
    }
  };

  // Helper function to render answer content with proper formatting
const renderAnswerContent = (answer) => {
  // First normalize the newlines by replacing single \n with \n\n if they aren't already
  const normalizedAnswer = answer.replace(/(?<!\n)\n(?!\n)/g, '\n\n');
  return normalizedAnswer.split("\n\n").map((paragraph, idx) => {
    // Check if paragraph contains bullet points
    if (paragraph.includes("- ")) {
      const [intro, ...bulletPoints] = paragraph.split("- ");
      return (
        <div key={idx} className="mb-4">
          {intro && <p className="mb-2">{intro}</p>}
          <ul className="list-disc pl-5 space-y-1">
            {bulletPoints.map((point, i) => (
              <li key={i}>{point.trim()}</li>
            ))}
          </ul>
        </div>
      );
    }
    return (
      <p key={idx} className="mb-4">
        {paragraph}
      </p>
    );
  });
};

  return (
    <section className="w-full py-16">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <Typography variant="h1" className="font-bold text-3xl mb-4">
          {t("faq_title")}
          </Typography>
        </div>

        {/* Desktop Layout (hidden on mobile) */}
        <div className="hidden md:flex flex-row items-center justify-center gap-4 px-10">
          {/* FAQ Accordion */}
          <div className="w-[70%] -mr-14 h-auto rounded-lg overflow-hidden shadow-lg z-10">
            {faqItems.map((item, index) => (
              <div
                key={`desktop-${index}`}
                className={`p-4 cursor-pointer transition-all duration-300 flex items-center gap-4 ${
                  activeIndex === index
                    ? "bg-[#E0E3FF] shadow-sm"
                    : "bg-white hover:bg-gray-100"
                }`}
                onClick={() => toggleAccordion(index)}
              >
                <div
                  className={`w-5 h-5 ${
                    activeIndex === index ? "bg-[#606AF5]" : "bg-[#606AF566]"
                  } rounded-full`}
                ></div>
                <div className="w-[90%] flex items-center justify-between">
                  <h3 className="font-medium text-sm pr-4">
                    {item.question}
                  </h3>
                  <ChevronRight
                    className={`w-5 h-5 text-blue-500 transition-transform duration-300`}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* FAQ Content */}
          <div  className="w-full bg-[#FAFBFF] h-[630px] py-6 px-14 rounded-lg shadow-md overflow-y-auto">
            <h3 className="font-semibold text-xl mb-4">
              {activeIndex !== null
                ? faqItems[activeIndex]?.question
                : "Hosting with ZtechEngineering"}
            </h3>
            <div className="text-gray-700 text-sm leading-relaxed">
              {activeIndex !== null
                ? renderAnswerContent(faqItems[activeIndex]?.answer)
                : renderAnswerContent(faqItems[0]?.answer)}
            </div>
          </div>
        </div>

        {/* Mobile Accordion Layout (shown only on mobile) */}
        <div className="md:hidden max-w-lg mx-auto px-4">
          {faqItems.map((item, index) => (
            <div
              key={`mobile-${index}`}
              className="mb-4 border rounded-lg overflow-hidden shadow-sm"
            >
              <div
                className={`p-4 cursor-pointer flex items-center justify-between ${
                  activeIndex === index ? "bg-[#E0E3FF]" : "bg-white"
                }`}
                onClick={() => toggleAccordion(index)}
              >
                <h3 className="font-medium text-sm pr-2">{item.question}</h3>
                <div className="flex items-center">
                  <div
                    className={`w-4 h-4 mr-2 rounded-full ${
                      activeIndex === index ? "bg-[#606AF5]" : "bg-[#606AF566]"
                    }`}
                  ></div>
                  <ChevronDown
                    className={`w-5 h-5 text-blue-500 transition-transform duration-300 ${
                      activeIndex === index ? "rotate-180" : ""
                    }`}
                  />
                </div>
              </div>

              {/* Improved Accordion Content for Mobile */}
              <div
                className={`overflow-hidden transition-all duration-300 bg-[#FAFBFF] ${
                  activeIndex === index ? "max-h-[2000px] p-4" : "max-h-0"
                }`}
              >
                <div className="text-gray-700 text-sm leading-relaxed">
                  {renderAnswerContent(item.answer)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ2;
