const fs = require('fs');
const path = require("path");
const Ticket = require("../models/Ticket");

// Create a new ticket
exports.createTicket = async (ticketData) => {
  try {
    // Create a new ticket instance
    const ticket = new Ticket(ticketData);

    // Save the ticket to the database
    const savedTicket = await ticket.save();

    // Populate the 'creator' field with specific fields
    const populatedTicket = await Ticket.findById(savedTicket._id)
      .populate('creator', 'firstName lastName email favoriteLang');

    return populatedTicket;
  } catch (error) {
    console.error("Error creating ticket:", error);
    throw new Error("Failed to create ticket");
  }
};

// Get all tickets sorted by creation date (newest first)
exports.getAllTickets = async () => {
  try {
    return await Ticket.find()
    .sort({ createdAt: -1 })
    .populate("creator", "firstName lastName email"); // Populate creator details (name and email);
  } catch (error) {
    throw new Error("Failed to fetch tickets");
  }
};

// Get a ticket by ID
exports.getTicketById = async (ticketId) => {
  try {
    return await Ticket.findById(ticketId).populate("creator", "firstName lastName email");
  } catch (error) {
    throw new Error("Failed to fetch ticket");
  }
};

// Get tickets for a specific user by user ID
exports.getUserTickets = async (userId) => {
  try {
    return await Ticket.find({ creator: userId, status: { $ne: "deleted" } })
    .sort({ createdAt: -1 })
    .populate("creator", "firstName lastName email"); // Populate creator details (name and email);
  } catch (error) {
    throw new Error("Failed to fetch user tickets");
  }
};

exports.updateTicket = async (ticketId, updateData) => {
  try {
    const ticket = await Ticket.findById(ticketId);
    if (!ticket) {
      throw new Error("Ticket not found");
    }

    // Merge new images with existing ones if provided
    if (updateData.images && Array.isArray(updateData.images)) {
      ticket.images = [...ticket.images, ...updateData.images];
    }

    // Prepare update object
    const updateFields = { ...updateData };

    // Update the ticket in the database
    const updatedTicket = await Ticket.findByIdAndUpdate(
      ticketId,
      { $set: updateFields },
      { new: true }
    );

    if (!updatedTicket) {
      throw new Error("Ticket not found after update attempt");
    }

    console.log("Ticket updated successfully:", updatedTicket);
    return updatedTicket;
  } catch (error) {
    console.error("Error updating ticket:", error.message);
    throw new Error("Failed to update ticket");
  }
};


exports.updateTicket = async (ticketId, updateData) => {
  try {
    const ticket = await Ticket.findById(ticketId);

    if (!ticket) {
      throw new Error("Ticket not found");
    }

    // Extract oldImages and images from updateData
    const { oldImages = [], images = [] } = updateData;

    // Combine oldImages and newly uploaded images
    const updatedImages = [...oldImages, ...images];

    // Identify images to remove (those in the database but not in oldImages)
    const imagesToRemove = ticket.images.filter(
      (image) => !oldImages.includes(image)
    );

    // Remove images from the server
    if (imagesToRemove.length > 0) {
      imagesToRemove.forEach((image) => {
        const imagePath = path.join(__dirname, "..", "public", image); // Full image path
        fs.unlink(imagePath, (err) => {
          if (err) {
            console.error("Error deleting image:", err);
          } else {
            console.log("Image removed from server:", imagePath);
          }
        });
      });
    }

    // Prepare the data to be updated
    const updateFields = {
      ...updateData,
      images: updatedImages, // Set the updated images array
    };

    // Update the ticket in the database
    const updatedTicket = await Ticket.findByIdAndUpdate(
      ticketId,
      { $set: updateFields },
      { new: true } // Return the updated document
    );

    return updatedTicket;
  } catch (error) {
    console.error("Error updating ticket:", error.message);
    throw new Error("Failed to update ticket");
  }
};



// Delete a ticket by ID
exports.deleteTicket = async (ticketId) => {
  try {
     
    return await Ticket.findByIdAndUpdate(ticketId, {status: "deleted"}, {new: true});
  } catch (error) {
    throw new Error("Failed to delete ticket");
  }
};
