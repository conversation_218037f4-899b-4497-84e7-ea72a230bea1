"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogHeader,
  DialogBody,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@material-tailwind/react";
import {
  ChevronDown,
  ChevronUp,
  Server,
  ArrowLeft,
  Shield,
  ShieldCheck,
  Calendar,
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Copy,
  Download,
  ChevronDownIcon
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { adminService } from "@/app/services/adminService";
import sslService from "@/app/services/sslService";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { OrderStatus } from "@/app/config/ConstStatus";
import { truncateString } from "@/app/helpers/helpers";

// Custom Scrollbar Styles
const scrollbarStyles = `
  .modern-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
  .modern-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .modern-scrollbar::-webkit-scrollbar-thumb {
    background: #9ca3af;
    border-radius: 9999px;
  }
  .modern-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
  .modern-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #9ca3af transparent;
  }
`;

// Status Styling
const STATUS_STYLES = {
  [OrderStatus.PENDING]: { bg: "bg-yellow-100", text: "text-yellow-800" },
  [OrderStatus.PROCESSING]: { bg: "bg-blue-100", text: "text-blue-800" },
  [OrderStatus.COMPLETED]: { bg: "bg-green-100", text: "text-green-800" },
  [OrderStatus.SHIPPED]: { bg: "bg-indigo-100", text: "text-indigo-800" },
  [OrderStatus.CANCELLED]: { bg: "bg-red-100", text: "text-red-800" },
  [OrderStatus.FAILED]: { bg: "bg-gray-100", text: "text-gray-800" },
  [OrderStatus.REFUNDED]: { bg: "bg-teal-100", text: "text-teal-800" },
  [OrderStatus.PROCESSINGREFUND]: { bg: "bg-orange-100", text: "text-orange-800" },
  [OrderStatus.ACTIVE]: { bg: "bg-emerald-100", text: "text-emerald-800" },
  [OrderStatus.EXPIRED]: { bg: "bg-red-100", text: "text-red-800" },
  unknown: { bg: "bg-gray-100", text: "text-gray-800" },
};

export default function OrderDetails() {
  const { orderId } = useParams();
  const router = useRouter();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [pendingUpdate, setPendingUpdate] = useState({ subOrderId: null, newStatus: null });
  const [menuOpenStates, setMenuOpenStates] = useState({}); // Track open state for each suborder
  const [expandedSubOrders, setExpandedSubOrders] = useState(new Set()); // Track expanded state for suborders
  const [updatingCertificate, setUpdatingCertificate] = useState(null); // Track which certificate is being updated

  useEffect(() => {
    if (orderId) {
      const fetchOrderDetails = async () => {
        try {
          const response = await adminService.getOrderDetails(orderId);
          setOrder(response.data.data);
        } catch (error) {
          console.error("Error fetching order details:", error);
        } finally {
          setLoading(false);
        }
      };
      fetchOrderDetails();
    }
  }, [orderId]);

  if (!order) return <div className="text-center py-8 text-red-500">Order not found</div>;

  const formatDate = (dateString) =>
    new Date(dateString).toLocaleDateString("fr-FR", { day: "2-digit", month: "2-digit", year: "numeric" });

  const getStatusStyle = (status) => STATUS_STYLES[status] || STATUS_STYLES.unknown;

  const getStatusIcon = (status) => {
    switch (status) {
      case OrderStatus.PROCESSING:
        return <Clock className="h-4 w-4" />;
      case OrderStatus.COMPLETED:
      case OrderStatus.ACTIVE:
        return <CheckCircle2 className="h-4 w-4" />;
      case OrderStatus.CANCELLED:
      case OrderStatus.FAILED:
      case OrderStatus.EXPIRED:
        return <XCircle className="h-4 w-4" />;
      case OrderStatus.PENDING:
        return <AlertCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const handleUpdateStatus = (subOrderId, newStatus) => {
    setPendingUpdate({ subOrderId, newStatus });
    setOpenModal(true);
  };

  const confirmUpdateStatus = async () => {
    const { subOrderId, newStatus } = pendingUpdate;

    if (!subOrderId || !newStatus) return;

    try {
      setLoading(true);
      await adminService.updateSubOrderStatus(orderId, subOrderId, { status: newStatus });
      setOrder((prevOrder) => ({
        ...prevOrder,
        subOrders: prevOrder.subOrders.map((subOrder) =>
          subOrder._id === subOrderId ? { ...subOrder, status: newStatus } : subOrder
        ),
      }));
      toast.success(`Sub-order status updated to ${newStatus}`, {
        position: "top-right",
        autoClose: 3000,
      });
    } catch (err) {
      toast.error(err.response?.data?.message || "Failed to update sub-order status", {
        position: "top-right",
        autoClose: 3000,
      });
    } finally {
      setLoading(false);
      setOpenModal(false);
      setPendingUpdate({ subOrderId: null, newStatus: null });
    }
  };

  // Toggle menu open state for a specific suborder
  const toggleMenu = (subOrderId) => {
    setMenuOpenStates((prev) => ({
      ...prev,
      [subOrderId]: !prev[subOrderId], // Toggle the state for the specific suborder
    }));
  };

  // Toggle expanded state for a suborder
  const toggleExpanded = (subOrderId) => {
    const newExpanded = new Set(expandedSubOrders);
    if (newExpanded.has(subOrderId)) {
      newExpanded.delete(subOrderId);
    } else {
      newExpanded.add(subOrderId);
    }
    setExpandedSubOrders(newExpanded);
  };

  // Copy text to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard", {
      position: "top-right",
      autoClose: 1500,
    });
  };

  // Handle updating SSL certificate status
  const handleUpdateCertificateStatus = async (subOrderId, certificateIndex, newStatus) => {
    try {
      // Set the specific certificate as updating
      setUpdatingCertificate(`${subOrderId}-${certificateIndex}`);

      // Find the suborder to get its period
      const subOrder = order.subOrders.find(so => so._id === subOrderId);
      if (!subOrder) {
        throw new Error('Suborder not found');
      }

      // Prepare the update data
      const updateData = { status: newStatus };

      // If status is being set to ISSUED, set issuedAt to now and calculate expiresAt
      if (newStatus === 'ISSUED') {
        const now = new Date();
        const expiresAt = new Date(now);
        expiresAt.setMonth(expiresAt.getMonth() + subOrder.period); // Add the period in months

        updateData.issuedAt = now.toISOString();
        updateData.expiresAt = expiresAt.toISOString();
      }

      // Send the update to the server
      await sslService.updateCertificateStatus(subOrderId, certificateIndex, updateData);

      // Update the local state to reflect the change
      setOrder((prevOrder) => {
        const updatedSubOrders = [...prevOrder.subOrders];
        const subOrderIndex = updatedSubOrders.findIndex(so => so._id === subOrderId);

        if (subOrderIndex !== -1) {
          const certificate = updatedSubOrders[subOrderIndex].ssl[certificateIndex.toString()];
          if (certificate) {
            certificate.status = newStatus;

            if (newStatus === 'ISSUED') {
              certificate.issuedAt = updateData.issuedAt;
              certificate.expiresAt = updateData.expiresAt;
            }
          }
        }

        return {
          ...prevOrder,
          subOrders: updatedSubOrders
        };
      });

      toast.success(`Certificate status updated to ${newStatus}`, {
        position: "top-right",
        autoClose: 3000,
      });
    } catch (error) {
      console.error('Error updating certificate status:', error);
      toast.error(error.response?.data?.message || 'Failed to update certificate status', {
        position: "top-right",
        autoClose: 3000,
      });
    } finally {
      // Clear the updating state
      setUpdatingCertificate(null);
    }
  };


  return (
    <>
      <style>{scrollbarStyles}</style>
      <div className="container mx-auto p-8 bg-gray-50 rounded-lg">
        {/* Back Arrow Button */}
        <div className="flex items-center mb-6">
          <Button
            variant="text"
            color="blue"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-5 h-5" />
            Back to Orders
          </Button>
        </div>

        {/* Order Summary Card - Always visible */}
        <Card className="w-full mb-6 p-6 shadow-sm rounded-lg bg-white">
          <div className="border-b border-gray-200 pb-4 mb-4">
            {/* Order ID and Status in a single row */}
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
              <Typography variant="h3" className="text-2xl font-semibold text-gray-800 mr-3">
                Order #{order.identifiant.toUpperCase()}
              </Typography>

              <div className={`${getStatusStyle(order.status).bg} ${getStatusStyle(order.status).text} px-3 py-1 rounded-full text-sm inline-flex items-center gap-2`}>
                {getStatusIcon(order.status)}
                <span>{order.status}</span>
              </div>
            </div>

            {/* Order details and price in a second row */}
            <div className="flex flex-wrap justify-between items-end">
              <div className="flex flex-wrap gap-4 text-gray-600">
                <div className="flex items-center gap-1.5">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>Created: {formatDate(order.createdAt)}</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <span className="text-gray-500">Payment:</span>
                  <span className="font-medium">{order.paymentMethod}</span>
                </div>
                <div className="flex items-center gap-1.5">
                  <span className="text-gray-500">Customer:</span>
                  <span className="font-medium">{order.user?.firstName} {order.user?.lastName}</span>
                </div>
              </div>

              <Typography className="text-lg font-bold text-green-600 mt-3 md:mt-0">
                {Number(order.totalPrice).toFixed(2)} {order.currency || "MAD"}
              </Typography>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
            <div>
              <Typography className="font-medium text-gray-800 mb-2">Billing Details</Typography>
              <div className="text-sm text-gray-600 space-y-1">
                <p>{order.billingInfo?.BillToName}</p>
                <p>{order.billingInfo?.email}</p>
                <p>{order.billingInfo?.phone}</p>
                <p>{order.billingInfo?.address}</p>
                <p>{order.billingInfo?.country}</p>
              </div>
            </div>

            <div>
              <Typography className="font-medium text-gray-800 mb-2">Order Summary</Typography>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{Number(order.subTotal).toFixed(2)} MAD</span>
                </div>
                <div className="flex justify-between">
                  <span>Discount:</span>
                  <span>{Number(order.totalDiscount).toFixed(2)} MAD</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax ({(order.taxRate * 100).toFixed(0)}%):</span>
                  <span>{(order.totalPrice * order.taxRate).toFixed(2)} MAD</span>
                </div>
                <div className="flex justify-between font-semibold">
                  <span>Total:</span>
                  <span>{Number(order.totalPrice).toFixed(2)} MAD</span>
                </div>
              </div>
            </div>

            <div>
              <Typography className="font-medium text-gray-800 mb-2">Transaction Details</Typography>
              <div className="text-sm text-gray-600 space-y-1">
                <div className="flex justify-between">
                  <span>Transaction ID:</span>
                  <span>{order.transactionId || "-"}</span>
                </div>
                <div className="flex justify-between">
                  <span>Payment Status:</span>
                  <span>{order.isPaid ? "Paid" : "Unpaid"}</span>
                </div>
                {order.datePaid && (
                  <div className="flex justify-between">
                    <span>Payment Date:</span>
                    <span>{formatDate(order.datePaid)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Order Items Section */}
        <div className="space-y-4">
          <Typography variant="h4" className="text-xl font-semibold text-gray-800 mb-4">
            Order Items ({order.subOrders.length})
          </Typography>

          {order.subOrders.map((subOrder) => {
            const statusStyle = getStatusStyle(subOrder.status);
            const isMenuOpen = menuOpenStates[subOrder._id] || false;
            const isExpanded = expandedSubOrders.has(subOrder._id);
            const expireAt = new Date(subOrder.createdAt);
            expireAt.setMonth(expireAt.getMonth() + subOrder.period);

            // Check if this is an SSL certificate by package category
            const isSSL = subOrder.package?.brand?.category?.name === "SSL" ||
                          (subOrder.ssl && Object.keys(subOrder.ssl).length > 0) ||
                          subOrder.package?.name?.toLowerCase().includes("ssl");

            console.log('SSL check:', {
              isSSL,
              categoryName: subOrder.package?.brand?.category?.name,
              hasSSLData: !!subOrder.ssl,
              certificateCount: subOrder.ssl ? Object.keys(subOrder.ssl).length : 0
            });

            return (
              <Card key={subOrder._id} className="overflow-hidden border border-gray-200 shadow-sm">
                {/* Item Header - Always visible */}
                <div className="p-5 bg-white">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div className="flex items-center gap-4">
                      {isSSL ? (
                        <div className="bg-blue-50 p-2 rounded-lg">
                          <ShieldCheck className="h-6 w-6 text-blue-500" />
                        </div>
                      ) : (
                        <Server className="h-10 w-10 text-blue-500" />
                      )}
                      <div>
                        <div className="flex items-center gap-3 mb-1">
                          <Typography className="text-lg font-semibold text-gray-800">
                            {subOrder.package?.name}
                          </Typography>
                          <Menu placement="bottom-start">
                            <MenuHandler>
                              <div className="flex items-center gap-1 cursor-pointer hover:opacity-80 transition-opacity">
                                <Chip
                                  value={subOrder.status}
                                  className={`${statusStyle.bg} ${statusStyle.text} font-medium rounded-full px-3 py-1 text-xs inline-flex items-center gap-1.5`}
                                  icon={getStatusIcon(subOrder.status)}
                                />
                                <ChevronDownIcon className="h-4 w-4 text-gray-600" />
                              </div>
                            </MenuHandler>
                            <MenuList className="p-1 max-h-64 modern-scrollbar">
                              {Object.values(OrderStatus).map(statusOption => (
                                <MenuItem
                                  key={statusOption}
                                  onClick={() => handleUpdateStatus(subOrder._id, statusOption)}
                                  className={`flex items-center gap-2 ${subOrder.status === statusOption ? "bg-gray-100" : ""}`}
                                >
                                  <span
                                    className={`h-2.5 w-2.5 rounded-full ${STATUS_STYLES[statusOption].bg.replace("100", "500")}`}
                                  />
                                  <Typography variant="small" className="font-normal">
                                    {statusOption}
                                  </Typography>
                                </MenuItem>
                              ))}
                            </MenuList>
                          </Menu>
                        </div>
                        <div className="flex flex-wrap gap-3 text-sm text-gray-600">
                          <span>#{subOrder.identifiant.toUpperCase()}</span>
                          <span>•</span>
                          <span>Qty: {subOrder.quantity}</span>
                          <span>•</span>
                          <span>Period: {subOrder.period} {subOrder.period === 1 ? 'month' : 'months'}</span>
                          <span>•</span>
                          <span>Price: {Number(subOrder.price).toFixed(2)} MAD</span>
                        </div>
                      </div>
                    </div>

                    {/* SSL certificate toggle button */}
                    {isSSL && (
                      <div className="mt-3 md:mt-0">
                        <Button
                          variant="text"
                          color="blue"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={() => toggleExpanded(subOrder._id)}
                        >
                          {isExpanded ? (
                            <>
                              <ChevronUp className="h-4 w-4" />
                              Hide Certificates
                            </>
                          ) : (
                            <>
                              <ChevronDown className="h-4 w-4" />
                              Show Certificates
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Always show expanded content (removing conditional rendering) */}
                <div className="bg-gray-50 border-t border-gray-200 p-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                      <Typography className="font-medium text-gray-800 mb-2">Order Details</Typography>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div><span className="text-gray-500">Issued on:</span> {formatDate(subOrder.createdAt)}</div>
                        <div><span className="text-gray-500">Expires:</span> {formatDate(expireAt)}</div>
                        <div><span className="text-gray-500">Based Price:</span> {subOrder.basedPrice ? Number(subOrder.basedPrice).toFixed(2) : "-"} MAD</div>
                        <div><span className="text-gray-500">Discount:</span> {subOrder.discount ? Number(subOrder.discount).toFixed(2) : "0.00"} MAD</div>
                      </div>
                    </div>

                    <div className="p-4 bg-white rounded-lg border border-gray-200">
                      <Typography className="font-medium text-gray-800 mb-2">Package Details</Typography>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div><span className="text-gray-500">Package ID:</span> {subOrder.package?._id}</div>
                        <div><span className="text-gray-500">Description:</span> {truncateString(subOrder.package?.description || "-", 30)}</div>
                        <div><span className="text-gray-500">Brand:</span> {subOrder.package?.brand?.name || "-"}</div>
                        <div><span className="text-gray-500">Category:</span> {subOrder.package?.brand?.category?.name || "-"}</div>
                      </div>
                    </div>
                  </div>

                  {/* SSL-specific details */}
                  {isSSL && isExpanded && (
                    <div className="mt-4">
                      <Typography className="font-medium text-gray-800 mb-3">
                        SSL Certificate Details ({subOrder.quantity})
                      </Typography>

                      <div className="space-y-4">
                        {subOrder.ssl && Object.entries(subOrder.ssl).map(([index, cert]) => {
                          const i = parseInt(index);
                          const isPending = !cert.status || cert.status === 'PENDING';

                          return (
                            <div key={index} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                              {/* Certificate Header */}
                              <div className="p-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <ShieldCheck className="h-5 w-5 text-blue-500" />
                                  <Typography className="font-medium text-gray-800">
                                    Certificate #{i + 1}
                                  </Typography>
                                </div>
                                {cert.status && (
                                  <span className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(cert.status)}`}>
                                    {getSSLStatusIcon(cert.status)}
                                    {cert.status?.toLowerCase() || "pending"}
                                  </span>
                                )}
                              </div>

                              {/* Certificate Details */}
                              <div className="p-4">
                                {isPending ? (
                                  <div className="text-sm text-gray-600 italic flex items-center gap-2">
                                    <Clock className="h-4 w-4 text-blue-500" />
                                    Certificate not yet activated by customer
                                  </div>
                                ) : (
                                  <div className="space-y-4">
                                    {/* Basic Info */}
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                                      {/* Domain */}
                                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                        <div className="flex items-center justify-between mb-1">
                                          <Typography className="text-xs font-medium text-gray-500">Domain</Typography>
                                          {cert.domain && (
                                            <Tooltip content="Copy Domain">
                                              <Button
                                                variant="text"
                                                size="sm"
                                                color="blue"
                                                className="p-1 h-6 w-6"
                                                onClick={() => copyToClipboard(cert.domain)}
                                              >
                                                <Copy className="h-3.5 w-3.5" />
                                              </Button>
                                            </Tooltip>
                                          )}
                                        </div>
                                        <Typography className="font-medium text-sm">
                                          {cert.domain || "-"}
                                        </Typography>
                                      </div>

                                      {/* Validation Email */}
                                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                        <div className="flex items-center justify-between mb-1">
                                          <Typography className="text-xs font-medium text-gray-500">Validation Email</Typography>
                                          {cert.validationEmail && (
                                            <Tooltip content="Copy Email">
                                              <Button
                                                variant="text"
                                                size="sm"
                                                color="blue"
                                                className="p-1 h-6 w-6"
                                                onClick={() => copyToClipboard(cert.validationEmail)}
                                              >
                                                <Copy className="h-3.5 w-3.5" />
                                              </Button>
                                            </Tooltip>
                                          )}
                                        </div>
                                        <Typography className="font-medium text-sm truncate" title={cert.validationEmail}>
                                          {cert.validationEmail || "-"}
                                        </Typography>
                                      </div>

                                      {/* Install Service */}
                                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                        <Typography className="text-xs font-medium text-gray-500 mb-1">Installation Service</Typography>
                                        <div className="flex items-center gap-1.5">
                                          {cert.installService ? (
                                            <>
                                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                                              <Typography className="font-medium text-sm text-green-600">Requested</Typography>
                                            </>
                                          ) : (
                                            <>
                                              <XCircle className="h-4 w-4 text-gray-400" />
                                              <Typography className="font-medium text-sm text-gray-600">Not Requested</Typography>
                                            </>
                                          )}
                                        </div>
                                      </div>
                                    </div>

                                    {/* Dates Row */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                        <Typography className="text-xs font-medium text-gray-500 mb-1">Issued At</Typography>
                                        <div className="flex items-center gap-2">
                                          <Calendar className="h-4 w-4 text-blue-500" />
                                          <Typography className="font-medium text-sm">
                                            {cert.issuedAt ? formatDate(cert.issuedAt) : "Not issued yet"}
                                          </Typography>
                                        </div>
                                      </div>

                                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                        <Typography className="text-xs font-medium text-gray-500 mb-1">Expires At</Typography>
                                        <div className="flex items-center gap-2">
                                          <Calendar className="h-4 w-4 text-blue-500" />
                                          <Typography className="font-medium text-sm">
                                            {cert.expiresAt ? formatDate(cert.expiresAt) : "Not set"}
                                          </Typography>
                                        </div>
                                      </div>
                                    </div>

                                    {/* CSR Data */}
                                    {cert.csr && (
                                      <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                        <div className="flex items-center justify-between mb-2">
                                          <Typography className="text-xs font-medium text-gray-500">CSR Data</Typography>
                                          <div className="flex gap-1">
                                            <Tooltip content="Copy Full CSR">
                                              <Button
                                                variant="text"
                                                size="sm"
                                                color="blue"
                                                className="p-1.5 h-7 flex items-center gap-1 text-xs"
                                                onClick={() => copyToClipboard(cert.csr)}
                                              >
                                                <Copy className="h-3.5 w-3.5" />
                                                Copy Full CSR
                                              </Button>
                                            </Tooltip>
                                          </div>
                                        </div>
                                        <div className="font-mono text-xs bg-white p-3 rounded border border-gray-200 overflow-x-auto max-h-32 overflow-y-auto modern-scrollbar">
                                          {cert.csr ? cert.csr.split('\n').map((line, idx) => (
                                            <div key={idx} className="whitespace-nowrap">{line}</div>
                                          )) : "No CSR data available"}
                                        </div>
                                      </div>
                                    )}

                                    {/* Certificate Action Buttons */}
                                    <div className="flex flex-wrap gap-2 justify-end mt-2">
                                      {cert.status === 'ISSUED' && (
                                        <>
                                          {cert.certificateFile && (
                                            <Button
                                              variant="outlined"
                                              color="blue"
                                              size="sm"
                                              className="flex items-center gap-2"
                                            >
                                              <Download className="h-4 w-4" />
                                              Download Certificate
                                            </Button>
                                          )}

                                          <Tooltip content="Mark certificate as installed">
                                            <Button
                                              variant="filled"
                                              color="green"
                                              size="sm"
                                              className="flex items-center gap-2"
                                              onClick={() => handleUpdateCertificateStatus(subOrder._id, index, 'INSTALLED')}
                                              disabled={updatingCertificate === `${subOrder._id}-${index}`}
                                            >
                                              {updatingCertificate === `${subOrder._id}-${index}` ? (
                                                <>
                                                  <span className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></span>
                                                  Processing...
                                                </>
                                              ) : (
                                                <>
                                                  <CheckCircle2 className="h-4 w-4" />
                                                  Mark as Installed
                                                </>
                                              )}
                                            </Button>
                                          </Tooltip>
                                        </>
                                      )}

                                      {cert.status === 'PROCESSING' && (
                                        <Tooltip content="Mark certificate as issued">
                                          <Button
                                            variant="filled"
                                            color="blue"
                                            size="sm"
                                            className="flex items-center gap-2"
                                            onClick={() => handleUpdateCertificateStatus(subOrder._id, index, 'ISSUED')}
                                            disabled={updatingCertificate === `${subOrder._id}-${index}`}
                                          >
                                            {updatingCertificate === `${subOrder._id}-${index}` ? (
                                              <>
                                                <span className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></span>
                                                Processing...
                                              </>
                                            ) : (
                                              <>
                                                <CheckCircle2 className="h-4 w-4" />
                                                Mark as Issued
                                              </>
                                            )}
                                          </Button>
                                        </Tooltip>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
                {console.log('Full subOrder:', JSON.stringify(subOrder, null, 2))}
              </Card>
            );
          })}
        </div>

        {/* Confirmation Modal */}
        <Dialog open={openModal} handler={() => setOpenModal(false)}>
          <DialogHeader>Confirm Status Update</DialogHeader>
          <DialogBody>
            Are you sure you want to update the sub-order status to{" "}
            <strong>{pendingUpdate.newStatus}</strong>?
          </DialogBody>
          <DialogFooter>
            <Button
              variant="text"
              color="gray"
              onClick={() => setOpenModal(false)}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button variant="gradient" color="blue" onClick={confirmUpdateStatus}>
              Confirm
            </Button>
          </DialogFooter>
        </Dialog>
      </div>
    </>
  );
}

// Helper function for SSL status colors
function getStatusColor(status) {
  switch (status?.toUpperCase()) {
    case 'PROCESSING':
      return "bg-yellow-100 text-yellow-800";
    case 'ISSUED':
      return "bg-green-100 text-green-800";
    case 'INSTALLED':
      return "bg-emerald-100 text-emerald-800";
    case 'EXPIRED':
      return "bg-red-100 text-red-800";
    case 'PENDING':
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Helper function for SSL status icons
function getSSLStatusIcon(status) {
  switch (status?.toUpperCase()) {
    case 'PROCESSING':
      return <Clock className="h-4 w-4" />;
    case 'ISSUED':
      return <CheckCircle2 className="h-4 w-4" />;
    case 'INSTALLED':
      return <ShieldCheck className="h-4 w-4" />;
    case 'EXPIRED':
      return <XCircle className="h-4 w-4" />;
    case 'PENDING':
      return <AlertCircle className="h-4 w-4" />;
    default:
      return null;
  }
}
