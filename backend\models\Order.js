const mongoose = require('mongoose');
const OrderStatus = require('../constants/enums/order-status');
const PaymentMethod = require('../constants/enums/payment-method');
const Schema = mongoose.Schema;

const orderSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    subOrders: [
        {
            type: Schema.Types.ObjectId,
            ref: 'SubOrder',
            required: true
        }
    ],
    identifiant: { type: String, required: true, unique: true },
    totalPrice: { type: Number, required: true },
    shippingAddress: { type: String },
    totalDiscount: { type: Number, default: 0 },
    subTotal: { type: Number, default: 0 },
    shippingFee: { type: Number, default: 0 },
    taxRate: { type: Number, default: 0.2 },
    taxAmount: { type: Number, default: 0 },
    status: {
        type: String,
        required: true,
        enum: [
            OrderStatus.PENDING,
            OrderStatus.PROCESSING,
            OrderStatus.COMPLETED,
            OrderStatus.SHIPPED,
            OrderStatus.PROCESSINGREFUND,
            OrderStatus.REFUNDED,
            OrderStatus.FAILED,
            OrderStatus.CANCELLED,
        ],
        default: OrderStatus.PENDING,
    },
    paymentMethod: {
        type: String,
        required: true,
        enum: [
            PaymentMethod.CMI,
            PaymentMethod.COD,
            PaymentMethod.PAYZONE,
        ],
        default: PaymentMethod.PAYZONE,
    },
    isPaid: { type: Boolean, default: false },
    billingInfo: {
        BillToName: { type: String, required: true, maxlength: 50 },
        email: { type: String, required: true, maxlength: 50 },
        phone: { type: String, required: true, maxlength: 20 },
        country: { type: String, maxlength: 100 },
        address: { type: String, maxlength: 100 },
        // Company specific fields
        isCompany: { type: Boolean, default: false },
        companyICE: { type: String, maxlength: 255 },
        companyAddress: { type: String, maxlength: 255 },
        companyPhone: { type: String, maxlength: 20 },
        companyEmail: { type: String, maxlength: 50 }
    },
    note: { type: String },
    datePaid: { type: Date },
    transactionId: { type: String },
    customerIpAddress: { type: String },
    customerUserAgent: { type: String },
    currency: { type: String, default: 'MAD' },
    deletedBy: [{
        value: { type: Schema.Types.ObjectId, ref: 'User', required: true },
        date: { type: Date, default: Date.now },
    }],
    isPaymentProcessed: { type: Boolean, default: false, required: true },
    callBackConfirmed: {
        value: { type: Boolean, default: false },
        at: { type: Date },
    },
    lastSSLActivation: { type: Date, default: null },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

const Order = mongoose.model('Order', orderSchema);
module.exports = Order;
