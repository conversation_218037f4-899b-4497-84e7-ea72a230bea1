import React from 'react';
import Section from '../home/<USER>';
import { motion } from "framer-motion";
import { Typography } from '@material-tailwind/react';
import Image from 'next/image';
import CTAButtons from '../home/<USER>';

import dataCenterImg from "/public/images/services/data-center.png";

const REASONS = Object.freeze([
  {
    id: "local_experience",
    titleKey: "reasons.0.title",
    descriptionKey: "reasons.0.description",
  },
  {
    id: "tailored_service",
    titleKey: "reasons.1.title",
    descriptionKey: "reasons.1.description",
  },
  {
    id: "security_commitment",
    titleKey: "reasons.2.title",
    descriptionKey: "reasons.2.description",
  },
]);

const ManagedServicesReasons = ({t, animations}) => {
  return (
    <Section bgColor="bg-white">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={animations.fadeInUp}
        >
          {/* Left Column: Content */}
          <div className="space-y-8">
            {/* Title */}
            <Typography
              variant="h2"
              className="text-3xl font-inter md:text-4xl font-bold text-gray-900 leading-tight"
            >
              {t("why_choose_title")}
            </Typography>

            {/* Reasons List */}
            <div className="space-y-6">
              {REASONS.map(({ id, titleKey, descriptionKey }, index) => (
                <motion.div
                  key={id}
                  className="border-l-4 border-indigo-600 pl-4"
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={animations.fadeInUp}
                  transition={{ delay: index * 0.1 }}
                >
                  {/* Reason Title */}
                  <Typography
                    variant="h5"
                    className="text-xl font-inter font-semibold text-indigo-600"
                  >
                    {t(titleKey)}
                  </Typography>

                  {/* Reason Description */}
                  <Typography
                    variant="paragraph"
                    className="mt-2 font-inter text-gray-600 text-base leading-relaxed"
                  >
                    {t(descriptionKey)}
                  </Typography>
                </motion.div>
              ))}
            </div>

            {/* Call-to-Action Buttons */}
            <CTAButtons t={t} />
          </div>

          {/* Right Column: Image */}
          <Image
            src={dataCenterImg}
            alt={t("data_center_alt")}
            sizes="(max-width: 768px) 100vw, 50vw"
            className="w-full block md:w-3/4 h-auto md:ml-24 rounded-md shadow-lg object-cover"
            quality={85}
            loading="lazy"
          />
        </motion.div>
      </Section>
  )
}

export default ManagedServicesReasons