'use client';

import React, { useState, useEffect } from 'react';
import { CheckCircle, X, Shield, Key, Server, AlertCircle, Info, Monitor } from 'lucide-react';

const SuccessNotification = ({ 
  isOpen, 
  onClose, 
  title = "Opération réussie",
  message = "L'opération a été effectuée avec succès.",
  type = "success", // success, info, warning, error
  autoClose = true,
  autoCloseDelay = 5000,
  icon: CustomIcon = null
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      
      if (autoClose) {
        const timer = setTimeout(() => {
          handleClose();
        }, autoCloseDelay);
        
        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, autoClose, autoCloseDelay]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300); // Attendre la fin de l'animation
  };

  if (!isOpen) return null;

  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          iconColor: 'text-green-600',
          titleColor: 'text-green-800',
          messageColor: 'text-green-700',
          icon: CustomIcon || CheckCircle
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          iconColor: 'text-blue-600',
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700',
          icon: CustomIcon || Shield
        };
      case 'warning':
        return {
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          iconColor: 'text-orange-600',
          titleColor: 'text-orange-800',
          messageColor: 'text-orange-700',
          icon: CustomIcon || Shield
        };
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconColor: 'text-red-600',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700',
          icon: CustomIcon || AlertCircle
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          iconColor: 'text-gray-600',
          titleColor: 'text-gray-800',
          messageColor: 'text-gray-700',
          icon: CustomIcon || CheckCircle
        };
    }
  };

  const config = getTypeConfig();
  const IconComponent = config.icon;

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-20 px-4">
      {/* Overlay */}
      <div 
        className={`fixed inset-0 bg-black transition-opacity duration-300 ${
          isVisible ? 'bg-opacity-25' : 'bg-opacity-0'
        }`}
        onClick={handleClose}
      />
      
      {/* Notification */}
      <div 
        className={`relative w-full max-w-md transform transition-all duration-300 ${
          isVisible 
            ? 'translate-y-0 opacity-100 scale-100' 
            : '-translate-y-4 opacity-0 scale-95'
        }`}
      >
        <div className={`
          ${config.bgColor} ${config.borderColor} 
          border rounded-lg shadow-lg p-6
        `}>
          <div className="flex items-start space-x-4">
            {/* Icon */}
            <div className={`flex-shrink-0 ${config.iconColor}`}>
              <IconComponent className="w-6 h-6" />
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <h3 className={`text-sm font-semibold ${config.titleColor} mb-1`}>
                {title}
              </h3>
              <p className={`text-sm ${config.messageColor} leading-relaxed`}>
                {message}
              </p>
            </div>
            
            {/* Close button */}
            <button
              onClick={handleClose}
              className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          {/* Progress bar for auto-close */}
          {autoClose && (
            <div className="mt-4 w-full bg-gray-200 rounded-full h-1">
              <div 
                className={`h-1 rounded-full transition-all ease-linear ${
                  type === 'success' ? 'bg-green-500' :
                  type === 'info' ? 'bg-blue-500' :
                  type === 'warning' ? 'bg-orange-500' :
                  type === 'error' ? 'bg-red-500' : 'bg-gray-500'
                }`}
                style={{
                  width: '100%',
                  animation: `shrink ${autoCloseDelay}ms linear forwards`
                }}
              />
            </div>
          )}
        </div>
      </div>
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

// Composant spécialisé pour le succès de reset password
export const PasswordResetSuccessNotification = ({ isOpen, onClose, serverInfo }) => {
  return (
    <SuccessNotification
      isOpen={isOpen}
      onClose={onClose}
      title="Mot de passe réinitialisé"
      message="Le mot de passe root a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec le nouveau mot de passe."
      type="success"
      icon={Key}
      autoClose={true}
      autoCloseDelay={6000}
    />
  );
};

// Composant spécialisé pour les actions VPS
export const VPSActionSuccessNotification = ({ 
  isOpen, 
  onClose, 
  action = "action",
  serverInfo = null 
}) => {
  const getActionMessage = (action) => {
    switch (action.toLowerCase()) {
      case 'start':
        return "Le serveur VPS a été démarré avec succès.";
      case 'stop':
        return "Le serveur VPS a été arrêté avec succès.";
      case 'restart':
        return "Le serveur VPS a été redémarré avec succès.";
      case 'reset-password':
        return "Le mot de passe root a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec le nouveau mot de passe.";
      case 'reinstall':
        return "La réinstallation du serveur VPS a été initiée avec succès.";
      case 'rescue':
        return "Le mode rescue a été activé avec succès.";
      default:
        return `L'action "${action}" a été effectuée avec succès.`;
    }
  };

  const getActionTitle = (action) => {
    switch (action.toLowerCase()) {
      case 'start':
        return "Serveur démarré";
      case 'stop':
        return "Serveur arrêté";
      case 'restart':
        return "Serveur redémarré";
      case 'reset-password':
        return "Mot de passe réinitialisé";
      case 'reinstall':
        return "Réinstallation initiée";
      case 'rescue':
        return "Mode rescue activé";
      default:
        return "Action effectuée";
    }
  };

  return (
    <SuccessNotification
      isOpen={isOpen}
      onClose={onClose}
      title={getActionTitle(action)}
      message={getActionMessage(action)}
      type="success"
      icon={action.toLowerCase() === 'reset-password' ? Key : Server}
      autoClose={true}
      autoCloseDelay={action.toLowerCase() === 'reset-password' ? 7000 : 5000}
    />
  );
};

// Composant spécialisé pour les erreurs
export const ErrorNotification = ({ isOpen, onClose, title = "Erreur", message, autoClose = false }) => {
  return (
    <SuccessNotification
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      message={message}
      type="error"
      icon={AlertCircle}
      autoClose={autoClose}
      autoCloseDelay={8000}
    />
  );
};

// Composant spécialisé pour les informations
export const InfoNotification = ({ isOpen, onClose, title = "Information", message, autoClose = true }) => {
  return (
    <SuccessNotification
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      message={message}
      type="info"
      icon={Info}
      autoClose={autoClose}
      autoCloseDelay={6000}
    />
  );
};

// Composant spécialisé pour la console
export const ConsoleNotification = ({ isOpen, onClose }) => {
  return (
    <SuccessNotification
      isOpen={isOpen}
      onClose={onClose}
      title="Console ouverte"
      message="La console VPS a été ouverte dans un nouvel onglet. Si elle ne s'ouvre pas, vérifiez votre bloqueur de pop-ups."
      type="info"
      icon={Monitor}
      autoClose={true}
      autoCloseDelay={5000}
    />
  );
};

export default SuccessNotification;
