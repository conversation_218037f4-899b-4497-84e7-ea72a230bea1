/**
 * Currency Controller for Admin
 * Provides endpoints for testing and managing currency conversion
 */

const CurrencyService = require('../../services/currencyService');
const PricingUpdateService = require('../../services/pricingUpdateService');

class CurrencyController {
  constructor() {
    this.currencyService = new CurrencyService();
    this.pricingService = new PricingUpdateService();
  }

  /**
   * Test currency API connection
   * GET /admin/currency/test
   */
  async testConnection(req, res) {
    try {
      const result = await this.currencyService.testConnection();
      
      res.json({
        success: true,
        data: result,
        message: result.success ? 'Currency API connection successful' : 'Currency API connection failed'
      });

    } catch (error) {
      console.error('[CURRENCY] Test connection error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to test currency API connection',
        error: error.message
      });
    }
  }

  /**
   * Get current USD to MAD exchange rate
   * GET /admin/currency/rate
   */
  async getExchangeRate(req, res) {
    try {
      const rate = await this.currencyService.getUsdToMadRate();
      const cacheStatus = this.currencyService.getCacheStatus();
      
      res.json({
        success: true,
        data: {
          rate: rate,
          currency: 'USD to MAD',
          cache: cacheStatus
        },
        message: `Current exchange rate: 1 USD = ${rate} MAD`
      });

    } catch (error) {
      console.error('[CURRENCY] Get exchange rate error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to get exchange rate',
        error: error.message
      });
    }
  }

  /**
   * Convert USD amount to MAD
   * POST /admin/currency/convert
   * Body: { usdAmount: number }
   */
  async convertUsdToMad(req, res) {
    try {
      const { usdAmount } = req.body;

      if (!usdAmount || isNaN(usdAmount)) {
        return res.status(400).json({
          success: false,
          message: 'Valid USD amount is required'
        });
      }

      const madAmount = await this.currencyService.convertUsdToMad(parseFloat(usdAmount));
      const rate = await this.currencyService.getUsdToMadRate();
      
      res.json({
        success: true,
        data: {
          usdAmount: parseFloat(usdAmount),
          madAmount: madAmount,
          rate: rate,
          conversion: `$${usdAmount} USD = ${madAmount} MAD`
        },
        message: 'Currency conversion successful'
      });

    } catch (error) {
      console.error('[CURRENCY] Convert USD to MAD error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to convert currency',
        error: error.message
      });
    }
  }

  /**
   * Convert multiple USD amounts to MAD
   * POST /admin/currency/convert-batch
   * Body: { usdAmounts: number[] }
   */
  async convertBatchUsdToMad(req, res) {
    try {
      const { usdAmounts } = req.body;

      if (!Array.isArray(usdAmounts) || usdAmounts.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Array of USD amounts is required'
        });
      }

      const madAmounts = await this.currencyService.convertMultipleUsdToMad(usdAmounts);
      const rate = await this.currencyService.getUsdToMadRate();
      
      const conversions = usdAmounts.map((usd, index) => ({
        usd: usd,
        mad: madAmounts[index],
        conversion: `$${usd} USD = ${madAmounts[index]} MAD`
      }));
      
      res.json({
        success: true,
        data: {
          rate: rate,
          conversions: conversions,
          summary: {
            totalUsd: usdAmounts.reduce((sum, amount) => sum + amount, 0),
            totalMad: madAmounts.reduce((sum, amount) => sum + amount, 0),
            count: usdAmounts.length
          }
        },
        message: `Successfully converted ${usdAmounts.length} amounts`
      });

    } catch (error) {
      console.error('[CURRENCY] Convert batch USD to MAD error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to convert currencies',
        error: error.message
      });
    }
  }

  /**
   * Clear currency cache
   * POST /admin/currency/clear-cache
   */
  async clearCache(req, res) {
    try {
      this.currencyService.clearCache();
      
      res.json({
        success: true,
        message: 'Currency cache cleared successfully'
      });

    } catch (error) {
      console.error('[CURRENCY] Clear cache error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to clear currency cache',
        error: error.message
      });
    }
  }

  /**
   * Get cache status
   * GET /admin/currency/cache-status
   */
  async getCacheStatus(req, res) {
    try {
      const cacheStatus = this.currencyService.getCacheStatus();
      
      res.json({
        success: true,
        data: cacheStatus,
        message: 'Cache status retrieved successfully'
      });

    } catch (error) {
      console.error('[CURRENCY] Get cache status error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to get cache status',
        error: error.message
      });
    }
  }

  /**
   * Simulate VPS pricing conversion
   * GET /admin/currency/simulate-vps-pricing
   */
  async simulateVpsPricing(req, res) {
    try {
      // Typical Contabo VPS prices in USD
      const typicalVpsPrices = [4.99, 9.99, 19.99, 39.99, 79.99, 159.99];

      const rate = await this.currencyService.getUsdToMadRate();
      const madPrices = await this.currencyService.convertMultipleUsdToMad(typicalVpsPrices);

      const pricingSimulation = typicalVpsPrices.map((usd, index) => ({
        packageName: `Cloud VPS ${(index + 1) * 10}`,
        usdPrice: usd,
        madPrice: madPrices[index],
        conversion: `$${usd} USD → ${madPrices[index]} MAD`
      }));

      res.json({
        success: true,
        data: {
          exchangeRate: rate,
          timestamp: new Date().toISOString(),
          pricingSimulation: pricingSimulation,
          summary: {
            totalPackages: typicalVpsPrices.length,
            priceRange: {
              usd: { min: Math.min(...typicalVpsPrices), max: Math.max(...typicalVpsPrices) },
              mad: { min: Math.min(...madPrices), max: Math.max(...madPrices) }
            }
          }
        },
        message: 'VPS pricing simulation completed'
      });

    } catch (error) {
      console.error('[CURRENCY] Simulate VPS pricing error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to simulate VPS pricing',
        error: error.message
      });
    }
  }

  /**
   * Simulate VPS pricing with margin
   * POST /admin/currency/simulate-pricing-with-margin
   * Body: { margin: number }
   */
  async simulatePricingWithMargin(req, res) {
    try {
      const { margin = 0 } = req.body;

      if (margin < 0 || margin > 100) {
        return res.status(400).json({
          success: false,
          message: 'Margin must be between 0 and 100 percent'
        });
      }

      // Typical Contabo VPS prices in USD
      const typicalVpsPrices = [4.99, 9.99, 19.99, 39.99, 79.99, 159.99];

      const rate = await this.currencyService.getUsdToMadRate();
      const madPrices = await this.currencyService.convertMultipleUsdToMad(typicalVpsPrices);

      const pricingSimulation = typicalVpsPrices.map((usd, index) => {
        const baseMadPrice = madPrices[index];
        const finalMadPrice = this.pricingService.applyPricingMargin(baseMadPrice, margin);
        const marginAmount = finalMadPrice - baseMadPrice;

        return {
          packageName: `Cloud VPS ${(index + 1) * 10}`,
          usdPrice: usd,
          baseMadPrice: baseMadPrice,
          finalMadPrice: finalMadPrice,
          marginAmount: marginAmount,
          marginPercentage: margin,
          conversion: `$${usd} USD → ${baseMadPrice} MAD → ${finalMadPrice} MAD (${margin}% margin)`
        };
      });

      res.json({
        success: true,
        data: {
          exchangeRate: rate,
          margin: margin,
          timestamp: new Date().toISOString(),
          pricingSimulation: pricingSimulation,
          summary: {
            totalPackages: typicalVpsPrices.length,
            totalMarginAmount: pricingSimulation.reduce((sum, pkg) => sum + pkg.marginAmount, 0),
            priceRange: {
              usd: { min: Math.min(...typicalVpsPrices), max: Math.max(...typicalVpsPrices) },
              madBase: { min: Math.min(...madPrices), max: Math.max(...madPrices) },
              madFinal: {
                min: Math.min(...pricingSimulation.map(p => p.finalMadPrice)),
                max: Math.max(...pricingSimulation.map(p => p.finalMadPrice))
              }
            }
          }
        },
        message: `VPS pricing simulation with ${margin}% margin completed`
      });

    } catch (error) {
      console.error('[CURRENCY] Simulate pricing with margin error:', error.message);
      res.status(500).json({
        success: false,
        message: 'Failed to simulate pricing with margin',
        error: error.message
      });
    }
  }
}

// Create controller instance
const currencyController = new CurrencyController();

// Export controller methods
module.exports = {
  testConnection: (req, res) => currencyController.testConnection(req, res),
  getExchangeRate: (req, res) => currencyController.getExchangeRate(req, res),
  convertUsdToMad: (req, res) => currencyController.convertUsdToMad(req, res),
  convertBatchUsdToMad: (req, res) => currencyController.convertBatchUsdToMad(req, res),
  clearCache: (req, res) => currencyController.clearCache(req, res),
  getCacheStatus: (req, res) => currencyController.getCacheStatus(req, res),
  simulateVpsPricing: (req, res) => currencyController.simulateVpsPricing(req, res),
  simulatePricingWithMargin: (req, res) => currencyController.simulatePricingWithMargin(req, res)
};
