"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogBody,
  Dialog<PERSON>ooter,
  Button,
  Typography,
  Select,
  Option,
  Textarea,
  Input,
  Checkbox,
} from "@material-tailwind/react";
import { X, AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

export default function CancellationModal({
  isOpen,
  onClose,
  onConfirm,
  loading = false,
  instanceName = "",
  serviceName = "",
}) {
  const t = useTranslations("client");

  const [formData, setFormData] = useState({
    reason: "",
    feedback: "",
    targetDate: "",
    confirmed: false,
  });

  const [errors, setErrors] = useState({});

  // Cancellation reasons with contextual feedback prompts
  const cancellationReasons = [
    {
      value: "missing_features",
      label: "Missing features",
      feedbackPrompt:
        "Which features would you like to see us offer in the future? Your feedback helps us improve.",
      placeholder: "Please describe the missing features you need...",
    },
    {
      value: "too_expensive",
      label: "Too expensive",
      feedbackPrompt:
        "What pricing would be more suitable for your needs? Help us understand your budget requirements.",
      placeholder: "Please share your pricing expectations...",
    },
    {
      value: "poor_performance",
      label: "Poor performance",
      feedbackPrompt:
        "What performance issues did you experience? Your feedback helps us improve our service quality.",
      placeholder: "Please describe the performance issues you encountered...",
    },
    {
      value: "switching_provider",
      label: "Switching to another provider",
      feedbackPrompt:
        "What made you choose another provider? Understanding this helps us improve our competitive position.",
      placeholder:
        "Please tell us what the other provider offers that we don't...",
    },
    {
      value: "no_longer_needed",
      label: "No longer needed",
      feedbackPrompt:
        "Has your project or business needs changed? We'd appreciate understanding your situation.",
      placeholder: "Please share what changed in your requirements...",
    },
    {
      value: "technical_issues",
      label: "Technical issues",
      feedbackPrompt:
        "What technical problems did you encounter? Your feedback helps us resolve issues for other customers.",
      placeholder: "Please describe the technical issues you faced...",
    },
    {
      value: "other",
      label: "Other",
      feedbackPrompt:
        "Please share any other feedback that would help us improve our services.",
      placeholder: "Please share your feedback...",
    },
  ];

  // Get the current reason's feedback configuration
  const getCurrentReasonConfig = () => {
    return (
      cancellationReasons.find((reason) => reason.value === formData.reason) ||
      cancellationReasons[cancellationReasons.length - 1]
    );
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.reason) {
      newErrors.reason = "Please select a reason for cancellation";
    }

    if (!formData.confirmed) {
      newErrors.confirmed = "You must confirm the cancellation terms";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    onConfirm({
      reason: formData.reason,
      feedback: formData.feedback,
      targetDate: formData.targetDate || null,
    });
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        reason: "",
        feedback: "",
        targetDate: "",
        confirmed: false,
      });
      setErrors({});
      onClose();
    }
  };

  return (
    <Dialog
      open={isOpen}
      handler={handleClose}
      size="md"
      className="max-h-[90vh] overflow-y-auto"
    >
      <DialogHeader className="flex items-center justify-between border-b border-gray-200 pb-4">
        <div className="flex items-center gap-3">
          <AlertCircle className="w-6 h-6 text-red-500" />
          <Typography variant="h5" className="text-gray-900">
            Cancellation of service
          </Typography>
        </div>
        <Button
          variant="text"
          color="gray"
          onClick={handleClose}
          className="p-2"
          disabled={loading}
        >
          <X className="h-5 w-5" />
        </Button>
      </DialogHeader>

      <DialogBody className="space-y-6 py-6">
        {/* Service Information */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <Typography className="text-sm text-gray-700">
            You are about to cancel the following service:{" "}
            <strong>
              {serviceName || instanceName || "Custom Images Storage"}
            </strong>
          </Typography>
        </div>

        {/* Warning Message */}
        <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <Typography className="font-semibold text-red-800 mb-1">
                Attention:
              </Typography>
              <Typography className="text-sm text-red-700">
                The product you wish to cancel is no longer available. You will
                not be able to reorder this product in the future.
              </Typography>
            </div>
          </div>
        </div>

        <Typography className="text-sm text-gray-600">
          Please let us know why you want to cancel this service below.
        </Typography>

        {/* Reason Selection */}
        <div className="space-y-2">
          <Typography className="text-sm font-medium text-gray-700">
            Reason <span className="text-red-500">*</span>
          </Typography>
          <Select
            value={formData.reason}
            onChange={(value) => handleInputChange("reason", value)}
            error={!!errors.reason}
            disabled={loading}
          >
            {cancellationReasons.map((reason) => (
              <Option key={reason.value} value={reason.value}>
                {reason.label}
              </Option>
            ))}
          </Select>
          {errors.reason && (
            <Typography className="text-xs text-red-500">
              {errors.reason}
            </Typography>
          )}
        </div>

        {/* Feedback Textarea */}
        <div className="space-y-2">
          <Typography className="text-sm font-medium text-gray-700">
            {getCurrentReasonConfig().feedbackPrompt}
          </Typography>
          <Textarea
            value={formData.feedback}
            onChange={(e) => handleInputChange("feedback", e.target.value)}
            placeholder={getCurrentReasonConfig().placeholder}
            rows={4}
            disabled={loading}
            className="resize-none"
          />
        </div>

        {/* Target Date */}
        <div className="space-y-2">
          <Typography className="text-sm font-medium text-gray-700">
            Target Date
          </Typography>
          <Input
            type="date"
            value={formData.targetDate}
            onChange={(e) => handleInputChange("targetDate", e.target.value)}
            disabled={loading}
          />
        </div>

        {/* Confirmation Checkbox */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <Checkbox
              checked={formData.confirmed}
              onChange={(e) => handleInputChange("confirmed", e.target.checked)}
              disabled={loading}
              className="mt-1"
            />
            <Typography className="text-sm text-gray-700 leading-relaxed">
              I am aware that, by cancelling this service, I will lose all
              rights associated with it as well as all data stored on/with the
              service. This is not recoverable. I confirm that I will create
              backups of all data if required, at least two days in advance of
              the cancellation date. I am also aware that, on the day of the
              cancellation, the loss of data/access/rights can take place at any
              time.
            </Typography>
          </div>
          {errors.confirmed && (
            <Typography className="text-xs text-red-500 ml-8">
              {errors.confirmed}
            </Typography>
          )}
        </div>
      </DialogBody>

      <DialogFooter className="border-t border-gray-200 pt-4">
        <Button
          variant="text"
          color="gray"
          onClick={handleClose}
          className="mr-3"
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          variant="gradient"
          color="blue"
          onClick={handleSubmit}
          disabled={loading || !formData.confirmed}
          loading={loading}
        >
          Submit cancellation
        </Button>
      </DialogFooter>
    </Dialog>
  );
}
