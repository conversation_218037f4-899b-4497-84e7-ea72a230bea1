/**
 * Service for managing VPS storage types and integration with Contabo product mapping
 */

const { 
  getStorageTypesForPlan, 
  getDefaultStorageType,
  getProductIdForStorage,
  hasMultipleStorageOptions 
} = require('../constants/vps-storage-mapping');

class VPSStorageService {
  
  /**
   * Populate storage types for a VPS package based on its name
   * @param {Object} packageData - Package data object
   * @returns {Object} Package data with populated storageTypes
   */
  populateStorageTypes(packageData) {
    if (!packageData.vpsConfig || !packageData.name) {
      return packageData;
    }

    const planName = packageData.name;
    const storageTypes = getStorageTypesForPlan(planName);
    
    if (storageTypes.length > 0) {
      // Set storage types
      packageData.vpsConfig.storageTypes = storageTypes;
      
      // Set the default product ID if not already set
      if (!packageData.vpsConfig.providerProductId) {
        const defaultStorage = getDefaultStorageType(planName);
        if (defaultStorage) {
          packageData.vpsConfig.providerProductId = defaultStorage.productId;
          packageData.vpsConfig.providerPlanName = `${planName} ${defaultStorage.type}`;
        }
      }
    }

    return packageData;
  }

  /**
   * Update existing package with storage types
   * @param {Object} packageDoc - Mongoose package document
   * @returns {Promise<Object>} Updated package document
   */
  async updatePackageStorageTypes(packageDoc) {
    if (!packageDoc.vpsConfig || !packageDoc.name) {
      return packageDoc;
    }

    const planName = packageDoc.name;
    const storageTypes = getStorageTypesForPlan(planName);
    
    if (storageTypes.length > 0) {
      packageDoc.vpsConfig.storageTypes = storageTypes;
      
      // Set default product ID if not set
      if (!packageDoc.vpsConfig.providerProductId) {
        const defaultStorage = getDefaultStorageType(planName);
        if (defaultStorage) {
          packageDoc.vpsConfig.providerProductId = defaultStorage.productId;
          packageDoc.vpsConfig.providerPlanName = `${planName} ${defaultStorage.type}`;
        }
      }
      
      await packageDoc.save();
    }

    return packageDoc;
  }

  /**
   * Get product ID for a specific storage type selection
   * @param {string} planName - VPS plan name
   * @param {string} storageType - Selected storage type (NVMe, SSD, Storage)
   * @returns {string|null} Contabo product ID
   */
  getProductIdForSelection(planName, storageType) {
    return getProductIdForStorage(planName, storageType);
  }

  /**
   * Check if a plan supports multiple storage options
   * @param {string} planName - VPS plan name
   * @returns {boolean} True if multiple storage options available
   */
  hasMultipleOptions(planName) {
    return hasMultipleStorageOptions(planName);
  }

  /**
   * Get storage types for a plan
   * @param {string} planName - VPS plan name
   * @returns {Array} Array of storage type objects
   */
  getStorageOptions(planName) {
    return getStorageTypesForPlan(planName);
  }

  /**
   * Validate storage description selection
   * @param {string} planName - VPS plan name
   * @param {string} storageDescription - Selected storage description
   * @returns {boolean} True if valid selection
   */
  isValidStorageType(planName, storageDescription) {
    const storageTypes = getStorageTypesForPlan(planName);
    return storageTypes.some(storage => storage.storageDescription === storageDescription);
  }

  /**
   * Get storage type details by description
   * @param {string} planName - VPS plan name
   * @param {string} storageDescription - Storage description (e.g., "75 GB NVMe")
   * @returns {Object|null} Storage type details
   */
  getStorageTypeDetails(planName, storageDescription) {
    const storageTypes = getStorageTypesForPlan(planName);
    return storageTypes.find(storage => storage.storageDescription === storageDescription) || null;
  }

  /**
   * Calculate total price including storage type additional cost
   * @param {number} basePrice - Base package price
   * @param {string} planName - VPS plan name
   * @param {string} storageDescription - Selected storage description
   * @returns {number} Total price including storage cost
   */
  calculateTotalPrice(basePrice, planName, storageDescription) {
    const storageDetails = this.getStorageTypeDetails(planName, storageDescription);
    const additionalPrice = storageDetails ? storageDetails.additionalPrice : 0;
    return basePrice + additionalPrice;
  }

  /**
   * Generate provider product ID from package name (for scraped packages)
   * This maps scraped package names to their default Contabo product IDs
   * @param {string} packageName - Package name from scraping
   * @returns {string|null} Contabo product ID
   */
  generateProviderProductId(packageName) {
    const defaultStorage = getDefaultStorageType(packageName);
    return defaultStorage ? defaultStorage.productId : null;
  }

  /**
   * Update package description to include storage type information
   * @param {string} baseDescription - Base package description
   * @param {string} planName - VPS plan name
   * @param {string} storageType - Selected storage type
   * @returns {string} Updated description
   */
  updateDescriptionWithStorage(baseDescription, planName, storageType) {
    const storageDetails = this.getStorageTypeDetails(planName, storageType);
    if (!storageDetails) {
      return baseDescription;
    }

    // Replace storage information in description
    const storageInfo = storageDetails.diskSize;
    
    // Try to replace existing storage info or append it
    if (baseDescription.includes('GB')) {
      // Replace existing storage info
      return baseDescription.replace(/\d+\s*GB\s*\w*\s*(Storage|SSD|NVMe)?/i, storageInfo);
    } else {
      // Append storage info
      return `${baseDescription}, ${storageInfo}`;
    }
  }
}

module.exports = new VPSStorageService();
