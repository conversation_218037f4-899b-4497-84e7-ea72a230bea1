import React, { useEffect, useState } from 'react';
import { Input, Button, Typography } from '@material-tailwind/react';
import profileService from '../../app/services/profileService';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { useAuth } from '../../app/context/AuthContext';

const EditPasswordForm = ({ userId, t }) => {
    const [formData, setFormData] = useState({ 
        id: '', 
        password: '', 
        newPassword: '', 
        repeatNewPassword: "", 
        isOAuth: false,
        hasPassword: false
    });
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const { user, updateUser } = useAuth(); // Make sure updateUser is destructured here

    useEffect(() => {
        // Get fresh user data to ensure we have current hasPassword status
        const getUserProfile = async () => {
            try {
                const profile = await profileService.getProfile();
                const userData = profile.data;
                
                // Only treat as OAuth without password if both conditions are true
                const isOAuthWithoutPassword = userData.isOAuth && !userData.hasPassword;
                console.log("isOAuth:", userData?.isOAuth);
                console.log("hasPassword:", userData?.hasPassword);
                
                setFormData(prev => ({
                    ...prev,
                    id: userId,
                    isOAuth: isOAuthWithoutPassword,
                    hasPassword: userData?.hasPassword || false
                }));
            } catch (error) {
                console.error("Error fetching user profile:", error);
            }
        };
        
        getUserProfile();
    }, [userId]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData((prevData) => ({ ...prevData, [name]: value }));
        // Clear error for the field being changed
        setErrors(prevErrors => ({ ...prevErrors, [name]: undefined }));
    };

    const validateForm = () => {
        let isValid = true;
        const newErrors = {};

        if (!formData.isOAuth && !formData.password) {
            newErrors.password = t('current_password_required');
            isValid = false;
        }
        if (!formData.newPassword) {
            newErrors.newPassword = t('new_password_required');
            isValid = false;
        }
        if (!formData.repeatNewPassword) {
            newErrors.repeatNewPassword = t('repeat_new_password_required');
            isValid = false;
        }
        if (formData.newPassword && formData.repeatNewPassword && formData.newPassword !== formData.repeatNewPassword) {
            newErrors.repeatNewPassword = t('passwords_must_match');
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setLoading(true);
       
        try {
            const changePassRes = await profileService.changePassword(formData);
            console.log('changePassRes:', changePassRes);

            // Update the user data after successful password change
            if (changePassRes.data && changePassRes.data.user) {
                if (typeof updateUser === 'function') {
                    updateUser({ ...user, hasPassword: true });
                } else {
                    console.warn("updateUser function is not defined in AuthContext");
                    // Fallback: Just refresh the page after a delay to get the updated state
                }
            }

            toast.success(t('password_updated_success', 'Password updated successfully.'));
            setTimeout(() => {
                router.push('/client/profile');
            }, 3000);
        } catch (error) {
             if (error.response?.data?.errors) {
                const serverErrors = error.response.data.errors.reduce((acc, curr) => {
                    acc[curr.key] = curr.msg;
                    return acc;
                }, {});
                setErrors(serverErrors);
            } else if (error.response?.data?.message) {
                // Handle non-validation error messages
                toast.error(error.response.data.message);
            } else {
                console.log("unhandled error:", error);
                toast.error(t('generic_error', 'An error occurred. Please try again.'));
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="">
            <Typography variant='h2' className="text-3xl font-semibold text-gray-800 mb-6">
                {t('change_password')}
            </Typography>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Show current password field only if not OAuth without password */}
                {!formData.isOAuth && (
                    <div className="">
                        <Input
                            type="password"
                            name="password"
                            label={t('current_password')}
                            value={formData.password}
                            onChange={handleInputChange}
                        />
                        {errors.password && <p className="text-red-500 text-sm">{errors.password}</p>}
                    </div>
                )}

                {/* Always show new password fields */}
                <div className="">
                    <Input
                        type="password"
                        name="newPassword"
                        label={t('new_password')}
                        value={formData.newPassword}
                        onChange={handleInputChange}
                    />
                    {errors.newPassword && <p className="text-red-500 text-sm">{errors.newPassword}</p>}
                </div>
                <div className="">
                    <Input
                        type="password"
                        name="repeatNewPassword"
                        label={t('repeat_new_password')}
                        value={formData.repeatNewPassword}
                        onChange={handleInputChange}
                    />
                    {errors.repeatNewPassword && <p className="text-red-500 text-sm">{errors.repeatNewPassword}</p>}
                </div>

                {/* Add informational message for OAuth users without password */}
                {formData.isOAuth && (
                    <div className="bg-blue-50 p-4 rounded-md mb-4">
                        <p className="text-sm text-blue-700">
                            {t('oauth_password_notice', {
                                defaultValue: 'Setting a password will allow you to login with your email and password in addition to your social login.'
                            })}
                        </p>
                    </div>
                )}

                <div className="flex flex-row gap-x-10">
                    <Button
                        type="submit" disabled={loading}
                        className='bg-secondary font-normal font-poppins rounded-md'>
                        {formData.isOAuth && formData.hasPassword ? (loading ? t('updating') : t('update_password')) : (loading ? t('saving') : t('set_password'))}
                    </Button>
                    <Button
                        onClick={() => router.push('/client/profile')}
                        className='bg-gray-50 text-gray-800 hover:shadow-sm border-gray-200 px-10 border shadow-none font-medium font-poppins rounded-md'>
                        {t('back')}
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default EditPasswordForm;
