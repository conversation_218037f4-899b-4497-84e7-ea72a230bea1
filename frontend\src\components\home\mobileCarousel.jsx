import React from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { Typography } from "@material-tailwind/react";
import Link from "next/link";
import { BsArrowUpRight } from "react-icons/bs";

const MobileCarousel = ({ SecondarySericesData, t }) => {
    const CustomPrevArrow = ({ onClick }) => {
        return (
            <div
                onClick={onClick}
                className="absolute -bottom-10 left-[30%] md:left-[42%] z-10 cursor-pointer bg-white border border-gray-300 rounded-full p-1 transition-transform hover:scale-110"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 23 23"
                    fill="black"
                    width="24"
                    height="24"
                >
                    <path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6z" />
                </svg>
            </div>
        );
    };

    const CustomNextArrow = ({ onClick }) => {
        return (
            <div
                onClick={onClick}
                className="absolute -bottom-10 left-[60%] md:left-[50%] z-10 cursor-pointer bg-white border border-gray-300 rounded-full p-1 transition-transform hover:scale-110"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 23 23"
                    fill="black"
                    width="24"
                    height="24"
                >
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6z" />
                </svg>
            </div>
        );
    };

    // Slick Slider settings
    const settings = {
        dots: false,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        nextArrow: <CustomNextArrow />,
        prevArrow: <CustomPrevArrow />,
        appendDots: (dots) => (
            <div style={{ bottom: "-30px" }}>
                <ul className="flex justify-center gap-2">{dots}</ul>
            </div>
        ),
        customPaging: (i) => (
            <span
                className="block h-2 w-2 rounded-full bg-gray-300 cursor-pointer transition-all"
            />
        ),
    };

    const SecondarySericesCard = ({ iconSrc, title, description, url }) => (
        <div
            className={`bg-white text-start flex flex-col border-[0.97px] shadow-md rounded-lg p-4 border-secondary h-full`}
        >
            <div className="bg-white flex justify-start mb-4 w-fit p-2 rounded-lg">
                {iconSrc}
            </div>
            <div className="flex-grow h-full">
                <Typography className="font-semibold mb-2 text-lg font-inter">
                    {t(title)}
                </Typography>
                <p className="text-[#939393] font-inter">{t(description)}</p>
            </div>

            <Link href={url} className="text-blue-600 font-medium mt-4 flex items-center text-xs">
                {t('voir_plus') + t(title)}
                <BsArrowUpRight className="text-sm ml-1" />
            </Link>
        </div>
    );

    return (
        <div className="relative w-full max-w-[300px] mx-auto md:hidden">
            <Slider {...settings}>
                {SecondarySericesData.map((card, index) => (
                    <div key={index} className="h-[360px]">
                        <SecondarySericesCard
                            iconSrc={card.iconSrc}
                            title={card.title}
                            description={card.description}
                            url={card.url}
                        />
                    </div>
                ))}
            </Slider>
        </div>
    );
};

export default MobileCarousel;
