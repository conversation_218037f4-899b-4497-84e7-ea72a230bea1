const mongoose = require('mongoose');
const { isProd } = require('../constants/constant');

require('dotenv').config();

const connectDatabase = async () => {
    try {
        let dbURI;
        let connectionMessage;

        if (isProd) {
            // Production: Use local MongoDB
            const dbName = "ztech";
            dbURI = `mongodb://0.0.0.0:27017/${dbName}`;
            connectionMessage = `Connected to local MongoDB (${dbName} database)`;
        } else {
            // Development: Use MongoDB Atlas
            dbURI = process.env.MONGODB_URI;
            connectionMessage = 'Connected to MongoDB Atlas';
        }

        if (!dbURI) {
            throw new Error('Database URI is not defined. Check your environment variables.');
        }

        const res = await mongoose.connect(dbURI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });

        console.log(`✅ ${connectionMessage}`);
        console.log(`📍 Environment: ${isProd ? 'Production' : 'Development'}`);

        // startCronJob(); this cron job will be removed soon
        // console.log('Starting cron job successfully');

    } catch (error) {
        console.error('❌ Error connecting to the database:', error.message);
        console.error('💡 Make sure:');
        if (isProd) {
            console.error('   - MongoDB is running locally on port 27017');
            console.error('   - Local MongoDB service is started');
        } else {
            console.error('   - MONGODB_URI is set in your .env file');
            console.error('   - Your IP is whitelisted in MongoDB Atlas');
            console.error('   - Database credentials are correct');
        }
        process.exit(1);
    }
};
connectDatabase();
module.exports = mongoose; // ✅ Export mongoose instance

// const mongoose = require("mongoose");
// require("dotenv").config();

// const connectDB = async () => {
//     const maxRetries = 3;
//     let currentRetry = 0;

//     while (currentRetry < maxRetries) {
//         try {
//             await mongoose.connect(process.env.MONGODB_URI);
//             console.log("✅ MongoDB Atlas connected successfully!");
//             return;
//         } catch (error) {
//             currentRetry++;
//             console.error(`❌ MongoDB Connection Failed (Attempt ${currentRetry}/${maxRetries})`);

//             if (error.name === "MongooseServerSelectionError") {
//                 console.error("⚠️ MongoDB Atlas Unreachable. Check:");
//                 console.error("1. IP whitelist in MongoDB Atlas");
//                 console.error("2. Your connection string in `.env`");
//                 console.error("3. Database user credentials");
//             } else {
//                 console.error("⚠️ Connection Error:", error.message);
//             }

//             if (currentRetry === maxRetries) {
//                 console.error("❌ Maximum connection attempts reached. Exiting...");
//                 process.exit(1);
//             }

//             // Wait 2 seconds before retrying
//             await new Promise(resolve => setTimeout(resolve, 2000));
//         }
//     }
// };
// connectDB();
// module.exports = mongoose;
