/**
 * <PERSON><PERSON><PERSON> to update existing packages from old storage format to new simplified format
 * Old: { type: "NVMe", productId: "V91", diskSize: "75 GB NVMe", ... }
 * New: { productId: "V91", storageDescription: "75 GB NVMe", ... }
 */

const mongoose = require('mongoose');
const Package = require('../models/Package');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ztech_dev', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

async function updateStorageTypesFormat() {
  try {
    console.log('🔄 Updating storage types format...');

    // Find all VPS packages with storage types
    const vpsPackages = await Package.find({
      'vpsConfig.storageTypes': { $exists: true, $ne: [] }
    });

    console.log(`📦 Found ${vpsPackages.length} VPS packages with storage types`);

    let updatedCount = 0;

    for (const pkg of vpsPackages) {
      console.log(`\n🔄 Processing package: ${pkg.name}`);
      
      let needsUpdate = false;
      const updatedStorageTypes = pkg.vpsConfig.storageTypes.map(storage => {
        // Check if this storage type uses the old format (has 'type' field)
        if (storage.type && !storage.storageDescription) {
          const storageDesc = storage.diskSize || `${storage.type} Storage`;
          console.log(`  Converting: ${storage.type} -> ${storageDesc}`);
          needsUpdate = true;
          return {
            productId: storage.productId,
            storageDescription: storageDesc,
            isDefault: storage.isDefault || false,
            additionalPrice: storage.additionalPrice || 0
          };
        } else if (storage.storageDescription) {
          // Already in new format
          console.log(`  Already updated: ${storage.storageDescription}`);
          return storage;
        } else {
          // Invalid format, skip
          console.log(`  ⚠️ Invalid storage type format, skipping`);
          return storage;
        }
      });

      if (needsUpdate) {
        // Update the package
        pkg.vpsConfig.storageTypes = updatedStorageTypes;
        await pkg.save();
        updatedCount++;
        console.log(`  ✅ Updated package: ${pkg.name}`);
        
        // Show updated storage types
        updatedStorageTypes.forEach(storage => {
          console.log(`    - ${storage.storageDescription} (${storage.productId})${storage.isDefault ? ' [DEFAULT]' : ''}`);
        });
      } else {
        console.log(`  ⏭️ Package already in new format`);
      }
    }

    console.log('\n📊 Update Summary:');
    console.log(`✅ Updated packages: ${updatedCount}`);
    console.log(`⏭️ Already updated: ${vpsPackages.length - updatedCount}`);
    console.log(`📦 Total packages: ${vpsPackages.length}`);

    // Verify the updates
    console.log('\n🔍 Verification:');
    const verifyPackages = await Package.find({
      'vpsConfig.storageTypes': { $exists: true, $ne: [] }
    });
    
    console.log(`✅ Packages with storage types: ${verifyPackages.length}`);
    
    // Show sample of updated packages
    if (verifyPackages.length > 0) {
      console.log('\n📋 Sample updated packages:');
      for (const pkg of verifyPackages.slice(0, 2)) {
        console.log(`\n📦 ${pkg.name}:`);
        pkg.vpsConfig.storageTypes.forEach(storage => {
          console.log(`   - ${storage.storageDescription} (${storage.productId})${storage.isDefault ? ' [DEFAULT]' : ''}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Error in updateStorageTypesFormat:', error);
  }
}

async function main() {
  try {
    await connectDB();
    await updateStorageTypesFormat();
    console.log('\n🎉 Storage types format update completed!');
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📴 Database connection closed');
    process.exit(0);
  }
}

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️ Script interrupted by user');
  await mongoose.disconnect();
  process.exit(0);
});

process.on('unhandledRejection', async (error) => {
  console.error('❌ Unhandled rejection:', error);
  await mongoose.disconnect();
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { updateStorageTypesFormat };
