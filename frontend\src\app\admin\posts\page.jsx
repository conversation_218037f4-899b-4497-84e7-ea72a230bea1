"use client"

import React, { useState, useEffect } from 'react';
import { FileText, Trash2, Edit, Plus, MessageSquare, Check, X, Image as ImageIcon, AlertCircle } from 'lucide-react';

// Dummy data for blog posts
const dummyPosts = [
  {
    id: '1',
    title: 'First Blog Post',
    content: 'This is the content of the first blog post.',
    excerpt: 'This is the excerpt of the first blog post.',
    status: 'published',
    featured_image: '',
    created_at: '2023-01-01T00:00:00Z',
    comments: [
      {
        id: '1',
        content: 'Great post!',
        status: 'approved',
        user: {
          email: '<EMAIL>',
          full_name: 'User One'
        },
        created_at: '2023-01-02T00:00:00Z'
      }
    ]
  },
  {
    id: '2',
    title: 'Second Blog Post',
    content: 'This is the content of the second blog post.',
    excerpt: 'This is the excerpt of the second blog post.',
    status: 'draft',
    featured_image: '',
    created_at: '2023-02-01T00:00:00Z',
    comments: []
  }
];

const BlogManagement = () => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [selectedPost, setSelectedPost] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    featured_image: '',
    status: 'draft'
  });
  const [editingId, setEditingId] = useState(null);
  const [imageUploading, setImageUploading] = useState(false);

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async () => {
    try {
      // Simulate fetching data
      setPosts(dummyPosts);
    } catch (err) {
      setError('Failed to fetch posts');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setImageUploading(true);
      setError(null);

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Please upload a valid image file (JPG, PNG, GIF, or WebP)');
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('Image size should be less than 5MB');
      }

      // Simulate image upload
      const fileExt = file.name.split('.').pop()?.toLowerCase();
      const fileName = `${Math.random()}${Date.now()}.${fileExt}`;
      const filePath = `posts/${fileName}`;

      // Simulate getting public URL
      const publicUrl = `https://dummyimage.com/${filePath}`;

      setFormData({ ...formData, featured_image: publicUrl });
    } catch (err) {
      setError(err.message);
    } finally {
      setImageUploading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    try {
      const { title, content, excerpt, featured_image, status } = formData;

      if (!title.trim() || !content.trim()) {
        throw new Error('Title and content are required');
      }

      const postData = {
        title: title.trim(),
        content: content.trim(),
        excerpt: excerpt.trim() || content.trim().substring(0, 200) + '...',
        featured_image: featured_image || null,
        status
      };

      if (editingId) {
        // Simulate updating a post
        setPosts(posts.map(post => post.id === editingId ? { ...post, ...postData } : post));
      } else {
        // Simulate adding a new post
        const newPost = {
          ...postData,
          id: (posts.length + 1).toString(),
          created_at: new Date().toISOString(),
          comments: []
        };
        setPosts([...posts, newPost]);
      }

      setFormData({
        title: '',
        content: '',
        excerpt: '',
        featured_image: '',
        status: 'draft'
      });
      setShowForm(false);
      setEditingId(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleEdit = (post) => {
    setFormData({
      title: post.title,
      content: post.content,
      excerpt: post.excerpt || '',
      featured_image: post.featured_image || '',
      status: post.status
    });
    setEditingId(post.id);
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this post?')) return;

    try {
      // Simulate deleting a post
      setPosts(posts.filter(post => post.id !== id));
    } catch (err) {
      setError('Failed to delete post');
    }
  };

  const handleCommentStatus = async (commentId, status) => {
    try {
      // Simulate updating comment status
      setPosts(posts.map(post => ({
        ...post,
        comments: post.comments.map(comment => comment.id === commentId ? { ...comment, status } : comment)
      })));
    } catch (err) {
      setError('Failed to update comment status');
    }
  };

  const truncateText = (text, maxLength = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Blog Posts</h2>
        <button
          onClick={() => {
            setShowForm(true);
            setEditingId(null);
            setFormData({
              title: '',
              content: '',
              excerpt: '',
              featured_image: '',
              status: 'draft'
            });
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          New Post
        </button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {showForm && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-4">
            {editingId ? 'Edit Post' : 'Add New Post'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title
              </label>
              <input
                type="text"
                required
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content
              </label>
              <textarea
                required
                rows={8}
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Excerpt
              </label>
              <textarea
                rows={3}
                value={formData.excerpt}
                onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Optional brief summary of the post"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Featured Image
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                />
                <label
                  htmlFor="image-upload"
                  className="flex items-center px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
                >
                  <ImageIcon className="h-5 w-5 mr-2 text-gray-500" />
                  <span>{imageUploading ? 'Uploading...' : 'Upload Image'}</span>
                </label>
                {formData.featured_image && (
                  <img
                    src={formData.featured_image}
                    alt="Preview"
                    className="h-20 w-20 object-cover rounded-lg"
                  />
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => {
                  setShowForm(false);
                  setEditingId(null);
                }}
                className="text-gray-600 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700"
              >
                {editingId ? 'Update Post' : 'Create Post'}
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                Post
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Status
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Comments
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                Created
              </th>
              <th className="px-6 py-3 bg-gray-50 w-1/12"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {posts.map((post) => (
              <React.Fragment key={post.id}>
                <tr>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      {post.featured_image ? (
                        <img
                          src={post.featured_image}
                          alt={post.title}
                          className="h-10 w-10 rounded object-cover mr-3"
                        />
                      ) : (
                        <FileText className="h-10 w-10 text-gray-400 mr-3" />
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {truncateText(post.title, 50)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {truncateText(post.excerpt || post.content, 60)}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${post.status === 'published'
                        ? 'bg-green-100 text-green-800'
                        : post.status === 'draft'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                      {post.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => setSelectedPost(selectedPost === post.id ? null : post.id)}
                      className="flex items-center text-gray-600 hover:text-gray-900"
                    >
                      <MessageSquare className="h-5 w-5 mr-2" />
                      {post.comments.length}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(post.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleEdit(post)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(post.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
                {selectedPost === post.id && post.comments.length > 0 && (
                  <tr>
                    <td colSpan={5} className="px-6 py-4">
                      <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                        {post.comments.map((comment) => (
                          <div key={comment.id} className="flex items-start justify-between bg-white p-4 rounded-lg">
                            <div className="flex-1">
                              <div className="flex justify-between items-start mb-2">
                                <div className="font-medium">{comment.user.full_name || comment.user.email}</div>
                                <div className="text-sm text-gray-500">
                                  {new Date(comment.created_at).toLocaleDateString()}
                                </div>
                              </div>
                              <p className="text-gray-600">{truncateText(comment.content, 100)}</p>
                            </div>
                            <div className="ml-4 flex items-center space-x-2">
                              {comment.status === 'pending' && (
                                <>
                                  <button
                                    onClick={() => handleCommentStatus(comment.id, 'approved')}
                                    className="text-green-600 hover:text-green-700"
                                  >
                                    <Check className="h-5 w-5" />
                                  </button>
                                  <button
                                    onClick={() => handleCommentStatus(comment.id, 'rejected')}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <X className="h-5 w-5" />
                                  </button>
                                </>
                              )}
                              <span className={`px-2 py-1 text-xs rounded-full ${comment.status === 'approved'
                                  ? 'bg-green-100 text-green-800'
                                  : comment.status === 'rejected'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                {comment.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BlogManagement;