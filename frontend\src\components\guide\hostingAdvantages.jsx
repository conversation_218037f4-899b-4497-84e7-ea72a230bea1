import { Typography } from '@material-tailwind/react'
import React from 'react'
import { HEADsvg, MALEPERSONsvg } from '../../icons/svgIcons'
import Image from 'next/image'

const benefits = [
    {
        icon: <HEADsvg color="#497ef7" />,
        title: "benefits.0.title",
        description: "benefits.0.description",
    },
    {
        icon: <MALEPERSONsvg />,
        title: "benefits.1.title",
        description: "benefits.1.description",
    },
    {
        icon: (
            <svg width="40" height="40" viewBox="0 0 52 43" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.03239 42.4318H45.9684C49.4884 38.0158 51.6004 32.4798 51.6004 26.3998C51.6004 12.2878 40.1444 0.799805 26.0004 0.799805C11.8564 0.799805 0.400391 12.2878 0.400391 26.3998C0.400391 32.4798 2.51239 38.0158 6.03239 42.4318ZM22.8004 7.1998C22.8004 5.4398 24.2404 3.9998 26.0004 3.9998C27.7604 3.9998 29.2004 5.4398 29.2004 7.1998C29.2004 8.99181 27.7604 10.3998 26.0004 10.3998C24.2404 10.3998 22.8004 8.99181 22.8004 7.1998ZM6.80039 13.5998C6.80039 11.8398 8.24039 10.3998 10.0004 10.3998C11.7604 10.3998 13.2004 11.8398 13.2004 13.5998C13.2004 15.3918 11.7604 16.7998 10.0004 16.7998C8.24039 16.7998 6.80039 15.3918 6.80039 13.5998ZM21.2644 24.4798C23.9524 21.8238 42.0964 13.2798 42.0964 13.2798C42.0964 13.2798 33.5844 31.4558 30.9284 34.1118C28.2404 36.7998 23.9524 36.7998 21.2644 34.1118C18.6084 31.4558 18.6084 27.1358 21.2644 24.4798ZM3.60039 29.5998C3.60039 27.8398 5.04039 26.3998 6.80039 26.3998C8.56039 26.3998 10.0004 27.8398 10.0004 29.5998C10.0004 31.3918 8.56039 32.7998 6.80039 32.7998C5.04039 32.7998 3.60039 31.3918 3.60039 29.5998ZM22.8004 29.5998C22.8004 27.8398 24.2404 26.3998 26.0004 26.3998C27.7604 26.3998 29.2004 27.8398 29.2004 29.5998C29.2004 31.3918 27.7604 32.7998 26.0004 32.7998C24.2404 32.7998 22.8004 31.3918 22.8004 29.5998ZM42.0004 29.5998C42.0004 27.8398 43.4404 26.3998 45.2004 26.3998C46.9604 26.3998 48.4004 27.8398 48.4004 29.5998C48.4004 31.3918 46.9604 32.7998 45.2004 32.7998C43.4404 32.7998 42.0004 31.3918 42.0004 29.5998Z" fill="#497EF7" />
            </svg>
        ),
        title: "benefits.2.title",
        description: "benefits.2.description",
    },
    {
        icon: (
            <svg width="38" height="38" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_1360_5935" maskUnits="userSpaceOnUse" x="0" y="0" width="48" height="48">
                    <path d="M48 0H0V48H48V0Z" fill="white" />
                </mask>
                <g mask="url(#mask0_1360_5935)">
                    <path d="M23 5.99951H8C6.89543 5.99951 6 6.89494 6 7.99951V39.9998C6 41.1044 6.89543 41.9998 8 41.9998H40C41.1046 41.9998 42 41.1044 42 39.9998V24.9998" stroke="#497EF7" strokeWidth="4.66667" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M24 15.9995V23.9995" stroke="#497EF7" strokeWidth="4.66667" strokeLinecap="round" />
                    <path d="M42 5.99951V13.9995" stroke="#497EF7" strokeWidth="4.66667" strokeLinecap="round" />
                    <path d="M32 23.9995H24" stroke="#497EF7" strokeWidth="4.66667" strokeLinecap="round" />
                    <path d="M42 5.99951L24 23.9996" stroke="#497EF7" strokeWidth="4.66667" />
                    <path d="M42 5.99951H34" stroke="#497EF7" strokeWidth="4.66667" strokeLinecap="round" />
                </g>
            </svg>
        ),
        title: "benefits.3.title",
        description: "benefits.3.description",
    }
]

function HostingAdvantages({ t }) {
    return (
        <div className='w-full relative mx-auto'
            style={{
                background: `linear-gradient(to right, #1a3fb4, #1c47a4)`,
            }}
        >
            <div className='max-w-[1400px] mx-auto items-center flex flex-col justify-center gap-y-2 p-0 overflow-hidden w-full'>
                <div className="w-full flex flex-col md:flex-row justify-between items-center">
                    <div className=" md:w-1/2 p-4">
                        <Typography
                            variant='h2'
                            className="text-3xl font-semibold mb-6 text-white md:max-w-lg">
                            {t('exclusive_hosting_benefits')}
                        </Typography>

                        <div className="mt-10 flex flex-col px-4 py-2 gap-y-6 max-w-lg">
                            {benefits.map((item, index) => (
                                <div
                                    key={index}
                                    className="flex flex-row justify-between gap-x-4">
                                    <div
                                        className="border border-[#F2F4FB] rounded-md bg-[#F2F4FB]  p-3 flex m-auto items-center text-4xl text-primary hover:border-secondary flex-shrink-0 w-fit">
                                        {item.icon}
                                    </div>
                                    <div className="flex flex-col gap-y-4">
                                        <Typography
                                            variant='h5'
                                            className="text-white font-inter text-lg font-medium">
                                            {t(item.title)}
                                        </Typography>
                                        <p className="text-white text-sm">
                                            {t(item.description)}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                    <div className="hidden md:w-1/2 md:flex items-end justify-end">
                        {/* <img
                            loading="lazy"
                            src="/images/morocco-blue-map.png"
                            alt="Data center"
                            className="h-full w-[80%] object-cover ml-auto"
                        /> */}
                        <Image
                            src="/images/morocco-blue-map.png"
                            alt="Data center"
                            width={500}
                            height={500}
                            sizes="(max-width: 768px) 100vw, 50vw"
                            className="h-full w-[80%] object-cover ml-auto"
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default HostingAdvantages