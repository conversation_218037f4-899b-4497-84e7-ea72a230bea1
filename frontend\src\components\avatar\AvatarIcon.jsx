"use client";
import React, { useState } from "react";
import {
  <PERSON>tar,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>po<PERSON>,
} from "@material-tailwind/react";
import {
  PowerIcon,
  UserCircleIcon,
  ShoppingBagIcon,
} from "@heroicons/react/24/solid";
import { useRouter } from "next/navigation";
import { useAuth } from "../../app/context/AuthContext";
import { setServerMedia } from "../../app/helpers/helpers";
import { useTranslations } from "next-intl";
import {
  Headset,
  LayoutDashboard,
  ServerIcon,
  ShoppingCartIcon,
  ChevronDownIcon,
  CodeXml,
  ShieldCheck,
} from "lucide-react";

// Profile menu items
const profileMenuItems = [
  {
    label: "my_profile",
    icon: UserCircleIcon,
    url: "/client/profile",
  },
  {
    label: "cart",
    icon: ShoppingCartIcon,
    url: "/client/cart",
  },
  {
    label: "subscriptions",
    icon: ShoppingBagIcon,
    hasSubmenu: true,
    submenuItems: [
      {
        label: "hosting_plans",
        icon: ServerIcon,
        url: "/client/hosting-plans",
      },
      {
        label: "ssl_certificates",
        icon: ShieldCheck,
        url: "/client/ssl-certificates",
      },
      {
        label: "web_dev.title",
        icon: CodeXml,
        url: "/client/web-development",
      },
    ],
  },
  {
    label: "support",
    icon: Headset,
    url: "/client/support/tickets",
  },
  {
    label: "admin",
    icon: LayoutDashboard,
    url: "/admin",
  },
  {
    label: "sign_out",
    icon: PowerIcon,
  },
];

export function AvatarIcon() {
  const t = useTranslations("client");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [openSubMenu, setOpenSubMenu] = useState(null);
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    setIsMenuOpen(false);
    window.location.href = "/auth/login";
  };

  const toggleSubMenu = (label) => {
    if (openSubMenu === label) {
      setOpenSubMenu(null);
    } else {
      setOpenSubMenu(label);
    }
  };

  const handleMenuItemClick = (item) => {
    if (item.hasSubmenu) {
      toggleSubMenu(item.label);
    } else if (item.label === "sign_out") {
      handleLogout();
    } else if (item.url) {
      router.push(item.url);
      setIsMenuOpen(false);
    }
  };

  return (
    <div className="flex justify-center h-full items-center gap-x-4 p-0 flex-shrink-0 max-w-[140px] min-w-[40px]">
      <Menu
        open={isMenuOpen}
        handler={setIsMenuOpen}
        placement="bottom-end"
        dismiss={{ itemPress: false }} // Prevent closing on item press
      >
        <MenuHandler>
          <Button
            variant="text"
            className="flex items-center rounded-full px-2 py-0 flex-shrink-0"
          >
            {user?.photo ? (
              <Avatar
                src={setServerMedia(user?.photo)}
                alt="User Avatar"
                className="cursor-pointer w-8 h-8"
              />
            ) : (
              <UserCircleIcon className="w-8 h-8" />
            )}
          </Button>
        </MenuHandler>
        <MenuList className="p-1 w-[240px]">
          {" "}
          {/* Fixed width for menu */}
          {profileMenuItems.map((item, key) => {
            const isLastItem = key === profileMenuItems.length - 1;
            if (item.label === "admin" && user?.role != "ADMIN") return null;
            return (
              <div key={item.label} className="w-full">
                <MenuItem
                  onClick={() => handleMenuItemClick(item)}
                  className={`flex items-center gap-2 rounded ${
                    isLastItem
                      ? "hover:bg-red-500/10 focus:bg-red-500/10"
                      : "hover:bg-gray-100"
                  } ${item.hasSubmenu ? "cursor-pointer" : ""}`}
                >
                  <div className={`flex items-center justify-between w-full`}>
                    <div className="flex items-center gap-2 min-w-0">
                      {React.createElement(item.icon, {
                        className: `h-4 w-4 flex-shrink-0 ${
                          isLastItem ? "text-red-500" : ""
                        }`,
                      })}
                      <Typography
                        variant="small"
                        className={`truncate ${
                          isLastItem ? "text-red-500" : ""
                        }`}
                      >
                        {t(item.label)}
                      </Typography>
                    </div>
                    {item.hasSubmenu && (
                      <ChevronDownIcon
                        className={`h-4 w-4 flex-shrink-0 transition-transform ${
                          openSubMenu === item.label ? "rotate-180" : ""
                        }`}
                      />
                    )}
                  </div>
                </MenuItem>

                {item.hasSubmenu && openSubMenu === item.label && (
                  <div className="pl-1 ml-2 border-l border-gray-200 w-full">
                    {item.submenuItems.map((subItem) => (
                      <MenuItem
                        key={subItem.label}
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          setTimeout(() => {
                            router.push(subItem.url);
                            setIsMenuOpen(false);
                          }, 10);
                        }}
                        className="flex items-center gap-2 rounded mt-1 hover:bg-gray-100 w-full"
                      >
                        <div className="flex items-center gap-2 min-w-0 w-full">
                          {React.createElement(subItem.icon, {
                            className: "h-4 w-4 flex-shrink-0",
                          })}
                          <Typography variant="small" className="truncate">
                            {t(subItem.label)}
                          </Typography>
                        </div>
                      </MenuItem>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </MenuList>
      </Menu>
    </div>
  );
}
