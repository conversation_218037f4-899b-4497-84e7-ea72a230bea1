"use client";

import "../../styles/globals.css"
import "react-toastify/dist/ReactToastify.css";
import { X } from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useIsMobile } from "../hook/useIsMobile";
import { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { ToastContainer } from "react-toastify";
import Notifications from "@/components/home/<USER>";
import UserAvatar from "@/components/avatar/UserAvatar";
import { Bars3CenterLeftIcon } from "@heroicons/react/24/solid";

// Dynamically import components that might cause hydration issues
const DynamicSidebar = dynamic(() => import("./sidebar"), {
  ssr: false,
});

export default function AdminLayout({ children }) {
  // Use state to track if component is mounted
  const [isMounted, setIsMounted] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  // Get mobile status and user info
  const isMobile = useIsMobile();
  const { user } = useAuth();

  // Handle component mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Close mobile sidebar when screen size changes
  useEffect(() => {
    if (!isMobile && isMounted) {
      setMobileSidebarOpen(false);
    }
  }, [isMobile, isMounted]);

  // Prevent hydration errors by rendering a simplified layout until client-side
  if (!isMounted) {
    return (
      <div className="w-full flex flex-col min-h-screen bg-gray-50">
        <div className="fixed top-0 left-0 right-0 w-full h-16 bg-white border-b border-gray-200 z-30"></div>
        <div className="flex flex-1 pt-16 relative">
          <main className="flex-1">
            <div className="p-4 md:p-6 min-h-[calc(100vh-4rem)]">
              {/* Don't render children until mounted to prevent hydration mismatch */}
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-64 bg-gray-200 rounded mb-4"></div>
                <div className="h-32 bg-gray-200 rounded mb-4"></div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 w-full h-16 bg-white border-b border-gray-200 z-30 flex items-center justify-between px-4 md:px-6">
        <div className="flex items-center space-x-4">
          {/* Mobile menu toggle */}
          {isMobile && (
            <button
              onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}
              className="p-2 rounded-md hover:bg-gray-100"
              aria-label="Toggle mobile menu"
            >
              <Bars3CenterLeftIcon className="h-7 w-7 text-gray-600" />
            </button>
          )}

          {/* Desktop sidebar collapse toggle */}
          {!isMobile && (
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 rounded-md hover:bg-gray-100"
              aria-label="Toggle sidebar"
            >
              <Bars3CenterLeftIcon className="h-7 w-7 text-gray-600" />
            </button>
          )}

          <h1 className="text-xl font-inter text-gray-800">Admin Dashboard</h1>
        </div>

        <div className="flex items-center space-x-4">
          <Notifications />

          {/* User Profile */}
          {user && <UserAvatar user={user} showName={!isMobile} />}
        </div>
      </header>

      {/* Main Content Section */}
      <div className="flex flex-1 pt-16 relative">
        {/* Sidebar - Desktop (Collapsible) */}
        {!isMobile && (
          <aside
            className={`fixed top-16 left-0 h-[calc(100vh-4rem)] bg-white border-r border-gray-200 z-20 transition-all duration-300 ${
              sidebarCollapsed ? "w-16" : "w-64"
            }`}
          >
            <DynamicSidebar collapsed={sidebarCollapsed} />
          </aside>
        )}

        {/* Sidebar - Mobile (Overlay) */}
        {isMobile && mobileSidebarOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setMobileSidebarOpen(false)}>
            <aside
              className="fixed top-0 left-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h2 className="font-semibold text-gray-800">Admin Menu</h2>
                <button
                  onClick={() => setMobileSidebarOpen(false)}
                  className="p-2 rounded-md hover:bg-gray-100"
                  aria-label="Close menu"
                >
                  <X className="h-5 w-5 text-gray-600" />
                </button>
              </div>
              <DynamicSidebar collapsed={false} />
            </aside>
          </div>
        )}

        {/* Main Content Area */}
        <main
          className={`flex-1 transition-all duration-300 ${
            !isMobile ? (sidebarCollapsed ? "ml-16" : "ml-64") : "ml-0"
          }`}
        >
          <div className="p-4 md:p-6 min-h-[calc(100vh-4rem)]">
            {children}
          </div>
        </main>
      </div>

      {/* Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
}
