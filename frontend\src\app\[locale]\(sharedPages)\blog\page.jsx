import { getPublishedPosts } from "@/app/lib/notion";
import PostCard from "@/components/blog/postCard";
import RecentPosts from "@/components/blog/recentPosts";
import DynamicMetadataClient from "@/components/DynamicMetadataClient";

export default async function BlogPage() {
  const posts = await getPublishedPosts();
  return (
    <div className="bg-white">
      <DynamicMetadataClient
        title={"ZtechEngineering | Blog"}
        desc={"Explore ZtechEngineering's blog for the latest insights on technology trends, engineering solutions, industry best practices, and innovative project case studies."} 
      />
      <div className="max-w-[1400px] mx-auto min-h-screen p-0">
        <RecentPosts posts={posts} />

        <section className="p-10 mx-auto flex flex-col items-center gap-y-6">
          <h2 className="text-xl font-medium font-inter self-start">
            Tous les articles
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 w-full mx-auto">
            {posts.map((post) => (
              <PostCard key={post.id} post={post} />
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
