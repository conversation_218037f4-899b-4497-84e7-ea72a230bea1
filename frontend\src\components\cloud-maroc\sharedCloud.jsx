import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>con<PERSON>utton,
  <PERSON>,
  <PERSON><PERSON>,
} from "@material-tailwind/react";
import {
  BACKUPsvg,
  BLUECHECKsvg,
  HTTPsvg,
  RAMsvg,
  SSDDISKsvg,
  TERMINALsvg,
  VCPUsvg,
} from "../../icons/svgIcons";
import { BsArrowUpRight } from "react-icons/bs";
import { ArrowRightIcon } from "@heroicons/react/24/solid";
import { useRef } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import sharedCloud from '/public/images/cloud-morocco/shared-cloud.jpg'
import Section from "../home/<USER>";

const plans = [
  {
    name: "plans.0.name",
    features: [
      "plans.0.features.0",
      "plans.0.features.1",
      "plans.0.features.2",
    ],
  },
  {
    name: "plans.1.name",
    features: [
      "plans.1.features.0",
      "plans.1.features.1",
      "plans.1.features.2",
    ],
  },
  {
    name: "plans.2.name",
    features: [
      "plans.2.features.0",
      "plans.2.features.1",
      "plans.2.features.2",
    ],
  },
];

const SharedCloud = ({ t, setData }) => {
  const scrollContainerRef = useRef(null);
  const router = useRouter();

  const scroll = (direction) => {
    const container = scrollContainerRef.current;
    const scrollAmount = 300;
    if (direction === "prev") {
      container.scrollBy({ left: -scrollAmount, behavior: "smooth" });
    } else if (direction === "next") {
      container.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };

  const handleOfferClick = (offerName) => {
    let initialeMessage = t("pack_shared_cloud");
    setData((prevState) => ({
      ...prevState,
      offerName,
      id: 3,
      initialeMessage,
    }));
    router.push("#contact-nous");
  };

  return (
    <Section className="bg-white w-full">
      <div className="max-w-[1400px] mx-auto flex flex-col gap-y-10 items-center py-20 bg-white text-primary">
        {/* Header Section */}
        <div className="bg-transparent relative text-center flex flex-col items-center justify-center gap-y-2 p-4 overflow-hidden w-full">
          <div className="flex flex-col md:flex-row-reverse justify-between mt-0">
          <div className="md:w-2/3 flex items-center justify-center">
            <Image 
              src={sharedCloud}
              alt="shared hosting"
              width={400}
              height={400}
              className="object-cover w-full h-full"
              loading="lazy"

            />
            </div>
            
            <div className="md:w-3/4 p-4 text-left flex flex-col gap-y-6">
              <Typography
                variant="h2"
                className="text-black font-extrabold font-inter text-5xl capitalize"
              >
                {t("shared_cloud_title")}
              </Typography>
              <p className="text-gray-600 text-lg">
                {t("shared_cloud_description")}
                <br />
                <br />
                {t("shared_cloud_benefits")}.
              </p>
              <div className="flex justify-between">
                <Button
                  onClick={() => handleOfferClick(t("shared_cloud_title"))}
                  className="w-full px-6 py-3 flex md:w-fit md:justify-normal text-black shadow-none uppercase border border-black gap-x-4 items-center justify-center rounded-3xl bg-transparent hover:bg-secondary hover:border-secondary hover:text-white font-inter text-sm font-medium transition-all duration-300"
                >
                  {t("request_quote")}
                  <ArrowRightIcon className="w-4 my-auto" />
                </Button>
              </div>

              {/* Plans Section */}
              <div
                ref={scrollContainerRef}
                className="w-full hidden md:flex relative md:overflow-x-auto hide-scrollbar"
              >
                <div className="flex md:flex-row flex-col gap-y-4 md:gap-x-4 md:w-max px-4">
                  {plans.map((plan, index) => (
                    <div
                      key={index}
                      className="flex-shrink-0 w-[300px] border border-gray-300 rounded-lg bg-white shadow-md p-6 hover:shadow-lg transition-shadow duration-300"
                    >
                      <Typography
                        variant="h5"
                        className="text-black font-semibold mb-4"
                      >
                        {t(plan.name)}
                      </Typography>
                      <ul className="space-y-3">
                        {plan.features.map((feature, idx) => (
                          <li
                            key={idx}
                            className="flex items-center gap-2 text-gray-700"
                          >
                            <BLUECHECKsvg />
                            {t(feature)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="w-full mt-6 md:hidden relative md:overflow-x-auto hide-scrollbar">
              <div className="flex flex-col gap-y-4 px-4">
                {plans.map((plan, index) => (
                  <div
                    key={index}
                    className="flex-shrink-0 w-[300px] border mx-auto border-gray-300 rounded-lg bg-white shadow-md p-6 hover:shadow-lg transition-shadow duration-300"
                  >
                    <Typography
                      variant="h5"
                      className="text-black font-semibold mb-4"
                    >
                      {t(plan.name)}
                    </Typography>
                    <ul className="space-y-3">
                      {plan.features.map((feature, idx) => (
                        <li
                          key={idx}
                          className="flex items-center gap-2 text-gray-700"
                        >
                          <BLUECHECKsvg />
                          {t(feature)}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="flex flex-col items-center justify-center mb-0">
          <Typography
            variant="h4"
            className="text-3xl text-center md:text-left md:text-2xl font-semibold"
          >
            {t("host_shared_cloud_in")}
          </Typography>
          <div className="flex gap-x-2">
            <Image
              src="/images/services/Morocco-flag.png"
              alt="Morocco flag"
              className="w-8 h-fit"
              width={32}
              height={32}
            />
            <Typography
              variant="h4"
              className="mt-2 text-secondary mx-auto font-jim_ngihtshade font-medium text-2xl"
            >
              {t("maroc")}
            </Typography>
            <Image
              src="/images/services/Morocco-flag.png"
              alt="Morocco flag"
              className="w-8 h-fit"
              width={32}
              height={32}
            />
          </div>
        </div>

        {/* Feature Cards Section */}
        <div className="w-full max-w-6xl p-4">
          {/* Row 1 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Card 1 */}
            <Card className="p-4 flex flex-row border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#F2F4FB] mr-2 w-fit h-fit rounded-xl p-2 flex">
                <VCPUsvg />
              </div>
              <div className="flex flex-col gap-y-2 my-auto pr-10">
                <Typography
                  variant="h6"
                  className="font-semibold text-md h-fit text-black"
                >
                  512 vCPU
                </Typography>
                <Typography className="text-sm text-gray-500 h-fit w-fit text-nowrap">
                  {t("up_to_512_vcpu_per_instance")}
                </Typography>
              </div>
            </Card>

            {/* Card 2 */}
            <Card className="p-4 flex flex-row border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#F2F4FB] mr-2 w-fit h-fit rounded-xl p-2 flex items-center">
                <RAMsvg />
              </div>
              <div className="flex flex-col gap-y-2 my-auto">
                <Typography
                  variant="h6"
                  className="text-black font-semibold text-md h-fit"
                >
                  {t("1024_gb_ram")}
                </Typography>
                <Typography className="text-sm text-gray-500 h-fit w-fit text-nowrap">
                  {t("up_to_1024_gb_ram_per_instance")}
                </Typography>
              </div>
            </Card>

            {/* Card 3 */}
            <Card className="p-4 flex flex-row border border-gray-200 hover:shadow-lg transition-shadow duration-300">
              <div className="bg-[#F2F4FB] mr-2 w-fit h-fit rounded-xl p-2 flex items-center">
                <SSDDISKsvg />
              </div>
              <div className="flex flex-col gap-y-2 my-auto pr-24">
                <Typography
                  variant="h6"
                  className="text-black font-semibold text-md h-fit"
                >
                  {t("nvme_ssd_storage")}
                </Typography>
                <Typography className="text-sm text-gray-500 h-fit w-fit text-nowrap">
                  {t("customizable_and_scalable")}
                </Typography>
              </div>
            </Card>
          </div>

          {/* Row 2 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Card 4 */}
            <Card
              className="p-4 flex items-center flex-row shadow-sm gap-x-2 text-white rounded-md hover:shadow-lg transition-shadow duration-300"
              style={{
                background: "linear-gradient(90deg, #497EF7 0%, #423FD9 100%)",
              }}
            >
              <div className="items-center h-fit relative p-0 w-fit">
                <BACKUPsvg />
              </div>
              <div className="flex flex-col">
                <Typography variant="h6" className="font-semibold text-md">
                  {t("automated_backups")}
                </Typography>
                <Typography className="text-sm">
                  {t("on_remote_servers")}
                </Typography>
              </div>
            </Card>

            {/* Card 5 */}
            <Card className="p-4 flex items-center flex-row shadow-sm gap-x-2 text-black bg-white h-full rounded-md border hover:shadow-lg transition-shadow duration-300">
              <div className="items-center h-fit relative py-1 w-fit">
                <Image
                  loading="lazy"
                  src="/images/services/iso-sert.png"
                  alt="iso"
                  width={175}
                  height={170}
                  className="object-cover"
                />
              </div>
              <div className="flex flex-row w-full gap-x-5 justify-between">
                <div className="flex flex-col">
                  <Typography variant="h6" className="font-semibold text-md">
                    {t("certified_datacenters")}
                  </Typography>
                  <div className="flex-grow"></div>
                  <div className="flex gap-x-2 w-full">
                    <Chip
                      variant="outlined"
                      value="ISO 27001"
                      className="text-[12px] font-extralight border-[#DADADA]"
                    />
                    <Chip
                      variant="outlined"
                      value="Tier III"
                      className="text-[12px] font-extralight border-[#DADADA]"
                    />
                  </div>
                </div>
                <IconButton
                  onClick={() => console.log("clicked")}
                  className="bg-transparent text-transparent opacity-0 cursor-default self-end"
                >
                  <BsArrowUpRight />
                </IconButton>
              </div>
            </Card>

            {/* Card 6 */}
            <div className="hidden md:flex flex-col gap-y-2">
              <Card className="row-span-1 flex flex-row-reverse p-4 justify-between rounded-md border hover:shadow-lg transition-shadow duration-300">
                <div className="flex flex-col text-sm gap-y-2">
                  <span className="text-black font-medium text-sm">
                    {t("ssh_access")}
                  </span>
                  <span className="text-sm">{t("included")}</span>
                </div>
                <div className="my-auto">
                  <TERMINALsvg />
                </div>
              </Card>
              <Card className="flex flex-row gap-x-4 p-4 justify-between rounded-md border hover:shadow-lg transition-shadow duration-300">
                <div className="flex flex-col text-sm gap-y-2">
                  <span className="text-black font-medium text-sm">HTTP/2</span>
                  <span className="text-sm">{t("included")}</span>
                </div>
                <div>
                  <HTTPsvg />
                </div>
              </Card>
            </div>
          </div>

          {/* Row 3 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: "/images/services/hand-icon.png",
                title: t("cpanel_management_panel"),
              },
              {
                icon: "/images/services/cpanel-icon.png",
                title: t("instant_activation"),
              },
              {
                icon: "/images/services/wp-logo.png",
                title: t("wordpress_toolkit_deluxe"),
              },
              { icon: <SSDDISKsvg />, title: t("antivirus_and_antimalware") },
              {
                icon: "/images/services/ssl-check-icon.png",
                title: t("free_ssl_certificate"),
              },
              {
                icon: "/images/services/ssl-icon.png",
                title: t("phone_and_ticket_support"),
              },
              { icon: <BLUECHECKsvg />, title: t("free_site_migration") },
              { icon: <BLUECHECKsvg />, title: t("30_day_money_back") },
              { icon: <BLUECHECKsvg />, title: t("free_domain_first_year") },
            ].map((item, index) => (
              <Card
                key={index}
                className="p-4 flex flex-row border border-gray-200 w-full justify-between hover:shadow-lg transition-shadow duration-300"
              >
                {typeof item.icon === "string" ? (
                  <img
                    loading="lazy"
                    src={item.icon}
                    alt={item.title}
                    className="w-12 h-12  mr-2"
                  />
                ) : (
                  <div className="bg-[#F2F4FB] mr-2 w-fit h-fit rounded-xl p-2 flex">
                    {item.icon}
                  </div>
                )}
                <div className="flex flex-col gap-y-2 my-auto pr-0 w-full">
                  <Typography
                    variant="h6"
                    className="font-semibold text-md h-fit my-auto"
                  >
                    {item.title}
                  </Typography>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </Section>
  );
};

export default SharedCloud;
