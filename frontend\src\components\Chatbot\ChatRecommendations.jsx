import React from 'react';
import PropTypes from 'prop-types';
import { useTranslations } from 'next-intl';

const ChatRecommendations = ({ onSelectRecommendation }) => {
  const t = useTranslations('shared.chatbot');

  // Define common questions/recommendations - limited to 3
  const recommendations = [
    t('website_creation_question'),
    t('hosting_plan_question'),
    t('ssl_question')
  ];

  return (
    <div className="mb-4">
      <p className="text-sm text-gray-600 mb-2">{t('common_questions')}</p>
      <div className="flex flex-col gap-2">
        {recommendations.map((recommendation, index) => (
          <button
            key={index}
            onClick={() => onSelectRecommendation(recommendation)}
            className="text-left py-3 px-4 rounded-full border border-[#497ef7]/30 text-gray-700 text-sm hover:border-[#497ef7] hover:bg-[#497ef7]/5 transition-colors"
          >
            {recommendation}
          </button>
        ))}
      </div>
    </div>
  );
};

ChatRecommendations.propTypes = {
  onSelectRecommendation: PropTypes.func.isRequired
};

export default ChatRecommendations;
