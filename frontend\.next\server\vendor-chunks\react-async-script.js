"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-async-script";
exports.ids = ["vendor-chunks/react-async-script"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-async-script/lib/esm/async-script-loader.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-async-script/lib/esm/async-script-loader.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ makeAsyncScript)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_1__);\nfunction _extends() {\n    _extends = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    subClass.__proto__ = superClass;\n}\n\n\n\nvar SCRIPT_MAP = {}; // A counter used to generate a unique id for each component that uses the function\nvar idCount = 0;\nfunction makeAsyncScript(getScriptURL, options) {\n    options = options || {};\n    return function wrapWithAsyncScript(WrappedComponent) {\n        var wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n        var AsyncScriptLoader = /*#__PURE__*/ function(_Component) {\n            _inheritsLoose(AsyncScriptLoader, _Component);\n            function AsyncScriptLoader(props, context) {\n                var _this;\n                _this = _Component.call(this, props, context) || this;\n                _this.state = {};\n                _this.__scriptURL = \"\";\n                return _this;\n            }\n            var _proto = AsyncScriptLoader.prototype;\n            _proto.asyncScriptLoaderGetScriptLoaderID = function asyncScriptLoaderGetScriptLoaderID() {\n                if (!this.__scriptLoaderID) {\n                    this.__scriptLoaderID = \"async-script-loader-\" + idCount++;\n                }\n                return this.__scriptLoaderID;\n            };\n            _proto.setupScriptURL = function setupScriptURL() {\n                this.__scriptURL = typeof getScriptURL === \"function\" ? getScriptURL() : getScriptURL;\n                return this.__scriptURL;\n            };\n            _proto.asyncScriptLoaderHandleLoad = function asyncScriptLoaderHandleLoad(state) {\n                var _this2 = this;\n                // use reacts setState callback to fire props.asyncScriptOnLoad with new state/entry\n                this.setState(state, function() {\n                    return _this2.props.asyncScriptOnLoad && _this2.props.asyncScriptOnLoad(_this2.state);\n                });\n            };\n            _proto.asyncScriptLoaderTriggerOnScriptLoaded = function asyncScriptLoaderTriggerOnScriptLoaded() {\n                var mapEntry = SCRIPT_MAP[this.__scriptURL];\n                if (!mapEntry || !mapEntry.loaded) {\n                    throw new Error(\"Script is not loaded.\");\n                }\n                for(var obsKey in mapEntry.observers){\n                    mapEntry.observers[obsKey](mapEntry);\n                }\n                delete window[options.callbackName];\n            };\n            _proto.componentDidMount = function componentDidMount() {\n                var _this3 = this;\n                var scriptURL = this.setupScriptURL();\n                var key = this.asyncScriptLoaderGetScriptLoaderID();\n                var _options = options, globalName = _options.globalName, callbackName = _options.callbackName, scriptId = _options.scriptId; // check if global object already attached to window\n                if (globalName && typeof window[globalName] !== \"undefined\") {\n                    SCRIPT_MAP[scriptURL] = {\n                        loaded: true,\n                        observers: {}\n                    };\n                } // check if script loading already\n                if (SCRIPT_MAP[scriptURL]) {\n                    var entry = SCRIPT_MAP[scriptURL]; // if loaded or errored then \"finish\"\n                    if (entry && (entry.loaded || entry.errored)) {\n                        this.asyncScriptLoaderHandleLoad(entry);\n                        return;\n                    } // if still loading then callback to observer queue\n                    entry.observers[key] = function(entry) {\n                        return _this3.asyncScriptLoaderHandleLoad(entry);\n                    };\n                    return;\n                }\n                /*\n         * hasn't started loading\n         * start the \"magic\"\n         * setup script to load and observers\n         */ var observers = {};\n                observers[key] = function(entry) {\n                    return _this3.asyncScriptLoaderHandleLoad(entry);\n                };\n                SCRIPT_MAP[scriptURL] = {\n                    loaded: false,\n                    observers: observers\n                };\n                var script = document.createElement(\"script\");\n                script.src = scriptURL;\n                script.async = true;\n                for(var attribute in options.attributes){\n                    script.setAttribute(attribute, options.attributes[attribute]);\n                }\n                if (scriptId) {\n                    script.id = scriptId;\n                }\n                var callObserverFuncAndRemoveObserver = function callObserverFuncAndRemoveObserver(func) {\n                    if (SCRIPT_MAP[scriptURL]) {\n                        var mapEntry = SCRIPT_MAP[scriptURL];\n                        var observersMap = mapEntry.observers;\n                        for(var obsKey in observersMap){\n                            if (func(observersMap[obsKey])) {\n                                delete observersMap[obsKey];\n                            }\n                        }\n                    }\n                };\n                if (callbackName && \"undefined\" !== \"undefined\") {}\n                script.onload = function() {\n                    var mapEntry = SCRIPT_MAP[scriptURL];\n                    if (mapEntry) {\n                        mapEntry.loaded = true;\n                        callObserverFuncAndRemoveObserver(function(observer) {\n                            if (callbackName) {\n                                return false;\n                            }\n                            observer(mapEntry);\n                            return true;\n                        });\n                    }\n                };\n                script.onerror = function() {\n                    var mapEntry = SCRIPT_MAP[scriptURL];\n                    if (mapEntry) {\n                        mapEntry.errored = true;\n                        callObserverFuncAndRemoveObserver(function(observer) {\n                            observer(mapEntry);\n                            return true;\n                        });\n                    }\n                };\n                document.body.appendChild(script);\n            };\n            _proto.componentWillUnmount = function componentWillUnmount() {\n                // Remove tag script\n                var scriptURL = this.__scriptURL;\n                if (options.removeOnUnmount === true) {\n                    var allScripts = document.getElementsByTagName(\"script\");\n                    for(var i = 0; i < allScripts.length; i += 1){\n                        if (allScripts[i].src.indexOf(scriptURL) > -1) {\n                            if (allScripts[i].parentNode) {\n                                allScripts[i].parentNode.removeChild(allScripts[i]);\n                            }\n                        }\n                    }\n                } // Clean the observer entry\n                var mapEntry = SCRIPT_MAP[scriptURL];\n                if (mapEntry) {\n                    delete mapEntry.observers[this.asyncScriptLoaderGetScriptLoaderID()];\n                    if (options.removeOnUnmount === true) {\n                        delete SCRIPT_MAP[scriptURL];\n                    }\n                }\n            };\n            _proto.render = function render() {\n                var globalName = options.globalName; // remove asyncScriptOnLoad from childProps\n                var _this$props = this.props, asyncScriptOnLoad = _this$props.asyncScriptOnLoad, forwardedRef = _this$props.forwardedRef, childProps = _objectWithoutPropertiesLoose(_this$props, [\n                    \"asyncScriptOnLoad\",\n                    \"forwardedRef\"\n                ]); // eslint-disable-line no-unused-vars\n                if (globalName && \"undefined\" !== \"undefined\") {}\n                childProps.ref = forwardedRef;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, childProps);\n            };\n            return AsyncScriptLoader;\n        }(react__WEBPACK_IMPORTED_MODULE_0__.Component); // Note the second param \"ref\" provided by React.forwardRef.\n        // We can pass it along to AsyncScriptLoader as a regular prop, e.g. \"forwardedRef\"\n        // And it can then be attached to the Component.\n        var ForwardedComponent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(props, ref) {\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(AsyncScriptLoader, _extends({}, props, {\n                forwardedRef: ref\n            }));\n        });\n        ForwardedComponent.displayName = \"AsyncScriptLoader(\" + wrappedComponentName + \")\";\n        ForwardedComponent.propTypes = {\n            asyncScriptOnLoad: (prop_types__WEBPACK_IMPORTED_MODULE_2___default().func)\n        };\n        return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_1___default()(ForwardedComponent, WrappedComponent);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-async-script/lib/esm/async-script-loader.js\n");

/***/ })

};
;