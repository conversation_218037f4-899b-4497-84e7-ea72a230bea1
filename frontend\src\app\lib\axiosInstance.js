import axios from "axios";
import { BACKEND_URL } from "../config/constant";

const axiosInstance = axios.create({
  baseURL: BACKEND_URL,
  timeout: 60000, // 60 secondes pour l'API Contabo
  withCredentials: true,
});

// Intercepteur de requête pour ajouter le token d'authentification
axiosInstance.interceptors.request.use(
  (config) => {
    // Récupérer le token depuis les cookies
    if (typeof document !== 'undefined') {
      const cookies = document.cookie.split(';');
      const tokenCookie = cookies.find(cookie => cookie.trim().startsWith('token='));
      if (tokenCookie) {
        const token = tokenCookie.split('=')[1];
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      console.log("from axios instance, try to redirect user to login");
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
