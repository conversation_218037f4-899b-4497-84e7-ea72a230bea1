import { useRef, useEffect } from "react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { InfoIcon, ChevronDown, LockIcon } from "lucide-react";
import SSLCard from "./SSLCard";

const SSLFilters = ({
  activeTab,
  handleTabChange,
  filter,
  handleFilterChange,
  toggleExpand,
  expandedItem,
  sslBrands,
  typeCategories,
  matchesBrand,
  filteredPlans,
  matchesSSLType,
  subTabsContainerRef,
  locale,
  onSelectPlan,
  billingPeriod,
  setBillingPeriod
}) => {
  const t = useTranslations("SSL2");

  const tabData = [
    { label: t("tabs.brand"), value: "brand" },
    { label: t("tabs.type"), value: "type" },
  ];

  // Helper to render empty state when no plans are available
  const renderEmptyState = () => (
    <div className="px-4 py-8 bg-gray-50 text-center">
      <LockIcon className="mx-auto h-8 w-8 text-gray-400" />
      <h3 className="mt-2 text-base font-medium text-gray-900">
        {t("empty_state.title")}
      </h3>
      <p className="mt-1 text-sm text-gray-500">
        {t("empty_state.subtitle")}
      </p>
    </div>
  );

  return (
    <div className="mb-0">
      {/* Primary Tabs - Material UI style */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-0">
          {/* Brand/Type Toggle */}
          <div className="flex bg-white rounded-lg shadow-sm p-1 border border-gray-200 mb-4 sm:mb-0">
            {tabData.map(({ label, value }) => (
              <button
                key={value}
                onClick={() => handleTabChange(value)}
                className={`
                  px-4 py-1.5 rounded-md text-sm font-medium transition-all flex-1 sm:flex-auto
                  ${activeTab === value
                    ? "bg-blue-500 text-white"
                    : "text-gray-600 hover:bg-gray-100"}
                `}
              >
                {label}
              </button>
            ))}
          </div>
          <div className="sm:ml-auto">
            {/* Period Dropdown */}
            <div className="relative">
              <select
                value={billingPeriod}
                onChange={(e) => setBillingPeriod(e.target.value)}
                className="appearance-none bg-white border border-gray-300 rounded-lg py-2 pl-4 pr-10 text-sm font-medium text-gray-700 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm w-36"
              >
                <option value="month">{t("periods.one_month")}</option>
                <option value="1year">{t("periods.one_year")}</option>
                <option value="2years">{t("periods.two_years")}</option>
                <option value="3years">{t("periods.three_years")}</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <ChevronDown className="h-4 w-4" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table-like container */}
      <div className="border border-gray-300 border-3">
        {/* Subtabs - Desktop vs Mobile rendering */}
        <div className="mb-0">
          {activeTab === "brand" && (
            <>
              {/* Desktop view - horizontal tabs */}
              <div className="hidden md:flex border-2 border-gray-200" ref={subTabsContainerRef}>
                {sslBrands.map((brand, index) => {
                  return (
                    <div key={brand.name} className="flex flex-1">
                      <button
                        data-value={brand.name}
                        onClick={() => handleFilterChange("brand", brand.name)}
                        className={`
                          flex-1 px-6 py-3 text-sm subtab-button relative
                          ${filter.value === brand.name ? 'bg-blue-50' : 'hover:bg-gray-50'}
                          ${filter.value === brand.name ? 'border-b-2 border-blue-500' : ''}
                        `}
                      >
                        <div className="flex flex-col items-center justify-center">
                          <div className="relative w-24 h-12 sm:w-28 sm:h-14">
                            <Image
                              src={`/images/ssl_brands/${brand.name.toLowerCase()}.svg`}
                              alt={`${brand.name} logo`}
                              fill
                              sizes="(max-width: 768px) 100px, 150px"
                              style={{ objectFit: "contain" }}
                            />
                          </div>
                        </div>
                      </button>
                      {index < sslBrands.length - 1 && (
                        <div className="w-0.5 bg-gray-200 self-stretch"></div>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Mobile view - accordions for brands */}
              <div className="md:hidden">
                {sslBrands.map((brand) => {
                  const isActive = filter.value === brand.name;
                  const isExpanded = expandedItem === brand.name;
                  const brandPlans = filteredPlans.filter(plan =>
                    matchesBrand(plan.brandObj.name, brand.name)
                  );

                  return (
                    <div key={brand.name} className="border-b border-gray-200 last:border-b-0">
                      {/* Brand Header Button */}
                      <button
                        onClick={(e) => {
                          handleFilterChange("brand", brand.name);
                          toggleExpand(brand.name, e);
                        }}
                        className={`
                          w-full flex items-center justify-between px-4 py-4
                          ${isActive ? 'bg-blue-50' : 'bg-white'}
                        `}
                      >
                        {/* Enhanced brand image container */}
                        <div className="flex-1 flex items-center">
                          <div className="relative w-32 h-16">
                            <Image
                              src={`/images/ssl_brands/${brand.name.toLowerCase()}.svg`}
                              alt={`${brand.name} logo`}
                              fill
                              sizes="128px"
                              style={{
                                objectFit: "contain",
                                objectPosition: "left center"
                              }}
                            />
                          </div>
                        </div>
                        <ChevronDown className={`h-5 w-5 text-gray-500 transition-transform ${isExpanded ? 'rotate-180' : ''} ml-2`} />
                      </button>

                      {/* Display expanded content */}
                      {isExpanded && (
                        <>
                          {/* Description section */}
                          {t(`brands.${brand.name.toLowerCase().replace(/\s+/g, '_')}`) && (
                            <div className="bg-blue-50 p-4 border-y border-blue-100">
                              <div className="flex items-start">
                                <InfoIcon className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5" />
                                <p className="text-blue-700 text-sm leading-relaxed">
                                  {t(`brands.${brand.name.toLowerCase().replace(/\s+/g, '_')}`)}
                                </p>
                              </div>
                            </div>
                          )}

                          {/* Brand Plans are now rendered here */}
                          {brandPlans.length > 0 ? (
                            <div className="px-4 py-5 bg-gray-50">
                              <div className="space-y-4">
                                {brandPlans.map(plan => (
                                  <SSLCard
                                    key={plan._id}
                                    plan={plan}
                                    onSelectPlan={onSelectPlan}
                                    locale={locale}
                                    billingPeriod={billingPeriod}
                                  />
                                ))}
                              </div>
                            </div>
                          ) : renderEmptyState()}
                        </>
                      )}
                    </div>
                  );
                })}
              </div>
            </>
          )}

          {activeTab === "type" && (
            <>
              {/* Desktop view - horizontal tabs */}
              <div className="hidden md:flex border-2 border-gray-200" ref={subTabsContainerRef}>
                {typeCategories.map((type, index) => (
                  <div key={type} className="flex flex-1">
                    <button
                      data-value={type}
                      onClick={() => handleFilterChange("type", type)}
                      className={`
                        flex-1 px-6 py-3 text-sm subtab-button relative
                        ${filter.value === type ? 'bg-blue-50' : 'hover:bg-gray-50'}
                        ${filter.value === type ? 'border-b-2 border-blue-500' : ''}
                      `}
                    >
                      {type}
                    </button>
                    {index < typeCategories.length - 1 && (
                      <div className="w-0.5 bg-gray-200 self-stretch"></div>
                    )}
                  </div>
                ))}
              </div>

              {/* Mobile view - accordions for types */}
              <div className="md:hidden">
                {typeCategories.map((type) => {
                  const isActive = filter.value === type;
                  const isExpanded = expandedItem === type;
                  const typePlans = filteredPlans.filter(plan =>
                    matchesSSLType(plan.sslType, type)
                  );

                  return (
                    <div key={type} className="border-b border-gray-200 last:border-b-0">
                      {/* Type Header Button */}
                      <button
                        onClick={(e) => {
                          handleFilterChange("type", type);
                          toggleExpand(type, e);
                        }}
                        className={`
                          w-full flex items-center justify-between px-4 py-3
                          ${isActive ? 'bg-blue-50' : 'bg-white'}
                        `}
                      >
                        <span className="font-medium">{type}</span>
                        <ChevronDown className={`h-5 w-5 text-gray-500 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
                      </button>

                      {/* Show description and plans when expanded */}
                      {isExpanded && (
                        <>
                          {/* Description section */}
                          {t(`types.${type.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_')}`) && (
                            <div className="bg-blue-50 p-4 border-y border-blue-100">
                              <div className="flex items-start">
                                <InfoIcon className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0 mt-0.5" />
                                <div>
                                  <h3 className="font-semibold text-blue-800 mb-1">
                                    {type} Certificates
                                  </h3>
                                  <p className="text-blue-700 text-sm leading-relaxed">
                                    {t(`types.${type.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_')}`)}
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Type Plans are now rendered here */}
                          {typePlans.length > 0 ? (
                            <div className="px-4 py-5 bg-gray-50">
                              <div className="space-y-4">
                                {typePlans.map(plan => (
                                  <SSLCard
                                    key={plan._id}
                                    plan={plan}
                                    onSelectPlan={onSelectPlan}
                                    locale={locale}
                                    billingPeriod={billingPeriod}
                                  />
                                ))}
                              </div>
                            </div>
                          ) : renderEmptyState()}
                        </>
                      )}
                    </div>
                  );
                })}
              </div>
            </>
          )}
        </div>

        {/* Description panel for desktop */}
        {filter && (
          <div className="hidden md:block bg-white p-5 pt-3 mt-0 border-t border-gray-200">
            <div className="flex flex-col">
              <div className="flex items-center mb-2">
                <InfoIcon className="h-5 w-5 text-blue-600 mr-2 flex-shrink-0" />
                <h3 className="font-bold text-lg text-blue-800">
                  {filter.type === "brand"
                    ? filter.value
                    : filter.value + " Certificates"}
                </h3>
              </div>
              <div className="pl-7">
                <p className="text-blue-700 leading-relaxed">
                  {filter.type === "brand"
                    ? t(`brands.${filter.value.toLowerCase().replace(/\s+/g, '_')}`)
                    : t(`types.${filter.value.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_')}`)}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SSLFilters;
