import apiService from '../lib/apiService';

const orderService = {
    // Create a new order from the cart
    createOrder: (data) => apiService.post('/order/create-order', data, { withCredentials: true }),

    // Get a specific order by ID
    getOrder: (orderId) => apiService.get(`/order/get-order/${orderId}`, { withCredentials: true }),

    getMyOrders: () => apiService.get(`/order/get-my-orders/`, { withCredentials: true }),

    // Update the order status
    updateOrderStatus: (orderId, data) => apiService.put(`/order/update-status/${orderId}`, data, { withCredentials: true }),

    // Mark the order as paid
    markOrderAsPaid: (orderId) => apiService.put(`/order/mark-paid/${orderId}`, { withCredentials: true }),

    // Process a refund for the order
    refundOrder: (orderId) => apiService.put(`/order/refund/${orderId}`, { withCredentials: true }),

    // Get suborders by user ID and category name
    getSubOrdersByCategory: (categoryName) => apiService.get(`/order/get-suborders-by-category/${categoryName}`, { withCredentials: true }),
    
    // Get a specific suborder by ID
    getSubOrderById: (subOrderId) => apiService.get(`/order/get-suborder/${subOrderId}`, { withCredentials: true }),

};

export default orderService;
