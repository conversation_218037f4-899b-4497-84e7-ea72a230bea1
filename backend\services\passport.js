const passport = require("passport");
const GoogleStrategy = require("passport-google-oauth20").Strategy;
const FacebookStrategy = require("passport-facebook").Strategy;
const GitHubStrategy = require("passport-github2").Strategy;
const { isProd } = require("../constants/constant");
require("dotenv").config();

// Google OAuth Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: isProd
        ? process.env.GOOGLE_REDIRECT_URI_PROD
        : process.env.GOOGLE_REDIRECT_URI_DEV,
    },
    async (accessToken, refreshToken, profile, done) => {
      return done(null, { profile });
    }
  )
);

// Facebook OAuth Strategy
passport.use(
  new FacebookStrategy(
    {
      clientID: process.env.FACEBOOK_APP_ID, // Facebook App ID
      clientSecret: process.env.FACEBOOK_APP_SECRET, // Facebook App Secret
      callbackURL: isProd
        ? process.env.FACEBOOK_REDIRECT_URI_PROD
        : process.env.FACEBOOK_REDIRECT_URI_DEV,
      profileFields: ["id", "emails", "name", "picture"],
    },
    async (accessToken, refreshToken, profile, done) => {
      //   console.log("Facebook profile:", profile);
      return done(null, { profile });
    }
  )
);

// Github OAuth Strategy
passport.use(
  new GitHubStrategy(
    {
      clientID: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
      callbackURL: isProd
        ? process.env.GITHUB_REDIRECT_URI_PROD
        : process.env.GITHUB_REDIRECT_URI_DEV,
      scope: ["user:email"],
      passReqToCallback: true, // Add this to get access to request object
    },
    async (req, accessToken, refreshToken, profile, done) => {
      try {
        // Log OAuth details for debugging (remove in production)
        console.log("GitHub OAuth Debug:", {
          clientID: process.env.GITHUB_CLIENT_ID,
          callbackURL: isProd
            ? process.env.GITHUB_REDIRECT_URI_PROD
            : process.env.GITHUB_REDIRECT_URI_DEV,
          accessToken: accessToken ? "Present" : "Missing",
          profile: profile ? "Present" : "Missing",
        });

        if (!accessToken) {
          throw new Error("Access token not received from GitHub");
        }

        const response = await fetch("https://api.github.com/user/emails", {
          headers: {
            Authorization: `token ${accessToken}`,
            Accept: "application/vnd.github.v3+json",
            "User-Agent": "node.js",
          },
        });

        if (!response.ok) {
          throw new Error(`GitHub API error: ${response.status}`);
        }

        const data = await response.json();
        const primaryEmail = data.find((email) => email.primary)?.email;
        profile.emails = [{ value: primaryEmail }];

        return done(null, { profile });
      } catch (error) {
        console.error("GitHub OAuth error:", error.message);
        return done(error, null);
      }
    }
  )
);
