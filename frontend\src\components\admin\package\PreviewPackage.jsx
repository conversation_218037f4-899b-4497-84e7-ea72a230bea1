import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { roundThis, setServerMedia } from "../../../app/helpers/helpers";

const PreviewPackage = ({ title, price, discounts = [], features, image }) => {
  const [billingPeriod, setBillingPeriod] = useState("monthly");
  const [discountPercentage, setDiscountPercentage] = useState(0);
  const [regularPrice, setRegularPrice] = useState(price);
  const [displayPrice, setDisplayPrice] = useState(price);

  const calculateDiscountedPrice = (originalPrice, discountPercent) => {
    return originalPrice * (1 - discountPercent / 100);
  };

  const calculateRegularPrice = (price, discountPercent) => {
    if (discountPercent === 0) return price;
    return price / (1 - discountPercent / 100);
  };

  useEffect(() => {
    const period = billingPeriod === "monthly" ? 1 : 12;
    const discountsArray = Array.isArray(discounts) ? discounts : [];
    const currentDiscount =
      discountsArray.find((d) => d.period === period)?.percentage || 0;

    if (billingPeriod === "monthly") {
      // For monthly: show original price and fake inflated regular price
      setDisplayPrice(price);
      setRegularPrice(calculateRegularPrice(price, currentDiscount));
    } else {
      // For yearly: show discounted price and original price as regular
      setDisplayPrice(calculateDiscountedPrice(price, currentDiscount));
      setRegularPrice(price);
    }
    setDiscountPercentage(currentDiscount);
  }, [billingPeriod, price, discounts]);

  const handleToggle = () => {
    setBillingPeriod((prev) => (prev === "monthly" ? "yearly" : "monthly"));
  };

  // Check if we have both monthly and yearly discounts
  const hasMultiplePeriods = discounts.length > 1;

  return (
    <div className="relative w-full flex flex-col items-center justify-center transform transition-all duration-300 hover:scale-[1.02]">
      {hasMultiplePeriods && (
        <button
          onClick={handleToggle}
          className="mb-4 px-4 py-2 mx-auto text-center bg-blue-300 text-white rounded-full"
        >
          Switch to {billingPeriod === "yearly" ? "Monthly" : "Yearly"}
        </button>
      )}
      <div className="max-w-sm w-full mx-auto flex flex-col rounded-2xl shadow-lg overflow-hidden border border-gray-200 bg-white">
        {/* Header with gradient */}
        <div className="bg-blue-300 text-white py-4 px-6 relative">
          <Typography
            variant="h2"
            className="text-white text-xl font-medium uppercase font-poppins tracking-wider"
          >
            {title || "Package Name"}
          </Typography>
          {discountPercentage > 0 && (
            <div className="absolute top-3 -right-9 bg-green-500 text-white px-10 py-1 rotate-45 text-sm">
              Save {discountPercentage}%
            </div>
          )}
          <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center">
              <div className="w-6 h-6 bg-blue-300 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 flex-1 flex flex-col">
          {/* Image */}
          {image && (
            <div className="mb-6 flex justify-center">
              <div className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-gray-100 shadow-inner">
                <Image
                  src={setServerMedia(image)}
                  alt={title}
                  layout="fill"
                  objectFit="cover"
                  className="transition-transform duration-300 hover:scale-110"
                />
              </div>
            </div>
          )}

          {/* Price Section */}
          <div className="mb-6 text-center">
            <div className="flex flex-col items-center justify-center gap-2 flex-wrap">
              {discountPercentage > 0 && (
                <span className="text-lg text-gray-400 line-through">
                  {roundThis(regularPrice)}
                  <span className=" font-medium ml-1">MAD</span>
                  <span className="text-base font-medium">/mo</span>
                </span>
              )}
              <div className="">
                <span className="text-4xl font-bold text-gray-800">
                  {roundThis(displayPrice)}
                  <span className="text-xl font-medium ml-2">MAD</span>
                  <span className="text-base font-medium">/mo</span>
                </span>
              </div>
            </div>
            {discountPercentage > 0 && billingPeriod === "yearly" && (
              <div className="text-green-600 text-sm font-medium mt-1">
                Save {discountPercentage}% with Yearly billing
              </div>
            )}
          </div>

          {/* Features */}
          <div className="flex-1"></div>
          <ul className="space-y-3">
            {features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2 text-gray-600">
                <span className="flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                  <svg
                    className="w-3.5 h-3.5 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </span>
                <span className="text-sm">{feature.value}</span>
              </li>
            ))}
          </ul>
          {/* Button */}
          <button className="mt-6 w-full px-6 py-3 bg-blue-300 text-white rounded-full font-medium transition-all duration-300 hover:bg-orange-600 hover:shadow-lg focus:ring-4 focus:ring-orange-200 focus:outline-none">
            Choose Plan
          </button>
        </div>
      </div>
    </div>
  );
};

export default PreviewPackage;
