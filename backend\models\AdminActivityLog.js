const mongoose = require("mongoose");

const adminActivityLogSchema = new mongoose.Schema({
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User", // Changed from 'Admin' to 'User' since admins are stored in the User model
    required: true,
  },
  action: {
    type: String,
    // Expanded to include all the custom actions being used in the application
    enum: [
      // Standard CRUD operations
      "CREATE",
      "UPDATE",
      "DELETE",
      "VIEW",
      // User management
      "CREATE_USER",
      "UPDATE_USER",
      "DELETE_USER",
      "MODIFY_USER",
      // Dashboard
      "VIEW_DASHBOARD_STATS",
      // Ticket management
      "CREATE_TICKET",
      "UPDATE_TICKET",
      "DELETE_TICKET",
      "RESOLVE_TICKET",
      "CLOSE_TICKET",
      // Order management
      "UPDATE_ORDER_STATUS",
      "UPDATE_SUBORDER_STATUS",
      // Package management
      "CREATE_PACKAGE",
      "UPDATE_PACKAGE",
      "DELETE_PACKAGE",
      // Other specific actions
      "LOGIN",
      "LOGOUT",
      "EXPORT",
      "IMPORT",
      "UPDATE_SETTINGS",
      "CHANGE_STATUS",
    ],
    required: true,
  },
  targetModel: {
    type: String,
    required: true,
  },
  target: {
    type: mongoose.Schema.Types.Mixed,
  },
  details: {
    type: mongoose.Schema.Types.Mixed,
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
});

// Add indexes for better query performance
adminActivityLogSchema.index({ admin: 1 });
adminActivityLogSchema.index({ action: 1 });
adminActivityLogSchema.index({ targetModel: 1 });
adminActivityLogSchema.index({ timestamp: -1 });

// Create and export the model
const AdminActivityLog = mongoose.model(
  "AdminActivityLog",
  adminActivityLogSchema
);
module.exports = AdminActivityLog;
