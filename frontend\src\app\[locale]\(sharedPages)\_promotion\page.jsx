"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import Head from "next/head"; // Import Head for meta tags
import PromotionIntro from "@/components/promotion/promotionIntro";
import ContactForm2 from "@/components/shared/contactForm2";

function Promotion() {
  const t = useTranslations("promotion");

  const [data, setData] = useState({
    page: "la page de promotion",
    offerName: "No offer selected",
    id: 0,
    url: "/promotion",
  });

  return (
    <div className="bg-white flex flex-col h-full w-full gap-y-16">
      <Head>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <PromotionIntro t={t} />
      <ContactForm2 data={data} setData={setData} />
    </div>
  );
}

export default Promotion;
