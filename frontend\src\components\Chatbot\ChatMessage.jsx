import React, { forwardRef, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import './Chatbot.css';
import AddToCartButton from './AddToCartButton';
import { useRouter } from 'next/navigation';

const ChatMessage = forwardRef(({ message, isUser }, ref) => {
  const router = useRouter();

  // Function to handle route navigation - wrapped in useCallback to prevent dependency changes
  const handleRouteClick = useCallback((route) => {
    router.push(route);
  }, [router]);

  // Function to safely render HTML content
  const createMarkup = (htmlContent) => {
    return { __html: htmlContent };
  };

  // Parse message content regardless of user type
  // Check if the message contains package information that can be added to cart
  const parsePackageInfo = (content) => {
    // Regular expression to find package IDs, names, optional period, and category in the message
    // Format: [[PACKAGE_ID:id:PACKAGE_NAME:name:PERIOD:period:CATEGORY:category]] or [[PACKAGE_ID:id:PACKAGE_NAME:name:CATEGORY:category]] or [[PACKAGE_ID:id:PACKAGE_NAME:name]]
    const packageRegex = /\[\[PACKAGE_ID:([a-f\d]+):PACKAGE_NAME:([^\]]+?)(?::PERIOD:(\d+))?(?::CATEGORY:([^\]]+))?\]\]/g;

    if (!packageRegex.test(content)) {
      return { content, packages: [] };
    }

    // Reset regex state
    packageRegex.lastIndex = 0;

    const packages = [];
    let match;

    // Find all package references
    while ((match = packageRegex.exec(content)) !== null) {
      packages.push({
        id: match[1],
        name: match[2],
        period: match[3] ? parseInt(match[3], 10) : 12, // Default to 12 months (1 year) if not specified
        category: match[4] || "" // Category if specified, empty string if not
      });
    }

    // Remove the package markers from the content
    const cleanContent = content.replace(packageRegex, '');

    return { content: cleanContent, packages };
  };

  // Parse route links in the message
  const parseRouteLinks = (content) => {
    // Regular expression to find route links in the message
    // Format: [[ROUTE:/path/to/page:TITLE:Link Text]]
    const routeRegex = /\[\[ROUTE:([^:]+):TITLE:([^\]]+)\]\]/g;

    if (!routeRegex.test(content)) {
      return { content, routes: [] };
    }

    // Reset regex state
    routeRegex.lastIndex = 0;

    const routes = [];
    let match;
    let processedContent = content;

    // Find all route references
    while ((match = routeRegex.exec(content)) !== null) {
      const route = match[1];
      const title = match[2];
      const fullMatch = match[0];

      // Generate a unique ID for this link
      const linkId = `route-link-${routes.length}`;

      routes.push({
        id: linkId,
        route,
        title
      });

      // Replace the route marker with a span that has an onClick handler
      processedContent = processedContent.replace(
        fullMatch,
        `<span id="${linkId}" class="route-link">${title}</span>`
      );
    }

    return { content: processedContent, routes };
  };

  // First parse package info
  const { content: contentWithoutPackages, packages } = parsePackageInfo(message);

  // Then parse route links
  const { content, routes } = parseRouteLinks(contentWithoutPackages);

  // Add click handlers to route links after the component renders
  useEffect(() => {
    // Only add event listeners if there are routes
    if (routes.length > 0) {
      routes.forEach(route => {
        const element = document.getElementById(route.id);
        if (element) {
          element.addEventListener('click', () => handleRouteClick(route.route));
        }
      });

      // Clean up event listeners when component unmounts
      return () => {
        routes.forEach(route => {
          const element = document.getElementById(route.id);
          if (element) {
            element.removeEventListener('click', () => handleRouteClick(route.route));
          }
        });
      };
    }
  }, [routes, handleRouteClick]);

  // Return different UI based on message type
  if (isUser) {
    return (
      <div className="self-end max-w-[80%]">
        <div className="py-2.5 px-3 rounded-2xl shadow-sm bg-[#497ef7] text-white rounded-br-none">
          <p className="text-sm">{message}</p>
        </div>
      </div>
    );
  }

  // For assistant messages, remove background and take full width
  return (
    <div className="w-full" ref={ref}>
      <div className="py-1 px-1">
        <div
          className="text-sm text-gray-700"
          dangerouslySetInnerHTML={createMarkup(content)}
        />

        {/* Render Add to Cart buttons if packages are found */}
        {packages.length > 0 && (
          <div className="mt-2">
            {packages.map((pkg, index) => (
              <AddToCartButton
                key={index}
                packageId={pkg.id}
                packageName={pkg.name}
                period={pkg.period}
                category={pkg.category}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

ChatMessage.propTypes = {
  message: PropTypes.string.isRequired,
  isUser: PropTypes.bool.isRequired
};

// Add display name to the component
ChatMessage.displayName = 'ChatMessage';

export default ChatMessage;
