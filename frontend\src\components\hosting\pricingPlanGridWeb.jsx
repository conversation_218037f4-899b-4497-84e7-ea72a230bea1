import { Check<PERSON><PERSON>, ArrowRight, AwardIcon } from "lucide-react";
import Skeleton from "react-loading-skeleton";
import { Carousel, Typography } from "@material-tailwind/react";
import { useAuth } from "@/app/context/AuthContext";
import { useParams, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import cartService from "@/app/services/cartService";
import { getLocalizedContent, roundThis } from "@/app/helpers/helpers";

export default function PricingPlanGrid({ loading, plans, billingPeriod, t }) {
  const { setCartCount } = useAuth();
  const { locale = "en" } = useParams();
  const router = useRouter();

  const handleSelectPlan = async (plan) => {
    try {
      const selectedPeriod = 1;
      const discountEntry = plan.discounts.find(
        (d) => d.period === selectedPeriod
      );
      const discountRate = discountEntry?.percentage ?? 0;

      const response = await cartService.addItemToCart({
        packageId: plan._id,
        quantity: 1,
        period: selectedPeriod,
        discountRate,
      });

      setCartCount(response.data.cart.cartCount);
      toast.success(t("added_to_cart"));
      router.push("/client/cart");
    } catch (error) {
      toast.error(
        error.response?.data?.message || t("error_adding_to_cart")
      );
      console.error("Error adding to cart:", error);
    }
  };

  const renderPlanCard = (plan, index) => {
    const price = parseFloat(plan.price);
    const regularPrice = parseFloat(plan.regularPrice);
    const savings = ((regularPrice - price) / regularPrice * 100).toFixed(0);
    const isFeatured = index === 2;

    return (
      <div
        key={plan._id}
        className={`relative bg-white rounded-2xl shadow-md transition-all duration-300 ${
          isFeatured
            ? "ring-4 ring-indigo-500 shadow-xl"
            : "border border-gray-200 hover:border-indigo-500 hover:shadow-lg"
        }`}
      >
        {isFeatured && (
          <div className="absolute -top-4 left-0 right-0 flex justify-center">
            <span className="inline-flex items-center bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              <AwardIcon className="h-4 w-4 mr-1" />
              {t("most_popular")}
            </span>
          </div>
        )}

        <div className="p-6">
          <div className="text-center mb-6">
            <Typography variant="h3" className="text-2xl font-semibold text-gray-900 mb-2">
              {getLocalizedContent(plan, "name", locale)}
            </Typography>
            <Typography className="text-gray-600 text-sm">
              {getLocalizedContent(plan, "description", locale)}
            </Typography>
            <div className="mt-6 space-y-2">
              <span className="text-green-600 font-medium text-sm">
                {t("save")} {savings}%
              </span>
              <div className="flex items-center justify-center gap-2">
                <span className="text-gray-500 line-through text-sm">
                  {roundThis(regularPrice)} MAD
                </span>
                <Typography variant="h2" className="text-3xl font-bold text-indigo-600">
                  {roundThis(price)}
                </Typography>
                <span className="text-gray-600 text-sm">MAD</span>
              </div>
            </div>
          </div>

          <button
            onClick={() => handleSelectPlan(plan)}
            className={`w-full py-3 px-4 rounded-lg font-medium transition-all flex items-center justify-center gap-2 ${
              isFeatured
                ? "bg-indigo-600 hover:bg-indigo-700 text-white"
                : "bg-white hover:bg-gray-50 text-indigo-600 border-2 border-indigo-600 hover:border-indigo-700"
            }`}
          >
            {t("select_plan")}
            <ArrowRight className="h-4 w-4" />
          </button>

          <div className="border-t border-gray-200 my-6" />

          <ul className="space-y-3">
            {plan.specifications.map((spec) => (
              <li key={spec._id} className="flex items-start gap-2">
                <CheckIcon className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                <span className="text-gray-700 text-sm">
                  {getLocalizedContent(spec, "value", locale)}
                </span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={index} height={400} className="rounded-2xl" />
        ))}
      </div>
    );
  }

  return (
    <div className="mb-16">
      {/* Desktop Grid */}
      <div className="hidden md:grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {plans.map(renderPlanCard)}
      </div>

      {/* Mobile Carousel */}
      <Carousel
        className="md:hidden"
        autoplay={false}
        loop={true}
        prevArrow={({ handlePrev }) => (
          <button
            onClick={handlePrev}
            className="absolute top-1/2 left-4 -translate-y-1/2 bg-white/80 p-2 rounded-full hover:bg-white transition-all"
          >
            <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        nextArrow={({ handleNext }) => (
          <button
            onClick={handleNext}
            className="absolute top-1/2 right-4 -translate-y-1/2 bg-white/80 p-2 rounded-full hover:bg-white transition-all"
          >
            <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        )}
        navigation={({ setActiveIndex, activeIndex, length }) => (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {Array.from({ length }).map((_, i) => (
              <span
                key={i}
                className={`block h-2 rounded-full cursor-pointer transition-all ${
                  activeIndex === i ? "w-6 bg-indigo-600" : "w-2 bg-gray-300"
                }`}
                onClick={() => setActiveIndex(i)}
              />
            ))}
          </div>
        )}
      >
        {plans.map((plan, index) => (
          <div key={plan._id} className="px-4">
            {renderPlanCard(plan, index)}
          </div>
        ))}
      </Carousel>
    </div>
  );
}