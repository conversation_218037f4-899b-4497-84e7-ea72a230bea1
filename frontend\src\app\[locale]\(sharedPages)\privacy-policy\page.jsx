import { useTranslations } from "next-intl";
import React from "react";

const PrivacyPolicy = () => {
  const t = useTranslations("privacy_policy");

  // Ensure lists are retrieved as arrays
  const chapter2List = t.raw("chapter2_list", { returnObjects: true }) || [];
  const chapter3List = t.raw("chapter3_list", { returnObjects: true }) || [];

  return (
    <div className="max-w-4xl mx-auto p-6 text-gray-800 leading-relaxed">
      <header className="mb-8">
        <h1 className="text-4xl font-bold text-center">{t("title")}</h1>
        <p className="text-sm text-center text-gray-600 mt-2">
          {t("last_updated")}
        </p>
      </header>

      {/* 1. Introduction */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter1_title")}</h2>
        <p className="mb-4">{t("chapter1_content")}</p>
      </section>

      {/* 2. Information We Collect */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter2_title")}</h2>
        <p className="mb-4">{t("chapter2_intro")}</p>
        {chapter2List.length > 0 ? (
          <ul className="list-disc ml-6 mb-4">
            {chapter2List.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        ) : (
          <p className="text-red-600">[Error: No list items found]</p>
        )}
      </section>

      {/* 3. How We Use Your Information */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter3_title")}</h2>
        <p className="mb-4">{t("chapter3_intro")}</p>
        {chapter3List.length > 0 ? (
          <ul className="list-disc ml-6 mb-4">
            {chapter3List.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        ) : (
          <p className="text-red-600">[Error: No list items found]</p>
        )}
      </section>

      {/* 4. Sharing Your Information */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter4_title")}</h2>
        <p className="mb-4">{t("chapter4_content")}</p>
      </section>

      {/* 5. Cookies and Tracking */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter5_title")}</h2>
        <p className="mb-4">{t("chapter5_content")}</p>
      </section>

      {/* 6. Security of Your Data */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter6_title")}</h2>
        <p className="mb-4">{t("chapter6_content")}</p>
      </section>

      {/* 7. Your Rights and Choices */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter7_title")}</h2>
        <p className="mb-4">{t("chapter7_content")}</p>
      </section>

      {/* 8. Changes to This Policy */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter8_title")}</h2>
        <p className="mb-4">{t("chapter8_content")}</p>
      </section>

      {/* 9. Contact Information */}
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-2">{t("chapter9_title")}</h2>
        <p>
          {t("chapter9_content")}{" "}
          <a href="mailto:<EMAIL>" className="text-blue-600 underline">
            {t("contact_email")}
          </a>{" "}
          {t("call_us")}
        </p>
      </section>
    </div>
  );
};

export default PrivacyPolicy;
