/**
 * Socket.IO service for handling real-time notifications
 */
const Notification = require('../models/Notification'); // Import the Notification model

// Store connected sockets
const adminSockets = new Map();
const userSockets = new Map();

// Cache for the socket service instance
let socketServiceInstance = null;

/**
 * Initialize the Socket.IO service
 * @param {Object} io - Socket.IO server instance
 * @returns {Object} - Socket service methods
 */
module.exports = function(io) {
  // Return cached instance if it exists
  if (socketServiceInstance) {
    return socketServiceInstance;
  }

  // Handle socket connections
  io.on('connection', (socket) => {
    console.log(`[SOCKET DEBUG] Socket connected: ${socket.id}`);
    console.log(`[SOCKET DEBUG] Socket handshake query:`, socket.handshake.query);
    console.log(`[SOCKET DEBUG] Current admin sockets count: ${adminSockets.size}`);
    console.log(`[SOCKET DEBUG] Current user sockets count: ${userSockets.size}`);

    // Handle admin room joining
    socket.on('join-admin', () => {
      console.log(`[SOCKET DEBUG] Socket ${socket.id} joining admin room...`);

      // Get admin ID from query if available
      const adminId = socket.handshake.query.adminId || 'unknown';
      console.log(`[SOCKET DEBUG] Admin ID from query: ${adminId}`);

      // Join the room
      socket.join('admin-room');
      adminSockets.set(socket.id, socket);

      // Get updated room size
      const roomSize = io.sockets.adapter.rooms.get('admin-room')?.size || 0;
      console.log(`[SOCKET DEBUG] Admin room members count: ${adminSockets.size} (room size: ${roomSize})`);

      // Log all rooms this socket is in
      const rooms = Array.from(socket.rooms);
      console.log(`[SOCKET DEBUG] Socket ${socket.id} is in rooms:`, rooms);

      // Log all sockets in the admin room
      const socketsInRoom = io.sockets.adapter.rooms.get('admin-room');
      if (socketsInRoom) {
        console.log(`[SOCKET DEBUG] Sockets in admin-room:`, Array.from(socketsInRoom));
      }

      // Send a confirmation back to the client
      socket.emit('admin-room-joined', {
        success: true,
        socketId: socket.id,
        roomSize: roomSize
      });

      console.log(`[SOCKET DEBUG] Socket ${socket.id} successfully joined admin room`);
    });

    // Handle user room joining
    socket.on('join-user', () => {
      console.log(`[SOCKET DEBUG] Socket ${socket.id} joining user room...`);

      // Get user ID from query if available
      const userId = socket.handshake.query.userId;
      if (!userId) {
        console.log(`[SOCKET DEBUG] No userId provided, cannot join user room`);
        socket.emit('user-room-joined', {
          success: false,
          error: 'No userId provided'
        });
        return;
      }

      console.log(`[SOCKET DEBUG] User ID from query: ${userId}`);

      // Create and join the user-specific room
      const userRoom = `user-${userId}`;
      socket.join(userRoom);
      userSockets.set(socket.id, { socket, userId });

      // Get updated room size
      const roomSize = io.sockets.adapter.rooms.get(userRoom)?.size || 0;
      console.log(`[SOCKET DEBUG] User room ${userRoom} members count: ${roomSize}`);

      // Log all rooms this socket is in
      const rooms = Array.from(socket.rooms);
      console.log(`[SOCKET DEBUG] Socket ${socket.id} is in rooms:`, rooms);

      // Send a confirmation back to the client
      socket.emit('user-room-joined', {
        success: true,
        socketId: socket.id,
        userId: userId,
        roomSize: roomSize
      });

      console.log(`[SOCKET DEBUG] Socket ${socket.id} successfully joined user room ${userRoom}`);
    });

    // Handle ping/pong for connection testing
    socket.on('ping', (data) => {
      console.log(`[SOCKET DEBUG] Received ping from socket ${socket.id}:`, data);

      // Send pong response back to the client
      socket.emit('pong', {
        serverTime: new Date().toISOString(),
        receivedPing: data,
        socketId: socket.id,
        inAdminRoom: adminSockets.has(socket.id),
        message: 'Socket connection is working correctly!'
      });

      console.log(`[SOCKET DEBUG] Sent pong response to socket ${socket.id}`);
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`[SOCKET DEBUG] Socket disconnected: ${socket.id}, reason: ${reason}`);

      // Clean up admin sockets
      if (adminSockets.has(socket.id)) {
        adminSockets.delete(socket.id);
        console.log(`[SOCKET DEBUG] Removed from admin sockets. Remaining count: ${adminSockets.size}`);
      }

      // Clean up user sockets
      if (userSockets.has(socket.id)) {
        const userData = userSockets.get(socket.id);
        userSockets.delete(socket.id);
        console.log(`[SOCKET DEBUG] Removed from user sockets for user ${userData.userId}. Remaining count: ${userSockets.size}`);
      }
    });

    // Log connection errors
    socket.on('error', (error) => {
      console.error(`[SOCKET DEBUG] Socket error for ${socket.id}:`, error);
    });
  });

  /**
   * Send a notification to all connected admin users and save it to the database
   * @param {Object} notificationData - Notification data
   */
  const notifyAdmins = async (notificationData) => {
    try {
      // Ensure notificationData includes userType (admin in this case)
      const notificationToSave = {
        ...notificationData,
        userType: 'admin', // Assuming these are admin notifications
        // adminId can be set if a specific admin is targeted or if it's a general admin notification
      };

      // Explicitly remove identifier if it exists to prevent unique constraint errors
      // if an 'identifier' field is not part of the Mongoose schema but is causing issues.
      if (notificationToSave.hasOwnProperty('identifier')) {
        delete notificationToSave.identifier;
      }

      const newNotification = new Notification(notificationToSave);
      await newNotification.save();

      // Get count of clients in the admin room
      const roomSize = io.sockets.adapter.rooms.get('admin-room')?.size || 0;

      console.log(`[SOCKET DEBUG] Preparing to send notification to admin room (${roomSize} clients)`);
      console.log(`[SOCKET DEBUG] Notification details:`, {
        id: newNotification._id,
        type: newNotification.type,
        title: newNotification.title,
        userType: newNotification.userType,
        adminId: newNotification.adminId || 'none',
        createdAt: newNotification.createdAt
      });

      // Emit the notification to the admin room
      io.to('admin-room').emit('notification', newNotification);

      console.log(`[SOCKET DEBUG] Notification emitted to admin room`);
      console.log(`[SOCKET DEBUG] Full notification data: ${JSON.stringify(newNotification)}`);
    } catch (error) {
      console.error('Error saving or sending notification:', error);
    }
  };

  /**
   * Send a notification to a specific user and save it to the database
   * @param {Object} notificationData - Notification data
   * @param {String} userId - User ID to send notification to
   */
  const notifyUser = async (notificationData, userId) => {
    try {
      if (!userId) {
        console.log(`[SOCKET DEBUG] No userId provided for user notification, skipping`);
        return;
      }

      // Ensure notificationData includes userType and userId
      const notificationToSave = {
        ...notificationData,
        userType: 'client', // These are client notifications
        userId: userId, // Set the userId for the notification
      };

      // Explicitly remove identifier if it exists to prevent unique constraint errors
      if (notificationToSave.hasOwnProperty('identifier')) {
        delete notificationToSave.identifier;
      }

      const newNotification = new Notification(notificationToSave);
      await newNotification.save();

      // Get count of clients in the user's room
      const userRoom = `user-${userId}`;
      const roomSize = io.sockets.adapter.rooms.get(userRoom)?.size || 0;

      console.log(`[SOCKET DEBUG] Preparing to send notification to user room ${userRoom} (${roomSize} clients)`);
      console.log(`[SOCKET DEBUG] User notification details:`, {
        id: newNotification._id,
        type: newNotification.type,
        title: newNotification.title,
        userType: newNotification.userType,
        userId: newNotification.userId,
        createdAt: newNotification.createdAt
      });

      // Emit the notification to the user's room
      io.to(userRoom).emit('notification', newNotification);

      console.log(`[SOCKET DEBUG] Notification emitted to user room ${userRoom}`);
      console.log(`[SOCKET DEBUG] Full user notification data: ${JSON.stringify(newNotification)}`);
    } catch (error) {
      console.error('Error saving or sending user notification:', error);
    }
  };


  /**
   * Send a notification about a new ticket
   * @param {Object} ticket - Ticket data
   */
  const notifyNewTicket = (ticket, adminId = null) => { // adminId can be passed if known
    console.log(`[SOCKET DEBUG] Creating new ticket notification for ticket ID: ${ticket._id}`);
    console.log(`[SOCKET DEBUG] Ticket details:`, {
      subject: ticket.subject,
      identifiant: ticket.identifiant,
      creator: ticket.creator,
      status: ticket.status,
      priority: ticket.priority
    });

    // Get creator information if available
    const creator = ticket.creator || {};

    const notification = {
      // id will be generated by MongoDB
      type: 'new_ticket',
      title: 'New Ticket Created',
      message: `New ticket "${ticket.subject}" (ID: ${ticket.identifiant}) has been created.`,
      link: `/admin/tickets/${ticket._id}`, // Example link
      adminId, // Assign if a specific admin is associated or if it's a general admin pool
      // Add creator information as the action performer
      actionBy: creator && typeof creator !== 'string' ? {
        userId: creator._id,
        firstName: creator.firstName || '',
        lastName: creator.lastName || '',
        role: creator.role || ''
      } : null,
      // userId would be for client notifications
      // userType will be set in notifyAdmins
      // isRead defaults to false
      // createdAt defaults to Date.now()
    };

    console.log(`[SOCKET DEBUG] Calling notifyAdmins with new ticket notification`);
    notifyAdmins(notification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for new ticket`);
  };

  /**
   * Send a notification about a ticket status update
   * @param {Object} ticket - Updated ticket data
   * @param {String} previousStatus - Previous ticket status
   */
  const notifyTicketStatusUpdate = (ticket, previousStatus, adminId = null, user = null) => { // adminId and user can be passed
    console.log(`[SOCKET DEBUG] Creating ticket status update notification`);
    console.log(`[SOCKET DEBUG] Status change details:`, {
      ticketId: ticket._id,
      subject: ticket.subject,
      identifiant: ticket.identifiant,
      previousStatus,
      newStatus: ticket.status,
      adminId: adminId || 'none',
      updatedBy: user ? `${user.firstName} ${user.lastName}` : 'Unknown admin'
    });

    const notification = {
      type: 'ticket_status_update',
      title: 'Ticket Status Updated',
      message: `Status of ticket "${ticket.subject}" (ID: ${ticket.identifiant}) changed from ${previousStatus} to ${ticket.status}.`,
      link: `/admin/tickets/${ticket._id}`, // Example link
      adminId,
      // Add user information who performed the action
      actionBy: user ? {
        userId: user._id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || ''
      } : null,
      // userType will be set in notifyAdmins
    };

    console.log(`[SOCKET DEBUG] Calling notifyAdmins with status update notification`);
    notifyAdmins(notification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for status update`);
  };

  /**
   * Send a notification about a ticket update by a user
   * @param {Object} ticket - Updated ticket data
   * @param {Object} user - User who updated the ticket
   */
  const notifyTicketUpdated = (ticket, user, adminId = null) => {
    console.log(`[SOCKET DEBUG] Creating ticket update notification`);
    console.log(`[SOCKET DEBUG] Update details:`, {
      ticketId: ticket._id,
      subject: ticket.subject,
      identifiant: ticket.identifiant,
      updatedBy: user ? `${user.firstName} ${user.lastName}` : 'Unknown user',
    });

    const notification = {
      type: 'ticket_updated',
      title: 'Ticket Updated',
      message: `Ticket "${ticket.subject}" (ID: ${ticket.identifiant}) was updated.`,
      link: `/admin/tickets/${ticket._id}`,
      adminId,
      // Add user information who performed the action
      actionBy: user ? {
        userId: user._id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || ''
      } : null
    };

    console.log(`[SOCKET DEBUG] Calling notifyAdmins with ticket update notification`);
    notifyAdmins(notification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for ticket update`);
  };

  /**
   * Send a notification about a ticket deletion by a user
   * @param {Object} ticket - Deleted ticket data
   * @param {Object} user - User who deleted the ticket
   */
  const notifyTicketDeleted = (ticket, user, adminId = null) => {
    console.log(`[SOCKET DEBUG] Creating ticket deletion notification`);
    console.log(`[SOCKET DEBUG] Deletion details:`, {
      ticketId: ticket._id,
      subject: ticket.subject,
      identifiant: ticket.identifiant,
      deletedBy: user ? `${user.firstName} ${user.lastName}` : 'Unknown user',
    });

    const notification = {
      type: 'ticket_deleted',
      title: 'Ticket Deleted',
      message: `Ticket "${ticket.subject}" (ID: ${ticket.identifiant}) was deleted.`,
      link: `/admin/tickets`, // Link to tickets list since the specific ticket is deleted
      adminId,
      // Add user information who performed the action
      actionBy: user ? {
        userId: user._id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || ''
      } : null
    };

    console.log(`[SOCKET DEBUG] Calling notifyAdmins with ticket deletion notification`);
    notifyAdmins(notification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for ticket deletion`);
  };

  /**
   * Send a notification to a user about their ticket status update
   * @param {Object} ticket - Updated ticket data
   * @param {String} previousStatus - Previous ticket status
   * @param {Object} user - Admin who updated the ticket
   */
  const notifyTicketStatusUpdateToUser = (ticket, previousStatus, user = null) => {
    if (!ticket.creator || !ticket.creator._id) {
      console.log(`[SOCKET DEBUG] No creator found for ticket ${ticket._id}, skipping user notification`);
      return;
    }

    console.log(`[SOCKET DEBUG] Creating ticket status update notification for user`);
    console.log(`[SOCKET DEBUG] User notification details:`, {
      ticketId: ticket._id,
      subject: ticket.subject,
      identifiant: ticket.identifiant,
      previousStatus,
      newStatus: ticket.status,
      userId: ticket.creator._id,
      updatedBy: user ? `${user.firstName} ${user.lastName}` : 'Admin'
    });

    // Create a user-friendly status message
    let statusMessage = '';
    switch(ticket.status) {
      case 'in_progress':
        statusMessage = 'Our team is now working on your ticket.';
        break;
      case 'resolved':
        statusMessage = 'Your ticket has been resolved. Please check the resolution details.';
        break;
      case 'closed':
        statusMessage = 'Your ticket has been closed.';
        break;
      default:
        statusMessage = `Status changed from ${previousStatus} to ${ticket.status}.`;
    }

    const notification = {
      type: 'ticket_status_update',
      title: 'Your Ticket Status Updated',
      message: `Your ticket "${ticket.subject}" (ID: ${ticket.identifiant}) has been updated. ${statusMessage}`,
      link: `/client/support/tickets/${ticket._id}`, // Fixed: Link to correct client ticket view
      // Add admin information who performed the action
      actionBy: user ? {
        userId: user._id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || ''
      } : null
    };

    console.log(`[SOCKET DEBUG] Calling notifyUser with ticket status update notification`);
    notifyUser(notification, ticket.creator._id);
    console.log(`[SOCKET DEBUG] notifyUser called successfully for ticket status update`);
  };

  /**
   * Send a notification about a new order
   * @param {Object} order - Order data
   */
  const notifyNewOrder = async (order) => {
    console.log(`[SOCKET DEBUG] Creating new order notification for order ID: ${order._id}`);

    // Create notification for admins
    const notification = {
      type: 'order_update',
      title: 'New Order Created',
      message: `New order #${order.identifiant} has been created.`,
      link: `/admin/orders/${order._id}`,
      // Include user information who created the order
      actionBy: order.user && typeof order.user !== 'string' ? {
        userId: order.user._id,
        firstName: order.user.firstName || '',
        lastName: order.user.lastName || '',
      } : null
    };

    console.log(`[SOCKET DEBUG] Calling notifyAdmins with new order notification`);
    notifyAdmins(notification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for new order`);
  };

  /**
   * Send a notification about an order status update
   * @param {Object} order - Updated order data
   * @param {String} previousStatus - Previous order status
   * @param {Object} admin - Admin who updated the order
   */
  const notifyOrderStatusUpdate = async (order, previousStatus, admin = null) => {
    console.log(`[SOCKET DEBUG] Creating order status update notification`);
    console.log(`[SOCKET DEBUG] Order details:`, {
      orderId: order._id,
      identifiant: order.identifiant,
      previousStatus,
      newStatus: order.status,
      userId: order.user?._id || 'unknown',
      updatedBy: admin ? `${admin.firstName} ${admin.lastName}` : 'Unknown admin'
    });

    // Notification for admins only
    const adminNotification = {
      type: 'order_update',
      title: 'Order Status Updated',
      message: `Order #${order.identifiant} status has changed from ${previousStatus} to ${order.status}.`,
      link: `/admin/orders/${order._id}`,
      actionBy: admin ? {
        userId: admin._id,
        firstName: admin.firstName || '',
        lastName: admin.lastName || '',
        role: admin.role || ''
      } : null
    };

    // Only notify admins about the order status change (not users)
    console.log(`[SOCKET DEBUG] Calling notifyAdmins with order status update notification`);
    notifyAdmins(adminNotification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for order status update`);
    console.log(`[SOCKET DEBUG] Not notifying user about parent order status change as per requirements`);
  };

  /**
   * Send a notification about a suborder status update
   * @param {Object} order - Parent order
   * @param {Object} suborder - Updated suborder
   * @param {String} previousStatus - Previous suborder status
   * @param {Object} admin - Admin who updated the suborder
   */
  const notifySubOrderStatusUpdate = async (order, suborder, previousStatus, admin = null) => {
    console.log(`[SOCKET DEBUG] Creating suborder status update notification`);
    console.log(`[SOCKET DEBUG] Suborder details:`, {
      suborderId: suborder._id,
      orderId: order._id,
      orderIdentifiant: order.identifiant,
      packageName: suborder.package?.name || 'Package',
      categoryName: suborder.package?.brand?.category?.name || 'Unknown',
      previousStatus,
      newStatus: suborder.status,
      userId: order.user?._id || 'unknown',
      updatedBy: admin ? `${admin.firstName} ${admin.lastName}` : 'Unknown admin'
    });

    // Get package name if available
    const packageName = suborder.package?.name || 'Package';

    // Determine notification type based on category or brand name as fallback
    const categoryName = suborder.package?.brand?.category?.name;
    const brandName = suborder.package?.brand?.name;
    let notificationType = 'order_update'; // Default fallback

    console.log(`[SOCKET DEBUG] 🔍 Category detection:`, {
      packageName: suborder.package?.name,
      brandName: brandName,
      categoryName: categoryName,
      fullPackageStructure: suborder.package
    });

    // Enhanced debugging for web development packages
    if (suborder.package?.name && (
      suborder.package.name.toLowerCase().includes('web') ||
      suborder.package.name.toLowerCase().includes('site') ||
      suborder.package.name.toLowerCase().includes('showcase') ||
      suborder.package.name.toLowerCase().includes('dynamic') ||
      suborder.package.name.toLowerCase().includes('e-commerce')
    )) {
      console.log(`[SOCKET DEBUG] 🌐 WEB DEV PACKAGE DETECTED!`);
      console.log(`[SOCKET DEBUG] 🌐 Package name: "${suborder.package?.name}"`);
      console.log(`[SOCKET DEBUG] 🌐 Brand name: "${brandName}"`);
      console.log(`[SOCKET DEBUG] 🌐 Category name: "${categoryName}"`);
      console.log(`[SOCKET DEBUG] 🌐 Full brand object:`, suborder.package?.brand);
      console.log(`[SOCKET DEBUG] 🌐 Full category object:`, suborder.package?.brand?.category);
    }

    // Map category names and brand names to notification types for flexible routing
    const categoryToNotificationTypeMap = {
      'SSL': 'ssl_update',
      'Hosting': 'hosting_update',
      'web creation': 'webdev_update'
    };

    // Brand name fallback mapping (when category is null/undefined)
    const brandToNotificationTypeMap = {
      'web creation': 'webdev_update'
    };

    console.log(`[SOCKET DEBUG] 🗺️ Available category mappings:`, categoryToNotificationTypeMap);
    console.log(`[SOCKET DEBUG] 🗺️ Available brand mappings:`, brandToNotificationTypeMap);
    console.log(`[SOCKET DEBUG] 🔍 Looking for category: "${categoryName}"`);
    console.log(`[SOCKET DEBUG] 🔍 Looking for brand: "${brandName}"`);

    // First try to match by category name
    if (categoryName && categoryToNotificationTypeMap[categoryName]) {
      notificationType = categoryToNotificationTypeMap[categoryName];
      console.log(`[SOCKET DEBUG] ✅ Category "${categoryName}" mapped to notification type: ${notificationType}`);
    }
    // Fallback to brand name if category is not available
    else if (brandName && brandToNotificationTypeMap[brandName]) {
      notificationType = brandToNotificationTypeMap[brandName];
      console.log(`[SOCKET DEBUG] ✅ Brand "${brandName}" mapped to notification type: ${notificationType}`);
    }
    else {
      console.log(`[SOCKET DEBUG] ⚠️ Neither category "${categoryName}" nor brand "${brandName}" found in mapping, using default: ${notificationType}`);
      console.log(`[SOCKET DEBUG] ⚠️ Available categories in map:`, Object.keys(categoryToNotificationTypeMap));
      console.log(`[SOCKET DEBUG] ⚠️ Available brands in map:`, Object.keys(brandToNotificationTypeMap));
    }

    console.log(`[SOCKET DEBUG] 🎯 Final notification type: ${notificationType}`);

    // Notification for the user
    const userNotification = {
      type: notificationType,
      title: 'Your Order Item Status Updated',
      message: `Status for ${packageName} in your order #${order.identifiant} has changed from ${previousStatus} to ${suborder.status}.`,
      link: `/orders/${order._id}`,
      actionBy: admin ? {
        userId: admin._id,
        firstName: admin.firstName || '',
        lastName: admin.lastName || '',
        role: admin.role || ''
      } : null
    };

    // Notification for admins
    const adminNotification = {
      type: notificationType,
      title: 'Order Item Status Updated',
      message: `Status for ${packageName} in order #${order.identifiant} has changed from ${previousStatus} to ${suborder.status}.`,
      link: `/admin/orders/${order._id}`,
      actionBy: admin ? {
        userId: admin._id,
        firstName: admin.firstName || '',
        lastName: admin.lastName || '',
        role: admin.role || ''
      } : null
    };

    // Notify the user who placed the order
    if (order.user && typeof order.user !== 'string') {
      console.log(`[SOCKET DEBUG] Calling notifyUser with suborder status update notification for user ${order.user._id}`);
      notifyUser(userNotification, order.user._id);
      console.log(`[SOCKET DEBUG] notifyUser called successfully for suborder status update`);
    }

    // Also notify all admins about the suborder status change
    console.log(`[SOCKET DEBUG] Calling notifyAdmins with suborder status update notification`);
    notifyAdmins(adminNotification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for suborder status update`);
  };

  /**
   * Send a notification about an SSL certificate status update
   * @param {Object} order - Parent order
   * @param {Object} suborder - Parent suborder
   * @param {Object} certificate - Updated certificate
   * @param {String} previousStatus - Previous certificate status
   * @param {Object} admin - Admin who updated the certificate
   */
  const notifySSLCertificateStatusUpdate = async (order, suborder, certificate, previousStatus, admin = null) => {
    console.log(`[SOCKET DEBUG] Creating SSL certificate status update notification`);
    console.log(`[SOCKET DEBUG] SSL certificate details:`, {
      orderId: order._id,
      orderIdentifiant: order.identifiant,
      domain: certificate.domain,
      previousStatus,
      newStatus: certificate.status,
      userId: order.user?._id || 'unknown',
      updatedBy: admin ? `${admin.firstName} ${admin.lastName}` : 'Unknown admin'
    });

    // Notification for the user
    const userNotification = {
      type: 'ssl_update', // Using ssl_update type for SSL certificate updates
      title: 'SSL Certificate Status Updated',
      message: `Your SSL certificate for ${certificate.domain} has been updated from ${previousStatus} to ${certificate.status}.`,
      link: `/orders/${order._id}`,
      actionBy: admin ? {
        userId: admin._id,
        firstName: admin.firstName || '',
        lastName: admin.lastName || '',
        role: admin.role || ''
      } : null
    };

    // Notification for admins
    const adminNotification = {
      type: 'ssl_update',
      title: 'SSL Certificate Status Updated',
      message: `SSL certificate for ${certificate.domain} (Order #${order.identifiant}) has been updated from ${previousStatus} to ${certificate.status}.`,
      link: `/admin/orders/${order._id}`,
      actionBy: admin ? {
        userId: admin._id,
        firstName: admin.firstName || '',
        lastName: admin.lastName || '',
        role: admin.role || ''
      } : null
    };

    // Notify the user who placed the order
    if (order.user && typeof order.user !== 'string') {
      console.log(`[SOCKET DEBUG] Calling notifyUser with SSL certificate status update notification for user ${order.user._id}`);
      notifyUser(userNotification, order.user._id);
      console.log(`[SOCKET DEBUG] notifyUser called successfully for SSL certificate status update`);
    }

    // Also notify all admins about the SSL certificate status change
    console.log(`[SOCKET DEBUG] Calling notifyAdmins with SSL certificate status update notification`);
    notifyAdmins(adminNotification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for SSL certificate status update`);
  };

  /**
   * Send a notification about a payment confirmation
   * @param {Object} order - Order data
   * @param {Object} payment - Payment data
   */
  const notifyPaymentConfirmation = async (order, payment) => {
    console.log(`[SOCKET DEBUG] Creating payment confirmation notification for order ID: ${order._id}`);

    // Notification for the user
    const userNotification = {
      type: 'order_update',
      title: 'Payment Confirmed',
      message: `Your payment for order #${order.identifiant} has been confirmed.`,
      link: `/orders/${order._id}`,
    };

    // Notification for admins
    const adminNotification = {
      type: 'order_update',
      title: 'Payment Received',
      message: `Payment received for order #${order.identifiant}.`,
      link: `/admin/orders/${order._id}`,
      actionBy: order.user && typeof order.user !== 'string' ? {
        userId: order.user._id,
        firstName: order.user.firstName || '',
        lastName: order.user.lastName || '',
      } : null
    };

    // Notify the user who placed the order
    if (order.user && typeof order.user !== 'string') {
      console.log(`[SOCKET DEBUG] Calling notifyUser with payment confirmation notification for user ${order.user._id}`);
      notifyUser(userNotification, order.user._id);
      console.log(`[SOCKET DEBUG] notifyUser called successfully for payment confirmation`);
    }

    // Notify admins
    console.log(`[SOCKET DEBUG] Calling notifyAdmins with payment confirmation notification`);
    notifyAdmins(adminNotification);
    console.log(`[SOCKET DEBUG] notifyAdmins called successfully for payment confirmation`);
  };

  /**
   * Send a notification about a new user registration
   * @param {Object} user - User data
   */
  const notifyUserRegistration = (user) => {
    console.log(`[SOCKET DEBUG] Creating new user registration notification for user: ${user._id}`);

    // Notification for the user (welcome message)
    const userNotification = {
      type: 'user_registered',
      title: 'Welcome to ZTech Engineering',
      message: 'Thank you for registering. Please verify your email to activate your account.',
      link: '/dashboard'
    };

    // Notification for admins about new user
    const adminNotification = {
      type: 'user_registered',
      title: 'New User Registration',
      message: `New user ${user.firstName} ${user.lastName} (${user.email}) has registered.`,
      link: `/admin/users/${user._id}`,
      actionBy: {
        userId: user._id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || ''
      }
    };

    // Send notifications
    notifyUser(userNotification, user._id);
    notifyAdmins(adminNotification);
  };

  /**
   * Send a notification when a user verifies their account
   * @param {Object} user - User data
   */
  const notifyUserVerification = (user) => {
    console.log(`[SOCKET DEBUG] Creating user verification notification for user: ${user._id}`);

    // Notification for the user (verification confirmation)
    const userNotification = {
      type: 'user_verified',
      title: 'Account Verified Successfully',
      message: 'Your email has been verified. You now have full access to all features.',
      link: '/dashboard'
    };

    // Notification for admins about user verification
    const adminNotification = {
      type: 'user_verified',
      title: 'User Account Verified',
      message: `User ${user.firstName} ${user.lastName} (${user.email}) has verified their account.`,
      link: `/admin/users/${user._id}`,
      actionBy: {
        userId: user._id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        role: user.role || ''
      }
    };

    // Send notifications
    notifyUser(userNotification, user._id);
    notifyAdmins(adminNotification);
  };

  // Create the service instance
  socketServiceInstance = {
    notifyNewTicket,
    notifyTicketStatusUpdate,
    notifyTicketUpdated,
    notifyTicketDeleted,
    notifyAdmins,
    notifyUser,
    notifyTicketStatusUpdateToUser,
    // Add the new order notification functions
    notifyNewOrder,
    notifyOrderStatusUpdate,
    notifySubOrderStatusUpdate,
    notifySSLCertificateStatusUpdate,
    notifyPaymentConfirmation,
    // Add user registration notification functions
    notifyUserRegistration,
    notifyUserVerification
  };

  // Return public methods
  return socketServiceInstance;
};
