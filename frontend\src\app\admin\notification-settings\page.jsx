"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../../context/AuthContext";
import notificationSettingsService from "../../services/notificationSettingsService";
import {
  createCustomNotification,
  getCustomNotifications,
  updateCustomNotification,
  deleteCustomNotification,
  cancelCustomNotification,
} from "../../services/customNotificationService";
import {
  Loader2,
  Save,
  Play,
  AlertCircle,
  Edit,
  Trash2,
  X,
  Eye,
  Calendar,
  Users,
} from "lucide-react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./styles.css";

const NotificationSettingsPage = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [triggering, setTriggering] = useState(false);
  const [settings, setSettings] = useState(null);
  const [vpsSettings, setVpsSettings] = useState(null);
  const [vpsBillingSettings, setVpsBillingSettings] = useState(null);
  const [vpsScheduleConfig, setVpsScheduleConfig] = useState({
    frequency: "daily", // daily, weekly, custom
    scheduledDate: "",
    scheduledTime: "02:00", // Default to 2 AM
  });
  const [vpsBillingScheduleConfig, setVpsBillingScheduleConfig] = useState({
    frequency: "daily", // daily, weekly, custom
    scheduledDate: "",
    scheduledTime: "09:00", // Default to 9 AM
  });
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("abandoned-carts");
  // Fetch notification settings on component mount
  useEffect(() => {
    if (!authLoading) {
      if (!user) {
        router.push("/auth/login");
        return;
      }

      if (user && user.role !== "ADMIN") {
        router.push("/404");
        return;
      }

      fetchSettings();
    }
  }, [user, authLoading, router]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch abandoned cart, VPS scraping, and VPS billing settings
      const [abandonedCartResponse, vpsResponse, vpsBillingResponse] =
        await Promise.all([
          notificationSettingsService.getNotificationSettingByType(
            "abandoned_cart"
          ),
          notificationSettingsService.getNotificationSettingByType(
            "vps_scraping"
          ),
          notificationSettingsService.getNotificationSettingByType(
            "vps_billing"
          ),
        ]);

      setSettings(abandonedCartResponse.data);
      setVpsSettings(vpsResponse.data);
      setVpsBillingSettings(vpsBillingResponse.data);
    } catch (err) {
      console.error("Error fetching notification settings:", err);
      setError("Failed to load notification settings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    setSettings((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleVpsInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    setVpsSettings((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  // Update VPS schedule and generate cron expression
  const updateVpsSchedule = (newScheduleConfig) => {
    setVpsScheduleConfig(newScheduleConfig);

    // Generate cron expression from the schedule config
    let cronExpression;

    if (newScheduleConfig.frequency === "daily") {
      // Daily at specified time
      const [hours, minutes] = newScheduleConfig.scheduledTime.split(":");
      cronExpression = `${minutes} ${hours} * * *`;
    } else if (newScheduleConfig.frequency === "weekly") {
      // Weekly on Monday at specified time
      const [hours, minutes] = newScheduleConfig.scheduledTime.split(":");
      cronExpression = `${minutes} ${hours} * * 1`;
    } else if (
      newScheduleConfig.frequency === "custom" &&
      newScheduleConfig.scheduledDate &&
      newScheduleConfig.scheduledTime
    ) {
      // One-time custom schedule
      const scheduleDate = new Date(
        `${newScheduleConfig.scheduledDate}T${newScheduleConfig.scheduledTime}`
      );
      const minutes = scheduleDate.getMinutes();
      const hours = scheduleDate.getHours();
      const day = scheduleDate.getDate();
      const month = scheduleDate.getMonth() + 1;
      cronExpression = `${minutes} ${hours} ${day} ${month} *`;
    } else {
      // Default to daily at 2 AM
      cronExpression = "0 2 * * *";
    }

    // Update VPS settings with new cron schedule
    setVpsSettings((prev) => ({
      ...prev,
      cronSchedule: cronExpression,
    }));
  };

  // Update VPS billing schedule and generate cron expression
  const updateVpsBillingSchedule = (newScheduleConfig) => {
    setVpsBillingScheduleConfig(newScheduleConfig);

    // Generate cron expression from the schedule config
    let cronExpression;

    if (newScheduleConfig.frequency === "daily") {
      // Daily at specified time
      const [hours, minutes] = newScheduleConfig.scheduledTime.split(":");
      cronExpression = `${minutes} ${hours} * * *`;
    } else if (newScheduleConfig.frequency === "weekly") {
      // Weekly on Monday at specified time
      const [hours, minutes] = newScheduleConfig.scheduledTime.split(":");
      cronExpression = `${minutes} ${hours} * * 1`;
    } else if (
      newScheduleConfig.frequency === "custom" &&
      newScheduleConfig.scheduledDate &&
      newScheduleConfig.scheduledTime
    ) {
      // One-time custom schedule
      const scheduleDate = new Date(
        `${newScheduleConfig.scheduledDate}T${newScheduleConfig.scheduledTime}`
      );
      const minutes = scheduleDate.getMinutes();
      const hours = scheduleDate.getHours();
      const day = scheduleDate.getDate();
      const month = scheduleDate.getMonth() + 1;
      cronExpression = `${minutes} ${hours} ${day} ${month} *`;
    } else {
      // Default to daily at 9 AM
      cronExpression = "0 9 * * *";
    }

    // Update VPS billing settings with new cron schedule
    setVpsBillingSettings((prev) => ({
      ...prev,
      cronSchedule: cronExpression,
    }));
  };

  const handleNumberChange = (e) => {
    const { name, value } = e.target;

    // Convert string to number and ensure it's not negative
    const numValue = Math.max(0, parseInt(value) || 0);

    setSettings((prev) => ({
      ...prev,
      [name]: numValue,
    }));
  };

  // State for time threshold components
  const [timeConfig, setTimeConfig] = useState({
    value: 1,
    unit: "hours", // minutes, hours, days
  });

  // State for schedule components
  const [scheduleConfig, setScheduleConfig] = useState({
    frequency: "every", // every, daily, weekly
    interval: 1,
    unit: "hours", // minutes, hours, days
    time: "09:00", // for daily/weekly
  });

  // State for customized notification
  const [customNotification, setCustomNotification] = useState({
    title: "",
    message: "",
    scheduledDate: "",
    scheduledTime: "",
    emailEnabled: true,
    inAppEnabled: true,
    targetAudience: "all", // all, verified, unverified
  });

  const [customNotifications, setCustomNotifications] = useState([]);
  const [loadingCustom, setLoadingCustom] = useState(false);
  const [savingCustom, setSavingCustom] = useState(false);

  // State for managing notifications table and modal
  const [editingNotification, setEditingNotification] = useState(null);
  const [deletingNotification, setDeletingNotification] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [viewingNotification, setViewingNotification] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editFormData, setEditFormData] = useState({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalNotifications, setTotalNotifications] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Convert time threshold to seconds
  const getTimeThresholdInSeconds = () => {
    const multipliers = {
      minutes: 60,
      hours: 3600,
      days: 86400,
    };
    return timeConfig.value * multipliers[timeConfig.unit];
  };

  // Generate cron expression from schedule config
  const generateCronExpression = () => {
    const { frequency, interval, unit, time } = scheduleConfig;

    if (frequency === "every") {
      if (unit === "minutes") {
        return `*/${interval} * * * *`;
      } else if (unit === "hours") {
        return `0 */${interval} * * *`;
      } else if (unit === "days") {
        return `0 0 */${interval} * *`;
      }
    } else if (frequency === "daily") {
      const [hour, minute] = time.split(":");
      return `${minute} ${hour} * * *`;
    } else if (frequency === "weekly") {
      const [hour, minute] = time.split(":");
      return `${minute} ${hour} * * 1`; // Every Monday
    }

    return "0 * * * *"; // Default: every hour
  };

  // Update settings when time config changes
  const updateTimeThreshold = (newTimeConfig) => {
    setTimeConfig(newTimeConfig);
    const multipliers = {
      minutes: 60,
      hours: 3600,
      days: 86400,
    };
    const seconds = newTimeConfig.value * multipliers[newTimeConfig.unit];
    setSettings((prev) => ({ ...prev, timeThreshold: seconds }));
  };

  // Update settings when schedule config changes
  const updateSchedule = (newScheduleConfig) => {
    setScheduleConfig(newScheduleConfig);

    // Generate cron expression from the new config
    const { frequency, interval, unit, time } = newScheduleConfig;
    let cronExpression;

    if (frequency === "every") {
      if (unit === "minutes") {
        cronExpression = `*/${interval} * * * *`;
      } else if (unit === "hours") {
        cronExpression = `0 */${interval} * * *`;
      } else if (unit === "days") {
        cronExpression = `0 0 */${interval} * *`;
      }
    } else if (frequency === "daily") {
      const [hour, minute] = time.split(":");
      cronExpression = `${minute} ${hour} * * *`;
    } else if (frequency === "weekly") {
      const [hour, minute] = time.split(":");
      cronExpression = `${minute} ${hour} * * 1`; // Every Monday
    }

    if (!cronExpression) {
      cronExpression = "0 * * * *"; // Default: every hour
    }

    setSettings((prev) => ({ ...prev, cronSchedule: cronExpression }));
  };

  // Initialize time and schedule configs from settings
  useEffect(() => {
    if (settings) {
      // Initialize time config from timeThreshold
      const threshold = settings.timeThreshold;
      if (threshold >= 86400 && threshold % 86400 === 0) {
        setTimeConfig({ value: threshold / 86400, unit: "days" });
      } else if (threshold >= 3600 && threshold % 3600 === 0) {
        setTimeConfig({ value: threshold / 3600, unit: "hours" });
      } else if (threshold >= 60 && threshold % 60 === 0) {
        setTimeConfig({ value: threshold / 60, unit: "minutes" });
      } else {
        setTimeConfig({
          value: Math.max(1, Math.floor(threshold / 3600)),
          unit: "hours",
        });
      }

      // Initialize schedule config from cronSchedule
      const cron = settings.cronSchedule;
      if (cron === "0 0 * * *") {
        setScheduleConfig({
          frequency: "daily",
          interval: 1,
          unit: "hours",
          time: "00:00",
        });
      } else if (cron === "0 9 * * *") {
        setScheduleConfig({
          frequency: "daily",
          interval: 1,
          unit: "hours",
          time: "09:00",
        });
      } else if (cron.startsWith("*/") && cron.endsWith(" * * * *")) {
        const interval = parseInt(cron.split("/")[1].split(" ")[0]);
        setScheduleConfig({
          frequency: "every",
          interval,
          unit: "minutes",
          time: "09:00",
        });
      } else if (cron.startsWith("0 */") && cron.endsWith(" * * *")) {
        const interval = parseInt(cron.split("*/")[1].split(" ")[0]);
        setScheduleConfig({
          frequency: "every",
          interval,
          unit: "hours",
          time: "09:00",
        });
      } else {
        setScheduleConfig({
          frequency: "every",
          interval: 1,
          unit: "hours",
          time: "09:00",
        });
      }
    }
  }, [settings?.timeThreshold, settings?.cronSchedule]);

  // Initialize VPS schedule config when VPS settings are loaded
  useEffect(() => {
    if (vpsSettings?.cronSchedule) {
      // Parse cron expression to schedule config
      const cron = vpsSettings.cronSchedule;

      if (cron === "0 2 * * *") {
        setVpsScheduleConfig({
          frequency: "daily",
          scheduledDate: "",
          scheduledTime: "02:00",
        });
      } else if (cron.match(/^\d+ \d+ \* \* \*$/)) {
        // Daily pattern: "minutes hours * * *"
        const parts = cron.split(" ");
        const hours = parts[1].padStart(2, "0");
        const minutes = parts[0].padStart(2, "0");
        setVpsScheduleConfig({
          frequency: "daily",
          scheduledDate: "",
          scheduledTime: `${hours}:${minutes}`,
        });
      } else if (cron.match(/^\d+ \d+ \* \* 1$/)) {
        // Weekly pattern: "minutes hours * * 1" (Monday)
        const parts = cron.split(" ");
        const hours = parts[1].padStart(2, "0");
        const minutes = parts[0].padStart(2, "0");
        setVpsScheduleConfig({
          frequency: "weekly",
          scheduledDate: "",
          scheduledTime: `${hours}:${minutes}`,
        });
      } else if (cron.match(/^\d+ \d+ \d+ \d+ \*$/)) {
        // Custom one-time pattern: "minutes hours day month *"
        const parts = cron.split(" ");
        const hours = parts[1].padStart(2, "0");
        const minutes = parts[0].padStart(2, "0");
        const day = parts[2].padStart(2, "0");
        const month = parts[3].padStart(2, "0");

        // Create date string for the date input
        const currentYear = new Date().getFullYear();
        const dateString = `${currentYear}-${month}-${day}`;

        setVpsScheduleConfig({
          frequency: "custom",
          scheduledDate: dateString,
          scheduledTime: `${hours}:${minutes}`,
        });
      } else {
        // Default fallback
        setVpsScheduleConfig({
          frequency: "daily",
          scheduledDate: "",
          scheduledTime: "02:00",
        });
      }
    }
  }, [vpsSettings?.cronSchedule]);

  // Initialize VPS billing schedule config when VPS billing settings are loaded
  useEffect(() => {
    if (vpsBillingSettings?.cronSchedule) {
      // Parse cron expression to schedule config
      const cron = vpsBillingSettings.cronSchedule;

      if (cron === "0 9 * * *") {
        setVpsBillingScheduleConfig({
          frequency: "daily",
          scheduledDate: "",
          scheduledTime: "09:00",
        });
      } else if (cron.match(/^\d+ \d+ \* \* \*$/)) {
        // Daily pattern: "minutes hours * * *"
        const parts = cron.split(" ");
        const hours = parts[1].padStart(2, "0");
        const minutes = parts[0].padStart(2, "0");
        setVpsBillingScheduleConfig({
          frequency: "daily",
          scheduledDate: "",
          scheduledTime: `${hours}:${minutes}`,
        });
      } else if (cron.match(/^\d+ \d+ \* \* 1$/)) {
        // Weekly pattern: "minutes hours * * 1" (Monday)
        const parts = cron.split(" ");
        const hours = parts[1].padStart(2, "0");
        const minutes = parts[0].padStart(2, "0");
        setVpsBillingScheduleConfig({
          frequency: "weekly",
          scheduledDate: "",
          scheduledTime: `${hours}:${minutes}`,
        });
      } else if (cron.match(/^\d+ \d+ \d+ \d+ \*$/)) {
        // Custom one-time pattern: "minutes hours day month *"
        const parts = cron.split(" ");
        const hours = parts[1].padStart(2, "0");
        const minutes = parts[0].padStart(2, "0");
        const day = parts[2].padStart(2, "0");
        const month = parts[3].padStart(2, "0");

        // Create date string for the date input
        const currentYear = new Date().getFullYear();
        const dateString = `${currentYear}-${month}-${day}`;

        setVpsBillingScheduleConfig({
          frequency: "custom",
          scheduledDate: dateString,
          scheduledTime: `${hours}:${minutes}`,
        });
      } else {
        // Default fallback
        setVpsBillingScheduleConfig({
          frequency: "daily",
          scheduledDate: "",
          scheduledTime: "09:00",
        });
      }
    }
  }, [vpsBillingSettings?.cronSchedule]);

  // Handle custom notification input changes
  const handleCustomNotificationChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCustomNotification((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  // Save custom notification
  const handleSaveCustomNotification = async () => {
    try {
      setSavingCustom(true);

      // Validate required fields
      if (
        !customNotification.title ||
        !customNotification.message ||
        !customNotification.scheduledDate ||
        !customNotification.scheduledTime
      ) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Validate that at least one notification channel is enabled
      if (
        !customNotification.emailEnabled &&
        !customNotification.inAppEnabled
      ) {
        toast.error(
          "Please enable at least one notification channel (Email or In-App)"
        );
        return;
      }

      // Combine date and time
      const scheduledDateTime = new Date(
        `${customNotification.scheduledDate}T${customNotification.scheduledTime}`
      );

      // Check if scheduled time is in the future
      if (scheduledDateTime <= new Date()) {
        toast.error("Scheduled time must be in the future");
        return;
      }

      const notificationData = {
        ...customNotification,
        scheduledDateTime: scheduledDateTime.toISOString(),
        type: "custom",
      };

      // Call the API to save the custom notification
      const response = await createCustomNotification(notificationData);

      toast.success("Custom notification scheduled successfully");

      // Reset form
      setCustomNotification({
        title: "",
        message: "",
        scheduledDate: "",
        scheduledTime: "",
        emailEnabled: true,
        inAppEnabled: true,
        targetAudience: "all",
      });

      // Close modal and refresh list
      setShowCreateModal(false);
      fetchCustomNotifications();
    } catch (error) {
      console.error("Error saving custom notification:", error);
      toast.error("Failed to schedule notification");
    } finally {
      setSavingCustom(false);
    }
  };

  // Fetch custom notifications with pagination
  const fetchCustomNotifications = async (
    page = currentPage,
    limit = pageSize
  ) => {
    try {
      setLoadingCustom(true);
      const response = await getCustomNotifications({ page, limit });
      console.log("Pagination Response:", response); // Debug log
      setCustomNotifications(response.data || []);

      // Update pagination state
      if (response.pagination) {
        console.log("Pagination Data:", response.pagination); // Debug log
        setCurrentPage(response.pagination.page);
        setTotalPages(response.pagination.pages);
        setTotalNotifications(response.pagination.total);
      } else {
        console.log("No pagination data in response"); // Debug log
      }
    } catch (error) {
      console.error("Error fetching custom notifications:", error);
      toast.error("Failed to fetch custom notifications");
    } finally {
      setLoadingCustom(false);
    }
  };

  // Handle notification cancellation
  const handleCancelNotification = async (notificationId) => {
    try {
      await cancelCustomNotification(notificationId);
      toast.success("Notification cancelled successfully");
      fetchCustomNotifications();
    } catch (error) {
      console.error("Error cancelling notification:", error);
      toast.error("Failed to cancel notification");
    }
  };

  // Handle notification deletion
  const handleDeleteNotification = async (notificationId) => {
    try {
      await deleteCustomNotification(notificationId);
      toast.success("Notification deleted successfully");
      fetchCustomNotifications();
      setDeletingNotification(null);
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast.error("Failed to delete notification");
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-100 text-blue-800";
      case "sent":
        return "bg-green-100 text-green-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // View/Edit notification handlers
  const handleViewNotification = (notification) => {
    setViewingNotification(notification);
    setIsEditMode(false);
    setEditFormData({
      title: notification.title,
      message: notification.message,
      scheduledDate: new Date(notification.scheduledDateTime)
        .toISOString()
        .split("T")[0],
      scheduledTime: new Date(notification.scheduledDateTime)
        .toTimeString()
        .slice(0, 5),
      targetAudience: notification.targetAudience,
      emailEnabled: notification.emailEnabled,
      inAppEnabled: notification.inAppEnabled,
    });
  };

  const handleEditNotification = () => {
    setIsEditMode(true);
  };

  const handleRescheduleNotification = () => {
    // Set tomorrow as default date and current time + 1 hour as default time
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextHour = new Date();
    nextHour.setHours(nextHour.getHours() + 1);

    setEditFormData({
      title: viewingNotification.title,
      message: viewingNotification.message,
      scheduledDate: tomorrow.toISOString().split("T")[0],
      scheduledTime: nextHour.toTimeString().slice(0, 5),
      targetAudience: viewingNotification.targetAudience,
      emailEnabled: viewingNotification.emailEnabled,
      inAppEnabled: viewingNotification.inAppEnabled,
    });
    setIsEditMode(true);
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
    // Reset form data to original values
    if (viewingNotification) {
      setEditFormData({
        title: viewingNotification.title,
        message: viewingNotification.message,
        scheduledDate: new Date(viewingNotification.scheduledDateTime)
          .toISOString()
          .split("T")[0],
        scheduledTime: new Date(viewingNotification.scheduledDateTime)
          .toTimeString()
          .slice(0, 5),
        targetAudience: viewingNotification.targetAudience,
        emailEnabled: viewingNotification.emailEnabled,
        inAppEnabled: viewingNotification.inAppEnabled,
      });
    }
  };

  const handleEditFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEditFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSaveEdit = async () => {
    try {
      setSavingCustom(true);

      // Validate required fields
      if (
        !editFormData.title ||
        !editFormData.message ||
        !editFormData.scheduledDate ||
        !editFormData.scheduledTime
      ) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Validate that at least one notification channel is enabled
      if (!editFormData.emailEnabled && !editFormData.inAppEnabled) {
        toast.error(
          "Please enable at least one notification channel (Email or In-App)"
        );
        return;
      }

      // Combine date and time
      const scheduledDateTime = new Date(
        `${editFormData.scheduledDate}T${editFormData.scheduledTime}`
      );

      // Check if scheduled time is in the future
      if (scheduledDateTime <= new Date()) {
        toast.error("Scheduled time must be in the future");
        return;
      }

      const notificationData = {
        title: editFormData.title,
        message: editFormData.message,
        scheduledDateTime: scheduledDateTime.toISOString(),
        targetAudience: editFormData.targetAudience,
        emailEnabled: editFormData.emailEnabled,
        inAppEnabled: editFormData.inAppEnabled,
      };

      // Check if this is a reschedule (sent notification) or edit (scheduled notification)
      if (viewingNotification.status === "sent") {
        // Create new notification for reschedule
        await createCustomNotification(notificationData);
        toast.success("Notification rescheduled successfully");
      } else {
        // Update existing notification for edit
        await updateCustomNotification(
          viewingNotification._id,
          notificationData
        );
        toast.success("Notification updated successfully");
      }

      // Close modal and refresh list
      setViewingNotification(null);
      setIsEditMode(false);
      fetchCustomNotifications();
    } catch (error) {
      console.error("Error saving notification:", error);
      toast.error(
        viewingNotification.status === "sent"
          ? "Failed to reschedule notification"
          : "Failed to update notification"
      );
    } finally {
      setSavingCustom(false);
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      fetchCustomNotifications(newPage, pageSize);
    }
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page
    fetchCustomNotifications(1, newPageSize);
  };

  // Load custom notifications when tab is active
  useEffect(() => {
    if (activeTab === "custom-notifications") {
      fetchCustomNotifications();
    }
  }, [activeTab]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const response =
        await notificationSettingsService.updateNotificationSetting(
          "abandoned_cart",
          settings
        );

      toast.success("Notification settings saved successfully", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      setSettings(response.data);
    } catch (err) {
      console.error("Error saving notification settings:", err);
      setError("Failed to save notification settings. Please try again.");
      toast.error("Failed to save notification settings", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setSaving(false);
    }
  };

  const handleTrigger = async () => {
    try {
      setTriggering(true);
      setError(null);

      await notificationSettingsService.triggerNotification("abandoned_cart");

      toast.success(
        "Abandoned cart notification process triggered successfully",
        {
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        }
      );
    } catch (err) {
      console.error("Error triggering notification process:", err);
      setError("Failed to trigger notification process. Please try again.");
      toast.error("Failed to trigger notification process", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setTriggering(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Notification Settings
          </h1>
          <p className="text-gray-600">
            Manage your automated notification preferences and schedules
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab("abandoned-carts")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "abandoned-carts"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Abandoned Carts
              </button>
              <button
                onClick={() => setActiveTab("custom-notifications")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "custom-notifications"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                Custom Notifications
              </button>
              <button
                onClick={() => setActiveTab("vps-scraping")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "vps-scraping"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                VPS Scraping
              </button>
              <button
                onClick={() => setActiveTab("vps-billing")}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === "vps-billing"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                VPS Billing
              </button>
            </nav>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-400 rounded-r-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Abandoned Carts Tab */}
        {activeTab === "abandoned-carts" && settings && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Abandoned Cart Notifications
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Automatically remind customers about items left in their
                    cart
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      settings.enabled
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {settings.enabled ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-8">
                {/* Master Toggle Section */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <label
                        htmlFor="enabled"
                        className="text-base font-medium text-gray-900 block"
                      >
                        Enable Abandoned Cart Notifications
                      </label>
                      <p className="text-sm text-gray-500 mt-1">
                        Turn on automated notifications for abandoned shopping
                        carts
                      </p>
                    </div>
                    <div className="ml-4">
                      <div className="relative inline-block w-14 align-middle select-none">
                        <input
                          type="checkbox"
                          name="enabled"
                          id="enabled"
                          checked={settings.enabled}
                          onChange={handleInputChange}
                          className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-2 appearance-none cursor-pointer transition-all duration-300 ease-in-out"
                        />
                        <label
                          htmlFor="enabled"
                          className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer transition-all duration-300 ease-in-out ${
                            settings.enabled ? "bg-blue-500" : "bg-gray-300"
                          }`}
                        ></label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Timing Configuration */}
                <div
                  className={`transition-all duration-300 ${
                    !settings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Timing Configuration
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Time Threshold */}
                    <div className="space-y-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Time Threshold
                      </label>
                      <p className="text-sm text-gray-500">
                        How long to wait before considering a cart abandoned
                      </p>

                      <div className="flex items-center space-x-3">
                        <div className="flex-1">
                          <input
                            type="number"
                            value={timeConfig.value}
                            onChange={(e) =>
                              updateTimeThreshold({
                                ...timeConfig,
                                value: parseInt(e.target.value) || 1,
                              })
                            }
                            min="1"
                            max="999"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-center text-lg font-medium"
                            disabled={!settings.enabled}
                          />
                        </div>
                        <div className="flex-1">
                          <select
                            value={timeConfig.unit}
                            onChange={(e) =>
                              updateTimeThreshold({
                                ...timeConfig,
                                unit: e.target.value,
                              })
                            }
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                            disabled={!settings.enabled}
                          >
                            <option value="minutes">Minutes</option>
                            <option value="hours">Hours</option>
                            <option value="days">Days</option>
                          </select>
                        </div>
                      </div>

                      <div className="text-center text-sm text-gray-600 bg-gray-50 rounded-lg p-2">
                        Carts will be considered abandoned after{" "}
                        <span className="font-medium text-gray-900">
                          {timeConfig.value} {timeConfig.unit}
                        </span>
                      </div>
                    </div>

                    {/* Check Schedule */}
                    <div className="space-y-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Check Schedule
                      </label>
                      <p className="text-sm text-gray-500">
                        How often to check for abandoned carts
                      </p>

                      {/* Frequency Type */}
                      <div>
                        <select
                          value={scheduleConfig.frequency}
                          onChange={(e) =>
                            updateSchedule({
                              ...scheduleConfig,
                              frequency: e.target.value,
                            })
                          }
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                          disabled={!settings.enabled}
                        >
                          <option value="every">Every</option>
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                        </select>
                      </div>

                      {/* Interval Configuration */}
                      {scheduleConfig.frequency === "every" && (
                        <div className="flex items-center space-x-3">
                          <div className="flex-1">
                            <input
                              type="number"
                              value={scheduleConfig.interval}
                              onChange={(e) =>
                                updateSchedule({
                                  ...scheduleConfig,
                                  interval: parseInt(e.target.value) || 1,
                                })
                              }
                              min="1"
                              max="999"
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-center text-lg font-medium"
                              disabled={!settings.enabled}
                            />
                          </div>
                          <div className="flex-1">
                            <select
                              value={scheduleConfig.unit}
                              onChange={(e) =>
                                updateSchedule({
                                  ...scheduleConfig,
                                  unit: e.target.value,
                                })
                              }
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                              disabled={!settings.enabled}
                            >
                              <option value="minutes">Minutes</option>
                              <option value="hours">Hours</option>
                              <option value="days">Days</option>
                            </select>
                          </div>
                        </div>
                      )}

                      {/* Time Configuration for Daily/Weekly */}
                      {(scheduleConfig.frequency === "daily" ||
                        scheduleConfig.frequency === "weekly") && (
                        <div>
                          <label className="block text-xs font-medium text-gray-600 mb-2">
                            {scheduleConfig.frequency === "weekly"
                              ? "Time (Every Monday)"
                              : "Time"}
                          </label>
                          <input
                            type="time"
                            value={scheduleConfig.time}
                            onChange={(e) =>
                              updateSchedule({
                                ...scheduleConfig,
                                time: e.target.value,
                              })
                            }
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-center text-lg font-medium"
                            disabled={!settings.enabled}
                          />
                        </div>
                      )}

                      <div className="text-center text-sm text-gray-600 bg-gray-50 rounded-lg p-2">
                        {scheduleConfig.frequency === "every" && (
                          <>
                            Check every{" "}
                            <span className="font-medium text-gray-900">
                              {scheduleConfig.interval} {scheduleConfig.unit}
                            </span>
                          </>
                        )}
                        {scheduleConfig.frequency === "daily" && (
                          <>
                            Check daily at{" "}
                            <span className="font-medium text-gray-900">
                              {scheduleConfig.time}
                            </span>
                          </>
                        )}
                        {scheduleConfig.frequency === "weekly" && (
                          <>
                            Check every Monday at{" "}
                            <span className="font-medium text-gray-900">
                              {scheduleConfig.time}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notification Channels */}
                <div
                  className={`transition-all duration-300 ${
                    !settings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Notification Channels
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors duration-200">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <input
                            type="checkbox"
                            name="emailEnabled"
                            id="emailEnabled"
                            checked={settings.emailEnabled}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                            disabled={!settings.enabled}
                          />
                        </div>
                        <div className="flex-1">
                          <label
                            htmlFor="emailEnabled"
                            className="block text-sm font-medium text-gray-900 cursor-pointer"
                          >
                            Email Notifications
                          </label>
                          <p className="text-sm text-gray-500 mt-1">
                            Send notifications via email to customers
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors duration-200">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <input
                            type="checkbox"
                            name="inAppEnabled"
                            id="inAppEnabled"
                            checked={settings.inAppEnabled}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                            disabled={!settings.enabled}
                          />
                        </div>
                        <div className="flex-1">
                          <label
                            htmlFor="inAppEnabled"
                            className="block text-sm font-medium text-gray-900 cursor-pointer"
                          >
                            In-App Notifications
                          </label>
                          <p className="text-sm text-gray-500 mt-1">
                            Show notifications within the application
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notification Content */}
                <div
                  className={`transition-all duration-300 ${
                    !settings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Notification Content
                  </h3>
                  <div className="space-y-6">
                    <div>
                      <label
                        htmlFor="notificationTitle"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Notification Title
                      </label>
                      <input
                        type="text"
                        name="notificationTitle"
                        id="notificationTitle"
                        value={settings.notificationTitle}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                        disabled={!settings.enabled}
                        placeholder="Enter notification title"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="notificationMessage"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Notification Message
                      </label>
                      <textarea
                        name="notificationMessage"
                        id="notificationMessage"
                        value={settings.notificationMessage}
                        onChange={handleInputChange}
                        rows="4"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed resize-none"
                        disabled={!settings.enabled}
                        placeholder="Enter notification message"
                      ></textarea>
                      <div className="mt-2 bg-amber-50 border border-amber-200 rounded-lg p-3">
                        <p className="text-sm text-amber-800">
                          <span className="font-medium">Tip:</span> You can use{" "}
                          <code className="bg-amber-100 px-1 rounded text-amber-900">
                            {"{{"} cartCount {"}}"}
                          </code>{" "}
                          as a placeholder for the number of items in the cart
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="bg-gray-50 rounded-lg p-6 mt-8">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        Actions
                      </h4>
                      <p className="text-sm text-gray-500 mt-1">
                        Test or save your notification settings
                      </p>
                    </div>
                    <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                      <button
                        type="button"
                        onClick={handleTrigger}
                        disabled={triggering || !settings.enabled}
                        className="inline-flex items-center justify-center px-6 py-3 border border-blue-300 rounded-lg text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        {triggering ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Play className="w-4 h-4 mr-2" />
                        )}
                        Test Notification
                      </button>

                      <button
                        type="button"
                        onClick={handleSave}
                        disabled={saving}
                        className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                      >
                        {saving ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Save className="w-4 h-4 mr-2" />
                        )}
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Custom Notifications Tab */}
        {activeTab === "custom-notifications" && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Custom Notifications
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Manage and schedule custom notifications
                  </p>
                </div>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Create Notification
                </button>
              </div>
            </div>

            {/* Notifications Management Table */}
            <div className="overflow-x-auto">
              {loadingCustom ? (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="w-6 h-6 animate-spin text-purple-600" />
                  <span className="ml-2 text-gray-600">
                    Loading notifications...
                  </span>
                </div>
              ) : customNotifications.length === 0 ? (
                <div className="text-center py-12">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No notifications scheduled
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Create your first custom notification to get started.
                  </p>
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Create Your First Notification
                  </button>
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Notification
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Scheduled
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Audience
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {customNotifications.map((notification) => (
                      <tr key={notification._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {notification.title}
                            </div>
                            <div className="text-sm text-gray-500 mt-1 max-w-xs truncate">
                              {notification.message}
                            </div>
                            <div className="flex items-center mt-2 space-x-4">
                              {notification.emailEnabled && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Email
                                </span>
                              )}
                              {notification.inAppEnabled && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  In-App
                                </span>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {new Date(
                              notification.scheduledDateTime
                            ).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(
                              notification.scheduledDateTime
                            ).toLocaleTimeString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <Users className="w-4 h-4 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-900 capitalize">
                              {notification.targetAudience === "all"
                                ? "All Users"
                                : notification.targetAudience === "verified"
                                ? "Verified"
                                : notification.targetAudience === "unverified"
                                ? "Unverified"
                                : notification.targetAudience}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(
                              notification.status
                            )}`}
                          >
                            {notification.status}
                          </span>
                          {notification.status === "sent" && (
                            <div className="text-xs text-gray-500 mt-1">
                              Sent to {notification.sentCount} users
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() =>
                                handleViewNotification(notification)
                              }
                              className="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                              title="View notification details"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            {notification.status === "scheduled" && (
                              <button
                                onClick={() =>
                                  handleCancelNotification(notification._id)
                                }
                                className="text-orange-600 hover:text-orange-900 transition-colors duration-200"
                                title="Cancel notification"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            )}
                            <button
                              onClick={() =>
                                setDeletingNotification(notification._id)
                              }
                              className="text-red-600 hover:text-red-900 transition-colors duration-200"
                              title={
                                notification.status === "sent"
                                  ? "Delete notification record"
                                  : "Delete notification"
                              }
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>

            {/* Pagination Controls */}
            {console.log("Pagination Debug:", {
              loadingCustom,
              notificationsLength: customNotifications.length,
              totalPages,
              totalNotifications,
              currentPage,
              pageSize,
            })}
            {!loadingCustom && customNotifications.length > 0 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-gray-700">
                      Showing{" "}
                      <span className="font-medium">
                        {(currentPage - 1) * pageSize + 1}
                      </span>{" "}
                      to{" "}
                      <span className="font-medium">
                        {Math.min(currentPage * pageSize, totalNotifications)}
                      </span>{" "}
                      of{" "}
                      <span className="font-medium">{totalNotifications}</span>{" "}
                      results
                    </p>
                    <div className="flex items-center space-x-2">
                      <label
                        htmlFor="pageSize"
                        className="text-sm text-gray-700"
                      >
                        Show:
                      </label>
                      <select
                        id="pageSize"
                        value={pageSize}
                        onChange={(e) =>
                          handlePageSizeChange(parseInt(e.target.value))
                        }
                        className="border border-gray-300 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      >
                        <option value={5}>5</option>
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={50}>50</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <nav
                      className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                      aria-label="Pagination"
                    >
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Previous</span>
                        <svg
                          className="h-5 w-5"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>

                      {/* Page Numbers */}
                      {Array.from(
                        { length: Math.min(5, totalPages) },
                        (_, i) => {
                          let pageNumber;
                          if (totalPages <= 5) {
                            pageNumber = i + 1;
                          } else if (currentPage <= 3) {
                            pageNumber = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNumber = totalPages - 4 + i;
                          } else {
                            pageNumber = currentPage - 2 + i;
                          }

                          return (
                            <button
                              key={pageNumber}
                              onClick={() => handlePageChange(pageNumber)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                pageNumber === currentPage
                                  ? "z-10 bg-purple-50 border-purple-500 text-purple-600"
                                  : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                              }`}
                            >
                              {pageNumber}
                            </button>
                          );
                        }
                      )}

                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">Next</span>
                        <svg
                          className="h-5 w-5"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Create Notification Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
              <div className="mt-3">
                {/* Modal Header */}
                <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Create Custom Notification
                  </h3>
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                {/* Modal Content */}
                <div className="py-6 space-y-6">
                  {/* Notification Content */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Notification Details
                    </h4>
                    <div className="space-y-4">
                      <div>
                        <label
                          htmlFor="customTitle"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Notification Title *
                        </label>
                        <input
                          type="text"
                          name="title"
                          id="customTitle"
                          value={customNotification.title}
                          onChange={handleCustomNotificationChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          placeholder="Enter notification title"
                          required
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="customMessage"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Notification Message *
                        </label>
                        <textarea
                          name="message"
                          id="customMessage"
                          value={customNotification.message}
                          onChange={handleCustomNotificationChange}
                          rows="4"
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                          placeholder="Enter notification message"
                          required
                        ></textarea>
                      </div>
                    </div>
                  </div>

                  {/* Scheduling */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Schedule
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="scheduledDate"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Date *
                        </label>
                        <input
                          type="date"
                          name="scheduledDate"
                          id="scheduledDate"
                          value={customNotification.scheduledDate}
                          onChange={handleCustomNotificationChange}
                          min={new Date().toISOString().split("T")[0]}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          required
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="scheduledTime"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Time *
                        </label>
                        <input
                          type="time"
                          name="scheduledTime"
                          id="scheduledTime"
                          value={customNotification.scheduledTime}
                          onChange={handleCustomNotificationChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                          required
                        />
                      </div>
                    </div>

                    {customNotification.scheduledDate &&
                      customNotification.scheduledTime && (
                        <div className="mt-3 text-center text-sm text-gray-600 bg-purple-50 rounded-lg p-3">
                          Notification will be sent on{" "}
                          <span className="font-medium text-purple-800">
                            {new Date(
                              `${customNotification.scheduledDate}T${customNotification.scheduledTime}`
                            ).toLocaleString()}
                          </span>
                        </div>
                      )}
                  </div>

                  {/* Target Audience */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Target Audience
                    </h4>
                    <select
                      name="targetAudience"
                      value={customNotification.targetAudience}
                      onChange={handleCustomNotificationChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                    >
                      <option value="all">All Users</option>
                      <option value="verified">Verified Users</option>
                      <option value="unverified">Unverified Users</option>
                    </select>
                  </div>

                  {/* Notification Channels */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Notification Channels
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors duration-200">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            <input
                              type="checkbox"
                              name="emailEnabled"
                              id="customEmailEnabled"
                              checked={customNotification.emailEnabled}
                              onChange={handleCustomNotificationChange}
                              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                            />
                          </div>
                          <div className="flex-1">
                            <label
                              htmlFor="customEmailEnabled"
                              className="block text-sm font-medium text-gray-900 cursor-pointer"
                            >
                              Email Notifications
                            </label>
                            <p className="text-sm text-gray-500 mt-1">
                              Send notifications via email
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors duration-200">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            <input
                              type="checkbox"
                              name="inAppEnabled"
                              id="customInAppEnabled"
                              checked={customNotification.inAppEnabled}
                              onChange={handleCustomNotificationChange}
                              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                            />
                          </div>
                          <div className="flex-1">
                            <label
                              htmlFor="customInAppEnabled"
                              className="block text-sm font-medium text-gray-900 cursor-pointer"
                            >
                              In-App Notifications
                            </label>
                            <p className="text-sm text-gray-500 mt-1">
                              Show notifications within the application
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Modal Footer */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveCustomNotification}
                    disabled={savingCustom}
                    className="inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {savingCustom ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="w-4 h-4 mr-2" />
                    )}
                    Schedule Notification
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* View/Edit Notification Modal */}
        {viewingNotification && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
              <div className="mt-3">
                {/* Modal Header */}
                <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {isEditMode
                        ? viewingNotification.status === "sent"
                          ? "Reschedule Notification"
                          : "Edit Notification"
                        : "View Notification"}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {viewingNotification.status === "sent"
                        ? isEditMode
                          ? "Create a new notification based on this sent notification"
                          : "This notification has been sent"
                        : isEditMode
                        ? "Make changes to your notification"
                        : "View notification details"}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {viewingNotification.status === "scheduled" &&
                      !isEditMode && (
                        <button
                          onClick={handleEditNotification}
                          className="inline-flex items-center px-3 py-2 border border-purple-300 rounded-md text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors duration-200"
                        >
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </button>
                      )}
                    {viewingNotification.status === "sent" && !isEditMode && (
                      <button
                        onClick={handleRescheduleNotification}
                        className="inline-flex items-center px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors duration-200"
                      >
                        <Calendar className="w-4 h-4 mr-2" />
                        Reschedule
                      </button>
                    )}
                    <button
                      onClick={() => {
                        setViewingNotification(null);
                        setIsEditMode(false);
                      }}
                      className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    >
                      <X className="w-6 h-6" />
                    </button>
                  </div>
                </div>

                {/* Modal Content */}
                <div className="py-6 space-y-6">
                  {/* Notification Status */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">
                          Status
                        </h4>
                        <div className="mt-2 flex items-center space-x-4">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(
                              viewingNotification.status
                            )}`}
                          >
                            {viewingNotification.status}
                          </span>
                          {viewingNotification.status === "sent" && (
                            <span className="text-sm text-gray-600">
                              Sent to {viewingNotification.sentCount} users
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Created by</p>
                        <p className="text-sm font-medium text-gray-900">
                          {viewingNotification.createdBy?.firstName}{" "}
                          {viewingNotification.createdBy?.lastName}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Notification Content */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Notification Details
                    </h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Title {isEditMode && "*"}
                        </label>
                        {isEditMode ? (
                          <input
                            type="text"
                            name="title"
                            value={editFormData.title}
                            onChange={handleEditFormChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                            placeholder="Enter notification title"
                            required
                          />
                        ) : (
                          <div className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900">
                            {viewingNotification.title}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Message {isEditMode && "*"}
                        </label>
                        {isEditMode ? (
                          <textarea
                            name="message"
                            value={editFormData.message}
                            onChange={handleEditFormChange}
                            rows="4"
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                            placeholder="Enter notification message"
                            required
                          />
                        ) : (
                          <div className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 whitespace-pre-wrap">
                            {viewingNotification.message}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Scheduling */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Schedule
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Date {isEditMode && "*"}
                        </label>
                        {isEditMode ? (
                          <input
                            type="date"
                            name="scheduledDate"
                            value={editFormData.scheduledDate}
                            onChange={handleEditFormChange}
                            min={new Date().toISOString().split("T")[0]}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                            required
                          />
                        ) : (
                          <div className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900">
                            {new Date(
                              viewingNotification.scheduledDateTime
                            ).toLocaleDateString()}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Time {isEditMode && "*"}
                        </label>
                        {isEditMode ? (
                          <input
                            type="time"
                            name="scheduledTime"
                            value={editFormData.scheduledTime}
                            onChange={handleEditFormChange}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                            required
                          />
                        ) : (
                          <div className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900">
                            {new Date(
                              viewingNotification.scheduledDateTime
                            ).toLocaleTimeString()}
                          </div>
                        )}
                      </div>
                    </div>

                    {isEditMode &&
                      editFormData.scheduledDate &&
                      editFormData.scheduledTime && (
                        <div className="mt-3 text-center text-sm text-gray-600 bg-purple-50 rounded-lg p-3">
                          Notification will be sent on{" "}
                          <span className="font-medium text-purple-800">
                            {new Date(
                              `${editFormData.scheduledDate}T${editFormData.scheduledTime}`
                            ).toLocaleString()}
                          </span>
                        </div>
                      )}
                  </div>

                  {/* Target Audience */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Target Audience
                    </h4>
                    {isEditMode ? (
                      <select
                        name="targetAudience"
                        value={editFormData.targetAudience}
                        onChange={handleEditFormChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                      >
                        <option value="all">All Users</option>
                        <option value="verified">Verified Users</option>
                        <option value="unverified">Unverified Users</option>
                      </select>
                    ) : (
                      <div className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 capitalize">
                        {viewingNotification.targetAudience === "all"
                          ? "All Users"
                          : viewingNotification.targetAudience === "verified"
                          ? "Verified Users"
                          : viewingNotification.targetAudience === "unverified"
                          ? "Unverified Users"
                          : viewingNotification.targetAudience}
                      </div>
                    )}
                  </div>

                  {/* Notification Channels */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 mb-4">
                      Notification Channels
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {isEditMode ? (
                              <input
                                type="checkbox"
                                name="emailEnabled"
                                checked={editFormData.emailEnabled}
                                onChange={handleEditFormChange}
                                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                              />
                            ) : (
                              <div
                                className={`h-4 w-4 rounded border-2 ${
                                  viewingNotification.emailEnabled
                                    ? "bg-purple-600 border-purple-600"
                                    : "border-gray-300"
                                } flex items-center justify-center`}
                              >
                                {viewingNotification.emailEnabled && (
                                  <svg
                                    className="w-3 h-3 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="block text-sm font-medium text-gray-900">
                              Email Notifications
                            </div>
                            <p className="text-sm text-gray-500 mt-1">
                              Send notifications via email
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {isEditMode ? (
                              <input
                                type="checkbox"
                                name="inAppEnabled"
                                checked={editFormData.inAppEnabled}
                                onChange={handleEditFormChange}
                                className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                              />
                            ) : (
                              <div
                                className={`h-4 w-4 rounded border-2 ${
                                  viewingNotification.inAppEnabled
                                    ? "bg-purple-600 border-purple-600"
                                    : "border-gray-300"
                                } flex items-center justify-center`}
                              >
                                {viewingNotification.inAppEnabled && (
                                  <svg
                                    className="w-3 h-3 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="block text-sm font-medium text-gray-900">
                              In-App Notifications
                            </div>
                            <p className="text-sm text-gray-500 mt-1">
                              Show notifications within the application
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Modal Footer */}
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                  {isEditMode ? (
                    <>
                      <button
                        onClick={handleCancelEdit}
                        className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors duration-200"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleSaveEdit}
                        disabled={savingCustom}
                        className={`inline-flex items-center justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                          viewingNotification.status === "sent"
                            ? "bg-green-600 hover:bg-green-700 focus:ring-green-500"
                            : "bg-purple-600 hover:bg-purple-700 focus:ring-purple-500"
                        } focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200`}
                      >
                        {savingCustom ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : viewingNotification.status === "sent" ? (
                          <Calendar className="w-4 h-4 mr-2" />
                        ) : (
                          <Save className="w-4 h-4 mr-2" />
                        )}
                        {viewingNotification.status === "sent"
                          ? "Reschedule Notification"
                          : "Save Changes"}
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => {
                        setViewingNotification(null);
                        setIsEditMode(false);
                      }}
                      className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-colors duration-200"
                    >
                      Close
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {deletingNotification && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mt-4">
                  Delete Notification
                </h3>
                <div className="mt-2 px-7 py-3">
                  <p className="text-sm text-gray-500">
                    Are you sure you want to delete this notification? This
                    action cannot be undone.
                  </p>
                  {(() => {
                    const notification = customNotifications.find(
                      (n) => n._id === deletingNotification
                    );
                    if (notification?.status === "sent") {
                      return (
                        <p className="text-xs text-amber-600 mt-2 bg-amber-50 p-2 rounded">
                          Note: This notification has already been sent to
                          users. Deleting will only remove the record.
                        </p>
                      );
                    }
                    return null;
                  })()}
                </div>
                <div className="flex justify-center space-x-4 mt-4">
                  <button
                    onClick={() => setDeletingNotification(null)}
                    className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() =>
                      handleDeleteNotification(deletingNotification)
                    }
                    className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* VPS Scraping Tab */}
        {activeTab === "vps-scraping" && vpsSettings && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    VPS Pricing Scraping
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Automatically scrape and update Contabo VPS pricing
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      vpsSettings.enabled
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {vpsSettings.enabled ? "Enabled" : "Disabled"}
                  </span>
                  <button
                    onClick={async () => {
                      setTriggering(true);
                      try {
                        const response =
                          await notificationSettingsService.triggerNotification(
                            "vps_scraping"
                          );
                        toast.success(
                          `VPS scraping triggered successfully! ${
                            response.result?.scraped || 0
                          } packages processed.`,
                          {
                            position: "top-right",
                            autoClose: 5000,
                          }
                        );
                      } catch (error) {
                        toast.error("Failed to trigger VPS scraping", {
                          position: "top-right",
                          autoClose: 3000,
                        });
                      } finally {
                        setTriggering(false);
                      }
                    }}
                    disabled={triggering}
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {triggering ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-2 h-3 w-3 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Running...
                      </>
                    ) : (
                      "Run Now"
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6 space-y-6">
              {/* Enable/Disable Toggle */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Enable VPS Scraping
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Automatically scrape Contabo VPS pricing on schedule
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    name="enabled"
                    checked={vpsSettings.enabled}
                    onChange={handleVpsInputChange}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                </label>
              </div>

              {/* Schedule Configuration */}
              <div
                className={`transition-all duration-300 ${
                  !vpsSettings.enabled ? "opacity-50" : ""
                }`}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Scraping Schedule
                </h3>
                <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
                  {/* Frequency Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Frequency
                    </label>
                    <select
                      value={vpsScheduleConfig.frequency}
                      onChange={(e) =>
                        updateVpsSchedule({
                          ...vpsScheduleConfig,
                          frequency: e.target.value,
                        })
                      }
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                      disabled={!vpsSettings.enabled}
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly (Every Monday)</option>
                      <option value="custom">Custom Date & Time</option>
                    </select>
                  </div>

                  {/* Time Configuration for Daily/Weekly */}
                  {(vpsScheduleConfig.frequency === "daily" ||
                    vpsScheduleConfig.frequency === "weekly") && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {vpsScheduleConfig.frequency === "weekly"
                          ? "Time (Every Monday)"
                          : "Time"}
                      </label>
                      <input
                        type="time"
                        value={vpsScheduleConfig.scheduledTime}
                        onChange={(e) =>
                          updateVpsSchedule({
                            ...vpsScheduleConfig,
                            scheduledTime: e.target.value,
                          })
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-center text-lg font-medium"
                        disabled={!vpsSettings.enabled}
                      />
                    </div>
                  )}

                  {/* Custom Date & Time Configuration */}
                  {vpsScheduleConfig.frequency === "custom" && (
                    <div>
                      <h4 className="text-md font-medium text-gray-900 mb-4">
                        Schedule
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Date *
                          </label>
                          <input
                            type="date"
                            value={vpsScheduleConfig.scheduledDate}
                            onChange={(e) =>
                              updateVpsSchedule({
                                ...vpsScheduleConfig,
                                scheduledDate: e.target.value,
                              })
                            }
                            min={new Date().toISOString().split("T")[0]}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                            disabled={!vpsSettings.enabled}
                            required
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Time *
                          </label>
                          <input
                            type="time"
                            value={vpsScheduleConfig.scheduledTime}
                            onChange={(e) =>
                              updateVpsSchedule({
                                ...vpsScheduleConfig,
                                scheduledTime: e.target.value,
                              })
                            }
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                            disabled={!vpsSettings.enabled}
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Schedule Summary */}
                  <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-3">
                    <p className="text-sm text-green-800">
                      <strong>Current schedule:</strong>{" "}
                      {vpsSettings.cronSchedule}
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      {vpsScheduleConfig.frequency === "daily" &&
                        `Daily at ${vpsScheduleConfig.scheduledTime} UTC`}
                      {vpsScheduleConfig.frequency === "weekly" &&
                        `Every Monday at ${vpsScheduleConfig.scheduledTime} UTC`}
                      {vpsScheduleConfig.frequency === "custom" &&
                        vpsScheduleConfig.scheduledDate &&
                        `One-time on ${vpsScheduleConfig.scheduledDate} at ${vpsScheduleConfig.scheduledTime} UTC`}
                    </p>
                  </div>
                </div>
              </div>

              {/* Notification Channels */}
              <div
                className={`transition-all duration-300 ${
                  !vpsSettings.enabled ? "opacity-50" : ""
                }`}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Notification Channels
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors duration-200">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <input
                          type="checkbox"
                          name="inAppEnabled"
                          id="vps-inAppEnabled"
                          checked={vpsSettings.inAppEnabled}
                          onChange={handleVpsInputChange}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded disabled:opacity-50"
                          disabled={!vpsSettings.enabled}
                        />
                      </div>
                      <div className="flex-1">
                        <label
                          htmlFor="vps-inAppEnabled"
                          className="block text-sm font-medium text-gray-900 cursor-pointer"
                        >
                          In-App Notifications
                        </label>
                        <p className="text-sm text-gray-600 mt-1">
                          Show notifications in the admin dashboard when pricing
                          is updated
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors duration-200">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <input
                          type="checkbox"
                          name="emailEnabled"
                          id="vps-emailEnabled"
                          checked={vpsSettings.emailEnabled}
                          onChange={handleVpsInputChange}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded disabled:opacity-50"
                          disabled={!vpsSettings.enabled}
                        />
                      </div>
                      <div className="flex-1">
                        <label
                          htmlFor="vps-emailEnabled"
                          className="block text-sm font-medium text-gray-900 cursor-pointer"
                        >
                          Email Notifications
                        </label>
                        <p className="text-sm text-gray-600 mt-1">
                          Send email alerts to all admin users when pricing
                          changes are detected
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notification Content */}
              <div
                className={`transition-all duration-300 ${
                  !vpsSettings.enabled ? "opacity-50" : ""
                }`}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Notification Content
                </h3>
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="vps-notificationTitle"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Notification Title
                    </label>
                    <input
                      type="text"
                      name="notificationTitle"
                      id="vps-notificationTitle"
                      value={vpsSettings.notificationTitle}
                      onChange={handleVpsInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                      disabled={!vpsSettings.enabled}
                      placeholder="Enter notification title"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="vps-notificationMessage"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Notification Message
                    </label>
                    <textarea
                      name="notificationMessage"
                      id="vps-notificationMessage"
                      value={vpsSettings.notificationMessage}
                      onChange={handleVpsInputChange}
                      rows="4"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed resize-none"
                      disabled={!vpsSettings.enabled}
                      placeholder="Enter notification message"
                    ></textarea>
                  </div>
                </div>
              </div>

              {/* Pricing Configuration */}
              <div
                className={`transition-all duration-300 ${
                  !vpsSettings.enabled ? "opacity-50" : ""
                }`}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Pricing Configuration
                </h3>
                <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
                  <div>
                    <label
                      htmlFor="vps-pricingMargin"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Pricing Margin (%)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        name="pricingMargin"
                        id="vps-pricingMargin"
                        value={vpsSettings.pricingMargin || 0}
                        onChange={handleVpsInputChange}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed pr-12"
                        disabled={!vpsSettings.enabled}
                        placeholder="0"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span className="text-gray-500 text-sm">%</span>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Add a margin percentage to the converted MAD prices. For
                      example, 10% margin on a $4.99 USD package (≈45 MAD) would
                      result in 49.5 MAD final price.
                    </p>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end pt-4 border-t border-gray-200">
                <button
                  onClick={async () => {
                    setSaving(true);
                    try {
                      const response =
                        await notificationSettingsService.updateNotificationSetting(
                          "vps_scraping",
                          vpsSettings
                        );
                      toast.success(
                        "VPS scraping settings saved successfully",
                        {
                          position: "top-right",
                          autoClose: 3000,
                        }
                      );
                      setVpsSettings(response.data);
                    } catch (err) {
                      console.error("Error saving VPS settings:", err);
                      toast.error("Failed to save VPS scraping settings", {
                        position: "top-right",
                        autoClose: 3000,
                      });
                    } finally {
                      setSaving(false);
                    }
                  }}
                  disabled={saving}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {saving ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    "Save Settings"
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* VPS Billing Tab */}
        {activeTab === "vps-billing" && vpsBillingSettings && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {/* Card Header */}
            <div className="bg-gradient-to-r from-orange-50 to-red-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    VPS Billing Notifications
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Automatically remind customers about upcoming VPS billing
                    dates
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      vpsBillingSettings.enabled
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {vpsBillingSettings.enabled ? "Active" : "Inactive"}
                  </span>
                  <button
                    onClick={async () => {
                      try {
                        setTriggering(true);
                        await notificationSettingsService.triggerNotification(
                          "vps_billing"
                        );
                        toast.success(
                          "VPS billing notification check triggered successfully!",
                          {
                            position: "top-right",
                            autoClose: 3000,
                            hideProgressBar: false,
                            closeOnClick: true,
                            pauseOnHover: true,
                            draggable: true,
                          }
                        );
                      } catch (err) {
                        console.error(
                          "Error triggering VPS billing check:",
                          err
                        );
                        toast.error(
                          "Failed to trigger VPS billing notification check",
                          {
                            position: "top-right",
                            autoClose: 3000,
                            hideProgressBar: false,
                            closeOnClick: true,
                            pauseOnHover: true,
                            draggable: true,
                          }
                        );
                      } finally {
                        setTriggering(false);
                      }
                    }}
                    disabled={triggering}
                    className="inline-flex items-center px-3 py-1.5 border border-orange-300 text-xs font-medium rounded-md text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {triggering ? (
                      <>
                        <Loader2 className="w-3 h-3 animate-spin mr-1" />
                        Checking...
                      </>
                    ) : (
                      <>
                        <Play className="w-3 h-3 mr-1" />
                        Test Now
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-8">
                {/* Master Toggle Section */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <label
                        htmlFor="vps-billing-enabled"
                        className="text-base font-medium text-gray-900 block"
                      >
                        Enable VPS Billing Notifications
                      </label>
                      <p className="text-sm text-gray-500 mt-1">
                        Turn on automated billing reminders for VPS customers
                      </p>
                    </div>
                    <div className="ml-4">
                      <div className="relative inline-block w-14 align-middle select-none">
                        <input
                          type="checkbox"
                          name="vps-billing-enabled"
                          id="vps-billing-enabled"
                          checked={vpsBillingSettings.enabled}
                          onChange={(e) => {
                            setVpsBillingSettings({
                              ...vpsBillingSettings,
                              enabled: e.target.checked,
                            });
                          }}
                          className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-2 appearance-none cursor-pointer transition-all duration-300 ease-in-out"
                        />
                        <label
                          htmlFor="vps-billing-enabled"
                          className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer transition-all duration-300 ease-in-out ${
                            vpsBillingSettings.enabled
                              ? "bg-orange-500"
                              : "bg-gray-300"
                          }`}
                        ></label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Billing Reminder Configuration */}
                <div
                  className={`transition-all duration-300 ${
                    !vpsBillingSettings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Billing Reminder Configuration
                  </h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Days Before Billing */}
                    <div className="space-y-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Reminder Days
                      </label>
                      <p className="text-sm text-gray-500">
                        Send notifications X days before billing date
                      </p>

                      <div className="space-y-3">
                        {[1, 3, 7, 14, 30].map((days) => (
                          <label key={days} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={
                                vpsBillingSettings.daysBefore?.includes(days) ||
                                false
                              }
                              onChange={(e) => {
                                const currentDays =
                                  vpsBillingSettings.daysBefore || [];
                                const newDays = e.target.checked
                                  ? [...currentDays, days]
                                  : currentDays.filter((d) => d !== days);
                                setVpsBillingSettings({
                                  ...vpsBillingSettings,
                                  daysBefore: newDays.sort((a, b) => a - b),
                                });
                              }}
                              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded disabled:opacity-50"
                              disabled={!vpsBillingSettings.enabled}
                            />
                            <span className="ml-3 text-sm text-gray-700">
                              {days} day{days > 1 ? "s" : ""} before billing
                            </span>
                          </label>
                        ))}
                      </div>

                      <div className="text-center text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                        Selected:{" "}
                        <span className="font-medium text-gray-900">
                          {vpsBillingSettings.daysBefore?.length > 0
                            ? vpsBillingSettings.daysBefore.join(", ") + " days"
                            : "None selected"}
                        </span>
                      </div>
                    </div>

                    {/* Notification Methods */}
                    <div className="space-y-3">
                      <label className="block text-sm font-medium text-gray-700">
                        Notification Methods
                      </label>
                      <p className="text-sm text-gray-500">
                        Choose how to notify customers about billing
                      </p>

                      <div className="space-y-3">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={vpsBillingSettings.methods?.email || false}
                            onChange={(e) => {
                              setVpsBillingSettings({
                                ...vpsBillingSettings,
                                methods: {
                                  ...vpsBillingSettings.methods,
                                  email: e.target.checked,
                                },
                              });
                            }}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded disabled:opacity-50"
                            disabled={!vpsBillingSettings.enabled}
                          />
                          <span className="ml-3 text-sm text-gray-700">
                            📧 Email Notifications
                          </span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={vpsBillingSettings.methods?.inApp || false}
                            onChange={(e) => {
                              setVpsBillingSettings({
                                ...vpsBillingSettings,
                                methods: {
                                  ...vpsBillingSettings.methods,
                                  inApp: e.target.checked,
                                },
                              });
                            }}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded disabled:opacity-50"
                            disabled={!vpsBillingSettings.enabled}
                          />
                          <span className="ml-3 text-sm text-gray-700">
                            🔔 In-App Notifications
                          </span>
                        </label>
                      </div>

                      <div className="text-center text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                        Active methods:{" "}
                        <span className="font-medium text-gray-900">
                          {[
                            vpsBillingSettings.methods?.email && "Email",
                            vpsBillingSettings.methods?.inApp && "In-App",
                          ]
                            .filter(Boolean)
                            .join(", ") || "None selected"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notification Channels */}
                <div
                  className={`transition-all duration-300 ${
                    !vpsBillingSettings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Notification Channels
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors duration-200">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <input
                            type="checkbox"
                            name="inAppEnabled"
                            id="billing-inAppEnabled"
                            checked={vpsBillingSettings.methods?.inApp || false}
                            onChange={(e) => {
                              setVpsBillingSettings({
                                ...vpsBillingSettings,
                                methods: {
                                  ...vpsBillingSettings.methods,
                                  inApp: e.target.checked,
                                },
                              });
                            }}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded disabled:opacity-50"
                            disabled={!vpsBillingSettings.enabled}
                          />
                        </div>
                        <div className="flex-1">
                          <label
                            htmlFor="billing-inAppEnabled"
                            className="block text-sm font-medium text-gray-900 cursor-pointer"
                          >
                            In-App Notifications
                          </label>
                          <p className="text-sm text-gray-600 mt-1">
                            Show notifications in the admin dashboard when
                            billing reminders are sent
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:border-orange-300 transition-colors duration-200">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <input
                            type="checkbox"
                            name="emailEnabled"
                            id="billing-emailEnabled"
                            checked={vpsBillingSettings.methods?.email || false}
                            onChange={(e) => {
                              setVpsBillingSettings({
                                ...vpsBillingSettings,
                                methods: {
                                  ...vpsBillingSettings.methods,
                                  email: e.target.checked,
                                },
                              });
                            }}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded disabled:opacity-50"
                            disabled={!vpsBillingSettings.enabled}
                          />
                        </div>
                        <div className="flex-1">
                          <label
                            htmlFor="billing-emailEnabled"
                            className="block text-sm font-medium text-gray-900 cursor-pointer"
                          >
                            Email Notifications
                          </label>
                          <p className="text-sm text-gray-600 mt-1">
                            Send email alerts to all admin users when billing
                            reminders are sent to customers
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Billing Schedule */}
                <div
                  className={`transition-all duration-300 ${
                    !vpsBillingSettings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Billing Schedule
                  </h3>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
                    {/* Frequency Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Frequency
                      </label>
                      <select
                        value={vpsBillingScheduleConfig.frequency}
                        onChange={(e) =>
                          updateVpsBillingSchedule({
                            ...vpsBillingScheduleConfig,
                            frequency: e.target.value,
                          })
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                        disabled={!vpsBillingSettings.enabled}
                      >
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly (Every Monday)</option>
                        <option value="custom">Custom Date & Time</option>
                      </select>
                    </div>

                    {/* Time Configuration for Daily/Weekly */}
                    {(vpsBillingScheduleConfig.frequency === "daily" ||
                      vpsBillingScheduleConfig.frequency === "weekly") && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {vpsBillingScheduleConfig.frequency === "weekly"
                            ? "Time (Every Monday)"
                            : "Time"}
                        </label>
                        <input
                          type="time"
                          value={vpsBillingScheduleConfig.scheduledTime}
                          onChange={(e) =>
                            updateVpsBillingSchedule({
                              ...vpsBillingScheduleConfig,
                              scheduledTime: e.target.value,
                            })
                          }
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed text-center text-lg font-medium"
                          disabled={!vpsBillingSettings.enabled}
                        />
                      </div>
                    )}

                    {/* Custom Date & Time Configuration */}
                    {vpsBillingScheduleConfig.frequency === "custom" && (
                      <div>
                        <h4 className="text-md font-medium text-gray-900 mb-4">
                          Schedule
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Date *
                            </label>
                            <input
                              type="date"
                              value={vpsBillingScheduleConfig.scheduledDate}
                              onChange={(e) =>
                                updateVpsBillingSchedule({
                                  ...vpsBillingScheduleConfig,
                                  scheduledDate: e.target.value,
                                })
                              }
                              min={new Date().toISOString().split("T")[0]}
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                              disabled={!vpsBillingSettings.enabled}
                              required
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Time *
                            </label>
                            <input
                              type="time"
                              value={vpsBillingScheduleConfig.scheduledTime}
                              onChange={(e) =>
                                updateVpsBillingSchedule({
                                  ...vpsBillingScheduleConfig,
                                  scheduledTime: e.target.value,
                                })
                              }
                              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                              disabled={!vpsBillingSettings.enabled}
                              required
                            />
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Schedule Summary */}
                    <div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <p className="text-sm text-orange-800">
                        <strong>Current schedule:</strong>{" "}
                        {vpsBillingSettings.cronSchedule}
                      </p>
                      <p className="text-xs text-orange-600 mt-1">
                        {vpsBillingScheduleConfig.frequency === "daily" &&
                          `Daily at ${vpsBillingScheduleConfig.scheduledTime} UTC`}
                        {vpsBillingScheduleConfig.frequency === "weekly" &&
                          `Every Monday at ${vpsBillingScheduleConfig.scheduledTime} UTC`}
                        {vpsBillingScheduleConfig.frequency === "custom" &&
                          vpsBillingScheduleConfig.scheduledDate &&
                          `One-time on ${vpsBillingScheduleConfig.scheduledDate} at ${vpsBillingScheduleConfig.scheduledTime} UTC`}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Email Template Preview */}
                <div
                  className={`transition-all duration-300 ${
                    !vpsBillingSettings.enabled ? "opacity-50" : ""
                  }`}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Email Template Preview
                  </h3>
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-6">
                    <div className="space-y-4">
                      <div>
                        <span className="text-sm font-medium text-gray-600">
                          Subject:
                        </span>
                        <div className="mt-1 text-sm text-gray-900 font-medium">
                          VPS Billing Reminder - Payment Due in X Days
                        </div>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-600">
                          Preview:
                        </span>
                        <div className="mt-2 p-4 bg-white border border-gray-200 rounded-md text-sm text-gray-700 leading-relaxed">
                          <p className="mb-3">
                            <strong>Dear [Customer Name],</strong>
                          </p>
                          <p className="mb-3">
                            This is a friendly reminder that your VPS service{" "}
                            <strong>"[VPS Name]"</strong> has an upcoming
                            billing date on <strong>[Billing Date]</strong>.
                          </p>
                          <p className="mb-3">
                            Please ensure your payment method is up to date to
                            avoid any service interruption.
                          </p>
                          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-3">
                            <p className="text-sm text-blue-700">
                              <strong>VPS Details:</strong> [VPS ID] •{" "}
                              <strong>Amount:</strong> [Amount] MAD •{" "}
                              <strong>Days until billing:</strong> [Days]
                            </p>
                          </div>
                          <p className="text-sm text-gray-600">
                            Thank you for choosing ZTech for your VPS hosting
                            needs!
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                  <button
                    onClick={async () => {
                      try {
                        setSaving(true);
                        const response =
                          await notificationSettingsService.updateNotificationSetting(
                            "vps_billing",
                            vpsBillingSettings
                          );
                        toast.success(
                          "VPS billing notification settings saved successfully!",
                          {
                            position: "top-right",
                            autoClose: 3000,
                            hideProgressBar: false,
                            closeOnClick: true,
                            pauseOnHover: true,
                            draggable: true,
                          }
                        );
                        setVpsBillingSettings(response.data);
                      } catch (err) {
                        console.error(
                          "Error saving VPS billing settings:",
                          err
                        );
                        toast.error(
                          "Failed to save VPS billing notification settings",
                          {
                            position: "top-right",
                            autoClose: 3000,
                            hideProgressBar: false,
                            closeOnClick: true,
                            pauseOnHover: true,
                            draggable: true,
                          }
                        );
                      } finally {
                        setSaving(false);
                      }
                    }}
                    disabled={saving}
                    className="flex-1 sm:flex-none inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    {saving ? (
                      <>
                        <Loader2 className="animate-spin -ml-1 mr-3 h-5 w-5" />
                        Saving Settings...
                      </>
                    ) : (
                      <>
                        <Save className="w-5 h-5 mr-2" />
                        Save Configuration
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationSettingsPage;
