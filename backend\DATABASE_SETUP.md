# Database Configuration Guide

This guide explains how to switch between MongoDB Atlas (development) and local MongoDB (production) in your ZTech application.

## Overview

The application now supports two database configurations:

- **Development Environment** (`isProd = false`): Uses MongoDB Atlas (cloud)
- **Production Environment** (`isProd = true`): Uses local MongoDB

## Quick Start

### Switch to Development (Atlas)
```bash
npm run env:dev
```

### Switch to Production (Local MongoDB)
```bash
npm run env:prod
```

### Check Current Environment
```bash
npm run env:status
```

### Test Database Connection
```bash
npm run test:db
```

## Environment Configuration

### Development (MongoDB Atlas)
- **When to use**: Local development, testing
- **Database**: MongoDB Atlas (cloud)
- **Configuration**: Uses `MONGODB_URI` from `.env` file
- **Benefits**: No local MongoDB installation required, shared data across team

### Production (Local MongoDB)
- **When to use**: Production deployment
- **Database**: Local MongoDB instance
- **Configuration**: `mongodb://0.0.0.0:27017/ztech`
- **Benefits**: Better performance, no external dependencies

## Manual Configuration

You can also manually edit the `isProd` flag in `backend/constants/constant.js`:

```javascript
const isProd = false; // Development (Atlas)
const isProd = true;  // Production (Local MongoDB)
```

## Environment Variables

Make sure your `.env` file contains:

```env
# MongoDB Atlas connection string (for development)
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
```

## Troubleshooting

### MongoDB Atlas Issues (Development)
- Verify `MONGODB_URI` is set in `.env`
- Check IP whitelist in MongoDB Atlas dashboard
- Confirm database credentials are correct
- Test network connectivity

### Local MongoDB Issues (Production)
- Ensure MongoDB is installed and running
- Check if MongoDB service is started
- Verify MongoDB is listening on port 27017
- Test connection: `mongosh mongodb://localhost:27017`

### Common Commands

```bash
# Check MongoDB status (Linux/Mac)
sudo systemctl status mongod

# Start MongoDB (Linux/Mac)
sudo systemctl start mongod

# Connect to local MongoDB
mongosh mongodb://localhost:27017

# List databases
show dbs

# Use specific database
use ztech
```

## Database Structure

### Production Database
- **Name**: `ztech`
- **Host**: `0.0.0.0:27017`

### Development Database
- **Name**: As specified in Atlas connection string
- **Host**: MongoDB Atlas cluster

## Scripts Reference

| Command | Description |
|---------|-------------|
| `npm run env:dev` | Switch to development (Atlas) |
| `npm run env:prod` | Switch to production (local) |
| `npm run env:status` | Show current environment |
| `npm run test:db` | Test database connection |
| `node scripts/switchEnvironment.js help` | Show detailed help |

## Best Practices

1. **Always test connections** after switching environments
2. **Restart your server** after changing environments
3. **Use development mode** for local testing
4. **Use production mode** for deployment
5. **Keep backups** of both local and Atlas databases

## Migration Notes

- Your existing local MongoDB data remains unchanged
- Atlas database is separate from local database
- You may need to migrate data between environments if needed
- Consider using database seeding scripts for consistent data across environments

## Support

If you encounter issues:
1. Run `npm run test:db` to diagnose connection problems
2. Check the troubleshooting section above
3. Verify environment variables are correctly set
4. Ensure the correct MongoDB service is running for your environment
