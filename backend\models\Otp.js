const mongoose = require('mongoose');

const otpSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    otpCode: {
        type: String,
        required: true,
    },
    reason: {
        type: String,
        required: true,
    },
    toBeVerified: {
        type: String,
        required: true,
    },
    expiresAt: {
        type: Date,
        required: true,
    },
    status: {
        type: String,
        required: true,
        default: 'pending',
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

const Otp = mongoose.model('Otp', otpSchema);

module.exports = Otp;
