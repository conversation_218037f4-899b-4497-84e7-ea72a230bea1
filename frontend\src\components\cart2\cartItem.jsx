import React, { useState, useEffect, useRef } from "react";
import {
  TrashIcon,
  ChevronsDown,
  ShoppingCart,
  ChevronsUp,
  ServerIcon,
  GlobeIcon,
  LayoutIcon,
  ShieldIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { getLocalizedContent } from "@/app/helpers/helpers";
console.log("🚀 ~ getLocalizedContent:", getLocalizedContent);
import { useParams } from "next/navigation";
import { toast } from "react-toastify";

// Mapping of category names to their corresponding icon components.
const categoryIcons = {
  Hosting: ServerIcon,
  SSL: ShieldIcon,
  Promotions: LayoutIcon,
};

// A small wrapper for the icon container.
const IconWrapper = ({ children }) => (
  <div className="h-12 w-12 flex-shrink-0 bg-blue-100 rounded-lg flex items-center justify-center">
    {children}
  </div>
);

function CartItem({ item, onQuantityChange, onPeriodChange, t }) {
  const [showDetails, setShowDetails] = useState(false);
  const [localQuantity, setLocalQuantity] = useState(item.quantity);
  const [isUpdating, setIsUpdating] = useState(false);
  const [imgError, setImgError] = useState(false);
  const [period, setPeriod] = useState(item.period || 1);

  // For the expanding details section
  const detailsRef = useRef(null);
  const [detailsHeight, setDetailsHeight] = useState(0);

  // Check if the item is an SSL certificate
  const isSSL = item?.package?.brand?.category?.name === "SSL";
  const isWebdev = item?.package?.brand?.category?.name === "web creation";
  console.log("🚀 ~ CartItem ~ isWebdev:", isWebdev);

  // When showDetails changes, measure the height of the inner content.
  useEffect(() => {
    if (detailsRef.current) {
      setDetailsHeight(detailsRef.current.scrollHeight);
    }
  }, [showDetails]);

  // Sync localQuantity with item.quantity if the parent updates it.
  useEffect(() => {
    setLocalQuantity(item.quantity);
  }, [item.quantity]);

  const handleQuantityChange = async (delta) => {
    try {
      // Enforce boundary conditions
      let newQuantity = localQuantity + delta;

      // Boundary conditions: Quantity must be between 1 and 10, and not updating
      if (newQuantity < 1 || newQuantity > 10) {
        toast.warn(t("quantity_boundary_error"));
        return;
      }

      // Call the parent function to update quantity
      const result = await onQuantityChange(
        item?.package._id,
        delta,
        1,
        period
      );

      // If the parent function succeeds, update local state
      if (result.success) {
        setLocalQuantity(newQuantity);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error handling quantity change:", error);
    }
  };

  const handlePeriodChange = (packID, period_v) => {
    const periodNum = parseInt(period_v, 10);
    setPeriod(periodNum);
    onPeriodChange(packID, periodNum);
  };

  // either no image URL is provided or the image failed to load.
  const shouldShowFallback = !item?.package?.image || imgError;

  // Retrieve the icon component based on the category.
  const IconComponent =
    categoryIcons[item?.package?.brand?.category?.name] || ShoppingCart;
  const params = useParams();

  return (
    <div className="relative cart-item-container bg-white sm:mt-3 pb-4 mb-4 rounded-lg">
      <div className="flex flex-col sm:flex-row items-center py-2 px-2 w-full">
        <div className="flex flex-col sm:flex-row items-center justify-between flex-grow w-full mb-4 md:mb-0">
          <div className="flex flex-row items-center gap-4 w-full justify-center sm:justify-start">
            <IconWrapper>
              {shouldShowFallback ? (
                <ShoppingCart className="h-6 w-6 text-blue-600" />
              ) : (
                <IconComponent className="h-6 w-6 text-blue-600" />
              )}
            </IconWrapper>
            <div className="min-w-0 flex-grow text-left">
              <p className="font-medium text-sm text-gray-800 truncate">
                {getLocalizedContent(
                  item.package.brand,
                  "name",
                  params.locale || "en"
                ) +
                  ", " +
                  getLocalizedContent(
                    item.package,
                    "name",
                    params.locale || "en"
                  )}
              </p>
              <p className="text-sm text-gray-600">
                {`${item?.package.price} MAD x ${localQuantity}`}
              </p>
              <p className="text-sm font-medium text-gray-800">
                Total: &nbsp;
                {`${(
                  item.price * localQuantity * period -
                  item.discount
                ).toFixed(2)} MAD`}
                {item.discount > 0 && (
                  <p className="ml-11 text-[14px] text-gray-600">
                    <del>
                      {" "}
                      {`${(item.price * localQuantity * period).toFixed(
                        2
                      )} MAD`}{" "}
                    </del>
                  </p>
                )}
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row items-center gap-[35px] mt-4 md:mt-0 self-start sm:self-auto">
            <div className="flex sm:flex-col items-center sm:items-left flex-row">
              <label
                htmlFor="quantity"
                className="block text-sm font-medium text-gray-700 mr-2"
              >
                {t("quantity")}
              </label>
              <div className="flex bg-white justify-between overflow-hidden">
                <button
                  className="text-black text-lg w-8 h-8 flex items-center justify-center rounded-l-full font-bold hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={() => handleQuantityChange(-1)}
                  aria-label="Decrease quantity"
                  // disabled={localQuantity <= 1 || isUpdating}
                >
                  -
                </button>
                <span className="text-black font-medium text-sm px-2 py-1">
                  {localQuantity}
                </span>
                <button
                  className="text-black text-lg w-8 h-8 flex items-center justify-center rounded-r-full font-bold hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={() => handleQuantityChange(1)}
                  aria-label="Increase quantity"
                  // disabled={localQuantity >= 10 || isUpdating}
                >
                  +
                </button>
              </div>
            </div>

            {!isWebdev && (
              <div className="flex sm:flex-col items-start mt-2 flex-row md:mt-0 md:mr-5">
                <label
                  htmlFor="period"
                  className="block text-sm font-medium w-full text-left text-gray-700 mr-2"
                >
                  {t("period")}
                </label>
                <select
                  id="period"
                  value={period}
                  onChange={(e) =>
                    handlePeriodChange(item?.package._id, e.target.value)
                  }
                  disabled={isSSL}
                  className={`text-sm lg:w-[150px] rounded-md border border-gray-300 py-1.5 px-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${
                    isSSL ? "bg-gray-100 cursor-not-allowed" : ""
                  }`}
                >
                  <option value="1">{t("1_month")}</option>
                  <option value="12">
                    {t("1_year", {
                      percentage:
                        item?.package?.discounts?.find((d) => d.period === 12)
                          ?.percentage || 0,
                    })}
                  </option>
                </select>
              </div>
            )}

            {/* <div className="flex sm:flex-col items-start mt-2  flex-row md:mt-0 md:mr-5">
              <label
                htmlFor="period"
                className="block text-sm font-medium w-full text-left text-gray-700 mr-2"
              >
                {t("period")}
              </label>
              <select
                id="period"
                value={period}
                onChange={(e) =>
                  handlePeriodChange(item?.package._id, e.target.value)
                }
                className="text-sm lg:w-[150px] rounded-md border border-gray-300 py-1.5 px-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
              >
                <option value="1">{t("1_month")}</option>
                <option value="12">
                  {t("1_year") + item?.package.discounts}
                </option>
              </select>
            </div> */}
          </div>

          <button
            className="absolute sm:static top-[34%] right-2  text-sm sm:ml-5 text-red-500 flex items-center justify-center hover:bg-red-500 hover:bg-opacity-80 hover:text-white py-1 px-2 rounded-md mt-4 md:mt-0"
            onClick={() =>
              onQuantityChange(item?.package._id, -1, localQuantity)
            }
          >
            <TrashIcon width={18} className="mx-auto" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default CartItem;
