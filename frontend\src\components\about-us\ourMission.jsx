import { ArrowRightIcon, PhoneIcon } from '@heroicons/react/24/solid'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image';
import Link from 'next/link'
import React from 'react'
import imgOurMission from "/public/images/services/data-center.png";


const contentData = [
    {
        text: "contentData.0.text",
        strongText: "contentData.0.strongText"
    },
    {
        text: "contentData.1.text",
        strongText: "contentData.1.strongText"
    },
    {
        text: "contentData.2.text",
        strongText: "contentData.2.strongText"
    },
    {
        text: "contentData.3.text",
        strongText: "contentData.3.strongText"
    },
];

function OurMission({ t }) {
    return (
        <div className='w-full'>
            <div className="max-w-[1400px] mx-auto md:p-16 p-4 flex flex-col md:flex-row items-center justify-between gap-8">
                <div className="bg-white">
                    <Typography
                        variant='h1'
                        className="text-3xl font-semibold mb-6 text-black max-w-xl">
                        {t('mission_title')}
                    </Typography>
                    <p>
                        {t('mission_description')}
                    </p>

                    <div className="mt-10 flex flex-col border-l-2 border-gray-700 px-4 py-2 gap-y-6 max-w-lg">
                        {contentData.map((item, index) => (
                            <div key={index} className="flex flex-col gap-y-2">
                                <p>
                                    <strong>{t(item.strongText)}</strong> {t(item.text)}
                                </p>
                            </div>
                        ))}
                    </div>

                    <div className="flex md:flex-row flex-col items-center gap-4 mt-12">
                        <Link
                            href="#contact-nous"
                            name='contact-nous'
                            className='px-4 py-3 md:py-2 flex justify-between md:justify-normal md:w-fit w-full  gap-x-4 text-black shadow-none uppercase border-2 border-black rounded-3xl bg-transparent hover:bg-secondary hover:border-secondary hover:text-white font-inter text-sm font-medium'>
                            {t('requestQuote')}
                            <ArrowRightIcon className='w-4 my-auto' />
                        </Link>
                        <Link
                            href="tel:+212662841605"

                            className='px-6 md:py-2 py-3 flex md:flex-row flex-row-reverse justify-between md:justify-normal md:w-fit w-full gap-x-2 text-secondary hover:border-primary shadow-none uppercase border-2 border-gray-500 rounded-3xl hover:bg-primary hover:text-white font-inter text-sm font-medium'>
                            <PhoneIcon className='text-secondary md:w-4 w-5 my-auto' />
                            <p className='md:m-auto'>+212 662 841 605</p>
                        </Link>
                    </div>
                </div>
                <div className="rounded-xl shadow-lg max-w-md h-full">
                    {/* <img
                        loading="lazy"
                        src="/images/services/data-center.png"
                        alt="Data center"
                        className="w-full h-full rounded-xl object-cover"
                    /> */}
                    <Image
                        src={imgOurMission}
                        alt="Data center"
                        width={900}
                        height={600}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        className="w-full h-full rounded-xl object-cover"
                        placeholder='blur'
                    />
                </div>
            </div>
        </div>
    )
}

export default OurMission