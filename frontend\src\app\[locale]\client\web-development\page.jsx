"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, CardBody, Alert } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import PaymentStatusModal from "../../../../components/order/paymentStatusModal";
import { useSearchParams } from "next/navigation";
import orderService from "../../../services/orderService";
import { CodeXml, Clock, CheckCircle, PhoneCall, Code } from "lucide-react";
import { OrderStatus } from "../../../config/ConstStatus";
import { getLocalizedContent } from "@/app/helpers/helpers";

export default function WebDevOrdersPage() {
  const t = useTranslations("client");
  const [subOrders, setSubOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const localeActive = useLocale();

  const searchParams = useSearchParams();
  const status = searchParams.get("status");
  const item = searchParams.get("item");
  const categoryName = "web creation";

  useEffect(() => {
    if (status && item) {
      setPaymentStatus(status);
      setOrderId(item);
      setOpenModal(true);
    }
  }, [status, item]);

  const closeModal = () => setOpenModal(false);

  useEffect(() => {
    const getSubOrders = async () => {
      try {
        const res = await orderService.getSubOrdersByCategory(categoryName);
        setSubOrders(res.data.subOrders);
      } catch (error) {
        console.error("Error getting suborders", error);
      } finally {
        setLoading(false);
      }
    };
    getSubOrders();
  }, [categoryName]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[80vh]">
        <Typography variant="h6" className="text-gray-600">
          {t("loading_orders")}
        </Typography>
      </div>
    );
  }

  const ProcessSteps = () => (
    <div className="bg-white rounded-xl shadow-sm p-6 mb-8 font-inter">
      <p className="text-gray-800 mb-6 text-lg font-medium">
        {t("web_dev.process_title")}
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="flex flex-col items-center text-center">
          <div className="bg-blue-50 p-3 rounded-full mb-4">
            <CheckCircle className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="mb-2">
            {t("web_dev.step1_title")}
          </Typography>
          <Typography className="text-gray-600">
            {t("web_dev.step1_desc")}
          </Typography>
        </div>
        <div className="flex flex-col items-center text-center">
          <div className="bg-blue-50 p-3 rounded-full mb-4">
            <PhoneCall className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="mb-2">
            {t("web_dev.step2_title")}
          </Typography>
          <Typography className="text-gray-600">
            {t("web_dev.step2_desc")}
          </Typography>
        </div>
        <div className="flex flex-col items-center text-center">
          <div className="bg-blue-50 p-3 rounded-full mb-4">
            <Code className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="mb-2">
            {t("web_dev.step3_title")}
          </Typography>
          <Typography className="text-gray-600">
            {t("web_dev.step3_desc")}
          </Typography>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-8 bg-gray-50 min-h-[80vh]">
      <ProcessSteps />

      {!subOrders?.length ? (
        <Alert className="bg-blue-50 text-blue-800 border border-blue-100">
          <div className="flex items-center gap-4">
            <Clock className="h-6 w-6" />
            <Typography>{t("web_dev.no_orders_message")}</Typography>
          </div>
        </Alert>
      ) : (
        <div className="space-y-4">
          {subOrders.map((subOrder) => {
            const {
              _id,
              package: pkg,
              status,
              createdAt,
              price,
              basedPrice,
            } = subOrder;

            return (
              <Card
                key={_id}
                className="bg-white rounded-xl shadow-sm border border-gray-200"
              >
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <CodeXml className="h-7 w-7 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <Typography variant="h5" className="text-gray-900">
                          {getLocalizedContent(pkg, "name", localeActive)}
                        </Typography>
                        <Typography className="text-sm text-gray-600">
                          {t("reference")} {pkg.reference}
                        </Typography>
                      </div>
                    </div>
                    <span
                      className={`px-4 py-2 rounded-full text-sm font-medium ${
                        status === OrderStatus.PROCESSING
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-green-100 text-green-800"
                      }`}
                    >
                      {status}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-6 mt-4">
                    <div>
                      <Typography className="text-sm text-gray-500">
                        {t("web_dev.order_date")}
                      </Typography>
                      <Typography className="font-medium">
                        {new Date(createdAt).toLocaleDateString()}
                      </Typography>
                    </div>
                    <div>
                      <Typography className="text-sm text-gray-500">
                        {t("web_dev.project_cost")}
                      </Typography>
                      <Typography className="font-medium">
                        {price} MAD
                        {basedPrice > price && (
                          <span className="line-through text-gray-400 ml-2">
                            {basedPrice} MAD
                          </span>
                        )}
                      </Typography>
                    </div>
                  </div>

                  {status === OrderStatus.PROCESSING && (
                    <Alert color="blue" className="mt-4">
                      {t("web_dev.contact_soon_message")}
                    </Alert>
                  )}
                </CardBody>
              </Card>
            );
          })}
        </div>
      )}

      {openModal && (
        <PaymentStatusModal
          status={paymentStatus}
          orderId={orderId}
          onClose={closeModal}
          t={t}
        />
      )}
    </div>
  );
}
