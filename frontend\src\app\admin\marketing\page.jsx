"use client"
import React, { useState, useEffect } from 'react';
import { Mail, Send, Users, ChevronRight, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Calendar, Edit, Trash2, Plus, AlertCircle, CheckCircle, X } from 'lucide-react';

// Dummy data for email campaigns
const dummyCampaigns = [
  {
    id: '1',
    name: 'Campaign One',
    subject: 'Welcome to our service',
    content: 'This is the content of the first email campaign.',
    status: 'sent',
    scheduled_for: null,
    sent_at: '2023-01-01T00:00:00Z',
    opens: 150,
    clicks: 50,
    recipients: 200
  },
  {
    id: '2',
    name: 'Campaign Two',
    subject: 'New Features',
    content: 'This is the content of the second email campaign.',
    status: 'scheduled',
    scheduled_for: '2023-02-01T00:00:00Z',
    sent_at: null,
    opens: 0,
    clicks: 0,
    recipients: 300
  }
];

// Dummy data for subscribers
const dummySubscribers = [
  {
    id: '1',
    email: '<EMAIL>',
    full_name: 'User One',
    subscribed_at: '2023-01-01T00:00:00Z',
    status: 'active',
    last_email_sent: '2023-01-10T00:00:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    full_name: 'User Two',
    subscribed_at: '2023-02-01T00:00:00Z',
    status: 'unsubscribed',
    last_email_sent: null
  }
];

export default function MarketingManagement() {
  const [campaigns, setCampaigns] = useState([]);
  const [subscribers, setSubscribers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    content: '',
    scheduled_for: ''
  });

  useEffect(() => {
    fetchCampaigns();
    fetchSubscribers();
  }, []);

  const fetchCampaigns = async () => {
    try {
      // Simulate fetching data
      setCampaigns(dummyCampaigns);
    } catch (err) {
      setError('Failed to fetch campaigns');
    }
  };

  const fetchSubscribers = async () => {
    try {
      // Simulate fetching data
      setSubscribers(dummySubscribers);
    } catch (err) {
      setError('Failed to fetch subscribers');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const newCampaign = {
        ...formData,
        id: (campaigns.length + 1).toString(),
        status: formData.scheduled_for ? 'scheduled' : 'draft',
        opens: 0,
        clicks: 0,
        recipients: subscribers.filter(s => s.status === 'active').length,
        sent_at: formData.scheduled_for ? null : new Date().toISOString()
      };

      // Simulate adding a new campaign
      setCampaigns([...campaigns, newCampaign]);

      setSuccess('Campaign created successfully!');
      setShowForm(false);
      setFormData({
        name: '',
        subject: '',
        content: '',
        scheduled_for: ''
      });
    } catch (err) {
      setError('Failed to create campaign');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-blue-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Subscribers</p>
              <h3 className="text-2xl font-bold">{subscribers.length}</h3>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-green-600">
              <ChevronRight className="h-4 w-4" />
              <span>{subscribers.filter(s => s.status === 'active').length} active</span>
            </div>
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Open Rate</p>
              <h3 className="text-2xl font-bold">32.4%</h3>
            </div>
            <PieChart className="h-8 w-8 text-green-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-green-600">
              <ChevronRight className="h-4 w-4" />
              <span>+2.3% from last month</span>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Click Rate</p>
              <h3 className="text-2xl font-bold">12.8%</h3>
            </div>
            <BarChart className="h-8 w-8 text-purple-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-purple-600">
              <ChevronRight className="h-4 w-4" />
              <span>+1.2% from last month</span>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 p-6 rounded-xl">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Campaigns</p>
              <h3 className="text-2xl font-bold">{campaigns.length}</h3>
            </div>
            <Mail className="h-8 w-8 text-orange-600" />
          </div>
          <div className="mt-4">
            <div className="flex items-center text-sm text-orange-600">
              <ChevronRight className="h-4 w-4" />
              <span>{campaigns.filter(c => c.status === 'scheduled').length} scheduled</span>
            </div>
          </div>
        </div>
      </div>

      {/* Email Campaigns */}
      <div className="bg-white rounded-lg shadow-lg">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Email Campaigns</h2>
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
            >
              <Plus className="h-5 w-5 mr-2" />
              New Campaign
            </button>
          </div>
        </div>

        {error && (
          <div className="m-6 bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}

        {success && (
          <div className="m-6 bg-green-50 text-green-600 p-4 rounded-lg flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            {success}
          </div>
        )}

        {showForm && (
          <div className="p-6 border-b">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Campaign Name
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Subject
                </label>
                <input
                  type="text"
                  required
                  value={formData.subject}
                  onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Content
                </label>
                <textarea
                  required
                  rows={6}
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Schedule Send (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={formData.scheduled_for}
                  onChange={(e) => setFormData({ ...formData, scheduled_for: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="text-gray-600 hover:text-gray-900"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Creating...' : 'Create Campaign'}
                </button>
              </div>
            </form>
          </div>
        )}

        <div className="divide-y">
          {campaigns.map((campaign) => (
            <div key={campaign.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold text-lg mb-1">{campaign.name}</h3>
                  <p className="text-gray-600 mb-2">{campaign.subject}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className={`px-2 py-1 rounded-full ${campaign.status === 'sent'
                        ? 'bg-green-100 text-green-800'
                        : campaign.status === 'scheduled'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                      {campaign.status}
                    </span>
                    {campaign.status === 'sent' && (
                      <>
                        <span className="text-gray-500">
                          Opens: {campaign.opens}
                        </span>
                        <span className="text-gray-500">
                          Clicks: {campaign.clicks}
                        </span>
                      </>
                    )}
                    {campaign.status === 'scheduled' && (
                      <span className="text-gray-500">
                        Scheduled for: {new Date(campaign.scheduled_for).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg">
                    <Edit className="h-5 w-5" />
                  </button>
                  <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg">
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Subscribers List */}
      <div className="bg-white rounded-lg shadow-lg">
        <div className="p-6 border-b">
          <h2 className="text-2xl font-bold">Subscribers</h2>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subscriber
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subscribed Date
                </th>
                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Email
                </th>
                <th className="px-6 py-3 bg-gray-50"></th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscribers.map((subscriber) => (
                <tr key={subscriber.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {subscriber.full_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {subscriber.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs rounded-full ${subscriber.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                      }`}>
                      {subscriber.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(subscriber.subscribed_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {subscriber.last_email_sent
                      ? new Date(subscriber.last_email_sent).toLocaleDateString()
                      : 'Never'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-red-600 hover:text-red-900">
                      <X className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}