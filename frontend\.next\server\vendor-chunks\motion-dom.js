"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/motion-dom";
exports.ids = ["vendor-chunks/motion-dom"];
exports.modules = {

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseGroupPlaybackControls: () => (/* binding */ BaseGroupPlaybackControls)\n/* harmony export */ });\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/supports/scroll-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n\nclass BaseGroupPlaybackControls {\n    constructor(animations){\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = ()=>this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        // Support for new finished Promise and legacy thennable API\n        return Promise.all(this.animations.map((animation)=>\"finished\" in animation ? animation.finished : animation));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */ getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for(let i = 0; i < this.animations.length; i++){\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation)=>{\n            if ((0,_utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsScrollTimeline)() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            } else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return ()=>{\n            subscriptions.forEach((cancel, i)=>{\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for(let i = 0; i < this.animations.length; i++){\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls)=>controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/controls/Group.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/controls/Group.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupPlaybackControls: () => (/* binding */ GroupPlaybackControls)\n/* harmony export */ });\n/* harmony import */ var _BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseGroup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs\");\n\n/**\n * TODO: This is a temporary class to support the legacy\n * thennable API\n */ class GroupPlaybackControls extends _BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_0__.BaseGroupPlaybackControls {\n    then(onResolve, onReject) {\n        return Promise.all(this.animations).then(onResolve).catch(onReject);\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9jb250cm9scy9Hcm91cC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEQ7QUFFNUQ7OztDQUdDLEdBQ0QsTUFBTUMsOEJBQThCRCxxRUFBeUJBO0lBQ3pERSxLQUFLQyxTQUFTLEVBQUVDLFFBQVEsRUFBRTtRQUN0QixPQUFPQyxRQUFRQyxHQUFHLENBQUMsSUFBSSxDQUFDQyxVQUFVLEVBQUVMLElBQUksQ0FBQ0MsV0FBV0ssS0FBSyxDQUFDSjtJQUM5RDtBQUNKO0FBRWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2NvbnRyb2xzL0dyb3VwLm1qcz9kNjA1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhc2VHcm91cFBsYXliYWNrQ29udHJvbHMgfSBmcm9tICcuL0Jhc2VHcm91cC5tanMnO1xuXG4vKipcbiAqIFRPRE86IFRoaXMgaXMgYSB0ZW1wb3JhcnkgY2xhc3MgdG8gc3VwcG9ydCB0aGUgbGVnYWN5XG4gKiB0aGVubmFibGUgQVBJXG4gKi9cbmNsYXNzIEdyb3VwUGxheWJhY2tDb250cm9scyBleHRlbmRzIEJhc2VHcm91cFBsYXliYWNrQ29udHJvbHMge1xuICAgIHRoZW4ob25SZXNvbHZlLCBvblJlamVjdCkge1xuICAgICAgICByZXR1cm4gUHJvbWlzZS5hbGwodGhpcy5hbmltYXRpb25zKS50aGVuKG9uUmVzb2x2ZSkuY2F0Y2gob25SZWplY3QpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgR3JvdXBQbGF5YmFja0NvbnRyb2xzIH07XG4iXSwibmFtZXMiOlsiQmFzZUdyb3VwUGxheWJhY2tDb250cm9scyIsIkdyb3VwUGxheWJhY2tDb250cm9scyIsInRoZW4iLCJvblJlc29sdmUiLCJvblJlamVjdCIsIlByb21pc2UiLCJhbGwiLCJhbmltYXRpb25zIiwiY2F0Y2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/controls/Group.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcGeneratorDuration: () => (/* binding */ calcGeneratorDuration),\n/* harmony export */   maxGeneratorDuration: () => (/* binding */ maxGeneratorDuration)\n/* harmony export */ });\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */ const maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while(!state.done && duration < maxGeneratorDuration){\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2NhbGMtZHVyYXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7OztDQUdDLEdBQ0QsTUFBTUEsdUJBQXVCO0FBQzdCLFNBQVNDLHNCQUFzQkMsU0FBUztJQUNwQyxJQUFJQyxXQUFXO0lBQ2YsTUFBTUMsV0FBVztJQUNqQixJQUFJQyxRQUFRSCxVQUFVSSxJQUFJLENBQUNIO0lBQzNCLE1BQU8sQ0FBQ0UsTUFBTUUsSUFBSSxJQUFJSixXQUFXSCxxQkFBc0I7UUFDbkRHLFlBQVlDO1FBQ1pDLFFBQVFILFVBQVVJLElBQUksQ0FBQ0g7SUFDM0I7SUFDQSxPQUFPQSxZQUFZSCx1QkFBdUJRLFdBQVdMO0FBQ3pEO0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvY2FsYy1kdXJhdGlvbi5tanM/ODMzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEltcGxlbWVudCBhIHByYWN0aWNhbCBtYXggZHVyYXRpb24gZm9yIGtleWZyYW1lIGdlbmVyYXRpb25cbiAqIHRvIHByZXZlbnQgaW5maW5pdGUgbG9vcHNcbiAqL1xuY29uc3QgbWF4R2VuZXJhdG9yRHVyYXRpb24gPSAyMDAwMDtcbmZ1bmN0aW9uIGNhbGNHZW5lcmF0b3JEdXJhdGlvbihnZW5lcmF0b3IpIHtcbiAgICBsZXQgZHVyYXRpb24gPSAwO1xuICAgIGNvbnN0IHRpbWVTdGVwID0gNTA7XG4gICAgbGV0IHN0YXRlID0gZ2VuZXJhdG9yLm5leHQoZHVyYXRpb24pO1xuICAgIHdoaWxlICghc3RhdGUuZG9uZSAmJiBkdXJhdGlvbiA8IG1heEdlbmVyYXRvckR1cmF0aW9uKSB7XG4gICAgICAgIGR1cmF0aW9uICs9IHRpbWVTdGVwO1xuICAgICAgICBzdGF0ZSA9IGdlbmVyYXRvci5uZXh0KGR1cmF0aW9uKTtcbiAgICB9XG4gICAgcmV0dXJuIGR1cmF0aW9uID49IG1heEdlbmVyYXRvckR1cmF0aW9uID8gSW5maW5pdHkgOiBkdXJhdGlvbjtcbn1cblxuZXhwb3J0IHsgY2FsY0dlbmVyYXRvckR1cmF0aW9uLCBtYXhHZW5lcmF0b3JEdXJhdGlvbiB9O1xuIl0sIm5hbWVzIjpbIm1heEdlbmVyYXRvckR1cmF0aW9uIiwiY2FsY0dlbmVyYXRvckR1cmF0aW9uIiwiZ2VuZXJhdG9yIiwiZHVyYXRpb24iLCJ0aW1lU3RlcCIsInN0YXRlIiwibmV4dCIsImRvbmUiLCJJbmZpbml0eSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGeneratorEasing: () => (/* binding */ createGeneratorEasing)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./calc-duration.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n\n\n/**\n * Create a progress => progress easing function from a generator.\n */ function createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({\n        ...options,\n        keyframes: [\n            0,\n            scale\n        ]\n    });\n    const duration = Math.min((0,_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__.calcGeneratorDuration)(generator), _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__.maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress)=>{\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(duration)\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isGenerator: () => (/* binding */ isGenerator)\n/* harmony export */ });\nfunction isGenerator(type) {\n    return typeof type === \"function\";\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2lzLWdlbmVyYXRvci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBLFlBQVlDLElBQUk7SUFDckIsT0FBTyxPQUFPQSxTQUFTO0FBQzNCO0FBRXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvaXMtZ2VuZXJhdG9yLm1qcz82NWUxIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzR2VuZXJhdG9yKHR5cGUpIHtcbiAgICByZXR1cm4gdHlwZW9mIHR5cGUgPT09IFwiZnVuY3Rpb25cIjtcbn1cblxuZXhwb3J0IHsgaXNHZW5lcmF0b3IgfTtcbiJdLCJuYW1lcyI6WyJpc0dlbmVyYXRvciIsInR5cGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueTransition: () => (/* binding */ getValueTransition)\n/* harmony export */ });\nfunction getValueTransition(transition, key) {\n    return transition ? transition[key] || transition[\"default\"] || transition : undefined;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi91dGlscy9nZXQtdmFsdWUtdHJhbnNpdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBLG1CQUFtQkMsVUFBVSxFQUFFQyxHQUFHO0lBQ3ZDLE9BQU9ELGFBQ0RBLFVBQVUsQ0FBQ0MsSUFBSSxJQUNiRCxVQUFVLENBQUMsVUFBVSxJQUNyQkEsYUFDRkU7QUFDVjtBQUU4QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi91dGlscy9nZXQtdmFsdWUtdHJhbnNpdGlvbi5tanM/ZWQ0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBnZXRWYWx1ZVRyYW5zaXRpb24odHJhbnNpdGlvbiwga2V5KSB7XG4gICAgcmV0dXJuIHRyYW5zaXRpb25cbiAgICAgICAgPyB0cmFuc2l0aW9uW2tleV0gfHxcbiAgICAgICAgICAgIHRyYW5zaXRpb25bXCJkZWZhdWx0XCJdIHx8XG4gICAgICAgICAgICB0cmFuc2l0aW9uXG4gICAgICAgIDogdW5kZWZpbmVkO1xufVxuXG5leHBvcnQgeyBnZXRWYWx1ZVRyYW5zaXRpb24gfTtcbiJdLCJuYW1lcyI6WyJnZXRWYWx1ZVRyYW5zaXRpb24iLCJ0cmFuc2l0aW9uIiwia2V5IiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeAnimationControls: () => (/* binding */ NativeAnimationControls)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/attach-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\");\n\n\nclass NativeAnimationControls {\n    constructor(animation){\n        this.animation = animation;\n    }\n    get duration() {\n        var _a, _b, _c;\n        const durationInMs = ((_b = (_a = this.animation) === null || _a === void 0 ? void 0 : _a.effect) === null || _b === void 0 ? void 0 : _b.getComputedTiming().duration) || ((_c = this.options) === null || _c === void 0 ? void 0 : _c.duration) || 300;\n        return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(Number(durationInMs));\n    }\n    get time() {\n        var _a;\n        if (this.animation) {\n            return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(((_a = this.animation) === null || _a === void 0 ? void 0 : _a.currentTime) || 0);\n        }\n        return 0;\n    }\n    set time(newTime) {\n        if (this.animation) {\n            this.animation.currentTime = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(newTime);\n        }\n    }\n    get speed() {\n        return this.animation ? this.animation.playbackRate : 1;\n    }\n    set speed(newSpeed) {\n        if (this.animation) {\n            this.animation.playbackRate = newSpeed;\n        }\n    }\n    get state() {\n        return this.animation ? this.animation.playState : \"finished\";\n    }\n    get startTime() {\n        return this.animation ? this.animation.startTime : null;\n    }\n    get finished() {\n        return this.animation ? this.animation.finished : Promise.resolve();\n    }\n    play() {\n        this.animation && this.animation.play();\n    }\n    pause() {\n        this.animation && this.animation.pause();\n    }\n    stop() {\n        if (!this.animation || this.state === \"idle\" || this.state === \"finished\") {\n            return;\n        }\n        if (this.animation.commitStyles) {\n            this.animation.commitStyles();\n        }\n        this.cancel();\n    }\n    flatten() {\n        var _a;\n        if (!this.animation) return;\n        (_a = this.animation.effect) === null || _a === void 0 ? void 0 : _a.updateTiming({\n            easing: \"linear\"\n        });\n    }\n    attachTimeline(timeline) {\n        if (this.animation) (0,_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_1__.attachTimeline)(this.animation, timeline);\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    }\n    complete() {\n        this.animation && this.animation.finish();\n    }\n    cancel() {\n        try {\n            this.animation && this.animation.cancel();\n        } catch (e) {}\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PseudoAnimation: () => (/* binding */ PseudoAnimation)\n/* harmony export */ });\n/* harmony import */ var _NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NativeAnimationControls.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\");\n/* harmony import */ var _utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/convert-options.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs\");\n\n\nclass PseudoAnimation extends _NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_0__.NativeAnimationControls {\n    constructor(target, pseudoElement, valueName, keyframes, options){\n        const animationOptions = (0,_utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_1__.convertMotionOptionsToNative)(valueName, keyframes, options);\n        const animation = target.animate(animationOptions.keyframes, {\n            pseudoElement,\n            ...animationOptions.options\n        });\n        super(animation);\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS9Qc2V1ZG9BbmltYXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3RTtBQUNHO0FBRTNFLE1BQU1FLHdCQUF3QkYsaUZBQXVCQTtJQUNqREcsWUFBWUMsTUFBTSxFQUFFQyxhQUFhLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFQyxPQUFPLENBQUU7UUFDOUQsTUFBTUMsbUJBQW1CUix3RkFBNEJBLENBQUNLLFdBQVdDLFdBQVdDO1FBQzVFLE1BQU1FLFlBQVlOLE9BQU9PLE9BQU8sQ0FBQ0YsaUJBQWlCRixTQUFTLEVBQUU7WUFDekRGO1lBQ0EsR0FBR0ksaUJBQWlCRCxPQUFPO1FBQy9CO1FBQ0EsS0FBSyxDQUFDRTtJQUNWO0FBQ0o7QUFFMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy9hbmltYXRpb24vd2FhcGkvUHNldWRvQW5pbWF0aW9uLm1qcz9iMjJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5hdGl2ZUFuaW1hdGlvbkNvbnRyb2xzIH0gZnJvbSAnLi9OYXRpdmVBbmltYXRpb25Db250cm9scy5tanMnO1xuaW1wb3J0IHsgY29udmVydE1vdGlvbk9wdGlvbnNUb05hdGl2ZSB9IGZyb20gJy4vdXRpbHMvY29udmVydC1vcHRpb25zLm1qcyc7XG5cbmNsYXNzIFBzZXVkb0FuaW1hdGlvbiBleHRlbmRzIE5hdGl2ZUFuaW1hdGlvbkNvbnRyb2xzIHtcbiAgICBjb25zdHJ1Y3Rvcih0YXJnZXQsIHBzZXVkb0VsZW1lbnQsIHZhbHVlTmFtZSwga2V5ZnJhbWVzLCBvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IGFuaW1hdGlvbk9wdGlvbnMgPSBjb252ZXJ0TW90aW9uT3B0aW9uc1RvTmF0aXZlKHZhbHVlTmFtZSwga2V5ZnJhbWVzLCBvcHRpb25zKTtcbiAgICAgICAgY29uc3QgYW5pbWF0aW9uID0gdGFyZ2V0LmFuaW1hdGUoYW5pbWF0aW9uT3B0aW9ucy5rZXlmcmFtZXMsIHtcbiAgICAgICAgICAgIHBzZXVkb0VsZW1lbnQsXG4gICAgICAgICAgICAuLi5hbmltYXRpb25PcHRpb25zLm9wdGlvbnMsXG4gICAgICAgIH0pO1xuICAgICAgICBzdXBlcihhbmltYXRpb24pO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgUHNldWRvQW5pbWF0aW9uIH07XG4iXSwibmFtZXMiOlsiTmF0aXZlQW5pbWF0aW9uQ29udHJvbHMiLCJjb252ZXJ0TW90aW9uT3B0aW9uc1RvTmF0aXZlIiwiUHNldWRvQW5pbWF0aW9uIiwiY29uc3RydWN0b3IiLCJ0YXJnZXQiLCJwc2V1ZG9FbGVtZW50IiwidmFsdWVOYW1lIiwia2V5ZnJhbWVzIiwib3B0aW9ucyIsImFuaW1hdGlvbk9wdGlvbnMiLCJhbmltYXRpb24iLCJhbmltYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachTimeline: () => (/* binding */ attachTimeline)\n/* harmony export */ });\nfunction attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9hdHRhY2gtdGltZWxpbmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxTQUFTQSxlQUFlQyxTQUFTLEVBQUVDLFFBQVE7SUFDdkNELFVBQVVDLFFBQVEsR0FBR0E7SUFDckJELFVBQVVFLFFBQVEsR0FBRztBQUN6QjtBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9hdHRhY2gtdGltZWxpbmUubWpzP2Y2ZTQiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYXR0YWNoVGltZWxpbmUoYW5pbWF0aW9uLCB0aW1lbGluZSkge1xuICAgIGFuaW1hdGlvbi50aW1lbGluZSA9IHRpbWVsaW5lO1xuICAgIGFuaW1hdGlvbi5vbmZpbmlzaCA9IG51bGw7XG59XG5cbmV4cG9ydCB7IGF0dGFjaFRpbWVsaW5lIH07XG4iXSwibmFtZXMiOlsiYXR0YWNoVGltZWxpbmUiLCJhbmltYXRpb24iLCJ0aW1lbGluZSIsIm9uZmluaXNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyGeneratorOptions: () => (/* binding */ applyGeneratorOptions),\n/* harmony export */   convertMotionOptionsToNative: () => (/* binding */ convertMotionOptionsToNative)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../generators/utils/create-generator-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\");\n/* harmony import */ var _generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../generators/utils/is-generator.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n/* harmony import */ var _easing_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\");\n\n\n\n\n\nconst defaultEasing = \"easeOut\";\nfunction applyGeneratorOptions(options) {\n    var _a;\n    if ((0,_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_3__.isGenerator)(options.type)) {\n        const generatorOptions = (0,_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_2__.createGeneratorEasing)(options, 100, options.type);\n        options.ease = (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)() ? generatorOptions.ease : defaultEasing;\n        options.duration = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(generatorOptions.duration);\n        options.type = \"keyframes\";\n    } else {\n        options.duration = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)((_a = options.duration) !== null && _a !== void 0 ? _a : 0.3);\n        options.ease = options.ease || defaultEasing;\n    }\n}\n// TODO: Reuse for NativeAnimation\nfunction convertMotionOptionsToNative(valueName, keyframes, options) {\n    var _a;\n    const nativeKeyframes = {};\n    const nativeOptions = {\n        fill: \"both\",\n        easing: \"linear\",\n        composite: \"replace\"\n    };\n    nativeOptions.delay = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)((_a = options.delay) !== null && _a !== void 0 ? _a : 0);\n    applyGeneratorOptions(options);\n    nativeOptions.duration = options.duration;\n    const { ease, times } = options;\n    if (times) nativeKeyframes.offset = times;\n    nativeKeyframes[valueName] = keyframes;\n    const easing = (0,_easing_mjs__WEBPACK_IMPORTED_MODULE_4__.mapEasingToNativeEasing)(ease, options.duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */ if (Array.isArray(easing)) {\n        nativeKeyframes.easing = easing;\n    } else {\n        nativeOptions.easing = easing;\n    }\n    return {\n        keyframes: nativeKeyframes,\n        options: nativeOptions\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezierAsString: () => (/* binding */ cubicBezierAsString),\n/* harmony export */   isWaapiSupportedEasing: () => (/* binding */ isWaapiSupportedEasing),\n/* harmony export */   mapEasingToNativeEasing: () => (/* binding */ mapEasingToNativeEasing),\n/* harmony export */   supportedWaapiEasing: () => (/* binding */ supportedWaapiEasing)\n/* harmony export */ });\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _linear_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linear.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n\n\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)() || !easing || typeof easing === \"string\" && (easing in supportedWaapiEasing || (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()) || (0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing) || Array.isArray(easing) && easing.every(isWaapiSupportedEasing));\n}\nconst cubicBezierAsString = ([a, b, c, d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([\n        0,\n        0.65,\n        0.55,\n        1\n    ]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([\n        0.55,\n        0,\n        1,\n        0.45\n    ]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([\n        0.31,\n        0.01,\n        0.66,\n        -0.59\n    ]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([\n        0.33,\n        1.53,\n        0.69,\n        0.99\n    ])\n};\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    } else if (typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()) {\n        return (0,_linear_mjs__WEBPACK_IMPORTED_MODULE_2__.generateLinearEasing)(easing, duration);\n    } else if ((0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing)) {\n        return cubicBezierAsString(easing);\n    } else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing)=>mapEasingToNativeEasing(segmentEasing, duration) || supportedWaapiEasing.easeOut);\n    } else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateLinearEasing: () => (/* binding */ generateLinearEasing)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n\nconst generateLinearEasing = (easing, duration, resolution = 10 // as milliseconds\n)=>{\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for(let i = 0; i < numPoints; i++){\n        points += easing((0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, numPoints - 1, i)) + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9saW5lYXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBRXhDLE1BQU1DLHVCQUF1QixDQUFDQyxRQUFRQyxVQUN0Q0MsYUFBYSxHQUFHLGtCQUFrQjtBQUFuQjtJQUVYLElBQUlDLFNBQVM7SUFDYixNQUFNQyxZQUFZQyxLQUFLQyxHQUFHLENBQUNELEtBQUtFLEtBQUssQ0FBQ04sV0FBV0MsYUFBYTtJQUM5RCxJQUFLLElBQUlNLElBQUksR0FBR0EsSUFBSUosV0FBV0ksSUFBSztRQUNoQ0wsVUFBVUgsT0FBT0Ysc0RBQVFBLENBQUMsR0FBR00sWUFBWSxHQUFHSSxNQUFNO0lBQ3REO0lBQ0EsT0FBTyxDQUFDLE9BQU8sRUFBRUwsT0FBT00sU0FBUyxDQUFDLEdBQUdOLE9BQU9PLE1BQU0sR0FBRyxHQUFHLENBQUMsQ0FBQztBQUM5RDtBQUVnQyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9saW5lYXIubWpzPzZmNDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJvZ3Jlc3MgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuXG5jb25zdCBnZW5lcmF0ZUxpbmVhckVhc2luZyA9IChlYXNpbmcsIGR1cmF0aW9uLCAvLyBhcyBtaWxsaXNlY29uZHNcbnJlc29sdXRpb24gPSAxMCAvLyBhcyBtaWxsaXNlY29uZHNcbikgPT4ge1xuICAgIGxldCBwb2ludHMgPSBcIlwiO1xuICAgIGNvbnN0IG51bVBvaW50cyA9IE1hdGgubWF4KE1hdGgucm91bmQoZHVyYXRpb24gLyByZXNvbHV0aW9uKSwgMik7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBudW1Qb2ludHM7IGkrKykge1xuICAgICAgICBwb2ludHMgKz0gZWFzaW5nKHByb2dyZXNzKDAsIG51bVBvaW50cyAtIDEsIGkpKSArIFwiLCBcIjtcbiAgICB9XG4gICAgcmV0dXJuIGBsaW5lYXIoJHtwb2ludHMuc3Vic3RyaW5nKDAsIHBvaW50cy5sZW5ndGggLSAyKX0pYDtcbn07XG5cbmV4cG9ydCB7IGdlbmVyYXRlTGluZWFyRWFzaW5nIH07XG4iXSwibmFtZXMiOlsicHJvZ3Jlc3MiLCJnZW5lcmF0ZUxpbmVhckVhc2luZyIsImVhc2luZyIsImR1cmF0aW9uIiwicmVzb2x1dGlvbiIsInBvaW50cyIsIm51bVBvaW50cyIsIk1hdGgiLCJtYXgiLCJyb3VuZCIsImkiLCJzdWJzdHJpbmciLCJsZW5ndGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: () => (/* binding */ isDragActive),\n/* harmony export */   isDragging: () => (/* binding */ isDragging)\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLE1BQU1BLGFBQWE7SUFDZkMsR0FBRztJQUNIQyxHQUFHO0FBQ1A7QUFDQSxTQUFTQztJQUNMLE9BQU9ILFdBQVdDLENBQUMsSUFBSUQsV0FBV0UsQ0FBQztBQUN2QztBQUVvQyIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvaXMtYWN0aXZlLm1qcz9hNGE5Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzRHJhZ2dpbmcgPSB7XG4gICAgeDogZmFsc2UsXG4gICAgeTogZmFsc2UsXG59O1xuZnVuY3Rpb24gaXNEcmFnQWN0aXZlKCkge1xuICAgIHJldHVybiBpc0RyYWdnaW5nLnggfHwgaXNEcmFnZ2luZy55O1xufVxuXG5leHBvcnQgeyBpc0RyYWdBY3RpdmUsIGlzRHJhZ2dpbmcgfTtcbiJdLCJuYW1lcyI6WyJpc0RyYWdnaW5nIiwieCIsInkiLCJpc0RyYWdBY3RpdmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: () => (/* binding */ setDragLock)\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        } else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return ()=>{\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    } else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        } else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return ()=>{\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL2RyYWcvc3RhdGUvc2V0LWFjdGl2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkM7QUFFN0MsU0FBU0MsWUFBWUMsSUFBSTtJQUNyQixJQUFJQSxTQUFTLE9BQU9BLFNBQVMsS0FBSztRQUM5QixJQUFJRixzREFBVSxDQUFDRSxLQUFLLEVBQUU7WUFDbEIsT0FBTztRQUNYLE9BQ0s7WUFDREYsc0RBQVUsQ0FBQ0UsS0FBSyxHQUFHO1lBQ25CLE9BQU87Z0JBQ0hGLHNEQUFVLENBQUNFLEtBQUssR0FBRztZQUN2QjtRQUNKO0lBQ0osT0FDSztRQUNELElBQUlGLHNEQUFVQSxDQUFDRyxDQUFDLElBQUlILHNEQUFVQSxDQUFDSSxDQUFDLEVBQUU7WUFDOUIsT0FBTztRQUNYLE9BQ0s7WUFDREosc0RBQVVBLENBQUNHLENBQUMsR0FBR0gsc0RBQVVBLENBQUNJLENBQUMsR0FBRztZQUM5QixPQUFPO2dCQUNISixzREFBVUEsQ0FBQ0csQ0FBQyxHQUFHSCxzREFBVUEsQ0FBQ0ksQ0FBQyxHQUFHO1lBQ2xDO1FBQ0o7SUFDSjtBQUNKO0FBRXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9zdGF0ZS9zZXQtYWN0aXZlLm1qcz8zMDg0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzRHJhZ2dpbmcgfSBmcm9tICcuL2lzLWFjdGl2ZS5tanMnO1xuXG5mdW5jdGlvbiBzZXREcmFnTG9jayhheGlzKSB7XG4gICAgaWYgKGF4aXMgPT09IFwieFwiIHx8IGF4aXMgPT09IFwieVwiKSB7XG4gICAgICAgIGlmIChpc0RyYWdnaW5nW2F4aXNdKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlzRHJhZ2dpbmdbYXhpc10gPSB0cnVlO1xuICAgICAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgICAgICBpc0RyYWdnaW5nW2F4aXNdID0gZmFsc2U7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBpZiAoaXNEcmFnZ2luZy54IHx8IGlzRHJhZ2dpbmcueSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpc0RyYWdnaW5nLnggPSBpc0RyYWdnaW5nLnkgPSB0cnVlO1xuICAgICAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgICAgICBpc0RyYWdnaW5nLnggPSBpc0RyYWdnaW5nLnkgPSBmYWxzZTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG59XG5cbmV4cG9ydCB7IHNldERyYWdMb2NrIH07XG4iXSwibmFtZXMiOlsiaXNEcmFnZ2luZyIsInNldERyYWdMb2NrIiwiYXhpcyIsIngiLCJ5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: () => (/* binding */ hover)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/setup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n\n\n/**\n * Filter out events that are not pointer events, or are triggering\n * while a Motion gesture is active.\n */ function filterEvents(callback) {\n    return (event)=>{\n        if (event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)()) return;\n        callback(event);\n    };\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */ function hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__.setupGesture)(elementOrSelector, options);\n    const onPointerEnter = filterEvents((enterEvent)=>{\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target) return;\n        const onPointerLeave = filterEvents((leaveEvent)=>{\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        });\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    });\n    elements.forEach((element)=>{\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   press: () => (/* binding */ press)\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/is-node-or-child.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/is-primary-pointer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/setup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n/* harmony import */ var _utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/is-keyboard-accessible.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\");\n/* harmony import */ var _utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/keyboard.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\");\n/* harmony import */ var _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/state.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n\n\n\n\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */ function isValidPressEvent(event) {\n    return (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPrimaryPointer)(event) && !(0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */ function press(elementOrSelector, onPressStart, options = {}) {\n    const [elements, eventOptions, cancelEvents] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_3__.setupGesture)(elementOrSelector, options);\n    const startPress = (startEvent)=>{\n        const element = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.has(element)) return;\n        _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.add(element);\n        const onPressEnd = onPressStart(startEvent);\n        const onPointerEnd = (endEvent, success)=>{\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !_utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.has(element)) {\n                return;\n            }\n            _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.delete(element);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, {\n                    success\n                });\n            }\n        };\n        const onPointerUp = (upEvent)=>{\n            onPointerEnd(upEvent, options.useGlobalTarget || (0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_1__.isNodeOrChild)(element, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent)=>{\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    elements.forEach((element)=>{\n        if (!(0,_utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_4__.isElementKeyboardAccessible)(element) && element.getAttribute(\"tabindex\") === null) {\n            element.tabIndex = 0;\n        }\n        const target = options.useGlobalTarget ? window : element;\n        target.addEventListener(\"pointerdown\", startPress, eventOptions);\n        element.addEventListener(\"focus\", (event)=>(0,_utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__.enableKeyboardPress)(event, eventOptions), eventOptions);\n    });\n    return cancelEvents;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementKeyboardAccessible: () => (/* binding */ isElementKeyboardAccessible)\n/* harmony export */ });\nconst focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\"\n]);\nfunction isElementKeyboardAccessible(element) {\n    return focusableElements.has(element.tagName) || element.tabIndex !== -1;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL2lzLWtleWJvYXJkLWFjY2Vzc2libGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxvQkFBb0IsSUFBSUMsSUFBSTtJQUM5QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0g7QUFDRCxTQUFTQyw0QkFBNEJDLE9BQU87SUFDeEMsT0FBUUgsa0JBQWtCSSxHQUFHLENBQUNELFFBQVFFLE9BQU8sS0FDekNGLFFBQVFHLFFBQVEsS0FBSyxDQUFDO0FBQzlCO0FBRXVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvaXMta2V5Ym9hcmQtYWNjZXNzaWJsZS5tanM/YTAzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmb2N1c2FibGVFbGVtZW50cyA9IG5ldyBTZXQoW1xuICAgIFwiQlVUVE9OXCIsXG4gICAgXCJJTlBVVFwiLFxuICAgIFwiU0VMRUNUXCIsXG4gICAgXCJURVhUQVJFQVwiLFxuICAgIFwiQVwiLFxuXSk7XG5mdW5jdGlvbiBpc0VsZW1lbnRLZXlib2FyZEFjY2Vzc2libGUoZWxlbWVudCkge1xuICAgIHJldHVybiAoZm9jdXNhYmxlRWxlbWVudHMuaGFzKGVsZW1lbnQudGFnTmFtZSkgfHxcbiAgICAgICAgZWxlbWVudC50YWJJbmRleCAhPT0gLTEpO1xufVxuXG5leHBvcnQgeyBpc0VsZW1lbnRLZXlib2FyZEFjY2Vzc2libGUgfTtcbiJdLCJuYW1lcyI6WyJmb2N1c2FibGVFbGVtZW50cyIsIlNldCIsImlzRWxlbWVudEtleWJvYXJkQWNjZXNzaWJsZSIsImVsZW1lbnQiLCJoYXMiLCJ0YWdOYW1lIiwidGFiSW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableKeyboardPress: () => (/* binding */ enableKeyboardPress)\n/* harmony export */ });\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */ function filterEvents(callback) {\n    return (event)=>{\n        if (event.key !== \"Enter\") return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, {\n        isPrimary: true,\n        bubbles: true\n    }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions)=>{\n    const element = focusEvent.currentTarget;\n    if (!element) return;\n    const handleKeydown = filterEvents(()=>{\n        if (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.isPressing.has(element)) return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(()=>{\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = ()=>firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */ element.addEventListener(\"blur\", ()=>element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPressing: () => (/* binding */ isPressing)\n/* harmony export */ });\nconst isPressing = new WeakSet();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3ByZXNzL3V0aWxzL3N0YXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsYUFBYSxJQUFJQztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvc3RhdGUubWpzP2EwNTciXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQcmVzc2luZyA9IG5ldyBXZWFrU2V0KCk7XG5cbmV4cG9ydCB7IGlzUHJlc3NpbmcgfTtcbiJdLCJuYW1lcyI6WyJpc1ByZXNzaW5nIiwiV2Vha1NldCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: () => (/* binding */ isNodeOrChild)\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */ const isNodeOrChild = (parent, child)=>{\n    if (!child) {\n        return false;\n    } else if (parent === child) {\n        return true;\n    } else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLW5vZGUtb3ItY2hpbGQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Ozs7O0NBTUMsR0FDRCxNQUFNQSxnQkFBZ0IsQ0FBQ0MsUUFBUUM7SUFDM0IsSUFBSSxDQUFDQSxPQUFPO1FBQ1IsT0FBTztJQUNYLE9BQ0ssSUFBSUQsV0FBV0MsT0FBTztRQUN2QixPQUFPO0lBQ1gsT0FDSztRQUNELE9BQU9GLGNBQWNDLFFBQVFDLE1BQU1DLGFBQWE7SUFDcEQ7QUFDSjtBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLW5vZGUtb3ItY2hpbGQubWpzPzY4YjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZWN1cnNpdmVseSB0cmF2ZXJzZSB1cCB0aGUgdHJlZSB0byBjaGVjayB3aGV0aGVyIHRoZSBwcm92aWRlZCBjaGlsZCBub2RlXG4gKiBpcyB0aGUgcGFyZW50IG9yIGEgZGVzY2VuZGFudCBvZiBpdC5cbiAqXG4gKiBAcGFyYW0gcGFyZW50IC0gRWxlbWVudCB0byBmaW5kXG4gKiBAcGFyYW0gY2hpbGQgLSBFbGVtZW50IHRvIHRlc3QgYWdhaW5zdCBwYXJlbnRcbiAqL1xuY29uc3QgaXNOb2RlT3JDaGlsZCA9IChwYXJlbnQsIGNoaWxkKSA9PiB7XG4gICAgaWYgKCFjaGlsZCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGVsc2UgaWYgKHBhcmVudCA9PT0gY2hpbGQpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gaXNOb2RlT3JDaGlsZChwYXJlbnQsIGNoaWxkLnBhcmVudEVsZW1lbnQpO1xuICAgIH1cbn07XG5cbmV4cG9ydCB7IGlzTm9kZU9yQ2hpbGQgfTtcbiJdLCJuYW1lcyI6WyJpc05vZGVPckNoaWxkIiwicGFyZW50IiwiY2hpbGQiLCJwYXJlbnRFbGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: () => (/* binding */ isPrimaryPointer)\n/* harmony export */ });\nconst isPrimaryPointer = (event)=>{\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    } else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */ return event.isPrimary !== false;\n    }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLE1BQU1BLG1CQUFtQixDQUFDQztJQUN0QixJQUFJQSxNQUFNQyxXQUFXLEtBQUssU0FBUztRQUMvQixPQUFPLE9BQU9ELE1BQU1FLE1BQU0sS0FBSyxZQUFZRixNQUFNRSxNQUFNLElBQUk7SUFDL0QsT0FDSztRQUNEOzs7Ozs7O1NBT0MsR0FDRCxPQUFPRixNQUFNRyxTQUFTLEtBQUs7SUFDL0I7QUFDSjtBQUU0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL2lzLXByaW1hcnktcG9pbnRlci5tanM/Nzg4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1ByaW1hcnlQb2ludGVyID0gKGV2ZW50KSA9PiB7XG4gICAgaWYgKGV2ZW50LnBvaW50ZXJUeXBlID09PSBcIm1vdXNlXCIpIHtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiBldmVudC5idXR0b24gIT09IFwibnVtYmVyXCIgfHwgZXZlbnQuYnV0dG9uIDw9IDA7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvKipcbiAgICAgICAgICogaXNQcmltYXJ5IGlzIHRydWUgZm9yIGFsbCBtaWNlIGJ1dHRvbnMsIHdoZXJlYXMgZXZlcnkgdG91Y2ggcG9pbnRcbiAgICAgICAgICogaXMgcmVnYXJkZWQgYXMgaXRzIG93biBpbnB1dC4gU28gc3Vic2VxdWVudCBjb25jdXJyZW50IHRvdWNoIHBvaW50c1xuICAgICAgICAgKiB3aWxsIGJlIGZhbHNlLlxuICAgICAgICAgKlxuICAgICAgICAgKiBTcGVjaWZpY2FsbHkgbWF0Y2ggYWdhaW5zdCBmYWxzZSBoZXJlIGFzIGluY29tcGxldGUgdmVyc2lvbnMgb2ZcbiAgICAgICAgICogUG9pbnRlckV2ZW50cyBpbiB2ZXJ5IG9sZCBicm93c2VyIG1pZ2h0IGhhdmUgaXQgc2V0IGFzIHVuZGVmaW5lZC5cbiAgICAgICAgICovXG4gICAgICAgIHJldHVybiBldmVudC5pc1ByaW1hcnkgIT09IGZhbHNlO1xuICAgIH1cbn07XG5cbmV4cG9ydCB7IGlzUHJpbWFyeVBvaW50ZXIgfTtcbiJdLCJuYW1lcyI6WyJpc1ByaW1hcnlQb2ludGVyIiwiZXZlbnQiLCJwb2ludGVyVHlwZSIsImJ1dHRvbiIsImlzUHJpbWFyeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupGesture: () => (/* binding */ setupGesture)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal\n    };\n    const cancel = ()=>gestureAbortController.abort();\n    return [\n        elements,\n        eventOptions,\n        cancel\n    ];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL3NldHVwLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRTtBQUVuRSxTQUFTQyxhQUFhQyxpQkFBaUIsRUFBRUMsT0FBTztJQUM1QyxNQUFNQyxXQUFXSiw0RUFBZUEsQ0FBQ0U7SUFDakMsTUFBTUcseUJBQXlCLElBQUlDO0lBQ25DLE1BQU1DLGVBQWU7UUFDakJDLFNBQVM7UUFDVCxHQUFHTCxPQUFPO1FBQ1ZNLFFBQVFKLHVCQUF1QkksTUFBTTtJQUN6QztJQUNBLE1BQU1DLFNBQVMsSUFBTUwsdUJBQXVCTSxLQUFLO0lBQ2pELE9BQU87UUFBQ1A7UUFBVUc7UUFBY0c7S0FBTztBQUMzQztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2dlc3R1cmVzL3V0aWxzL3NldHVwLm1qcz8wMjhjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc29sdmVFbGVtZW50cyB9IGZyb20gJy4uLy4uL3V0aWxzL3Jlc29sdmUtZWxlbWVudHMubWpzJztcblxuZnVuY3Rpb24gc2V0dXBHZXN0dXJlKGVsZW1lbnRPclNlbGVjdG9yLCBvcHRpb25zKSB7XG4gICAgY29uc3QgZWxlbWVudHMgPSByZXNvbHZlRWxlbWVudHMoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgIGNvbnN0IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgZXZlbnRPcHRpb25zID0ge1xuICAgICAgICBwYXNzaXZlOiB0cnVlLFxuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBzaWduYWw6IGdlc3R1cmVBYm9ydENvbnRyb2xsZXIuc2lnbmFsLFxuICAgIH07XG4gICAgY29uc3QgY2FuY2VsID0gKCkgPT4gZ2VzdHVyZUFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgIHJldHVybiBbZWxlbWVudHMsIGV2ZW50T3B0aW9ucywgY2FuY2VsXTtcbn1cblxuZXhwb3J0IHsgc2V0dXBHZXN0dXJlIH07XG4iXSwibmFtZXMiOlsicmVzb2x2ZUVsZW1lbnRzIiwic2V0dXBHZXN0dXJlIiwiZWxlbWVudE9yU2VsZWN0b3IiLCJvcHRpb25zIiwiZWxlbWVudHMiLCJnZXN0dXJlQWJvcnRDb250cm9sbGVyIiwiQWJvcnRDb250cm9sbGVyIiwiZXZlbnRPcHRpb25zIiwicGFzc2l2ZSIsInNpZ25hbCIsImNhbmNlbCIsImFib3J0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupPlaybackControls: () => (/* reexport safe */ _animation_controls_Group_mjs__WEBPACK_IMPORTED_MODULE_0__.GroupPlaybackControls),\n/* harmony export */   NativeAnimationControls: () => (/* reexport safe */ _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_5__.NativeAnimationControls),\n/* harmony export */   ViewTransitionBuilder: () => (/* reexport safe */ _view_index_mjs__WEBPACK_IMPORTED_MODULE_16__.ViewTransitionBuilder),\n/* harmony export */   attachTimeline: () => (/* reexport safe */ _animation_waapi_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_6__.attachTimeline),\n/* harmony export */   calcGeneratorDuration: () => (/* reexport safe */ _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_2__.calcGeneratorDuration),\n/* harmony export */   createGeneratorEasing: () => (/* reexport safe */ _animation_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_3__.createGeneratorEasing),\n/* harmony export */   cubicBezierAsString: () => (/* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.cubicBezierAsString),\n/* harmony export */   generateLinearEasing: () => (/* reexport safe */ _animation_waapi_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_8__.generateLinearEasing),\n/* harmony export */   getValueTransition: () => (/* reexport safe */ _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_1__.getValueTransition),\n/* harmony export */   hover: () => (/* reexport safe */ _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_9__.hover),\n/* harmony export */   isBezierDefinition: () => (/* reexport safe */ _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_11__.isBezierDefinition),\n/* harmony export */   isDragActive: () => (/* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_17__.isDragActive),\n/* harmony export */   isDragging: () => (/* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_17__.isDragging),\n/* harmony export */   isGenerator: () => (/* reexport safe */ _animation_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_4__.isGenerator),\n/* harmony export */   isNodeOrChild: () => (/* reexport safe */ _gestures_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_19__.isNodeOrChild),\n/* harmony export */   isPrimaryPointer: () => (/* reexport safe */ _gestures_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_20__.isPrimaryPointer),\n/* harmony export */   isWaapiSupportedEasing: () => (/* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.isWaapiSupportedEasing),\n/* harmony export */   mapEasingToNativeEasing: () => (/* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.mapEasingToNativeEasing),\n/* harmony export */   maxGeneratorDuration: () => (/* reexport safe */ _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_2__.maxGeneratorDuration),\n/* harmony export */   press: () => (/* reexport safe */ _gestures_press_index_mjs__WEBPACK_IMPORTED_MODULE_10__.press),\n/* harmony export */   resolveElements: () => (/* reexport safe */ _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_12__.resolveElements),\n/* harmony export */   setDragLock: () => (/* reexport safe */ _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_18__.setDragLock),\n/* harmony export */   supportedWaapiEasing: () => (/* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.supportedWaapiEasing),\n/* harmony export */   supportsFlags: () => (/* reexport safe */ _utils_supports_flags_mjs__WEBPACK_IMPORTED_MODULE_13__.supportsFlags),\n/* harmony export */   supportsLinearEasing: () => (/* reexport safe */ _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_14__.supportsLinearEasing),\n/* harmony export */   supportsScrollTimeline: () => (/* reexport safe */ _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_15__.supportsScrollTimeline),\n/* harmony export */   view: () => (/* reexport safe */ _view_index_mjs__WEBPACK_IMPORTED_MODULE_16__.view)\n/* harmony export */ });\n/* harmony import */ var _animation_controls_Group_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./animation/controls/Group.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/controls/Group.mjs\");\n/* harmony import */ var _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation/utils/get-value-transition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\");\n/* harmony import */ var _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animation/generators/utils/calc-duration.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n/* harmony import */ var _animation_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./animation/generators/utils/create-generator-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\");\n/* harmony import */ var _animation_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./animation/generators/utils/is-generator.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n/* harmony import */ var _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./animation/waapi/NativeAnimationControls.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\");\n/* harmony import */ var _animation_waapi_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./animation/waapi/utils/attach-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\");\n/* harmony import */ var _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./animation/waapi/utils/easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\");\n/* harmony import */ var _animation_waapi_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./animation/waapi/utils/linear.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./gestures/hover.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _gestures_press_index_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./gestures/press/index.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\");\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utils/is-bezier-definition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utils/resolve-elements.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n/* harmony import */ var _utils_supports_flags_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./utils/supports/flags.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/supports/linear-easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./utils/supports/scroll-timeline.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n/* harmony import */ var _view_index_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./view/index.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/index.mjs\");\n/* harmony import */ var _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./gestures/drag/state/is-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./gestures/drag/state/set-active.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\");\n/* harmony import */ var _gestures_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./gestures/utils/is-node-or-child.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _gestures_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./gestures/utils/is-primary-pointer.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBezierDefinition: () => (/* binding */ isBezierDefinition)\n/* harmony export */ });\nconst isBezierDefinition = (easing)=>Array.isArray(easing) && typeof easing[0] === \"number\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL2lzLWJlemllci1kZWZpbml0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEscUJBQXFCLENBQUNDLFNBQVdDLE1BQU1DLE9BQU8sQ0FBQ0YsV0FBVyxPQUFPQSxNQUFNLENBQUMsRUFBRSxLQUFLO0FBRXZEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvaXMtYmV6aWVyLWRlZmluaXRpb24ubWpzPzZiNzgiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNCZXppZXJEZWZpbml0aW9uID0gKGVhc2luZykgPT4gQXJyYXkuaXNBcnJheShlYXNpbmcpICYmIHR5cGVvZiBlYXNpbmdbMF0gPT09IFwibnVtYmVyXCI7XG5cbmV4cG9ydCB7IGlzQmV6aWVyRGVmaW5pdGlvbiB9O1xuIl0sIm5hbWVzIjpbImlzQmV6aWVyRGVmaW5pdGlvbiIsImVhc2luZyIsIkFycmF5IiwiaXNBcnJheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\nfunction resolveElements(elementOrSelector, scope, selectorCache) {\n    var _a;\n    if (elementOrSelector instanceof Element) {\n        return [\n            elementOrSelector\n        ];\n    } else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            // TODO: Refactor to utils package\n            // invariant(\n            //     Boolean(scope.current),\n            //     \"Scope provided, but no element detected.\"\n            // )\n            root = scope.current;\n        }\n        const elements = (_a = selectorCache === null || selectorCache === void 0 ? void 0 : selectorCache[elementOrSelector]) !== null && _a !== void 0 ? _a : root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/flags.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsFlags: () => (/* binding */ supportsFlags)\n/* harmony export */ });\n/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */ const supportsFlags = {\n    linearEasing: undefined\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2ZsYWdzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBQ0QsTUFBTUEsZ0JBQWdCO0lBQ2xCQyxjQUFjQztBQUNsQjtBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2ZsYWdzLm1qcz81NzZmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQWRkIHRoZSBhYmlsaXR5IGZvciB0ZXN0IHN1aXRlcyB0byBtYW51YWxseSBzZXQgc3VwcG9ydCBmbGFnc1xuICogdG8gYmV0dGVyIHRlc3QgbW9yZSBlbnZpcm9ubWVudHMuXG4gKi9cbmNvbnN0IHN1cHBvcnRzRmxhZ3MgPSB7XG4gICAgbGluZWFyRWFzaW5nOiB1bmRlZmluZWQsXG59O1xuXG5leHBvcnQgeyBzdXBwb3J0c0ZsYWdzIH07XG4iXSwibmFtZXMiOlsic3VwcG9ydHNGbGFncyIsImxpbmVhckVhc2luZyIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsLinearEasing: () => (/* binding */ supportsLinearEasing)\n/* harmony export */ });\n/* harmony import */ var _memo_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./memo.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\");\n\nconst supportsLinearEasing = /*@__PURE__*/ (0,_memo_mjs__WEBPACK_IMPORTED_MODULE_0__.memoSupports)(()=>{\n    try {\n        document.createElement(\"div\").animate({\n            opacity: 0\n        }, {\n            easing: \"linear(0, 1)\"\n        });\n    } catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL2xpbmVhci1lYXNpbmcubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDO0FBRTFDLE1BQU1DLHVCQUF1QixXQUFXLEdBQUdELHVEQUFZQSxDQUFDO0lBQ3BELElBQUk7UUFDQUUsU0FDS0MsYUFBYSxDQUFDLE9BQ2RDLE9BQU8sQ0FBQztZQUFFQyxTQUFTO1FBQUUsR0FBRztZQUFFQyxRQUFRO1FBQWU7SUFDMUQsRUFDQSxPQUFPQyxHQUFHO1FBQ04sT0FBTztJQUNYO0lBQ0EsT0FBTztBQUNYLEdBQUc7QUFFNkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy91dGlscy9zdXBwb3J0cy9saW5lYXItZWFzaW5nLm1qcz80Y2M1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW9TdXBwb3J0cyB9IGZyb20gJy4vbWVtby5tanMnO1xuXG5jb25zdCBzdXBwb3J0c0xpbmVhckVhc2luZyA9IC8qQF9fUFVSRV9fKi8gbWVtb1N1cHBvcnRzKCgpID0+IHtcbiAgICB0cnkge1xuICAgICAgICBkb2N1bWVudFxuICAgICAgICAgICAgLmNyZWF0ZUVsZW1lbnQoXCJkaXZcIilcbiAgICAgICAgICAgIC5hbmltYXRlKHsgb3BhY2l0eTogMCB9LCB7IGVhc2luZzogXCJsaW5lYXIoMCwgMSlcIiB9KTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn0sIFwibGluZWFyRWFzaW5nXCIpO1xuXG5leHBvcnQgeyBzdXBwb3J0c0xpbmVhckVhc2luZyB9O1xuIl0sIm5hbWVzIjpbIm1lbW9TdXBwb3J0cyIsInN1cHBvcnRzTGluZWFyRWFzaW5nIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiYW5pbWF0ZSIsIm9wYWNpdHkiLCJlYXNpbmciLCJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/memo.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoSupports: () => (/* binding */ memoSupports)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _flags_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./flags.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(callback);\n    return ()=>{\n        var _a;\n        return (_a = _flags_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsFlags[supportsFlag]) !== null && _a !== void 0 ? _a : memoized();\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL21lbW8ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvQztBQUNRO0FBRTVDLFNBQVNFLGFBQWFDLFFBQVEsRUFBRUMsWUFBWTtJQUN4QyxNQUFNQyxXQUFXTCxrREFBSUEsQ0FBQ0c7SUFDdEIsT0FBTztRQUFRLElBQUlHO1FBQUksT0FBTyxDQUFDQSxLQUFLTCxxREFBYSxDQUFDRyxhQUFhLE1BQU0sUUFBUUUsT0FBTyxLQUFLLElBQUlBLEtBQUtEO0lBQVk7QUFDbEg7QUFFd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy91dGlscy9zdXBwb3J0cy9tZW1vLm1qcz80Y2RkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW8gfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgc3VwcG9ydHNGbGFncyB9IGZyb20gJy4vZmxhZ3MubWpzJztcblxuZnVuY3Rpb24gbWVtb1N1cHBvcnRzKGNhbGxiYWNrLCBzdXBwb3J0c0ZsYWcpIHtcbiAgICBjb25zdCBtZW1vaXplZCA9IG1lbW8oY2FsbGJhY2spO1xuICAgIHJldHVybiAoKSA9PiB7IHZhciBfYTsgcmV0dXJuIChfYSA9IHN1cHBvcnRzRmxhZ3Nbc3VwcG9ydHNGbGFnXSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogbWVtb2l6ZWQoKTsgfTtcbn1cblxuZXhwb3J0IHsgbWVtb1N1cHBvcnRzIH07XG4iXSwibmFtZXMiOlsibWVtbyIsInN1cHBvcnRzRmxhZ3MiLCJtZW1vU3VwcG9ydHMiLCJjYWxsYmFjayIsInN1cHBvcnRzRmxhZyIsIm1lbW9pemVkIiwiX2EiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsScrollTimeline: () => (/* binding */ supportsScrollTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n\nconst supportsScrollTimeline = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(()=>window.ScrollTimeline !== undefined);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3V0aWxzL3N1cHBvcnRzL3Njcm9sbC10aW1lbGluZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0M7QUFFcEMsTUFBTUMseUJBQXlCRCxrREFBSUEsQ0FBQyxJQUFNRSxPQUFPQyxjQUFjLEtBQUtDO0FBRWxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvc2Nyb2xsLXRpbWVsaW5lLm1qcz85NjE1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW8gfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuXG5jb25zdCBzdXBwb3J0c1Njcm9sbFRpbWVsaW5lID0gbWVtbygoKSA9PiB3aW5kb3cuU2Nyb2xsVGltZWxpbmUgIT09IHVuZGVmaW5lZCk7XG5cbmV4cG9ydCB7IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgfTtcbiJdLCJuYW1lcyI6WyJtZW1vIiwic3VwcG9ydHNTY3JvbGxUaW1lbGluZSIsIndpbmRvdyIsIlNjcm9sbFRpbWVsaW5lIiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewTransitionBuilder: () => (/* binding */ ViewTransitionBuilder),\n/* harmony export */   view: () => (/* binding */ view)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _start_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./start.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/start.mjs\");\n\n\n/**\n * TODO:\n * - Create view transition on next tick\n * - Replace animations with Motion animations\n * - Return GroupAnimation on next tick\n */ class ViewTransitionBuilder {\n    constructor(update, options = {}){\n        this.currentTarget = \"root\";\n        this.targets = new Map();\n        this.notifyReady = motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n        this.readyPromise = new Promise((resolve)=>{\n            this.notifyReady = resolve;\n        });\n        queueMicrotask(()=>{\n            (0,_start_mjs__WEBPACK_IMPORTED_MODULE_1__.startViewAnimation)(update, options, this.targets).then((animation)=>this.notifyReady(animation));\n        });\n    }\n    get(selector) {\n        this.currentTarget = selector;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", {\n            opacity: 1\n        }, options);\n        this.updateTarget(\"exit\", {\n            opacity: 0\n        }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentTarget, targets } = this;\n        if (!targets.has(currentTarget)) {\n            targets.set(currentTarget, {});\n        }\n        const targetData = targets.get(currentTarget);\n        targetData[target] = {\n            keyframes,\n            options\n        };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction view(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/start.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/start.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startViewAnimation: () => (/* binding */ startViewAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(ssr)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _animation_controls_BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../animation/controls/BaseGroup.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs\");\n/* harmony import */ var _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../animation/utils/get-value-transition.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\");\n/* harmony import */ var _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../animation/waapi/NativeAnimationControls.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\");\n/* harmony import */ var _animation_waapi_PseudoAnimation_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../animation/waapi/PseudoAnimation.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs\");\n/* harmony import */ var _animation_waapi_utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../animation/waapi/utils/convert-options.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs\");\n/* harmony import */ var _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../animation/waapi/utils/easing.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\");\n/* harmony import */ var _utils_choose_layer_type_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/choose-layer-type.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs\");\n/* harmony import */ var _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/css.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/css.mjs\");\n/* harmony import */ var _utils_get_layer_name_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/get-layer-name.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs\");\n/* harmony import */ var _utils_get_view_animations_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/get-view-animations.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs\");\n/* harmony import */ var _utils_has_target_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utils/has-target.mjs */ \"(ssr)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst definitionNames = [\n    \"layout\",\n    \"enter\",\n    \"exit\",\n    \"new\",\n    \"old\"\n];\nfunction startViewAnimation(update, defaultOptions, targets) {\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve)=>{\n            await update();\n            resolve(new _animation_controls_BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_1__.BaseGroupPlaybackControls([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */ if (!(0,_utils_has_target_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTarget)(\"root\", targets)) {\n        _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.set(\":root\", {\n            \"view-transition-name\": \"none\"\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */ _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", {\n        \"animation-timing-function\": \"linear !important\"\n    });\n    _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.commit(); // Write\n    const transition = document.startViewTransition(async ()=>{\n        await update();\n    // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(()=>{\n        _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.remove(); // Write\n    });\n    return new Promise((resolve)=>{\n        transition.ready.then(()=>{\n            var _a;\n            const generatedViewAnimations = (0,_utils_get_view_animations_mjs__WEBPACK_IMPORTED_MODULE_10__.getViewAnimations)();\n            const animations = [];\n            /**\n             * Create animations for our definitions\n             */ targets.forEach((definition, target)=>{\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames){\n                    if (!definition[key]) continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)){\n                        if (!valueKeyframes) continue;\n                        const valueOptions = {\n                            ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueTransition)(defaultOptions, valueName),\n                            ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueTransition)(options, valueName)\n                        };\n                        const type = (0,_utils_choose_layer_type_mjs__WEBPACK_IMPORTED_MODULE_7__.chooseLayerType)(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */ if (valueName === \"opacity\" && !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [\n                                initialValue,\n                                valueKeyframes\n                            ];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */ if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        const animation = new _animation_waapi_PseudoAnimation_mjs__WEBPACK_IMPORTED_MODULE_4__.PseudoAnimation(document.documentElement, `::view-transition-${type}(${target})`, valueName, valueKeyframes, valueOptions);\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */ for (const animation of generatedViewAnimations){\n                if (animation.playState === \"finished\") continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect)) continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement) continue;\n                const name = (0,_utils_get_layer_name_mjs__WEBPACK_IMPORTED_MODULE_9__.getLayerName)(pseudoElement);\n                if (!name) continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */ const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    const animationTransition = {\n                        ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueTransition)(defaultOptions, transitionName)\n                    };\n                    (0,_animation_waapi_utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_5__.applyGeneratorOptions)(animationTransition);\n                    const easing = (0,_animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_6__.mapEasingToNativeEasing)(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)((_a = animationTransition.delay) !== null && _a !== void 0 ? _a : 0),\n                        duration: animationTransition.duration,\n                        easing\n                    });\n                    animations.push(new _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_3__.NativeAnimationControls(animation));\n                } else if (hasOpacity(targetDefinition, \"enter\") && hasOpacity(targetDefinition, \"exit\") && effect.getKeyframes().some((keyframe)=>keyframe.mixBlendMode)) {\n                    animations.push(new _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_3__.NativeAnimationControls(animation));\n                } else {\n                    animation.cancel();\n                }\n            }\n            resolve(new _animation_controls_BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_1__.BaseGroupPlaybackControls(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    var _a;\n    return (_a = target === null || target === void 0 ? void 0 : target[key]) === null || _a === void 0 ? void 0 : _a.keyframes.opacity;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/start.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chooseLayerType: () => (/* binding */ chooseLayerType)\n/* harmony export */ });\nfunction chooseLayerType(valueName) {\n    if (valueName === \"layout\") return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\") return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\") return \"old\";\n    return \"group\";\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvY2hvb3NlLWxheWVyLXR5cGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxTQUFTQSxnQkFBZ0JDLFNBQVM7SUFDOUIsSUFBSUEsY0FBYyxVQUNkLE9BQU87SUFDWCxJQUFJQSxjQUFjLFdBQVdBLGNBQWMsT0FDdkMsT0FBTztJQUNYLElBQUlBLGNBQWMsVUFBVUEsY0FBYyxPQUN0QyxPQUFPO0lBQ1gsT0FBTztBQUNYO0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdmlldy91dGlscy9jaG9vc2UtbGF5ZXItdHlwZS5tanM/ZTFlNCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjaG9vc2VMYXllclR5cGUodmFsdWVOYW1lKSB7XG4gICAgaWYgKHZhbHVlTmFtZSA9PT0gXCJsYXlvdXRcIilcbiAgICAgICAgcmV0dXJuIFwiZ3JvdXBcIjtcbiAgICBpZiAodmFsdWVOYW1lID09PSBcImVudGVyXCIgfHwgdmFsdWVOYW1lID09PSBcIm5ld1wiKVxuICAgICAgICByZXR1cm4gXCJuZXdcIjtcbiAgICBpZiAodmFsdWVOYW1lID09PSBcImV4aXRcIiB8fCB2YWx1ZU5hbWUgPT09IFwib2xkXCIpXG4gICAgICAgIHJldHVybiBcIm9sZFwiO1xuICAgIHJldHVybiBcImdyb3VwXCI7XG59XG5cbmV4cG9ydCB7IGNob29zZUxheWVyVHlwZSB9O1xuIl0sIm5hbWVzIjpbImNob29zZUxheWVyVHlwZSIsInZhbHVlTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/css.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/css.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css)\n/* harmony export */ });\nlet pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values)=>{\n        pendingRules[selector] = values;\n    },\n    commit: ()=>{\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for(const selector in pendingRules){\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)){\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: ()=>{\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/css.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLayerName: () => (/* binding */ getLayerName)\n/* harmony export */ });\nfunction getLayerName(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match) return null;\n    return {\n        layer: match[2],\n        type: match[1]\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LWxheWVyLW5hbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxTQUFTQSxhQUFhQyxhQUFhO0lBQy9CLE1BQU1DLFFBQVFELGNBQWNDLEtBQUssQ0FBQztJQUNsQyxJQUFJLENBQUNBLE9BQ0QsT0FBTztJQUNYLE9BQU87UUFBRUMsT0FBT0QsS0FBSyxDQUFDLEVBQUU7UUFBRUUsTUFBTUYsS0FBSyxDQUFDLEVBQUU7SUFBQztBQUM3QztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LWxheWVyLW5hbWUubWpzPzEyNzUiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZ2V0TGF5ZXJOYW1lKHBzZXVkb0VsZW1lbnQpIHtcbiAgICBjb25zdCBtYXRjaCA9IHBzZXVkb0VsZW1lbnQubWF0Y2goLzo6dmlldy10cmFuc2l0aW9uLShvbGR8bmV3fGdyb3VwfGltYWdlLXBhaXIpXFwoKC4qPylcXCkvKTtcbiAgICBpZiAoIW1hdGNoKVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICByZXR1cm4geyBsYXllcjogbWF0Y2hbMl0sIHR5cGU6IG1hdGNoWzFdIH07XG59XG5cbmV4cG9ydCB7IGdldExheWVyTmFtZSB9O1xuIl0sIm5hbWVzIjpbImdldExheWVyTmFtZSIsInBzZXVkb0VsZW1lbnQiLCJtYXRjaCIsImxheWVyIiwidHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getViewAnimations: () => (/* binding */ getViewAnimations)\n/* harmony export */ });\nfunction filterViewAnimations(animation) {\n    var _a;\n    const { effect } = animation;\n    if (!effect) return false;\n    return effect.target === document.documentElement && ((_a = effect.pseudoElement) === null || _a === void 0 ? void 0 : _a.startsWith(\"::view-transition\"));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LXZpZXctYW5pbWF0aW9ucy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBLHFCQUFxQkMsU0FBUztJQUNuQyxJQUFJQztJQUNKLE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUdGO0lBQ25CLElBQUksQ0FBQ0UsUUFDRCxPQUFPO0lBQ1gsT0FBUUEsT0FBT0MsTUFBTSxLQUFLQyxTQUFTQyxlQUFlLElBQzdDLEVBQUNKLEtBQUtDLE9BQU9JLGFBQWEsTUFBTSxRQUFRTCxPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUdNLFVBQVUsQ0FBQyxvQkFBbUI7QUFDM0c7QUFDQSxTQUFTQztJQUNMLE9BQU9KLFNBQVNLLGFBQWEsR0FBR0MsTUFBTSxDQUFDWDtBQUMzQztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LXZpZXctYW5pbWF0aW9ucy5tanM/M2QxMSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBmaWx0ZXJWaWV3QW5pbWF0aW9ucyhhbmltYXRpb24pIHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3QgeyBlZmZlY3QgfSA9IGFuaW1hdGlvbjtcbiAgICBpZiAoIWVmZmVjdClcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIHJldHVybiAoZWZmZWN0LnRhcmdldCA9PT0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50ICYmXG4gICAgICAgICgoX2EgPSBlZmZlY3QucHNldWRvRWxlbWVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnN0YXJ0c1dpdGgoXCI6OnZpZXctdHJhbnNpdGlvblwiKSkpO1xufVxuZnVuY3Rpb24gZ2V0Vmlld0FuaW1hdGlvbnMoKSB7XG4gICAgcmV0dXJuIGRvY3VtZW50LmdldEFuaW1hdGlvbnMoKS5maWx0ZXIoZmlsdGVyVmlld0FuaW1hdGlvbnMpO1xufVxuXG5leHBvcnQgeyBnZXRWaWV3QW5pbWF0aW9ucyB9O1xuIl0sIm5hbWVzIjpbImZpbHRlclZpZXdBbmltYXRpb25zIiwiYW5pbWF0aW9uIiwiX2EiLCJlZmZlY3QiLCJ0YXJnZXQiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsInBzZXVkb0VsZW1lbnQiLCJzdGFydHNXaXRoIiwiZ2V0Vmlld0FuaW1hdGlvbnMiLCJnZXRBbmltYXRpb25zIiwiZmlsdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/has-target.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasTarget: () => (/* binding */ hasTarget)\n/* harmony export */ });\nfunction hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvaGFzLXRhcmdldC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLFNBQVNBLFVBQVVDLE1BQU0sRUFBRUMsT0FBTztJQUM5QixPQUFPQSxRQUFRQyxHQUFHLENBQUNGLFdBQVdHLE9BQU9DLElBQUksQ0FBQ0gsUUFBUUksR0FBRyxDQUFDTCxTQUFTTSxNQUFNLEdBQUc7QUFDNUU7QUFFcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy92aWV3L3V0aWxzL2hhcy10YXJnZXQubWpzP2YzZjgiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaGFzVGFyZ2V0KHRhcmdldCwgdGFyZ2V0cykge1xuICAgIHJldHVybiB0YXJnZXRzLmhhcyh0YXJnZXQpICYmIE9iamVjdC5rZXlzKHRhcmdldHMuZ2V0KHRhcmdldCkpLmxlbmd0aCA+IDA7XG59XG5cbmV4cG9ydCB7IGhhc1RhcmdldCB9O1xuIl0sIm5hbWVzIjpbImhhc1RhcmdldCIsInRhcmdldCIsInRhcmdldHMiLCJoYXMiLCJPYmplY3QiLCJrZXlzIiwiZ2V0IiwibGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs\n");

/***/ })

};
;