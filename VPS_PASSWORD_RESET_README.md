# VPS Password Reset Feature Documentation

## Overview
This document provides a comprehensive guide to the VPS password reset functionality, including all components, code flow, API interactions, and how passwords are securely handled and reset through the Contabo API.

## Architecture Overview

The VPS password reset feature follows a multi-layer architecture:

```
Frontend (React) → Backend API → VPS Service → Contabo Provider → Contabo API
```

## Components Involved

### 1. Frontend Components

#### PasswordResetModal.jsx
**Location**: `frontend/src/app/components/PasswordResetModal.jsx`

**Purpose**: React modal component for VPS password reset interface

**Key Features**:
- Password validation according to Contabo requirements
- Secure password generation
- Form validation and error handling
- Real-time password strength checking

**Password Requirements (Contabo)**:
- Minimum 8 characters, maximum 30 characters
- At least one uppercase letter (A-Z)
- At least one lowercase letter (a-z)
- Either: (1 number + 2 special chars) OR (3 numbers + 1 special char)
- Allowed special characters: `!@#$^&*?_~`
- No other characters allowed

**Key Functions**:
```javascript
// Validates password against Contabo requirements
validateForm()

// Generates secure password meeting all requirements
generateSecurePassword()

// Handles form submission and API call
handleSubmit()
```

#### VPS Service (Frontend)
**Location**: `frontend/src/app/services/vpsService.js`

**Purpose**: Frontend API service for VPS operations

**Key Function**:
```javascript
resetVPSPassword: (instanceId, resetData) => {
  return apiService.post(`/api/vps/instances/${instanceId}/reset-password`, resetData, { withCredentials: true });
}
```

### 2. Backend Components

#### VPS Router
**Location**: `backend/routes/vpsRouter.js`

**Purpose**: Defines API routes for VPS operations

**Password Reset Route**:
```javascript
vpsRouter.post('/instances/:instanceId/reset-password',
  fetchUserMiddleware,
  validateInstanceId,
  rateLimitVPSOperations,
  resetVPSPassword
);
```

**Middleware Applied**:
- `fetchUserMiddleware`: Authenticates user
- `validateInstanceId`: Validates instance ID format
- `rateLimitVPSOperations`: Rate limiting for VPS operations

#### VPS Controller
**Location**: `backend/controllers/vpsController.js`

**Purpose**: Handles HTTP requests and responses for VPS operations

**Key Function**: `resetVPSPassword(req, res)`

**Responsibilities**:
1. Extract instanceId and resetData from request
2. Validate password format and requirements
3. Call VPS service for password reset
4. Return success/error response

**Password Validation Logic**:
```javascript
// Validates password length (8-30 characters)
if (resetData.rootPassword.length < 8 || resetData.rootPassword.length > 30) {
  return res.status(400).json({
    success: false,
    message: 'Password must be between 8 and 30 characters'
  });
}

// Validates allowed characters
if (!/^[a-zA-Z0-9!@#$^&*?_~]+$/.test(resetData.rootPassword)) {
  return res.status(400).json({
    success: false,
    message: 'Password contains invalid characters. Allowed: a-z, A-Z, 0-9, !@#$^&*?_~'
  });
}

// Validates Contabo requirements
const hasUppercase = /[A-Z]/.test(password);
const hasLowercase = /[a-z]/.test(password);
const numberCount = (password.match(/[0-9]/g) || []).length;
const specialCount = (password.match(/[!@#$^&*?_~]/g) || []).length;
const meetsNumberSpecialReq = (numberCount >= 1 && specialCount >= 2) ||
                             (numberCount >= 3 && specialCount >= 1);
```

#### VPS Service
**Location**: `backend/services/vpsService.js`

**Purpose**: Business logic layer for VPS operations

**Key Function**: `resetVPSPassword(instanceId, resetData)`

**Responsibilities**:
1. Validate and convert instance ID
2. Call Contabo provider for password reset
3. Update VPS instance in database
4. Handle errors and logging

**Database Update**:
```javascript
const updateData = {
  'access.rootPassword': resetData.rootPassword,
  'access.lastPasswordReset': new Date(),
  'access.passwordSecretId': result.passwordSecretId,
  updatedAt: new Date()
};
```

#### Contabo Provider
**Location**: `backend/services/providers/ContaboProvider.js`

**Purpose**: Direct integration with Contabo API

**Key Function**: `resetPassword(instanceId, resetData)`

**Responsibilities**:
1. Validate instance exists and check status
2. Convert plain text password to Contabo secret
3. Make API call to Contabo
4. Handle response and errors

## Password Reset Flow

### Step 1: Frontend Initiation
1. User opens PasswordResetModal for a VPS instance
2. User enters new password or generates secure password
3. Frontend validates password against Contabo requirements
4. Form submission triggers API call to backend

### Step 2: Backend Processing
1. **Router**: Receives POST request at `/api/vps/instances/:instanceId/reset-password`
2. **Middleware**: Authenticates user, validates instance ID, applies rate limiting
3. **Controller**: Validates password format and requirements
4. **Service**: Processes business logic and calls Contabo provider

### Step 3: Contabo Integration
1. **Instance Verification**: Checks if VPS instance exists and its status
2. **Password Secret Creation**: Converts plain text password to Contabo secret
3. **API Call**: Makes POST request to Contabo API endpoint
4. **Response Processing**: Handles Contabo response and validates success

### Step 4: Database Update
1. Updates VPS instance record with new password information
2. Stores password secret ID from Contabo
3. Records timestamp of password reset

### Step 5: Response
1. Returns success response with operation details
2. Frontend closes modal and shows success message

## Contabo API Integration

### Secret Management
Contabo requires passwords to be stored as "secrets" before use:

```javascript
// Create password secret
const secretName = `VPS-${instanceId}-Reset-${Date.now()}`;
const passwordSecretId = await this.createPasswordSecret(
  resetData.rootPassword,
  secretName
);
```

### API Endpoint
```
POST /compute/instances/{instanceId}/actions/resetPassword
```

### Payload Structure
```json
{
  "rootPassword": 12345,  // Secret ID (number)
  "sshKeys": [67890],     // Optional: Array of SSH key secret IDs
  "userData": "..."       // Optional: Cloud-init configuration
}
```

### Response Structure
```json
{
  "data": [
    {
      "instanceId": 12345,
      "action": "resetPassword"
    }
  ],
  "_links": {
    "self": {
      "href": "..."
    }
  }
}
```

## Security Considerations

### Password Handling
1. **Frontend**: Passwords are validated but not stored
2. **Backend**: Passwords are logged as `***HIDDEN***` in logs
3. **Contabo**: Passwords are converted to secrets immediately
4. **Database**: Original password is stored (consider encryption)

### Authentication & Authorization
1. User must be authenticated to access password reset
2. Rate limiting prevents abuse
3. Instance ownership validation (implicit through user context)

### Validation
1. **Frontend**: Real-time validation with user feedback
2. **Backend**: Server-side validation as security layer
3. **Contabo**: Final validation by provider API

## Error Handling

### Common Error Scenarios
1. **Invalid Password Format**: Caught by frontend and backend validation
2. **Instance Not Found**: Handled by Contabo provider
3. **Instance in Wrong State**: Warning logged, operation may fail
4. **Network Errors**: Proper error propagation to frontend
5. **Rate Limiting**: HTTP 429 responses

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message",
  "details": {
    "instanceId": "12345",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## Testing Considerations

### Unit Tests
- Password validation logic
- API endpoint responses
- Error handling scenarios

### Integration Tests
- End-to-end password reset flow
- Contabo API integration
- Database updates

### Manual Testing
- UI/UX validation
- Password generation functionality
- Error message display

## Logging

The system has been cleaned up to minimize logging. Only essential logs remain:
- **Contabo API Response**: The raw response from Contabo API is logged for debugging purposes
- **Critical Errors**: Only essential error information is logged

**Removed Logs**:
- Request initiation and parameters
- Password validation steps (except critical errors)
- Database update confirmations
- Verbose debugging information
- Step-by-step process logs

**Remaining Log Example**:
```javascript
console.log(`📥 CONTABO API RESPONSE:`, JSON.stringify(response, null, 2));
```

This provides the essential information needed to debug Contabo API interactions while keeping the logs clean and focused.
