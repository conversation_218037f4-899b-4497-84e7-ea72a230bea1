"use client";
import {
  LayoutDashboard,
  Users,
  Package,
  FileText,
  Image,
  LogOut,
  Settings,
  Mail,
  MessageSquare,
  Briefcase,
  PackageCheck,
  LogsIcon,
  Bell,
  Headset,
  HardDrive,
  Globe,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { Tooltip } from "@material-tailwind/react";
import { useAuth } from "../context/AuthContext";

function Sidebar({ collapsed = false }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);
  const { logout } = useAuth();

  // Set mounted state on client-side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Normalize the pathname to handle trailing slashes
  const normalizedPathname = pathname?.endsWith("/")
    ? pathname.slice(0, -1)
    : pathname;

  const [stats, setStats] = useState({
    admins: 5,
    users: 100,
    packages: 9,
    pages: 20,
    posts: 30,
    orders: 40,
    support: 10,
    jobs: 5,
    vpsImages: 25,
    vpsRegions: 12,
    revenue: 50000,
    systemStatus: {
      cpu: 45.5,
      memory: 60.3,
      storage: 70.2,
      uptime: "99.99%",
    },
  });

  const menuItems = [
    { icon: LayoutDashboard, label: "Overview", value: "/", count: null },
    { icon: Users, label: "Users", value: "users", count: stats.admins },
    {
      icon: Package,
      label: "Packages",
      value: "packages",
      count: stats.packages,
    },
    {
      icon: HardDrive,
      label: "VPS Images",
      value: "vps-images",
      count: stats.vpsImages,
    },
    {
      icon: Globe,
      label: "VPS Regions",
      value: "vps-regions",
      count: stats.vpsRegions,
    },
    // { icon: FileText, label: "Pages", value: "pages", count: stats.pages },
    { icon: LogsIcon, label: "Activity Logs", value: "activity-logs", count: stats.posts },
    { icon: PackageCheck, label: "Orders", value: "orders", count: stats.orders },
    {
      icon: Headset,
      label: "Support",
      value: "support",
      count: stats.support,
    },
    {
      icon: MessageSquare,
      label: "Chats",
      value: "chats",
      count: stats.support,
    },
    { icon: Briefcase, label: "Jobs", value: "jobs", count: stats.jobs },
    // { icon: Mail, label: "Marketing", value: "marketing", count: null },
    { icon: Bell, label: "Notification Settings", value: "notification-settings", count: null },
    { icon: Settings, label: "Settings", value: "settings", count: null },
  ];

  const handleLogout = async () => {
    try {
      // Use the logout function from AuthContext
      await logout();
    } catch (error) {
      console.error("Logout failed:", error);
      // Fallback to redirect if logout fails
      router.push("/admin/login");
    }
  };

  const handleNavigation = (path) => {
    router.push(`/admin/${path === "/" ? "" : path}`);
  };

  // Prevent hydration errors by not rendering until client-side
  if (!isMounted) {
    return <div className="h-full"></div>;
  }

  return (
    <div className="flex flex-col h-full overflow-y-auto">
      <nav className={`flex-1 ${collapsed ? 'px-2' : 'px-4'} space-y-1 py-2`}>
        {menuItems.map((item) => {
          const isActive =
            (item.value === "/" && normalizedPathname === "/admin") ||
            (item.value !== "/" && normalizedPathname?.includes(`/admin/${item.value}`));

          const MenuItem = (
            <div
              key={item.value}
              onClick={() => handleNavigation(item.value)}
              className={`flex items-center w-full ${collapsed ? 'justify-center' : 'justify-between'} px-3 py-3 rounded-lg text-left cursor-pointer ${
                isActive
                  ? "bg-blue-50 text-blue-600"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <div className="flex items-center">
                <item.icon className={`h-5 w-5 ${!collapsed && 'mr-3'}`} />
                {!collapsed && <span className="flex-1">{item.label}</span>}
              </div>

              {!collapsed && item.count !== null && (
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    isActive
                      ? "bg-blue-100 text-blue-600"
                      : "bg-gray-100 text-gray-600"
                  }`}
                >
                  {item.count}
                </span>
              )}
            </div>
          );

          return collapsed ? (
            <Tooltip key={item.value} content={item.label} placement="right" className="bg-gray-900">
              {MenuItem}
            </Tooltip>
          ) : (
            MenuItem
          );
        })}
      </nav>

      <div className={`p-4 border-t ${collapsed ? 'flex justify-center' : ''} absolute left-0 right-0 bottom-0  bg-white`}>
        {collapsed ? (
          <Tooltip content="Sign Out" placement="right" className="bg-gray-900">
            <button
              onClick={handleLogout}
              className="flex items-center justify-center text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50"
            >
              <LogOut className="h-5 w-5" />
            </button>
          </Tooltip>
        ) : (
          <button
            onClick={handleLogout}
            className="flex items-center text-red-600 hover:text-red-700 w-full"
          >
            <LogOut className="h-5 w-5 mr-2" />
            <span>Sign Out</span>
          </button>
        )}
      </div>
    </div>
  );
}

export default Sidebar;
