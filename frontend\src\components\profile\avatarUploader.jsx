// import React, { useState } from 'react';
// import { ArrowUpTrayIcon } from '@heroicons/react/24/solid';
// import { setServerMedia } from '../../app/helpers/helpers';
// import { toast } from 'react-toastify';

// const AvatarUploader = ({ avatarPreview, setAvatarPreview, onAvatarChange, error }) => {
//     const [localError, setLocalError] = useState('');

//     const handleFileChange = (e) => {
//         const file = e.target.files[0];
//         if (file) {
//             const validExtensions = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
//             const isValidExtension = validExtensions.includes(file.type);
//             const isValidSize = file.size < 2 * 1024 * 1024; // Example: 2MB limit

//             if (!isValidExtension) {
//                 setLocalError('Only JPG, JPEG, WEBP, and PNG files are allowed.');
//                 toast.error('Only JPG, JPEG, WEBP, and PNG files are allowed.');
//                 return;
//             }

//             if (!isValidSize) {
//                 setLocalError('File size should be less than 2MB.');
//                 toast.error('File size should be less than 2MB.');
//                 return;
//             }

//             setLocalError('');
//             console.log('Uploading file...', file);
//             onAvatarChange(file);
//         }
//     };

//     return (
//         <div className="flex items-center space-x-4">
//             <img
//                 src={setServerMedia(avatarPreview)}
//                 alt="Avatar"
//                 className="w-24 h-24 rounded-full object-cover"
//             />
//             <div className="relative">
//                 <input
//                     type="file"
//                     id="avatar-upload"
//                     name="avatar"
//                     onChange={handleFileChange}
//                     className="absolute inset-0 opacity-0 cursor-pointer"
//                 />
//                 <button
//                     htmlFor="avatar-upload"
//                     className="p-3 bg-gray-300 text-gray-800 font-normal text-sm rounded-md hover:bg-blue-600 focus:outline-none flex items-center justify-center space-x-2"
//                 >
//                     <ArrowUpTrayIcon width={20} />
//                     <span>Upload Avatar</span>
//                 </button>
//             </div>
//             {(error || localError) && (
//                 <p className="text-red-500 text-sm">{error || localError}</p>
//             )}
//         </div>
//     );
// };

// export default AvatarUploader;










import React, { useState } from 'react';
import { ArrowUpTrayIcon } from '@heroicons/react/24/solid';
import { setServerMedia } from '../../app/helpers/helpers';
import { toast } from 'react-toastify';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

const AvatarUploader = ({ avatarPreview, onAvatarChange, error, t }) => {
    const [localError, setLocalError] = useState('');
    const [loading, setLoading] = useState(false);

    const handleFileChange = async (e) => {
        const file = e.target.files[0];
        if (file) {
            const validExtensions = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            const isValidExtension = validExtensions.includes(file.type);
            const isValidSize = file.size < 2 * 1024 * 1024;

            if (!isValidExtension) {
                setLocalError(t('file_format_limit'));
                toast.error(t('file_format_limit'));
                return;
            }

            if (!isValidSize) {
                setLocalError(t('file_size_limit'));
                toast.error(t('file_size_limit'));
                return;
            }

            setLocalError('');
            setLoading(true);
            try {
                await onAvatarChange(file);
            } catch (error) {
                toast.error(t('error_uploading_avatar'));
            } finally {
                setLoading(false);
            }
        }
    };

    return (
        <div className="flex items-center space-x-4">
            {loading ? (
                <Skeleton circle height={96} width={96} />
            ) : (
                <img
                    src={setServerMedia(avatarPreview)}
                    alt="Avatar"
                    className="w-24 h-24 rounded-full object-cover"
                />
            )}
            <div className="relative">
                <input
                    type="file"
                    id="avatar-upload"
                    name="avatar"
                    onChange={handleFileChange}
                    className="absolute inset-0 opacity-0 cursor-pointer"
                />
                <button
                    htmlFor="avatar-upload"
                    className="p-3 bg-gray-300 text-gray-800 font-normal text-sm rounded-md hover:bg-blue-600 focus:outline-none flex items-center justify-center space-x-2"
                    disabled={loading}
                >
                    <ArrowUpTrayIcon width={20} />
                    <span>{loading ? t('updating') : t('upload_avatar')}</span>
                </button>
            </div>
            {(error || localError) && (
                <p className="text-red-500 text-sm">{error || localError}</p>
            )}
        </div>
    );
};

export default AvatarUploader;
