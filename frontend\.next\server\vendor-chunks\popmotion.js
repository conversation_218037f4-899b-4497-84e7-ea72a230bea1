"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/popmotion";
exports.ids = ["vendor-chunks/popmotion"];
exports.modules = {

/***/ "(ssr)/./node_modules/popmotion/dist/popmotion.cjs.js":
/*!******************************************************!*\
  !*** ./node_modules/popmotion/dist/popmotion.cjs.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar tslib = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\nvar heyListen = __webpack_require__(/*! hey-listen */ \"(ssr)/./node_modules/hey-listen/dist/hey-listen.es.js\");\nvar styleValueTypes = __webpack_require__(/*! style-value-types */ \"(ssr)/./node_modules/style-value-types/dist/valueTypes.cjs.js\");\nvar sync = __webpack_require__(/*! framesync */ \"(ssr)/./node_modules/framesync/dist/framesync.cjs.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nvar sync__default = /*#__PURE__*/ _interopDefaultLegacy(sync);\nconst clamp = (min, max, v)=>Math.min(Math.max(v, min), max);\nconst safeMin = 0.001;\nconst minDuration = 0.01;\nconst maxDuration = 10.0;\nconst minDamping = 0.05;\nconst maxDamping = 1;\nfunction findSpring({ duration = 800, bounce = 0.25, velocity = 0, mass = 1 }) {\n    let envelope;\n    let derivative;\n    heyListen.warning(duration <= maxDuration * 1000, \"Spring duration must be 10 seconds or less\");\n    let dampingRatio = 1 - bounce;\n    dampingRatio = clamp(minDamping, maxDamping, dampingRatio);\n    duration = clamp(minDuration, maxDuration, duration / 1000);\n    if (dampingRatio < 1) {\n        envelope = (undampedFreq)=>{\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - a / b * c;\n        };\n        derivative = (undampedFreq)=>{\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return factor * ((d - e) * f) / g;\n        };\n    } else {\n        envelope = (undampedFreq)=>{\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq)=>{\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = duration * 1000;\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: 100,\n            damping: 10,\n            duration\n        };\n    } else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for(let i = 1; i < rootIterations; i++){\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\nconst durationKeys = [\n    \"duration\",\n    \"bounce\"\n];\nconst physicsKeys = [\n    \"stiffness\",\n    \"damping\",\n    \"mass\"\n];\nfunction isSpringType(options, keys) {\n    return keys.some((key)=>options[key] !== undefined);\n}\nfunction getSpringOptions(options) {\n    let springOptions = Object.assign({\n        velocity: 0.0,\n        stiffness: 100,\n        damping: 10,\n        mass: 1.0,\n        isResolvedFromDuration: false\n    }, options);\n    if (!isSpringType(options, physicsKeys) && isSpringType(options, durationKeys)) {\n        const derived = findSpring(options);\n        springOptions = Object.assign(Object.assign(Object.assign({}, springOptions), derived), {\n            velocity: 0.0,\n            mass: 1.0\n        });\n        springOptions.isResolvedFromDuration = true;\n    }\n    return springOptions;\n}\nfunction spring(_a) {\n    var { from = 0.0, to = 1.0, restSpeed = 2, restDelta } = _a, options = tslib.__rest(_a, [\n        \"from\",\n        \"to\",\n        \"restSpeed\",\n        \"restDelta\"\n    ]);\n    const state = {\n        done: false,\n        value: from\n    };\n    let { stiffness, damping, mass, velocity, duration, isResolvedFromDuration } = getSpringOptions(options);\n    let resolveSpring = zero;\n    let resolveVelocity = zero;\n    function createSpring() {\n        const initialVelocity = velocity ? -(velocity / 1000) : 0.0;\n        const initialDelta = to - from;\n        const dampingRatio = damping / (2 * Math.sqrt(stiffness * mass));\n        const undampedAngularFreq = Math.sqrt(stiffness / mass) / 1000;\n        if (restDelta === undefined) {\n            restDelta = Math.min(Math.abs(to - from) / 100, 0.4);\n        }\n        if (dampingRatio < 1) {\n            const angularFreq = calcAngularFreq(undampedAngularFreq, dampingRatio);\n            resolveSpring = (t)=>{\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                return to - envelope * ((initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) / angularFreq * Math.sin(angularFreq * t) + initialDelta * Math.cos(angularFreq * t));\n            };\n            resolveVelocity = (t)=>{\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                return dampingRatio * undampedAngularFreq * envelope * (Math.sin(angularFreq * t) * (initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) / angularFreq + initialDelta * Math.cos(angularFreq * t)) - envelope * (Math.cos(angularFreq * t) * (initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) - angularFreq * initialDelta * Math.sin(angularFreq * t));\n            };\n        } else if (dampingRatio === 1) {\n            resolveSpring = (t)=>to - Math.exp(-undampedAngularFreq * t) * (initialDelta + (initialVelocity + undampedAngularFreq * initialDelta) * t);\n        } else {\n            const dampedAngularFreq = undampedAngularFreq * Math.sqrt(dampingRatio * dampingRatio - 1);\n            resolveSpring = (t)=>{\n                const envelope = Math.exp(-dampingRatio * undampedAngularFreq * t);\n                const freqForT = Math.min(dampedAngularFreq * t, 300);\n                return to - envelope * ((initialVelocity + dampingRatio * undampedAngularFreq * initialDelta) * Math.sinh(freqForT) + dampedAngularFreq * initialDelta * Math.cosh(freqForT)) / dampedAngularFreq;\n            };\n        }\n    }\n    createSpring();\n    return {\n        next: (t)=>{\n            const current = resolveSpring(t);\n            if (!isResolvedFromDuration) {\n                const currentVelocity = resolveVelocity(t) * 1000;\n                const isBelowVelocityThreshold = Math.abs(currentVelocity) <= restSpeed;\n                const isBelowDisplacementThreshold = Math.abs(to - current) <= restDelta;\n                state.done = isBelowVelocityThreshold && isBelowDisplacementThreshold;\n            } else {\n                state.done = t >= duration;\n            }\n            state.value = state.done ? to : current;\n            return state;\n        },\n        flipTarget: ()=>{\n            velocity = -velocity;\n            [from, to] = [\n                to,\n                from\n            ];\n            createSpring();\n        }\n    };\n}\nspring.needsInterpolation = (a, b)=>typeof a === \"string\" || typeof b === \"string\";\nconst zero = (_t)=>0;\nconst progress = (from, to, value)=>{\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\nconst mix = (from, to, progress)=>-progress * from + progress * to + from;\nfunction hueToRgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    } else {\n        const q = lightness < 0.5 ? lightness * (1 + saturation) : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha\n    };\n}\nconst mixLinearColor = (from, to, v)=>{\n    const fromExpo = from * from;\n    const toExpo = to * to;\n    return Math.sqrt(Math.max(0, v * (toExpo - fromExpo) + fromExpo));\n};\nconst colorTypes = [\n    styleValueTypes.hex,\n    styleValueTypes.rgba,\n    styleValueTypes.hsla\n];\nconst getColorType = (v)=>colorTypes.find((type)=>type.test(v));\nconst notAnimatable = (color)=>`'${color}' is not an animatable color. Use the equivalent color code instead.`;\nconst mixColor = (from, to)=>{\n    let fromColorType = getColorType(from);\n    let toColorType = getColorType(to);\n    heyListen.invariant(!!fromColorType, notAnimatable(from));\n    heyListen.invariant(!!toColorType, notAnimatable(to));\n    let fromColor = fromColorType.parse(from);\n    let toColor = toColorType.parse(to);\n    if (fromColorType === styleValueTypes.hsla) {\n        fromColor = hslaToRgba(fromColor);\n        fromColorType = styleValueTypes.rgba;\n    }\n    if (toColorType === styleValueTypes.hsla) {\n        toColor = hslaToRgba(toColor);\n        toColorType = styleValueTypes.rgba;\n    }\n    const blended = Object.assign({}, fromColor);\n    return (v)=>{\n        for(const key in blended){\n            if (key !== \"alpha\") {\n                blended[key] = mixLinearColor(fromColor[key], toColor[key], v);\n            }\n        }\n        blended.alpha = mix(fromColor.alpha, toColor.alpha, v);\n        return fromColorType.transform(blended);\n    };\n};\nconst zeroPoint = {\n    x: 0,\n    y: 0,\n    z: 0\n};\nconst isNum = (v)=>typeof v === \"number\";\nconst combineFunctions = (a, b)=>(v)=>b(a(v));\nconst pipe = (...transformers)=>transformers.reduce(combineFunctions);\nfunction getMixer(origin, target) {\n    if (isNum(origin)) {\n        return (v)=>mix(origin, target, v);\n    } else if (styleValueTypes.color.test(origin)) {\n        return mixColor(origin, target);\n    } else {\n        return mixComplex(origin, target);\n    }\n}\nconst mixArray = (from, to)=>{\n    const output = [\n        ...from\n    ];\n    const numValues = output.length;\n    const blendValue = from.map((fromThis, i)=>getMixer(fromThis, to[i]));\n    return (v)=>{\n        for(let i = 0; i < numValues; i++){\n            output[i] = blendValue[i](v);\n        }\n        return output;\n    };\n};\nconst mixObject = (origin, target)=>{\n    const output = Object.assign(Object.assign({}, origin), target);\n    const blendValue = {};\n    for(const key in output){\n        if (origin[key] !== undefined && target[key] !== undefined) {\n            blendValue[key] = getMixer(origin[key], target[key]);\n        }\n    }\n    return (v)=>{\n        for(const key in blendValue){\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n};\nfunction analyse(value) {\n    const parsed = styleValueTypes.complex.parse(value);\n    const numValues = parsed.length;\n    let numNumbers = 0;\n    let numRGB = 0;\n    let numHSL = 0;\n    for(let i = 0; i < numValues; i++){\n        if (numNumbers || typeof parsed[i] === \"number\") {\n            numNumbers++;\n        } else {\n            if (parsed[i].hue !== undefined) {\n                numHSL++;\n            } else {\n                numRGB++;\n            }\n        }\n    }\n    return {\n        parsed,\n        numNumbers,\n        numRGB,\n        numHSL\n    };\n}\nconst mixComplex = (origin, target)=>{\n    const template = styleValueTypes.complex.createTransformer(target);\n    const originStats = analyse(origin);\n    const targetStats = analyse(target);\n    const canInterpolate = originStats.numHSL === targetStats.numHSL && originStats.numRGB === targetStats.numRGB && originStats.numNumbers >= targetStats.numNumbers;\n    if (canInterpolate) {\n        return pipe(mixArray(originStats.parsed, targetStats.parsed), template);\n    } else {\n        heyListen.warning(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return (p)=>`${p > 0 ? target : origin}`;\n    }\n};\nconst mixNumber = (from, to)=>(p)=>mix(from, to, p);\nfunction detectMixerFactory(v) {\n    if (typeof v === \"number\") {\n        return mixNumber;\n    } else if (typeof v === \"string\") {\n        if (styleValueTypes.color.test(v)) {\n            return mixColor;\n        } else {\n            return mixComplex;\n        }\n    } else if (Array.isArray(v)) {\n        return mixArray;\n    } else if (typeof v === \"object\") {\n        return mixObject;\n    }\n}\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || detectMixerFactory(output[0]);\n    const numMixers = output.length - 1;\n    for(let i = 0; i < numMixers; i++){\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\nfunction fastInterpolate([from, to], [mixer]) {\n    return (v)=>mixer(progress(from, to, v));\n}\nfunction slowInterpolate(input, mixers) {\n    const inputLength = input.length;\n    const lastInputIndex = inputLength - 1;\n    return (v)=>{\n        let mixerIndex = 0;\n        let foundMixerIndex = false;\n        if (v <= input[0]) {\n            foundMixerIndex = true;\n        } else if (v >= input[lastInputIndex]) {\n            mixerIndex = lastInputIndex - 1;\n            foundMixerIndex = true;\n        }\n        if (!foundMixerIndex) {\n            let i = 1;\n            for(; i < inputLength; i++){\n                if (input[i] > v || i === lastInputIndex) {\n                    break;\n                }\n            }\n            mixerIndex = i - 1;\n        }\n        const progressInRange = progress(input[mixerIndex], input[mixerIndex + 1], v);\n        return mixers[mixerIndex](progressInRange);\n    };\n}\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    heyListen.invariant(inputLength === output.length, \"Both input and output ranges must be the same length\");\n    heyListen.invariant(!ease || !Array.isArray(ease) || ease.length === inputLength - 1, \"Array of easing functions must be of length `input.length - 1`, as it applies to the transitions **between** the defined values.\");\n    if (input[0] > input[inputLength - 1]) {\n        input = [].concat(input);\n        output = [].concat(output);\n        input.reverse();\n        output.reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const interpolator = inputLength === 2 ? fastInterpolate(input, mixers) : slowInterpolate(input, mixers);\n    return isClamp ? (v)=>interpolator(clamp(input[0], input[inputLength - 1], v)) : interpolator;\n}\nconst reverseEasing = (easing)=>(p)=>1 - easing(1 - p);\nconst mirrorEasing = (easing)=>(p)=>p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\nconst createExpoIn = (power)=>(p)=>Math.pow(p, power);\nconst createBackIn = (power)=>(p)=>p * p * ((power + 1) * p - power);\nconst createAnticipate = (power)=>{\n    const backEasing = createBackIn(power);\n    return (p)=>(p *= 2) < 1 ? 0.5 * backEasing(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n};\nconst DEFAULT_OVERSHOOT_STRENGTH = 1.525;\nconst BOUNCE_FIRST_THRESHOLD = 4.0 / 11.0;\nconst BOUNCE_SECOND_THRESHOLD = 8.0 / 11.0;\nconst BOUNCE_THIRD_THRESHOLD = 9.0 / 10.0;\nconst linear = (p)=>p;\nconst easeIn = createExpoIn(2);\nconst easeOut = reverseEasing(easeIn);\nconst easeInOut = mirrorEasing(easeIn);\nconst circIn = (p)=>1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circOut);\nconst backIn = createBackIn(DEFAULT_OVERSHOOT_STRENGTH);\nconst backOut = reverseEasing(backIn);\nconst backInOut = mirrorEasing(backIn);\nconst anticipate = createAnticipate(DEFAULT_OVERSHOOT_STRENGTH);\nconst ca = 4356.0 / 361.0;\nconst cb = 35442.0 / 1805.0;\nconst cc = 16061.0 / 1805.0;\nconst bounceOut = (p)=>{\n    if (p === 1 || p === 0) return p;\n    const p2 = p * p;\n    return p < BOUNCE_FIRST_THRESHOLD ? 7.5625 * p2 : p < BOUNCE_SECOND_THRESHOLD ? 9.075 * p2 - 9.9 * p + 3.4 : p < BOUNCE_THIRD_THRESHOLD ? ca * p2 - cb * p + cc : 10.8 * p * p - 20.52 * p + 10.72;\n};\nconst bounceIn = reverseEasing(bounceOut);\nconst bounceInOut = (p)=>p < 0.5 ? 0.5 * (1.0 - bounceOut(1.0 - p * 2.0)) : 0.5 * bounceOut(p * 2.0 - 1.0) + 0.5;\nfunction defaultEasing(values, easing) {\n    return values.map(()=>easing || easeInOut).splice(0, values.length - 1);\n}\nfunction defaultOffset(values) {\n    const numValues = values.length;\n    return values.map((_value, i)=>i !== 0 ? i / (numValues - 1) : 0);\n}\nfunction convertOffsetToTimes(offset, duration) {\n    return offset.map((o)=>o * duration);\n}\nfunction keyframes({ from = 0, to = 1, ease, offset, duration = 300 }) {\n    const state = {\n        done: false,\n        value: from\n    };\n    const values = Array.isArray(to) ? to : [\n        from,\n        to\n    ];\n    const times = convertOffsetToTimes(offset && offset.length === values.length ? offset : defaultOffset(values), duration);\n    function createInterpolator() {\n        return interpolate(times, values, {\n            ease: Array.isArray(ease) ? ease : defaultEasing(values, ease)\n        });\n    }\n    let interpolator = createInterpolator();\n    return {\n        next: (t)=>{\n            state.value = interpolator(t);\n            state.done = t >= duration;\n            return state;\n        },\n        flipTarget: ()=>{\n            values.reverse();\n            interpolator = createInterpolator();\n        }\n    };\n}\nfunction decay({ velocity = 0, from = 0, power = 0.8, timeConstant = 350, restDelta = 0.5, modifyTarget }) {\n    const state = {\n        done: false,\n        value: from\n    };\n    let amplitude = power * velocity;\n    const ideal = from + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    if (target !== ideal) amplitude = target - from;\n    return {\n        next: (t)=>{\n            const delta = -amplitude * Math.exp(-t / timeConstant);\n            state.done = !(delta > restDelta || delta < -restDelta);\n            state.value = state.done ? target : target + delta;\n            return state;\n        },\n        flipTarget: ()=>{}\n    };\n}\nconst types = {\n    keyframes,\n    spring,\n    decay\n};\nfunction detectAnimationFromOptions(config) {\n    if (Array.isArray(config.to)) {\n        return keyframes;\n    } else if (types[config.type]) {\n        return types[config.type];\n    }\n    const keys = new Set(Object.keys(config));\n    if (keys.has(\"ease\") || keys.has(\"duration\") && !keys.has(\"dampingRatio\")) {\n        return keyframes;\n    } else if (keys.has(\"dampingRatio\") || keys.has(\"stiffness\") || keys.has(\"mass\") || keys.has(\"damping\") || keys.has(\"restSpeed\") || keys.has(\"restDelta\")) {\n        return spring;\n    }\n    return keyframes;\n}\nfunction loopElapsed(elapsed, duration, delay = 0) {\n    return elapsed - duration - delay;\n}\nfunction reverseElapsed(elapsed, duration, delay = 0, isForwardPlayback = true) {\n    return isForwardPlayback ? loopElapsed(duration + -elapsed, duration, delay) : duration - (elapsed - duration) + delay;\n}\nfunction hasRepeatDelayElapsed(elapsed, duration, delay, isForwardPlayback) {\n    return isForwardPlayback ? elapsed >= duration + delay : elapsed <= -delay;\n}\nconst framesync = (update)=>{\n    const passTimestamp = ({ delta })=>update(delta);\n    return {\n        start: ()=>sync__default[\"default\"].update(passTimestamp, true),\n        stop: ()=>sync.cancelSync.update(passTimestamp)\n    };\n};\nfunction animate(_a) {\n    var _b, _c;\n    var { from, autoplay = true, driver = framesync, elapsed = 0, repeat: repeatMax = 0, repeatType = \"loop\", repeatDelay = 0, onPlay, onStop, onComplete, onRepeat, onUpdate } = _a, options = tslib.__rest(_a, [\n        \"from\",\n        \"autoplay\",\n        \"driver\",\n        \"elapsed\",\n        \"repeat\",\n        \"repeatType\",\n        \"repeatDelay\",\n        \"onPlay\",\n        \"onStop\",\n        \"onComplete\",\n        \"onRepeat\",\n        \"onUpdate\"\n    ]);\n    let { to } = options;\n    let driverControls;\n    let repeatCount = 0;\n    let computedDuration = options.duration;\n    let latest;\n    let isComplete = false;\n    let isForwardPlayback = true;\n    let interpolateFromNumber;\n    const animator = detectAnimationFromOptions(options);\n    if ((_c = (_b = animator).needsInterpolation) === null || _c === void 0 ? void 0 : _c.call(_b, from, to)) {\n        interpolateFromNumber = interpolate([\n            0,\n            100\n        ], [\n            from,\n            to\n        ], {\n            clamp: false\n        });\n        from = 0;\n        to = 100;\n    }\n    const animation = animator(Object.assign(Object.assign({}, options), {\n        from,\n        to\n    }));\n    function repeat() {\n        repeatCount++;\n        if (repeatType === \"reverse\") {\n            isForwardPlayback = repeatCount % 2 === 0;\n            elapsed = reverseElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback);\n        } else {\n            elapsed = loopElapsed(elapsed, computedDuration, repeatDelay);\n            if (repeatType === \"mirror\") animation.flipTarget();\n        }\n        isComplete = false;\n        onRepeat && onRepeat();\n    }\n    function complete() {\n        driverControls.stop();\n        onComplete && onComplete();\n    }\n    function update(delta) {\n        if (!isForwardPlayback) delta = -delta;\n        elapsed += delta;\n        if (!isComplete) {\n            const state = animation.next(Math.max(0, elapsed));\n            latest = state.value;\n            if (interpolateFromNumber) latest = interpolateFromNumber(latest);\n            isComplete = isForwardPlayback ? state.done : elapsed <= 0;\n        }\n        onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(latest);\n        if (isComplete) {\n            if (repeatCount === 0) computedDuration !== null && computedDuration !== void 0 ? computedDuration : computedDuration = elapsed;\n            if (repeatCount < repeatMax) {\n                hasRepeatDelayElapsed(elapsed, computedDuration, repeatDelay, isForwardPlayback) && repeat();\n            } else {\n                complete();\n            }\n        }\n    }\n    function play() {\n        onPlay === null || onPlay === void 0 ? void 0 : onPlay();\n        driverControls = driver(update);\n        driverControls.start();\n    }\n    autoplay && play();\n    return {\n        stop: ()=>{\n            onStop === null || onStop === void 0 ? void 0 : onStop();\n            driverControls.stop();\n        }\n    };\n}\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\nfunction inertia({ from = 0, velocity = 0, min, max, power = 0.8, timeConstant = 750, bounceStiffness = 500, bounceDamping = 10, restDelta = 1, modifyTarget, driver, onUpdate, onComplete, onStop }) {\n    let currentAnimation;\n    function isOutOfBounds(v) {\n        return min !== undefined && v < min || max !== undefined && v > max;\n    }\n    function boundaryNearest(v) {\n        if (min === undefined) return max;\n        if (max === undefined) return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    }\n    function startAnimation(options) {\n        currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop();\n        currentAnimation = animate(Object.assign(Object.assign({}, options), {\n            driver,\n            onUpdate: (v)=>{\n                var _a;\n                onUpdate === null || onUpdate === void 0 ? void 0 : onUpdate(v);\n                (_a = options.onUpdate) === null || _a === void 0 ? void 0 : _a.call(options, v);\n            },\n            onComplete,\n            onStop\n        }));\n    }\n    function startSpring(options) {\n        startAnimation(Object.assign({\n            type: \"spring\",\n            stiffness: bounceStiffness,\n            damping: bounceDamping,\n            restDelta\n        }, options));\n    }\n    if (isOutOfBounds(from)) {\n        startSpring({\n            from,\n            velocity,\n            to: boundaryNearest(from)\n        });\n    } else {\n        let target = power * velocity + from;\n        if (typeof modifyTarget !== \"undefined\") target = modifyTarget(target);\n        const boundary = boundaryNearest(target);\n        const heading = boundary === min ? -1 : 1;\n        let prev;\n        let current;\n        const checkBoundary = (v)=>{\n            prev = current;\n            current = v;\n            velocity = velocityPerSecond(v - prev, sync.getFrameData().delta);\n            if (heading === 1 && v > boundary || heading === -1 && v < boundary) {\n                startSpring({\n                    from: v,\n                    to: boundary,\n                    velocity\n                });\n            }\n        };\n        startAnimation({\n            type: \"decay\",\n            from,\n            velocity,\n            timeConstant,\n            power,\n            restDelta,\n            modifyTarget,\n            onUpdate: isOutOfBounds(target) ? checkBoundary : undefined\n        });\n    }\n    return {\n        stop: ()=>currentAnimation === null || currentAnimation === void 0 ? void 0 : currentAnimation.stop()\n    };\n}\nconst radiansToDegrees = (radians)=>radians * 180 / Math.PI;\nconst angle = (a, b = zeroPoint)=>radiansToDegrees(Math.atan2(b.y - a.y, b.x - a.x));\nconst applyOffset = (from, to)=>{\n    let hasReceivedFrom = true;\n    if (to === undefined) {\n        to = from;\n        hasReceivedFrom = false;\n    }\n    return (v)=>{\n        if (hasReceivedFrom) {\n            return v - from + to;\n        } else {\n            from = v;\n            hasReceivedFrom = true;\n            return to;\n        }\n    };\n};\nconst identity = (v)=>v;\nconst createAttractor = (alterDisplacement = identity)=>(constant, origin, v)=>{\n        const displacement = origin - v;\n        const springModifiedDisplacement = -(0 - constant + 1) * (0 - alterDisplacement(Math.abs(displacement)));\n        return displacement <= 0 ? origin + springModifiedDisplacement : origin - springModifiedDisplacement;\n    };\nconst attract = createAttractor();\nconst attractExpo = createAttractor(Math.sqrt);\nconst degreesToRadians = (degrees)=>degrees * Math.PI / 180;\nconst isPoint = (point)=>point.hasOwnProperty(\"x\") && point.hasOwnProperty(\"y\");\nconst isPoint3D = (point)=>isPoint(point) && point.hasOwnProperty(\"z\");\nconst distance1D = (a, b)=>Math.abs(a - b);\nfunction distance(a, b) {\n    if (isNum(a) && isNum(b)) {\n        return distance1D(a, b);\n    } else if (isPoint(a) && isPoint(b)) {\n        const xDelta = distance1D(a.x, b.x);\n        const yDelta = distance1D(a.y, b.y);\n        const zDelta = isPoint3D(a) && isPoint3D(b) ? distance1D(a.z, b.z) : 0;\n        return Math.sqrt(Math.pow(xDelta, 2) + Math.pow(yDelta, 2) + Math.pow(zDelta, 2));\n    }\n}\nconst pointFromVector = (origin, angle, distance)=>{\n    angle = degreesToRadians(angle);\n    return {\n        x: distance * Math.cos(angle) + origin.x,\n        y: distance * Math.sin(angle) + origin.y\n    };\n};\nconst toDecimal = (num, precision = 2)=>{\n    precision = Math.pow(10, precision);\n    return Math.round(num * precision) / precision;\n};\nconst smoothFrame = (prevValue, nextValue, duration, smoothing = 0)=>toDecimal(prevValue + duration * (nextValue - prevValue) / Math.max(smoothing, duration));\nconst smooth = (strength = 50)=>{\n    let previousValue = 0;\n    let lastUpdated = 0;\n    return (v)=>{\n        const currentFramestamp = sync.getFrameData().timestamp;\n        const timeDelta = currentFramestamp !== lastUpdated ? currentFramestamp - lastUpdated : 0;\n        const newValue = timeDelta ? smoothFrame(previousValue, v, timeDelta, strength) : previousValue;\n        lastUpdated = currentFramestamp;\n        previousValue = newValue;\n        return newValue;\n    };\n};\nconst snap = (points)=>{\n    if (typeof points === \"number\") {\n        return (v)=>Math.round(v / points) * points;\n    } else {\n        let i = 0;\n        const numPoints = points.length;\n        return (v)=>{\n            let lastDistance = Math.abs(points[0] - v);\n            for(i = 1; i < numPoints; i++){\n                const point = points[i];\n                const distance = Math.abs(point - v);\n                if (distance === 0) return point;\n                if (distance > lastDistance) return points[i - 1];\n                if (i === numPoints - 1) return point;\n                lastDistance = distance;\n            }\n        };\n    }\n};\nfunction velocityPerFrame(xps, frameDuration) {\n    return xps / (1000 / frameDuration);\n}\nconst wrap = (min, max, v)=>{\n    const rangeSize = max - min;\n    return ((v - min) % rangeSize + rangeSize) % rangeSize + min;\n};\nconst a = (a1, a2)=>1.0 - 3.0 * a2 + 3.0 * a1;\nconst b = (a1, a2)=>3.0 * a2 - 6.0 * a1;\nconst c = (a1)=>3.0 * a1;\nconst calcBezier = (t, a1, a2)=>((a(a1, a2) * t + b(a1, a2)) * t + c(a1)) * t;\nconst getSlope = (t, a1, a2)=>3.0 * a(a1, a2) * t * t + 2.0 * b(a1, a2) * t + c(a1);\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 10;\nfunction binarySubdivide(aX, aA, aB, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = aA + (aB - aA) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - aX;\n        if (currentX > 0.0) {\n            aB = currentT;\n        } else {\n            aA = currentT;\n        }\n    }while (Math.abs(currentX) > subdivisionPrecision && ++i < subdivisionMaxIterations);\n    return currentT;\n}\nconst newtonIterations = 8;\nconst newtonMinSlope = 0.001;\nfunction newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n    for(let i = 0; i < newtonIterations; ++i){\n        const currentSlope = getSlope(aGuessT, mX1, mX2);\n        if (currentSlope === 0.0) {\n            return aGuessT;\n        }\n        const currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n        aGuessT -= currentX / currentSlope;\n    }\n    return aGuessT;\n}\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    if (mX1 === mY1 && mX2 === mY2) return linear;\n    const sampleValues = new Float32Array(kSplineTableSize);\n    for(let i = 0; i < kSplineTableSize; ++i){\n        sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n    }\n    function getTForX(aX) {\n        let intervalStart = 0.0;\n        let currentSample = 1;\n        const lastSample = kSplineTableSize - 1;\n        for(; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample){\n            intervalStart += kSampleStepSize;\n        }\n        --currentSample;\n        const dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n        const guessForT = intervalStart + dist * kSampleStepSize;\n        const initialSlope = getSlope(guessForT, mX1, mX2);\n        if (initialSlope >= newtonMinSlope) {\n            return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n        } else if (initialSlope === 0.0) {\n            return guessForT;\n        } else {\n            return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n        }\n    }\n    return (t)=>t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\nconst steps = (steps, direction = \"end\")=>(progress)=>{\n        progress = direction === \"end\" ? Math.min(progress, 0.999) : Math.max(progress, 0.001);\n        const expanded = progress * steps;\n        const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n        return clamp(0, 1, rounded / steps);\n    };\nexports.angle = angle;\nexports.animate = animate;\nexports.anticipate = anticipate;\nexports.applyOffset = applyOffset;\nexports.attract = attract;\nexports.attractExpo = attractExpo;\nexports.backIn = backIn;\nexports.backInOut = backInOut;\nexports.backOut = backOut;\nexports.bounceIn = bounceIn;\nexports.bounceInOut = bounceInOut;\nexports.bounceOut = bounceOut;\nexports.circIn = circIn;\nexports.circInOut = circInOut;\nexports.circOut = circOut;\nexports.clamp = clamp;\nexports.createAnticipate = createAnticipate;\nexports.createAttractor = createAttractor;\nexports.createBackIn = createBackIn;\nexports.createExpoIn = createExpoIn;\nexports.cubicBezier = cubicBezier;\nexports.decay = decay;\nexports.degreesToRadians = degreesToRadians;\nexports.distance = distance;\nexports.easeIn = easeIn;\nexports.easeInOut = easeInOut;\nexports.easeOut = easeOut;\nexports.inertia = inertia;\nexports.interpolate = interpolate;\nexports.isPoint = isPoint;\nexports.isPoint3D = isPoint3D;\nexports.keyframes = keyframes;\nexports.linear = linear;\nexports.mirrorEasing = mirrorEasing;\nexports.mix = mix;\nexports.mixColor = mixColor;\nexports.mixComplex = mixComplex;\nexports.pipe = pipe;\nexports.pointFromVector = pointFromVector;\nexports.progress = progress;\nexports.radiansToDegrees = radiansToDegrees;\nexports.reverseEasing = reverseEasing;\nexports.smooth = smooth;\nexports.smoothFrame = smoothFrame;\nexports.snap = snap;\nexports.spring = spring;\nexports.steps = steps;\nexports.toDecimal = toDecimal;\nexports.velocityPerFrame = velocityPerFrame;\nexports.velocityPerSecond = velocityPerSecond;\nexports.wrap = wrap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/popmotion/dist/popmotion.cjs.js\n");

/***/ })

};
;