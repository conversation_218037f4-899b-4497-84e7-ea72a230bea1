"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-google-recaptcha";
exports.ids = ["vendor-chunks/react-google-recaptcha"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-google-recaptcha/lib/esm/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-google-recaptcha/lib/esm/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReCAPTCHA: () => (/* reexport safe */ _recaptcha__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _recaptcha_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./recaptcha-wrapper */ \"(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha-wrapper.js\");\n/* harmony import */ var _recaptcha__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./recaptcha */ \"(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_recaptcha_wrapper__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZ29vZ2xlLXJlY2FwdGNoYS9saWIvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUQ7QUFDZjtBQUNwQyxpRUFBZUEsMERBQWdCQSxFQUFDO0FBQ1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWdvb2dsZS1yZWNhcHRjaGEvbGliL2VzbS9pbmRleC5qcz9mODMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWNhcHRjaGFXcmFwcGVyIGZyb20gXCIuL3JlY2FwdGNoYS13cmFwcGVyXCI7XG5pbXBvcnQgUmVDQVBUQ0hBIGZyb20gXCIuL3JlY2FwdGNoYVwiO1xuZXhwb3J0IGRlZmF1bHQgUmVjYXB0Y2hhV3JhcHBlcjtcbmV4cG9ydCB7IFJlQ0FQVENIQSB9OyJdLCJuYW1lcyI6WyJSZWNhcHRjaGFXcmFwcGVyIiwiUmVDQVBUQ0hBIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-google-recaptcha/lib/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha-wrapper.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-google-recaptcha/lib/esm/recaptcha-wrapper.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _recaptcha__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./recaptcha */ \"(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha.js\");\n/* harmony import */ var react_async_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-async-script */ \"(ssr)/./node_modules/react-async-script/lib/esm/async-script-loader.js\");\n\n\nvar callbackName = \"onloadcallback\";\nvar globalName = \"grecaptcha\";\nfunction getOptions() {\n    return  false || {};\n}\nfunction getURL() {\n    var dynamicOptions = getOptions();\n    var hostname = dynamicOptions.useRecaptchaNet ? \"recaptcha.net\" : \"www.google.com\";\n    if (dynamicOptions.enterprise) {\n        return \"https://\" + hostname + \"/recaptcha/enterprise.js?onload=\" + callbackName + \"&render=explicit\";\n    }\n    return \"https://\" + hostname + \"/recaptcha/api.js?onload=\" + callbackName + \"&render=explicit\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_async_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(getURL, {\n    callbackName: callbackName,\n    globalName: globalName,\n    attributes: getOptions().nonce ? {\n        nonce: getOptions().nonce\n    } : {}\n})(_recaptcha__WEBPACK_IMPORTED_MODULE_0__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha-wrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-google-recaptcha/lib/esm/recaptcha.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReCAPTCHA)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_1__);\nvar _excluded = [\n    \"sitekey\",\n    \"onChange\",\n    \"theme\",\n    \"type\",\n    \"tabindex\",\n    \"onExpired\",\n    \"onErrored\",\n    \"size\",\n    \"stoken\",\n    \"grecaptcha\",\n    \"badge\",\n    \"hl\",\n    \"isolated\"\n];\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nfunction _assertThisInitialized(self) {\n    if (self === void 0) {\n        throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    }\n    return self;\n}\nfunction _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\n\n\nvar ReCAPTCHA = /*#__PURE__*/ function(_React$Component) {\n    _inheritsLoose(ReCAPTCHA, _React$Component);\n    function ReCAPTCHA() {\n        var _this;\n        _this = _React$Component.call(this) || this;\n        _this.handleExpired = _this.handleExpired.bind(_assertThisInitialized(_this));\n        _this.handleErrored = _this.handleErrored.bind(_assertThisInitialized(_this));\n        _this.handleChange = _this.handleChange.bind(_assertThisInitialized(_this));\n        _this.handleRecaptchaRef = _this.handleRecaptchaRef.bind(_assertThisInitialized(_this));\n        return _this;\n    }\n    var _proto = ReCAPTCHA.prototype;\n    _proto.getCaptchaFunction = function getCaptchaFunction(fnName) {\n        if (this.props.grecaptcha) {\n            if (this.props.grecaptcha.enterprise) {\n                return this.props.grecaptcha.enterprise[fnName];\n            }\n            return this.props.grecaptcha[fnName];\n        }\n        return null;\n    };\n    _proto.getValue = function getValue() {\n        var getResponse = this.getCaptchaFunction(\"getResponse\");\n        if (getResponse && this._widgetId !== undefined) {\n            return getResponse(this._widgetId);\n        }\n        return null;\n    };\n    _proto.getWidgetId = function getWidgetId() {\n        if (this.props.grecaptcha && this._widgetId !== undefined) {\n            return this._widgetId;\n        }\n        return null;\n    };\n    _proto.execute = function execute() {\n        var execute = this.getCaptchaFunction(\"execute\");\n        if (execute && this._widgetId !== undefined) {\n            return execute(this._widgetId);\n        } else {\n            this._executeRequested = true;\n        }\n    };\n    _proto.executeAsync = function executeAsync() {\n        var _this2 = this;\n        return new Promise(function(resolve, reject) {\n            _this2.executionResolve = resolve;\n            _this2.executionReject = reject;\n            _this2.execute();\n        });\n    };\n    _proto.reset = function reset() {\n        var resetter = this.getCaptchaFunction(\"reset\");\n        if (resetter && this._widgetId !== undefined) {\n            resetter(this._widgetId);\n        }\n    };\n    _proto.forceReset = function forceReset() {\n        var resetter = this.getCaptchaFunction(\"reset\");\n        if (resetter) {\n            resetter();\n        }\n    };\n    _proto.handleExpired = function handleExpired() {\n        if (this.props.onExpired) {\n            this.props.onExpired();\n        } else {\n            this.handleChange(null);\n        }\n    };\n    _proto.handleErrored = function handleErrored() {\n        if (this.props.onErrored) {\n            this.props.onErrored();\n        }\n        if (this.executionReject) {\n            this.executionReject();\n            delete this.executionResolve;\n            delete this.executionReject;\n        }\n    };\n    _proto.handleChange = function handleChange(token) {\n        if (this.props.onChange) {\n            this.props.onChange(token);\n        }\n        if (this.executionResolve) {\n            this.executionResolve(token);\n            delete this.executionReject;\n            delete this.executionResolve;\n        }\n    };\n    _proto.explicitRender = function explicitRender() {\n        var render = this.getCaptchaFunction(\"render\");\n        if (render && this._widgetId === undefined) {\n            var wrapper = document.createElement(\"div\");\n            this._widgetId = render(wrapper, {\n                sitekey: this.props.sitekey,\n                callback: this.handleChange,\n                theme: this.props.theme,\n                type: this.props.type,\n                tabindex: this.props.tabindex,\n                \"expired-callback\": this.handleExpired,\n                \"error-callback\": this.handleErrored,\n                size: this.props.size,\n                stoken: this.props.stoken,\n                hl: this.props.hl,\n                badge: this.props.badge,\n                isolated: this.props.isolated\n            });\n            this.captcha.appendChild(wrapper);\n        }\n        if (this._executeRequested && this.props.grecaptcha && this._widgetId !== undefined) {\n            this._executeRequested = false;\n            this.execute();\n        }\n    };\n    _proto.componentDidMount = function componentDidMount() {\n        this.explicitRender();\n    };\n    _proto.componentDidUpdate = function componentDidUpdate() {\n        this.explicitRender();\n    };\n    _proto.handleRecaptchaRef = function handleRecaptchaRef(elem) {\n        this.captcha = elem;\n    };\n    _proto.render = function render() {\n        // consume properties owned by the reCATPCHA, pass the rest to the div so the user can style it.\n        /* eslint-disable no-unused-vars */ var _this$props = this.props, sitekey = _this$props.sitekey, onChange = _this$props.onChange, theme = _this$props.theme, type = _this$props.type, tabindex = _this$props.tabindex, onExpired = _this$props.onExpired, onErrored = _this$props.onErrored, size = _this$props.size, stoken = _this$props.stoken, grecaptcha = _this$props.grecaptcha, badge = _this$props.badge, hl = _this$props.hl, isolated = _this$props.isolated, childProps = _objectWithoutPropertiesLoose(_this$props, _excluded);\n        /* eslint-enable no-unused-vars */ return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", _extends({}, childProps, {\n            ref: this.handleRecaptchaRef\n        }));\n    };\n    return ReCAPTCHA;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n\nReCAPTCHA.displayName = \"ReCAPTCHA\";\nReCAPTCHA.propTypes = {\n    sitekey: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().string).isRequired,\n    onChange: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().func),\n    grecaptcha: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().object),\n    theme: prop_types__WEBPACK_IMPORTED_MODULE_1___default().oneOf([\n        \"dark\",\n        \"light\"\n    ]),\n    type: prop_types__WEBPACK_IMPORTED_MODULE_1___default().oneOf([\n        \"image\",\n        \"audio\"\n    ]),\n    tabindex: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().number),\n    onExpired: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().func),\n    onErrored: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().func),\n    size: prop_types__WEBPACK_IMPORTED_MODULE_1___default().oneOf([\n        \"compact\",\n        \"normal\",\n        \"invisible\"\n    ]),\n    stoken: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().string),\n    hl: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().string),\n    badge: prop_types__WEBPACK_IMPORTED_MODULE_1___default().oneOf([\n        \"bottomright\",\n        \"bottomleft\",\n        \"inline\"\n    ]),\n    isolated: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().bool)\n};\nReCAPTCHA.defaultProps = {\n    onChange: function onChange() {},\n    theme: \"light\",\n    type: \"image\",\n    tabindex: 0,\n    size: \"normal\",\n    badge: \"bottomright\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-google-recaptcha/lib/esm/recaptcha.js\n");

/***/ })

};
;