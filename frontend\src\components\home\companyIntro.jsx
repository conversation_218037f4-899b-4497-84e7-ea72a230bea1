import React from 'react';
import { Typography } from '@material-tailwind/react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { CloudIcon } from '@heroicons/react/24/outline';

const CompanyIntro = ({ t }) => {
    // Feature cards data
    const features = [
        {
            title: t("features.global_reach.title"),
            description: t("features.global_reach.description"),
            icon: "globe"
        },
        {
            title: t("features.expert_development.title"),
            description: t("features.expert_development.description"),
            icon: "code"
        },
        {
            title: t("features.cloud_solutions.title"),
            description: t("features.cloud_solutions.description"),
            icon: "cloud"
        },
        {
            title: t("features.security_first.title"),
            description: t("features.security_first.description"),
            icon: "shield"
        }
    ];

    // Team icons
    const teamIcons = [
        {
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
            ),
            alt: "Code"
        },
        {
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                </svg>
            ),
            alt: "Cloud"
        },
        {
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
            ),
            alt: "Security"
        },
        {
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
            ),
            alt: "Global"
        },
        {
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
            ),
            alt: "Web"
        },
        {
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
            ),
            alt: "Mobile"
        }
    ];

    // Icon components mapping
    const getIcon = (iconName) => {
        switch (iconName) {
            case 'globe':
                return (
                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-6">
                        <g clipPath="url(#clip0_181_518)">
                            <path d="M13.2788 21.3097C13.0566 21.4905 12.8334 21.6326 12.6087 21.7399C12.4561 21.7562 12.3025 21.7681 12.1481 21.7772V19.9865C11.7788 19.6842 11.4073 19.3704 11.0347 19.0479V21.7775C10.8804 21.7681 10.7267 21.7561 10.5745 21.7398C10.3498 21.6325 10.1262 21.4904 9.90405 21.3096C9.17997 20.7203 8.50225 19.7255 7.98255 18.4452H10.3491C9.94572 18.0846 9.54199 17.7131 9.1383 17.3319H7.59335C7.40885 16.7183 7.26035 16.0566 7.14724 15.3626C6.70765 14.9077 6.28071 14.4518 5.8661 13.9967C5.95924 15.1817 6.15021 16.3059 6.43469 17.3319H3.59093C2.79471 16.045 2.30111 14.5534 2.2076 12.9508H4.9376C4.61505 12.5782 4.30118 12.2067 3.9993 11.8375H2.20765C2.24604 11.1808 2.35516 10.5444 2.52077 9.93153C2.14277 9.41586 1.7883 8.90778 1.46144 8.41016C0.975443 9.64452 0.70549 10.9876 0.70549 12.3945C0.705818 18.4068 5.57876 23.2793 11.5911 23.2797C12.9976 23.2797 14.3407 23.0098 15.5746 22.5241C14.9118 22.0881 14.2297 21.6036 13.5361 21.0785C13.4509 21.1607 13.3654 21.239 13.2788 21.3097ZM4.94346 19.0422C4.75282 18.8515 4.5716 18.6515 4.39802 18.4452H6.79605C6.97469 18.9323 7.17115 19.3962 7.39108 19.8224C7.65021 20.3232 7.93727 20.7788 8.25072 21.1825C6.99897 20.7066 5.87482 19.9728 4.94346 19.0422Z" fill="#616BF5"/>
                            <path d="M3.67717 4.92448C3.91492 5.35029 4.18525 5.79391 4.48281 6.25085C4.6314 6.07835 4.78215 5.90801 4.9434 5.74676C5.87659 4.81427 7.00333 4.07894 8.25794 3.60312C7.67481 4.35185 7.1845 5.28068 6.79422 6.34329H4.54192C4.78033 6.70638 5.03729 7.07824 5.30945 7.45657H6.43764C6.33508 7.82585 6.247 8.21004 6.16764 8.60285C6.4659 8.98629 6.77795 9.37404 7.10265 9.76474C7.21975 8.9464 7.38423 8.16902 7.59883 7.45652H11.0348V11.8376H8.91976C9.26373 12.2098 9.61595 12.5813 9.97656 12.951H11.0348V14.0092C11.4044 14.3695 11.7759 14.7224 12.1481 15.066V12.951H16.2614C16.2158 14.5492 15.9733 16.0409 15.5845 17.3321H14.7694C15.3043 17.763 15.8323 18.1685 16.3488 18.545C16.3615 18.511 16.3763 18.4798 16.389 18.4454H18.7849C18.6109 18.6516 18.4301 18.8516 18.2395 19.0423C18.0785 19.2032 17.9096 19.3561 17.7371 19.5047C18.1934 19.8018 18.6362 20.0715 19.061 20.3092C21.1633 18.3247 22.4774 15.5139 22.4774 12.3947C22.477 6.38243 17.6037 1.50916 11.5911 1.50879C8.47183 1.50916 5.66172 2.82288 3.67717 4.92448ZM19.5919 17.3322H16.7452C17.1127 16.0076 17.3352 14.5253 17.3783 12.9511H20.9752C20.8817 14.5536 20.3881 16.0452 19.5919 17.3322ZM19.5911 7.45657C20.3877 8.74348 20.8813 10.2355 20.9752 11.8377H17.3747C17.3312 10.2649 17.1152 8.78041 16.7477 7.45657H19.5911ZM18.239 5.74676C18.4296 5.9374 18.6105 6.13709 18.784 6.34329H16.3868C16.2081 5.85659 16.0113 5.39271 15.7917 4.96652C15.5326 4.46604 15.2455 4.01051 14.9324 3.6064C16.1838 4.08223 17.3077 4.81648 18.239 5.74676ZM12.148 3.01132C12.3028 3.02074 12.4565 3.0327 12.609 3.04901C12.8337 3.15593 13.0566 3.29801 13.2788 3.4788C14.0028 4.06774 14.6802 5.0629 15.1999 6.34324H12.148V3.01132ZM12.148 7.45657H15.5891C15.978 8.74746 16.219 10.2409 16.265 11.8377H12.148V7.45657ZM9.90404 3.4788C10.1262 3.29796 10.3491 3.15593 10.5738 3.04863C10.7263 3.03232 10.88 3.02037 11.0347 3.01095V6.34329H7.98869C8.11155 6.03991 8.24059 5.74713 8.38009 5.47751C8.82733 4.6106 9.35354 3.92857 9.90404 3.4788Z" fill="#616BF5"/>
                            <path d="M23.5809 19.873C23.3555 19.2653 23.0423 18.6083 22.6589 17.9117C22.3969 18.4343 22.0998 18.937 21.7692 19.4146C21.937 19.7614 22.0777 20.0876 22.1889 20.3884C22.4154 20.9954 22.5176 21.5017 22.5157 21.8402C22.5157 22.0007 22.4944 22.1214 22.4664 22.2026C22.4382 22.2842 22.4085 22.3266 22.3744 22.3617C22.34 22.3951 22.2979 22.4244 22.216 22.453C22.1352 22.4806 22.0146 22.5019 21.854 22.5019C21.5343 22.5034 21.0647 22.4132 20.5026 22.212C19.9394 22.0123 19.2824 21.7068 18.5616 21.306C16.0903 19.9325 12.8805 17.4384 9.71559 14.2713C6.54853 11.1065 4.05445 7.89663 2.68054 5.425C2.27976 4.70416 1.9746 4.04748 1.77454 3.48428C1.57378 2.9222 1.48317 2.45256 1.48462 2.13287C1.48462 1.97195 1.50637 1.8513 1.53393 1.77081C1.5622 1.68892 1.59159 1.64725 1.62525 1.61247C1.66078 1.57806 1.70278 1.5483 1.78471 1.52008C1.86553 1.49252 1.98618 1.47077 2.14748 1.47077C2.48559 1.46898 2.99189 1.57117 3.59784 1.79697C3.89971 1.90858 4.22737 2.04991 4.57603 2.21805C5.05368 1.88753 5.5552 1.59039 6.07818 1.32836C5.38017 0.943797 4.72171 0.630344 4.11285 0.404922C3.40035 0.142891 2.75348 -0.0115156 2.14753 -0.0136719C1.85934 -0.01325 1.57809 0.0229844 1.30846 0.113969C1.03959 0.204531 0.782949 0.354578 0.575339 0.563313C0.367308 0.770219 0.217636 1.02648 0.12773 1.29503C0.0367925 1.56428 0.000558105 1.84516 0.000183105 2.13292C0.00201123 2.70555 0.140058 3.31398 0.37598 3.9812C0.61298 4.64767 0.952542 5.37105 1.38346 6.14659C2.85989 8.79611 5.41996 12.0734 8.66601 15.3209C11.9136 18.567 15.1908 21.127 17.84 22.6035C18.6159 23.0343 19.3393 23.3739 20.0057 23.611C20.6729 23.8465 21.2814 23.9846 21.854 23.9864C22.1417 23.9864 22.4226 23.9498 22.6919 23.8592C22.9601 23.769 23.2163 23.6197 23.4236 23.4116C23.6324 23.2036 23.7824 22.947 23.8726 22.6788C23.9636 22.4092 23.9999 22.128 24.0002 21.8403C23.998 21.2335 23.8436 20.5863 23.5809 19.873Z" fill="#616BF5"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_181_518">
                                <rect width="24" height="24" fill="white" transform="translate(0 0.662109)"/>
                            </clipPath>
                        </defs>
                    </svg>
                );
            case 'code':
                return (
                    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-6">
                        <path d="M2.33325 6.11101C2.33325 4.01692 4.03084 2.31934 6.12492 2.31934H19.5416C21.6357 2.31934 23.3333 4.01692 23.3333 6.11101V9.33024C22.7442 9.26888 22.1524 9.30856 21.5833 9.44261V8.15267H4.08325V19.5277C4.08325 20.6553 4.99734 21.5694 6.12492 21.5694H15.4404L14.9257 22.4608C14.7672 22.7353 14.6593 23.025 14.599 23.3194H6.12492C4.03084 23.3194 2.33325 21.6217 2.33325 19.5277V6.11101ZM6.12492 4.06934C4.99734 4.06934 4.08325 4.98342 4.08325 6.11101V6.40267H21.5833V6.11101C21.5833 4.98342 20.6692 4.06934 19.5416 4.06934H6.12492Z" fill="#616BF5"/>
                        <path d="M23.1659 10.4866C22.9146 10.4633 22.663 10.4636 22.4143 10.4866C21.1392 10.6044 19.9391 11.3184 19.2502 12.5116C18.4234 13.9437 18.5627 15.6683 19.4658 16.9311L15.936 23.0447C15.8844 23.1341 15.8419 23.2261 15.8081 23.3199C15.534 24.0801 15.8344 24.9506 16.5588 25.3688C17.3725 25.8386 18.4132 25.5598 18.883 24.7461L22.4193 18.6211C23.9541 18.7586 25.5016 18.0172 26.3227 16.5949C27.2117 15.0553 26.9839 13.1773 25.8907 11.8988L23.9653 15.2339C23.5894 15.8849 22.7569 16.1079 22.1059 15.732C21.4549 15.3561 21.2319 14.5237 21.6078 13.8727L23.5332 10.5377C23.4111 10.5149 23.2885 10.4979 23.1659 10.4866Z" fill="#616BF5"/>
                        <path d="M17.6927 15.823C17.4944 15.0283 17.4806 14.1865 17.6707 13.3675L15.0008 10.6941C14.7732 10.4661 14.4039 10.4659 14.1759 10.6935C13.9479 10.9212 13.9477 11.2905 14.1753 11.5185L17.2589 14.6063L14.1753 17.6941C13.9477 17.9221 13.9479 18.2914 14.1759 18.519C14.4039 18.7467 14.7732 18.7464 15.0008 18.5185L17.6927 15.823Z" fill="#616BF5"/>
                        <path d="M11.4995 11.519C11.7275 11.2913 11.7277 10.922 11.5 10.694C11.2723 10.4661 10.9029 10.4659 10.675 10.6936L7.17108 14.1936C7.06154 14.3031 7 14.4515 7 14.6063C7 14.7611 7.06155 14.9096 7.17109 15.019L10.675 18.519C10.9029 18.7467 11.2723 18.7464 11.5 18.5186C11.7277 18.2906 11.7275 17.9212 11.4995 17.6936L8.40875 14.6063L11.4995 11.519Z" fill="#616BF5"/>
                    </svg>
                );
            case 'cloud':
                return (
                    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-6">
                        <path d="M11.6666 4.36133C12.1498 4.36133 12.5416 3.96958 12.5416 3.48633C12.5416 3.00308 12.1498 2.61133 11.6666 2.61133V4.36133ZM16.3333 2.61133C15.85 2.61133 15.4583 3.00308 15.4583 3.48633C15.4583 3.96958 15.85 4.36133 16.3333 4.36133V2.61133ZM15.1666 25.3614C15.6498 25.3614 16.0416 24.9696 16.0416 24.4863C16.0416 24.0031 15.6498 23.6113 15.1666 23.6113V25.3614ZM24.7916 15.153C24.7916 15.6362 25.1834 16.028 25.6666 16.028C26.1498 16.028 26.5416 15.6362 26.5416 15.153H24.7916ZM16.3333 14.8613C16.8165 14.8613 17.2083 14.4696 17.2083 13.9863C17.2083 13.5031 16.8165 13.1113 16.3333 13.1113V14.8613ZM20.9999 13.1113C20.5167 13.1113 20.1249 13.5031 20.1249 13.9863C20.1249 14.4696 20.5167 14.8613 20.9999 14.8613V13.1113ZM3.20825 15.153V13.9863H1.45825V15.153H3.20825ZM3.20825 13.9863V12.8197H1.45825V13.9863H3.20825ZM15.1666 23.6113H11.6666V25.3614H15.1666V23.6113ZM24.7916 12.8197V13.9863H26.5416V12.8197H24.7916ZM1.45825 15.153C1.45825 17.3281 1.4564 19.0476 1.6369 20.3902C1.82081 21.758 2.20828 22.8651 3.08137 23.7383L4.31881 22.5008C3.82506 22.0071 3.52911 21.3307 3.37131 20.1571C3.21011 18.9581 3.20825 17.3776 3.20825 15.153H1.45825ZM11.6666 23.6113C9.44197 23.6113 7.86152 23.6095 6.66258 23.4482C5.48881 23.2905 4.81255 22.9945 4.31881 22.5008L3.08137 23.7383C3.95447 24.6113 5.06156 24.9988 6.4294 25.1827C7.77205 25.3632 9.49144 25.3614 11.6666 25.3614V23.6113ZM16.3333 4.36133C18.5579 4.36133 20.1384 4.36319 21.3373 4.52438C22.511 4.68219 23.1873 4.97814 23.6811 5.47188L24.9185 4.23444C24.0454 3.36136 22.9382 2.97388 21.5704 2.78998C20.2278 2.60947 18.5084 2.61133 16.3333 2.61133V4.36133ZM26.5416 12.8197C26.5416 10.6445 26.5435 8.92513 26.363 7.58248C26.179 6.21464 25.7916 5.10754 24.9185 4.23444L23.6811 5.47188C24.1748 5.96563 24.4708 6.64189 24.6285 7.81566C24.7897 9.0146 24.7916 10.595 24.7916 12.8197H26.5416ZM11.6666 2.61133C9.49144 2.61133 7.77205 2.60947 6.4294 2.78998C5.06158 2.97388 3.95447 3.36136 3.08137 4.23444L4.31881 5.47188C4.81255 4.97814 5.48881 4.68219 6.66258 4.52438C7.86152 4.36319 9.44197 4.36133 11.6666 4.36133V2.61133ZM3.20825 12.8197C3.20825 10.595 3.21011 9.0146 3.37131 7.81566C3.52911 6.64189 3.82506 5.96563 4.31881 5.47188L3.08137 4.23444C2.20828 5.10754 1.82081 6.21464 1.6369 7.58248C1.4564 8.92513 1.45825 10.6445 1.45825 12.8197H3.20825ZM24.7916 13.9863V15.153H26.5416V13.9863H24.7916ZM2.33325 14.8613H16.3333V13.1113H2.33325V14.8613ZM20.9999 14.8613H25.6666V13.1113H20.9999V14.8613Z" fill="#616BF5"/>
                        <path d="M15.7499 8.73682H20.9999" stroke="#616BF5" stroke-width="1.75" stroke-linecap="round"/>
                        <path d="M6.99994 20.4032V18.0698" stroke="#616BF5" stroke-width="1.75" stroke-linecap="round"/>
                        <path d="M6.99994 9.90316V7.56982" stroke="#616BF5" stroke-width="1.75" stroke-linecap="round"/>
                        <path d="M10.4999 20.4032V18.0698" stroke="#616BF5" stroke-width="1.75" stroke-linecap="round"/>
                        <path d="M10.4999 9.90316V7.56982" stroke="#616BF5" stroke-width="1.75" stroke-linecap="round"/>
                        <path d="M22.5555 19.6857C22.7988 19.5947 23.0605 19.5451 23.3333 19.5451C23.6006 19.5451 23.8575 19.5927 24.0966 19.6804M24.0966 19.6804C25.0104 20.0152 25.6666 20.9343 25.6666 22.0158C25.6666 23.3802 24.622 24.4863 23.3333 24.4863H19.2499C18.2835 24.4863 17.4999 23.6567 17.4999 22.6334C17.4999 21.61 18.2835 20.7804 19.2499 20.7804C19.3659 20.7804 19.4793 20.7924 19.5891 20.8152M24.0966 19.6804C23.9668 18.446 22.9781 17.4863 21.7778 17.4863C20.4891 17.4863 19.4444 18.5924 19.4444 19.9569C19.4444 20.2587 19.4955 20.5479 19.5891 20.8152M19.5891 20.8152C19.8195 20.8631 20.0337 20.9589 20.2221 21.0925" stroke="#616BF5" stroke-width="1.75" stroke-linecap="round"/>
                    </svg>
                );
            case 'shield':
                return (
                    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-6">
                        <g clipPath="url(#clip0_181_701)">
                            <path fillRule="evenodd" clipRule="evenodd" d="M16.0427 26.5273C14.8207 27.4001 13.1793 27.4001 11.9574 26.5273L7.61266 23.4239C3.93353 20.7959 1.75 16.553 1.75 12.0317V7.55364C1.75 6.14783 2.5878 4.87728 3.87994 4.3235L12.6157 0.57962C13.4997 0.200756 14.5003 0.200756 15.3844 0.579619L24.1201 4.3235C25.4122 4.87728 26.25 6.14783 26.25 7.55364V12.0317C26.25 16.553 24.0665 20.7959 20.3873 23.4239L16.0427 26.5273ZM20.125 11.2112C20.8084 10.5278 20.8084 9.41976 20.125 8.73634C19.4416 8.05292 18.3336 8.05292 17.6502 8.73633L13.8188 12.5677C12.9524 13.4341 11.5476 13.4341 10.6812 12.5677L10.3499 12.2363C9.66647 11.5529 8.55843 11.5529 7.87501 12.2363C7.19159 12.9198 7.19159 14.0278 7.87501 14.7112L9.76504 16.6012C11.1375 17.9736 13.3626 17.9736 14.735 16.6012L20.125 11.2112Z" fill="#616BF5"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_181_701">
                                <rect width="28" height="28" fill="white" transform="translate(0 0.662109)"/>
                            </clipPath>
                        </defs>
                    </svg>
                );
            default:
                return null;
        }
    };

    return (
        <div className="w-full bg-white py-16">
            <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-12">
                {/* Header Section */}
                <motion.div
                    className="text-center mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.7 }}
                    viewport={{ once: true }}
                >
                    {/* "We are" badge */}
                    <motion.div
                        className="bg-blue-600 rounded-full inline-block px-4 py-2 mb-4 text-sm text-white font-medium font-inter"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                    >
                        {t('we_are')}
                    </motion.div>

                    {/* Company name with cloud icons */}
                    <motion.div
                        className="flex items-center justify-center gap-2 mb-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                    >
                        <svg width="35" height="22" viewBox="0 0 44 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22.1463 28.7714C6.43728 28.0389 7.32583 15.2397 9.73374 8.93164C7.41406 10.6409 6.46104 15.104 6.27449 17.1219C-1.50881 17.1219 -1.10186 28.7714 5.56231 28.7714H22.1463Z" fill="#497EF7"/>
                            <path d="M36.9615 9.66653C34.1534 2.50386 27.7877 1.20493 25.2611 1.42538C15.6159 20.6344 30.1278 27.0815 38.4367 27.8784C48.1633 21.5297 42.0995 10.277 36.9615 9.66653Z" fill="#497EF7"/>
                            <path d="M34.2573 28.9751C12.5861 27.042 17.8767 5.98127 23.7269 0.283691C18.1107 1.34181 15.011 6.96478 14.1631 9.644C11.4364 10.2951 10.6869 10.8649 10.653 11.0684C6.99028 26.7367 24.863 29.5346 34.2573 28.9751Z" fill="#497EF7"/>
                            <path d="M32.3454 9.89544C30.921 5.37807 27.0039 3.06174 25.2234 2.46824C25.2404 2.40041 25.315 2.17319 25.4778 1.80691C31.1754 0.870881 35.2451 6.80925 36.5677 9.89544C39.9456 10.5873 42.2484 14.4908 42.9775 16.3561C41.8075 18.3401 39.3555 21.2295 38.9078 16.9157C38.3482 11.5233 32.2791 9.69195 32.3454 9.89544Z" fill="url(#paint0_linear_181_457)"/>
                            <path d="M15.5071 20.7337C10.6235 9.01296 18.5255 2.48788 23.0869 0.69043C16.8603 8.30077 18.1185 16.5453 19.526 19.7163C20.1364 21.8868 20.1873 25.129 15.5071 20.7337Z" fill="#497EF7"/>
                            <path d="M4.1453 17.6816C4.59297 17.3561 5.79014 17.3425 6.33276 17.3764C6.21602 15.0417 7.85729 11.7147 8.80188 10.1527C7.07375 17.4375 9.14888 22.4805 10.4025 24.0914C11.1316 25.4989 11.9184 28.0899 9.23243 27.1946C5.87493 26.0754 3.58572 18.0886 4.1453 17.6816Z" fill="#497EF7"/>
                            <defs>
                            <linearGradient id="paint0_linear_181_457" x1="34.1005" y1="1.70801" x2="34.1005" y2="19.158" gradientUnits="userSpaceOnUse">
                            <stop stop-color="white"/>
                            <stop offset="1" stop-color="#C4C4C4" stop-opacity="0"/>
                            </linearGradient>
                            </defs>
                        </svg>

                        <Typography variant="h2" className="text-3xl md:text-4xl font-bold text-gray-900">
                            {t('company_name', 'ZTechEngineering')}
                        </Typography>
                        <svg width="35" height="22" viewBox="0 0 44 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22.1463 28.7714C6.43728 28.0389 7.32583 15.2397 9.73374 8.93164C7.41406 10.6409 6.46104 15.104 6.27449 17.1219C-1.50881 17.1219 -1.10186 28.7714 5.56231 28.7714H22.1463Z" fill="#497EF7"/>
                        <path d="M36.9615 9.66653C34.1534 2.50386 27.7877 1.20493 25.2611 1.42538C15.6159 20.6344 30.1278 27.0815 38.4367 27.8784C48.1633 21.5297 42.0995 10.277 36.9615 9.66653Z" fill="#497EF7"/>
                        <path d="M34.2573 28.9751C12.5861 27.042 17.8767 5.98127 23.7269 0.283691C18.1107 1.34181 15.011 6.96478 14.1631 9.644C11.4364 10.2951 10.6869 10.8649 10.653 11.0684C6.99028 26.7367 24.863 29.5346 34.2573 28.9751Z" fill="#497EF7"/>
                        <path d="M32.3454 9.89544C30.921 5.37807 27.0039 3.06174 25.2234 2.46824C25.2404 2.40041 25.315 2.17319 25.4778 1.80691C31.1754 0.870881 35.2451 6.80925 36.5677 9.89544C39.9456 10.5873 42.2484 14.4908 42.9775 16.3561C41.8075 18.3401 39.3555 21.2295 38.9078 16.9157C38.3482 11.5233 32.2791 9.69195 32.3454 9.89544Z" fill="url(#paint0_linear_181_457)"/>
                        <path d="M15.5071 20.7337C10.6235 9.01296 18.5255 2.48788 23.0869 0.69043C16.8603 8.30077 18.1185 16.5453 19.526 19.7163C20.1364 21.8868 20.1873 25.129 15.5071 20.7337Z" fill="#497EF7"/>
                        <path d="M4.1453 17.6816C4.59297 17.3561 5.79014 17.3425 6.33276 17.3764C6.21602 15.0417 7.85729 11.7147 8.80188 10.1527C7.07375 17.4375 9.14888 22.4805 10.4025 24.0914C11.1316 25.4989 11.9184 28.0899 9.23243 27.1946C5.87493 26.0754 3.58572 18.0886 4.1453 17.6816Z" fill="#497EF7"/>
                        <defs>
                        <linearGradient id="paint0_linear_181_457" x1="34.1005" y1="1.70801" x2="34.1005" y2="19.158" gradientUnits="userSpaceOnUse">
                        <stop stop-color="white"/>
                        <stop offset="1" stop-color="#C4C4C4" stop-opacity="0"/>
                        </linearGradient>
                        </defs>
                        </svg>

                    </motion.div>

                    {/* Tagline */}
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                    >
                        <Typography variant="h3" className="text-xl md:text-2xl font-semibold text-gray-800 mb-4">
                            {t('web_dev_cloud_security_experts')}
                        </Typography>
                    </motion.div>

                    {/* Description */}
                    <motion.p
                        className="text-base text-gray-600 max-w-3xl mx-auto mb-12"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                    >
                        {t('company_description')}
                    </motion.p>

                    {/* Feature Cards */}
                    <motion.div
                        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.5 }}
                        viewport={{ once: true }}
                    >
                        {features.map((feature, index) => (
                            <motion.div
                                key={index}
                                className="bg-gradient-to-b from-[#f8faff] to-white rounded-2xl border border-gray-100 p-6 shadow-[0_8px_30px_rgba(0,0,0,0.08)] hover:shadow-[0_15px_35px_rgba(0,0,0,0.12)] transition-all duration-300 relative overflow-hidden"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: 0.1 * index }}
                                viewport={{ once: true }}
                            >
                                {/* Full grid pattern background */}
                                <div className="absolute inset-0 w-full h-full">
                                    <div className="w-full h-full grid grid-cols-8 grid-rows-8">
                                        {[...Array(64)].map((_, i) => (
                                            <div key={i} className="border border-gray-100"></div>
                                        ))}
                                    </div>
                                </div>

                                {/* Content area with white background and gradient fade */}
                                <div className="absolute inset-0 bg-gradient-to-tr from-white via-white to-transparent z-[1]"></div>

                                <div className="flex justify-start mb-5 relative z-10">
                                    <div className="bg-white p-3 rounded-xl shadow-sm border border-[#e0e7ff]">
                                        {getIcon(feature.icon)}
                                    </div>
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 mb-3 text-left relative z-10">{feature.title}</h3>
                                <p className="text-sm text-gray-600 text-left relative z-10">{feature.description}</p>
                            </motion.div>
                        ))}
                    </motion.div>

                    {/* Team Icons - Curved Layout (Hidden on mobile) */}
                    <motion.div
                        className="relative h-28 md:h-32 w-full mt-8 max-w-5xl mx-auto hidden md:block"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.6 }}
                        viewport={{ once: true }}
                    >
                        {/* Person 1 */}
                        <motion.div
                            className="absolute left-[calc(50%-24rem)] md:left-[calc(45%-23.5rem)] -top-3 w-12 h-12 md:w-14 md:h-14 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.1 }}
                            viewport={{ once: true }}
                            title="Developer"
                        >
                            <div className="relative w-full h-full">
                                <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                <Image
                                    src="/images/team/person-1.png"
                                    alt="Team Member"
                                    width={56}
                                    height={56}
                                    className="relative z-10 w-full h-full object-cover"
                                />
                            </div>
                        </motion.div>

                        {/* Code icon */}
                        <motion.div
                            className="absolute left-[calc(50%-18.5rem)] md:left-[calc(45%-18rem)] top-[25%] w-12 h-12 md:w-14 md:h-14 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.2 }}
                            viewport={{ once: true }}
                            title="Code"
                        >
                            {/* Light blue circle inside white circle */}
                            <div className="w-9 h-9 md:w-10 md:h-10 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                <svg width="24" height="24" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.6272 7.39607C11.0121 7.04969 11.0433 6.45693 10.6969 6.07207C10.3505 5.68722 9.75776 5.65602 9.37291 6.00239L7.20105 7.95707C6.28062 8.78539 5.51711 9.47251 4.99285 10.0957C4.44156 10.751 4.04968 11.4551 4.04968 12.3242C4.04968 13.1934 4.44156 13.8975 4.99285 14.5527C5.51711 15.176 6.28061 15.8631 7.20103 16.6914L9.37291 18.6461C9.75776 18.9925 10.3505 18.9612 10.6969 18.5764C11.0433 18.1915 11.0121 17.5987 10.6272 17.2524L8.50663 15.3439C7.52178 14.4575 6.85778 13.857 6.42766 13.3457C6.01485 12.855 5.92468 12.5685 5.92468 12.3242C5.92468 12.0799 6.01485 11.7935 6.42766 11.3027C6.85778 10.7915 7.52178 10.191 8.50663 9.30459L10.6272 7.39607Z" fill="#606AF5"/>
                                <path d="M17.7244 6.0063C18.2248 6.1393 18.5225 6.65276 18.3896 7.15316L13.406 25.9032C13.273 26.4036 12.7596 26.7013 12.2592 26.5683C11.7588 26.4353 11.461 25.922 11.594 25.4216L16.5775 6.67152C16.7105 6.17114 17.224 5.8733 17.7244 6.0063Z" fill="#606AF5"/>
                                <path d="M19.3031 13.785C19.6495 13.4001 20.2422 13.3689 20.6271 13.7153L22.799 15.67C23.7195 16.4983 24.483 17.1854 25.0072 17.8086C25.5585 18.464 25.9504 19.168 25.9504 20.0371C25.9504 20.9063 25.5585 21.6104 25.0072 22.2656C24.483 22.8889 23.7195 23.576 22.799 24.4043L20.6271 26.359C20.2422 26.7054 19.6495 26.6741 19.3031 26.2893C18.9567 25.9044 18.988 25.3116 19.3729 24.9653L21.4934 23.0568C22.4782 22.1704 23.1422 21.5699 23.5724 21.0586C23.9852 20.5679 24.0754 20.2814 24.0754 20.0371C24.0754 19.7929 23.9852 19.5064 23.5724 19.0156C23.1422 18.5044 22.4782 17.9039 21.4934 17.0175L19.3729 15.109C18.988 14.7626 18.9567 14.1699 19.3031 13.785Z" fill="#606AF5"/>
                                </svg>
                            </div>
                        </motion.div>

                        {/* Person 2 */}
                        <motion.div
                            className="absolute left-[calc(50%-13rem)] md:left-[calc(45%-12.5rem)] top-[45%] w-12 h-12 md:w-16 md:h-16 rounded-full bg-blue-50 flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.3 }}
                            viewport={{ once: true }}
                            title="Team Member"
                        >
                            <div className="relative w-full h-full">
                                <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                <Image
                                    src="/images/team/person-2.png"
                                    alt="Team Member"
                                    width={64}
                                    height={64}
                                    className="relative z-10 w-full h-full object-cover"
                                />
                            </div>
                        </motion.div>

                        {/* Teams/Communication icon */}
                        <motion.div
                            className="absolute left-[calc(50%-7.5rem)] md:left-[calc(45%-7rem)] top-[50%] w-12 h-12 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.4 }}
                            viewport={{ once: true }}
                            title="Communication"
                        >
                            {/* Light blue circle inside white circle */}
                            <div className="w-9 h-9 md:w-16 md:h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                <svg width="28" height="28" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_181_722)">
                                    <path d="M22.5 24.4121H20.4125L18.25 22.7871L16.75 24.7871L19.5875 26.9121H22.5V29.4121H30V21.9121H22.5V24.4121ZM25 24.4121H27.5V26.9121H25V24.4121Z" fill="#606AF5"/>
                                    <path d="M22.5 14.4121H18.75V16.9121H22.5V19.4121H30V11.9121H22.5V14.4121ZM25 14.4121H27.5V16.9121H25V14.4121Z" fill="#606AF5"/>
                                    <path d="M22.5 1.91211V4.41211H19.5875L16.75 6.53711L18.25 8.53711L20.4125 6.91211H22.5V9.41211H30V1.91211H22.5ZM27.5 6.91211H25V4.41211H27.5V6.91211Z" fill="#606AF5"/>
                                    <path d="M17.5 16.2871V15.6621V10.6621C17.5 7.81211 13.7375 5.66211 8.75 5.66211C3.7625 5.66211 0 7.81211 0 10.6621V15.6621V16.2871V21.2871C0 24.1621 4.4 25.6621 8.75 25.6621C13.1 25.6621 17.5 24.1621 17.5 21.2871V16.2871ZM8.75 8.16211C12.5625 8.16211 15 9.63711 15 10.6621C15 11.6871 12.5625 13.1621 8.75 13.1621C4.9375 13.1621 2.5 11.6871 2.5 10.6621C2.5 9.63711 4.9375 8.16211 8.75 8.16211ZM2.5 14.2246C4.0625 15.1246 6.25 15.6621 8.75 15.6621C11.25 15.6621 13.4375 15.1246 15 14.2246V15.6621V16.2871C15 16.7746 12.85 18.1621 8.75 18.1621C4.65 18.1621 2.5 16.7746 2.5 16.2871V15.6621V14.2246ZM8.75 23.1621C4.65 23.1621 2.5 21.7746 2.5 21.2871V19.4246C4.1625 20.2371 6.4625 20.6621 8.75 20.6621C11.0375 20.6621 13.3375 20.2371 15 19.4246V21.2871C15 21.7746 12.85 23.1621 8.75 23.1621Z" fill="#606AF5"/>
                                    </g>
                                    <defs>
                                    <clipPath id="clip0_181_722">
                                    <rect width="30" height="30" fill="white" transform="translate(0 0.662109)"/>
                                    </clipPath>
                                    </defs>
                                </svg>
                            </div>
                        </motion.div>

                        {/* Person 3 - Center */}
                        <motion.div
                            className="absolute left-[45%] -translate-x-1/2 top-[50%] w-16 h-20 md:w-24 md:h-24 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.5 }}
                            viewport={{ once: true }}
                            title="Team Lead"
                        >
                            <div className="relative w-full h-full">
                                <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                <Image
                                    src="/images/team/person-3.png"
                                    alt="Team Lead"
                                    width={116}
                                    height={116}
                                    className="relative z-10 w-full h-full object-cover object-[center_2%] scale-[1.8] translate-y-4"
                                    priority
                                />
                            </div>
                        </motion.div>

                        {/* Cloud icon */}
                        <motion.div
                            className="absolute left-[calc(50%+7.5rem)] md:left-[calc(45%+8rem)] top-[50%] w-12 h-12 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.6 }}
                            viewport={{ once: true }}
                            title="Cloud"
                        >
                            {/* Light blue circle inside white circle */}
                            <div className="w-9 h-9 md:w-16 md:h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                <svg width="28" height="18" viewBox="0 0 30 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20.1771 3.086C14.9113 -2.07025 5.99133 0.759126 4.67326 8.00465C2.03379 8.3875 0 10.6653 0 13.4092C0 16.4205 2.44986 18.8703 5.46117 18.8703H22.0028C26.4125 18.8703 29.9999 15.2829 29.9999 10.8733C30 5.75049 25.212 1.90897 20.1771 3.086Z" fill="#606AF5"/>
                                </svg>
                            </div>
                        </motion.div>

                        {/* Person 4 */}
                        <motion.div
                            className="absolute left-[calc(50%+13rem)] md:left-[calc(45%+14rem)] top-[45%] w-12 h-12 md:w-16 md:h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.7 }}
                            viewport={{ once: true }}
                            title="Team Member"
                        >
                            <div className="relative w-full h-full">
                                <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                <Image
                                    src="/images/team/person-4.png"
                                    alt="Team Member"
                                    width={64}
                                    height={64}
                                    className="relative z-10 w-full h-full object-cover -translate-x-1"
                                />
                            </div>
                        </motion.div>

                        {/* Document/List icon */}
                        <motion.div
                            className="absolute left-[calc(50%+18.5rem)] md:left-[calc(45%+20rem)] top-[25%] w-12 h-12 md:w-14 md:h-14 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.8 }}
                            viewport={{ once: true }}
                            title="Documentation"
                        >
                            {/* Light blue circle inside white circle */}
                            <div className="w-9 h-9 md:w-10 md:h-10 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2 18C2 17.0681 2 16.6022 2.15224 16.2346C2.35523 15.7446 2.74458 15.3552 3.23463 15.1522C3.60218 15 4.06812 15 5 15H19C19.9319 15 20.3978 15 20.7654 15.1522C21.2554 15.3552 21.6448 15.7446 21.8478 16.2346C22 16.6022 22 17.0681 22 18C22 18.9319 22 19.3978 21.8478 19.7654C21.6448 20.2554 21.2554 20.6448 20.7654 20.8478C20.3978 21 19.9319 21 19 21H5C4.06812 21 3.60218 21 3.23463 20.8478C2.74458 20.6448 2.35523 20.2554 2.15224 19.7654C2 19.3978 2 18.9319 2 18Z" stroke="#606AF5" stroke-width="1.5"/>
                                <path d="M2 12C2 11.0681 2 10.6022 2.15224 10.2346C2.35523 9.74458 2.74458 9.35523 3.23463 9.15224C3.60218 9 4.06812 9 5 9H19C19.9319 9 20.3978 9 20.7654 9.15224C21.2554 9.35523 21.6448 9.74458 21.8478 10.2346C22 10.6022 22 11.0681 22 12C22 12.9319 22 13.3978 21.8478 13.7654C21.6448 14.2554 21.2554 14.6448 20.7654 14.8478C20.3978 15 19.9319 15 19 15H5C4.06812 15 3.60218 15 3.23463 14.8478C2.74458 14.6448 2.35523 14.2554 2.15224 13.7654C2 13.3978 2 12.9319 2 12Z" stroke="#606AF5" stroke-width="1.5"/>
                                <path d="M2 6C2 5.06812 2 4.60218 2.15224 4.23463C2.35523 3.74458 2.74458 3.35523 3.23463 3.15224C3.60218 3 4.06812 3 5 3H19C19.9319 3 20.3978 3 20.7654 3.15224C21.2554 3.35523 21.6448 3.74458 21.8478 4.23463C22 4.60218 22 5.06812 22 6C22 6.93188 22 7.39782 21.8478 7.76537C21.6448 8.25542 21.2554 8.64477 20.7654 8.84776C20.3978 9 19.9319 9 19 9H5C4.06812 9 3.60218 9 3.23463 8.84776C2.74458 8.64477 2.35523 8.25542 2.15224 7.76537C2 7.39782 2 6.93188 2 6Z" stroke="#606AF5" stroke-width="1.5"/>
                                <path opacity="0.5" d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" fill="#606AF5"/>
                                <path opacity="0.5" d="M5 7C5.55228 7 6 6.55228 6 6C6 5.44772 5.55228 5 5 5C4.44772 5 4 5.44772 4 6C4 6.55228 4.44772 7 5 7Z" fill="#606AF5"/>
                                <path opacity="0.5" d="M5 19C5.55228 19 6 18.5523 6 18C6 17.4477 5.55228 17 5 17C4.44772 17 4 17.4477 4 18C4 18.5523 4.44772 19 5 19Z" fill="#606AF5"/>
                                </svg>
                            </div>
                        </motion.div>

                        {/* Person 5 */}
                        <motion.div
                            className="absolute left-[calc(50%+24rem)] md:left-[calc(45%+26rem)] -top-3 w-12 h-12 md:w-14 md:h-14 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3, delay: 0.9 }}
                            viewport={{ once: true }}
                            title="Developer"
                        >
                            <div className="relative w-full h-full">
                                <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                <Image
                                    src="/images/team/person-5.png"
                                    alt="Team Member"
                                    width={56}
                                    height={66}
                                    className="relative z-10 w-full h-full object-cover object-[center_2%] scale-[1.0] translate-y-1"
                                />
                            </div>
                        </motion.div>
                    </motion.div>

                    {/* Team Icons - Mobile Layout */}
                    <motion.div
                        className="md:hidden w-full mt-12 mb-8 max-w-[360px] mx-auto"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.5 }}
                        viewport={{ once: true }}
                    >
                        {/* First Row */}
                        <div className="flex justify-center items-center gap-5 mb-8">
                            {/* Person 1 */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.1 }}
                                viewport={{ once: true }}
                                title="Developer"
                            >
                                <div className="relative w-full h-full">
                                    <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                    <Image
                                        src="/images/team/person-1.png"
                                        alt="Team Member"
                                        width={56}
                                        height={56}
                                        className="relative z-10 w-full h-full object-cover"
                                    />
                                </div>
                            </motion.div>

                            {/* Code icon */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.2 }}
                                viewport={{ once: true }}
                                title="Code"
                            >
                                {/* Light blue circle inside white circle */}
                                <div className="w-12 h-12 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                    <svg width="30" height="30" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.6272 7.39607C11.0121 7.04969 11.0433 6.45693 10.6969 6.07207C10.3505 5.68722 9.75776 5.65602 9.37291 6.00239L7.20105 7.95707C6.28062 8.78539 5.51711 9.47251 4.99285 10.0957C4.44156 10.751 4.04968 11.4551 4.04968 12.3242C4.04968 13.1934 4.44156 13.8975 4.99285 14.5527C5.51711 15.176 6.28061 15.8631 7.20103 16.6914L9.37291 18.6461C9.75776 18.9925 10.3505 18.9612 10.6969 18.5764C11.0433 18.1915 11.0121 17.5987 10.6272 17.2524L8.50663 15.3439C7.52178 14.4575 6.85778 13.857 6.42766 13.3457C6.01485 12.855 5.92468 12.5685 5.92468 12.3242C5.92468 12.0799 6.01485 11.7935 6.42766 11.3027C6.85778 10.7915 7.52178 10.191 8.50663 9.30459L10.6272 7.39607Z" fill="#606AF5"/>
                                    <path d="M17.7244 6.0063C18.2248 6.1393 18.5225 6.65276 18.3896 7.15316L13.406 25.9032C13.273 26.4036 12.7596 26.7013 12.2592 26.5683C11.7588 26.4353 11.461 25.922 11.594 25.4216L16.5775 6.67152C16.7105 6.17114 17.224 5.8733 17.7244 6.0063Z" fill="#606AF5"/>
                                    <path d="M19.3031 13.785C19.6495 13.4001 20.2422 13.3689 20.6271 13.7153L22.799 15.67C23.7195 16.4983 24.483 17.1854 25.0072 17.8086C25.5585 18.464 25.9504 19.168 25.9504 20.0371C25.9504 20.9063 25.5585 21.6104 25.0072 22.2656C24.483 22.8889 23.7195 23.576 22.799 24.4043L20.6271 26.359C20.2422 26.7054 19.6495 26.6741 19.3031 26.2893C18.9567 25.9044 18.988 25.3116 19.3729 24.9653L21.4934 23.0568C22.4782 22.1704 23.1422 21.5699 23.5724 21.0586C23.9852 20.5679 24.0754 20.2814 24.0754 20.0371C24.0754 19.7929 23.9852 19.5064 23.5724 19.0156C23.1422 18.5044 22.4782 17.9039 21.4934 17.0175L19.3729 15.109C18.988 14.7626 18.9567 14.1699 19.3031 13.785Z" fill="#606AF5"/>
                                    </svg>
                                </div>
                            </motion.div>

                            {/* Person 2 */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.3 }}
                                viewport={{ once: true }}
                                title="Team Member"
                            >
                                <div className="relative w-full h-full">
                                    <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                    <Image
                                        src="/images/team/person-2.png"
                                        alt="Team Member"
                                        width={64}
                                        height={64}
                                        className="relative z-10 w-full h-full object-cover"
                                    />
                                </div>
                            </motion.div>
                        </div>

                        {/* Middle Row */}
                        <div className="flex justify-center items-center gap-8 mb-8">
                            {/* Database/Communication icon */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.4 }}
                                viewport={{ once: true }}
                                title="Communication"
                            >
                                {/* Light blue circle inside white circle */}
                                <div className="w-12 h-12 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                    <svg width="30" height="30" viewBox="0 0 30 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <g clip-path="url(#clip0_181_722)">
                                        <path d="M22.5 24.4121H20.4125L18.25 22.7871L16.75 24.7871L19.5875 26.9121H22.5V29.4121H30V21.9121H22.5V24.4121ZM25 24.4121H27.5V26.9121H25V24.4121Z" fill="#606AF5"/>
                                        <path d="M22.5 14.4121H18.75V16.9121H22.5V19.4121H30V11.9121H22.5V14.4121ZM25 14.4121H27.5V16.9121H25V14.4121Z" fill="#606AF5"/>
                                        <path d="M22.5 1.91211V4.41211H19.5875L16.75 6.53711L18.25 8.53711L20.4125 6.91211H22.5V9.41211H30V1.91211H22.5ZM27.5 6.91211H25V4.41211H27.5V6.91211Z" fill="#606AF5"/>
                                        <path d="M17.5 16.2871V15.6621V10.6621C17.5 7.81211 13.7375 5.66211 8.75 5.66211C3.7625 5.66211 0 7.81211 0 10.6621V15.6621V16.2871V21.2871C0 24.1621 4.4 25.6621 8.75 25.6621C13.1 25.6621 17.5 24.1621 17.5 21.2871V16.2871ZM8.75 8.16211C12.5625 8.16211 15 9.63711 15 10.6621C15 11.6871 12.5625 13.1621 8.75 13.1621C4.9375 13.1621 2.5 11.6871 2.5 10.6621C2.5 9.63711 4.9375 8.16211 8.75 8.16211ZM2.5 14.2246C4.0625 15.1246 6.25 15.6621 8.75 15.6621C11.25 15.6621 13.4375 15.1246 15 14.2246V15.6621V16.2871C15 16.7746 12.85 18.1621 8.75 18.1621C4.65 18.1621 2.5 16.7746 2.5 16.2871V15.6621V14.2246ZM8.75 23.1621C4.65 23.1621 2.5 21.7746 2.5 21.2871V19.4246C4.1625 20.2371 6.4625 20.6621 8.75 20.6621C11.0375 20.6621 13.3375 20.2371 15 19.4246V21.2871C15 21.7746 12.85 23.1621 8.75 23.1621Z" fill="#606AF5"/>
                                        </g>
                                        <defs>
                                        <clipPath id="clip0_181_722">
                                        <rect width="30" height="30" fill="white" transform="translate(0 0.662109)"/>
                                        </clipPath>
                                        </defs>
                                    </svg>
                                </div>
                            </motion.div>

                            {/* Person 3 - Center (Larger) */}
                            <motion.div
                                className="w-24 h-24 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.5 }}
                                viewport={{ once: true }}
                                title="Team Lead"
                            >
                                <div className="relative w-full h-full">
                                    <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                    <Image
                                        src="/images/team/person-3.png"
                                        alt="Team Lead"
                                        width={116}
                                        height={116}
                                        className="relative z-10 w-full h-full object-cover object-[center_2%] scale-[1.8] translate-y-4"
                                        priority
                                    />
                                </div>
                            </motion.div>

                            {/* Cloud icon */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.6 }}
                                viewport={{ once: true }}
                                title="Cloud"
                            >
                                {/* Light blue circle inside white circle */}
                                <div className="w-12 h-12 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                    <svg width="30" height="20" viewBox="0 0 30 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20.1771 3.086C14.9113 -2.07025 5.99133 0.759126 4.67326 8.00465C2.03379 8.3875 0 10.6653 0 13.4092C0 16.4205 2.44986 18.8703 5.46117 18.8703H22.0028C26.4125 18.8703 29.9999 15.2829 29.9999 10.8733C30 5.75049 25.212 1.90897 20.1771 3.086Z" fill="#606AF5"/>
                                    </svg>
                                </div>
                            </motion.div>
                        </div>

                        {/* Bottom Row */}
                        <div className="flex justify-center items-center gap-5">
                            {/* Person 4 */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.7 }}
                                viewport={{ once: true }}
                                title="Team Member"
                            >
                                <div className="relative w-full h-full">
                                    <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                    <Image
                                        src="/images/team/person-4.png"
                                        alt="Team Member"
                                        width={64}
                                        height={64}
                                        className="relative z-10 w-full h-full object-cover -translate-x-1"
                                    />
                                </div>
                            </motion.div>

                            {/* Document/List icon */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-white flex items-center justify-center border-2 border-white shadow-md"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.8 }}
                                viewport={{ once: true }}
                                title="Documentation"
                            >
                                {/* Light blue circle inside white circle */}
                                <div className="w-12 h-12 rounded-full bg-[#f1f5ff] flex items-center justify-center">
                                    <svg width="26" height="26" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2 18C2 17.0681 2 16.6022 2.15224 16.2346C2.35523 15.7446 2.74458 15.3552 3.23463 15.1522C3.60218 15 4.06812 15 5 15H19C19.9319 15 20.3978 15 20.7654 15.1522C21.2554 15.3552 21.6448 15.7446 21.8478 16.2346C22 16.6022 22 17.0681 22 18C22 18.9319 22 19.3978 21.8478 19.7654C21.6448 20.2554 21.2554 20.6448 20.7654 20.8478C20.3978 21 19.9319 21 19 21H5C4.06812 21 3.60218 21 3.23463 20.8478C2.74458 20.6448 2.35523 20.2554 2.15224 19.7654C2 19.3978 2 18.9319 2 18Z" stroke="#606AF5" stroke-width="1.5"/>
                                    <path d="M2 12C2 11.0681 2 10.6022 2.15224 10.2346C2.35523 9.74458 2.74458 9.35523 3.23463 9.15224C3.60218 9 4.06812 9 5 9H19C19.9319 9 20.3978 9 20.7654 9.15224C21.2554 9.35523 21.6448 9.74458 21.8478 10.2346C22 10.6022 22 11.0681 22 12C22 12.9319 22 13.3978 21.8478 13.7654C21.6448 14.2554 21.2554 14.6448 20.7654 14.8478C20.3978 15 19.9319 15 19 15H5C4.06812 15 3.60218 15 3.23463 14.8478C2.74458 14.6448 2.35523 14.2554 2.15224 13.7654C2 13.3978 2 12.9319 2 12Z" stroke="#606AF5" stroke-width="1.5"/>
                                    <path d="M2 6C2 5.06812 2 4.60218 2.15224 4.23463C2.35523 3.74458 2.74458 3.35523 3.23463 3.15224C3.60218 3 4.06812 3 5 3H19C19.9319 3 20.3978 3 20.7654 3.15224C21.2554 3.35523 21.6448 3.74458 21.8478 4.23463C22 4.60218 22 5.06812 22 6C22 6.93188 22 7.39782 21.8478 7.76537C21.6448 8.25542 21.2554 8.64477 20.7654 8.84776C20.3978 9 19.9319 9 19 9H5C4.06812 9 3.60218 9 3.23463 8.84776C2.74458 8.64477 2.35523 8.25542 2.15224 7.76537C2 7.39782 2 6.93188 2 6Z" stroke="#606AF5" stroke-width="1.5"/>
                                    <path opacity="0.5" d="M5 13C5.55228 13 6 12.5523 6 12C6 11.4477 5.55228 11 5 11C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13Z" fill="#606AF5"/>
                                    <path opacity="0.5" d="M5 7C5.55228 7 6 6.55228 6 6C6 5.44772 5.55228 5 5 5C4.44772 5 4 5.44772 4 6C4 6.55228 4.44772 7 5 7Z" fill="#606AF5"/>
                                    <path opacity="0.5" d="M5 19C5.55228 19 6 18.5523 6 18C6 17.4477 5.55228 17 5 17C4.44772 17 4 17.4477 4 18C4 18.5523 4.44772 19 5 19Z" fill="#606AF5"/>
                                    </svg>
                                </div>
                            </motion.div>

                            {/* Person 5 */}
                            <motion.div
                                className="w-16 h-16 rounded-full bg-[#f1f5ff] flex items-center justify-center border-2 border-white shadow-md overflow-hidden"
                                initial={{ opacity: 0, scale: 0.8 }}
                                whileInView={{ opacity: 1, scale: 1 }}
                                transition={{ duration: 0.3, delay: 0.9 }}
                                viewport={{ once: true }}
                                title="Developer"
                            >
                                <div className="relative w-full h-full">
                                    <div className="absolute inset-0 bg-[#f1f5ff] z-0"></div>
                                    <Image
                                        src="/images/team/person-5.png"
                                        alt="Team Member"
                                        width={56}
                                        height={66}
                                        className="relative z-10 w-full h-full object-cover object-[center_2%] scale-[1.0] translate-y-1"
                                    />
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </div>
    );
};

export default CompanyIntro;
