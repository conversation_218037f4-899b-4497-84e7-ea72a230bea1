'use client';
import { <PERSON><PERSON>, <PERSON>, Input, Textarea, Typography } from "@material-tailwind/react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import ReCAPTCHA from "react-google-recaptcha";
import { BsWhatsapp } from "react-icons/bs";
import { toast } from "react-toastify";
import { FaMapMarkerAlt, FaPhone } from "react-icons/fa";
import { BiMessageDetail } from "react-icons/bi";
import { ArrowUpRight, GlobeIcon, MoveRight, MoveUpRight } from "lucide-react";

const ContactForm2 = ({ data, setData }) => {

    const t = useTranslations('shared');

    const [formData, setFormData] = useState({
        firstName: "", // Changed from fullName to firstName
        lastName: "",
        email: "",
        phone: "",
        message: "",
    });

    const [open, setOpen] = useState(false);
    const [offerStyles, setOfferStyles] = useState({});

    useEffect(() => {
        const newOfferStyles = {};
        newOfferStyles.formContainer =
            [1, 4].includes(data.id)
                ? "border-[#aeaeaf] bg-[#f5f5f5] shadow-inner"
                : [2, 5].includes(data.id)
                    ? "border-orange-500 bg-[#fef6e8] shadow-inner"
                    : [3, 6].includes(data.id)
                        ? "border-secondary bg-[#ebf1fe] shadow-inner"
                        : "border-transparent bg-transparent shadow-none";
        newOfferStyles.title =
            [1, 4].includes(data.id)
                ? "text-gray-700"
                : [2, 5].includes(data.id)
                    ? "text-orange-500"
                    : [3, 6].includes(data.id)
                        ? "text-secondary"
                        : "text-transparent";
        setOfferStyles(newOfferStyles);
        setOpen(true);
        setFormData({
            ...formData,
            message: data.initialeMessage,
        });
        console.log('data.initialeMessage:=>', data.initialeMessage);
    }, [data]);


    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [recaptchaToken, setRecaptchaToken] = useState(null);
    const [isOpen, setIsOpen] = useState(false);

    // Add a ref to the ReCAPTCHA component
    const recaptchaRef = useRef(null);


    // Handle input changes
    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        });
    };

    // Validate the form fields
    const validateForm = () => {
        const newErrors = {};

        if (!formData.firstName?.trim()) {
            newErrors.firstName = t('first_name_required') || "First name is required";
        } else if (!/^[a-zA-Z\s]+$/.test(formData.firstName)) {
            newErrors.firstName = t("first_name_letters_only") || "First name should contain only letters";
        }

        if (!formData.lastName?.trim()) {
            newErrors.lastName = t('last_name_required') || "Last name is required";
        } else if (!/^[a-zA-Z\s]+$/.test(formData.lastName)) {
            newErrors.lastName = t("last_name_letters_only") || "Last name should contain only letters";
        }

        if (!formData.email.trim()) {
            newErrors.email = t("email_required");
        } else if (
            !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
        ) {
            newErrors.email = t('invalid_email');
        }

        if (!formData.phone.trim()) {
            newErrors.phone = t("phone_number_required");
        } else if (!/^\d{9}$/.test(formData.phone)) {
            newErrors.phone = t("phone_number_ten_digits");
        }

        if (!formData.message?.trim()) {
            newErrors.message = t("message_required");
        }

        setErrors(newErrors);

        // Return true if there are no errors
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (!validateForm()) return;

        setLoading(true);
        try {
            // Check if recaptchaRef is available
            if (!recaptchaRef.current) {
                console.error("Une erreur inattendue est survenue avec le reCAPTCHA. Veuillez patienter ou actualiser la page.");
                toast.error(t('recaptcha_error'));
                return;
            }
            if (!recaptchaToken) {
                console.error("Veuillez vérifier le reCAPTCHA pour continuer, merci !");
                toast.warn(t('recaptcha_verification'));
                return;
            }

            // Create a new object without firstName and lastName
            const { firstName, lastName, ...formDataWithoutNames } = formData;

            const payload = {
                ...formDataWithoutNames,
                fullName: `${firstName} ${lastName}`.trim(),
                data: data || null,
                recaptchaToken,
            };

            const response = await fetch('/api/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            });

            if (response.ok) {
                setIsOpen(true);
                setSuccess(true);
                setTimeout(() => {
                    setIsOpen(false);
                }, 6000);
                toast.success(t('cnt_message_sent_successfully'));
                setFormData((prev) => ({
                    ...prev,
                    message: "",
                }));
            } else {
                console.error("Échec de l'envoi du message. Veuillez réessayer plus tard.");
                toast.error(t('message_send_failed'));
            }
        } catch (error) {
            console.error("Erreur lors de la soumission du formulaire. Veuillez réessayer plus tard.");
            toast.error(t('form_submission_error'));
        } finally {
            setLoading(false);
        }
    };

    const handleUnselectOffer = () => {
        setOpen(false);
        setData({
            ...data,
            id: 0,
            offerName: "@Edited: No offer selected"
        });
    };

    // Contact info cards data
    const contactCards = [
        {
            title: t('contact_cards.chat_with_us'),
            icon: <GlobeIcon className="text-blue-500 w-6 h-6" />,
            content: [
                "<EMAIL>"
            ]
        },
        {
            title: t('contact_cards.visit_us'),
            icon: <FaMapMarkerAlt className="text-blue-500 w-6 h-6" />,
            content: [
                t("address1"),
                t("address2")
            ]
        },
        {
            title: t('contact_cards.call_us'),
            icon: <FaPhone className="text-blue-500 w-6 h-6" />,
            content: [
                "+212 662 841 605",
                t('contact_cards.business_hours')
            ]
        },
        {
            title: t('contact_cards.chat_via_whatsapp'),
            icon: <BsWhatsapp className="text-green-500 w-6 h-6" />,
            content: [
                "+212 662841605",
            ],
            action: {
                url: "https://wa.me/212662841605",
            }
        }
    ];

    return (
        <section id="contact-nous" className="relative">

            {/* Contact Info Cards */}
            <div className="bg-gradient-to-b from-[#FFFFFF] via-[#A2ABFF5E] to-[#FFFFFF] px-6 py-10 ">
                <div className="w-full flex flex-col items-center justify-center text-center py-10 ">
                    <Typography variant="h1" className="font-inter" >{t('contact_title')}</Typography>
                    <Typography variant="p" className="font-inter text-gray-500" > {t('contact_subtitle')} </Typography>
                </div>
               <div>
                <div className="" ></div>
               <div className="max-w-[1400px] mx-auto mb-5">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {contactCards.map((card, index) => (
                            <div key={index} className="relative bg-white p-6 rounded-lg shadow-sm flex flex-col items-start">
                                <div className="mb-4">
                                    {card.icon}
                                </div>
                                <h3 className="text-lg font-semibold mb-2">{card.title}</h3>
                                {card.content.map((line, i) => (
                                    <p key={i} className="text-gray-600 text-sm">{line}</p>
                                ))}
                                {index === 3 && (
                                    <>
                                        <p className="mt-2 text-xs text-gray-500">
                                            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                                            {t('contact_cards.available_24_7')}
                                        </p>
                                        {card.action && (
                                            <a
                                                href={card.action.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="absolute bottom-3 right-2 flex justify-center items-center w-10 h-10 rounded-full border"
                                            >
                                                <ArrowUpRight className="w-5 h-5" />
                                            </a>
                                        )}
                                    </>
                                )}
                            </div>
                        ))}
                    </div>
                </div>
                <div className="relative max-w-[1350px]  h-[10rem] md:h-[26rem] mx-auto rounded-lg overflow-hidden mb-10" >
                    <Image
                        src="/images/contact_us.png"
                        alt="Contact Us"
                        fill
                        objectFit="cover"
                        className="mx-auto mb-8"
                    />
                </div>
               </div>
            </div>
            {/* Main Contact Form */}
            <div className="relative mt-16 md:-mt-52 p-6 md:p-0">
                {/* Contact Form */}
                <div className="max-w-[800px] mx-auto bg-white rounded-xl p-8 shadow-lg z-10 relative">
                    <div className="text-center mb-10">
                        <Typography className="text-3xl font-inter font-semibold mb-4">
                            {t('reach_us_anytime')}
                        </Typography>
                    </div>

                    {data?.id > 0 && (
                        <div className={`w-full flex md:flex-row flex-col gap-4 mb-6`}>
                            <Typography
                                variant="h5"
                                className={offerStyles.title + ` font-medium font-poppins text-lg md:text-xl`}
                            >
                                {t('request_a_quote_for')}
                            </Typography>
                            <Chip
                                color="green"
                                open={open}
                                animate={{
                                    mount: { y: 0 },
                                    unmount: { y: 50 },
                                }}
                                value={data.offerName}
                                onClose={() => handleUnselectOffer()}
                                className="w-fit mx-auto"
                            />
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="flex flex-col">
                            <Input
                                type="text"
                                name="firstName" // Changed from fullName to firstName
                                label={t('first_name')}
                                value={formData.firstName} // Changed from fullName to firstName
                                onChange={handleChange}
                                className="w-full bg-white border border-gray-300 rounded-md"
                                error={!!errors.firstName}
                            />
                            {errors.firstName && (
                                <Typography
                                    variant="small"
                                    color="red"
                                    className="mt-1 flex items-center gap-1 font-normal"
                                >
                                    {errors.firstName}
                                </Typography>
                            )}
                        </div>
                        <div className="flex flex-col">
                            <Input
                                type="text"
                                name="lastName"
                                label={t('last_name')}
                                value={formData.lastName}
                                onChange={handleChange}
                                className="w-full bg-white border border-gray-300 rounded-md"
                                error={!!errors.lastName}
                            />
                            {errors.lastName && (
                                <Typography
                                    variant="small"
                                    color="red"
                                    className="mt-1 flex items-center gap-1 font-normal"
                                >
                                    {errors.lastName}
                                </Typography>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="flex flex-col">
                            <Input
                                type="email"
                                name="email"
                                label={t('email')}
                                value={formData.email}
                                onChange={handleChange}
                                className="w-full bg-white border border-gray-300 rounded-md"
                                error={!!errors.email}
                            />
                            {errors.email && (
                                <Typography
                                    variant="small"
                                    color="red"
                                    className="mt-1 flex items-center gap-1 font-normal"
                                >
                                    {errors.email}
                                </Typography>
                            )}
                        </div>
                        <div className="flex flex-col">
                            <div className="flex">
                                <div className="flex items-center justify-center bg-gray-100 border border-gray-300 border-r-0 rounded-l-md px-3">
                                    +212
                                </div>
                                <Input
                                    type="tel"
                                    name="phone"
                                    value={formData.phone}
                                    label={t('phone')}
                                    error={!!errors.phone}
                                    onChange={handleChange}
                                    className="w-full bg-white border border-gray-300 !rounded-l-none rounded-r-md focus:rounded-l-none"
                                />
                            </div>
                            {errors.phone && (
                                <Typography
                                    variant="small"
                                    color="red"
                                    className="mt-1 flex items-center gap-1 font-normal"
                                >
                                    {errors.phone}
                                </Typography>
                            )}

                        </div>
                    </div>

                    <div className="mb-6">
                        <Textarea
                            rows={5}
                            resize={false}
                            name="message"
                            label={'message'}
                            value={formData.message}
                            onChange={handleChange}
                            className="min-h-full resize-none bg-white w-full border border-gray-300 rounded-md p-2"
                            error={!!errors.message}
                        />
                        {errors.message && (
                            <Typography
                                variant="small"
                                color="red"
                                className="mt-1 flex items-center gap-1 font-normal"
                            >
                                {errors.message}
                            </Typography>
                        )}
                    </div>

                    <div className="mb-6">
                        <ReCAPTCHA
                            ref={recaptchaRef}
                            sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
                            onChange={setRecaptchaToken}
                        />
                    </div>

                    <div className="flex items-center justify-center">
                        <button
                        name="submit"
                            onClick={handleSubmit}
                            disabled={loading}
                            loading={loading}
                            className="flex justify-center items-center gap-5 w-64 py-2 rounded-lg  relative overflow-hidden group"
                        >
                            <span className="absolute inset-0 bg-blue-700 origin-top-left rounded-lg transform scale-x-0 h-full transition-all duration-500 ease-out group-hover:scale-x-100 group-hover:w-[110%]"></span>
                            <span className="relative z-10 transition-all duration-500 group-hover:text-white group-hover:scale-110">{!loading && t("submit")}</span>
                            <MoveRight className="w-10 h-5 relative z-10 transition-all duration-500 group-hover:translate-x-2 group-hover:text-white" />
                            <span className="absolute inset-0 border-2 border-black group-hover:border-blue-700 rounded-lg transition-all duration-500"></span>
                        </button>
                        {success && isOpen && (
                            <Typography variant="small" color="green" className="mt-4">
                                {t('message_sent_successfully')}
                            </Typography>
                        )}
                    </div>
                </div>

                {/* Background Images */}
                <div className="relative mt-5">
                    <div className="absolute bottom-8 left-52 hidden md:block w-[600px] h-[400px] z-0">
                        <Image
                            src="/images/contact_bg1.png"
                            alt="Contact background"
                            fill
                            objectFit="cover"
                            className="mx-auto"
                        />
                    </div>
                    <div className="absolute bottom-8 right-52 hidden md:block w-[600px] h-[400px] z-0">
                        <Image
                            src="/images/contact_bg1.png"
                            alt="Contact background"
                            fill
                            objectFit="cover"
                            className="mx-auto"
                        />
                    </div>
                </div>
            </div>
        </section>
    );
};

export default ContactForm2;