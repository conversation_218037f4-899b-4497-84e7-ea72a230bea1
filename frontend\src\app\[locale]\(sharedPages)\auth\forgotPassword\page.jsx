"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Input, Typography } from "@material-tailwind/react";
import ReCAPTCHA from "react-google-recaptcha";
import { useTranslations } from "next-intl";
import Image from "next/image";
import authService from "@/app/services/authService";

const ForgotPasswordPage = () => {
  const t = useTranslations("auth");

  const router = useRouter();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [recaptchaValue, setRecaptchaValue] = useState(null); // State to hold reCAPTCHA value
  const [recaptchaError, setRecaptchaError] = useState(null);

  const handleEmailChange = (e) => setEmail(e.target.value);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Reset errors
    setRecaptchaError(null);
    setError(null);

    // Check if reCAPTCHA is valid
    if (!recaptchaValue) {
      setRecaptchaError(t("recaptcha_required"));
      return;
    }

    setLoading(true);
    try {
      // Pass email and recaptchaValue to the backend
      const res = await authService.forgotPassword({
        email,
        "g-recaptcha-response": recaptchaValue,
      });
      console.log("forgotPassword res:", res);
      setSuccess(true);
    } catch (err) {
      setError(err.response?.data?.message || "Something went wrong.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <section className="w-full  min-h-[90vh] flex justify-center bg-white px-4">
      <div className=" rounded-lg md:my-0 my-6 h-fit p-8 w-full max-w-md">
        {/* Success Message */}
        {success ? (
          <div className="text-green-600 text-center mb-6">
            <Image
              className="h-auto mx-auto"
              src={"/images/verification-pending2.svg"}
              width={300}
              height={300}
              alt="verification status img"
            />
            <Typography variant="body1">
              {t("check_your_inbox_email_to_reset_password")}
            </Typography>
          </div>
        ) : (
          <>
            {/* Title */}
            <Typography
              variant="h4"
              className="font-bold text-gray-800 text-center mb-6"
            >
              {t("forgot_password_title")}
            </Typography>

            {/* Error Message */}
            {error && (
              <div className="text-red-500 text-center mb-4">
                <Typography variant="small">{error}</Typography>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Input */}
              <Input
                type="email"
                id="email"
                label={t("email_label")}
                value={email}
                onChange={handleEmailChange}
                placeholder={t("email_placeholder")}
                required
                className="w-full"
              />

              {/* reCAPTCHA */}
              <div className="flex justify-center">
                <ReCAPTCHA
                  sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY}
                  onChange={setRecaptchaValue}
                />
              </div>
              {recaptchaError && (
                <Typography
                  variant="small"
                  color="red"
                  className="text-center mt-2"
                >
                  {recaptchaError}
                </Typography>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                className={`w-full py-3 text-white font-medium rounded-md transition-all ${
                  loading
                    ? "bg-blue-300 cursor-not-allowed"
                    : "bg-blue-500 hover:bg-blue-600"
                }`}
                disabled={loading}
              >
                {loading ? t("sending") : t("send_reset_link")}
              </button>
            </form>
          </>
        )}

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-white px-2 text-gray-500">{t("or")}</span>
          </div>
        </div>

        {/* Back to Login */}
        <div className="text-center">
          <button
            onClick={() => router.push("/auth/login")}
            className="text-blue-500 hover:text-blue-700 text-sm transition-all font-medium"
          >
            {t("back_to_login")}
          </button>
        </div>
      </div>
    </section>
  );
};

export default ForgotPasswordPage;
