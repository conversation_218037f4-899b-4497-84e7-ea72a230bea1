import { MinusIcon, PlusIcon } from "@heroicons/react/24/solid";
import { Typography } from "@material-tailwind/react";
import React, { useState } from "react";

const faqs = [
  {
    question: "faq_1_question",
    answer: "faq_1_answer",
  },
  {
    question: "faq_2_question",
    answer: "faq_2_answer",
  },
  {
    question: "faq_3_question",
    answer: "faq_3_answer",
  },
  {
    question: "faq_4_question",
    answer: "faq_4_answer",
  },
];

const FAQ = ({ t }) => {
  const [activeIndex, setActiveIndex] = useState(null);

  const toggleFAQ = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <section className="p-8 pb-10 bg-gray-50 text-center">
      <h2 className="text-3xl font-medium font-inter mb-6">{t("faq_title")}</h2>
      <div className="max-w-5xl mx-auto gap-y-4 flex flex-col">
        {faqs.map((faq, index) => (
          <div
            key={index}
            className="bg-white rounded-md shadow-md px-4 py-6 cursor-pointer"
            onClick={() => toggleFAQ(index)}
          >
            <div className="flex justify-between items-center">
              <Typography
                variant="h2"
                className="text-left text-xl font-medium font-inter text-[#05144B]"
              >
                {t(faq.question)}
              </Typography>
              <span className="text-secondary">
                {activeIndex === index ? (
                  <MinusIcon className="w-5 h-5" />
                ) : (
                  <PlusIcon className="w-5 h-5 text-black" />
                )}
              </span>
            </div>
            {activeIndex === index && (
              <p className="mt-4 text-gray-800 text-left font-roboto text-base whitespace-pre-line">
                {t(faq.answer)}
              </p>
            )}
          </div>
        ))}
      </div>
    </section>
  );
};

export default FAQ;
