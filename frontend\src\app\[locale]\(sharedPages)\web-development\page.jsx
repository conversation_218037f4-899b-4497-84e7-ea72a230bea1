"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, CardBody, But<PERSON> } from "@material-tailwind/react";
import <PERSON><PERSON> from "lottie-react";
import WebDevAnimation from "src/assets/web-developmen.json";
import {
  CheckCircleIcon,
  RocketIcon,
  Tags,
  ShieldCheck,
} from "lucide-react";
import PricingPlanGridWeb from "@/components/hosting/pricingPlanGridWeb";
import { useTranslations } from "next-intl";
import packageService from "@/app/services/packageService";
import { getFormattedPlans} from "@/app/helpers/helpers";
import CountdownTimer from "@/components/shared/countdown";
import DynamicMetadataClient from "@/components/DynamicMetadataClient";

function WebDevelopment() {
  const t = useTranslations("web_development");
  const [billingPeriod, setBillingPeriod] = useState("monthly");
  const [dedicatedHostingPacks, setDedicatedHostingPacks] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getPackagesData = async () => {
      try {
        const response = await packageService.getPackages("web creation");
        setDedicatedHostingPacks(response.data);
      } catch (error) {
        console.error("error fetching promotions: ", error);
      } finally {
        setLoading(false);
      }
    };

    getPackagesData();
  }, []);

  const dedicatedPlans = getFormattedPlans(dedicatedHostingPacks, billingPeriod);

  const targetDate = new Date("2025-04-15T00:00:00").getTime();

  return (
    <div className="min-h-screen bg-gray-50 pt-2 pb-20">
      <DynamicMetadataClient
        title={`ZtechEngineering | ${t('title')}`}
        desc={`${t('desc')}`}
      />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <section>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 items-center">
            <div className="text-center lg:text-left">
              <Typography
                variant="small"
                className="inline-flex font-inter items-center px-3 py-1 rounded-full bg-indigo-100 text-primary font-medium"
              >
                <Tags className="w-4 h-4 mr-1" />
                {t('discount_label')}
              </Typography>
              <Typography
                variant="h1"
                color="blue-gray"
                className="text-4xl lg:text-5xl font-inter font-bold mb-6 tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500"
              >
                {t('hero_title')}
              </Typography>
              <Typography
                variant="lead"
                className="text-lg mb-8 text-gray-700"
              >
                {t('hero_description')}
              </Typography>
              <div className="md:flex grid grid-cols-1 gap-4 items-center">
                <Button
                  className="bg-[#606AF5] text-sm md:text-lg rounded-md flex gap-2 justify-center items-center w-full sm:w-auto hover:shadow-lg transition-all duration-300"
                  onClick={() =>
                    document
                      .getElementById("pricing")
                      ?.scrollIntoView({ behavior: "smooth" })
                  }
                >
                  <RocketIcon className="w-6 h-6 mr-2" />
                  {t('claim_deal')}
                </Button>
                <CountdownTimer targetDate={targetDate} />
              </div>
              <div>
                <Typography
                  variant="small"
                  className="flex items-center gap-1 pt-3 text-sm md:text-base lg:text-lg font-light text-gray-600"
                >
                  <ShieldCheck className="w-5 h-5 text-gray-600" />
                  {t('money_back_guarantee')}
                </Typography>
              </div>
            </div>
            <div className="flex md:mt-8 justify-center lg:justify-end">
              <Lottie
                animationData={WebDevAnimation}
                loop={true}
                className="w-full max-w-sm lg:max-w-md"
              />
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="mt-5 mb-20">
          <PricingPlanGridWeb
            loading={loading}
            plans={dedicatedPlans}
            billingPeriod={billingPeriod}
            t={t}
          />
        </section>

        {/* Features Section */}
        <section className="py-16 bg-gray-50">
          <div className="text-center mb-12">
            <Typography
              variant="h2"
              color="blue-gray"
              className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4"
            >
              {t('features_title')}
            </Typography>
            <Typography
              variant="lead"
              color="gray"
              className="text-lg md:text-xl font-light text-gray-600 max-w-2xl mx-auto"
            >
              {t('features_description')}
            </Typography>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 px-4 sm:px-6 lg:px-8">
            {[
              {
                title: t("features.0.title"),
                description: t("features.0.description"),
                icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
              },
              {
                title: t("features.1.title"),
                description: t("features.1.description"),
                icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
              },
              {
                title: t("features.2.title"),
                description: t("features.2.description"),
                icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
              },
              {
                title: t("features.3.title"),
                description: t("features.3.description"),
                icon: <CheckCircleIcon className="h-8 w-8 text-green-700" />,
              }
            ].map((feature, index) => (
              <Card
                key={index}
                className="bg-white hover:shadow-2xl transition-shadow duration-300 rounded-2xl border border-gray-100 p-6 flex flex-col items-center text-center"
              >
                <div className="mb-6">{feature.icon}</div>
                <CardBody className="p-0">
                  <Typography
                    variant="h6"
                    color="blue-gray"
                    className="text-lg md:text-xl font-semibold mb-3"
                  >
                    {feature.title}
                  </Typography>
                  <Typography
                    color="gray"
                    className="text-xs md:text-base font-normal text-gray-600"
                  >
                    {feature.description}
                  </Typography>
                </CardBody>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}

export default WebDevelopment;