"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  Typography,
  Input,
  Select,
  Option,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON>,
  DialogFooter,
} from "@material-tailwind/react";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { ChevronDownIcon, EyeIcon } from "@heroicons/react/24/solid";
import { adminService } from "../../services/adminService";
import { OrderStatus } from "../../config/ConstStatus";
import "react-toastify/dist/ReactToastify.css";
import Link from "next/link";
import { PaymentMethodConfig } from "@/app/config/PaymentMethodConfig";
import { toast } from "react-toastify";
import useDebounce from "@/app/hook/useDebounce";

const PAYMENT_METHOD_OPTIONS = [
  "ALL",
  PaymentMethodConfig.Cmi,
  PaymentMethodConfig.Cod,
  PaymentMethodConfig.PAYZONE,
  PaymentMethodConfig.Virement,
];
const ITEMS_PER_PAGE_OPTIONS = [5, 10, 25, 50];

const STATUS_STYLES = {
  [OrderStatus.PENDING]: {
    color: "amber",
    bg: "bg-amber-100",
    text: "text-amber-800",
  },
  [OrderStatus.PROCESSING]: {
    color: "blue",
    bg: "bg-blue-100",
    text: "text-blue-800",
  },
  [OrderStatus.COMPLETED]: {
    color: "green",
    bg: "bg-green-100",
    text: "text-green-800",
  },
  [OrderStatus.SHIPPED]: {
    color: "purple",
    bg: "bg-purple-100",
    text: "text-purple-800",
  },
  [OrderStatus.CANCELLED]: {
    color: "red",
    bg: "bg-red-100",
    text: "text-red-800",
  },
  [OrderStatus.FAILED]: {
    color: "gray",
    bg: "bg-gray-100",
    text: "text-gray-800",
  },
  [OrderStatus.REFUNDED]: {
    color: "teal",
    bg: "bg-teal-100",
    text: "text-teal-800",
  },
  [OrderStatus.PROCESSINGREFUND]: {
    color: "orange",
    bg: "bg-orange-100",
    text: "text-orange-800",
  },
  [OrderStatus.ACTIVE]: {
    color: "emerald",
    bg: "bg-emerald-100",
    text: "text-emerald-800",
  },
  [OrderStatus.EXPIRED]: {
    color: "red",
    bg: "bg-red-100",
    text: "text-red-800",
  },
  unknown: { color: "gray", bg: "bg-gray-200", text: "text-gray-700" },
};

const STATUS_OPTIONS = ["ALL", ...Object.values(OrderStatus)];

const scrollbarStyles = `
  .modern-scrollbar::-webkit-scrollbar {
    width: 4px;
  }
  .modern-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  .modern-scrollbar::-webkit-scrollbar-thumb {
    background: #9ca3af;
    border-radius: 9999px;
  }
  .modern-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
  .modern-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #9ca3af transparent;
  }
`;

export default function OrdersManagement() {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchQuery = useDebounce(searchTerm, 300);
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [paymentMethodFilter, setPaymentMethodFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [openModal, setOpenModal] = useState(false);
  const [pendingUpdate, setPendingUpdate] = useState(null);

  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: debouncedSearchQuery,
        status: statusFilter,
        paymentMethod: paymentMethodFilter,
      };
      const response = await adminService.getAllOrders(params);
      const normalizedOrders = response.data.data.map((order) => ({
        ...order,
        status:
          order.status && Object.values(OrderStatus).includes(order.status)
            ? order.status
            : "unknown",
      }));

      setOrders(normalizedOrders || []);
      setTotalItems(response.data.pagination?.totalItems || 0);
      setTotalPages(response.data.pagination?.totalPages || 0);
    } catch (err) {
      toast.error(err.response?.data?.message || "Failed to fetch orders", {
        position: "top-right",
        autoClose: 3000,
      });
      setOrders([]);
      setTotalItems(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    itemsPerPage,
    debouncedSearchQuery,
    statusFilter,
    paymentMethodFilter,
  ]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const handleUpdateStatus = useCallback((orderId, newStatus) => {
    setPendingUpdate({ orderId, newStatus });
    setOpenModal(true);
  }, []);

  const confirmUpdateStatus = useCallback(async () => {
    if (!pendingUpdate) return;

    try {
      setLoading(true);
      await adminService.updateOrderStatus(pendingUpdate.orderId, {
        status: pendingUpdate.newStatus,
      });
      await fetchOrders();
      toast.success(`Order status updated to ${pendingUpdate.newStatus}`, {
        position: "top-right",
        autoClose: 3000,
      });
    } catch (err) {
      toast.error(
        err.response?.data?.message || "Failed to update order status",
        {
          position: "top-right",
          autoClose: 3000,
        }
      );
    } finally {
      setLoading(false);
      setOpenModal(false);
      setPendingUpdate(null);
    }
  }, [pendingUpdate, fetchOrders]);

  const getStatusStyle = useCallback(
    (status) => STATUS_STYLES[status] || STATUS_STYLES.unknown,
    []
  );

  const handleSearchChange = useCallback((e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on filter change
  }, []);

  const handleStatusFilterChange = useCallback((value) => {
    setStatusFilter(value);
    setCurrentPage(1); // Reset to first page on filter change
  }, []);

  const handlePaymentMethodFilterChange = useCallback((value) => {
    setPaymentMethodFilter(value);
    setCurrentPage(1); // Reset to first page on filter change
  }, []);

  const handleItemsPerPageChange = useCallback((value) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1);
  }, []);

  const handlePrevPage = useCallback(
    () => setCurrentPage((prev) => Math.max(prev - 1, 1)),
    []
  );
  const handleNextPage = useCallback(
    () => setCurrentPage((prev) => Math.min(prev + 1, totalPages)),
    [totalPages]
  );

  return (
    <>
      <style>{scrollbarStyles}</style>
      <div className="space-y-6 p-6 h-[770px]">
        <Typography variant="h2" color="blue-gray" className="mb-4">
          Orders Management
        </Typography>

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="w-full md:w-1/3">
            <Input
              label="Search by Order ID or User"
              icon={<MagnifyingGlassIcon className="h-5 w-5" />}
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <div className="w-full md:w-1/3">
            <Select
              label="Filter by Status"
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              {STATUS_OPTIONS.map((status) => (
                <Option key={status} value={status}>
                  {status}
                </Option>
              ))}
            </Select>
          </div>
          <div className="w-full md:w-1/3">
            <Select
              label="Filter by Payment Method"
              value={paymentMethodFilter}
              onChange={handlePaymentMethodFilterChange}
            >
              {PAYMENT_METHOD_OPTIONS.map((method) => (
                <Option key={method} value={method}>
                  {method}
                </Option>
              ))}
            </Select>
          </div>
        </div>

        {/* Table */}
        <Card className="w-full">
          <table className="w-full table-auto text-left">
            <thead>
              <tr>
                {[
                  "Order ID",
                  "Customer",
                  "Amount",
                  "Status",
                  "Payment",
                  "Date",
                  "Actions",
                ].map((head) => (
                  <th
                    key={head}
                    className="border-b border-blue-gray-100 bg-blue-gray-50 p-4"
                  >
                    <Typography
                      variant="small"
                      color="blue-gray"
                      className="font-normal leading-none opacity-70"
                    >
                      {head}
                    </Typography>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {orders.length > 0 ? (
                orders.map(
                  (
                    {
                      identifiant,
                      _id,
                      user,
                      totalPrice,
                      status,
                      paymentMethod,
                      currency,
                      createdAt,
                    },
                    index
                  ) => (
                    <tr
                      key={_id}
                      className="even:bg-blue-gray-50/50 hover:bg-blue-gray-100 transition-colors"
                    >
                      <td className="p-4">
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="font-normal"
                        >
                          #{identifiant?.toUpperCase()}
                        </Typography>
                      </td>
                      <td className="p-4">
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="font-normal"
                        >
                          {`${user?.firstName} ${user?.lastName}`}
                        </Typography>
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="font-normal opacity-70"
                        >
                          {user?.email}
                        </Typography>
                      </td>
                      <td className="p-4">
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="font-normal"
                        >
                          {totalPrice.toFixed(2)} <span>{currency}</span>
                        </Typography>
                      </td>
                      <td className="p-4">
                        <Menu placement="bottom-start">
                          <MenuHandler>
                            <div className="flex items-center gap-1 cursor-pointer hover:opacity-80 transition-opacity">
                              <Chip
                                value={status}
                                className={`${getStatusStyle(status).bg} ${
                                  getStatusStyle(status).text
                                } font-medium rounded-full px-3 py-1 text-xs`}
                              />
                              <ChevronDownIcon className="h-4 w-4 text-gray-600" />
                            </div>
                          </MenuHandler>
                          <MenuList className="p-1 max-h-64 modern-scrollbar">
                            {STATUS_OPTIONS.slice(1).map((statusOption) => (
                              <MenuItem
                                key={statusOption}
                                onClick={() =>
                                  handleUpdateStatus(_id, statusOption)
                                }
                                className={`flex items-center gap-2 ${
                                  status === statusOption ? "bg-gray-100" : ""
                                }`}
                              >
                                <span
                                  className={`h-2.5 w-2.5 rounded-full ${STATUS_STYLES[
                                    statusOption
                                  ].bg.replace("100", "500")}`}
                                />
                                <Typography
                                  variant="small"
                                  className="font-normal"
                                >
                                  {statusOption}
                                </Typography>
                              </MenuItem>
                            ))}
                          </MenuList>
                        </Menu>
                      </td>
                      <td className="p-4">
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="font-normal"
                        >
                          {paymentMethod}
                        </Typography>
                      </td>
                      <td className="p-4">
                        <Typography
                          variant="small"
                          color="blue-gray"
                          className="font-normal"
                        >
                          {new Date(createdAt).toLocaleDateString("fr-FR")}
                        </Typography>
                      </td>
                      <td className="p-4">
                        <Button
                          variant="text"
                          color="blue"
                          size="sm"
                          className="p-2"
                        >
                          <Link href={`/admin/orders/${_id}`}>
                            <EyeIcon className="h-5 w-5" />
                          </Link>
                        </Button>
                      </td>
                    </tr>
                  )
                )
              ) : (
                <tr>
                  <td className="p-2" colSpan="7">
                    NO ORDERS FOUND
                  </td>
                </tr>
              )}
            </tbody>
          </table>

          {/* Pagination */}
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <Select
                label="Items per page"
                value={itemsPerPage.toString()}
                onChange={handleItemsPerPageChange}
              >
                {ITEMS_PER_PAGE_OPTIONS.map((option) => (
                  <Option key={option} value={option.toString()}>
                    {option}
                  </Option>
                ))}
              </Select>
              <Typography variant="small" color="blue-gray">
                Page {currentPage} of {totalPages}
              </Typography>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outlined"
                size="sm"
                onClick={handlePrevPage}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outlined"
                size="sm"
                onClick={handleNextPage}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        </Card>

        {/* Confirmation Modal */}
        <Dialog open={openModal} handler={() => setOpenModal(false)}>
          <DialogHeader>Confirm Status Update</DialogHeader>
          <DialogBody>
            Are you sure you want to update the order status to{" "}
            <strong>{pendingUpdate?.newStatus}</strong>?
          </DialogBody>
          <DialogFooter>
            <Button
              variant="text"
              color="gray"
              onClick={() => {
                setOpenModal(false);
                setPendingUpdate(null);
              }}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button
              variant="gradient"
              color="blue"
              onClick={confirmUpdateStatus}
            >
              Confirm
            </Button>
          </DialogFooter>
        </Dialog>
      </div>
    </>
  );
}
