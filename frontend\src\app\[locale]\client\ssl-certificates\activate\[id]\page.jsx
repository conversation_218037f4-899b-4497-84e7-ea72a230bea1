"use client";
import { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useTranslations, useLocale } from "next-intl";
import { countries } from "countries-list";
import {
  Typography,
  Card,
  CardBody,
  Button,
  Stepper,
  Step,
  Textarea,
  Input,
  Select,
  Option,
  Alert,
} from "@material-tailwind/react";
import {
  ShieldCheck,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
  Mail,
  Lock,
  Shield,
  Globe,
  Copy
} from "lucide-react";
import { toast } from "react-toastify";
import orderService from "@/app/services/orderService";
import sslService from "@/app/services/sslService";
import { getLocalizedContent } from "@/app/helpers/helpers";

// Add this function to extract domain from CSR
const extractDomainFromCSR = async (csrText) => {
  try {
    const response = await sslService.verifyCSR({
      csr: csrText,
      domain: '' // We don't need to verify here, just extract
    });

    return response.data.csrDomain;
  } catch (error) {
    console.error("Error extracting domain from CSR:", error);
    return null;
  }
};

// Add this helper function to check if a domain exists
const checkDomainExists = async (domain) => {
  try {
    // Simple approach: Try to fetch the domain's favicon
    // This is a lightweight way to check if the domain exists
    const response = await fetch(`https://${domain}/favicon.ico`, {
      method: 'HEAD',
      mode: 'no-cors', // This prevents CORS issues
      cache: 'no-store',
    });

    return true; // If we get here, the domain likely exists
  } catch (error) {
    try {
      // Fallback: Try without HTTPS
      const httpResponse = await fetch(`http://${domain}/favicon.ico`, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-store',
      });

      return true;
    } catch (httpError) {
      console.error("Error checking domain existence:", httpError);
      return false;
    }
  }
};

export default function SSLActivationPage() {
  const { id } = useParams();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("client.ssl");

  const [activeStep, setActiveStep] = useState(0);
  const [isLastStep, setIsLastStep] = useState(false);
  const [isFirstStep, setIsFirstStep] = useState(true);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [sslOrder, setSslOrder] = useState(null);

  // Form state
  const [csr, setCSR] = useState("");
  const [domain, setDomain] = useState("");
  const [validationEmails, setValidationEmails] = useState([]);
  const [selectedEmail, setSelectedEmail] = useState("");
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(null);

  // Tab state
  const [activeTab, setActiveTab] = useState(0);

  // CSR generation form state
  const [csrFormData, setCsrFormData] = useState({
    country: "",
    state: "",
    city: "",
    organization: "",
    organizationalUnit: "",
    email: "",
    keySize: "2048" // Convert to string to avoid prop type warning
  });

  // Country search state
  const [countrySearch, setCountrySearch] = useState("");
  const [generatedPrivateKey, setGeneratedPrivateKey] = useState("");
  const [generateLoading, setGenerateLoading] = useState(false);

  const [installService, setInstallService] = useState(true);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        const response = await orderService.getSubOrderById(id);
        setSslOrder(response.data);
      } catch (error) {
        console.error("Error fetching SSL order:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [id]);

  useEffect(() => {
    if (domain) {
      setSelectedEmail(""); // Clear email when domain changes
    }
  }, [domain]);

  const handleNext = async () => {
    // Show loading state when validating
    setSubmitting(true);
    if (activeStep === 0) {
      // Validate CSR and domain
      const newErrors = {};

      if (!domain.trim()) {
        newErrors.domain = t("csr.errors.domain_required");
      } else if (!/^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/.test(domain)) {
        newErrors.domain = t("csr.errors.invalid_domain");
      } else if (domainExists === false) {
        // Show error and prevent proceeding instead of just a warning
        newErrors.domain = t("csr.errors.domain_may_not_exist");
        // toast.error(t("csr.errors.domain_may_not_exist"));
        // Don't continue the process - block user
      }

      if (!csr.trim()) {
        newErrors.csr = t("csr.errors.required");
      } else if (!csr.includes("-----BEGIN CERTIFICATE REQUEST-----") ||
      !csr.includes("-----END CERTIFICATE REQUEST-----")) {
        newErrors.csr = t("csr.errors.invalid_format");
      } else {
        try {
          // Use server-side verification
          const response = await sslService.verifyCSR({
            csr,
            domain: domain.trim()
          });

          if (!response.data.match) {
            newErrors.csr = t("csr.errors.domain_mismatch", {
              csrDomain: response.data.csrDomain,
              inputDomain: domain
            });
          }
        } catch (error) {
          if (error.response?.data?.message) {
            newErrors.csr = error.response.data.message;
          } else {
            newErrors.csr = t("csr.errors.verification_failed");
          }
        }
      }

      if (Object.keys(newErrors).length > 0) {
        toast.error(Object.values(newErrors)[0]);
        setErrors(newErrors); // Replace all errors (don't merge)
        setSubmitting(false); // Reset loading state on error
        return;
      }

      // Clear all relevant errors
      setErrors({});
      toast.success(t("notifications.step_1_success"));
    }

    if (activeStep === 1) {
      // Modified: Improved validation to check for exact domain match
      const newErrors = {};

      if (!selectedEmail) {
        newErrors.email = t("validation.errors.email_required");
      } else if (!selectedEmail.includes('@')) {
        newErrors.email = t("validation.errors.invalid_email");
      } else {
        // Extract the domain part of the email and ensure exact match
        const emailParts = selectedEmail.split('@');
        if (emailParts.length !== 2 || emailParts[1] !== domain) {
          newErrors.email = t("validation.errors.email_domain_mismatch");
        }
      }

      if (Object.keys(newErrors).length > 0) {
        toast.error(Object.values(newErrors)[0]);
        setErrors(newErrors);
        setSubmitting(false); // Reset loading state on error
        return;
      }
      toast.success(t("notifications.step_2_success"));
    }

    if (activeStep === 2) {
      handleSubmit();
      return;
    }

    setErrors({});
    setActiveStep((cur) => cur + 1);

    // Reset loading state after moving to the next step
    // (unless we're submitting the form in the last step)
    if (activeStep !== 2) {
      setSubmitting(false);
    }
  };

  const handlePrev = () => {
    setActiveStep((cur) => cur - 1);
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    try {
      const searchParams = new URLSearchParams(window.location.search);
      const index = searchParams.get('index');

      // Include installService flag in the request
      await sslService.submitCSR(id, {
        csr,
        domain,
        validationEmail: selectedEmail,
        index: parseInt(index) || 0,
        installService: installService // This is correctly sending the flag
      });

      toast.success(t("csr.activation_success"));
      setSuccess(t("csr.activation_success"));

      // Keep the submitting state true until redirect
      // The button will remain disabled and show the loader
      setTimeout(() => {
        router.push(`/${locale}/client/ssl-certificates`);
        // We don't need to set submitting to false here since we're redirecting
      }, 3000);
    } catch (error) {
      console.error("Error activating SSL certificate:", error);
      toast.error(error.response?.data?.message || t("review.error_message"));
      setErrors({ submit: t("review.error_message") });
      // Only set submitting to false if there's an error
      setSubmitting(false);
    }
  };

  // Handle CSR form input changes with error clearing
  const handleCsrFormChange = (field, value) => {
    // Clear the error for this field when it gets a value
    if (value && errors[field]) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[field];
        return newErrors;
      });
    }

    setCsrFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // For domain field changes with existence check
  const [domainExists, setDomainExists] = useState(null);
  const [checkingDomain, setCheckingDomain] = useState(false);
  const domainCheckRef = useRef(null);

  const handleDomainChange = (e) => {
    const value = e.target.value;
    setDomain(value);

    // Reset domain existence state
    setDomainExists(null);

    // Clear domain error if present and value is not empty
    if (value && errors.domain) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors.domain;
        return newErrors;
      });
    }

    // Clear previous timeout
    if (domainCheckRef.current) {
      clearTimeout(domainCheckRef.current);
    }

    // Only check if domain is valid
    if (value && /^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/.test(value)) {
      setCheckingDomain(true);

      // Set a timeout to avoid too many checks while typing
      domainCheckRef.current = setTimeout(async () => {
        const exists = await checkDomainExists(value);
        setDomainExists(exists);
        setCheckingDomain(false);
      }, 800); // Check after 800ms of typing pause
    } else {
      setCheckingDomain(false);
    }
  };

  // For CSR textarea changes
  const handleCsrChange = (e) => {
    const value = e.target.value;
    setCSR(value);

    // Clear CSR error if present and value is not empty
    if (value && errors.csr) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors.csr;
        return newErrors;
      });
    }
  };

  // Generate CSR
  const handleGenerateCSR = async () => {
    // Initialize new errors object
    const newErrors = {};

    // Validate domain
    if (!domain.trim()) {
      newErrors.domain = t("csr.errors.domain_required");
    }

    // Validate country
    if (!csrFormData.country) {
      newErrors.country = true;
    }

    // If there are validation errors, show them and return
    if (Object.keys(newErrors).length > 0) {
      // Show error message
      toast.error(t("csr.errors.required_fields"));

      // Update errors state (preserving other errors)
      setErrors(prev => ({ ...prev, ...newErrors }));
      return;
    }

    // Clear all relevant errors before proceeding
    setErrors(prev => {
      const updatedErrors = {...prev};
      delete updatedErrors.domain;
      delete updatedErrors.country;
      return updatedErrors;
    });

    setGenerateLoading(true);
    try {
      const response = await sslService.generateCSR({
        domain,
        ...csrFormData
      });

      setCSR(response.data.data.csr);
      setGeneratedPrivateKey(response.data.data.privateKey);
      toast.success(t("csr.generated_success"));
    } catch (error) {
      console.error("Error generating CSR:", error);
      toast.error(error.response?.data?.message || t("csr.generation_failed"));
    } finally {
      setGenerateLoading(false);
    }
  };

  // Copy text to clipboard
  const copyToClipboard = (text, message) => {
    navigator.clipboard.writeText(text);
    toast.success(message || t("notifications.copy_success"));
  };

  // Modified tab change handler
  const handleTabChange = (tabIndex) => {
    // Clear any CSR-related errors when switching tabs if CSR is filled
    if (csr && errors.csr) {
      setErrors(prev => {
        const newErrors = {...prev};
        delete newErrors.csr;
        return newErrors;
      });
    }

    setActiveTab(tabIndex);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4" />
          <Typography className="text-gray-600">{t("common.processing")}</Typography>
        </div>
      </div>
    );
  }

  if (!sslOrder) {
    return (
      <div className="p-8 bg-gray-50 min-h-screen flex flex-col items-center justify-center">
        <Card className="w-full max-w-lg mx-auto shadow-md">
          <CardBody className="p-6">
            <div className="text-center mb-6">
              <div className="bg-red-50 p-4 inline-block rounded-full mb-4">
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
              <Typography variant="h5" className="mb-2">
                {t("errors.order_not_found")}
              </Typography>
              <Typography className="text-gray-600">
                We couldn&apos;t find the SSL certificate you&apos;re looking for.
              </Typography>
            </div>
            <Button
              color="blue"
              fullWidth
              className="flex items-center justify-center gap-2"
              onClick={() => router.push(`/${locale}/client/ssl-certificates`)}
            >
              <ArrowLeft className="h-4 w-4" />
              {t("back_to_certificates")}
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-8 bg-gray-50 min-h-screen">
      {/* Header Section */}
      <div className="max-w-5xl mx-auto mb-8">
        <Button
          variant="text"
          color="blue"
          size="sm"
          className="flex items-center gap-2 mb-6 hover:bg-blue-50 transition-colors"
          onClick={() => router.push(`/${locale}/client/ssl-certificates`)}
        >
          <ArrowLeft className="h-4 w-4" />
          {t("back_to_certificates")}
        </Button>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            <div className="bg-blue-50 p-3 rounded-full">
              <Shield className="h-8 w-8 text-blue-500" />
            </div>
            <div className="flex-1">
              <Typography variant="h3" className="text-xl font-semibold text-gray-800">
                {t("ssl_activation")}
              </Typography>
              <Typography className="text-gray-600 mt-1">
                {getLocalizedContent(sslOrder.package, "name", locale)}
              </Typography>
            </div>
          </div>
        </div>

        {/* Success message */}
        {success && (
          <Alert
            color="green"
            icon={<CheckCircle className="h-5 w-5" />}
            className="mt-6 border border-green-200"
            open={!!success}
          >
            <div className="flex items-center gap-2">
              <Typography className="font-medium">{success}</Typography>
            </div>
          </Alert>
        )}

        {/* Error message */}
        {errors.submit && (
          <Alert
            color="red"
            icon={<AlertCircle className="h-5 w-5" />}
            className="mt-6 border border-red-200"
            open={!!errors.submit}
          >
            <div className="flex items-center gap-2">
              <Typography className="font-medium">{errors.submit}</Typography>
            </div>
          </Alert>
        )}

        {/* Stepper */}
        <div className="w-full bg-white rounded-xl p-9 mt-6 shadow-sm border border-gray-100">
          <Stepper
            activeStep={activeStep}
            isLastStep={(value) => setIsLastStep(value)}
            isFirstStep={(value) => setIsFirstStep(value)}
            lineClassName="bg-gray-200 hover:bg-gray-200"
            activeLineClassName="bg-blue-500"
          >
            <Step
              className={activeStep === 0 ? "bg-blue-500 text-white" : ""}
              activeClassName="bg-blue-500 text-white"
              completedClassName="bg-blue-500 text-white"
            >
              <ShieldCheck className="h-5 w-5" />
              <div className="absolute -bottom-[1.9rem] w-max text-center ml-6">
                <Typography
                  variant="h6"
                  color={activeStep === 0 ? "blue" : "blue-gray"}
                  className="text-sm font-medium"
                >
                  {t("csr.steps.csr_configuration")}
                </Typography>
              </div>
            </Step>
            <Step
              className={activeStep === 1 ? "bg-blue-500 text-white" : ""}
              activeClassName="bg-blue-500 text-white"
              completedClassName="bg-blue-500 text-white"
            >
              <Mail className="h-5 w-5" />
              <div className="absolute -bottom-[1.9rem] w-max text-center ">
                <Typography
                  variant="h6"
                  color={activeStep === 1 ? "blue" : "blue-gray"}
                  className="text-sm font-medium"
                >
                  {t("csr.steps.domain_validation")}
                </Typography>
              </div>
            </Step>
            <Step
              className={activeStep === 2 ? "bg-blue-500 text-white" : ""}
              activeClassName="bg-blue-500 text-white"
              completedClassName="bg-blue-500 text-white"
            >
              <CheckCircle className="h-5 w-5" />
              <div className="absolute -bottom-[1.9rem] w-max text-center mr-10">
                <Typography
                  variant="h6"
                  color={activeStep === 2 ? "blue" : "blue-gray"}
                  className="text-sm font-medium"
                >
                  {t("csr.steps.review_confirm")}
                </Typography>
              </div>
            </Step>
          </Stepper>
        </div>

        {/* Main Content Card */}
        <Card className="mt-16 shadow-md border border-gray-100">
          <CardBody className="p-6 md:p-8">
            {/* Step 1: CSR Configuration */}
            {activeStep === 0 && (
              <div className="space-y-6">
                <div className="pb-6 border-b border-gray-100">
                  <Typography variant="h4" className="text-xl font-semibold text-gray-800 mb-2">
                    {t("csr.steps.csr_configuration")}
                  </Typography>
                  <Typography className="text-gray-600">
                    {t("csr.info_message")}
                  </Typography>
                </div>

                <div className="space-y-4">
                  {/* Domain field - outside the tabs */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-blue-500" />
                      <Typography className="text-sm font-medium text-gray-700">
                        {t("csr.domain_name")}
                      </Typography>
                    </div>
                    {/* Domain input with existence indicator */}
                    <div className="relative">
                      <Input
                        type="text"
                        size="lg"
                        placeholder="example.com"
                        value={domain}
                        onChange={handleDomainChange}
                        error={!!errors.domain}
                        className="!border-gray-300 focus:!border-blue-500"
                        labelProps={{
                          className: "hidden",
                        }}
                      />

                      {/* Domain existence indicator */}
                      {domain && !errors.domain && (
                        <div className="absolute right-3 top-1/2 -translate-y-1/2">
                          {checkingDomain ? (
                            <div className="h-5 w-5 rounded-full border-2 border-blue-500 border-t-transparent animate-spin"></div>
                          ) : domainExists === true ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : domainExists === false ? (
                            <AlertCircle className="h-5 w-5 text-orange-500" />
                          ) : null}
                        </div>
                      )}
                    </div>

                    {errors.domain && (
                      <Typography color="red" className="text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.domain}
                      </Typography>
                    )}

                    {/* Show warning if domain doesn't exist */}
                    {domainExists === false && !errors.domain && (
                      <Typography color="orange" className="text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {t("csr.errors.domain_may_not_exist")}
                      </Typography>
                    )}
                  </div>

                  {/* Simplified Tab Interface */}
                  <div className="mt-6">
                    <div className="flex border-b border-gray-200">
                      <button
                        className={`py-3 px-4 text-sm font-medium relative ${
                          activeTab === 0
                            ? "text-blue-500 border-b-2 border-blue-500"
                            : "text-gray-500 hover:text-gray-700"
                        }`}
                        onClick={() => handleTabChange(0)}
                      >
                        <div className="flex items-center gap-2">
                          <ShieldCheck className="h-4 w-4" />
                          {t("csr.paste_csr_tab")}
                        </div>
                      </button>

                      {/* Light separator line between tabs */}
                      <div className="h-6 self-center border-r border-gray-200"></div>

                      <button
                        className={`py-3 px-4 text-sm font-medium ${
                          activeTab === 1
                            ? "text-blue-500 border-b-2 border-blue-500"
                            : "text-gray-500 hover:text-gray-700"
                        }`}
                        onClick={() => handleTabChange(1)}
                      >
                        <div className="flex items-center gap-2">
                          <Lock className="h-4 w-4" />
                          {t("csr.generate_csr_tab")}
                        </div>
                      </button>
                    </div>

                    {/* Tab Content */}
                    <div className="mt-4">
                      {/* Tab 1: Paste CSR (existing functionality) */}
                      {activeTab === 0 && (
                        <div className="space-y-2">
                          <Typography className="text-sm font-medium text-gray-700">
                            {t("csr.paste_csr")}
                          </Typography>
                          {/* CSR textarea */}
                          <Textarea
                            placeholder="-----BEGIN CERTIFICATE REQUEST-----..."
                            value={csr}
                            onChange={handleCsrChange}
                            rows={8}
                            error={!!errors.csr}
                            className="!border-gray-300 focus:!border-blue-500 font-mono text-sm"
                          />
                          {errors.csr && (
                            <Typography color="red" className="text-xs mt-1 flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {errors.csr}
                            </Typography>
                          )}
                        </div>
                      )}

                      {/* Tab 2: Generate CSR (modified functionality) */}
                      {activeTab === 1 && (
                        <div className="space-y-4">
                          {/* CSR Form */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Country */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.country")} *
                              </Typography>
                              <Select
                                size="lg"
                                value={csrFormData.country}
                                onChange={(val) => handleCsrFormChange("country", val)}
                                className={`!border-${errors.country ? "red" : "gray"}-300 focus:!border-blue-500`}
                                placeholder={t("csr.choose_country") || "Choose a country"}
                                required
                                containerProps={{
                                  className: "min-w-full",
                                }}
                                menuProps={{
                                  className: "max-h-[400px] overflow-y-auto p-1 border border-blue-100 shadow-lg rounded-lg",
                                }}
                                labelProps={{
                                  className: "hidden",
                                }}
                                arrow={
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    strokeWidth={2}
                                    stroke="currentColor"
                                    className="h-4 w-4 text-blue-500"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      d="M19.5 8.25l-7.5 7.5-7.5-7.5"
                                    />
                                  </svg>
                                }
                                selected={() => (
                                  <div className="flex items-center gap-2">
                                    {csrFormData.country ? (
                                      <>
                                        <span className="font-medium">
                                          {countries[csrFormData.country]?.name}
                                        </span>
                                        <span className="text-xs text-gray-500">
                                          ({csrFormData.country})
                                        </span>
                                      </>
                                    ) : (
                                      <span className="text-gray-500">{t("csr.choose_country") || "Choose a country"}</span>
                                    )}
                                  </div>
                                )}
                              >
                                <div className="sticky top-0 p-2 bg-white z-10 border-b border-blue-100">
                                  <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                      </svg>
                                    </div>
                                    <input
                                      type="text"
                                      placeholder={t("csr.search_country") || "Search country..."}
                                      value={countrySearch}
                                      onChange={(e) => setCountrySearch(e.target.value)}
                                      className="w-full pl-10 p-2 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 text-sm"
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    {countrySearch && (
                                      <div
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setCountrySearch("");
                                        }}
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {(() => {
                                  const filteredCountries = Object.entries(countries)
                                    .filter(([code, country]) =>
                                      country.name.toLowerCase().includes(countrySearch.toLowerCase()) ||
                                      code.toLowerCase().includes(countrySearch.toLowerCase())
                                    )
                                    .sort((a, b) => a[1].name.localeCompare(b[1].name));

                                  if (filteredCountries.length === 0) {
                                    return (
                                      <div className="p-3 text-center text-gray-500 text-sm">
                                        {t("csr.no_countries_found") || "No countries found"}
                                      </div>
                                    );
                                  }

                                  return filteredCountries.map(([code, country]) => (
                                    <Option
                                      key={code}
                                      value={code}
                                      className="hover:bg-blue-50 mb-1 rounded-md transition-colors"
                                    >
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium">{country.name}</span>
                                        <span className="text-xs text-gray-500">({code})</span>
                                      </div>
                                    </Option>
                                  ));
                                })()}
                              </Select>
                              {errors.country && (
                                <Typography color="red" className="text-xs mt-1 flex items-center gap-1">
                                  <AlertCircle className="h-3 w-3" />
                                  {t("csr.errors.country_required")}
                                </Typography>
                              )}
                            </div>

                            {/* State/Province */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.state")}
                              </Typography>
                              <Input
                                type="text"
                                size="lg"
                                placeholder={t("csr.state_placeholder")}
                                value={csrFormData.state}
                                onChange={(e) => handleCsrFormChange("state", e.target.value)}
                                className="!border-gray-300 focus:!border-blue-500"
                                labelProps={{ className: "hidden" }}
                              />
                            </div>

                            {/* City/Locality */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.city")}
                              </Typography>
                              <Input
                                type="text"
                                size="lg"
                                placeholder={t("csr.city_placeholder")}
                                value={csrFormData.city}
                                onChange={(e) => handleCsrFormChange("city", e.target.value)}
                                className="!border-gray-300 focus:!border-blue-500"
                                labelProps={{ className: "hidden" }}
                              />
                            </div>

                            {/* Organization */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.organization")}
                              </Typography>
                              <Input
                                type="text"
                                size="lg"
                                placeholder={t("csr.organization_placeholder")}
                                value={csrFormData.organization}
                                onChange={(e) => handleCsrFormChange("organization", e.target.value)}
                                className="!border-gray-300 focus:!border-blue-500"
                                labelProps={{ className: "hidden" }}
                              />
                            </div>

                            {/* Organizational Unit */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.organizational_unit")}
                              </Typography>
                              <Input
                                type="text"
                                size="lg"
                                placeholder={t("csr.organizational_unit_placeholder")}
                                value={csrFormData.organizationalUnit}
                                onChange={(e) => handleCsrFormChange("organizationalUnit", e.target.value)}
                                className="!border-gray-300 focus:!border-blue-500"
                                labelProps={{ className: "hidden" }}
                              />
                            </div>

                            {/* Email */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.email")}
                              </Typography>
                              <Input
                                type="email"
                                size="lg"
                                placeholder="<EMAIL>"
                                value={csrFormData.email}
                                onChange={(e) => handleCsrFormChange("email", e.target.value)}
                                className="!border-gray-300 focus:!border-blue-500"
                                labelProps={{ className: "hidden" }}
                              />
                            </div>

                            {/* Key Size */}
                            <div>
                              <Typography className="text-sm font-medium text-gray-700 mb-1">
                                {t("csr.key_size")}
                              </Typography>
                              <Select
                                size="lg"
                                value={csrFormData.keySize}
                                onChange={(val) => handleCsrFormChange("keySize", val)}
                                className="!border-gray-300"
                              >
                                <Option value="2048">2048 bits (Standard)</Option>
                                <Option value="4096">4096 bits (Strong)</Option>
                              </Select>
                            </div>
                          </div>

                          {/* Generate Button */}
                          <Button
                            variant="filled"
                            color="blue"
                            className="mt-4 flex items-center justify-center gap-2"
                            onClick={handleGenerateCSR}
                            disabled={generateLoading}
                          >
                            {generateLoading ? (
                              <>
                                <span className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-1"></span>
                                {t("csr.generating")}
                              </>
                            ) : (
                              <>
                                <Lock className="h-4 w-4" />
                                {t("csr.generate_button")}
                              </>
                            )}
                          </Button>

                          {/* Generated CSR and Key Display */}
                          {csr && activeTab === 1 && (
                            <div className="mt-6 space-y-6">
                              {/* CSR Display */}
                              <div className="p-5 border border-blue-100 rounded-lg bg-blue-50">
                                <div className="flex items-center justify-between mb-3">
                                  <div>
                                    <Typography className="font-medium text-gray-800">
                                      {t("csr.generated_csr")}
                                    </Typography>
                                    <Typography className="text-xs text-gray-600 mt-1">
                                      {t("csr.csr_description")}
                                    </Typography>
                                  </div>
                                  <Button
                                    variant="text"
                                    color="blue"
                                    size="sm"
                                    className="p-1 h-8 w-8"
                                    onClick={() => copyToClipboard(csr, t("csr.csr_copied"))}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </div>
                                <div className="bg-white p-3 rounded-md border border-blue-100 max-h-40 overflow-y-auto">
                                  <Typography className="font-mono text-xs whitespace-pre-wrap break-all">
                                    {csr}
                                  </Typography>
                                </div>
                              </div>

                              {/* Private Key Display - Improved Design */}
                              {generatedPrivateKey && (
                                <div className="p-5 border border-gray-300 rounded-lg bg-gray-50">
                                  <div className="flex items-center justify-between mb-3">
                                    <div>
                                      <Typography className="font-medium text-gray-800 flex items-center gap-2">
                                        <Lock className="h-4 w-4 text-red-500" />
                                        {t("csr.private_key")}
                                      </Typography>
                                      <Typography className="text-xs text-gray-600 mt-1">
                                        {t("csr.private_key_description")}
                                      </Typography>
                                    </div>
                                    <Button
                                      variant="text"
                                      color="blue"
                                      size="sm"
                                      className="p-1 h-8 w-8"
                                      onClick={() => copyToClipboard(generatedPrivateKey, t("csr.key_copied"))}
                                    >
                                      <Copy className="h-4 w-4" />
                                    </Button>
                                  </div>
                                  <div className="bg-white p-3 rounded-md border border-gray-200 max-h-40 overflow-y-auto">
                                    <Typography className="font-mono text-xs whitespace-pre-wrap break-all text-gray-800">
                                      {generatedPrivateKey}
                                    </Typography>
                                  </div>
                                  <div className="flex items-start gap-3 mt-3 p-3 bg-red-50 border border-red-100 rounded-md">
                                    <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                                    <Typography className="text-sm text-gray-800">
                                      {t("csr.private_key_warning_extended")}
                                    </Typography>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Domain Validation */}
            {activeStep === 1 && (
              <div className="space-y-6">
                <div className="pb-6 border-b border-gray-100">
                  <Typography variant="h4" className="text-xl font-semibold text-gray-800 mb-2">
                    {t("csr.steps.domain_validation")}
                  </Typography>
                  <Typography className="text-gray-600">
                    {t("csr.email_validation_info")}
                  </Typography>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-100 mb-6">
                    <div className="flex items-start gap-3">
                      <div className="bg-white p-2 rounded-full border border-blue-200">
                        <Globe className="h-5 w-5 text-blue-500" />
                      </div>
                      <div>
                        <Typography className="font-medium text-gray-800">
                          {domain}
                        </Typography>
                        <Typography className="text-sm text-gray-600 mt-1">
                          {t("validation.email_help")}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-blue-500" />
                      <Typography className="text-sm font-medium text-gray-700">
                        {t("validation.email_label")}
                      </Typography>
                    </div>
                    <Input
                      type="email"
                      size="lg"
                      placeholder={`admin@${domain}`}
                      value={selectedEmail}
                      onChange={(e) => setSelectedEmail(e.target.value)}
                      error={!!errors.email}
                      className="!border-gray-300 focus:!border-blue-500"
                      labelProps={{
                        className: "hidden",
                      }}
                    />
                    {errors.email && (
                      <Typography color="red" className="text-xs mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.email}
                      </Typography>
                    )}

                    <div className="flex items-start gap-3 mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
                      <Info className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                      <Typography className="text-sm text-blue-800">
                        {t("validation.email_domain_requirement")}
                      </Typography>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Review & Confirm */}
            {activeStep === 2 && (
              <div className="space-y-6">
                <div className="pb-6 border-b border-gray-100">
                  <Typography variant="h4" className="text-xl font-semibold text-gray-800 mb-2">
                    {t("csr.steps.review_confirm")}
                  </Typography>
                  <Typography className="text-gray-600">
                    {t("csr.review_info")}
                  </Typography>
                </div>

                <div className="p-5 bg-blue-50 rounded-xl border border-blue-100 mb-6">
                  <div className="flex items-center gap-3 mb-4">
                    <ShieldCheck className="h-6 w-6 text-blue-500" />
                    <Typography className="font-medium text-gray-800">
                      {t("ssl_activation")}
                    </Typography>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Existing review cards */}
                    <div className="p-4 bg-white rounded-lg border border-blue-100 shadow-sm">
                      <Typography className="text-xs text-gray-500 mb-1">
                        {t("csr.certificate_type")}
                      </Typography>
                      <Typography className="font-medium text-gray-800 flex items-center gap-2">
                        <Shield className="h-4 w-4 text-blue-500" />
                        {getLocalizedContent(sslOrder.package, "name", locale)}
                      </Typography>
                    </div>

                    <div className="p-4 bg-white rounded-lg border border-blue-100 shadow-sm">
                      <Typography className="text-xs text-gray-500 mb-1">
                        {t("csr.domain_name")}
                      </Typography>
                      <Typography className="font-medium text-gray-800 flex items-center gap-2">
                        <Globe className="h-4 w-4 text-blue-500" />
                        {domain}
                      </Typography>
                    </div>

                    <div className="p-4 bg-white rounded-lg border border-blue-100 shadow-sm">
                      <Typography className="text-xs text-gray-500 mb-1">
                        {t("csr.validation_email")}
                      </Typography>
                      <Typography className="font-medium text-gray-800 flex items-center gap-2 truncate">
                        <Mail className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        <span className="truncate">{selectedEmail}</span>
                      </Typography>
                    </div>

                    <div className="p-4 bg-white rounded-lg border border-blue-100 shadow-sm group relative">
                      <Typography className="text-xs text-gray-500 mb-1 flex items-center justify-between">
                        <span>{t("csr.csr_fingerprint")}</span>
                        <Button
                          variant="text"
                          color="blue"
                          size="sm"
                          className="p-0 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => copyToClipboard(csr)}
                        >
                          <Copy className="h-3.5 w-3.5" />
                        </Button>
                      </Typography>
                      <Typography className="font-mono text-xs bg-gray-50 p-2 rounded border border-gray-200 overflow-hidden text-gray-700">
                        {csr.substring(0, 40)}...
                      </Typography>
                    </div>
                  </div>
                </div>

                {/* SSL Installation Service Checkbox */}
                <div className="p-5 bg-green-50 border border-green-100 rounded-lg mb-6">
                  <div className="flex items-start gap-3">
                    <div className="flex items-center h-5 mt-0.5">
                      <input
                        id="install-service"
                        type="checkbox"
                        checked={installService}
                        onChange={(e) => setInstallService(e.target.checked)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="install-service" className="font-medium text-gray-800 flex items-center gap-2">
                        <Shield className="h-4 w-4 text-green-600" />
                        {t("install_service_title")}
                        <span className="ml-1 px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full font-bold">
                          500 MAD
                        </span>
                      </label>
                      {/* <Typography className="text-sm text-gray-700 mt-1">
                        {t("installing_service_description")}
                      </Typography> */}
                      <div className="flex flex-col sm:flex-row gap-3 mt-2">
                        <div className="flex items-center gap-1 text-xs text-green-700">
                          <CheckCircle className="h-3.5 w-3.5" />
                          <span>{t("install_benefit_1")}</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-green-700">
                          <CheckCircle className="h-3.5 w-3.5" />
                          <span>{t("install_benefit_2")}</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-green-700">
                          <CheckCircle className="h-3.5 w-3.5" />
                          <span>{t("install_benefit_3")}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 border border-yellow-100 rounded-lg">
                  <div className="flex items-start gap-3">
                    <Lock className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                    <Typography className="text-sm text-yellow-800">
                      {t("csr.activation_warning")}
                    </Typography>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation buttons */}
            <div className="mt-10 flex justify-between pt-6 border-t border-gray-100">
              {!isFirstStep && (
                <Button
                  variant="outlined"
                  color="blue"
                  className="flex items-center gap-2 px-4"
                  onClick={handlePrev}
                  disabled={submitting}
                >
                  <ArrowLeft className="h-4 w-4" />
                  {t("common.previous")}
                </Button>
              )}

              <Button
                variant="filled"
                color="blue"
                className={`flex items-center gap-2 px-4 shadow-md ${isFirstStep ? 'ml-auto' : ''}`}
                onClick={handleNext}
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-1"></span>
                    {t("common.processing")}
                  </>
                ) : isLastStep ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    {t("common.confirm_activation")}
                  </>
                ) : (
                  <>
                    {t("common.next")}
                    <ArrowRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
