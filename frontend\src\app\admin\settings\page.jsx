"use client";

import React, { useState, useEffect } from "react";
import {
  Save,
  AlertCircle,
  CheckCircle,
  Search,
  Settings as SettingsIcon,
  Globe,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Eye,
  Loader2,
  RefreshCw,
} from "lucide-react";
import siteSettingsService from "../../services/siteSettingsService";
import { toast } from "react-toastify";
import "./settings.css";

// Default settings structure
const defaultSettings = {
  general: {
    siteName: "",
    siteDescription: "",
    contactEmail: "",
    phone: "",
    address: "",
    socialLinks: {
      linkedin: "",
      instagram: "",
      facebook: "",
    },
  },
  seo: {
    defaultTitle: "",
    defaultDescription: "",
    defaultKeywords: "",
    favicon: "",
    googleTagManagerId: "",
    googleSiteVerification: "",
    bingVerification: "",
    robotsTxt: "",
    sitemapEnabled: true,
  },
};

export default function SettingsManagement() {
  const [settings, setSettings] = useState(defaultSettings);
  const [activeTab, setActiveTab] = useState("general");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Toast notification helper
  const showToast = {
    success: (message, options = {}) => {
      toast.success(message, {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        className: "toast-success",
        ...options,
      });
    },
    error: (message, options = {}) => {
      toast.error(message, {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        ...options,
      });
    },
    warning: (message, options = {}) => {
      toast.warn(message, {
        position: "top-right",
        autoClose: 6000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        ...options,
      });
    },
    info: (message, options = {}) => {
      toast.info(message, {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        ...options,
      });
    },
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await siteSettingsService.getSiteSettings();
      setSettings(response.data);

      showToast.success("✅ Settings loaded successfully", { autoClose: 2000 });
    } catch (err) {
      console.error("Error fetching settings:", err);
      const errorMessage =
        err.response?.data?.message ||
        "Failed to load settings. Please try again.";
      setError(errorMessage);

      showToast.error(`❌ Failed to load settings: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await siteSettingsService.updateSiteSettings(settings);
      setSettings(response.data);
      setHasChanges(false);

      // Show success toast
      showToast.success("🎉 Settings saved successfully!");

      setSuccess("Settings saved successfully!");
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error("Error saving settings:", err);

      // Extract detailed error information
      const errorData = err.response?.data;
      let errorMessage = "Failed to save settings";
      let detailedErrors = [];

      if (errorData) {
        errorMessage = errorData.message || errorMessage;

        // Handle validation errors
        if (errorData.errors && Array.isArray(errorData.errors)) {
          detailedErrors = errorData.errors;
          errorMessage = `Validation failed: ${detailedErrors.length} error(s)`;
        }
      }

      setError(errorMessage);

      // Show error toast with details
      if (detailedErrors.length > 0) {
        // Show main error toast
        showToast.error(`❌ ${errorMessage}`, { autoClose: 6000 });

        // Show individual validation errors
        detailedErrors.forEach((error, index) => {
          setTimeout(() => {
            showToast.warning(
              `🔸 ${error.field || "Field"}: ${error.message}`,
              {
                autoClose: 8000,
              }
            );
          }, (index + 1) * 500); // Stagger the error messages
        });
      } else {
        // Show single error toast
        showToast.error(`❌ ${errorMessage}`);
      }
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (section, field, value) => {
    setSettings((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));

    if (!hasChanges) {
      setHasChanges(true);
      showToast.info("📝 You have unsaved changes", {
        autoClose: 2000,
        toastId: "unsaved-changes", // Prevent duplicate toasts
      });
    }
  };

  const handleSocialLinkChange = (platform, value) => {
    setSettings((prev) => ({
      ...prev,
      general: {
        ...prev.general,
        socialLinks: {
          ...prev.general.socialLinks,
          [platform]: value,
        },
      },
    }));

    if (!hasChanges) {
      setHasChanges(true);
      showToast.info("📝 You have unsaved changes", {
        autoClose: 2000,
        toastId: "unsaved-changes", // Prevent duplicate toasts
      });
    }
  };

  const tabs = [
    {
      id: "general",
      label: "General",
      icon: SettingsIcon,
      description: "Basic site information and contact details",
    },
    {
      id: "seo",
      label: "SEO & Analytics",
      icon: Search,
      description: "Search engine optimization and tracking",
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "general":
        return (
          <div className="space-y-8">
            {/* Site Information Section */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
              <div className="flex items-center mb-4">
                <Globe className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Site Information
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Site Name *
                  </label>
                  <input
                    type="text"
                    value={settings.general.siteName}
                    onChange={(e) =>
                      handleInputChange("general", "siteName", e.target.value)
                    }
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                    placeholder="Enter your site name"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Site Description
                  </label>
                  <textarea
                    value={settings.general.siteDescription}
                    onChange={(e) =>
                      handleInputChange(
                        "general",
                        "siteDescription",
                        e.target.value
                      )
                    }
                    rows={3}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
                    placeholder="Brief description of your website"
                  />
                </div>
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
              <div className="flex items-center mb-4">
                <Mail className="h-5 w-5 text-green-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Contact Information
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Email *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="email"
                      value={settings.general.contactEmail}
                      onChange={(e) =>
                        handleInputChange(
                          "general",
                          "contactEmail",
                          e.target.value
                        )
                      }
                      className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="tel"
                      value={settings.general.phone}
                      onChange={(e) =>
                        handleInputChange("general", "phone", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      placeholder="+****************"
                    />
                  </div>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Business Address
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <textarea
                      value={settings.general.address}
                      onChange={(e) =>
                        handleInputChange("general", "address", e.target.value)
                      }
                      rows={3}
                      className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-200"
                      placeholder="123 Business Street, City, State, ZIP"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Social Media Section */}
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
              <div className="flex items-center mb-4">
                <ExternalLink className="h-5 w-5 text-purple-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Social Media Links
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    LinkedIn Profile
                  </label>
                  <div className="relative">
                    <ExternalLink className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="url"
                      value={settings.general.socialLinks.linkedin}
                      onChange={(e) =>
                        handleSocialLinkChange("linkedin", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
                      placeholder="https://linkedin.com/company/..."
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Instagram Profile
                  </label>
                  <div className="relative">
                    <ExternalLink className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="url"
                      value={settings.general.socialLinks.instagram}
                      onChange={(e) =>
                        handleSocialLinkChange("instagram", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
                      placeholder="https://instagram.com/..."
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Facebook Page
                  </label>
                  <div className="relative">
                    <ExternalLink className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="url"
                      value={settings.general.socialLinks.facebook}
                      onChange={(e) =>
                        handleSocialLinkChange("facebook", e.target.value)
                      }
                      className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-200"
                      placeholder="https://facebook.com/..."
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case "seo":
        return (
          <div className="space-y-8">
            {/* Meta Tags Section */}
            <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-xl p-6 border border-indigo-100">
              <div className="flex items-center mb-4">
                <Search className="h-5 w-5 text-indigo-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Meta Tags & SEO
                </h3>
              </div>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Meta Title *
                  </label>
                  <input
                    type="text"
                    value={settings.seo.defaultTitle}
                    onChange={(e) =>
                      handleInputChange("seo", "defaultTitle", e.target.value)
                    }
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                    placeholder="Your Site Title - Tagline"
                    maxLength={60}
                  />
                  <div className="flex justify-between mt-1">
                    <p className="text-sm text-gray-500">
                      Recommended length: 50-60 characters
                    </p>
                    <p className="text-sm text-gray-400">
                      {settings.seo.defaultTitle.length}/60
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Meta Description *
                  </label>
                  <textarea
                    value={settings.seo.defaultDescription}
                    onChange={(e) =>
                      handleInputChange(
                        "seo",
                        "defaultDescription",
                        e.target.value
                      )
                    }
                    rows={3}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                    placeholder="A compelling description of your website that appears in search results"
                    maxLength={160}
                  />
                  <div className="flex justify-between mt-1">
                    <p className="text-sm text-gray-500">
                      Recommended length: 150-160 characters
                    </p>
                    <p className="text-sm text-gray-400">
                      {settings.seo.defaultDescription.length}/160
                    </p>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Default Keywords
                  </label>
                  <input
                    type="text"
                    value={settings.seo.defaultKeywords}
                    onChange={(e) =>
                      handleInputChange(
                        "seo",
                        "defaultKeywords",
                        e.target.value
                      )
                    }
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                    placeholder="keyword1, keyword2, keyword3"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Comma-separated keywords relevant to your site
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Favicon URL
                  </label>
                  <div className="flex items-center space-x-3">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={settings.seo.favicon}
                        onChange={(e) =>
                          handleInputChange("seo", "favicon", e.target.value)
                        }
                        className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
                        placeholder="https://example.com/favicon.ico"
                      />
                    </div>
                    {settings.seo.favicon && (
                      <div className="flex items-center space-x-2">
                        <img
                          src={settings.seo.favicon}
                          alt="Favicon preview"
                          className="w-6 h-6 rounded"
                          onError={(e) => (e.target.style.display = "none")}
                        />
                        <Eye className="h-4 w-4 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    URL to your site&apos;s favicon (16x16 or 32x32 pixels)
                  </p>
                </div>
              </div>
            </div>

            {/* Analytics & Tracking Section */}
            <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100">
              <div className="flex items-center mb-4">
                <Globe className="h-5 w-5 text-orange-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Analytics & Tracking
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Google Tag Manager ID
                  </label>
                  <input
                    type="text"
                    value={settings.seo.googleTagManagerId}
                    onChange={(e) =>
                      handleInputChange(
                        "seo",
                        "googleTagManagerId",
                        e.target.value
                      )
                    }
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all duration-200"
                    placeholder="GTM-XXXXXXXXXX"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Your Google Tag Manager measurement ID
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Google Site Verification
                  </label>
                  <input
                    type="text"
                    value={settings.seo.googleSiteVerification}
                    onChange={(e) =>
                      handleInputChange(
                        "seo",
                        "googleSiteVerification",
                        e.target.value
                      )
                    }
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all duration-200"
                    placeholder="verification-code"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Google Search Console verification code
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bing Verification
                  </label>
                  <input
                    type="text"
                    value={settings.seo.bingVerification}
                    onChange={(e) =>
                      handleInputChange(
                        "seo",
                        "bingVerification",
                        e.target.value
                      )
                    }
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all duration-200"
                    placeholder="verification-code"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Bing Webmaster Tools verification code
                  </p>
                </div>
              </div>
            </div>

            {/* Advanced SEO Section */}
            <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <SettingsIcon className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Advanced SEO
                </h3>
              </div>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Robots.txt Content
                  </label>
                  <textarea
                    value={settings.seo.robotsTxt}
                    onChange={(e) =>
                      handleInputChange("seo", "robotsTxt", e.target.value)
                    }
                    rows={4}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:border-gray-500 focus:ring-2 focus:ring-gray-200 transition-all duration-200 font-mono text-sm"
                    placeholder="User-agent: *&#10;Allow: /"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Content for your robots.txt file
                  </p>
                </div>

                <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      XML Sitemap
                    </h4>
                    <p className="text-sm text-gray-500">
                      Enable automatic XML sitemap generation
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.seo.sitemapEnabled}
                      onChange={(e) =>
                        handleInputChange(
                          "seo",
                          "sitemapEnabled",
                          e.target.checked
                        )
                      }
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Site Settings
              </h1>
              <p className="text-gray-600 text-lg">
                Manage your website&apos;s configuration and SEO settings
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {hasChanges && (
                <div className="flex items-center text-amber-600 bg-amber-50 px-3 py-2 rounded-lg border border-amber-200 animate-pulse">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">Unsaved changes</span>
                </div>
              )}
              {loading && (
                <div className="flex items-center text-blue-600">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  <span className="text-sm">Loading...</span>
                </div>
              )}
              <button
                onClick={() => {
                  fetchSettings();
                  showToast.info("🔄 Refreshing settings...", {
                    autoClose: 2000,
                  });
                }}
                disabled={loading || saving}
                className="flex items-center px-3 py-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 disabled:opacity-50"
                title="Refresh settings"
              >
                <RefreshCw
                  className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                />
              </button>
            </div>
          </div>
        </div>
        {/* Fallback Alerts (shown if toasts fail)
      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center alert-slide-in">
          <AlertCircle className="h-5 w-5 mr-2" />
          <div>
            <p className="font-medium">{error}</p>
            <p className="text-sm mt-1 opacity-75">Check the browser console for more details</p>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 text-green-600 p-4 rounded-lg flex items-center alert-slide-in">
          <CheckCircle className="h-5 w-5 mr-2" />
          {success}
        </div>
      )}*/}

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 inline-flex items-center border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="p-6">{renderTabContent()}</div>

            <div className="bg-gray-50 px-6 py-4 flex justify-end">
              <button
                type="submit"
                disabled={saving}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 flex items-center"
              >
                <Save className="h-5 w-5 mr-2" />
                {saving ? "Saving..." : "Save Changes"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
