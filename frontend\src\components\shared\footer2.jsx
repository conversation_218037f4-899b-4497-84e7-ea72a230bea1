"use client";
import {
  EnvelopeOpenIcon,
  MapPinIcon,
  PhoneIcon,
} from "@heroicons/react/24/solid";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import {
  FaFacebook,
  FaFacebookSquare,
  FaInstagram,
  FaLinkedin,
  FaLinkedinIn,
  FaTiktok,
  FaYoutube,
} from "react-icons/fa";

const Footer2 = () => {
  const pathname = usePathname();
  const t = useTranslations("shared");

  // Update this check to look for /admin directly
  if (pathname.includes("/client") || pathname.startsWith("/admin")) {
    console.log("pathname contains: " + pathname);
    return null;
  }

  return (
    <footer
      className="relative bg-gradient-to-r from-blue-900 to-indigo-900 text-[#576484] w-full"
      style={{
        background: "linear-gradient(90deg, #092654 0%, #140537 100%)",
      }}
    >
      <div className="max-w-[1800px] m-auto py-10 xl:py-4">
        <div className="flex justify-between mx-auto md:flex-row flex-col">
          <div className="w-full md:w-1/4 p-4">
            <Link href={"/"}>
              <Image
                src="/images/home/<USER>"
                alt="Accueil"
                className="mb-4"
                width={200}
                height={100}
              />
            </Link>
            <p className="mt-4 text-sm text-[#cad2e7]">
              {t("footer_citation")}
            </p>
            <div className="w-fit cursor-pointer mt-16 px-6 py-2 border border-gray-500 text-white rounded hover:bg-white hover:text-primary transition">
              <Link href={"#contact-nous"}>{t("contact_us")}</Link>
            </div>
          </div>

          <div className="hidden md:flex justify-between w-3/5 xl:w-full p-4">
            <div className="flex flex-col items-center gap-y-8 mx-auto">
              <div>
                <h3 className="text-lg font-bold mb-4 text-white">
                  {t("quick_links")}
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="hover:text-white">
                    <Link href={"about-us"}>{t("about_us")}</Link>
                  </li>
                  <li className="hover:text-white">
                    <Link href={"blog"}>Blog</Link>
                  </li>
                  <li className="hover:text-white">
                    <Link href={"guide"}>Guide</Link>
                  </li>
                  <li className="hover:text-white">
                    <Link href={"terms-of-service"}>
                      {t("terms_and_conditions")}
                    </Link>
                  </li>
                  <li className="hover:text-white">
                    <Link href={"privacy-policy"}>{t("privacy_policy")}</Link>
                  </li>
                </ul>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-bold mb-4 text-white">
                {t("company")}
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex flex-row gap-x-2">
                  <span className="inline-block">
                    <MapPinIcon className="w-[17px] text-secondary" />
                  </span>
                  <span>{t("address")}</span>
                </li>
                <li className="flex flex-row gap-x-2">
                  <span className="inline-block">
                    <PhoneIcon className="w-[17px] text-secondary" />
                  </span>
                  <span>{t("phone")}: +212 *********</span>
                </li>
                <li className="flex flex-row gap-x-2">
                  <span className="inline-block">
                    <EnvelopeOpenIcon className="w-[17px] text-secondary" />
                  </span>
                  <span>{t("email")}: <EMAIL></span>
                </li>
              </ul>
              <div className="mt-16 flex flex-col gap-y-8">
                <div className="flex space-x-4 text-white">
                  <Link
                  aria-label="facebook"
                    href="https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr"
                    target="_blank"
                    name="facebook"
                    rel="noopener noreferrer"
                    className="text-white hover:text-blue-800 p-2 rounded-full bg-transparent  border border-gray-700"
                  >
                    <FaFacebookSquare />
                  </Link>
                  <Link
                  aria-label="instagram"
                    href="https://www.instagram.com/ztechengineering"
                    target="_blank"
                    name="instagram"
                    className="text-white hover:text-deep-orange-100 p-2 rounded-full bg-transparent  border border-gray-700"
                  >
                    <FaInstagram />
                  </Link>
                  <Link
                  aria-label="tiktok"
                    href="https://www.tiktok.com/@ztechengineering?is_from_webapp=1&sender_device=pc"
                    target="_blank"
                    name="tiktok"
                    rel="noopener noreferrer"
                    className="text-white hover:text-purple-400 p-2 rounded-full border border-gray-700"
                  >
                    <FaTiktok />
                  </Link>
                  <Link
                  aria-label="linkedin"
                    href="https://www.linkedin.com/company/ztechengineering"
                    target="_blank"
                    name="linkedin"
                    rel="noopener noreferrer"
                    className="text-white hover:text-blue-500 p-2 rounded-full border border-gray-700"
                  >
                    <FaLinkedinIn />
                  </Link>
                  <Link
                  aria-label="youtube"
                    href="https://www.youtube.com/@ztechengineering/"
                    target="_blank"
                    name="youtube"
                    rel="noopener noreferrer"
                    className="text-white hover:text-red-500 p-2 rounded-full border border-gray-700"
                  >
                    <FaYoutube />
                  </Link>
                </div>
                <img
                  className="w-[600px] h-fit rounded-lg"
                  src="/images/payzone/Moyen-de-sécurité-ABB2.png"
                  alt="Payment Methods"
                />
              </div>
            </div>
          </div>

          <div className="flex md:hidden flex-col gap-y-4 w-full justify-between md:w-1/2 p-4 my-5 md:m-0">
            <details open>
              <summary className="text-base font-semibold mb-0 text-white">
                {t("company")}
              </summary>
              <hr />
              <div className="flex flex-col gap-y-5 p-6">
                <ul className="space-y-2 text-sm">
                  <li className="flex flex-row gap-x-2">
                    <span className="inline-block">
                      <MapPinIcon className="w-[17px] text-secondary" />
                    </span>
                    <span>{t("address")}</span>
                  </li>
                  <li className="flex flex-row gap-x-2">
                    <span className="inline-block">
                      <PhoneIcon className="w-[17px] text-secondary" />
                    </span>
                    <span>{t("phone")}: +212 *********</span>
                  </li>
                  <li className="flex flex-row gap-x-2">
                    <span className="inline-block">
                      <EnvelopeOpenIcon className="w-[17px] text-secondary" />
                    </span>
                    <span>{t("email")}: <EMAIL></span>
                  </li>
                </ul>
              </div>
            </details>

            <details>
              <summary className="text-base font-semibold mb-0 text-white">
                {t("quick_links")}
              </summary>
              <hr />
              <ul className="flex flex-col gap-y-2 text-sm p-6">
                <li className="hover:text-white">
                  <Link href={"blog"}>Blog</Link>
                </li>
                <li className="hover:text-white">
                  <Link href={"guide"}>Guide</Link>
                </li>
                <li className="hover:text-white">
                  <Link href={"about-us"}>{t("about_us")}</Link>
                </li>
                <li className="hover:text-white">
                  <Link href={"privacy-policy"}>{t("privacy_policy")}</Link>
                </li>
                <li className="hover:text-white">
                  <Link href={"terms-of-service"}>
                    {t("terms_and_conditions")}
                  </Link>
                </li>
              </ul>
            </details>

            <details>
              <summary className="text-base font-semibold mb-0 text-white">
                Services
              </summary>
              <hr />
              <div className="flex flex-col gap-y-5 uppercase text-xs text-white p-6">
                <Link href="/web-development" className="hover:text-secondary">
                  {t("services.web_creation")}
                </Link>
                <Link href="/hosting" className="hover:text-secondary">
                  {t("services.hosting")}
                </Link>
                <Link href="/ssl" className="hover:text-secondary">
                  SSL
                </Link>
                <Link href="/cloud-maroc" className="hover:text-secondary">
                  {t("services.cloud_morocco")}
                </Link>
                <Link href="/cloud-security" className="hover:text-secondary">
                  {t("services.cloud_security")}
                </Link>
                <Link href="/managed-services" className="hover:text-secondary">
                  {t("services.managed_services")}
                </Link>
                {/* <Link href="/servers" className="hover:text-secondary">
                  {t("services.servers")}
                </Link> */}
                
                <Link href="/ai-services" className="hover:text-secondary">
                  {t("services.ai-services")}
                </Link>
              </div>
            </details>
            <Image
              width={400}
              height={400}
              className="w-[400px] h-fit rounded-lg"
              src="/images/payzone/Moyen-de-sécurité-ABB2.png"
              alt="moyen de sécurité payzone"
            />
          </div>
        </div>

        <div className="mt-10 border-t border-gray-600 pt-6">
          <div className="mx-auto px-4 flex flex-col md:flex-row items-center justify-between">
            <div className="md:hidden mt-0 flex space-x-4 text-white">
              <Link
                href="https://web.facebook.com/profile.php?id=61551999353576&_rdc=1&_rdr"
                name="facebook"
                className="text-white"
              >
                <FaFacebook className="w-[25px] h-auto" />
              </Link>
              <Link
                href="https://www.instagram.com/ztechengineering"
                name="instagram"
                className="text-white"
              >
                <FaInstagram className="w-[25px] h-auto" />
              </Link>
              <Link
                href="https://www.linkedin.com/company/ztechengineering"
                name="linkedin"
                className="text-white"
              >
                <FaLinkedin className="w-[25px] h-auto" />
              </Link>
              <Link
                href="https://www.tiktok.com/@ztechengineering?is_from_webapp=1&sender_device=pc"
                name="tiktok"
                className="text-white"
              >
                <FaTiktok className="w-[25px] h-auto" />
              </Link>
              <Link
                href="https://www.youtube.com/@ztechengineering/"
                name="youtube"
                className="text-white"
              >
                <FaYoutube className="w-[25px] h-auto" />
              </Link>
            </div>

            <div className="hidden md:flex uppercase gap-x-5 text-xs text-white">
              <Link href="/web-development" className="hover:text-secondary">
                {t("services.web_creation")}
              </Link>
              <Link href="/hosting" className="hover:text-secondary">
                {t("services.hosting")}
              </Link>
              <Link href="/ssl" className="hover:text-secondary">
                SSL
              </Link>
              <Link href="/cloud-maroc" className="hover:text-secondary">
                {t("services.cloud_morocco")}
              </Link>
              <Link href="/cloud-security" className="hover:text-secondary">
                {t("services.cloud_security")}
              </Link>
              <Link href="/managed-services" className="hover:text-secondary">
                {t("services.managed_services")}
              </Link>
              {/* <Link href="/servers" className="hover:text-secondary">
                {t("services.servers")}
              </Link> */}
              
              <Link href="/ai-services" className="hover:text-secondary">
                {t("services.ai-services")}
              </Link>
            </div>
            <p className="text-xs text-gray-400 mt-4 md:mt-0">
              © {new Date().getFullYear()} ZTech Engineering
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer2;
