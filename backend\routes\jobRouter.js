const express = require("express");
const multer = require("multer");
const path = require("path");
const { asyncBackFrontEndLang } = require("../midelwares/sharedMidd");
const { getAllJobs, getJobById, applyForJob, getUserApplication, getUserApplicationInterviews } = require("../controllers/admin/jobController");
const { authenticateToken } = require("../midelwares/auth");
const jobRouter = express.Router();

// Sanitize filename
const sanitizeFilename = (filename) => {
  return filename.replace(/[^a-zA-Z0-9.]/g, "_");
};

// Multer configuration for job applications
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, "..", "public", "uploads", "job-applications"));
  },
  filename: function (req, file, cb) {
    const sanitizedFilename = sanitizeFilename(file.originalname);
    cb(null, Date.now() + "-" + sanitizedFilename); // Unique filename
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // Maximum file size: 5MB
    files: 2, // Allow up to 2 files (resume and cover letter)
  },
  fileFilter: function (req, file, cb) {
    // Accept only PDF, DOC, and DOCX files
    const filetypes = /pdf|doc|docx/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error("Only PDF, DOC, and DOCX files are allowed"));
  },
});

// Define the fields for file uploads
const uploadFields = upload.fields([
  { name: 'resume', maxCount: 1 },
  { name: 'coverLetter', maxCount: 1 }
]);

jobRouter.get("/", asyncBackFrontEndLang, getAllJobs);
jobRouter.get("/:id", asyncBackFrontEndLang, getJobById);
jobRouter.post("/:id/apply", authenticateToken, asyncBackFrontEndLang, uploadFields, applyForJob);

// User application tracking routes (require authentication)
jobRouter.get("/:id/my-application", authenticateToken, asyncBackFrontEndLang, getUserApplication);
jobRouter.get("/:id/my-interviews", authenticateToken, asyncBackFrontEndLang, getUserApplicationInterviews);


module.exports = jobRouter;