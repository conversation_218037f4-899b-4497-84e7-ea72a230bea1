import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

class CustomImageService {
  constructor() {
    this.apiClient = axios.create({
      baseURL: `${API_BASE_URL}/api/custom-images`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(`🔄 Custom Image API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Custom Image API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`✅ Custom Image API Response: ${response.status}`, response.data);
        return response;
      },
      (error) => {
        console.error('❌ Custom Image API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Create a new custom image
   * @param {Object} imageData - Custom image data
   * @returns {Promise<Object>} API response
   */
  async createCustomImage(imageData) {
    try {
      console.log('🖼️ Creating custom image:', imageData);
      
      const response = await this.apiClient.post('/', imageData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Custom image created successfully'
      };
    } catch (error) {
      console.error('❌ Failed to create custom image:', error);
      throw new Error(
        error.response?.data?.message || 
        'Failed to create custom image'
      );
    }
  }

  /**
   * Get all custom images
   * @returns {Promise<Array>} List of custom images
   */
  async getCustomImages() {
    try {
      console.log('📋 Fetching custom images...');
      
      const response = await this.apiClient.get('/');
      
      return {
        success: true,
        data: response.data.data || [],
        message: response.data.message || 'Custom images retrieved successfully'
      };
    } catch (error) {
      console.error('❌ Failed to fetch custom images:', error);
      throw new Error(
        error.response?.data?.message || 
        'Failed to fetch custom images'
      );
    }
  }

  /**
   * Get custom image details
   * @param {string} imageId - Image ID
   * @returns {Promise<Object>} Image details
   */
  async getCustomImageDetails(imageId) {
    try {
      console.log('🔍 Getting custom image details:', imageId);
      
      const response = await this.apiClient.get(`/${imageId}`);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Custom image details retrieved successfully'
      };
    } catch (error) {
      console.error('❌ Failed to get custom image details:', error);
      throw new Error(
        error.response?.data?.message || 
        'Failed to get custom image details'
      );
    }
  }

  /**
   * Update a custom image
   * @param {string} imageId - Image ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Update result
   */
  async updateCustomImage(imageId, updateData) {
    try {
      console.log('✏️ Updating custom image:', imageId, updateData);
      
      const response = await this.apiClient.put(`/${imageId}`, updateData);
      
      return {
        success: true,
        data: response.data.data,
        message: response.data.message || 'Custom image updated successfully'
      };
    } catch (error) {
      console.error('❌ Failed to update custom image:', error);
      throw new Error(
        error.response?.data?.message || 
        'Failed to update custom image'
      );
    }
  }

  /**
   * Delete a custom image
   * @param {string} imageId - Image ID
   * @returns {Promise<Object>} Deletion result
   */
  async deleteCustomImage(imageId) {
    try {
      console.log('🗑️ Deleting custom image:', imageId);
      
      const response = await this.apiClient.delete(`/${imageId}`);
      
      return {
        success: true,
        message: response.data.message || 'Custom image deleted successfully'
      };
    } catch (error) {
      console.error('❌ Failed to delete custom image:', error);
      throw new Error(
        error.response?.data?.message || 
        'Failed to delete custom image'
      );
    }
  }
}

// Create and export a singleton instance
const customImageService = new CustomImageService();
export default customImageService;
