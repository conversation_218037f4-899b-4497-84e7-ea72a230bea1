import apiService from '../lib/apiService';

const specificationService = {
    createSpecification: (data) => apiService.post('/specification/create-specification', data),
    getSpecifications: () => apiService.get('/specification/get-specifications'),
    getSpecification: (id) => apiService.get(`/specification/get-specification/${id}`),
    updateSpecification: (id, data) => apiService.put(`/specification/update-specification/${id}`, data),
    deleteSpecification: (id) => apiService.delete(`/specification/delete-specification/${id}`),
};

export default specificationService;
