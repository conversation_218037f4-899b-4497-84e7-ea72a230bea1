"use client";

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Briefcase, Edit, Trash2, Plus, Eye, CheckCircle, XCircle, FileText,X, Mail, Phone, Download, Clock, AlertCircle, ArrowLeft, Archive, Calendar, Users } from 'lucide-react';
import { adminService } from '@/app/services/adminService';
import { toast } from 'react-toastify';
import { BACKEND_URL } from '@/app/config/constant';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import InterviewCalendar from '@/components/InterviewCalendar';

function JobApplicationsPage() {
  const { jobId } = useParams();
  const [applications, setApplications] = useState([]);
  const [jobTitle, setJobTitle] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [admins, setAdmins] = useState([]);
  const [existingInterviews, setExistingInterviews] = useState([]);
  const [showCalendar, setShowCalendar] = useState(false);
  const [showInterviewDetails, setShowInterviewDetails] = useState(false);
  const [selectedInterview, setSelectedInterview] = useState(null);
  const [showEditInterview, setShowEditInterview] = useState(false);
  const [editInterviewData, setEditInterviewData] = useState({
    scheduledAt: '',
    duration: 60,
    interviewerId: '',
    notes: '',
    status: 'scheduled'
  });

  useEffect(() => {
    if (jobId) {
      fetchJobAndApplications(jobId);
    }
  }, [jobId]);

  const fetchJobAndApplications = async (id) => {
    setLoading(true);
    setError(null);

    try {
      // Fetch job details
      const jobResponse = await adminService.getJobById(id);
      const jobData = jobResponse.data;

      // Set job title (handle multilingual titles)
      if (jobData.title && typeof jobData.title === 'object') {
        setJobTitle(jobData.title.en || Object.values(jobData.title)[0]);
      } else {
        setJobTitle(jobData.title || 'Job');
      }

      // Fetch job applications
      const applicationsResponse = await adminService.getJobApplications(id);
      setApplications(applicationsResponse.data || []);
    } catch (err) {
      console.error('Error fetching job and applications:', err);
      toast.error('Failed to fetch job applications');
      setError('Failed to fetch job details or applications');
      setApplications([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch admins for interviewer selection
  const fetchAdmins = async () => {
    try {
      const response = await adminService.getAdmins();
      console.log('Admins API response:', response);
      setAdmins(response.data || []);
    } catch (err) {
      console.error('Error fetching admins:', err);
      toast.error('Failed to fetch admin users');
    }
  };

  // Fetch existing interviews
  const fetchExistingInterviews = async () => {
    try {
      const response = await adminService.getInterviews();
      console.log('Interviews API response:', response);

      // Handle different response structures
      let interviews = [];
      if (response?.data?.data) {
        // If response has nested data structure
        interviews = response.data.data;
      } else if (response?.data) {
        // If response.data is the interviews array
        interviews = response.data;
      }

      // Ensure we always set an array
      setExistingInterviews(Array.isArray(interviews) ? interviews : []);
      console.log('Set interviews:', Array.isArray(interviews) ? interviews : []);
    } catch (err) {
      console.error('Error fetching interviews:', err);
      // Set empty array on error to prevent map errors
      setExistingInterviews([]);
    }
  };

  // Open interview scheduling calendar
  const openInterviewModal = (application) => {
    setSelectedApplication(application);
    setShowCalendar(true);
    fetchAdmins();
    fetchExistingInterviews();
  };

  // Close interview calendar
  const closeInterviewModal = () => {
    setShowCalendar(false);
    setSelectedApplication(null);
  };

  // Schedule interview from calendar
  const handleScheduleInterview = async (interviewPayload) => {
    try {
      // Create interview
      const response = await adminService.createInterview(interviewPayload);

      if (response.data.meetLink) {
        toast.success('Interview scheduled successfully! Google Meet link created.');
      } else {
        toast.success('Interview scheduled successfully!');
      }

      // Update application status to 'interviewed'
      await updateApplicationStatus(selectedApplication._id, 'interviewed');

      // Refresh interviews list
      fetchExistingInterviews();

      closeInterviewModal();
    } catch (err) {
      console.error('Error scheduling interview:', err);
      toast.error('Failed to schedule interview');
    }
  };

  // View interview details
  const viewInterviewDetails = (interview) => {
    setSelectedInterview(interview);
    setShowInterviewDetails(true);
  };

  // Open edit interview modal
  const openEditInterview = (interview) => {
    setSelectedInterview(interview);
    setEditInterviewData({
      scheduledAt: new Date(interview.scheduledAt).toISOString().slice(0, 16),
      duration: interview.duration || 60,
      interviewerId: interview.interviewerId,
      notes: interview.notes || '',
      status: interview.status || 'scheduled'
    });
    setShowEditInterview(true);
  };

  // Close interview modals
  const closeInterviewModals = () => {
    setShowInterviewDetails(false);
    setShowEditInterview(false);
    setSelectedInterview(null);
    setEditInterviewData({
      scheduledAt: '',
      duration: 60,
      interviewerId: '',
      notes: '',
      status: 'scheduled'
    });
  };

  // Handle edit interview data changes
  const handleEditInterviewChange = (field, value) => {
    setEditInterviewData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Update interview
  const updateInterview = async () => {
    try {
      if (!editInterviewData.scheduledAt || !editInterviewData.interviewerId) {
        toast.error('Please fill in all required fields');
        return;
      }

      const updatePayload = {
        scheduledAt: new Date(editInterviewData.scheduledAt).toISOString(),
        duration: editInterviewData.duration,
        interviewerId: editInterviewData.interviewerId,
        notes: editInterviewData.notes,
        status: editInterviewData.status
      };

      await adminService.updateInterview(selectedInterview._id, updatePayload);
      toast.success('Interview updated successfully!');

      // Refresh interviews list
      fetchExistingInterviews();

      closeInterviewModals();
    } catch (err) {
      console.error('Error updating interview:', err);
      toast.error('Failed to update interview');
    }
  };

  // Delete interview
  const deleteInterview = async (interviewId) => {
    if (!window.confirm('Are you sure you want to delete this interview?')) {
      return;
    }

    try {
      await adminService.deleteInterview(interviewId);
      toast.success('Interview deleted successfully!');

      // Refresh interviews list
      fetchExistingInterviews();

      closeInterviewModals();
    } catch (err) {
      console.error('Error deleting interview:', err);
      toast.error('Failed to delete interview');
    }
  };

  // Validate file path
  const validateFilePath = (path) => {
    if (!path) return false;

    // Check if path is a string
    if (typeof path !== 'string') return false;

    // Check if path has a valid extension
    const validExtensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
    const extension = path.toLowerCase().substring(path.lastIndexOf('.'));
    return validExtensions.includes(extension);
  };

  const handleDownloadResume = async (application) => {
    try {
      // Validate application object
      if (!application || typeof application !== 'object') {
        toast.error('Invalid application data');
        return;
      }

      // Validate resume path
      if (!application.resumePath) {
        toast.error('Resume file is not available');
        return;
      }

      if (!validateFilePath(application.resumePath)) {
        toast.error('Invalid resume file format');
        return;
      }

      // Download resume from the server using the correct backend URL
      const url = `${BACKEND_URL}${application.resumePath}`;

      try {
        // Check if file exists by making a HEAD request
        const checkResponse = await fetch(url, { method: 'HEAD' });
        if (!checkResponse.ok) {
          toast.error('Resume file not found on server');
          return;
        }
      } catch (error) {
        console.error("Error checking file existence:", error);
        toast.error('Could not verify file existence');
        return;
      }

      const a = document.createElement('a');
      a.href = url;
      const fileName = application.resumePath.split('/').pop();
      a.download = fileName || `${application.name}_resume.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast.success('Resume download started');
    } catch (err) {
      console.error("Error downloading resume:", err);
      toast.error('Could not download resume. The file may have been moved or deleted.');
    }
  };

  const handleDownloadCoverLetter = async (application) => {
    try {
      // Validate application object
      if (!application || typeof application !== 'object') {
        toast.error('Invalid application data');
        return;
      }

      // Validate cover letter path
      if (!application.coverLetterPath) {
        toast.error('Cover letter file is not available');
        return;
      }

      if (!validateFilePath(application.coverLetterPath)) {
        toast.error('Invalid cover letter file format');
        return;
      }

      // Download cover letter from the server using the correct backend URL
      const url = `${BACKEND_URL}${application.coverLetterPath}`;

      try {
        // Check if file exists by making a HEAD request
        const checkResponse = await fetch(url, { method: 'HEAD' });
        if (!checkResponse.ok) {
          toast.error('Cover letter file not found on server');
          return;
        }
      } catch (error) {
        console.error("Error checking file existence:", error);
        toast.error('Could not verify file existence');
        return;
      }

      const a = document.createElement('a');
      a.href = url;
      const fileName = application.coverLetterPath.split('/').pop();
      a.download = fileName || `${application.name}_cover_letter.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast.success('Cover letter download started');
    } catch (err) {
      console.error("Error downloading cover letter:", err);
      toast.error('Could not download cover letter. The file may have been moved or deleted.');
    }
  };

  // Validate application status
  const validateStatus = (status) => {
    const validStatuses = ['pending', 'reviewed', 'interviewed', 'hired', 'rejected'];
    return validStatuses.includes(status);
  };

  const updateApplicationStatus = async (applicationId, newStatus) => {
    try {
      // Validate the status before updating
      if (!validateStatus(newStatus)) {
        toast.error('Invalid status value');
        return;
      }

      // Validate application ID
      if (!applicationId) {
        toast.error('Invalid application ID');
        return;
      }

      // Update application status via API
      await adminService.updateApplicationStatus(applicationId, { status: newStatus });

      // Update local state
      setApplications(prev =>
        prev.map(app => app._id === applicationId ? { ...app, status: newStatus } : app)
      );

      toast.success(`Application status updated to ${newStatus}`);
    } catch (err) {
      console.error("Error updating application status:", err);
      toast.error('Failed to update application status');
    }
  };

  // Function to download all application files as a zip
  const downloadAllFiles = async () => {
    try {
      // Validate applications array
      if (!applications || !Array.isArray(applications) || applications.length === 0) {
        toast.error("No applications available to download");
        return;
      }

      // Show loading toast
      const loadingToastId = toast.loading("Preparing files for download...");

      // Create a new zip file
      const zip = new JSZip();

      // Track download promises
      const downloadPromises = [];

      // Track success and failure counts
      let successCount = 0;
      let failureCount = 0;

      // Process each application
      for (const application of applications) {
        // Validate application object
        if (!application || typeof application !== 'object' || !application.name) {
          console.error("Invalid application object:", application);
          failureCount++;
          continue;
        }

        // Create a folder for each applicant
        const folderName = application.name.replace(/[^a-z0-9]/gi, '-').toLowerCase();
        const folder = zip.folder(folderName);

        // Add resume if available
        if (application.resumePath && validateFilePath(application.resumePath)) {
          const resumePromise = fetch(`${BACKEND_URL}${application.resumePath}`)
            .then(response => {
              if (!response.ok) throw new Error(`Failed to fetch resume: ${response.statusText}`);
              return response.blob();
            })
            .then(blob => {
              const fileName = application.resumePath.split('/').pop();
              folder.file(fileName, blob);
              successCount++;
            })
            .catch(error => {
              console.error(`Error downloading resume for ${application.name}:`, error);
              failureCount++;
              // Continue with other files even if one fails
            });

          downloadPromises.push(resumePromise);
        }

        // Add cover letter if available
        if (application.coverLetterPath && validateFilePath(application.coverLetterPath)) {
          const coverLetterPromise = fetch(`${BACKEND_URL}${application.coverLetterPath}`)
            .then(response => {
              if (!response.ok) throw new Error(`Failed to fetch cover letter: ${response.statusText}`);
              return response.blob();
            })
            .then(blob => {
              const fileName = application.coverLetterPath.split('/').pop();
              folder.file(fileName, blob);
              successCount++;
            })
            .catch(error => {
              console.error(`Error downloading cover letter for ${application.name}:`, error);
              failureCount++;
              // Continue with other files even if one fails
            });

          downloadPromises.push(coverLetterPromise);
        }
      }

      // Check if any files were added
      if (downloadPromises.length === 0) {
        toast.update(loadingToastId, {
          render: "No valid files found to download",
          type: "error",
          isLoading: false,
          autoClose: 3000
        });
        return;
      }

      // Wait for all downloads to complete
      await Promise.all(downloadPromises);

      // Generate the zip file
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      // Validate job title
      const safeJobTitle = (jobTitle && typeof jobTitle === 'string')
        ? jobTitle.replace(/[^a-z0-9]/gi, '-').toLowerCase()
        : 'job-applications';

      // Create a meaningful filename with job title and date
      const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const filename = `${safeJobTitle}-applications-${date}.zip`;

      // Save the zip file
      saveAs(zipBlob, filename);

      // Update toast with success/failure counts
      toast.update(loadingToastId, {
        render: `Downloaded ${successCount} files successfully${failureCount > 0 ? ` (${failureCount} failed)` : ''}`,
        type: failureCount > 0 ? "warning" : "success",
        isLoading: false,
        autoClose: 3000
      });
    } catch (error) {
      console.error("Error creating zip file:", error);
      toast.error("Failed to download files. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !applications.length) { // Show error only if no applications are loaded
    return (
      <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
        <AlertCircle className="h-5 w-5 mr-2" />
        {error}
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="flex items-center mb-6">
        <Link href="/admin/jobs" passHref>
          <button className="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg shadow-md hover:bg-gray-300 transition-colors duration-200 flex items-center mr-4">
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Job Listings
          </button>
        </Link>
      </div>

      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-800">Applications for {jobTitle}</h2>
        {applications.length > 0 && (
          <button
            onClick={downloadAllFiles}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg shadow-md hover:bg-blue-700 transition-colors duration-200 flex items-center"
          >
            <Archive className="h-5 w-5 mr-2" />
            Download All Files
          </button>
        )}
      </div>

      {error && ( // Show error message if there is one, even if applications are loaded
        <div className="bg-red-100 text-red-700 p-4 rounded-lg flex items-center mb-4 border border-red-200">
          <AlertCircle className="h-5 w-5 mr-3 text-red-500" />
          <span className="text-sm font-medium">{error}</span>
        </div>
      )}

      {applications.length === 0 ? (
        <p className="text-gray-600">No applications yet for this job.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 shadow-lg rounded-xl overflow-hidden">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resume</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cover Letter</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied At</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interview</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {applications.map(application => (
                <tr key={application._id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{application.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center"><Mail className="h-4 w-4 mr-1 text-gray-500" /> {application.email}</div>
                    {application.phone && (
                      <div className="text-sm text-gray-500 flex items-center mt-1"><Phone className="h-4 w-4 mr-1 text-gray-500" /> {application.phone}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <select
                      value={application.status}
                      onChange={(e) => updateApplicationStatus(application._id, e.target.value)}
                      className={`text-sm border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500
                        ${application.status === 'pending' ? 'bg-yellow-100 text-yellow-800 border-yellow-300' :
                          application.status === 'reviewed' ? 'bg-blue-100 text-blue-800 border-blue-300' :
                          application.status === 'interviewed' ? 'bg-purple-100 text-purple-800 border-purple-300' :
                          application.status === 'hired' ? 'bg-green-100 text-green-800 border-green-300' :
                          'bg-red-100 text-red-800 border-red-300'}`}
                    >
                      <option value="pending">Pending</option>
                      <option value="reviewed">Reviewed</option>
                      <option value="interviewed">Interviewed</option>
                      <option value="hired">Hired</option>
                      <option value="rejected">Rejected</option>
                    </select>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {application.resumePath ? (
                      <button
                        onClick={() => handleDownloadResume(application)}
                        className="text-blue-600 hover:text-blue-800 flex items-center font-medium transition-colors duration-150"
                      >
                        <Download className="h-4 w-4 mr-1" /> Download
                      </button>
                    ) : (
                      'N/A'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {application.coverLetterPath ? (
                      <button
                        onClick={() => handleDownloadCoverLetter(application)}
                        className="text-green-600 hover:text-green-800 flex items-center font-medium transition-colors duration-150"
                      >
                        <Download className="h-4 w-4 mr-1" /> Download
                      </button>
                    ) : (
                      'N/A'
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {application.appliedAt ? new Date(application.appliedAt).toLocaleDateString() : 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => openInterviewModal(application)}
                      className="text-purple-600 hover:text-purple-800 flex items-center font-medium transition-colors duration-150"
                    >
                      <Calendar className="h-4 w-4 mr-1" /> Schedule Interview
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Interview Calendar */}
      <InterviewCalendar
        isOpen={showCalendar}
        onClose={closeInterviewModal}
        selectedApplication={selectedApplication}
        admins={admins}
        onScheduleInterview={handleScheduleInterview}
        existingInterviews={existingInterviews}
        onViewInterview={viewInterviewDetails}
        onEditInterview={openEditInterview}
        onDeleteInterview={deleteInterview}
      />

      {/* Interview Details Modal */}
      {showInterviewDetails && selectedInterview && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Interview Details</h3>
                <button
                  onClick={closeInterviewModals}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Candidate</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedInterview.candidateName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedInterview.candidateEmail}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Scheduled Date & Time</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedInterview.scheduledAt).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Duration</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedInterview.duration} minutes</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedInterview.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                      selectedInterview.status === 'completed' ? 'bg-green-100 text-green-800' :
                      selectedInterview.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {selectedInterview.status}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Interviewer</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedInterview.interviewer?.firstName + ' ' + selectedInterview.interviewer?.lastName || 'Unknown'}
                    </p>
                  </div>
                </div>

                {selectedInterview.meetLink && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Meeting Link</label>
                    <a
                      href={selectedInterview.meetLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-1 text-sm text-blue-600 hover:text-blue-800 underline"
                    >
                      Join Google Meet
                    </a>
                  </div>
                )}

                {selectedInterview.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{selectedInterview.notes}</p>
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={() => deleteInterview(selectedInterview._id)}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
                <button
                  onClick={() => {
                    closeInterviewModals();
                    openEditInterview(selectedInterview);
                  }}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                  Edit
                </button>
                <button
                  onClick={closeInterviewModals}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Interview Modal */}
      {showEditInterview && selectedInterview && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Edit Interview</h3>
                <button
                  onClick={closeInterviewModals}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-800 mb-2">Candidate Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Name:</span> {selectedInterview.candidateName}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {selectedInterview.candidateEmail}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {/* Date and Time */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Scheduled Date & Time *
                  </label>
                  <input
                    type="datetime-local"
                    value={editInterviewData.scheduledAt}
                    onChange={(e) => handleEditInterviewChange('scheduledAt', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Duration */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Duration (minutes)
                  </label>
                  <select
                    value={editInterviewData.duration}
                    onChange={(e) => handleEditInterviewChange('duration', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={30}>30 minutes</option>
                    <option value={45}>45 minutes</option>
                    <option value={60}>1 hour</option>
                    <option value={90}>1.5 hours</option>
                    <option value={120}>2 hours</option>
                  </select>
                </div>

                {/* Interviewer */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Interviewer *
                  </label>
                  <select
                    value={editInterviewData.interviewerId}
                    onChange={(e) => handleEditInterviewChange('interviewerId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select an interviewer</option>
                    {admins.map(admin => (
                      <option key={admin._id} value={admin._id}>
                        {admin.name} ({admin.email})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={editInterviewData.status}
                    onChange={(e) => handleEditInterviewChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="scheduled">Scheduled</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="rescheduled">Rescheduled</option>
                  </select>
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    value={editInterviewData.notes}
                    onChange={(e) => handleEditInterviewChange('notes', e.target.value)}
                    rows={3}
                    placeholder="Add any additional notes for the interview..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  onClick={closeInterviewModals}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={updateInterview}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                >
                  Update Interview
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}

export default JobApplicationsPage;