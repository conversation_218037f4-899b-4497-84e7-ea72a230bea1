"use client";

import { useState } from "react";

export default function Search({ posts }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedTag, setSelectedTag] = useState('');

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
    };

    const handleTagFilter = (e) => {
        setSelectedTag(e.target.value);
    };

    const filteredPosts = posts.filter((post) => {
        const matchesSearchTerm = post.title.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesTag = selectedTag ? post.tags.some((tag) => tag.name === selectedTag) : true;
        return matchesSearchTerm && matchesTag;
    });

    return (
        <div className="flex justify-between items-center mb-6">
            <div className="w-full sm:w-1/2 md:w-1/3">
                <input
                    type="text"
                    placeholder="Search by post name..."
                    className="w-full px-4 py-2 rounded-md border border-gray-300"
                    value={searchTerm}
                    onChange={handleSearch}
                />
            </div>
            <div className="w-full sm:w-1/4 md:w-1/4 ml-4">
                <select
                    className="w-full px-4 py-2 rounded-md border border-gray-300"
                    value={selectedTag}
                    onChange={handleTagFilter}
                >
                    <option value="">Filter by Tag</option>
                    <option value="Technology">Technology</option>
                    <option value="Lifestyle">Lifestyle</option>
                    <option value="Business">Business</option>
                </select>
            </div>
        </div>
    );
}
