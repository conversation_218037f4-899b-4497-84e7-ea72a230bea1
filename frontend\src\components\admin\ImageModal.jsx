import { Dialog, DialogBody } from "@material-tailwind/react";
import { XCircle } from "lucide-react";

const ImageModal = ({ isOpen, imageSrc, onClose }) => {
    const handleClose = (e) => {
      e.preventDefault(); // Prevent default behavior (e.g., form submission)
      e.stopPropagation(); // Stop event bubbling
      onClose(); // Close the modal
    };
  
    return (
      <Dialog
        open={isOpen}
        handler={() => {}} // Empty handler to prevent default behavior; we control it manually
        className="p-4 bg-black bg-opacity-50" // Darker overlay
        dismiss={{ outsidePress: true, escapeKey: true }} // Close on outside click or ESC
        onClose={handleClose} // Ensure close is triggered
      >
        <DialogBody className="p-0 flex justify-center items-center">
          {imageSrc && (
            <div className="relative max-w-[90vw] max-h-[90vh]">
              <img
                src={imageSrc}
                alt="Zoomed Image"
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
              />
              <button
                onClick={handleClose} // Direct close handler
                className="absolute top-2 right-2 p-2 bg-gray-800 bg-opacity-50 rounded-full text-white hover:bg-opacity-75 transition-opacity focus:outline-none"
              >
                <XCircle size={24} />
              </button>
            </div>
          )}
        </DialogBody>
      </Dialog>
    );
  };

  export default ImageModal;