"use client";

import { useEffect, useState } from "react";

export default function TypingAnimation({ title }) {
  const phrases = title.split(",");
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [currentText, setCurrentText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const typeText = () => {
      const currentPhrase = phrases[currentPhraseIndex];
      if (!isDeleting) {
        // Typing animation
        if (currentText.length < currentPhrase.length) {
          setCurrentText(currentPhrase.slice(0, currentText.length + 1));
        } else {
          // Pause before deleting
          setTimeout(() => setIsDeleting(true), 1000);
        }
      } else {
        // Deleting animation
        if (currentText.length > 0) {
          setCurrentText(currentPhrase.slice(0, currentText.length - 1));
        } else {
          // Move to the next phrase
          setIsDeleting(false);
          setCurrentPhraseIndex(
            (prevIndex) => (prevIndex + 1) % phrases.length
          );
        }
      }
    };

    const typingSpeed = isDeleting ? 50 : 150; // Adjust speed for typing and deleting
    const timeout = setTimeout(typeText, typingSpeed);

    return () => clearTimeout(timeout);
  }, [currentText, isDeleting, currentPhraseIndex, phrases]);

  return (
    <h2 className="text-3xl 2xl:text-4xl font-bold text-white">
      {currentText}
      <span className="text-blue-600">{phrases.pop()}</span>
      <span className="animate-blink">|</span> {/* Cursor effect */}
    </h2>
  );
}
