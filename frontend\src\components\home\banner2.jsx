"use client";
import Image from "next/image";
import Link from "next/link";
import { motion, useScroll, useTransform } from "framer-motion";
import { Shield, Server, Cloud, Cpu, Globe, Star } from "lucide-react";
import homeHeroImage from "/public/images/home/<USER>";
import { useIsMobile } from "@/app/hook/useIsMobile";
export const bannerContent = {
  title: "Professional Web Hosting Solutions for Your Business",
  subtitle: "Reliable, Secure, and Lightning-Fast Hosting Infrastructure",
  features: [
    {
      icon: "Shield",
      text: "99.9% Uptime Guarantee",
      description: "Enterprise-grade reliability for your business",
    },
    {
      icon: "Globe",
      text: "Global CDN Network",
      description: "Lightning-fast content delivery worldwide",
    },
    {
      icon: "Cpu",
      text: "High-Performance Servers",
      description: "State-of-the-art hardware for optimal speed",
    },
  ],
  pricing: {
    amount: 2.99,
    period: "mo",
    promotion: "3 months free hosting",
    guarantee: "30-day money-back guarantee",
  },
  cta: {
    primary: "Start Your Project",
    secondary: "View Plans",
  },
};
function Banner2({ t }) {
  const isMobile = useIsMobile();
  const { scrollY } = useScroll();
  const backgroundY = useTransform(scrollY, [0, 500], [0, 150]);

  const features = bannerContent.features;

  return (
    <section
      className="relative min-h-[70vh] bg-primary overflow-hidden"
      aria-label="Web Hosting Solutions Banner"
    >
      {/* Animated Background */}
      <motion.div
        className="absolute inset-0 opacity-10"
        style={{ y: backgroundY }}
      >
        <div className="grid grid-cols-6 gap-8 p-8">
          {[Server, Cloud, Cpu, Globe, Shield].map((Icon, index) => (
            <Icon
              key={index}
              className="w-12 h-12 text-white transform rotate-12 opacity-20"
            />
          ))}
        </div>
      </motion.div>

      <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        {/* Promotion Banner */}
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-center mb-8"
        >
          <div className="hidden items-center gap-2 bg-white px-6 py-3 rounded-full backdrop-blur-sm">
            <span className="animate-pulse text-xl">⚡</span>
            <span className="text-white font-medium">
              Limited Time Offer: {bannerContent.pricing.promotion}
            </span>
            <div className="flex items-center gap-1 ml-2">
              {[1, 2, 3, 4, 5].map((_, i) => (
                <Star
                  key={i}
                  className="w-4 h-4 text-yellow-400 fill-yellow-400"
                />
              ))}
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content Column */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="text-white"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
              {bannerContent.title}
              <span className="block mt-2 text-2xl md:text-3xl text-blue-300 font-normal">
                {bannerContent.subtitle}
              </span>
            </h1>

            <div className="mt-6 space-y-4">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.2 }}
                  className="flex items-center gap-3"
                >
                  <feature.icon className="h-6 w-6 text-blue-400" />
                  <span className="text-lg">{feature.text}</span>
                </motion.div>
              ))}
            </div>

            <div className="mt-8">
              <div className="price-tag mb-4">
                <span className="price-tag-currency">$</span>
                <span className="price-tag-amount">
                  {bannerContent.pricing.amount}
                </span>
                <span className="price-tag-period">
                  /{bannerContent.pricing.period}
                </span>
              </div>
              {/* <p className="text-blue-300">
                + {bannerContent.pricing.promotion}
              </p> */}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-8 flex flex-wrap gap-4"
            >
              <Link
                href="#contact-nous"
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-3 transition-all duration-300 shadow-lg hover:shadow-blue-500/25"
              >
                {bannerContent.cta.primary}
                <Server className="h-5 w-5" />
              </Link>
              <div className="flex items-center gap-2 text-blue-200">
                <Shield className="h-5 w-5" />
                <span>{bannerContent.pricing.guarantee}</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Image Column with Enhanced Animation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="relative"
          >
            <motion.div
              animate={{
                y: [-10, 10, -10],
                rotate: [-1, 1, -1],
              }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="relative z-10"
            >
              <Image
                src={homeHeroImage}
                alt={bannerContent.title}
                width={900}
                height={900}
                className="rounded-2xl shadow-2xl"
                priority
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/40 via-black/20 to-transparent" />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

export default Banner2;
