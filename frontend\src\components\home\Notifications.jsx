"use client"

import { useState, useEffect, useRef } from "react";
import { Bell, Check, Clock, Info, AlertCircle, Loader2, Edit, Trash, ShoppingBag, Shield, MessageSquare, Star, Server, Code } from "lucide-react";
import { useRouter } from 'next/navigation';
import { useAuth } from "../../app/context/AuthContext"; // Assuming path
import { adminService } from "../../app/services/adminService"; // Assuming path
import io from 'socket.io-client';
import { formatDistanceToNow } from 'date-fns';
import { isProd, BACKEND_URL } from "../../app/config/constant";

const Notifications = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const notificationRef = useRef(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const router = useRouter();
  const { user } = useAuth();
  const socket = useRef(null);

  const timeAgo = (dateString) => {
    if (!dateString) return '';
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (e) {
      console.error("Error formatting date:", e);
      return dateString; // fallback to original string
    }
  };

  // We've removed the debug functions since they're no longer needed in production
  // If you need to debug in the future, you can add them back

  // State to track socket connection status
  const [socketStatus, setSocketStatus] = useState({
    connected: false,
    joinedAdminRoom: false,
    error: null
  });

  useEffect(() => {
    setIsMounted(true);
    console.log('[SOCKET DEBUG-FRONTEND] Component mounted, user:', user ? 'available' : 'not available');
    console.log('[SOCKET DEBUG-FRONTEND] Socket URL:', process.env.NEXT_PUBLIC_SOCKET_URL || 'not available');

    // Only proceed if we have a user
    if (user) {
      console.log('[SOCKET DEBUG-FRONTEND] Initializing socket connection (automatic in all environments)');

      // Ensure we don't have an existing connection
      if (socket.current) {
        console.log('[SOCKET DEBUG-FRONTEND] Cleaning up existing socket connection');
        socket.current.disconnect();
        socket.current = null;
      }

      // Use the BACKEND_URL from our constants file for socket connection
      // This ensures we connect to the correct backend in both dev and prod environments
      const socketUrl = BACKEND_URL;
      console.log('[SOCKET DEBUG-FRONTEND] Using socket URL for auto-connection:', socketUrl);

      // Create new socket connection with explicit options
      socket.current = io(socketUrl, {
        query: { adminId: user._id }, // Pass adminId for specific room or identification
        transports: ['websocket', 'polling'], // Try both transports like in the test page
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 30000, // Increase timeout
        withCredentials: true, // Match the test page configuration
        forceNew: true, // Force a new connection
        autoConnect: true, // Ensure auto-connect is enabled
        debug: !isProd // Enable debug mode in development
      });

      // Log connection attempt
      console.log(`[SOCKET DEBUG-FRONTEND] Attempting to auto-connect to ${socketUrl} with transports:`, ['websocket', 'polling']);

      socket.current.on('connect', () => {
        console.log('[SOCKET DEBUG-FRONTEND] Socket connected for notifications');
        console.log('[SOCKET DEBUG-FRONTEND] Socket ID:', socket.current.id);
        console.log('[SOCKET DEBUG-FRONTEND] Connected with user ID:', user._id);

        // Update socket status
        setSocketStatus(prev => ({ ...prev, connected: true, error: null }));

        // Add a small delay before joining the admin room (ensures connection is fully established)
        setTimeout(() => {
          // Join admin room
          socket.current.emit('join-admin');
          console.log('[SOCKET DEBUG-FRONTEND] Emitted join-admin event');

          // Test notification reception
          socket.current.on('notification', (notification) => {
            console.log('[SOCKET DEBUG-FRONTEND] NOTIFICATION RECEIVED:', notification);
            console.log('[SOCKET DEBUG-FRONTEND] Notification type:', notification.type);
            console.log('[SOCKET DEBUG-FRONTEND] Notification title:', notification.title);
            console.log('[SOCKET DEBUG-FRONTEND] Notification message:', notification.message);
          });
        }, 500);
      });

      // Listen for 'notification' event from the backend
      socket.current.on('notification', (newNotification) => {
        console.log('[SOCKET DEBUG-FRONTEND] New notification received via socket:');
        console.log('[SOCKET DEBUG-FRONTEND] Notification type:', newNotification.type);
        console.log('[SOCKET DEBUG-FRONTEND] Notification title:', newNotification.title);
        console.log('[SOCKET DEBUG-FRONTEND] Notification message:', newNotification.message);
        console.log('[SOCKET DEBUG-FRONTEND] Notification ID:', newNotification._id);

        if (process.env.NODE_ENV === 'development') {
          console.log('[SOCKET DEBUG-FRONTEND] Full notification data:', newNotification);
        }

        // Add to the beginning of the list to show newest first
        setNotifications(prevNotifications => {
          console.log('[SOCKET DEBUG-FRONTEND] Adding notification to state, current count:', prevNotifications.length);
          return [newNotification, ...prevNotifications].slice(0, 20); // Keep a reasonable limit, e.g., 20
        });

        if (!newNotification.isRead) {
          setUnreadCount(prevCount => {
            console.log('[SOCKET DEBUG-FRONTEND] Incrementing unread count from', prevCount, 'to', prevCount + 1);
            return prevCount + 1;
          });
        }
      });

      // Fetch initial notifications and unread count on mount
      const fetchInitialNotifs = async () => {
        setLoading(true);
        setError(null);
        try {
          const response = await adminService.getAdminNotifications({ page: 1, limit: 20, unreadOnly: false });
          console.log('[SOCKET DEBUG-FRONTEND] Initial notifications fetched:', response.data.notifications?.length || 0);
          setNotifications(response.data.notifications || []);
          setUnreadCount(response.data.unreadCount || 0);
        } catch (err) {
          console.error("[SOCKET DEBUG-FRONTEND] Failed to fetch initial notifications:", err);
          setError("Failed to load notifications.");
          setNotifications([]); // Clear notifications on error
          setUnreadCount(0);
        } finally {
          setLoading(false);
        }
      };

      fetchInitialNotifs();

      socket.current.on('disconnect', (reason) => {
        console.log('[SOCKET DEBUG-FRONTEND] Socket disconnected, reason:', reason);
        setSocketStatus(prev => ({ ...prev, connected: false, joinedAdminRoom: false }));
      });

      socket.current.on('connect_error', (err) => {
        console.error('[SOCKET DEBUG-FRONTEND] Socket connection error:', err.message);
        console.error('[SOCKET DEBUG-FRONTEND] Socket URL that failed:', socketUrl);
        console.error('[SOCKET DEBUG-FRONTEND] Error details:', err);

        // Log environment information to help with debugging
        console.log('[SOCKET DEBUG-FRONTEND] Environment:', isProd ? 'Production' : 'Development');
        console.log('[SOCKET DEBUG-FRONTEND] BACKEND_URL:', BACKEND_URL);

        setSocketStatus(prev => ({ ...prev, connected: false, error: err.message }));

        // Attempt to reconnect after a delay
        setTimeout(() => {
          console.log('[SOCKET DEBUG-FRONTEND] Attempting to reconnect...');
          if (socket.current) {
            socket.current.connect();
          }
        }, 5000); // Try to reconnect after 5 seconds
      });

      socket.current.on('error', (err) => {
        console.error('[SOCKET DEBUG-FRONTEND] Socket error:', err);
        setSocketStatus(prev => ({ ...prev, error: err.message }));
      });

      // Listen for admin room join confirmation
      socket.current.on('admin-room-joined', (data) => {
        console.log('[SOCKET DEBUG-FRONTEND] Received admin-room-joined confirmation:', data);
        if (data.success) {
          console.log(`[SOCKET DEBUG-FRONTEND] Successfully joined admin room. Room size: ${data.roomSize}`);
          setSocketStatus(prev => ({ ...prev, joinedAdminRoom: true }));
        }
      });

      // Log reconnection attempts
      socket.current.io.on('reconnect_attempt', (attempt) => {
        console.log('[SOCKET DEBUG-FRONTEND] Socket reconnection attempt:', attempt);
      });

      socket.current.io.on('reconnect', (attempt) => {
        console.log('[SOCKET DEBUG-FRONTEND] Socket reconnected after', attempt, 'attempts');
        setSocketStatus(prev => ({ ...prev, connected: true }));
      });
    } else {
      console.log('[SOCKET DEBUG-FRONTEND] User not available, skipping socket connection');
    }

    return () => {
      if (socket.current) {
        console.log('[SOCKET DEBUG-FRONTEND] Cleaning up socket on component unmount');
        socket.current.disconnect();
      }
    };
  }, [user]);



  useEffect(() => {
    if (!isMounted) return;

    const handleClickOutside = (event) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, isMounted]);

  const toggleNotifications = () => {
    setIsOpen(!isOpen);
  };

  const handleMarkAsRead = async (notificationId) => {
    try {
      await adminService.markNotificationAsRead(notificationId);
      setNotifications(prev => prev.map(n => n._id === notificationId ? { ...n, isRead: true } : n));
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error("Failed to mark notification as read:", err);
      // Optionally show a toast error
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      setLoading(true);
      const response = await adminService.markAllNotificationsAsRead();

      // Update local state to mark all notifications as read
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);

      console.log('[NOTIFICATIONS] Successfully marked all notifications as read:', response);
    } catch (err) {
      console.error("[NOTIFICATIONS] Failed to mark all notifications as read:", err);
      // Optionally show a toast error
    } finally {
      setLoading(false);
    }
  };

  // Function to manually reconnect the socket
  const handleReconnect = () => {
    if (!socket.current) {
      return;
    }

    console.log('[SOCKET DEBUG-FRONTEND] Manual reconnection attempt');

    // First disconnect if already connected
    if (socket.current.connected) {
      socket.current.disconnect();
    }

    // Then try to reconnect
    socket.current.connect();

    // Update UI to show connecting state
    setSocketStatus(prev => ({ ...prev, connected: false, error: null }));
  };

  // We've removed the debug functions since they're no longer needed in production
  // If you need to debug in the future, you can add them back

  const handleNotificationClick = (notification) => {
    if (!notification.isRead) {
      handleMarkAsRead(notification._id);
    }

    console.log('🔔 [ADMIN] Notification clicked!');
    console.log('📋 [ADMIN] Notification type:', notification.type);
    console.log('🔗 [ADMIN] Notification link:', notification.link);
    console.log('📄 [ADMIN] Full notification object:', notification);

    // Handle order-related notifications (order_update, ssl_update, hosting_update, webdev_update)
    if (notification.type === 'order_update' ||
        notification.type === 'ssl_update' ||
        notification.type === 'hosting_update' ||
        notification.type === 'webdev_update') {

      // Check if notification has a link property (preferred way)
      if (notification.link) {
        console.log('✅ [ADMIN] Using notification link:', notification.link);
        router.push(notification.link);
      }
      // As a fallback, try to extract order ID from the notification message
      // This is a common pattern: "Order #12345 has been updated"
      else {
        console.log('⚠️ [ADMIN] No link found, trying to extract order ID from message');
        const orderIdMatch = notification.message.match(/Order #([\w\d]+)/i);
        if (orderIdMatch && orderIdMatch[1]) {
          const orderId = orderIdMatch[1];
          const fallbackUrl = `/admin/orders/${orderId}`;
          console.log('✅ [ADMIN] Using extracted order ID:', fallbackUrl);
          router.push(fallbackUrl);
        } else {
          console.warn('❌ [ADMIN] Could not extract order ID from notification:', notification);
          // Navigate to orders list as fallback
          router.push('/admin/orders');
        }
      }
    }
    // Handle ticket-related notification types
    else if (notification.type === 'new_ticket' || notification.type === 'ticket_updated' || 
             notification.type === 'ticket_status_update') {
      // If notification has a specific ticket ID, navigate to the ticket section
      
        router.push('/admin/support');
      
    } 
    else if (notification.type === 'user_registered' || notification.type === 'user_verified') {
      if (notification.link) {
        router.push(notification.link);
      } else {
        router.push('/admin/users');
      }
    }
    // Generic fallback: if notification has a link, use it
    else if (notification.link) {
      console.log('🔗 [ADMIN] Using generic link fallback:', notification.link);
      router.push(notification.link);
    }
    // For all other notification types, just close the dropdown
    else {
      console.log('ℹ️ [ADMIN] No specific handler for notification type:', notification.type);
    }

    setIsOpen(false); // Close dropdown after handling the notification
  };

  if (!isMounted) {
    return (
      <div className="relative">
        <button className="relative p-2 rounded-full hover:bg-gray-100">
          <Bell className="w-5 h-5 text-gray-600" />
        </button>
      </div>
    );
  }

  const getNotificationIcon = (type) => {
    // Assuming notification types like 'new_ticket', 'ticket_status_update', 'order_update', etc.
    // You might want to map these types to specific icons and colors.
    switch (type) {
      case 'new_ticket':
        return <Info className="w-4 h-4 text-blue-500" />;
      case 'ticket_status_update':
        return <Check className="w-4 h-4 text-green-500" />;
      case 'ticket_updated':
        return <Edit className="w-4 h-4 text-purple-500" />;
      case 'ticket_deleted':
        return <Trash className="w-4 h-4 text-red-500" />;
      case 'order_update':
        return <ShoppingBag className="w-4 h-4 text-amber-500" />;
      case 'ssl_expiry':
      case 'ssl_update':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'hosting_update':
        return <Server className="w-4 h-4 text-green-500" />;
      case 'webdev_update':
        return <Code className="w-4 h-4 text-purple-500" />;
      case 'abandoned_cart':
        return <ShoppingBag className="w-4 h-4 text-orange-500" />;
      case 'custom_notification':
        return <Star className="w-4 h-4 text-purple-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  // Function to get enhanced notification message with context
  const getEnhancedMessage = (notification) => {
    // For order notifications, add more context
    if (notification.type === 'order_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              By: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // For SSL notifications, add more context
    if (notification.type === 'ssl_expiry' || notification.type === 'ssl_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              By: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // For hosting notifications, add more context
    if (notification.type === 'hosting_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              By: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // For web development notifications, add more context
    if (notification.type === 'webdev_update') {
      return (
        <div>
          <p className="text-xs text-gray-600">{notification.message}</p>
          {notification.actionBy && notification.actionBy.firstName && (
            <p className="text-xs text-gray-500 mt-1">
              By: {notification.actionBy.firstName} {notification.actionBy.lastName}
              {notification.actionBy.role && ` (${notification.actionBy.role})`}
            </p>
          )}
        </div>
      );
    }

    // Default display for other notification types
    return (
      <div>
        <p className="text-xs text-gray-600">{notification.message}</p>
        {notification.actionBy && notification.actionBy.firstName && (
          <p className="text-xs text-gray-500 mt-1">
            By: {notification.actionBy.firstName} {notification.actionBy.lastName}
            {notification.actionBy.role && ` (${notification.actionBy.role})`}
          </p>
        )}
      </div>
    );
  };

  return (
    <div className="relative" ref={notificationRef}>
      <button
        className="relative p-2 rounded-full hover:bg-gray-100 border border-gray-100"
        aria-label="Notifications"
        onClick={toggleNotifications}
      >
        <Bell className="w-5 h-5 text-gray-600 " />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 flex size-2">
          <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75"></span>
          <span className="relative inline-flex size-2 rounded-full bg-red-500"></span>
        </span>
        )}
      </button>

      {/* Notification dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-custom-heavy border border-gray-200 z-50 overflow-hidden">
          <div className="p-3 border-b border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-gray-700">Notifications</h3>
              {unreadCount > 0 && (
                <span className="px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full">
                  {unreadCount} new
                </span>
              )}
            </div>

            {/* Only show reconnect button when there's a connection error */}
            {socketStatus.error && (
              <div className="flex justify-end mt-2 text-xs">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleReconnect();
                  }}
                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                >
                  Reconnect
                </button>
              </div>
            )}

            {/* Show socket status only in development */}
            {!isProd && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="flex items-center gap-2 text-xs">
                  <span>Socket:</span>
                  {socketStatus.connected ? (
                    <span className="text-green-600 flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                      Connected
                    </span>
                  ) : (
                    <span className="text-red-600 flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
                      Disconnected
                    </span>
                  )}
                  {socketStatus.error && (
                    <span className="text-red-500 ml-2">Error: {socketStatus.error}</span>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto hide-scrollbar">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <Loader2 className="w-6 h-6 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="py-8 px-4 text-center text-red-500">
                <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                <p>{error}</p>
              </div>
            ) : notifications.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification) => (
                  <div
                    key={notification._id} // Use _id from MongoDB
                    className={`p-3 hover:bg-gray-50 transition-colors cursor-pointer ${
                      !notification.isRead ? 'bg-blue-50/50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          !notification.isRead ? 'bg-primary/10' : 'bg-gray-100'
                        }`}>
                          {getNotificationIcon(notification.type)}
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-start mb-1">
                          <h4 className={`text-sm font-medium truncate ${
                            !notification.isRead ? 'text-primary' : 'text-gray-800'
                          }`}>
                            {notification.title}
                          </h4>
                          <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                            {timeAgo(notification.createdAt)}
                          </span>
                        </div>
                        {getEnhancedMessage(notification)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-8 px-4 text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3">
                  <Bell className="w-6 h-6 text-gray-400" />
                </div>
                <p className="text-gray-500 mb-1">No notifications yet</p>
                <p className="text-sm text-gray-400">We{"'"}ll notify you when something arrives</p>
              </div>
            )}
          </div>

          <div className="p-2 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <button
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0 || loading}
                className="text-sm text-primary hover:text-blue-700 font-medium transition-colors py-1 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Mark all as read
              </button>
              <button
                onClick={() => { router.push('/admin/notifications'); setIsOpen(false); }}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors py-1"
               >
                View all
              </button>
            </div>

            {/* Socket status indicator is now shown at the top of the dropdown */}
          </div>
        </div>
      )}
    </div>
  );
};

export default Notifications;
