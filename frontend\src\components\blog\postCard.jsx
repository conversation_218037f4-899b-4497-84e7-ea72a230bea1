'use client';

import { ArrowUpRightIcon } from "@heroicons/react/24/solid";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Footer, Typography } from "@material-tailwind/react";
import Image from "next/image";
import Link from "next/link";

function PostCard({ post }) {
    return (
        <Card className="shadow-sm max-w-[400px] flex flex-col hover:shadow-lg transition-all duration-300 rounded-lg border border-gray-300 bg-white overflow-hidden">
            {post.image && (
                <div className="relative p-2 pb-0">
                    <Link
                        href={`/blog/${post.slug}`}
                    >
                        <Image
                            // src={"/images/blog/" + post.image}`
                            src={post.image.startsWith("https://") ? post.image : "/images/blog/" + post.image}
                            alt={post.title}
                            width={1000}
                            height={240}
                            className="h-56 w-full object-cover rounded-lg"
                        />
                    </Link>
                </div>
            )}

            <CardBody className="px-4 flex flex-col gap-y-4 flex-grow">
                {/* Tags */}
                <div className="flex flex-wrap items-center gap-2">
                    <p className="text-xs">Mots clés: </p>
                    {post.tags?.map((tag) => (
                        <span
                            key={tag.id}
                            className={`px-3 py-0.5 text-xs font-medium bg-secondary bg-opacity-30  rounded-full capitalize`}
                            style={{ color: tag.color }}
                        >
                            {tag.name}
                        </span>
                    ))}
                </div>

                {/* Title */}
                <Typography
                    variant="h2"
                    className="text-xl font-bold text-gray-800 capitalize">
                    {post.title}
                </Typography>

                {/* Description */}
                <p className="text-gray-600 text-sm leading-relaxed">
                    {post.description && typeof post.description === "string" ?
                        (post.description.slice(0, 150) + (post.description.length > 150 ? " ..." : ""))
                        : ""}
                </p>
                {/* <div className="flex-grow"></div> */}
            </CardBody>

            <CardFooter className="border-t border-gray-200 p-4 flex justify-between items-center">
                <span className="text-gray-500 text-xs">
                    Publié le {post.publishedAt}
                </span>
                <Link
                    href={`/blog/${post.slug}`}

                    className="text-blue-600 flex flex-row gap-x-2 hover:underline items-center justify-center">
                    <span className="uppercase text-xs my-auto">Voir Plus</span>
                    <ArrowUpRightIcon className='w-4 my-auto' />
                </Link>
            </CardFooter>
        </Card>
    );
}

export default PostCard;
