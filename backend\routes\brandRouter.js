const express = require('express');
const { createBrand, getBrand, updateBrand, deleteBrand, getAllBrands } = require('../controllers/brandController');
const brandRouter = express.Router();

//This is the active routes for now
brandRouter.get('/get-brands', getAllBrands);

// All non-active routes
brandRouter.post('/create-brand', createBrand);
brandRouter.get('/get-brand/:id', getBrand);
brandRouter.put('/update-brand/:id', updateBrand);
brandRouter.delete('/delete-brand/:id', deleteBrand);

module.exports = brandRouter;
