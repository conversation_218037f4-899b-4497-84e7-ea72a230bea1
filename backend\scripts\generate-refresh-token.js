const { google } = require('googleapis');
const readline = require('readline');
const path = require('path');

// Load .env from project root (go up two levels from backend/scripts)
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });


// Script to generate Google Calendar refresh token
async function generateRefreshToken() {
  console.log('🔑 Google Calendar Refresh Token Generator\n');

  // Check if credentials are set
  if (!process.env.GOOGLE_CALENDAR_CLIENT_ID || !process.env.GOOGLE_CALENDAR_CLIENT_SECRET) {
    console.log('❌ Missing Google Calendar credentials in .env file');
    console.log('Please add the following to your .env file:');
    console.log('GOOGLE_CALENDAR_CLIENT_ID=your_client_id');
    console.log('GOOGLE_CALENDAR_CLIENT_SECRET=your_client_secret');
    console.log('GOOGLE_CALENDAR_REDIRECT_URI=http://localhost:5002/auth/google/calendar/callback');
    return;
  }

  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CALENDAR_CLIENT_ID,
    process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
    process.env.GOOGLE_CALENDAR_REDIRECT_URI || 'http://localhost:5002/auth/google/calendar/callback'
  );

  // Generate the URL for authorization
  const scopes = [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events'
  ];

  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent' // Force consent screen to get refresh token
  });

  console.log('📋 Step 1: Open this URL in your browser:');
  console.log('🔗', authUrl);
  console.log('\n📋 Step 2: Complete the authorization process');
  console.log('📋 Step 3: Copy the authorization code from the callback URL');
  console.log('📋 Step 4: Paste the code below\n');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('Enter the authorization code: ', async (code) => {
    try {
      console.log('\n🔄 Exchanging authorization code for tokens...');
      
      const { tokens } = await oauth2Client.getToken(code);
      
      console.log('✅ Tokens received successfully!');
      console.log('\n📋 Add these to your .env file:');
      console.log('GOOGLE_CALENDAR_CLIENT_ID=' + process.env.GOOGLE_CALENDAR_CLIENT_ID);
      console.log('GOOGLE_CALENDAR_CLIENT_SECRET=' + process.env.GOOGLE_CALENDAR_CLIENT_SECRET);
      console.log('GOOGLE_CALENDAR_REDIRECT_URI=' + (process.env.GOOGLE_CALENDAR_REDIRECT_URI || 'http://localhost:5002/auth/google/calendar/callback'));
      console.log('GOOGLE_CALENDAR_REFRESH_TOKEN=' + tokens.refresh_token);
      
      if (tokens.access_token) {
        console.log('\n🧪 Testing the tokens...');
        
        oauth2Client.setCredentials(tokens);
        const calendar = google.calendar({ version: 'v3', auth: oauth2Client });
        
        const calendarList = await calendar.calendarList.list();
        console.log('✅ Successfully connected to Google Calendar');
        console.log(`📅 Found ${calendarList.data.items.length} calendars`);
        
        console.log('\n🎉 Setup complete! Your Google Calendar integration is ready.');
      }
      
    } catch (error) {
      console.error('❌ Error exchanging code for tokens:', error.message);
      
      if (error.message.includes('invalid_grant')) {
        console.log('\n💡 Troubleshooting:');
        console.log('- Make sure you copied the entire authorization code');
        console.log('- The code might have expired, try generating a new one');
        console.log('- Ensure your redirect URI matches exactly');
      }
    }
    
    rl.close();
  });
}

// Instructions
console.log('🚀 Google Calendar Setup Instructions:\n');
console.log('1. Go to Google Cloud Console (https://console.cloud.google.com/)');
console.log('2. Create a new project or select existing one');
console.log('3. Enable Google Calendar API');
console.log('4. Create OAuth 2.0 credentials (Web application)');
console.log('5. Add redirect URI: http://localhost:5002/auth/google/calendar/callback');
console.log('6. Download the credentials and add to .env file');
console.log('7. Run this script to generate refresh token\n');

generateRefreshToken();
