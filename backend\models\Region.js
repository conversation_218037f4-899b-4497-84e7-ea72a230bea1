const mongoose = require('mongoose');

// Modèle simplifié pour les prix des régions par package
const regionPriceSchema = new mongoose.Schema({
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package',
    required: true
  },
  regionId: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  price: {
    type: Number,
    required: true,
    min: 0,
    default: 0 // Prix supplémentaire en euros
  }
}, {
  timestamps: true
});

// Index composé pour éviter les doublons
regionPriceSchema.index({ packageId: 1, regionId: 1 }, { unique: true });

module.exports = mongoose.model('RegionPrice', regionPriceSchema);


