name: Deploy ztechEngineering to the PROD WITH CART Environment

on:
  push:
    # branches: [prodwithcart]

jobs:
  build:
    # runs-on: ["self-hosted", "ztech-nextjs"]
    timeout-minutes: 30 # Increase this value as needed

    steps:
      - name: Get previous commit hash
        id: get_prev_hash
        run: echo "prev_hash=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT

      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 2 # Fetch enough history for diff

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Detect changes in both frontend and backend
      - name: Detect changes
        id: changes
        run: |
          # Show what files changed
          echo "Changed files:"
          git diff --name-only ${{ steps.get_prev_hash.outputs.prev_hash }} HEAD

          # Check if frontend files changed
          if git diff --name-only ${{ steps.get_prev_hash.outputs.prev_hash }} HEAD | grep -q "^frontend/"; then
            echo "frontend_changed=true" >> $GITHUB_OUTPUT
            echo "Frontend files changed"
          else
            echo "frontend_changed=false" >> $GITHUB_OUTPUT
            echo "No frontend files changed"
          fi

          # Check if backend files changed
          if git diff --name-only ${{ steps.get_prev_hash.outputs.prev_hash }} HEAD | grep -q "^backend/"; then
            echo "backend_changed=true" >> $GITHUB_OUTPUT
            echo "Backend files changed"
          else
            echo "backend_changed=false" >> $GITHUB_OUTPUT
            echo "No backend files changed"
          fi

          # Check if package.json files changed
          if git diff --name-only ${{ steps.get_prev_hash.outputs.prev_hash }} HEAD | grep -q "frontend/package.json"; then
            echo "frontend_deps_changed=true" >> $GITHUB_OUTPUT
            echo "Frontend dependencies changed"
          else
            echo "frontend_deps_changed=false" >> $GITHUB_OUTPUT
            echo "No frontend dependency changes"
          fi

          if git diff --name-only ${{ steps.get_prev_hash.outputs.prev_hash }} HEAD | grep -q "backend/package.json"; then
            echo "backend_deps_changed=true" >> $GITHUB_OUTPUT
            echo "Backend dependencies changed"
          else
            echo "backend_deps_changed=false" >> $GITHUB_OUTPUT
            echo "No backend dependency changes"
          fi

      # Add linting check before building - only fails on errors (not warnings)
      - name: Check for linting errors
        if: steps.changes.outputs.frontend_changed == 'true'
        run: |
          cd frontend
          npm ci --prefer-offline
          npm run lint:quiet

      # Frontend deployment - only if frontend files changed
      - name: Process frontend
        # if: steps.changes.outputs.frontend_changed == 'true'
        run: |
          cd frontend

          # Install dependencies only if package.json changed
          # if [[ "${{ steps.changes.outputs.frontend_deps_changed }}" == "true" ]]; then
            echo "Installing frontend dependencies because package.json changed"
            npm ci --prefer-offline  # Faster than npm i, uses package-lock.json
          # else
          #   echo "No changes to frontend/package.json, skipping npm install"
          # fi

          # Build the frontend
          npm run build

      # Backend deployment - only if backend files changed
      - name: Process backend
        if: steps.changes.outputs.backend_changed == 'true'
        run: |
          cd backend

          # Write .env file
          echo "${{ secrets.PROD_ENV_FILE_CONTENT }}" > .env

          # Always install dependencies to ensure all modules are available
          echo "Installing backend dependencies"
          npm ci --prefer-offline

          # Verify node_modules exists and contains expected modules
          echo "Verifying node_modules directory"
          ls -la node_modules
          echo "Number of modules installed: $(ls -1 node_modules | wc -l)"

      # Deploy services - only restart the ones that changed
      - name: Deploy services
        run: |
          echo "Starting deployment process..."

          # Only restart backend if it changed
          if [[ "${{ steps.changes.outputs.backend_changed }}" == "true" ]]; then
            echo "Restarting backend service..."
            cd backend
            echo "Current directory: $(pwd)"
            echo "Checking PM2 status before delete:"
            pm2 list
            echo "Deleting old backend process..."
            pm2 delete ztech-backend || true
            echo "Starting new backend process..."
            pm2 start npm --name ztech-backend -- start -- --port=5002
            echo "Waiting for backend to start..."
            sleep 3  # Short wait to ensure backend is up
            echo "Backend service restarted"
          else
            echo "No backend changes, skipping backend restart"
          fi

          # Only restart frontend if it changed
          if [[ "${{ steps.changes.outputs.frontend_changed }}" == "true" ]]; then
            echo "Restarting frontend service..."
            cd frontend
            echo "Current directory: $(pwd)"
            echo "Checking PM2 status before delete:"
            pm2 list
            echo "Deleting old frontend process..."
            pm2 delete ztech-frontend || true
            echo "Starting new frontend process..."
            pm2 start npm --name ztech-frontend -- start -- --port=3001
            echo "Frontend service restarted"
          else
            echo "No frontend changes, skipping frontend restart"
          fi

          echo "Saving PM2 configuration..."
          pm2 save
          echo "Deployment completed successfully"

      #- name: Restart Nginx
      #  run: sudo service nginx restart
