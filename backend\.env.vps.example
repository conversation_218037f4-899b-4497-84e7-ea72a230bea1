# VPS Provider Configuration
# Copy this file to .env and configure your VPS provider settings

# Default VPS Provider (contabo, digitalocean, vultr, linode)
DEFAULT_VPS_PROVIDER=contabo

# Contabo API Configuration
CONTABO_API_URL=https://api.contabo.com/v1
CONTABO_CLIENT_ID=your_contabo_client_id
CONTABO_CLIENT_SECRET=your_contabo_client_secret
CONTABO_USERNAME=your_contabo_username
CONTABO_PASSWORD=your_contabo_password

# DigitalOcean API Configuration (for future implementation)
# DIGITALOCEAN_API_TOKEN=your_digitalocean_api_token
# DIGITALOCEAN_API_URL=https://api.digitalocean.com/v2

# Vultr API Configuration (for future implementation)
# VULTR_API_KEY=your_vultr_api_key
# VULTR_API_URL=https://api.vultr.com/v2

# Linode API Configuration (for future implementation)
# LINODE_API_TOKEN=your_linode_api_token
# LINODE_API_URL=https://api.linode.com/v4

# VPS Service Configuration
VPS_ORDER_TIMEOUT=300000  # 5 minutes in milliseconds
VPS_PROVISION_TIMEOUT=1800000  # 30 minutes in milliseconds
VPS_MAX_INSTANCES_PER_USER=10
VPS_ENABLE_AUTO_PROVISIONING=true

# Security Configuration
VPS_ENCRYPT_PASSWORDS=true
VPS_PASSWORD_ENCRYPTION_KEY=your_encryption_key_here

# Monitoring Configuration
VPS_ENABLE_MONITORING=true
VPS_STATS_UPDATE_INTERVAL=300000  # 5 minutes in milliseconds

# Backup Configuration
VPS_ENABLE_AUTO_BACKUP=false
VPS_BACKUP_RETENTION_DAYS=7

# Rate Limiting
VPS_RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds
VPS_RATE_LIMIT_MAX_REQUESTS=10

# Webhook Configuration (for provider callbacks)
VPS_WEBHOOK_SECRET=your_webhook_secret_here
VPS_WEBHOOK_TIMEOUT=30000  # 30 seconds in milliseconds
