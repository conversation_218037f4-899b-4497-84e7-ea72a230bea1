'use client'
import React from 'react'
import { Typography } from '@material-tailwind/react'
import Image from 'next/image';
import imgIntro from "/public/images/services/ssl-hero.png";


function SSLIntro({ t }) {
    return (
        <div className="w-full bg-gradient-to-b from-white via-indigo-50 to-white">
            <div className="max-w-[1400px] mx-auto w-full flex flex-col items-center pt-8 pb-0 px-4 text-center">
                <Typography
                    variant='h2'
                    className="text-secondary text-sm font-medium">
                    {t('title')}
                </Typography>
                <Typography
                    variant='h1'
                    className="md:text-4xl text-3xl font-poppins md:max-w-3xl mx-auto font-medium text-primary mt-0 mb-2">
                    {t('subtitle')}
                </Typography>
                <p className="text-secondary text-base font-medium mb-0">
                    {t('description')}
                </p>

                <div className="md:w-2/5 m-auto mt-0 px-12 py-1">
                    {/* <img
                        src="/images/services/ssl-hero.png"
                        alt="infogerance"
                        className='w-full h-full m-auto rounded-2xl'
                    /> */}
                    <Image
                        src={imgIntro}
                        width={900}
                        height={300}
                        sizes="(max-width: 768px) 100vw, 50vw"
                        alt="ssl-hero"
                        className="w-full h-full object-cover rounded-2xl"
                        placeholder='blur'
                        priority
                    />
                </div>

                <p className="text-gray-900 max-w-4xl text-lg mx-auto">
                    {t('details')}
                </p>
            </div>
        </div>
    )
}

export default SSLIntro;