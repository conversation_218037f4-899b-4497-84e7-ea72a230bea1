'use client';

import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, HardDrive, Key, Settings, Info, Trash2, Eye, EyeOff, RefreshCw, Upload } from 'lucide-react';
import vpsService from '../services/vpsService';
import customImageService from '../services/customImageService';

const ReinstallModal = ({ 
  isOpen, 
  onClose, 
  server, 
  onReinstallSuccess 
}) => {
  const [formData, setFormData] = useState({
    imageId: '',
    password: '',
    userData: '',
    sshKeys: [],
    installationType: 'standard', // 'standard' ou 'advanced'
    advancedImageType: 'standard', // 'standard' ou 'custom' (pour l'installation avancée)
    selectedApplication: '', // Application sélectionnée
    adminPassword: '',
    enableRootUser: false,
    publicSshKey: '',
    cloudInitTemplate: '',

    // Custom Image fields
    customImageId: '', // ID of selected custom image
    customImageUrl: '', // URL for creating new custom image
    customImageName: '',
    customImageOsType: 'Linux',
    customImageVersion: '',
    customImageDescription: ''
  });

  const [availableImages, setAvailableImages] = useState([]);
  const [availableApplications, setAvailableApplications] = useState([]);
  const [customImages, setCustomImages] = useState([]);
  const [loadingImages, setLoadingImages] = useState(false);
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [loadingCustomImages, setLoadingCustomImages] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showAdminPassword, setShowAdminPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isGeneratingPassword, setIsGeneratingPassword] = useState(false);
  const [showCustomImageModal, setShowCustomImageModal] = useState(false);
  const [notification, setNotification] = useState(null); // { type: 'success'|'error'|'warning', message: string }

  useEffect(() => {
    if (isOpen) {
      fetchAvailableImages();
      fetchAvailableApplications();
      fetchCustomImages();
      setErrors({});
      setShowPassword(false);
      setShowCustomImageModal(false); // Reset modal state
    }
  }, [isOpen]);

  // Debug log for modal state
  useEffect(() => {
    console.log('🔍 Custom Image Modal State:', showCustomImageModal);
  }, [showCustomImageModal]);

  // Fonction pour afficher les notifications
  const showNotification = (type, message, duration = 5000) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), duration);
  };

  // Scripts Cloud-Init pour les applications Contabo
  const applicationCloudInitScripts = {
    // Webmin
    'webmin': `#cloud-config
# Installation automatique de Webmin
# Replace fqdn.example.com below with the actual hostname of your server
runcmd:
  - 'curl -o /install.sh http://software.virtualmin.com/gpl/scripts/install.sh'
  - 'sh /install.sh -f -n fqdn.example.com'
  - 'rm -f /install.sh'`,

    // Docker
    'docker': `#cloud-config
# Installation automatique de Docker
package_update: true
package_upgrade: true

packages:
  - apt-transport-https
  - ca-certificates
  - curl
  - gnupg
  - lsb-release

runcmd:
  - curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
  - echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
  - apt-get update
  - apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
  - systemctl enable docker
  - systemctl start docker
  - usermod -aG docker ubuntu`,

    // LAMP Stack
    'lamp': `#cloud-config
# Installation automatique LAMP (Linux, Apache, MySQL, PHP)
package_update: true
package_upgrade: true

packages:
  - apache2
  - mysql-server
  - php
  - php-mysql
  - php-curl
  - php-gd
  - php-mbstring
  - php-xml
  - php-zip
  - libapache2-mod-php

runcmd:
  - systemctl enable apache2
  - systemctl enable mysql
  - systemctl start apache2
  - systemctl start mysql
  - a2enmod rewrite
  - systemctl restart apache2
  - mysql_secure_installation --use-default
  - ufw allow 'Apache Full'
  - ufw allow ssh
  - ufw --force enable

write_files:
  - path: /var/www/html/info.php
    content: |
      <?php phpinfo(); ?>
    permissions: '0644'`,

    // LEMP Stack
    'lemp': `#cloud-config
# Installation automatique LEMP (Linux, Nginx, MySQL, PHP)
package_update: true
package_upgrade: true

packages:
  - nginx
  - mysql-server
  - php-fpm
  - php-mysql
  - php-curl
  - php-gd
  - php-mbstring
  - php-xml
  - php-zip

runcmd:
  - systemctl enable nginx
  - systemctl enable mysql
  - systemctl enable php8.1-fpm
  - systemctl start nginx
  - systemctl start mysql
  - systemctl start php8.1-fpm
  - mysql_secure_installation --use-default
  - ufw allow 'Nginx Full'
  - ufw allow ssh
  - ufw --force enable

write_files:
  - path: /var/www/html/info.php
    content: |
      <?php phpinfo(); ?>
    permissions: '0644'
  - path: /etc/nginx/sites-available/default
    content: |
      server {
          listen 80 default_server;
          listen [::]:80 default_server;

          root /var/www/html;
          index index.php index.html index.htm;

          server_name _;

          location / {
              try_files $uri $uri/ =404;
          }

          location ~ \.php$ {
              include snippets/fastcgi-php.conf;
              fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
          }
      }
    permissions: '0644'`,

    // WordPress
    'wordpress': `#cloud-config
# Installation automatique de WordPress avec LEMP
package_update: true
package_upgrade: true

packages:
  - nginx
  - mysql-server
  - php-fpm
  - php-mysql
  - php-curl
  - php-gd
  - php-mbstring
  - php-xml
  - php-zip
  - wget
  - unzip

runcmd:
  - systemctl enable nginx mysql php8.1-fpm
  - systemctl start nginx mysql php8.1-fpm
  - mysql -e "CREATE DATABASE wordpress;"
  - mysql -e "CREATE USER 'wpuser'@'localhost' IDENTIFIED BY 'wppassword';"
  - mysql -e "GRANT ALL PRIVILEGES ON wordpress.* TO 'wpuser'@'localhost';"
  - mysql -e "FLUSH PRIVILEGES;"
  - cd /var/www/html
  - wget https://wordpress.org/latest.tar.gz
  - tar xzf latest.tar.gz
  - mv wordpress/* .
  - rm -rf wordpress latest.tar.gz
  - chown -R www-data:www-data /var/www/html
  - chmod -R 755 /var/www/html`,

    // Node.js
    'nodejs': `#cloud-config
# Installation automatique de Node.js
package_update: true
package_upgrade: true

packages:
  - curl
  - build-essential

runcmd:
  - curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
  - apt-get install -y nodejs
  - npm install -g pm2 yarn
  - npm install -g @vue/cli create-react-app
  - mkdir -p /var/www/nodejs
  - chown ubuntu:ubuntu /var/www/nodejs

write_files:
  - path: /var/www/nodejs/app.js
    content: |
      const express = require('express');
      const app = express();
      const port = 3000;

      app.get('/', (req, res) => {
        res.send('Hello World from Node.js!');
      });

      app.listen(port, () => {
        console.log(\`App listening at http://localhost:\${port}\`);
      });
    permissions: '0644'
    owner: ubuntu:ubuntu`,

    // GitLab
    'gitlab': `#cloud-config
# Installation automatique de GitLab CE
package_update: true
package_upgrade: true

packages:
  - curl
  - openssh-server
  - ca-certificates
  - tzdata
  - perl

runcmd:
  - curl https://packages.gitlab.com/install/repositories/gitlab/gitlab-ce/script.deb.sh | bash
  - apt-get install gitlab-ce
  - gitlab-ctl reconfigure
  - ufw allow http
  - ufw allow https
  - ufw allow ssh
  - ufw --force enable`,

    // Nextcloud
    'nextcloud': `#cloud-config
# Installation automatique de Nextcloud
package_update: true
package_upgrade: true

packages:
  - apache2
  - mysql-server
  - php
  - php-mysql
  - php-curl
  - php-gd
  - php-mbstring
  - php-xml
  - php-zip
  - php-intl
  - libapache2-mod-php
  - wget
  - unzip

runcmd:
  - systemctl enable apache2 mysql
  - systemctl start apache2 mysql
  - mysql -e "CREATE DATABASE nextcloud;"
  - mysql -e "CREATE USER 'ncuser'@'localhost' IDENTIFIED BY 'ncpassword';"
  - mysql -e "GRANT ALL PRIVILEGES ON nextcloud.* TO 'ncuser'@'localhost';"
  - cd /var/www/html
  - wget https://download.nextcloud.com/server/releases/latest.zip
  - unzip latest.zip
  - chown -R www-data:www-data nextcloud
  - a2enmod rewrite
  - systemctl restart apache2`,

    // Prometheus
    'prometheus': `#cloud-config
# Installation automatique de Prometheus
package_update: true
package_upgrade: true

packages:
  - wget

runcmd:
  - useradd --no-create-home --shell /bin/false prometheus
  - mkdir /etc/prometheus /var/lib/prometheus
  - chown prometheus:prometheus /etc/prometheus /var/lib/prometheus
  - cd /tmp
  - wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
  - tar xzf prometheus-2.40.0.linux-amd64.tar.gz
  - cp prometheus-2.40.0.linux-amd64/prometheus /usr/local/bin/
  - cp prometheus-2.40.0.linux-amd64/promtool /usr/local/bin/
  - chown prometheus:prometheus /usr/local/bin/prometheus /usr/local/bin/promtool
  - cp -r prometheus-2.40.0.linux-amd64/consoles /etc/prometheus
  - cp -r prometheus-2.40.0.linux-amd64/console_libraries /etc/prometheus
  - chown -R prometheus:prometheus /etc/prometheus/consoles /etc/prometheus/console_libraries

write_files:
  - path: /etc/prometheus/prometheus.yml
    content: |
      global:
        scrape_interval: 15s

      scrape_configs:
        - job_name: 'prometheus'
          static_configs:
            - targets: ['localhost:9090']
    permissions: '0644'
    owner: prometheus:prometheus`
  };

  // Fonction pour générer le script Cloud-Init basé sur l'application sélectionnée
  const generateCloudInitScript = (applicationId) => {
    if (!applicationId) {
      return '';
    }

    // Chercher l'application sélectionnée
    const selectedApp = availableApplications.find(app =>
      (app.applicationId || app.id) === applicationId
    );

    if (!selectedApp) {
      return '';
    }

    // Mapper le nom de l'application vers le script correspondant
    const appName = selectedApp.name.toLowerCase();

    // Correspondances exactes
    if (applicationCloudInitScripts[applicationId]) {
      return applicationCloudInitScripts[applicationId];
    }

    // Correspondances par nom (pour gérer les variations)
    if (appName.includes('webmin') || appName.includes('virtualmin')) {
      return applicationCloudInitScripts['webmin'];
    }
    if (appName.includes('docker')) {
      return applicationCloudInitScripts['docker'];
    }
    if (appName.includes('lamp') || (appName.includes('apache') && appName.includes('mysql'))) {
      return applicationCloudInitScripts['lamp'];
    }
    if (appName.includes('lemp') || (appName.includes('nginx') && appName.includes('mysql'))) {
      return applicationCloudInitScripts['lemp'];
    }
    if (appName.includes('wordpress')) {
      return applicationCloudInitScripts['wordpress'];
    }
    if (appName.includes('node') || appName.includes('nodejs')) {
      return applicationCloudInitScripts['nodejs'];
    }
    if (appName.includes('gitlab')) {
      return applicationCloudInitScripts['gitlab'];
    }
    if (appName.includes('nextcloud')) {
      return applicationCloudInitScripts['nextcloud'];
    }
    if (appName.includes('prometheus')) {
      return applicationCloudInitScripts['prometheus'];
    }

    // Script générique si aucune correspondance
    return `#cloud-config
# Configuration automatique pour ${selectedApp.name}
package_update: true
package_upgrade: true

# Installation de packages de base
packages:
  - curl
  - wget
  - git

runcmd:
  - echo "Installation de ${selectedApp.name} en cours..."
  # Ajoutez ici les commandes spécifiques pour ${selectedApp.name}`;
  };



  // Composant réutilisable pour le champ mot de passe avec génération
  const PasswordField = ({
    value,
    onChange,
    placeholder = "Mot de passe pour l'accès root",
    error = null,
    fieldName = "password", // "password" ou "adminPassword"
    showClearButton = false // Afficher le bouton de suppression
  }) => {

    const generatePasswordForField = () => {
      setIsGeneratingPassword(true);

      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const numbers = '0123456789';
      const allChars = lowercase + uppercase + numbers;

      let password = '';
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];

      for (let i = 3; i < 12; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      password = password.split('').sort(() => Math.random() - 0.5).join('');

      setTimeout(() => {
        if (fieldName === "adminPassword") {
          setFormData(prev => ({ ...prev, adminPassword: password }));
        } else {
          setFormData(prev => ({ ...prev, password: password }));
        }
        setIsGeneratingPassword(false);
        setShowPassword(true);
      }, 500);
    };

    return (
      <div>
        <div className="relative">
          <input
            type={showPassword ? 'text' : 'password'}
            value={value}
            onChange={onChange}
            className={`w-full px-3 py-2 ${showClearButton ? 'pr-24' : 'pr-20'} border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder={placeholder}
            disabled={isLoading}
          />
          <div className="absolute inset-y-0 right-0 flex items-center">
            <button
              type="button"
              onClick={generatePasswordForField}
              disabled={isGeneratingPassword || isLoading}
              className="px-2 py-1 text-xs bg-blue-500 text-white rounded-l hover:bg-blue-600 transition-colors"
              title="Générer un mot de passe sécurisé"
            >
              <RefreshCw className={`w-3 h-3 ${isGeneratingPassword ? 'animate-spin' : ''}`} />
            </button>
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="px-2 py-1 text-gray-500 hover:text-gray-700"
              title={showPassword ? "Masquer le mot de passe" : "Afficher le mot de passe"}
              disabled={isLoading}
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
            {showClearButton && value && (
              <button
                type="button"
                onClick={() => {
                  if (fieldName === "adminPassword") {
                    setFormData(prev => ({ ...prev, adminPassword: '' }));
                  } else {
                    setFormData(prev => ({ ...prev, password: '' }));
                  }
                }}
                className="px-2 py-1 text-gray-400 hover:text-red-500 rounded-r"
                title="Supprimer le mot de passe"
                disabled={isLoading}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
        {value && (
          <div className="mt-2">
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    getPasswordStrength(value).color
                  }`}
                  style={{ width: `${getPasswordStrength(value).percentage}%` }}
                />
              </div>
              <span className={`text-xs font-medium ${getPasswordStrength(value).color.replace('bg-', 'text-')}`}>
                {getPasswordStrength(value).label}
              </span>
            </div>
          </div>
        )}
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
      </div>
    );
  };

  // Fonction pour générer une clé SSH publique d'exemple (simulation)
  const generateExampleSSHKey = () => {
    const keyTypes = ['ssh-rsa', 'ssh-ed25519'];
    const keyType = keyTypes[Math.floor(Math.random() * keyTypes.length)];
    const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const username = 'user';
    const hostname = 'localhost';

    if (keyType === 'ssh-ed25519') {
      return `ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAI${randomString} ${username}@${hostname}`;
    } else {
      return `ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ${randomString} ${username}@${hostname}`;
    }
  };

  // Fonction pour évaluer la force du mot de passe (alphanumeric)
  const getPasswordStrength = (password) => {
    if (!password) return { percentage: 0, label: '', color: 'bg-gray-300' };

    let score = 0;

    // Longueur (plus important pour les mots de passe alphanumériques)
    if (password.length >= 8) score += 2;
    if (password.length >= 10) score += 1;
    if (password.length >= 12) score += 1;

    // Complexité (alphanumeric seulement)
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;

    // Évaluation finale adaptée pour alphanumeric
    if (score <= 3) {
      return { percentage: 25, label: 'Faible', color: 'bg-red-500' };
    } else if (score <= 5) {
      return { percentage: 60, label: 'Bon', color: 'bg-yellow-500' };
    } else if (score <= 6) {
      return { percentage: 85, label: 'Fort', color: 'bg-blue-500' };
    } else {
      return { percentage: 100, label: 'Excellent', color: 'bg-green-500' };
    }
  };

  // Fonction pour récupérer les images disponibles
  const fetchAvailableImages = async () => {
    try {
      setLoadingImages(true);
      console.log('🖼️ Fetching available images...');

      const response = await vpsService.getAvailableImages();

      if (response.data.success && response.data.data) {
        const allImages = response.data.data;
        console.log(`✅ Retrieved ${allImages.length} images:`, allImages);

        // Filter out Windows images that are incompatible with Linux VPS
        const compatibleImages = allImages.filter(img => {
          const name = img.name.toLowerCase();
          const osType = (img.osType || '').toLowerCase();
          
          // Exclude Windows images
          if (osType === 'windows' || name.includes('windows')) {
            console.log(`🚫 Filtering out Windows image: ${img.name}`);
            return false;
          }
          
          // Include Linux images
          return true;
        });

        console.log(`✅ Compatible images after filtering: ${compatibleImages.length}`);
        setAvailableImages(compatibleImages);

        // Sélectionner la première image compatible par défaut
        if (compatibleImages.length > 0) {
          setFormData(prev => ({
            ...prev,
            imageId: compatibleImages[0].imageId || compatibleImages[0].id || ''
          }));
        }
      } else {
        console.error('❌ Failed to fetch images:', response.data.message);
        // Fallback vers des images par défaut en cas d'erreur
        const fallbackImages = [
          {
            imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb',
            name: 'Ubuntu 22.04 LTS',
            osType: 'Linux',
            description: 'Ubuntu 22.04 LTS Jammy Jellyfish'
          }
        ];
        setAvailableImages(fallbackImages);
        setFormData(prev => ({
          ...prev,
          imageId: fallbackImages[0].imageId
        }));
      }
    } catch (error) {
      console.error('❌ Error fetching images:', error);
      // Fallback vers des images par défaut en cas d'erreur
      const fallbackImages = [
        {
          imageId: 'afecbb85-e2fc-46f0-9684-b46b1faf00bb',
          name: 'Ubuntu 22.04 LTS',
          osType: 'Linux',
          description: 'Ubuntu 22.04 LTS Jammy Jellyfish'
        }
      ];
      setAvailableImages(fallbackImages);
      setFormData(prev => ({
        ...prev,
        imageId: fallbackImages[0].imageId
      }));
    } finally {
      setLoadingImages(false);
    }
  };

  // Fonction pour récupérer les applications disponibles
  const fetchAvailableApplications = async () => {
    try {
      setLoadingApplications(true);
      console.log('📦 Fetching available applications...');

      const response = await vpsService.getAvailableApplications();

      if (response.data.success && response.data.data) {
        const applications = response.data.data;
        console.log(`✅ Retrieved ${applications.length} applications:`, applications);
        setAvailableApplications(applications);
      } else {
        console.error('❌ Failed to fetch applications:', response.data.message);
        setAvailableApplications([]);
      }
    } catch (error) {
      console.error('❌ Error fetching applications:', error);
      setAvailableApplications([]);
    } finally {
      setLoadingApplications(false);
    }
  };

  // Fonction pour récupérer les custom images
  const fetchCustomImages = async () => {
    setLoadingCustomImages(true);
    try {
      console.log('🖼️ Fetching custom images...');
      console.log('🔍 customImageService:', customImageService);

      const response = await customImageService.getCustomImages();
      console.log('🔍 Custom images response:', response);

      // Le service customImageService retourne directement { success, data, message }
      if (response && response.success) {
        console.log(`✅ Retrieved ${response.data.length} custom images`);
        setCustomImages(response.data);
      } else {
        console.error('❌ Failed to fetch custom images:', response?.message || 'Unknown error while fetching custom images');
        setCustomImages([]);
      }
    } catch (error) {
      console.error('❌ Error fetching custom images:', error);
      console.error('❌ Error details:', error.message, error.stack);
      setCustomImages([]);
    } finally {
      setLoadingCustomImages(false);
    }
  };

  // Fonction pour gérer la création d'une image personnalisée
  const handleCreateCustomImage = async () => {
    try {
      // Valider les champs requis
      if (!formData.customImageUrl || !formData.customImageName || !formData.customImageVersion) {
        showNotification('error', 'Veuillez remplir tous les champs requis : URL, nom et version de l\'image.');
        return;
      }

      // Validation préventive : vérifier si le nom existe déjà
      const existingImageWithSameName = customImages.find(img =>
        img.name.toLowerCase() === formData.customImageName.toLowerCase()
      );

      if (existingImageWithSameName) {
        showNotification('warning',
          `Une image avec le nom "${formData.customImageName}" existe déjà. ` +
          `Veuillez choisir un nom différent ou ajouter un suffixe (ex: ${formData.customImageName}-v2).`
        );
        return;
      }

      // Vérifier si une image avec ce nom/version existe déjà pour éviter les doublons
      const existingImage = customImages.find(img =>
        img.name === formData.customImageName &&
        img.version === formData.customImageVersion
      );

      if (existingImage) {
        console.log('✅ L\'image personnalisée existe déjà:', existingImage);
        setFormData(prev => ({
          ...prev,
          customImageId: existingImage.imageId || existingImage.id,
          customImageUrl: '', customImageName: '', customImageVersion: '', customImageDescription: ''
        }));
        setShowCustomImageModal(false);
        showNotification('success', `L'image "${existingImage.name}" existe déjà et a été sélectionnée automatiquement.`);
        return;
      }

      // Créer l'image via le service
      const result = await customImageService.createCustomImage({
        url: formData.customImageUrl,
        name: formData.customImageName,
        osType: formData.customImageOsType,
        version: formData.customImageVersion,
        description: formData.customImageDescription
      });

      if (result.data.success) {
        console.log('✅ La demande de création d\'image personnalisée a été acceptée:', result.data.data);

        // Mise à jour optimiste de l'interface utilisateur
        const newImage = {
          imageId: result.data.data.imageId || result.data.data.id,
          name: formData.customImageName,
          version: formData.customImageVersion,
          osType: formData.customImageOsType,
          description: formData.customImageDescription,
          standardImage: false,
          status: 'creating' // Statut initial
        };

        // Ajouter à la liste locale et sélectionner
        setCustomImages(prev => [...prev, newImage]);
        setFormData(prev => ({
          ...prev,
          customImageId: newImage.imageId,
          customImageUrl: '', customImageName: '', customImageVersion: '', customImageDescription: ''
        }));

        setShowCustomImageModal(false);
        showNotification('success', `L'image "${formData.customImageName}" a été créée avec succès et est maintenant sélectionnée. Le processus de téléchargement peut prendre quelques minutes.`);

        // Rafraîchir la liste complète depuis le serveur après un délai
        setTimeout(fetchCustomImages, 30000); // 30 secondes
      }
    } catch (error) {
      console.error('❌ Échec du traitement de l\'image personnalisée:', error);

      // Gestion spéciale pour l'erreur de nom dupliqué
      if (error.message && error.message.includes('DUPLICATE_NAME:')) {
        const cleanMessage = error.message.replace('DUPLICATE_NAME:', '');
        showNotification('warning', cleanMessage);
      } else {
        // Autres erreurs
        showNotification('error', `Échec de la création de l'image : ${error.message}`);
      }
    }
  };

  // Fonction de validation
  const validateForm = () => {
    const newErrors = {};

    // Validation de l'image
    if (!formData.imageId && formData.installationType === 'standard') {
      newErrors.imageId = 'Veuillez sélectionner un système d\'exploitation';
    }

    // Validation selon le type d'installation
    if (formData.installationType === 'standard') {
      // Installation standard : mot de passe requis
      if (!formData.password) {
        newErrors.password = 'Le mot de passe est requis';
      } else if (formData.password.length < 8) {
        newErrors.password = 'Le mot de passe doit contenir au moins 8 caractères';
      }
    } else {
      // Installation avancée : mot de passe OU clé SSH requis
      if (!formData.adminPassword && !formData.publicSshKey) {
        newErrors.access = 'Password or public SSH-Keys must be set in order to access the VPS.';
      }

      if (formData.adminPassword && formData.adminPassword.length < 8) {
        newErrors.adminPassword = 'Le mot de passe doit contenir au moins 8 caractères';
      }

      // Validation Custom Image
      if (formData.advancedImageType === 'custom' && !formData.customImageId) {
        newErrors.customImage = 'Custom image is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Fonction de soumission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      console.log('🔄 Starting VPS reinstallation...');

      // Préparer les données de réinstallation
      const reinstallData = {};

      // Données selon le type d'installation
      if (formData.installationType === 'standard') {
        reinstallData.imageId = formData.imageId;
        reinstallData.password = formData.password;
        reinstallData.enableRootUser = true; // Force enable root user for password authentication
        reinstallData.defaultUser = "root"; // Set default user to root

        // Add selected application if any
        if (formData.selectedApplication) {
          reinstallData.selectedApplication = formData.selectedApplication;
        }

        // Add Cloud-Init configuration to enable SSH password authentication
        reinstallData.userData = `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${formData.password}
chpasswd:
  expire: false

# Configure SSH to allow password authentication
write_files:
  - path: /etc/ssh/sshd_config.d/99-enable-password-auth.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin yes
      PubkeyAuthentication yes
    permissions: '0644'

runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Password authentication enabled for standard installation" >> /root/install.log`;
      } else {
        // Installation avancée
        if (formData.adminPassword) {
          reinstallData.password = formData.adminPassword;
        }

        if (formData.publicSshKey) {
          reinstallData.sshKeys = [formData.publicSshKey];
        }

        // Pour l'authentification par mot de passe, forcer enableRootUser = true comme Standard
        if (formData.adminPassword) {
          reinstallData.enableRootUser = true; // Force comme Standard pour que l'auth par mot de passe fonctionne
          reinstallData.defaultUser = "root"; // Set default user to root when password is provided
        } else {
          reinstallData.enableRootUser = formData.enableRootUser; // Respecter le choix si pas de mot de passe
          reinstallData.defaultUser = formData.enableRootUser ? "root" : "admin"; // Set user based on enableRootUser
        }

        // Garder le fonctionnement original pour les applications sélectionnées
        if (formData.selectedApplication) {
          reinstallData.selectedApplication = formData.selectedApplication;
        }

        // Configuration Cloud-Init pour l'authentification par mot de passe (garder la philosophie Advanced)
        if (formData.userData) {
          reinstallData.userData = formData.userData;
        } else if (formData.adminPassword) {
          // Configuration SSH simple pour Advanced (garder sa philosophie)
          reinstallData.userData = `#cloud-config
# Enable password authentication for SSH
ssh_pwauth: true
password: ${formData.adminPassword}
chpasswd:
  expire: false
runcmd:
  - systemctl restart sshd || service ssh restart
  - echo "Password authentication enabled for advanced installation" >> /root/install.log`;
        }

        if (formData.cloudInitTemplate) {
          reinstallData.cloudInitTemplate = formData.cloudInitTemplate;
        }

        // Custom Image handling
        if (formData.advancedImageType === 'custom') {
          reinstallData.customImageId = formData.customImageId;
          // Remove imageId when using custom image to avoid conflicts
          delete reinstallData.imageId;
        } else {
          // Use standard image for advanced installation
          reinstallData.imageId = formData.imageId;
        }
      }

      console.log('📤 Reinstall data:', reinstallData);
      console.log('🔍 Installation type:', formData.installationType);
      console.log('🔍 Advanced type:', formData.advancedImageType);
      console.log('🔍 Enable root user (form):', formData.enableRootUser);
      console.log('🔍 Enable root user (payload):', reinstallData.enableRootUser);
      console.log('🔍 Default user (payload):', reinstallData.defaultUser);
      console.log('🔍 Admin password length:', formData.adminPassword ? formData.adminPassword.length : 0);
      console.log('🔍 SSH keys:', formData.publicSshKey ? 'Present' : 'None');

      // Debug détaillé pour Advanced
      if (formData.installationType === 'advanced') {
        console.log('🚨 ADVANCED INSTALLATION DEBUG:');
        console.log('  - adminPassword:', formData.adminPassword ? '***SET***' : 'NOT SET');
        console.log('  - enableRootUser (form):', formData.enableRootUser);
        console.log('  - publicSshKey:', formData.publicSshKey ? 'SET' : 'NOT SET');
        console.log('  - userData length:', formData.userData ? formData.userData.length : 0);
        console.log('  - Final reinstallData.password:', reinstallData.password ? '***SET***' : 'NOT SET');
        console.log('  - Final reinstallData.enableRootUser:', reinstallData.enableRootUser);
        console.log('  - Final reinstallData.defaultUser:', reinstallData.defaultUser);
        console.log('  - Final reinstallData.userData:', reinstallData.userData ? 'SET' : 'NOT SET');
      }

      // Appeler l'API de réinstallation
      const response = await vpsService.reinstallVPS(server.id, reinstallData);

      if (response.data.success) {
        console.log('✅ VPS reinstallation started successfully');

        // Appeler le callback de succès si fourni
        if (onReinstallSuccess) {
          onReinstallSuccess(response.data);
        }

        // Fermer le modal
        onClose();
      } else {
        throw new Error(response.data.message || 'Failed to start VPS reinstallation');
      }

    } catch (error) {
      console.error('❌ VPS reinstallation failed:', error);
      setErrors({
        submit: error.message || 'Une erreur est survenue lors de la réinstallation'
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center">
              <AlertTriangle className="w-6 h-6 text-orange-500 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">
                Réinstaller le VPS {server?.name || server?.id}
              </h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Notification */}
          {notification && (
            <div className={`mx-6 mt-4 p-4 rounded-md border-l-4 ${
              notification.type === 'success' ? 'bg-green-50 border-green-400' :
              notification.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
              'bg-red-50 border-red-400'
            }`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  {notification.type === 'success' && (
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  )}
                  {notification.type === 'warning' && (
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                  {notification.type === 'error' && (
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm ${
                    notification.type === 'success' ? 'text-green-700' :
                    notification.type === 'warning' ? 'text-yellow-700' :
                    'text-red-700'
                  }`}>
                    {notification.message}
                  </p>
                </div>
                <div className="ml-auto pl-3">
                  <div className="-mx-1.5 -my-1.5">
                    <button
                      type="button"
                      onClick={() => setNotification(null)}
                      className={`inline-flex rounded-md p-1.5 ${
                        notification.type === 'success' ? 'text-green-500 hover:bg-green-100' :
                        notification.type === 'warning' ? 'text-yellow-500 hover:bg-yellow-100' :
                        'text-red-500 hover:bg-red-100'
                      } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                        notification.type === 'success' ? 'focus:ring-green-600' :
                        notification.type === 'warning' ? 'focus:ring-yellow-600' :
                        'focus:ring-red-600'
                      }`}
                    >
                      <span className="sr-only">Dismiss</span>
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="p-6">
            <div className="bg-orange-50 border border-orange-200 rounded-md p-4 mb-6">
              <div className="flex">
                <AlertTriangle className="w-5 h-5 text-orange-400 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-orange-800">
                    Attention : Réinstallation du VPS
                  </h3>
                  <div className="mt-2 text-sm text-orange-700">
                    <p>
                      Cette action va <strong>effacer complètement</strong> toutes les données présentes sur le VPS et installer un nouveau système d'exploitation.
                    </p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Tous les fichiers et configurations seront perdus</li>
                      <li>Le VPS sera temporairement indisponible pendant l'installation</li>
                      <li>Cette action est <strong>irréversible</strong></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Type d'installation */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Type d'installation</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Installation standard */}
                  <div className="relative">
                    <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                      formData.installationType === 'standard' 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200'
                    }`}>
                      <input
                        type="radio"
                        name="installationType"
                        value="standard"
                        checked={formData.installationType === 'standard'}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          installationType: e.target.value 
                        }))}
                        className="mr-3"
                      />
                      <div className="flex items-center">
                        <HardDrive className="w-5 h-5 text-blue-600 mr-2" />
                        <span className="font-medium">Installation standard</span>
                      </div>
                    </label>
                  </div>

                  {/* Installation avancée */}
                  <div className="relative">
                    <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                      formData.installationType === 'advanced' 
                        ? 'border-purple-500 bg-purple-50' 
                        : 'border-gray-200'
                    }`}>
                      <input
                        type="radio"
                        name="installationType"
                        value="advanced"
                        checked={formData.installationType === 'advanced'}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          installationType: e.target.value 
                        }))}
                        className="mr-3"
                      />
                      <div className="flex items-center">
                        <Settings className="w-5 h-5 text-purple-600 mr-2" />
                        <span className="font-medium">Installation avancée/Image personnalisée</span>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Installation avancée */}
              {formData.installationType === 'advanced' && (
                <>
                  {/* Choix entre Standard Image et Custom Image */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Type d'image
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      {/* Standard Image Option */}
                      <div className="relative">
                        <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                          formData.advancedImageType === 'standard'
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200'
                        }`}>
                          <input
                            type="radio"
                            name="advancedImageType"
                            value="standard"
                            checked={formData.advancedImageType === 'standard'}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              advancedImageType: e.target.value
                            }))}
                            className="mr-3"
                          />
                          <div className="flex items-center">
                            <HardDrive className="w-5 h-5 text-blue-600 mr-2" />
                            <span className="font-medium">Standard Image</span>
                          </div>
                        </label>
                      </div>

                      {/* Custom Image Option */}
                      <div className="relative">
                        <label className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                          formData.advancedImageType === 'custom'
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200'
                        }`}>
                          <input
                            type="radio"
                            name="advancedImageType"
                            value="custom"
                            checked={formData.advancedImageType === 'custom'}
                            onChange={(e) => setFormData(prev => ({
                              ...prev,
                              advancedImageType: e.target.value
                            }))}
                            className="mr-3"
                          />
                          <div className="flex items-center">
                            <Upload className="w-5 h-5 text-purple-600 mr-2" />
                            <span className="font-medium">Custom Image</span>
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Standard Image Section */}
                  {formData.advancedImageType === 'standard' && (
                    <div className="space-y-6">
                      {/* Standard Image Selector */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Standard Image
                        </label>
                        {loadingImages ? (
                          <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center">
                            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                            <span className="text-gray-600">Chargement des images...</span>
                          </div>
                        ) : (
                          <select
                            value={formData.imageId}
                            onChange={(e) => setFormData(prev => ({ ...prev, imageId: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            disabled={isLoading}
                          >
                            <option value="">Sélectionner un système d'exploitation</option>
                            {availableImages.map((image) => (
                              <option key={image.imageId || image.id} value={image.imageId || image.id}>
                                {image.name} {image.version ? `(${image.version})` : ''}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>

                      {/* Admin Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Root Password' : 'Admin Password'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title={formData.enableRootUser ? "Mot de passe pour l'utilisateur root" : "Mot de passe pour l'utilisateur admin"} />
                        </label>
                        <PasswordField
                          value={formData.adminPassword}
                          onChange={(e) => setFormData(prev => ({ ...prev, adminPassword: e.target.value }))}
                          placeholder="Select or create new password"
                          error={errors.adminPassword}
                          fieldName="adminPassword"
                          showClearButton={true}
                        />
                      </div>

                      {/* Enable Root User */}
                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.enableRootUser}
                            onChange={(e) => setFormData(prev => ({ ...prev, enableRootUser: e.target.checked }))}
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            disabled={isLoading}
                          />
                          <span className="text-sm font-medium text-gray-700">
                            Enable Root User
                            <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Activer l'utilisateur root" />
                          </span>
                        </label>
                      </div>

                      {/* Public SSH-Key */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Public SSH-Key for User Root' : 'Public SSH-Key for User Admin'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Clé SSH publique pour l'accès" />
                        </label>
                        <div className="relative">
                          <textarea
                            value={formData.publicSshKey}
                            onChange={(e) => setFormData(prev => ({ ...prev, publicSshKey: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20"
                            rows={3}
                            placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname"
                            disabled={isLoading}
                          />
                          <div className="absolute top-2 right-2 flex flex-col space-y-1">
                            <button
                              type="button"
                              onClick={() => {
                                const key = generateExampleSSHKey();
                                setFormData(prev => ({ ...prev, publicSshKey: key }));
                              }}
                              className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                              title="Générer une clé SSH d'exemple"
                              disabled={isLoading}
                            >
                              <Key className="w-3 h-3" />
                            </button>
                            {formData.publicSshKey && (
                              <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, publicSshKey: '' }))}
                                className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                title="Supprimer la clé SSH"
                                disabled={isLoading}
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Cloud-Init */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Cloud-Init
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Template Cloud-Init pour la configuration automatique" />
                        </label>
                        <div className="relative">
                          <select
                            value={formData.cloudInitTemplate}
                            onChange={(e) => {
                              const templateKey = e.target.value;
                              setFormData(prev => ({
                                ...prev,
                                cloudInitTemplate: templateKey,
                                userData: templateKey ? generateCloudInitScript(templateKey) : ''
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                            disabled={isLoading}
                          >
                            <option value="">Select cloud init template</option>
                            {availableApplications.map((app) => (
                              <option key={app.applicationId || app.id} value={app.applicationId || app.id}>
                                {app.name} {app.version ? `(${app.version})` : ''}
                              </option>
                            ))}
                          </select>
                          {formData.cloudInitTemplate && (
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({
                                ...prev,
                                cloudInitTemplate: '',
                                userData: ''
                              }))}
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500"
                              title="Supprimer la sélection"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        {formData.cloudInitTemplate && (
                          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                            <p className="text-sm text-blue-700">
                              <strong>Script généré automatiquement</strong> pour l'application sélectionnée. Vous pouvez le modifier si nécessaire.
                            </p>
                          </div>
                        )}
                        <textarea
                          value={formData.userData}
                          onChange={(e) => setFormData(prev => ({ ...prev, userData: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                          rows={8}
                          placeholder="#cloud-config&#10;# Configuration Cloud-Init personnalisée&#10;packages:&#10;  - nginx&#10;  - docker.io"
                          disabled={isLoading}
                        />
                      </div>


                    </div>
                  )}

                  {/* Custom Image Section */}
                  {formData.advancedImageType === 'custom' && (
                    <div className="space-y-6">
                      {/* Custom Image Selector */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Custom Image
                          <Info className="w-4 h-4 inline ml-1 text-purple-500" title="Sélectionner ou créer une image personnalisée" />
                        </label>
                        <div className="space-y-2">
                          {loadingCustomImages ? (
                            <div className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 flex items-center">
                              <RefreshCw className="w-4 h-4 mr-2 animate-spin text-purple-500" />
                              <span className="text-gray-600">Chargement des images personnalisées...</span>
                            </div>
                          ) : (
                            <select
                              value={formData.customImageId || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, customImageId: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                              disabled={isLoading}
                            >
                              <option value="">
                                {customImages.length === 0
                                  ? "Aucune image personnalisée disponible"
                                  : "Sélectionner une image personnalisée existante"
                                }
                              </option>
                              {customImages.map((image) => (
                                <option key={image.imageId || image.id} value={image.imageId || image.id}>
                                  {image.name} {image.version ? `v${image.version}` : ''} - {image.osType}
                                  {image.sizeMb ? ` (${Math.round(image.sizeMb / 1024 * 100) / 100} GB)` : ''}
                                </option>
                              ))}
                            </select>
                          )}

                          <div className="flex justify-center space-x-3">
                            <button
                              type="button"
                              onClick={() => {
                                console.log('🔄 Refreshing custom images...');
                                fetchCustomImages();
                              }}
                              className="inline-flex items-center px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
                              disabled={isLoading || loadingCustomImages}
                            >
                              <RefreshCw className={`w-4 h-4 mr-2 ${loadingCustomImages ? 'animate-spin' : ''}`} />
                              Actualiser
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                console.log('🔄 Opening Custom Image Modal...');
                                setShowCustomImageModal(true);
                              }}
                              className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                              disabled={isLoading}
                            >
                              <Upload className="w-4 h-4 mr-2" />
                              Ajouter une image
                            </button>
                          </div>
                        </div>
                        {errors.customImage && (
                          <p className="text-xs text-red-600 mt-1">{errors.customImage}</p>
                        )}
                        {formData.customImageId && (() => {
                          const selectedImage = customImages.find(img => (img.imageId || img.id) === formData.customImageId);
                          return selectedImage ? (
                            <div className="mt-3 p-3 bg-purple-50 border border-purple-200 rounded-md">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <p className="text-sm font-medium text-purple-800">
                                    Image personnalisée sélectionnée
                                  </p>
                                  <p className="text-sm text-purple-700 mt-1">
                                    <strong>{selectedImage.name}</strong>
                                    {selectedImage.version && <span className="ml-2 text-purple-600">v{selectedImage.version}</span>}
                                  </p>
                                  <div className="mt-2 space-y-1">
                                    <p className="text-xs text-purple-600">
                                      <strong>OS:</strong> {selectedImage.osType}
                                    </p>
                                    {selectedImage.sizeMb && (
                                      <p className="text-xs text-purple-600">
                                        <strong>Taille:</strong> {Math.round(selectedImage.sizeMb / 1024 * 100) / 100} GB
                                      </p>
                                    )}
                                    {selectedImage.creationDate && (
                                      <p className="text-xs text-purple-600">
                                        <strong>Créée le:</strong> {new Date(selectedImage.creationDate).toLocaleDateString('fr-FR')}
                                      </p>
                                    )}
                                    {selectedImage.description && (
                                      <p className="text-xs text-purple-600 mt-2">
                                        <strong>Description:</strong> {selectedImage.description}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : null;
                        })()}
                      </div>

                      {/* Admin Password */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Root Password' : 'Admin Password'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title={formData.enableRootUser ? "Mot de passe pour l'utilisateur root" : "Mot de passe pour l'utilisateur admin"} />
                        </label>
                        <PasswordField
                          value={formData.adminPassword}
                          onChange={(e) => setFormData(prev => ({ ...prev, adminPassword: e.target.value }))}
                          placeholder="Select or create new password"
                          error={errors.adminPassword}
                          fieldName="adminPassword"
                          showClearButton={true}
                        />
                      </div>

                      {/* Enable Root User */}
                      <div>
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.enableRootUser}
                            onChange={(e) => setFormData(prev => ({ ...prev, enableRootUser: e.target.checked }))}
                            className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            disabled={isLoading}
                          />
                          <span className="text-sm font-medium text-gray-700">
                            Enable Root User
                            <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Activer l'utilisateur root" />
                          </span>
                        </label>
                      </div>

                      {/* Public SSH-Key */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          {formData.enableRootUser ? 'Public SSH-Key for User Root' : 'Public SSH-Key for User Admin'}
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Clé SSH publique pour l'accès" />
                        </label>
                        <div className="relative">
                          <textarea
                            value={formData.publicSshKey}
                            onChange={(e) => setFormData(prev => ({ ...prev, publicSshKey: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-20"
                            rows={3}
                            placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ... user@hostname"
                            disabled={isLoading}
                          />
                          <div className="absolute top-2 right-2 flex flex-col space-y-1">
                            <button
                              type="button"
                              onClick={() => {
                                const key = generateExampleSSHKey();
                                setFormData(prev => ({ ...prev, publicSshKey: key }));
                              }}
                              className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                              title="Générer une clé SSH d'exemple"
                              disabled={isLoading}
                            >
                              <Key className="w-3 h-3" />
                            </button>
                            {formData.publicSshKey && (
                              <button
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, publicSshKey: '' }))}
                                className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                title="Supprimer la clé SSH"
                                disabled={isLoading}
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Cloud-Init */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Cloud-Init
                          <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Template Cloud-Init pour la configuration automatique" />
                        </label>
                        <div className="relative">
                          <select
                            value={formData.cloudInitTemplate}
                            onChange={(e) => {
                              const templateKey = e.target.value;
                              setFormData(prev => ({
                                ...prev,
                                cloudInitTemplate: templateKey,
                                userData: templateKey ? generateCloudInitScript(templateKey) : ''
                              }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                            disabled={isLoading}
                          >
                            <option value="">Select cloud init template</option>
                            {availableApplications.map((app) => (
                              <option key={app.applicationId || app.id} value={app.applicationId || app.id}>
                                {app.name} {app.version ? `(${app.version})` : ''}
                              </option>
                            ))}
                          </select>
                          {formData.cloudInitTemplate && (
                            <button
                              type="button"
                              onClick={() => setFormData(prev => ({
                                ...prev,
                                cloudInitTemplate: '',
                                userData: ''
                              }))}
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-500"
                              title="Supprimer la sélection"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                        {formData.cloudInitTemplate && (
                          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                            <p className="text-sm text-blue-700">
                              <strong>Script généré automatiquement</strong> pour l'application sélectionnée. Vous pouvez le modifier si nécessaire.
                            </p>
                          </div>
                        )}
                        <textarea
                          value={formData.userData}
                          onChange={(e) => setFormData(prev => ({ ...prev, userData: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                          rows={8}
                          placeholder="#cloud-config&#10;# Configuration Cloud-Init personnalisée&#10;packages:&#10;  - nginx&#10;  - docker.io"
                          disabled={isLoading}
                        />
                      </div>
                    </div>
                  )}
                </>
              )}

              {/* Installation standard */}
              {formData.installationType === 'standard' && (
                <div className="space-y-6">
                  {/* Image Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Système d'exploitation
                    </label>
                    {loadingImages ? (
                      <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                        <span className="text-gray-600">Chargement des images...</span>
                      </div>
                    ) : (
                      <select
                        value={formData.imageId}
                        onChange={(e) => setFormData(prev => ({ ...prev, imageId: e.target.value }))}
                        className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.imageId ? 'border-red-300' : 'border-gray-300'
                        }`}
                        disabled={isLoading}
                      >
                        <option value="">Sélectionner un système d'exploitation</option>
                        {availableImages.map((image) => (
                          <option key={image.imageId || image.id} value={image.imageId || image.id}>
                            {image.name} {image.version ? `(${image.version})` : ''}
                          </option>
                        ))}
                      </select>
                    )}
                    {errors.imageId && (
                      <p className="text-red-600 text-sm mt-1">{errors.imageId}</p>
                    )}
                  </div>

                  {/* Application Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Application (optionnel)
                    </label>

                    {loadingApplications ? (
                      <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 flex items-center">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
                        <span className="text-gray-600">Chargement des applications...</span>
                      </div>
                    ) : (
                      <select
                        value={formData.selectedApplication}
                        onChange={(e) => setFormData(prev => ({ ...prev, selectedApplication: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        disabled={isLoading}
                      >
                        <option value="">Aucune application (OS seulement)</option>
                        {availableApplications.map((app) => (
                          <option key={app.applicationId || app.id} value={app.applicationId || app.id}>
                            {app.name} {app.version ? `(${app.version})` : ''}
                          </option>
                        ))}
                      </select>
                    )}

                    {formData.selectedApplication && (
                      <div className="mt-2 p-2 bg-green-50 rounded-md">
                        <p className="text-sm text-green-800">
                          Application sélectionnée: <strong>
                            {availableApplications.find(app => (app.applicationId || app.id) === formData.selectedApplication)?.name || formData.selectedApplication}
                          </strong>
                        </p>
                        {availableApplications.find(app => (app.applicationId || app.id) === formData.selectedApplication)?.description && (
                          <p className="text-xs text-green-600 mt-1">
                            {availableApplications.find(app => (app.applicationId || app.id) === formData.selectedApplication)?.description}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Password */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Key className="w-4 h-4 inline mr-1" />
                      Mot de passe root
                    </label>
                    <PasswordField
                      value={formData.password}
                      onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                      placeholder="Mot de passe pour l'accès root"
                      error={errors.password}
                      fieldName="password"
                    />
                  </div>
                </div>
              )}

              {/* Message d'erreur global */}
              {errors.submit && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{errors.submit}</p>
                </div>
              )}

              {errors.access && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{errors.access}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  disabled={isLoading}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"></div>
                      Réinstallation...
                    </>
                  ) : (
                    'Réinstaller le VPS'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Custom Image Modal */}
      {showCustomImageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">Add Custom Image</h2>
              <button
                onClick={() => setShowCustomImageModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="p-6 space-y-4">
              {/* Image URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="URL de l'image personnalisée" />
                </label>
                <input
                  type="url"
                  value={formData.customImageUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageUrl: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/my-custom-image.iso"
                />
              </div>

              {/* Image Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image Name
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Nom de l'image personnalisée" />
                </label>
                <input
                  type="text"
                  value={formData.customImageName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="My Custom Image"
                />
              </div>

              {/* OS Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OS Type
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Type de système d'exploitation" />
                </label>
                <select
                  value={formData.customImageOsType}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageOsType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Linux">Linux</option>
                  <option value="Windows">Windows</option>
                </select>
              </div>

              {/* Version */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Version
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Version du système d'exploitation" />
                </label>
                <input
                  type="text"
                  value={formData.customImageVersion}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageVersion: e.target.value }))}
                  className="w-full px-3 py-2 border border-red-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="22.04, 2022, etc."
                />
                <p className="text-xs text-red-600 mt-1">Version is required.</p>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                  <Info className="w-4 h-4 inline ml-1 text-blue-500" title="Description de l'image personnalisée" />
                </label>
                <textarea
                  value={formData.customImageDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, customImageDescription: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Description de votre image personnalisée..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t">
              <button
                type="button"
                onClick={() => setShowCustomImageModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleCreateCustomImage}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Upload
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ReinstallModal;
