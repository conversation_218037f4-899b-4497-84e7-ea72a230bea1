import { AlertCircle } from "lucide-react";
import React from "react";

function ErrorAlert({ error }) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 animate-[fadeIn_0.2s_ease-out]">
      <div className="flex">
        <AlertCircle className="h-5 w-5 text-red-400" />
        <div className="ml-3">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      </div>
    </div>
  );
}

export default ErrorAlert;
