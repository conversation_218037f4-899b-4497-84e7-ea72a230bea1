/**
 * Currency Routes for Admin
 * Routes for testing and managing currency conversion
 */

const express = require('express');
const router = express.Router();
const currencyController = require('../../controllers/admin/currencyController');
const { authenticateToken, requireAdmin } = require('../../middleware/auth');

// Apply authentication and admin middleware to all routes
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @route   GET /admin/currency/test
 * @desc    Test currency API connection
 * @access  Admin
 */
router.get('/test', currencyController.testConnection);

/**
 * @route   GET /admin/currency/rate
 * @desc    Get current USD to MAD exchange rate
 * @access  Admin
 */
router.get('/rate', currencyController.getExchangeRate);

/**
 * @route   POST /admin/currency/convert
 * @desc    Convert USD amount to MAD
 * @access  Admin
 * @body    { usdAmount: number }
 */
router.post('/convert', currencyController.convertUsdToMad);

/**
 * @route   POST /admin/currency/convert-batch
 * @desc    Convert multiple USD amounts to MAD
 * @access  Admin
 * @body    { usdAmounts: number[] }
 */
router.post('/convert-batch', currencyController.convertBatchUsdToMad);

/**
 * @route   POST /admin/currency/clear-cache
 * @desc    Clear currency cache
 * @access  Admin
 */
router.post('/clear-cache', currencyController.clearCache);

/**
 * @route   GET /admin/currency/cache-status
 * @desc    Get cache status
 * @access  Admin
 */
router.get('/cache-status', currencyController.getCacheStatus);

/**
 * @route   GET /admin/currency/simulate-vps-pricing
 * @desc    Simulate VPS pricing conversion from USD to MAD
 * @access  Admin
 */
router.get('/simulate-vps-pricing', currencyController.simulateVpsPricing);

/**
 * @route   POST /admin/currency/simulate-pricing-with-margin
 * @desc    Simulate VPS pricing with margin applied
 * @access  Admin
 * @body    { margin: number }
 */
router.post('/simulate-pricing-with-margin', currencyController.simulatePricingWithMargin);

module.exports = router;
