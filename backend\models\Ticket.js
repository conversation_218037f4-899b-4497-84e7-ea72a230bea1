// models/Ticket.js
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const ticketSchema = new mongoose.Schema(
  {
    identifiant: { type: String, required: true, unique: true },
    creator: {type: Schema.Types.ObjectId, ref: 'User'},
    subject: { type: String, required: true },
    service: { type: String, default: "none" },
    priority: { type: String, default: "medium", enum: ["low", "medium", "high", "urgent"] },
    message: { type: String, required: true },
    images: [{ type: String }], // URLs or file paths of uploaded images
    status: { type: String, default: "open", enum: ["open", "in_progress", "resolved", "closed", "deleted"] },
    resolutionComment: { type: String, default: "" },
    resolvedBy: { type: Schema.Types.ObjectId, ref: 'User'},
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);


module.exports = mongoose.model("Ticket", ticketSchema);