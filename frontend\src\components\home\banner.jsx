"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { BsArrowUpRight } from "react-icons/bs";
import { useIsMobile } from "../../app/hook/useIsMobile";
import { motion, useScroll, useTransform } from "framer-motion";
import homeHeroImage from "/public/images/home/<USER>";
import googleLogo from "/public/images/home/<USER>";
import partenersLogo from "/public/images/home/<USER>";
import TypingAnimation from "./TypingAnimation";

function Banner({ t }) {
  const isMobile = useIsMobile();
  const { scrollY } = useScroll();

  // Animation configurations
  const textAnimationStart = isMobile ? 350 : 80;
  const imgAnimationStart = isMobile ? 100 : 80;

  const textX = useTransform(
    scrollY,
    [textAnimationStart, textAnimationStart + 300],
    [0, -200]
  );
  const imgX = useTransform(
    scrollY,
    [imgAnimationStart, imgAnimationStart + 300],
    [0, 200]
  );
  const opacity = useTransform(
    scrollY,
    [textAnimationStart, textAnimationStart + 300],
    [1, 0]
  );

  // Function to scroll smoothly to a section
  const scrollToSection = (id) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Reusable Contact Button Component
  const ContactButton = ({ className = "" }) => (
    <button
      onClick={() => scrollToSection('contact-section')}
      className={`flex items-center justify-between w-fit gap-x-5 text-white rounded-full ${className}`}
    >
      <span className="text-base xl:text-lg font-inter p-1">
        {t("contact_us")}
      </span>
      <span
        className="p-2 bg-black rounded-full border-[0.69px] border-[#2C52F7]"
        style={{ boxShadow: "0px 0px 19.61px 7.54px rgba(66, 99, 242, 0.39)" }}
      >
        <BsArrowUpRight className="text-white" />
      </span>
    </button>
  );

  return (
    <div className="bg-custom-gradient w-full h-full relative overflow-hidden max-w-[1900px] mx-auto">
      {/* Background Video */}
      <video
        loop
        muted
        autoPlay
        playsInline
        poster="/images/home/<USER>"
        className="rounded-lg absolute inset-0 w-full h-full object-cover"
      >
        <source src="/videos/banner-bghero.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Content Container */}
      <div className="relative max-h-screen min-h-[70vh] gap-x-6  max-w-[1800px] z-10 mx-auto text-white py-6 md:py-16 px-4 lg:px-20 lg:py-10 flex flex-col-reverse lg:flex-row items-center justify-between">
        {/* Left Section - Text Content */}
        <motion.div
          style={{ x: textX, opacity }}
          className="flex flex-col w-full lg:w-1/2 gap-y-6 xl:gap-y-10 md:gap-x-4"
        >
          <TypingAnimation title={t("web_dev_mobile_apps_cloud")} />
          <p className="text-[15px] 2xl:text-lg text-center md:text-start">
            {t("ztechengineering_intro")}
          </p>

          {/* Rating Section */}
          <div className="flex items-center space-x-5 p-0 justify-center md:justify-normal">
            <span className="text-white md:text-xl text-lg">
              {t("excellent")}
            </span>
            <div className="flex space-x-1">
              {[...Array(5)].map((_, index) => (
                <span key={index} className="text-yellow-500 text-2xl">
                  ★
                </span>
              ))}
            </div>
            <Image
              src={googleLogo}
              alt="Google Reviews"
              width={80}
              height={100}
              className="md:w-20 w-16 my-auto"
            />
          </div>

          {/* Partners Logo */}
          <div className="justify-center flex md:justify-start">
            <Image
              src={partenersLogo}
              alt="Our Partners"
              width={190}
              height={200}
              className="w-[190px]"
            />
          </div>

          {/* Contact Button */}
          <div className="block mt-5 w-fit rounded-full mx-auto md:mx-0 bg-opacity-30 bg-gray-500 px-2 py-1">
            <ContactButton />
          </div>
        </motion.div>

        {/* Right Section - Hero Image */}
        <motion.div
          style={{ x: imgX, opacity }}
          className="lg:w-1/2 m-auto p-0 mt-2 md:mt-auto overflow-hidden flex items-center justify-center"
        >
          <Image
            src={homeHeroImage}
            alt="Engineer working on server"
            width={600}
            height={600}
            // sizes="(max-width: 768px) 100vw, 800px"
            className="rounded-3xl object-cover"
            placeholder="blur"
            priority
          />
        </motion.div>
      </div>
    </div>
  );
}

export default Banner;
