/**
 * Test script for VPS APIs from frontend perspective
 * This script tests the VPS APIs using the same configuration as the frontend
 */

const axios = require('axios');

// Use the same configuration as frontend
const BACKEND_URL = 'http://localhost:5002';

const testVPSAPIs = async () => {
  console.log('🧪 Testing VPS APIs from Frontend perspective...\n');

  try {
    // Test OS Images
    console.log('1. Testing OS Images API...');
    const imagesResponse = await axios.get(`${BACKEND_URL}/api/vps/images?provider=contabo`);
    console.log(`✅ Status: ${imagesResponse.status}`);
    console.log(`✅ OS Images: ${imagesResponse.data.data?.length || 0} images found`);
    
    if (imagesResponse.data.data?.length > 0) {
      console.log('   Sample images:');
      imagesResponse.data.data.slice(0, 3).forEach(img => {
        console.log(`   - ${img.name} (${img.imageId}) - ${img.osType}`);
      });
    }
    console.log('');

    // Test Regions
    console.log('2. Testing Regions API...');
    const regionsResponse = await axios.get(`${BACKEND_URL}/api/vps/regions?provider=contabo`);
    console.log(`✅ Status: ${regionsResponse.status}`);
    console.log(`✅ Regions: ${regionsResponse.data.data?.length || 0} regions found`);
    
    if (regionsResponse.data.data?.length > 0) {
      console.log('   Available regions:');
      regionsResponse.data.data.forEach(region => {
        console.log(`   - ${region.name} (${region.id}) - ${region.city}, ${region.country}`);
      });
    }
    console.log('');

    // Test Providers
    console.log('3. Testing Providers API...');
    const providersResponse = await axios.get(`${BACKEND_URL}/api/vps/providers`);
    console.log(`✅ Status: ${providersResponse.status}`);
    console.log(`✅ Providers: ${providersResponse.data.data?.length || 0} providers found`);
    console.log('');

    console.log('🎉 All frontend VPS API tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - OS Images: ${imagesResponse.data.data?.length || 0}`);
    console.log(`   - Regions: ${regionsResponse.data.data?.length || 0}`);
    console.log(`   - Providers: ${providersResponse.data.data?.length || 0}`);

    // Test data transformation (like frontend does)
    console.log('\n🔄 Testing data transformation...');
    
    if (imagesResponse.data.data?.length > 0) {
      const transformedImages = imagesResponse.data.data.map(img => ({
        id: img.imageId || img.id,
        name: img.name,
        description: img.description,
        type: img.osType || 'linux',
        version: img.version,
        provider: img.provider
      }));
      console.log(`✅ Transformed ${transformedImages.length} OS images successfully`);
    }

    if (regionsResponse.data.data?.length > 0) {
      const transformedRegions = regionsResponse.data.data.map(region => ({
        id: region.id,
        name: region.name,
        description: region.description,
        country: region.country,
        city: region.city,
        provider: region.provider
      }));
      console.log(`✅ Transformed ${transformedRegions.length} regions successfully`);
    }

  } catch (error) {
    console.error('❌ Error testing APIs:', error.message);
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   URL: ${error.config?.url}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    console.log('\n💡 Make sure the backend server is running on port 5002');
  }
};

// Run the test
testVPSAPIs();
