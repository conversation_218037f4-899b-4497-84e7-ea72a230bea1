const SubOrder = require("../../models/SubOrder");
const Order = require("../../models/Order");
const User = require("../../models/User");
const OrderStatus = require("../../constants/enums/order-status");
const {
  getSubOrderEmailTemplate,
} = require("../../routes/sendEmail/emailTemplates");
const {
  sendSubOrderStatusUpdateEmail,
} = require("../../routes/sendEmail/sendEmail");
const adminLogger = require("../../utils/adminLogger");

// Controller for getting all orders
const getAllOrders = async (req, res) => {
  try {
    // Extract pagination and filter parameters from query string
    const page = parseInt(req.query.params.page) || 1;
    const limit = parseInt(req.query.params.limit) || 10;
    const skip = (page - 1) * limit;
    const searchTerm = req.query.params.search || "";
    const statusFilter = req.query.params.status || "ALL";
    const paymentMethodFilter = req.query.params.paymentMethod || "ALL";

    // Build the query object
    let query = {};

    // Step 1: Find users that match the search term (if any)
    let userIds = [];
    if (searchTerm) {
      const users = await User.find({
        $or: [
          { firstName: { $regex: searchTerm, $options: "i" } },
          { lastName: { $regex: searchTerm, $options: "i" } },
        ],
      }).select("_id");

      userIds = users.map((user) => user._id);
    }

    // Step 2: Add search conditions for orders
    if (searchTerm) {
      query.$or = [
        { identifiant: { $regex: searchTerm, $options: "i" } }, // Search by identifiant
        { user: { $in: userIds } }, // Match orders linked to found users
      ];
    }

    // Step 3: Add filters (status & payment method)
    if (statusFilter !== "ALL") {
      query.status = statusFilter;
    }
    if (paymentMethodFilter !== "ALL") {
      query.paymentMethod = paymentMethodFilter;
    }

    // Step 4: Get total count of orders matching the query
    const totalOrders = await Order.countDocuments(query);

    // Step 5: Fetch paginated and filtered orders
    const orders = await Order.find(query)
      .populate("user", "firstName lastName email") // Populate user details
      .populate("subOrders")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Step 6: Calculate pagination metadata
    const totalPages = Math.ceil(totalOrders / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Step 7: Send response
    return res.status(200).json({
      success: true,
      data: orders,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalItems: totalOrders,
        itemsPerPage: limit,
        hasNextPage: hasNextPage,
        hasPrevPage: hasPrevPage,
        nextPage: hasNextPage ? page + 1 : null,
        prevPage: hasPrevPage ? page - 1 : null,
      },
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error fetching orders",
      error: error.message,
    });
  }
};

// Controller for changing order status
const updateOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    // Verify if the provided status is valid
    if (!Object.values(OrderStatus).includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid order status",
      });
    }

    // Find the order and populate user information
    const order = await Order.findById(orderId).populate('user', 'firstName lastName email');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Store previous status for notification
    const previousStatus = order.status;

    // Update the order status
    order.status = status;

    // Optionally update related fields based on status
    if (status === OrderStatus.COMPLETED && order.isPaid) {
      order.datePaid = new Date();
    }

    const updatedOrder = await order.save();

    // Log admin activity using the enhanced logger
    await adminLogger.logChangeStatus(
      req.user?._id,
      "Order",
      {
        identifiant: order.identifiant,
      },
      updatedOrder.toObject()
    );

    // Send notification about status update if status has changed
    if (global.io && previousStatus !== status) {
      setImmediate(async () => {
        try {
          console.log(`[SOCKET DEBUG] Preparing to send order status update notification`);
          console.log(`[SOCKET DEBUG] Order ID: ${updatedOrder._id}`);
          console.log(`[SOCKET DEBUG] Previous status: ${previousStatus}`);
          console.log(`[SOCKET DEBUG] New status: ${status}`);
          console.log(`[SOCKET DEBUG] Admin ID: ${req.user?._id}`);

          const socketService = require('../../services/socketService')(global.io);

          // Get admin information
          const admin = await User.findById(req.user?._id).select('firstName lastName role');
          console.log(`[SOCKET DEBUG] Admin info:`, admin ? {
            id: admin._id,
            name: `${admin.firstName} ${admin.lastName}`,
            role: admin.role
          } : 'Admin not found');

          // Get a fresh copy of the order with populated user information
          const populatedOrder = await Order.findById(updatedOrder._id).populate('user', 'firstName lastName email');
          console.log(`[SOCKET DEBUG] Populated order:`, {
            id: populatedOrder._id,
            identifiant: populatedOrder.identifiant,
            status: populatedOrder.status,
            userId: populatedOrder.user?._id
          });

          await socketService.notifyOrderStatusUpdate(populatedOrder, previousStatus, admin);
          console.log(`[SOCKET DEBUG] Notification sent for order status update ${updatedOrder._id}`);
        } catch (error) {
          console.error('Error sending order status notification:', error);
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: "Order status updated successfully",
      data: updatedOrder,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error updating order status",
      error: error.message,
    });
  }
};

const updateSubOrderStatus = async (req, res) => {
  try {
    const { suborderId, orderId } = req.params;
    const { status } = req.body;

    // Verify if the provided status is valid
    if (!Object.values(OrderStatus).includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid order status",
      });
    }

    // Find the order and populate user information
    const order = await Order.findById(orderId).populate(
      "user",
      "firstName lastName email favouriteLang"
    );

    // Find the suborder and populate package information with full hierarchy for notification routing
    const suborder = await SubOrder.findById(suborderId).populate({
      path: 'package',
      select: 'name',
      populate: {
        path: 'brand',
        select: 'name',
        populate: {
          path: 'category',
          select: 'name'
        }
      }
    });

    if (!suborder) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Store previous status for notification
    const previousStatus = suborder.status;

    // Update the suborder status
    suborder.status = status;

    const updatedSuborder = await suborder.save();

    // Log admin activity using the enhanced logger
    await adminLogger.logChangeStatus(
      req.user?._id,
      "SubOrder",
      {
        subOrderId: suborder._id,
        packageName: suborder.package?.name,
      },
      updatedSuborder.toObject()
    );

    // Send email notification
    try {
      await sendSubOrderStatusUpdateEmail(
        updatedSuborder,
        order.user,
        getSubOrderEmailTemplate
      );
    } catch (emailError) {
      console.error("Failed to send email notification:", emailError);
    }

    // Send socket notification about status update if status has changed
    if (global.io && previousStatus !== status) {
      setImmediate(async () => {
        try {
          console.log(`[SOCKET DEBUG] Preparing to send suborder status update notification`);
          console.log(`[SOCKET DEBUG] Suborder ID: ${updatedSuborder._id}`);
          console.log(`[SOCKET DEBUG] Order ID: ${order._id}`);
          console.log(`[SOCKET DEBUG] Previous status: ${previousStatus}`);
          console.log(`[SOCKET DEBUG] New status: ${status}`);
          console.log(`[SOCKET DEBUG] Admin ID: ${req.user?._id}`);

          const socketService = require('../../services/socketService')(global.io);

          // Get admin information
          const admin = await User.findById(req.user?._id).select('firstName lastName role');
          console.log(`[SOCKET DEBUG] Admin info:`, admin ? {
            id: admin._id,
            name: `${admin.firstName} ${admin.lastName}`,
            role: admin.role
          } : 'Admin not found');

          // Get a fresh copy of the order with populated user information
          const populatedOrder = await Order.findById(order._id).populate('user', 'firstName lastName email');
          console.log(`[SOCKET DEBUG] Populated order:`, {
            id: populatedOrder._id,
            identifiant: populatedOrder.identifiant,
            userId: populatedOrder.user?._id
          });

          // Get a fresh copy of the suborder with populated package information with full hierarchy for notification routing
          const populatedSuborder = await SubOrder.findById(updatedSuborder._id).populate({
            path: 'package',
            select: 'name',
            populate: {
              path: 'brand',
              select: 'name',
              populate: {
                path: 'category',
                select: 'name'
              }
            }
          });
          console.log(`[SOCKET DEBUG] Populated suborder:`, {
            id: populatedSuborder._id,
            status: populatedSuborder.status,
            packageName: populatedSuborder.package?.name
          });

          await socketService.notifySubOrderStatusUpdate(populatedOrder, populatedSuborder, previousStatus, admin);
          console.log(`[SOCKET DEBUG] Notification sent for suborder status update ${updatedSuborder._id}`);
        } catch (error) {
          console.error('Error sending suborder status notification:', error);
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: "SubOrder status updated successfully",
      data: updatedSuborder,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error updating order status",
      error: error.message,
    });
  }
};

// Controller for getting a specific order with details
const getOrderById = async (req, res) => {
  try {
    const { orderId } = req.params;

    const order = await Order.findById(orderId)
      .populate("user", "firstName lastName email phone") // User details
      .populate({
        path: "subOrders",
        populate: {
          path: "package", // Populate package instead of items.product
          select: "name price description", // Adjust fields based on your Package schema
        },
      })
      .populate("deletedBy.value", "firstName lastName") // Populate deletedBy user details
      .select("-__v") // Exclude version key
      .lean(); // Convert to plain JavaScript object

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Enhance the response with calculated fields
    const enhancedOrder = {
      ...order,
      taxRatePercentage: (order.taxRate * 100).toFixed(1),
      isDeleted: order.deletedBy && order.deletedBy.length > 0,
    };

    return res.status(200).json({
      success: true,
      data: enhancedOrder,
    });
  } catch (error) {
    // Handle specific Mongoose errors
    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid order ID",
        error: error.message,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Error fetching order",
      error: error.message,
    });
  }
};

// Export the controllers
module.exports = {
  getAllOrders,
  updateOrderStatus,
  getOrderById,
  updateSubOrderStatus,
};
