'use client'
import { Typography } from '@material-tailwind/react';
import React from 'react';
import { BLUECHECKsvg, ZTECHLOGOsvg } from '../../icons/svgIcons';
import Link from 'next/link';
import { BsArrowUpRight } from 'react-icons/bs';

const solutionsData = [
    {
        title: 'solutionsData.0.title',
        idealFor: 'solutionsData.0.idealFor',
        advantages: 'solutionsData.0.advantages',
        particularities: 'solutionsData.0.particularities',
    },
    {
        title: 'solutionsData.1.title',
        idealFor: 'solutionsData.1.idealFor',
        advantages: 'solutionsData.1.advantages',
        particularities: 'solutionsData.1.particularities',
    },
    {
        title: 'solutionsData.2.title',
        idealFor: 'solutionsData.2.idealFor',
        advantages: 'solutionsData.2.advantages',
        particularities: 'solutionsData.2.particularities',
    },
    {
        title: 'solutionsData.3.title',
        idealFor: 'solutionsData.3.idealFor',
        advantages: 'solutionsData.3.advantages',
        particularities: 'solutionsData.3.particularities',
    },
    {
        title: 'solutionsData.4.title',
        idealFor: 'solutionsData.4.idealFor',
        advantages: 'solutionsData.4.advantages',
        particularities: 'solutionsData.4.particularities',
    },
    {
        title: 'solutionsData.5.title',
        idealFor: 'solutionsData.5.idealFor',
        advantages: 'solutionsData.5.advantages',
        particularities: 'solutionsData.5.particularities',
    },
];

const OurSolutionsGuide = ({ t }) => {
    return (
        <section className="w-full bg-white font-inter">
            <div className="max-w-[1400px] mx-auto px-4 py-10 md:py-14">
                <Typography
                    variant='h2'
                    className="text-2xl md:text-4xl font-semibold flex flex-row gap-x-2 justify-center text-center mb-6 md:mb-10 font-inter">
                    <span className="text-black">{t('web_hosting_solutions.part1')} </span>
                    <span className="text-blue-500 font-bold">{t('web_hosting_solutions.highlight')}</span>
                    <span className="text-black">{t('web_hosting_solutions.part2')}</span>
                </Typography>
                <p className="text-center text-gray-600 mb-8 md:mb-12 max-w-3xl mx-auto">
                    {t('web_hosting_overview')}
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {solutionsData.map((solution, index) => (
                        <div
                            key={index}
                            className="p-2 pb-0 bg-[#497EF70F] flex flex-col gap-y-2 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                        >
                            <div className="flex flex-col bg-white p-4 rounded-lg shadow-sm flex-grow">
                                <div className="flex gap-x-4 items-center justify-start">
                                    <span className='items-center flex my-auto pb-1'>
                                        <ZTECHLOGOsvg />
                                    </span>
                                    <Typography variant='h3'
                                        className='font-semibold text-base  text-gray-900 '>
                                        {t(solution.title)}
                                    </Typography>
                                </div>
                                <hr className='my-4' />
                                <div className="flex flex-col gap-y-4">
                                    <div className="flex gap-x-2">
                                        <span>
                                            <BLUECHECKsvg />
                                        </span>
                                        <div className="flex">
                                            <p className="text-gray-700 text-sm leading-5">
                                                <span className="font-semibold text-sm">
                                                    {t('ideal_for')}
                                                </span>
                                                {t(solution.idealFor)}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex gap-x-2">
                                        <span>
                                            <BLUECHECKsvg />
                                        </span>
                                        <div className="flex">
                                            <p className="text-gray-700 text-sm  leading-5">
                                                <span className="font-semibold text-sm">
                                                    {t('advantages')}
                                                </span>
                                                {t(solution.advantages)}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex gap-x-2">
                                        <span>
                                            <BLUECHECKsvg />
                                        </span>
                                        <div className="flex">
                                            <p className="text-gray-700 text-sm leading-5">
                                                <span className="text-sm font-semibold">
                                                    {t('features')}
                                                </span>
                                                {t(solution.particularities)}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="w-full flex items-center justify-center py-2">
                                <Link
                                    href="/hosting"
                                    className="text-black text-sm font-semibold hover:underline flex gap-x-2 text-center">
                                    <span className='uppercase text-sm'>{t('see_offers')}</span>
                                    <BsArrowUpRight className='w-4' />
                                </Link>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default OurSolutionsGuide;
