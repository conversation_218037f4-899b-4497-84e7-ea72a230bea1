import Image from "next/image";
import { useTranslations } from "next-intl";
import { CheckIcon, X } from "lucide-react";
import { getLocalizedContent } from "@/app/helpers/helpers";

const SSLCard = ({ plan, onSelectPlan, locale, billingPeriod = "1year" }) => {
  const t = useTranslations("SSL2");

  // Helper functions
  const getSpecificationIcon = (value) => {
    if ((typeof value === 'string' && (
        value.toLowerCase().includes('n/a') ||
        value.toLowerCase().includes('none') ||
        value.toLowerCase().includes('aucune')
      ))
    ) {
      return <X className="h-4 w-4 text-red-500 mr-2 flex-shrink-0 mt-0.5" />;
    }
    return <CheckIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />;
  };

  // Get discount percentage if available
  const getDiscountPercentage = (plan) => {
    if (!plan || !plan.discounts || !plan.discounts.length) return 0;

    const selectedPeriod = billingPeriod === "month" ? 1 :
                         billingPeriod === "1year" ? 12 :
                         billingPeriod === "2years" ? 24 : 36;
    const discount = plan.discounts.find(d => d.period === selectedPeriod);
    return discount ? discount.percentage : 0;
  };

  // Calculate the price with period
  const getPriceWithPeriod = (plan) => {
    if (!plan || !plan.price) return 0;
    const selectedPeriod = billingPeriod === "month" ? 1 :
                         billingPeriod === "1year" ? 12 :
                         billingPeriod === "2years" ? 24 : 36;
    return plan.price * selectedPeriod;
  };

  // Calculate the final price after applying discount
  const getFinalPrice = (plan) => {
    if (!plan || !plan.price) return 0;

    const priceWithPeriod = getPriceWithPeriod(plan);
    const discountPercentage = getDiscountPercentage(plan);

    if (discountPercentage > 0) {
      return priceWithPeriod * (1 - discountPercentage / 100);
    }

    return priceWithPeriod;
  };

  // Helper to get SSL type category
  const getTypeCategory = (planType) => {
    if (!planType) return "DV";
    const normalizedType = planType.toUpperCase().replace('MULTI-DOMAINS', 'MULTI-DOMAIN');
    if (normalizedType.includes("WILDCARD")) return "Wildcard";
    if (normalizedType.includes("MULTI-DOMAIN")) return "Multi-Domain";
    if (normalizedType.includes("EV")) return "EV";
    if (normalizedType.includes("OV")) return "OV";
    return "DV";
  };

  return (
    <div className="flex flex-col bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl transform hover:-translate-y-1">
      {/* Card Header */}
      <div className="relative pb-3 py-4 px-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 via-white to-blue-50">
        <div className="flex flex-col">
          {/* Brand Logo and Type Badge */}
          <div className="flex items-center justify-between mb-3">
            <div className="relative w-28 h-14">
              <Image
                src={`/images/ssl_brands/${plan.brandObj.name.toLowerCase()}.svg`}
                alt={`${plan.brandObj.name} logo`}
                fill
                sizes="(max-width: 768px) 100px, 150px"
                style={{ objectFit: "contain" }}
                className="object-left"
              />
            </div>
            <span className="text-sm font-semibold px-3 py-1 rounded-full bg-blue-100 text-blue-600">
              {plan.sslType || getTypeCategory(plan.type)}
            </span>
          </div>

          {/* Plan Name */}
          <h3 className="text-xl font-bold text-gray-900 mb-3 text-center uppercase min-h-[3.7rem] flex items-center justify-center">
            {getLocalizedContent(plan, "name", locale)}
          </h3>

          {/* Price with discount */}
          <div className="flex flex-col items-center">
            <div className="flex items-baseline">
              <span className="text-2xl font-bold text-gray-900">
                {getFinalPrice(plan).toFixed(2)}
              </span>
              <span className="text-gray-500 ml-1">MAD</span>
            </div>
            {getDiscountPercentage(plan) > 0 && (
              <div className="mt-1 flex items-center">
                <span className="text-sm text-gray-500 line-through mr-2">
                  {getPriceWithPeriod(plan).toFixed(2)} MAD
                </span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full font-medium">
                  -{getDiscountPercentage(plan)}%
                </span>
              </div>
            )}
            <p className="text-sm text-gray-600 mt-3">
              {billingPeriod === "month" ? t("pricing.duration_info_month") :
               billingPeriod === "1year" ? t("pricing.duration_info") :
               billingPeriod === "2years" ? t("pricing.duration_info_2years") :
               t("pricing.duration_info_3years")}
            </p>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <div className="px-6 py-4 border-b border-gray-100">
        <button
          onClick={() => onSelectPlan(plan)}
          className="w-full py-3 px-4 text-center bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
        >
          {t("actions.select_plan")}
        </button>
      </div>

      {/* Features List */}
      <div className="flex-1 px-6 py-4">
        <ul className="space-y-3">
          {plan.specifications && plan.specifications.map((spec, i) => (
            <li key={i} className="flex items-start min-h-[40px]">
              {getSpecificationIcon(typeof spec === 'object'
                ? getLocalizedContent(spec, "value", locale)
                : spec)}
              <span className="text-gray-600 text-sm">
                {typeof spec === 'object'
                  ? getLocalizedContent(spec, "value", locale)
                  : spec}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default SSLCard;
