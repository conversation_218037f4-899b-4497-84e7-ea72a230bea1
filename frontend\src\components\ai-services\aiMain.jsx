'use client'
import { Typography } from '@material-tailwind/react'
import React from 'react'
import { ArrowRightIcon, PhoneIcon } from '@heroicons/react/24/solid'
import Link from 'next/link'
import { useRef } from 'react'
import { BsArrowUpRight } from "react-icons/bs";
import WhatWeOffers2 from './whatWeOffers2'
import Image from 'next/image'
import whyUsAIimg from "/public/images/ai/ai-why-us.jpg";
import humanInteractAI from "/public/images/ai/human-interact-ai.png";
import CTAButtons from '../home/<USER>'
import { motion } from "framer-motion";
import Section from '../home/<USER>'


const benefits = [
    {
        title: "benefits.0.title",
        description: "benefits.0.description"
    },
    {
        title: "benefits.1.title",
        description: "benefits.2.description"
    },
    {
        title: "benefits.2.title",
        description: "benefits.2.description"
    },
    {
        title: "benefits.3.title",
        description: "benefits.3.description"
    }
]

const offers = [
    {
        // icon: "/images/ai/check-mark.png",
        // isImg: true,
        title: "offers.0.title",
        items: [
            "offers.0.items.0",
            "offers.0.items.1",
            "offers.0.items.2",
            "offers.0.items.3",
        ],
    },
    {
        // icon: "/images/ai/check-mark.png",
        // isImg: true,
        // icon: <LOCKsvg />,
        title: "offers.1.title",
        items: [
            "offers.1.items.0",
            "offers.1.items.1",
            "offers.1.items.2",
            "offers.1.items.3",
        ],
    },
    {
        // icon: "/images/ai/check-mark.png",
        // isImg: true,
        title: "offers.2.title",
        items: [
            "offers.2.items.0",
            "offers.2.items.1",
            "offers.2.items.2",
            "offers.2.items.3",
        ],
    },
    {
        // icon: "/images/ai/check-mark.png",
        // isImg: true,
        title: "offers.3.title",
        items: [
            "offers.3.items.0",
            "offers.3.items.1",
            "offers.3.items.2",
            // "offers.3.items.3",
        ],
    },
    {
        // icon: "/images/ai/check-mark.png",
        // isImg: true,
        title: "offers.4.title",
        items: [
            "offers.4.items.0",
            "offers.4.items.1",
            "offers.4.items.2",
        ],
    },

    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offers.7.title",
        items: [
            "offers.7.items.0",
            "offers.7.items.1",
            "offers.7.items.2",
        ],
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offers.6.title",
        items: [
            "offers.6.items.0",
            "offers.6.items.1",
        ],
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offers.8.title",
        items: [
            "offers.8.items.0",
            "offers.8.items.1",
            "offers.8.items.2",
        ],
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offers.9.title",
        items: [
            "offers.9.items.3",
            "offers.9.items.2",
            "offers.9.items.1",
            "offers.9.items.0",
        ],
    },
    {
        icon: "/images/ai/check.png",
        isImg: true,
        title: "offers.5.title",
        items: [
            "offers.5.items.0",
            "offers.5.items.1",
            "offers.5.items.2",
            "offers.5.items.3",
        ],
    },
];


function AiMain({ t, animations }) {

    const rowRefs = useRef([]);

    const scrollRow = (rowIndex, direction) => {
        const row = rowRefs.current[rowIndex];
        if (row) {
            const scrollAmount = direction === "left" ? -300 : 300;
            row.scrollBy({ left: scrollAmount, behavior: "smooth" });
        }
    };


    return (
        <Section className="flex flex-col gap-y-0 p-0">
            <div className="max-w-[1400px] mx-auto flex rounded-2xl md:p-10 p-2">
                <motion.div
                    variants={animations?.fadeInUp}
                    initial="hidden"
                    animate="visible"
                    className="flex md:flex-row flex-col rounded-2xl md:px-8 md:py-20 py-5 gap-x-10 shadow-inner relative bg-cover bg-no-repeat bg-center"
                    // style={{
                    //     backgroundImage: "url('/images/services/bg-img.svg')",
                    // }}
                    style={{
                        backgroundImage: "url('/images/background_lines.svg.png'), linear-gradient(100.13deg, #497EF7 0.96%, #020060 98.7%)",
                        backgroundRepeat: "no-repeat",
                        backgroundSize: "cover",
                        backgroundPosition: "center"
                    }}
                >
                    <div className="flex flex-col items-center justify-center text-white 2xl:w-2/3 xl:w-3/5 md:w-1/2 md:py-8 relative text-center">
                        <Typography
                            variant="h1"
                            className="md:text-5xl text-2xl font-inter font-bold"
                        >
                            {t('why_choose_ai_services')}
                        </Typography>
                        <p className="my-5 md:p-0 px-2 md:text-lg text-base">
                            {t('ai_solutions_overview')}
                        </p>
                    </div>

                    <div className="rounded-xl relative flex justify-center items-center 2xl:w-1/3 xl:w-2/5 md:w-1/2 w-2/3 mx-auto">
                        {/* <picture>
                            <source
                                srcSet="/images/ai/human-interact-ai.png"
                                media="(max-width: 768px)"
                            />

                            <img
                                loading="lazy"
                                src="/images/ai/human-interact-ai.png"
                                alt="img"
                                className="mx-auto w-full rounded-md h-full"
                            />
                        </picture> */}
                        <Image
                            src={humanInteractAI}
                            alt="Data center"
                            width={900}
                            height={900}
                            quality={100}
                            className="h-auto rounded-xl object-cover m-auto"
                        />
                    </div>
                </motion.div>
            </div>

            <WhatWeOffers2 offersData={offers} t={t} />

            <div className='max-w-[1400px] mx-auto  flex flex-col gap-y-10 p-0'>
                <div className="md:p-16 p-4 flex flex-col md:flex-row items-center justify-between gap-8">
                    <div className="w-full">
                        <Typography
                            variant='h2'
                            className="text-3xl font-semibold normal-case mb-6 text-black w-full font-outfit">
                            {t('why_ztech_ai')}
                        </Typography>
                        <p className='max-w-md normal-case'>
                            {t('why_ztech_ai_subtitle')}
                        </p>
                        <div className="mt-10 flex flex-col border-l-2 border-gray-700 px-4 py-2 gap-y-6 max-w-lg">
                            {benefits.map((benefit, index) => (
                                <div key={index} className="flex flex-col gap-y-2">
                                    <Typography
                                        variant="h5"
                                        className="text-secondary font-inter font-medium text-xl"
                                    >
                                        {t(benefit.title)}
                                    </Typography>
                                    <p className="text-gray-700">{t(benefit.description)}</p>
                                </div>
                            ))}
                        </div>
                        <div className="mt-8">
                            <CTAButtons t={t} />
                        </div>
                    </div>
                    <div className="border rounded-md shadow-inner flex items-center justify-center w-full">
                        <Image
                            src={whyUsAIimg}
                            alt="Data center"
                            width={900}
                            height={900}
                            className="h-auto rounded-xl object-cover m-auto"
                        />
                    </div>
                </div>

                
            </div>
        </Section >
    )
}

export default AiMain;