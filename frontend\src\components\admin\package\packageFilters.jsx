import React from "react";
import { Filter } from "lucide-react";

export default function PackageFilters({
  brandFilter,
  setBrandFilter,
  stateFilter,
  setStateFilter,
  brands,
}) {
  return (
    <div className="flex flex-wrap gap-3 items-center">
      <div className="flex items-center">
        <span className="mr-2 text-gray-500 flex items-center">
          <Filter size={16} className="mr-1" />
          <span className="text-sm font-medium">Brand:</span>
        </span>
        <select
          value={brandFilter}
          onChange={(e) => setBrandFilter(e.target.value)}
          className="block w-full py-2 pl-3 pr-10 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white focus:bg-white transition-all duration-200"
        >
          <option value="all">All Brands</option>
          {brands.map((brand) => (
            <option key={brand._id} value={brand.name}>
              {brand.name}
            </option>
          ))}
        </select>
      </div>
      <div className="flex items-center">
        <span className="mr-2 text-gray-500 flex items-center">
          <span className="text-sm font-medium">Status:</span>
        </span>
        <select
          value={stateFilter}
          onChange={(e) => setStateFilter(e.target.value)}
          className="block w-full py-2 pl-3 pr-10 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white focus:bg-white transition-all duration-200"
        >
          <option value="all">All Statuses</option>
          <option value="published">Published</option>
          <option value="draft">Draft</option>
        </select>
      </div>
    </div>
  );
}
