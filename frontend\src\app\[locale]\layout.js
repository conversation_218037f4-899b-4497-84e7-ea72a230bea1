import { Inter } from 'next/font/google';
import '../../globals.css';
import { notFound } from 'next/navigation';
import { locales } from '../../../i18n';

const inter = Inter({ subsets: ['latin'] });

export function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

export default function LocaleLayout({ children, params: { locale } }) {
  // Validate that the incoming locale is valid
  if (!locales.includes(locale)) {
    notFound();
  }

  return (
    <html lang={locale}>
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}