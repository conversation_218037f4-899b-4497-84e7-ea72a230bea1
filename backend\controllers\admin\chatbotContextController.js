const ChatbotContext = require('../../models/ChatbotContext');

const chatbotContextController = {
  // Get the default chatbot context
  getContext: async (req, res) => {
    try {
      const context = await ChatbotContext.getDefaultContext();
      res.json(context);
    } catch (error) {
      console.error('Error getting chatbot context:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Update the default chatbot context
  updateContext: async (req, res) => {
    try {
      const { content } = req.body;
      
      if (!content) {
        return res.status(400).json({ error: 'Bad Request', message: 'Content is required' });
      }

      // Get the admin user from the request
      const adminUser = req.user ? req.user.email : 'admin';

      // Update the default context
      const updatedContext = await ChatbotContext.findOneAndUpdate(
        { name: 'default' },
        { 
          content,
          lastUpdated: new Date(),
          updatedBy: adminUser
        },
        { new: true, upsert: true }
      );

      res.json(updatedContext);
    } catch (error) {
      console.error('Error updating chatbot context:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  }
};

module.exports = chatbotContextController;
