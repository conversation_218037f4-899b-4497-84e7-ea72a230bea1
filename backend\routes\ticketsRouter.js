const express = require("express");
const ticketsRouter = express.Router();
const ticketController = require("../controllers/ticketController");
const {
  checkUserOrRefreshToken,
  adminMiddleware,
} = require("../midelwares/authorization");
const multer = require("multer");
const path = require("path");

// Sanitize filename
const sanitizeFilename = (filename) => {
  return filename.replace(/[^a-zA-Z0-9.]/g, "_");
};

// Multer configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, "..", "public", "images", "uploads"));
  },
  filename: function (req, file, cb) {
    const sanitizedFilename = sanitizeFilename(file.originalname);
    cb(null, Date.now() + "-" + sanitizedFilename); // Unique filename
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024, // Maximum file size: 2MB
    files: 4,
  },
});

// POST route for creating a new ticket with optional image uploads
ticketsRouter.post(
  "/",
  upload.array("newImages"), // Handle image uploads
  checkUserOrRefreshToken,
  ticketController.createTicket
);

// GET route for fetching all tickets for the logged-in user
ticketsRouter.get(
  "/user",
  checkUserOrRefreshToken,
  ticketController.getUserTickets
);

// GET route for fetching a ticket by ID
ticketsRouter.get(
  "/:id",
  checkUserOrRefreshToken,
  ticketController.getTicketById
);

// PUT route for updating a ticket with optional image uploads
ticketsRouter.put(
  "/:id",
  upload.array("newImages"), // Handle image uploads
  checkUserOrRefreshToken,
  ticketController.updateTicket
);

// DELETE route for deleting a ticket
ticketsRouter.delete(
  "/:id",
  checkUserOrRefreshToken,
  ticketController.deleteTicket
);

ticketsRouter.get("/", adminMiddleware, ticketController.getAllTickets);

ticketsRouter.put(
  "/:id/status",
  adminMiddleware,
  ticketController.updateTicketStatus
);

module.exports = ticketsRouter;
