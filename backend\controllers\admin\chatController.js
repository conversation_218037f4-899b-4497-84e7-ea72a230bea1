const ChatConversation = require('../../models/ChatConversation');

const chatController = {
  // Get all chat conversations with pagination
  getAllConversations: async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Build search query if search parameter is provided
      const searchQuery = {};

      // Filter by user ID if provided
      if (req.query.userId) {
        if (req.query.userId === 'guest') {
          // Special case to filter for guest users (where userId is null)
          searchQuery['userInfo.userId'] = { $exists: false };
        } else {
          searchQuery['userInfo.userId'] = req.query.userId;
        }
      }

      // Text search if provided
      if (req.query.search) {
        const search = req.query.search;
        searchQuery.$or = [
          { 'messages.content': { $regex: search, $options: 'i' } },
          { sessionId: { $regex: search, $options: 'i' } }
        ];
      }

      const conversations = await ChatConversation.find(searchQuery)
        .populate('userInfo.userId', 'firstName lastName email photo identifiant')
        .sort({ lastActivity: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const total = await ChatConversation.countDocuments(searchQuery);

      res.json({
        data: conversations,
        pagination: {
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          itemsPerPage: limit
        }
      });
    } catch (error) {
      console.error('Error fetching chat conversations:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Get a specific conversation by ID
  getConversationById: async (req, res) => {
    try {
      const { conversationId } = req.params;

      const conversation = await ChatConversation.findById(conversationId)
        .populate('userInfo.userId', 'firstName lastName email photo identifiant')
        .lean();

      if (!conversation) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      res.json(conversation);
    } catch (error) {
      console.error('Error fetching conversation:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Delete a specific conversation by ID
  deleteConversation: async (req, res) => {
    try {
      const { conversationId } = req.params;

      const result = await ChatConversation.findByIdAndDelete(conversationId);

      if (!result) {
        return res.status(404).json({ error: 'Conversation not found' });
      }

      res.json({ success: true, message: 'Conversation deleted successfully' });
    } catch (error) {
      console.error('Error deleting conversation:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Delete multiple conversations by IDs
  deleteMultipleConversations: async (req, res) => {
    try {
      const { conversationIds } = req.body;

      if (!conversationIds || !Array.isArray(conversationIds) || conversationIds.length === 0) {
        return res.status(400).json({ error: 'Bad Request', message: 'Conversation IDs array is required' });
      }

      const result = await ChatConversation.deleteMany({ _id: { $in: conversationIds } });

      res.json({
        success: true,
        message: `${result.deletedCount} conversations deleted successfully`
      });
    } catch (error) {
      console.error('Error deleting multiple conversations:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Delete all conversations
  deleteAllConversations: async (req, res) => {
    try {
      const result = await ChatConversation.deleteMany({});

      res.json({
        success: true,
        message: `${result.deletedCount} conversations deleted successfully`
      });
    } catch (error) {
      console.error('Error deleting all conversations:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  },

  // Get all conversations for a specific user
  getUserConversations: async (req, res) => {
    try {
      const { userId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      if (!userId) {
        return res.status(400).json({ error: 'Bad Request', message: 'User ID is required' });
      }

      let searchQuery = {};
      if (userId === 'guest') {
        // Special case to filter for guest users (where userId is null)
        searchQuery = { 'userInfo.userId': { $exists: false } };
      } else {
        searchQuery = { 'userInfo.userId': userId };
      }

      // Add text search if provided
      if (req.query.search) {
        const search = req.query.search;
        searchQuery.$or = [
          { 'messages.content': { $regex: search, $options: 'i' } },
          { sessionId: { $regex: search, $options: 'i' } }
        ];
      }

      const conversations = await ChatConversation.find(searchQuery)
        .populate('userInfo.userId', 'firstName lastName email photo identifiant')
        .sort({ lastActivity: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const total = await ChatConversation.countDocuments(searchQuery);

      res.json({
        data: conversations,
        pagination: {
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
          itemsPerPage: limit
        }
      });
    } catch (error) {
      console.error('Error fetching user chat conversations:', error);
      res.status(500).json({ error: 'Internal Server Error', message: error.message });
    }
  }
};

module.exports = chatController;
