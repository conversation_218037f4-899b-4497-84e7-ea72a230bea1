/**
 * VPS Instance Model
 * Represents an active VPS instance in the system
 */

const mongoose = require('mongoose');

const vpsInstanceSchema = new mongoose.Schema({
  // Instance identification
  instanceId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Reference to the original order
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'VPSOrder',
    required: true,
    index: true
  },

  // Reference to the suborder (if applicable)
  subOrder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubOrder',
    required: true,
    index: true
  },

  // User who owns the instance
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },

  // Instance configuration
  config: {
    name: {
      type: String,
      required: true
    },
    provider: {
      type: String,
      required: true,
      enum: ['contabo', 'digitalocean', 'vultr', 'linode']
    },
    planId: {
      type: String,
      required: true
    },
    region: {
      type: String,
      required: true
    },
    operatingSystem: {
      type: String,
      required: true
    },
    // Resource specifications
    specifications: {
      cpu: Number,
      ram: Number, // in GB
      storage: Number, // in GB
      bandwidth: Number // in GB
    }
  },

  // Network configuration
  network: {
    ipv4: {
      type: String,
      index: true
    },
    ipv6: String,
    vHostName: String,
    hostname: String,
    ports: [{
      port: Number,
      protocol: {
        type: String,
        enum: ['tcp', 'udp']
      },
      description: String
    }]
  },

  // Access credentials
  access: {
    rootPassword: String, // Should be encrypted in production
    sshKeys: [String],
    sshPort: {
      type: Number,
      default: 22
    },
    lastPasswordReset: Date,
    passwordSecretId: Number // Contabo secret ID for the password
  },

  // Instance status
  status: {
    type: String,
    enum: [
      'creating',
      'running',
      'stopped',
      'restarting',
      'suspended',
      'terminated',
      'error'
    ],
    default: 'creating',
    index: true
  },

  // Provider-specific data
  providerData: {
    providerInstanceId: String,
    providerStatus: String,
    providerRegion: String,
    additionalData: mongoose.Schema.Types.Mixed
  },

  // Usage statistics
  usage: {
    lastUpdated: Date,
    cpu: {
      current: Number, // percentage
      average: Number,
      peak: Number
    },
    memory: {
      used: Number, // in GB
      total: Number,
      percentage: Number
    },
    disk: {
      used: Number, // in GB
      total: Number,
      percentage: Number
    },
    network: {
      inbound: Number, // in GB
      outbound: Number,
      total: Number
    }
  },

  // Billing information
  billing: {
    monthlyPrice: Number,
    hourlyPrice: Number,
    currency: {
      type: String,
      default: 'MAD'
    },
    billingCycle: {
      type: String,
      enum: ['hourly', 'monthly', 'yearly'],
      default: 'monthly'
    },
    nextBillingDate: Date,
    lastBilledDate: Date
  },

  // Backup configuration
  backups: {
    enabled: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'weekly'
    },
    retention: {
      type: Number,
      default: 7 // days
    },
    lastBackup: Date,
    nextBackup: Date
  },

  // Monitoring configuration
  monitoring: {
    enabled: {
      type: Boolean,
      default: true
    },
    alerts: [{
      type: {
        type: String,
        enum: ['cpu', 'memory', 'disk', 'network', 'uptime']
      },
      threshold: Number,
      enabled: {
        type: Boolean,
        default: true
      }
    }],
    uptime: {
      percentage: Number,
      lastCheck: Date
    }
  },

  // Lifecycle dates
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  startedAt: Date,
  stoppedAt: Date,
  suspendedAt: Date,
  terminatedAt: Date,

  // Maintenance windows
  maintenance: {
    scheduled: [{
      startTime: Date,
      endTime: Date,
      description: String,
      type: {
        type: String,
        enum: ['system', 'security', 'hardware']
      }
    }],
    lastMaintenance: Date
  },

  // Tags and metadata
  metadata: {
    tags: [String],
    notes: String,
    environment: {
      type: String,
      enum: ['production', 'staging', 'development', 'testing'],
      default: 'production'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
vpsInstanceSchema.index({ user: 1, status: 1 });
vpsInstanceSchema.index({ 'config.provider': 1, status: 1 });
vpsInstanceSchema.index({ 'network.ipv4': 1 });
vpsInstanceSchema.index({ createdAt: -1 });

// Virtual for instance age
vpsInstanceSchema.virtual('age').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Virtual for uptime
vpsInstanceSchema.virtual('uptime').get(function() {
  if (!this.startedAt) return 0;
  const endTime = this.stoppedAt || Date.now();
  return endTime - this.startedAt.getTime();
});

// Pre-save middleware
vpsInstanceSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static methods
vpsInstanceSchema.statics.findByUser = function(userId, status = null) {
  const query = { user: userId };
  if (status) {
    query.status = status;
  }
  return this.find(query).sort({ createdAt: -1 });
};

vpsInstanceSchema.statics.findByProvider = function(provider, status = null) {
  const query = { 'config.provider': provider };
  if (status) {
    query.status = status;
  }
  return this.find(query).sort({ createdAt: -1 });
};

vpsInstanceSchema.statics.getInstanceStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

// Instance methods
vpsInstanceSchema.methods.updateStatus = function(newStatus) {
  const oldStatus = this.status;
  this.status = newStatus;
  
  // Set specific timestamps based on status
  const now = new Date();
  switch (newStatus) {
    case 'running':
      if (oldStatus !== 'running') {
        this.startedAt = now;
      }
      break;
    case 'stopped':
      this.stoppedAt = now;
      break;
    case 'suspended':
      this.suspendedAt = now;
      break;
    case 'terminated':
      this.terminatedAt = now;
      break;
  }
  
  return this.save();
};

vpsInstanceSchema.methods.updateUsage = function(usageData) {
  this.usage = {
    ...this.usage,
    ...usageData,
    lastUpdated: new Date()
  };
  return this.save();
};

vpsInstanceSchema.methods.addMaintenanceWindow = function(maintenance) {
  this.maintenance.scheduled.push(maintenance);
  return this.save();
};

vpsInstanceSchema.methods.isMaintenanceScheduled = function() {
  const now = new Date();
  return this.maintenance.scheduled.some(m => 
    m.startTime <= now && m.endTime >= now
  );
};

const VPSInstance = mongoose.model('VPSInstance', vpsInstanceSchema);

module.exports = VPSInstance;
