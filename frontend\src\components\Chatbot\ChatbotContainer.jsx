"use client";

import React, { useState, useEffect, useRef } from 'react';
import ChatbotButton from './ChatbotButton';
import ChatbotWindow from './ChatbotWindow';

const ChatbotContainer = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const chatWindowRef = useRef(null);
  const chatButtonRef = useRef(null);

  const toggleChatbot = () => {
    setIsOpen(!isOpen);
    
    if (isMinimized) {
      setIsMinimized(false);
    }
    
    if (!hasInteracted) {
      setHasInteracted(true);
    }
  };

  const minimizeChatbot = () => {
    setIsMinimized(true);
    setIsOpen(false);
  };

  const closeChatbot = () => {
    setIsOpen(false);
    setIsMinimized(false);
  };

  // <PERSON>le click outside to close the chatbot
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isOpen &&
        chatWindowRef.current && 
        !chatWindowRef.current.contains(event.target) &&
        chatButtonRef.current &&
        !chatButtonRef.current.contains(event.target)
      ) {
        closeChatbot();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Show notification dot on initial load
  useEffect(() => {
    if (!hasInteracted && !isOpen) {
      // Auto open effect could be added here
    }
  }, [hasInteracted, isOpen]);

  return (
    <div className="w-[46px] relative">
      <div ref={chatWindowRef} className="absolute bottom-0 right-0">
        <ChatbotWindow 
          isOpen={isOpen} 
          onClose={closeChatbot} 
          onMinimize={minimizeChatbot} 
        />
      </div>
      
      <div ref={chatButtonRef}>
        <ChatbotButton 
          onClick={toggleChatbot} 
          isMinimized={isMinimized}
          isOpen={isOpen}
        />
      </div>
    </div>
  );
};

export default ChatbotContainer;