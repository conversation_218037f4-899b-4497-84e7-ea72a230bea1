const i18next = require("i18next");
const Backend = require("i18next-fs-backend");
const Middleware = require("i18next-http-middleware");

i18next
  .use(Backend)
  .use(Middleware.LanguageDetector)
  .init({
    fallbackLng: "en",
    preload: ["en", "fr"], // Add other languages here
    backend: {
      loadPath: "./locales/{{lng}}/{{ns}}.json",
    },
    detection: {
      order: ["header", "querystring", "cookie"],
      caches: ["cookie"],
    },
  });

module.exports = i18next;
