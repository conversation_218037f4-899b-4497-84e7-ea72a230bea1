"use client";
import { Typography } from "@material-tailwind/react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import aboutUsImg from "/public/images/home/<USER>";
import { motion } from "framer-motion";
import {
  ArrowUpRight,
  Globe,
  ServerCog,
  CodeXml,
  ShieldCheck,
} from "lucide-react";

function AboutUs({ t }) {
  const features = [
    {
      icon: <Globe className="w-6 h-6" />,
      title: t("features.global_reach.title"),
      description: t("features.global_reach.description"),
    },
    {
      icon: <CodeXml className="w-6 h-6" />,
      title: t("features.expert_development.title"),
      description: t("features.expert_development.description"),
    },
    {
      icon: <ServerCog className="w-6 h-6" />,
      title: t("features.cloud_solutions.title"),
      description: t("features.cloud_solutions.description"),
    },
    {
      icon: <ShieldCheck className="w-6 h-6" />,
      title: t("features.security_first.title"),
      description: t("features.security_first.description"),
    },
  ];

  return (
    <section className="relative py-10  overflow-hidden bg-white">
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8 md:mb-16"
        >
          <span className="inline-block px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-blue-50 text-blue-600 font-medium text-xs md:text-sm mb-3 md:mb-4">
            {t("we_are")}
          </span>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-medium text-gray-900 mb-4 md:mb-6">
            ZTechEngineering
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto px-4">
            {t("web_dev_cloud_security_experts")}
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
          {/* Left Column - Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="relative"
          >
            <div className="relative h-[300px] xs:h-[400px] md:h-[500px] rounded-xl md:rounded-2xl overflow-hidden">
              <Image
                src={aboutUsImg}
                alt="Team collaboration"
                fill
                className="object-cover"
                quality={90}
                priority
              />
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-tr from-blue-900/20 to-transparent"></div>
            </div>
          </motion.div>

          {/* Right Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-6 md:space-y-8"
          >
            <Typography className="text-base md:text-lg text-gray-700 leading-relaxed px-2">
              {t("company_description")}
            </Typography>

            {/* Features Grid */}
            <div className="grid grid-cols-1 xs:grid-cols-2 gap-4 md:gap-6 mt-6 md:mt-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="p-3 md:p-4 rounded-lg md:rounded-xl bg-white shadow-sm border border-gray-100"
                >
                  <div className="w-8 h-8 md:w-10 md:h-10 rounded-lg bg-blue-50 flex items-center justify-center text-blue-600 mb-2 md:mb-3">
                    {feature.icon}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1 text-sm md:text-base">
                    {feature.title}
                  </h3>
                  <p className="text-xs md:text-sm text-gray-600">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>

            {/* CTA Button */}
            <div className="pt-4">
              <Link
                href="/about-us"
                className="group inline-flex items-center gap-2 px-4 py-2.5 md:px-6 md:py-3 rounded-full bg-blue-600 text-white text-sm md:text-base
                  hover:bg-blue-700 transition-all duration-300 shadow-lg capitalize shadow-blue-600/20"
              >
                <span>{t("our_success_story")}</span>
                <ArrowUpRight className="w-3.5 h-3.5 md:w-4 md:h-4 group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform" />
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}

export default AboutUs;
