"use client"
import React, { useState, useEffect } from 'react';
import { Briefcase, Edit, Trash2, Plus, Eye, X, CheckCircle, XCircle, FileText, Mail, Phone, Download, Clock, AlertCircle, ChevronRight, FileUser, Archive } from 'lucide-react';
import Link from 'next/link';
import JobFormModal from './components/jobFormModal/JobFormModal';
import { adminService } from '../../services/adminService';
import { toast } from 'react-toastify';
import { BACKEND_URL } from '@/app/config/constant';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';


function JobManagement() {
  const [jobs, setJobs] = useState([]);
  const [applications, setApplications] = useState({});
  const [selectedJob, setSelectedJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    department: '',
    location: '',
    type: 'full-time',
    description: '',
    requirements: [''],
    responsibilities: [''],
    benefits: [''],
    salary_range: null,
    status: 'draft'
  });

  useEffect(() => {
    fetchJobs();
  }, []);

  // Fetch applications count for all jobs
  useEffect(() => {
    if (jobs.length > 0) {
      jobs.forEach(job => {
        fetchApplications(job._id);
      });
    }
  }, [jobs]);

  useEffect(() => {
    if (selectedJob) {
      fetchApplications(selectedJob);
    }
  }, [selectedJob]);

  const fetchApplications = async (jobId) => {
    try {
      const response = await adminService.getJobApplications(jobId);
      setApplications(prevApplications => ({
        ...prevApplications,
        [jobId]: response.data
      }));
    } catch (error) {
      console.error("Error fetching job applications:", error);
      setError('Failed to fetch job applications');
    }
  };

  const fetchJobs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await adminService.getAllJobs();
      setJobs(response.data);
      // Initialize applications state based on fetched jobs if needed, or fetch separately
      // For now, keep dummy applications or remove if not needed
    } catch (err) {
      console.error("Error fetching jobs:", err);
      setError('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };



  const handleDownloadResume = async (application) => {
    try {
      // Download resume from the server using the correct backend URL
      const url = `${BACKEND_URL}${application.resumePath}`;
      const a = document.createElement('a');
      a.href = url;
      const fileName = application.resumePath.split('/').pop();
      a.download = fileName || `${application.name}_resume.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (err) {
      console.error("Error downloading resume:", err);
      toast.error('Could not download resume. The file may have been moved or deleted.');
    }
  };

  const handleDownloadCoverLetter = async (application) => {
    try {
      if (!application.coverLetterPath) {
        toast.error('No cover letter available for this application.');
        return;
      }

      // Download cover letter from the server using the correct backend URL
      const url = `${BACKEND_URL}${application.coverLetterPath}`;
      const a = document.createElement('a');
      a.href = url;
      const fileName = application.coverLetterPath.split('/').pop();
      a.download = fileName || `${application.name}_cover_letter.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (err) {
      console.error("Error downloading cover letter:", err);
      toast.error('Could not download cover letter. The file may have been moved or deleted.');
    }
  };

  // Function to download all application files for a job as a zip
  const downloadAllFilesForJob = async (jobId, jobTitle) => {
    try {
      // Check if applications are loaded for this job
      if (!applications[jobId] || applications[jobId].length === 0) {
        // If not loaded, fetch them first
        await fetchApplications(jobId);
      }

      // If still no applications, show message
      if (!applications[jobId] || applications[jobId].length === 0) {
        toast.info("No applications to download for this job.");
        return;
      }

      // Show loading toast
      const loadingToastId = toast.loading("Preparing files for download...");

      // Create a new zip file
      const zip = new JSZip();

      // Track download promises
      const downloadPromises = [];

      // Process each application
      for (const application of applications[jobId]) {
        // Create a folder for each applicant
        const folderName = application.name.replace(/[^a-z0-9]/gi, '-').toLowerCase();
        const folder = zip.folder(folderName);

        // Add resume if available
        if (application.resumePath) {
          const resumePromise = fetch(`${BACKEND_URL}${application.resumePath}`)
            .then(response => {
              if (!response.ok) throw new Error(`Failed to fetch resume: ${response.statusText}`);
              return response.blob();
            })
            .then(blob => {
              const fileName = application.resumePath.split('/').pop();
              folder.file(fileName, blob);
            })
            .catch(error => {
              console.error(`Error downloading resume for ${application.name}:`, error);
              // Continue with other files even if one fails
            });

          downloadPromises.push(resumePromise);
        }

        // Add cover letter if available
        if (application.coverLetterPath) {
          const coverLetterPromise = fetch(`${BACKEND_URL}${application.coverLetterPath}`)
            .then(response => {
              if (!response.ok) throw new Error(`Failed to fetch cover letter: ${response.statusText}`);
              return response.blob();
            })
            .then(blob => {
              const fileName = application.coverLetterPath.split('/').pop();
              folder.file(fileName, blob);
            })
            .catch(error => {
              console.error(`Error downloading cover letter for ${application.name}:`, error);
              // Continue with other files even if one fails
            });

          downloadPromises.push(coverLetterPromise);
        }
      }

      // Wait for all downloads to complete
      await Promise.all(downloadPromises);

      // Generate the zip file
      const zipBlob = await zip.generateAsync({ type: 'blob' });

      // Create a meaningful filename with job title and date
      const jobTitleForFilename = jobTitle.replace(/[^a-z0-9]/gi, '-').toLowerCase();
      const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const filename = `${jobTitleForFilename}-applications-${date}.zip`;

      // Save the zip file
      saveAs(zipBlob, filename);

      // Update toast
      toast.update(loadingToastId, {
        render: "All files downloaded successfully!",
        type: "success",
        isLoading: false,
        autoClose: 3000
      });
    } catch (error) {
      console.error("Error creating zip file:", error);
      toast.error("Failed to download files. Please try again.");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);

    try {
      const jobData = {
        ...formData,
        requirements: formData.requirements.filter(r => {
          if (typeof r === 'object') {
            return r.en.trim() || r.fr.trim();
          }
          return r.trim();
        }),
        responsibilities: formData.responsibilities.filter(r => {
          if (typeof r === 'object') {
            return r.en.trim() || r.fr.trim();
          }
          return r.trim();
        }),
        benefits: formData.benefits.filter(b => {
          if (typeof b === 'object') {
            return b.en.trim() || b.fr.trim();
          }
          return b.trim();
        })
      };

      if (editingId) {
        await adminService.updateJob(editingId, jobData);
      } else {
        await adminService.addJob(jobData);
      }

      // Refresh the job list after adding/updating
      fetchJobs();

      setFormData({
        title: '',
        department: '',
        location: '',
        type: 'full-time',
        description: '',
        requirements: [''],
        responsibilities: [''],
        benefits: [''],
        salary_range: null,
        status: 'draft'
      });
      setShowForm(false);
      setEditingId(null);
    } catch (err) {
      console.error("Error submitting job form:", err);
      setError(err.response?.data?.message || 'Failed to save job');
    }
  };

  const handleEdit = (job) => {
    console.log("Editing job:", job);
    setFormData({
      title: job.title,
      department: job.department,
      location: job.location,
      type: job.type,
      description: job.description,
      requirements: job.requirements,
      responsibilities: job.responsibilities,
      benefits: job.benefits,
      salary_range: job.salary_range,
      status: job.status
    });
    setEditingId(job._id);
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    console.log("Deleting job with ID:", id);
    if (!window.confirm('Are you sure you want to delete this job listing?')) return;

    try {
      await adminService.deleteJob(id);
      // Refresh the job list after deleting
      fetchJobs();
    } catch (err) {
      console.error("Error deleting job:", err);
      setError('Failed to delete job');
    }
  };

  const handleArrayInput = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const toggleStatus = async (id, currentStatus) => {
    try {
      const newStatus = currentStatus === 'published' ? 'draft' : 'published';
      // Simulate updating job status
      // setJobs(jobs.map(job => job._id === id ? { ...job, status: newStatus } : job));
      await adminService.updateJob(id, { status: newStatus });
      // Refresh the job list after updating status
      fetchJobs();
    } catch (err) {
      console.error("Error toggling job status:", err);
      setError('Failed to update job status');
    }
  };

  const updateApplicationStatus = async (applicationId, newStatus) => {
    try {
      await adminService.updateApplicationStatus(applicationId, { status: newStatus });

      // Update the local state to reflect the change
      setApplications(prevApplications => {
        const updatedApplications = { ...prevApplications };

        // Find which job contains this application
        Object.keys(updatedApplications).forEach(jobId => {
          updatedApplications[jobId] = updatedApplications[jobId].map(app =>
            app._id === applicationId ? { ...app, status: newStatus } : app
          );
        });

        return updatedApplications;
      });
    } catch (err) {
      console.error("Error updating application status:", err);
      setError('Failed to update application status');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Job Listings</h2>
        <button
          onClick={() => {
            setShowForm(true);
            setEditingId(null);
            setFormData({
              title: '',
              department: '',
              location: '',
              type: 'full-time',
              description: '',
              requirements: [''],
              responsibilities: [''],
              benefits: [''],
              salary_range: null,
              status: 'draft'
            });
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Job Listing
        </button>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg flex items-center">
          <AlertCircle className="h-5 w-5 mr-2" />
          {error}
        </div>
      )}

      {/* Job Form Modal */}
      <JobFormModal
        showForm={showForm}
        setShowForm={setShowForm}
        formData={formData}
        setFormData={setFormData}
        handleSubmit={handleSubmit}
        editingId={editingId}
        setEditingId={setEditingId}
        handleArrayInput={handleArrayInput}
        addArrayItem={addArrayItem}
        removeArrayItem={removeArrayItem}
        error={error}
      />

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Job Title
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Department
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Applications
              </th>
              <th className="px-6 py-3 bg-gray-50"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {jobs.map((job) => (
              <React.Fragment key={job._id}>
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Briefcase className="h-5 w-5 text-gray-500 mr-3" />
                      <div className="text-sm font-medium text-gray-900">
                        {typeof job.title === 'object' ? job.title.en : job.title}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{job.department}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{job.location}</div>
                    <div className="text-sm text-gray-500">{job.type}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        job.status === 'published' ? 'bg-green-100 text-green-800' :
                        job.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {job.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <Link href={`/admin/jobs/${job._id}/applications`}>
                    <span className='text-blue-400 hover:text-blue-200 flex items-center gap-2 hover:underline '>
                      <FileUser className="h-6 w-6" />
                      {applications[job._id]?.length || 0} Applications
                      <ChevronRight className="h-4 w-4 inline-block ml-1" />
                    </span>
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEdit(job)}
                      className="text-indigo-600 hover:text-indigo-900 mr-4"
                      title="Edit Job"
                    >
                      <Edit className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(job._id)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete Job"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
                {selectedJob === job._id && applications[job._id] && (
                  <tr>
                    <td colSpan="6" className="px-6 py-4 bg-gray-50">
                      <div className="flex justify-between items-center mb-4">
                        <h4 className="text-lg font-semibold">Applications for {typeof job.title === 'object' ? job.title.en : job.title}</h4>
                        {applications[job._id]?.length > 0 && (
                          <button
                            onClick={() => downloadAllFilesForJob(job._id, typeof job.title === 'object' ? job.title.en : job.title)}
                            className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm flex items-center hover:bg-blue-700 transition-colors"
                          >
                            <Archive className="h-4 w-4 mr-1" /> Download All Files
                          </button>
                        )}
                      </div>
                      {applications[job._id].length === 0 ? (
                        <p className="text-gray-600">No applications yet.</p>
                      ) : (
                        <div className="space-y-4">
                          {applications[job._id].map(application => (
                            <div key={application._id} className="bg-white p-4 rounded-lg shadow">
                              <div className="flex justify-between items-center mb-2">
                                <h5 className="text-md font-bold">{application.name}</h5>
                                <span
                                  className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    application.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                    application.status === 'reviewed' ? 'bg-blue-100 text-blue-800' :
                                    application.status === 'interviewed' ? 'bg-purple-100 text-purple-800' :
                                    application.status === 'hired' ? 'bg-green-100 text-green-800' :
                                    'bg-red-100 text-red-800'
                                  }`}
                                >
                                  {application.status}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 flex items-center mb-1">
                                <Mail className="h-4 w-4 mr-2" /> {application.email}
                              </p>
                              {application.phone && (
                                <p className="text-sm text-gray-600 flex items-center mb-1">
                                  <Phone className="h-4 w-4 mr-2" /> {application.phone}
                                </p>
                              )}
                              <p className="text-sm text-gray-600 flex items-center mb-1">
                                <Clock className="h-4 w-4 mr-2" /> Applied on: {new Date(application.appliedAt).toLocaleDateString()}
                              </p>
                              <div className="mt-3 flex flex-wrap items-center gap-4">
                                <button
                                  onClick={() => handleDownloadResume(application)}
                                  className="text-blue-600 hover:text-blue-900 flex items-center text-sm"
                                >
                                  <Download className="h-4 w-4 mr-1" /> Download Resume
                                </button>

                                {application.coverLetterPath && (
                                  <button
                                    onClick={() => handleDownloadCoverLetter(application)}
                                    className="text-green-600 hover:text-green-900 flex items-center text-sm"
                                  >
                                    <Download className="h-4 w-4 mr-1" /> Download Cover Letter
                                  </button>
                                )}

                                <div className="flex items-center space-x-2">
                                  <span className="text-sm text-gray-600">Status:</span>
                                  <select
                                    value={application.status}
                                    onChange={(e) => updateApplicationStatus(application._id, e.target.value)}
                                    className="text-sm border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  >
                                    <option value="pending">Pending</option>
                                    <option value="reviewed">Reviewed</option>
                                    <option value="interviewed">Interviewed</option>
                                    <option value="hired">Hired</option>
                                    <option value="rejected">Rejected</option>
                                  </select>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default JobManagement;




