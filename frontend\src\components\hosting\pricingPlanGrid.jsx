import { CheckIcon, ArrowRight, AwardIcon } from "lucide-react";
import Skeleton from "react-loading-skeleton";
import { getLocalizedContent, roundThis } from "@/app/helpers/helpers";
import cartService from "@/app/services/cartService";
import { useAuth } from "@/app/context/AuthContext";
import { useParams, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { Carousel, Typography } from "@material-tailwind/react";

export default function PricingPlanGrid({
  loading,
  plans,
  billingPeriod,
  t,
  setBillingPeriod,
  getMaxDiscount,
  isVPS = false,
}) {
  const { setCartCount } = useAuth();
  const params = useParams();
  const locale = params.locale || "en";
  const router = useRouter();

  const handleSelectPlan = async (pack) => {
    // Si c'est un VPS, rediriger vers la page de configuration
    if (isVPS) {
      const configureUrl = `/${locale}/hosting/vps/configure?plan=${pack._id}`;
      router.push(configureUrl);
      return;
    }

    // Sinon, ajouter directement au panier (comportement normal pour les autres services)
    try {
      const selectedPeriod = billingPeriod === "monthly" ? 1 : 12;
      console.log("🚀 ~ handleSelectPlan ~ billingPeriod:", selectedPeriod);

      const res = await cartService.addItemToCart({
        packageId: pack?._id,
        quantity: 1,
        period: parseInt(selectedPeriod, 10),
      });
      setCartCount(res.data.cart.cartCount);
      router.push("/client/cart");
    } catch (error) {
      if (!error.response.data.success) {
        toast.error(error.response.data.message);
      } else {
        console.log("error: adding to cart", error);
      }
    }
  };

  return (
    <>
      {/* Billing Toggle */}
      <div className="flex justify-center w-fit items-center bg-gray-100 rounded-lg p-2 mb-10 mx-auto">
        <button
          onClick={() => setBillingPeriod("monthly")}
          className={`px-4 py-2 rounded-md text-md font-medium transition-all ${
            billingPeriod === "monthly"
              ? "bg-white text-secondary shadow-sm"
              : "text-gray-700 hover:text-gray-900"
          }`}
        >
          {t("monthly")}
        </button>
        <button
          onClick={() => setBillingPeriod("yearly")}
          className={`px-4 py-2 rounded-md text-md font-medium transition-all ${
            billingPeriod === "yearly"
              ? "bg-white text-secondary shadow-sm"
              : "text-gray-700 hover:text-gray-900"
          }`}
        >
          {t("yearly")}
          <span className="ml-1 text-sm text-green-600 font-normal">
            {t("save_up_to")} {getMaxDiscount()}%
          </span>
        </button>
      </div>

      {/* Pricing Grid or Carousel */}
      <div className="mb-16">
        {loading ? (
          <>
            <div className="hidden md:grid grid-cols-3 gap-8">
              {Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="relative bg-white rounded-2xl transition-all duration-300 h-full border border-gray-200 shadow-lg p-6"
                >
                  {/* Skeleton for Plan Name */}
                  <Skeleton height={30} width={200} className="mb-4" />

                  {/* Skeleton for Plan Description */}
                  <Skeleton height={20} width={150} className="mb-2" />

                  {/* Skeleton for Savings */}
                  <Skeleton height={20} width={100} className="mb-6" />

                  {/* Skeleton for Price */}
                  <div className="flex items-center justify-center mb-6">
                    <Skeleton height={40} width={100} />
                    <Skeleton height={20} width={50} className="ml-2" />
                    <Skeleton height={20} width={50} className="ml-2" />
                  </div>

                  {/* Skeleton for Select Button */}
                  <Skeleton height={40} width="100%" className="mb-6" />

                  {/* Skeleton for Specifications */}
                  <Skeleton count={5} height={20} className="mb-4" />
                </div>
              ))}
            </div>
            <div className="md:hidden grid grid-cols-1 gap-4 mt-10">
              {Array.from({ length: 1 }).map((_, index) => (
                <div
                  key={index}
                  className="relative bg-white w-[300px] mx-auto rounded-xl transition-all duration-300 h-full border border-gray-200 shadow-lg p-4"
                >
                  {/* Skeleton for Plan Name */}
                  <Skeleton height={30} width={200} className="mb-4" />

                  {/* Skeleton for Plan Description */}
                  <Skeleton height={20} width={150} className="mb-2" />

                  {/* Skeleton for Savings */}
                  <Skeleton height={20} width={100} className="mb-6" />

                  {/* Skeleton for Price */}
                  <div className="flex items-center justify-center mb-6">
                    <Skeleton height={40} width={100} />
                    <Skeleton height={20} width={50} className="ml-2" />
                    <Skeleton height={20} width={50} className="ml-2" />
                  </div>

                  {/* Skeleton for Select Button */}
                  <Skeleton height={40} width="100%" className="mb-6" />

                  {/* Skeleton for Specifications */}
                  <Skeleton count={5} height={20} className="mb-4" />
                </div>
              ))}
            </div>
          </>
        ) : (
          <>
            {/* Desktop Grid Layout */}
            <div className="hidden md:grid grid-cols-3 gap-8">
              {plans.map((plan, index) => {
                const price = parseFloat(plan.price);
                const regularPrice = parseFloat(plan.regularPrice);
                const savings = (
                  ((regularPrice - price) / regularPrice) *
                  100
                ).toFixed(0);

                return (
                  <div
                    key={plan._id}
                    className={`relative bg-white rounded-2xl transition-all duration-300 h-full ${
                      index == 1
                        ? "ring-4 ring-secondary shadow-xl"
                        : "border border-gray-200 hover:border-secondary shadow-lg"
                    }`}
                  >
                    {index == 1 && (
                      <div className="absolute -top-5 left-0 right-0 flex justify-center">
                        <span className="bg-secondary text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                          <AwardIcon className="h-4 w-4 mr-1" />
                          {t("most_popular")}
                        </span>
                      </div>
                    )}

                    <div className="p-6">
                      <div className="text-center mb-6">
                        <h3 className="text-2xl text-left font-medium text-gray-900 mb-2">
                          {getLocalizedContent(plan, "name", locale)}
                        </h3>
                        <p className="font-normal text-left text-sm">
                          {getLocalizedContent(plan, "description", locale)}
                        </p>
                        <div className="flex flex-col my-4">
                          {savings != 0 && (
                            <span className="text-green-600 font-semibold font-inter text-center">
                              {t("save")} {savings}%
                            </span>
                          )}
                          <div className="flex items-center justify-center">
                            {savings != 0 && (
                              <span className="text-gray-600 mx-2 line-through text-sm">
                                {roundThis(regularPrice)}MAD
                              </span>
                            )}

                            <span className="text-5xl font-medium text-primary">
                              {roundThis(price)}
                            </span>
                            <span className="text-gray-800 ml-2 mt-auto text-md font-medium">
                              MAD
                            </span>
                            <span className="text-gray-800 mt-auto text-md font-medium">
                              /mo
                            </span>
                          </div>
                        </div>
                      </div>

                      <button
                        onClick={() => handleSelectPlan(plan)}
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-all flex items-center justify-center ${
                          index == 1
                            ? "bg-secondary hover:bg-purple-700 text-white"
                            : "bg-white hover:bg-gray-50 text-secondary border-2 border-secondary"
                        }`}
                      >
                        {t("select_plan")}
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </button>

                      <div className="border-t border-gray-100 mb-6"></div>

                      <ul className="space-y-4">
                        {plan.specifications.map((spec) => (
                          <li key={spec?._id} className="flex items-start">
                            <CheckIcon className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                            <span className="text-gray-600 text-sm capitalize">
                              {getLocalizedContent(spec, "value", locale)}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Mobile Carousel Layout */}
            <Carousel
              className="md:hidden"
              prevArrow={({ handlePrev }) => (
                <button
                  onClick={handlePrev}
                  className="!absolute top-2/4 left-4 -translate-y-2/4  text-black hover:bg-gray-100/45 hover:shadow-md transition-colors rounded-full p-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </button>
              )}
              nextArrow={({ handleNext }) => (
                <button
                  onClick={handleNext}
                  className="!absolute top-2/4 right-4 -translate-y-2/4  text-black hover:bg-gray-100/45 hover:shadow-md transition-colors rounded-full p-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={2}
                    stroke="currentColor"
                    className="h-6 w-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              )}
              navigation={({ setActiveIndex, activeIndex, length }) => (
                <div className="absolute bottom-4 left-1/2 z-50 flex -translate-x-1/2 gap-2">
                  {new Array(length).fill("").map((_, i) => (
                    <span
                      key={i}
                      className={`block h-2 cursor-pointer rounded-full transition-all ${
                        activeIndex === i ? "w-6 bg-black" : "w-2 bg-gray-400"
                      }`}
                      onClick={() => setActiveIndex(i)}
                    />
                  ))}
                </div>
              )}
            >
              {plans.map((plan, index) => {
                const price = parseFloat(plan.price);
                const regularPrice = parseFloat(plan.regularPrice);
                const savings = (
                  ((regularPrice - price) / regularPrice) *
                  100
                ).toFixed(0);

                return (
                  <div
                    key={plan._id}
                    className="relative bg-white rounded-2xl transition-all duration-300 h-full p-6"
                  >
                    {index == 1 && (
                      <div className="absolute -top-5 left-0 right-0 flex justify-center">
                        <span className="bg-secondary text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                          <AwardIcon className="h-4 w-4 mr-1" />
                          {t("most_popular")}
                        </span>
                      </div>
                    )}

                    <div className="p-6">
                      <div className="text-center mb-6">
                        <h3 className="text-2xl text-left font-medium text-gray-900 mb-2">
                          {getLocalizedContent(plan, "name", locale)}
                        </h3>
                        <p className="font-normal text-left text-sm">
                          {getLocalizedContent(plan, "description", locale)}
                        </p>
                        <div className="flex flex-col my-4">
                          <span className="text-green-600 font-semibold font-inter text-center">
                            {t("save")} {savings}%
                          </span>
                          <div className="flex items-center justify-center">
                            <span className="text-gray-600 mx-2 line-through text-sm">
                              {roundThis(regularPrice)}MAD
                            </span>
                            <span className="text-5xl font-medium text-primary">
                              {roundThis(price)}
                            </span>
                            <span className="text-gray-800 ml-2 mt-auto text-md font-medium">
                              MAD
                            </span>
                            <span className="text-gray-800 mt-auto text-md font-medium">
                              /mo
                            </span>
                          </div>
                        </div>
                      </div>

                      <button
                        onClick={() => handleSelectPlan(plan)}
                        className={`w-full py-3 px-4 rounded-lg font-medium transition-all flex items-center justify-center ${
                          index == 1
                            ? "bg-secondary hover:bg-purple-700 text-white"
                            : "bg-white hover:bg-gray-50 text-secondary border-2 border-secondary"
                        }`}
                      >
                        {t("select_plan")}
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </button>

                      <div className="border-t border-gray-100 mb-6"></div>

                      <ul className="space-y-4">
                        {plan.specifications.map((spec) => (
                          <li key={spec?._id} className="flex items-start">
                            <CheckIcon className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                            <span className="text-gray-600 text-sm capitalize">
                              {getLocalizedContent(spec, "value", locale)}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                );
              })}
            </Carousel>
          </>
        )}
      </div>
    </>
  );
}
