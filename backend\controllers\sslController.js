const SubOrder = require("../models/SubOrder");
const Order = require("../models/Order");
const User = require("../models/User");
const { getSSLCertificateStatusUpdateTemplate } = require("../routes/sendEmail/emailTemplates");
const { sendSSLCertificateStatusUpdateEmail, sendSSLCertificateActivationAdminAlert } = require("../routes/sendEmail/sendEmail");

// Define SSL Certificate Status enum
const SSLCertificateStatus = {
    PENDING: 'PENDING',
    PROCESSING: 'PROCESSING',
    ISSUED: 'ISSUED',
    INSTALLED: 'INSTALLED',
    EXPIRED: 'EXPIRED',
    REVOKED: 'REVOKED'
};

exports.submitCSR = async (req, res) => {
    try {
        const { subOrderId } = req.params;
        const { csr, domain, validationEmail, index, installService } = req.body;
        const userId = req.user?._id;

        // Find and verify ownership of the suborder with full hierarchy for notification routing
        const subOrder = await SubOrder.findById(subOrderId).populate({
            path: 'package',
            select: 'name',
            populate: {
                path: 'brand',
                select: 'name',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            }
        });
        if (!subOrder) {
            return res.status(404).json({ message: req.t("order.order_not_found") });
        }

        // Verify that the index is valid
        if (index >= subOrder.quantity) {
            return res.status(400).json({ message: req.t("ssl.invalid_certificate_index") });
        }

        // Create or update the SSL certificate at the specific index
        const sslCertificate = {
            csr,
            domain,
            validationEmail,
            status: SSLCertificateStatus.PROCESSING,
            issuedAt: null,
            expiresAt: null,
            certificateFile: null,
            installService: !!installService // Store whether installation service was requested
        };

        // Store previous status if certificate exists
        const previousStatus = subOrder.ssl.has(index.toString())
            ? subOrder.ssl.get(index.toString()).status
            : null;

        // Update the specific certificate in the SSL Map
        subOrder.ssl.set(index.toString(), sslCertificate);

        // Set suborder status to processing when any certificate is processing
        subOrder.status = 'processing'; // Using the string directly to match OrderStatus.PROCESSING
        console.log(`Certificate is now PROCESSING, setting suborder status to PROCESSING`);

        await subOrder.save();

        // Find the parent order to get the user information for sending email
        const parentOrder = await Order.findOne({ subOrders: subOrderId }).populate('user', 'firstName lastName email favoriteLang');

        // Update the lastSSLActivation field on the parent order
        if (parentOrder) {
            parentOrder.lastSSLActivation = new Date();
            await parentOrder.save();
            console.log(`Updated lastSSLActivation for order ${parentOrder._id}`);
        }

        // Send admin notification email
        if (parentOrder && parentOrder.user) {
            try {
                await sendSSLCertificateActivationAdminAlert(
                    sslCertificate,
                    subOrder,
                    parentOrder.user
                );
                console.log(`Admin notification sent for SSL certificate activation by ${parentOrder.user.email}`);
            } catch (emailError) {
                console.error("Failed to send SSL certificate activation admin alert:", emailError);
                // Continue with the response even if email sending fails
            }
        }

        // Send socket notification about status update if status has changed
        if (global.io && previousStatus !== SSLCertificateStatus.PROCESSING) {
            setImmediate(async () => {
                try {
                    const socketService = require('../services/socketService')(global.io);
                    const admin = await User.findById(req.user._id).select('firstName lastName role');
                    await socketService.notifySSLCertificateStatusUpdate(
                        parentOrder,
                        subOrder,
                        sslCertificate,
                        previousStatus || 'PENDING',
                        admin
                    );
                    console.log(`[SOCKET DEBUG] Notification sent for SSL certificate activation for domain ${domain}`);
                } catch (error) {
                    console.error('Error sending SSL certificate activation notification:', error);
                }
            });
        }

        return res.status(200).json({
            message: req.t("ssl.certificate_activation_success"),
            certificate: sslCertificate
        });
    } catch (error) {
        console.error('Error in submitCSR:', error);
        return res.status(500).json({ message: req.t("common.internal_error") });
    }
};

// Add new method to update SSL certificate status
exports.updateSSLCertificateStatus = async (req, res) => {
    try {
        const { subOrderId, certificateIndex } = req.params;
        const { status, certificateFile, issuedAt, expiresAt } = req.body;

        // Find the suborder and populate the package information with full hierarchy for notification routing
        const subOrder = await SubOrder.findById(subOrderId).populate({
            path: 'package',
            select: 'name',
            populate: {
                path: 'brand',
                select: 'name',
                populate: {
                    path: 'category',
                    select: 'name'
                }
            }
        });
        if (!subOrder) {
            return res.status(404).json({ message: req.t("order.order_not_found") });
        }

        const certificate = subOrder.ssl.get(certificateIndex.toString());
        if (!certificate) {
            return res.status(404).json({ message: "Certificate not found" });
        }

        // Validate the status
        if (!Object.values(SSLCertificateStatus).includes(status)) {
            return res.status(400).json({ message: "Invalid status value" });
        }

        // Store previous status for notification
        const previousStatus = certificate.status;

        // Update certificate details
        certificate.status = status;
        if (certificateFile) certificate.certificateFile = certificateFile;
        if (issuedAt) certificate.issuedAt = new Date(issuedAt);
        if (expiresAt) certificate.expiresAt = new Date(expiresAt);

        subOrder.ssl.set(certificateIndex.toString(), certificate);

        // Check all certificates to determine the suborder status
        const allCertificates = Array.from(subOrder.ssl.values());
        const allIssued = allCertificates.length > 0 && allCertificates.every(cert => cert.status === SSLCertificateStatus.ISSUED);
        const allExpired = allCertificates.length > 0 && allCertificates.every(cert => cert.status === SSLCertificateStatus.EXPIRED);
        const anyProcessing = allCertificates.length > 0 && allCertificates.some(cert => cert.status === SSLCertificateStatus.PROCESSING);

        // Update suborder status based on certificate statuses
        if (allIssued) {
            subOrder.status = 'active'; // Using the string directly to match OrderStatus.ACTIVE
            console.log(`All certificates are ISSUED, setting suborder status to ACTIVE`);
        } else if (allExpired) {
            subOrder.status = 'expired'; // Using the string directly to match OrderStatus.EXPIRED
            console.log(`All certificates are EXPIRED, setting suborder status to EXPIRED`);
        } else if (anyProcessing) {
            subOrder.status = 'processing'; // Using the string directly to match OrderStatus.PROCESSING
            console.log(`At least one certificate is PROCESSING, setting suborder status to PROCESSING`);
        }

        await subOrder.save();

        // Find the parent order to get the user information for sending email
        const parentOrder = await Order.findOne({ subOrders: subOrderId }).populate('user', 'firstName lastName email favoriteLang');

        // Update the lastSSLActivation field on the parent order if status is changing to PROCESSING
        if (parentOrder && status === SSLCertificateStatus.PROCESSING) {
            parentOrder.lastSSLActivation = new Date();
            await parentOrder.save();
            console.log(`Updated lastSSLActivation for order ${parentOrder._id} due to status change to PROCESSING`);
        }

        // Send email notification if the parent order and user are found
        if (parentOrder && parentOrder.user) {
            try {
                await sendSSLCertificateStatusUpdateEmail(
                    certificate,
                    subOrder,
                    parentOrder.user,
                    getSSLCertificateStatusUpdateTemplate
                );
                console.log(`Email notification sent for SSL certificate status update to ${parentOrder.user.email}`);
            } catch (emailError) {
                console.error("Failed to send SSL certificate status update email:", emailError);
                // Continue with the response even if email sending fails
            }
        }

        // Send socket notification about status update if status has changed
        if (global.io && previousStatus !== status) {
            setImmediate(async () => {
                try {
                    const socketService = require('../services/socketService')(global.io);
                    const admin = await User.findById(req.user._id).select('firstName lastName role');
                    await socketService.notifySSLCertificateStatusUpdate(parentOrder, subOrder, certificate, previousStatus, admin);
                    console.log(`[SOCKET DEBUG] Notification sent for SSL certificate status update for domain ${certificate.domain}`);
                } catch (error) {
                    console.error('Error sending SSL certificate status notification:', error);
                }
            });
        }

        res.status(200).json({
            success: true,
            message: "SSL certificate status updated successfully",
            data: subOrder
        });
    } catch (error) {
        console.error("Error updating SSL certificate status:", error);
        res.status(500).json({
            success: false,
            message: req.t("errors.server_error")
        });
    }
};

exports.generateCSR = async (req, res) => {
    try {
        console.log("Starting CSR generation process...");
        const {
            domain,
            country,
            state,
            city,
            organization,
            organizationalUnit,
            email,
            keySize = 2048 // Default to 2048 if not specified
        } = req.body;

        console.log(`Generating CSR for domain: ${domain} with key size: ${keySize}`);

        // Validate required fields
        if (!domain) {
            return res.status(400).json({ message: req.t("ssl.domain_required") });
        }

        if (!country || country.length !== 2) {
            return res.status(400).json({ message: req.t("ssl.invalid_country_code") });
        }

        // Validate key size
        if (![2048, 4096].includes(Number(keySize))) {
            return res.status(400).json({ message: req.t("ssl.invalid_key_size") });
        }

        // First, install node-forge: npm install node-forge
        const crypto = require('crypto');
        const forge = require('node-forge');

        console.log("Generating RSA key pair...");
        const { privateKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: Number(keySize),
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem'
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem'
            }
        });

        console.log("Key pair generated successfully. Creating CSR...");

        // Convert Node.js key to forge format
        const forgePrivateKey = forge.pki.privateKeyFromPem(privateKey);
        const csr = forge.pki.createCertificationRequest();

        // Create subject attributes as an array
        const subjectAttrs = [
            { name: 'commonName', value: domain },
            { name: 'countryName', value: country }
        ];

        // Add optional fields
        if (state) subjectAttrs.push({ name: 'stateOrProvinceName', value: state });
        if (city) subjectAttrs.push({ name: 'localityName', value: city });
        if (organization) subjectAttrs.push({ name: 'organizationName', value: organization });
        if (organizationalUnit) subjectAttrs.push({ name: 'organizationalUnitName', value: organizationalUnit });
        if (email) subjectAttrs.push({ name: 'emailAddress', value: email });

        // Set subject using the array
        csr.setSubject(subjectAttrs);

        // Set public key
        csr.publicKey = forge.pki.setRsaPublicKey(forgePrivateKey.n, forgePrivateKey.e);

        // Sign certification request
        csr.sign(forgePrivateKey, forge.md.sha256.create());

        // Convert to PEM format
        const csrPem = forge.pki.certificationRequestToPem(csr);

        console.log("CSR generated successfully");

        return res.status(200).json({
            success: true,
            message: req.t("ssl.csr_generated_successfully"),
            data: {
                csr: csrPem,
                privateKey: privateKey
            }
        });
    } catch (error) {
        console.error('Error generating CSR:', error);
        return res.status(500).json({
            success: false,
            message: req.t("common.internal_error")
        });
    }
};

// Add this new method to your existing controller
exports.verifyCSR = async (req, res) => {
    try {
        const { csr, domain } = req.body;

        if (!csr || !domain) {
            return res.status(400).json({
                success: false,
                message: req.t("ssl.missing_parameters")
            });
        }

        // Use node-forge to parse the CSR
        const forge = require('node-forge');

        try {
            // Parse the CSR
            const csrObj = forge.pki.certificationRequestFromPem(csr);

            // Get the subject attributes
            const subject = csrObj.subject.attributes;

            // Find the Common Name (domain)
            let csrDomain = null;
            for (let attr of subject) {
                if (attr.name === 'commonName') {
                    csrDomain = attr.value;
                    break;
                }
            }

            // Verify if the domain in CSR matches the provided domain
            if (!csrDomain) {
                return res.status(400).json({
                    success: false,
                    message: req.t("ssl.domain_not_found_in_csr")
                });
            }

            const domainsMatch = csrDomain.toLowerCase() === domain.toLowerCase();

            return res.status(200).json({
                success: true,
                match: domainsMatch,
                csrDomain: csrDomain,
                message: domainsMatch
                    ? req.t("ssl.csr_domain_match")
                    : req.t("ssl.csr_domain_mismatch")
            });

        } catch (error) {
            console.error('Error parsing CSR:', error);
            return res.status(400).json({
                success: false,
                message: req.t("ssl.invalid_csr_format")
            });
        }

    } catch (error) {
        console.error('Error verifying CSR:', error);
        return res.status(500).json({
            success: false,
            message: req.t("common.internal_error")
        });
    }
};

// Export the SSLCertificateStatus enum if needed elsewhere
module.exports.SSLCertificateStatus = SSLCertificateStatus;






