import React from "react";
import { Filter, Plus } from "lucide-react";
import { AccountState } from "../../app/config/AccountState";
import { AccountRole } from "../../app/config/AccountRole";

// Reusable FilterDropdown component
const FilterDropdown = ({ icon, value, onChange, options, label }) => (
  <div className="flex items-center space-x-2 w-full md:w-auto">
    {icon}
    <select
      value={value}
      onChange={onChange}
      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
      aria-label={label}
    >
      {options}
    </select>
  </div>
);

const AdminFilters = ({
  roleFilter,
  setRoleFilter,
  stateFilter,
  setStateFilter,
  resetForm,
  setIsModalOpen,
}) => {
  // Options for State Filter
  const stateOptions = [
    <option key="all" value="all">
      All States
    </option>,
    ...Object.entries(AccountState)
      .filter(([key]) => key !== "GUEST")
      .map(([key, value]) => (
        <option key={key} value={value}>
          {value}
        </option>
      )),
  ];

  // Options for Role Filter
  const roleOptions = [
    <option key="all" value="all">
      All Roles
    </option>,
    ...Object.entries(AccountRole).map(([key, value]) => (
      <option key={key} value={value}>
        {value}
      </option>
    )),
  ];

  return (
    <div className="flex flex-col md:flex-row md:justify-evenly items-center space-y-4 md:space-y-0 md:space-x-4 w-full">
      {/* State Filter */}
      <FilterDropdown
        icon={<Filter className="h-5 w-5 text-gray-400" />}
        value={stateFilter}
        onChange={(e) => setStateFilter(e.target.value)}
        options={stateOptions}
        label="Filter by State"
      />

      {/* Role Filter */}
      <FilterDropdown
        icon={<Filter className="h-5 w-5 text-gray-400" />}
        value={roleFilter}
        onChange={(e) => setRoleFilter(e.target.value)}
        options={roleOptions}
        label="Filter by Role"
      />

      {/* Add Admin Button */}
      <button
        onClick={() => {
          resetForm();
          setIsModalOpen(true);
        }}
        className="w-full md:w-auto bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="Add User"
      >
        <Plus className="h-5 w-5 mr-2" />
        Add User
      </button>
    </div>
  );
};

export default AdminFilters;