// Script pour initialiser toutes les régions avec des entrées pour chaque package VPS
const regionsService = require('./services/regionsService');

// Liste des régions disponibles
const regions = [
  { regionId: 'eu', name: 'Europe (Germany)' },
  { regionId: 'us-central', name: 'US Central' },
  { regionId: 'us-east', name: 'US East' },
  { regionId: 'us-west', name: 'US West' },
  { regionId: 'aus', name: 'Australia (Sydney)' },
  { regionId: 'jpn', name: 'Asia (Japan)' },
  { regionId: 'ind', name: 'Asia (India)' }
];

// IDs des packages VPS (vous devrez les adapter selon votre base de données)
const vpsPackageIds = [
  '6870f496c9583fd217ee4d31', // CLOUD VPS 10
  '687109aa5096836f2776d174'  // CLOUD VPS 20
];

async function initAllRegions() {
  console.log('🚀 Initializing all regions with VPS packages...');
  
  for (const region of regions) {
    console.log(`\n🌍 Processing region: ${region.name} (${region.regionId})`);
    
    for (let i = 0; i < vpsPackageIds.length; i++) {
      const packageId = vpsPackageIds[i];
      const packageName = i === 0 ? 'CLOUD VPS 10' : 'CLOUD VPS 20';
      
      try {
        console.log(`   📦 Adding ${packageName}...`);
        
        // Simuler une requête admin
        const mockReq = {
          body: {
            regionId: region.regionId,
            packageId: packageId,
            additionalPrice: 0
          },
          user: { _id: 'admin-init' }
        };
        
        const mockRes = {
          status: (code) => ({
            json: (data) => {
              if (code === 200 || code === 201) {
                console.log(`   ✅ ${packageName} added successfully`);
              } else {
                console.log(`   ❌ ${packageName} failed:`, data.message);
              }
            }
          })
        };
        
        // Appeler directement le contrôleur (vous devrez adapter le chemin)
        // await regionController.createVPSRegion(mockReq, mockRes);
        
        console.log(`   ⏳ Simulated addition of ${packageName} to ${region.name}`);
        
      } catch (error) {
        console.error(`   ❌ Error adding ${packageName} to ${region.name}:`, error.message);
      }
    }
  }
  
  console.log('\n🎉 Region initialization completed!');
}

// Pour l'instant, juste afficher ce qui serait fait
console.log('📋 This script would initialize the following:');
regions.forEach(region => {
  console.log(`\n🌍 ${region.name} (${region.regionId}):`);
  console.log(`   📦 CLOUD VPS 10: 0 MAD`);
  console.log(`   📦 CLOUD VPS 20: 0 MAD`);
});

console.log('\n💡 To actually run this, you need to:');
console.log('1. Update the vpsPackageIds with your real package IDs');
console.log('2. Import and call the regionController.createVPSRegion method');
console.log('3. Connect to MongoDB');

// initAllRegions();
