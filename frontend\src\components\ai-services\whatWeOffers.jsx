'use client'
import { Typography } from '@material-tailwind/react'
import React from 'react'
function WhatWeOffers({ offers, t }) {
    return (
        <div className="bg-[#F2F4FB] py-10 md:p-10 w-full">
            <Typography
                variant='h2'
                className="text-3xl font-semibold text-center mb-4 font-outfit">
                {t('ai_offers_title')}
            </Typography>

            <div className="max-w-[1400px] mx-auto  flex flex-col gap-y-10 md:p-4 overflow-x-hidden">
                {[0, 1].map(rowIndex => (
                    <div
                        key={rowIndex}
                        className="flex gap-x-6 overflow-x-auto scrollbar-small scrollbar-custom md:p-4 p-2 rounded-lg md:bg-gradient-primary md:shadow-inner"
                    >
                        {offers
                            .slice(rowIndex * 5, rowIndex * 5 + 5)
                            .map((item, index) => (
                                <div
                                    key={index}
                                    className="md:w-[380px] w-[340px] flex-shrink-0 bg-white shadow-md rounded-lg md:p-6 p-4 border border-secondary text-center"
                                >
                                    <div className="w-full flex items-center justify-center text-4xl text-blue-500 mb-4">
                                        {/* <span className="border border-[#F2F4FB] rounded-md p-2 bg-[#F2F4FB]"> */}
                                        {item.isImg ? (
                                            <img
                                                src={item.icon}
                                                alt={item.title}
                                                className="w-[40px]"
                                            />
                                        ) : (
                                            item.icon
                                        )}
                                        {/* </span> */}
                                    </div>
                                    <Typography
                                        variant="h3"
                                        className="font-semibold text-lg mb-6 mx-auto w-full"
                                    >
                                        {t(item.title)}
                                    </Typography>
                                    <ul className="text-gray-800 text-[15px] list-disc text-left flex flex-col gap-y-2 p-1">
                                        {item.items.map((subItem, subIndex) => (
                                            <li key={subIndex}>{t(subItem)}</li>
                                        ))}
                                    </ul>
                                </div>
                            ))}
                    </div>
                ))}
            </div>


            {/* <div className="max-w-7xl mx-auto p-4 space-y-8 border">
                        {[0, 1].map((rowIndex) => (
                            <div key={rowIndex} className="relative">
                                <button
                                    className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-blue-500 text-white rounded-full p-2 shadow-md hover:bg-blue-600"
                                    onClick={() => scrollRow(rowIndex, "left")}
                                >
                                    &larr;
                                </button>
                                <button
                                    className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-blue-500 text-white rounded-full p-2 shadow-md hover:bg-blue-600"
                                    onClick={() => scrollRow(rowIndex, "right")}
                                >
                                    &rarr;
                                </button>

                                <div
                                    className="flex gap-x-6 overflow-hidden scroll-smooth"
                                    ref={(el) => (rowRefs.current[rowIndex] = el)}
                                >
                                    {offers
                                        .slice(rowIndex * 5, rowIndex * 5 + 5)
                                        .map((item, index) => (
                                            <div
                                                key={index}
                                                className="flex-shrink-0 w-[350px] bg-white shadow-lg rounded-lg p-6 border border-gray-200 text-center hover:shadow-xl transition-shadow duration-300"
                                            >
                                                <div className="flex items-center justify-center text-4xl text-blue-500 mb-4">
                                                    <span className="border border-[#F2F4FB] rounded-md p-0 bg-[#F2F4FB]">
                                                        {item.icon}
                                                    </span>
                                                </div>
                                                <h3 className="font-medium text-lg mb-6 text-gray-900 border">
                                                    {t(item.title)}
                                                </h3>
                                                <ul className="text-gray-800 text-sm list-disc text-left flex flex-col gap-y-4">
                                                    {item.items.map((subItem, subIndex) => (
                                                        <li key={subIndex}>{t(subItem)}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        ))}
                                </div>
                            </div>
                        ))}
                    </div> */}
        </div>
    )
}

export default WhatWeOffers